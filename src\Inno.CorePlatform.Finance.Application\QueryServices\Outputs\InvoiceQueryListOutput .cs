﻿using Grpc.Core;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Data.Enums;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Org.BouncyCastle.Asn1.Cms;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 发票查询，出参
    /// </summary>
    public class InvoiceQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// Id
        /// </summary>
        public Guid InvoiceId { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary>
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 应收单开票金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string Type { get; set; }

        /// <summary>
        /// 相关联应收单Id
        /// </summary>
        [ForeignKey("CreditPo")]
        public Guid? CreditId { get; set; }



        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        public bool? IsCancel { get; set; }

        /// <summary>
        /// 变更状态
        /// </summary>
        public CustomizeInvoiceChangedStatusEnum? ChangedStatus { get; set; }
        public string ChangedStatusStr
        {
            get
            {
                if (ChangedStatus.HasValue)
                {

                    return ChangedStatus.GetDescription();
                }
                else
                {
                    return "未红冲";
                }
            }
        }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }

        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

    }

    /// <summary>
    /// 发票清单查询，出参
    /// </summary>
    public class InvoicesQueryListOutput
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票号(提交给阳采的发票号)
        /// </summary>  
        public string? SunPurchaseInvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }
        /// <summary>
        /// 阳采发票代码
        /// </summary> 
        public string? SunPurchaseInvoiceCode { get; set; }
        /// <summary>
        /// 阳采发票状态
        /// </summary> 
        public string? SunPurchaseInvoiceStatus { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>

        public string? Type { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public string InvoiceTimeStr
        {
            get
            {
                return InvoiceTime.HasValue ? InvoiceTime.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 开票申请单号
        /// </summary> 
        public string? Code { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }


        public bool? IsCancel { get; set; }

        public string? StatusStr
        {
            get
            {
                return IsCancel.HasValue && IsCancel.Value ? "已作废" : "已开票";
            }
        }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 填报人
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// SPD审核状态
        /// </summary>
        public SPDStatusEnum? SPDStatus { get; set; }

        public string? SPDStatusStr
        {
            get
            {
                return SPDStatus.HasValue ? SPDStatus.GetDescription() : "";
            }
        }

        /// <summary>
        /// 变更状态
        /// </summary>
        public CustomizeInvoiceChangedStatusEnum? ChangedStatus { get; set; }
        public string ChangedStatusStr
        {
            get
            {
                if (ChangedStatus.HasValue)
                {

                    return ChangedStatus.GetDescription();
                }
                else
                {
                    return "未红冲";
                }
            }
        }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }

        /// <summary>
        /// 阳采状态
        /// </summary>
        public SunPurchaseStatusEnum? SunPurchaseStatus { get; set; }

        public string? SunPurchaseStatusStr
        {
            get
            {
                return SunPurchaseStatus.HasValue ? SunPurchaseStatus.GetDescription() : "";
            }
        }

        ///// <summary>
        ///// 业务单元
        ///// </summary> 
        //public Guid? ServiceId { get; set; }
        //public string? ServiceName { get; set; }

        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }


        /// <summary>
        /// 项目单号 
        /// </summary> 

        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>  
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 预开票Code
        /// </summary>  
        public string? PreCustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>  
        public decimal? AlreadyWriteOffAmount { get; set; }

        /// <summary>
        /// 剩余冲销金额
        /// </summary>  
        public decimal? ResidueWriteOffAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 认款金额
        /// </summary>
        public decimal? ReceiveAmount { get; set; }

        /// <summary>
        /// 是否初始化
        /// </summary>
        public bool? IsInit { get; set; }

        /// <summary>
        /// 红冲金额
        /// </summary>
        public decimal? RedAmount { get; set; }

        /// <summary>
        /// 终端医院Id
        /// </summary> 
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }
        /// <summary>
        /// 税前金额
        /// </summary>
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 价税类型
        /// </summary>
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 价税类型
        /// </summary>
        public string PriceSourceStr
        {
            get
            {
                return PriceSource.HasValue ? PriceSource.GetDescription() : "";
            }
        }

        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }

        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string ApplyUserByName { get; set; }
    }

    /// <summary>
    /// 发票清单导出返回
    /// </summary>
    public class InvoicesExportListOutput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>

        public string? Type { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public string? InvoiceTimeStr { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public string? InvoiceAmount { get; set; }

        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 开票申请单号
        /// </summary> 
        public string? Code { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }

        public string? StatusStr { get; set; }

        public List<InvoiceCreditsOutput> Details { get; set; }

        /// <summary>
        /// 变更状态
        /// </summary>
        public string ChangedStatusStr { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 应收明细返回
    /// </summary>
    public class InvoiceCreditsOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 销售子系统
        /// </summary>
        public string? SaleSystemName { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 是否确认收入 说明
        /// </summary>
        public string? IsSureIncomeStr
        {
            get
            {
                return IsSureIncome == 1 ? "已确认" : "未确认";
            }
        }

        /// <summary>
        /// 业务部门
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 业务部门路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        public DateTime CreatedTime { get; set; }
        public string? InvoiceNo { get; set; }
        public Guid? CompanyId { get; set; }

        public string? ShipmentCode { get; set; }

        /// <summary>
        /// 应收单金额 
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 应收单类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string? ProjectCode { get; set; }



        /// <summary>
        /// 价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 价格来源描述
        /// </summary>   
        public string PriceSourceStr
        {
            get
            {
                return PriceSource.HasValue ? PriceSource.GetDescription() : string.Empty;
            }
        }

        public string? RedReversalConsumNo { get; set; }
        /// <summary>
        /// 自动类型
        /// </summary>
        public InvoiceCreditMarkEnum? Mark { get; set; }
        /// <summary>
        /// 自动类型
        /// </summary>
        public string? MarkStr
        {
            get
            {
                return Mark.HasValue ? Mark.GetDescription() : "";
            }
        }
    }
    public class InvoiceCreditsSumOutput
    {
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
    }
    /// <summary>
    /// 发票清单导出返回（协调服务导出）
    /// </summary>
    public class InvoicesExportListCoordinateOutput
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票号(提交给阳采的发票号)
        /// </summary>  
        public string? SunPurchaseInvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>

        public string? Type { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public string InvoiceTimeStr
        {
            get
            {
                return InvoiceTime.HasValue ? InvoiceTime.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 开票申请单号
        /// </summary> 
        public string? Code { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }


        public string? StatusStr { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// SPD审核状态
        /// </summary>
        public SPDStatusEnum? SPDStatus { get; set; }

        public string? SPDStatusStr
        {
            get
            {
                return SPDStatus.HasValue ? SPDStatus.GetDescription() : "";
            }
        }

        /// <summary>
        /// 变更状态
        /// </summary>
        public string ChangedStatusStr { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }

        /// <summary>
        /// 阳采状态
        /// </summary>
        public SunPurchaseStatusEnum? SunPurchaseStatus { get; set; }

        public string? SunPurchaseStatusStr
        {
            get
            {
                return SunPurchaseStatus.HasValue ? SunPurchaseStatus.GetDescription() : "";
            }
        }

        ///// <summary>
        ///// 业务单元
        ///// </summary> 
        //public Guid? ServiceId { get; set; }
        //public string? ServiceName { get; set; }

        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }


        /// <summary>
        /// 项目单号 
        /// </summary> 

        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>  
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 预开票Code
        /// </summary>  
        public string? PreCustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>  
        public decimal? AlreadyWriteOffAmount { get; set; }

        /// <summary>
        /// 剩余冲销金额
        /// </summary>  
        public decimal? ResidueWriteOffAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 认款金额
        /// </summary>
        public decimal? ReceiveAmount { get; set; }

        /// <summary>
        /// 是否初始化
        /// </summary>
        public bool? IsInit { get; set; }

        /// <summary>
        /// 红冲金额
        /// </summary>
        public decimal? RedAmount { get; set; }

        /// <summary>
        /// 终端医院Id
        /// </summary> 
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public string? BillDateStr { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 销售子系统
        /// </summary>
        public string? SaleSystemName { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 是否确认收入 说明
        /// </summary>
        public string? IsSureIncomeStr
        {
            get
            {
                return IsSureIncome == 1 ? "已确认" : "未确认";
            }
        }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        public string? ShipmentCode { get; set; }

        /// <summary>
        /// 应收单金额 
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 应收单类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? InvoiceAmountNoTax { get; set; }
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 价税类型
        /// </summary>
        public string PriceSourceStr
        {
            get
            {
                return PriceSource.HasValue ? PriceSource.GetDescription() : "";
            }
        }

        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }

        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string ApplyUserByName { get; set; }
    }

    public class SPDInvoiceListTabOutput
    {
        public int WaitSubmitCount { get; set; }
        public int WaitAuditCount { get; set; }
        public int RejectCount { get; set; }
        public int ComplateCount { get; set; }
        public int AllCount { get; set; }
    }

    /// <summary>
    /// 阳采订单详情查询出参
    /// </summary>
    public class SunPurchaseInvoiceDetailsOutput
    {
        public Guid Id { get; set; }

        public Guid InvoiceId { get; set; }

        public virtual InvoicePo Invoice { get; set; }

        public string PurchaseCode { get; set; }

        public string ProductNo { get; set; }

        public Guid ProductId { get; set; }
        /// <summary>
        /// 配送明细对应的顺序号，同一个配送 单下必须唯一
        /// </summary>
        public string SXH { get; set; }

        /// <summary>
        ///是否无配送发票 0：否；1：是 选择“否”的必须与配送名字一一关 联，选择“是”的则无需与配送明细关联
        /// </summary> 
        public string SFWPSFP { get; set; }

        /// <summary>
        ///无配送发票说明
        /// </summary> 
        public string? WPSFPSM { get; set; }

        /// <summary>
        ///是否冲红 标识是否是由于退货而产生的冲红  发票明细；0：否，1：是
        /// </summary> 
        public string SFCH { get; set; }

        /// <summary>
        ///耗材统编代码
        /// </summary> 
        public string HCTBDM { get; set; }

        /// <summary>
        ///耗材国家规格明细27位码（20位码+7 位规格明细码），若本次采购的耗材  是骨科集采范围内的耗材，则必须填  写27位码
        /// </summary> 
        public string? HCXFDM { get; set; }

        /// <summary>
        ///耗材统编代码在企业本地的编码，如 收产品编码等，多个企业本地代码可 对应到同一耗材统编代码上
        /// </summary> 
        public string? QYBDDM { get; set; }

        /// <summary>
        ///医疗器械具体规格型号的详细说明
        /// </summary> 
        public string? GGXHSM { get; set; }

        /// <summary>
        ///关联明细编号
        /// </summary> 
        public string? GLMXBH { get; set; }

        /// <summary>
        ///销售订单号
        /// </summary> 
        public string? XSDDH { get; set; }

        /// <summary>
        ///生产批号
        /// </summary> 
        public string SCPH { get; set; }

        /// <summary>
        ///生产日期
        /// </summary> 
        public DateTime SCRQ { get; set; }

        /// <summary>
        ///有效日期
        /// </summary> 
        public DateTime YXRQ { get; set; }

        /// <summary>
        ///商品数量
        /// </summary>  
        public decimal SPSL { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>  
        public decimal WSDJ { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>  
        public decimal HSDJ { get; set; }

        /// <summary>
        ///税率
        /// </summary>  
        public decimal SL { get; set; }

        /// <summary>
        ///税额
        /// </summary>  
        public decimal SE { get; set; }

        /// <summary>
        ///含税金额
        /// </summary>  
        public decimal HSJE { get; set; }

        /// <summary>
        ///批发价
        /// </summary>  
        public decimal PFJ { get; set; }

        /// <summary>
        ///零售价
        /// </summary>  
        public decimal LSJ { get; set; }

        /// <summary>
        ///注册证号 食药监批文的注册证号
        /// </summary>  
        public string ZCZH { get; set; }

        /// <summary>
        /// 小计
        /// </summary>
        public decimal Subtotal
        {
            get
            {
                return SPSL * HSDJ;
            }
        }
    }

    /// <summary>
    /// 获取阳采配送单出参
    /// </summary>
    public class ShycShipmentDetailOutput
    {
        public string? billCode { get; set; }
        public string? billId { get; set; }
        public string? createdBy { get; set; }
        public string? purchaseCode { get; set; }
        public long? createdTime { get; set; }
        public string? deliveryPointCode { get; set; }
        public string? deliveryPointName { get; set; }
        public string? enterpriseCode { get; set; }
        public string? hospitalCode { get; set; }
        public string? customerProductNoCode { get; set; }
        public string? id { get; set; }
        public string? lotNo { get; set; }
        public string? modelNo { get; set; }
        public string? no { get; set; }
        public string? operateType { get; set; }
        public string? operateTypeName { get; set; }
        public long? produceDate { get; set; }
        public string? producerId { get; set; }
        public string? producerName { get; set; }
        public Guid? productId { get; set; }
        public string? productName { get; set; }
        public string? productNameId { get; set; }
        public string? productNo { get; set; }
        public string? projectCode { get; set; }
        public string? projectId { get; set; }
        public string? projectName { get; set; }
        public string? psmxtmlx { get; set; }
        public string? purchaseDetailCode { get; set; }
        public int? purchaseModel { get; set; }
        public string? purchaseModelName { get; set; }
        public int? purchaseOrderType { get; set; }
        public string? purchaseOrderTypeName { get; set; }
        public int? purchaseType { get; set; }
        public string? purchaseTypeName { get; set; }
        public int? quantity { get; set; }
        public string? specification { get; set; }
        public string? sunPurchaseProductNoCode { get; set; }
        public string? sunPurchaseProductNoSubCode { get; set; }
        public string? sunShipmentDetailCode { get; set; }
        public string? updatedBy { get; set; }
        public long? updatedTime { get; set; }
        public long? validDate { get; set; }
        public string? registrationNo { get; set; }
        public string? reportCode { get; set; }
        public int? reportType { get; set; }
        public string? reportTypeName { get; set; }
    }

    /// <summary>
    /// 根据应收单号获取冲销明细
    /// </summary>
    public class AbatementByCreditBillCodeQueryOutput
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal? Value { get; set; }
        /// <summary>
        /// 冲销时间
        /// </summary>
        public DateTime? Abadate { get; set; }
        /// <summary>
        /// 收款人
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 发票查询分页返回
    /// </summary>
    public class InvoicesPageResponse : PageResponse<InvoicesQueryListOutput>
    {
        /// <summary>
        /// 总开票金额
        /// </summary>
        public decimal? TotalInvoiceAmount { get; set; }

        /// <summary>
        /// 总已冲销金额
        /// </summary>
        public decimal? TotalAWOAmount { get; set; }

        /// <summary>
        /// 总未冲销金额
        /// </summary>
        public decimal? TotalUWOAmount { get; set; }
    }

    /// <summary>
    /// 发票查询总金额返回
    /// </summary>
    public class InvoiceAmountOutput
    {
        /// <summary>
        /// 总开票金额
        /// </summary>
        public decimal? TotalInvoiceAmount { get; set; }

        /// <summary>
        /// 总已冲销金额
        /// </summary>
        public decimal? TotalAWOAmount { get; set; }

        /// <summary>
        /// 总未冲销金额
        /// </summary>
        public decimal? TotalUWOAmount { get; set; }
    }
}
