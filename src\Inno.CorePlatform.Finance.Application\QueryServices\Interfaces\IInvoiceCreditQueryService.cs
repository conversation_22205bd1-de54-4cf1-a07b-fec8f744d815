﻿using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IInvoiceCreditQueryService
    {
        Task<List<InvoiceCredit>> GetInvoiceCredits(List<string> codes);

        Task<InvoiceCredit> GetInvoiceCreditByInvoiceNo(string invoiceNo);
        Task<List<Invoice>> GetInvoices(InvoiceQueryWebApiInput input);
        Task<List<OrderInfoForInvoiceNoOutput>> GetOrderInfoForInvoiceNo(OrderInfoForInvoiceNoInput input);
        Task<List<GetInvoiceByOrderNosOutput>> GetInvoiceByOrderNos(GetInvoiceByOrderNosInput input);
    }
}
