﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PurchasePayPlanRepository : EfBaseRepository<Guid, PurchasePayPlan, PurchasePayPlanPo>, IPurchasePayPlanRepository
    {
        private FinanceDbContext _db;
        public PurchasePayPlanRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<List<PurchasePayPlan>> GetByPurchaseNo(string purchaseCode)
        {
            return (await _db.PurchasePayPlans.Where(p => p.PurchaseCode == purchaseCode).ToListAsync()).Adapt<List<PurchasePayPlan>>();
        }
        public async Task<BaseResponseData<int>> DeleteByPurchaseNo(string purchaseCode)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            var purchasePayPlans = await _db.PurchasePayPlans.Where(p => p.PurchaseCode == purchaseCode).ToListAsync();
            _db.PurchasePayPlans.RemoveRange(purchasePayPlans);
            if (UowJoined)
            {
                await _db.SaveChangesAsync();
            }
            return ret;
        }


        public override Task<int> UpdateAsync(PurchasePayPlan root)
        {
            throw new NotImplementedException();
        }

        protected override PurchasePayPlanPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<PurchasePayPlanPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
