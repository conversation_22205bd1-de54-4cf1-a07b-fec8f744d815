﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Extensions
{
    public class ChineseNumberConverter
    {
        //public static string GetChinaMoney(decimal money)
        //{
        //    string[] strArray;
        //    string str = "";
        //    string str2 = "";
        //    string str3 = money.ToString("0.00");
        //    switch (str3.Trim().Length)
        //    {
        //        case 4:
        //            strArray = new string[] { str3[0].ToString(), "y", str3[2].ToString(), "j", str3[3].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 5:
        //            strArray = new string[] { str3[0].ToString(), "s", str3[1].ToString(), "y", str3[3].ToString(), "j", str3[4].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 6:
        //            strArray = new string[] { str3[0].ToString(), "b", str3[1].ToString(), "s", str3[2].ToString(), "y", str3[4].ToString(), "j", str3[5].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 7:
        //            strArray = new string[] { str3[0].ToString(), "q", str3[1].ToString(), "b", str3[2].ToString(), "s", str3[3].ToString(), "y", str3[5].ToString(), "j", str3[6].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 8:
        //            strArray = new string[] { str3[0].ToString(), "w", str3[1].ToString(), "q", str3[2].ToString(), "b", str3[3].ToString(), "s", str3[4].ToString(), "y", str3[6].ToString(), "j", str3[7].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 9:
        //            strArray = new string[] { str3[0].ToString(), "s", str3[1].ToString(), "w", str3[2].ToString(), "q", str3[3].ToString(), "b", str3[4].ToString(), "s", str3[5].ToString(), "y", str3[7].ToString(), "j", str3[8].ToString(), "f" };
        //            str = string.Concat(strArray);
        //            break;

        //        case 10:
        //            strArray = new string[] {
        //                str3[0].ToString(), "b", str3[1].ToString(), "s", str3[2].ToString(), "w", str3[3].ToString(), "q", str3[4].ToString(), "b", str3[5].ToString(), "s", str3[6].ToString(), "y", str3[8].ToString(), "j",
        //                str3[9].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;

        //        case 11:
        //            strArray = new string[] {
        //                str3[0].ToString(), "q", str3[1].ToString(), "b", str3[2].ToString(), "s", str3[3].ToString(), "w", str3[4].ToString(), "q", str3[5].ToString(), "b", str3[6].ToString(), "s", str3[7].ToString(), "y",
        //                str3[9].ToString(), "j", str3[10].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;

        //        case 12:
        //            strArray = new string[] {
        //                str3[0].ToString(), "m", str3[1].ToString(), "q", str3[2].ToString(), "b", str3[3].ToString(), "s", str3[4].ToString(), "w", str3[5].ToString(), "q", str3[6].ToString(), "b", str3[7].ToString(), "s",
        //                str3[8].ToString(), "y", str3[10].ToString(), "j", str3[11].ToString(), "f"
        //             };
        //            str = string.Concat(strArray);
        //            break;
        //    }
        //    for (int i = 0; i < str.Trim().Length; i++)
        //    {
        //        switch (str[i])
        //        {
        //            case '0':
        //                str2 = str2 + "零";
        //                break;

        //            case '1':
        //                str2 = str2 + "壹";
        //                break;

        //            case '2':
        //                str2 = str2 + "贰";
        //                break;

        //            case '3':
        //                str2 = str2 + "叁";
        //                break;

        //            case '4':
        //                str2 = str2 + "肆";
        //                break;

        //            case '5':
        //                str2 = str2 + "伍";
        //                break;

        //            case '6':
        //                str2 = str2 + "陆";
        //                break;

        //            case '7':
        //                str2 = str2 + "柒";
        //                break;

        //            case '8':
        //                str2 = str2 + "捌";
        //                break;

        //            case '9':
        //                str2 = str2 + "玖";
        //                break;

        //            case 'b':
        //                str2 = str2 + "佰";
        //                break;

        //            case 'f':
        //                str2 = str2 + "分";
        //                break;

        //            case 'j':
        //                str2 = str2 + "角";
        //                break;

        //            case 'm':
        //                str2 = str2 + "亿";
        //                break;

        //            case 'q':
        //                str2 = str2 + "仟";
        //                break;

        //            case 's':
        //                str2 = str2 + "拾";
        //                break;

        //            case 'w':
        //                str2 = str2 + "万";
        //                break;

        //            case 'y':
        //                str2 = str2 + "元";
        //                break;
        //        }
        //    }
        //    return str2;
        //}

        private static readonly string[] NumMap = { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
        private static readonly string[] UnitMap = { "", "拾", "佰", "仟" };
        private static readonly string[] LevelMap = { "", "万", "亿", "万亿" };

        public static string GetChinaMoney(decimal number)
        {
            if (number == 0)
                return "零元整";

            bool isNegative = number < 0;
            number = Math.Abs(number);

            var integerPart = (long)Math.Truncate(number);
            var decimalPart = (int)Math.Round((number - integerPart) * 100);

            var integerStr = ConvertIntegerToChinese(integerPart);
            var result = new StringBuilder();

            if (isNegative)
                result.Append("负");

            result.Append(integerStr).Append("元");

            if (decimalPart > 0)
            {
                int jiao = decimalPart / 10;
                int fen = decimalPart % 10;

                if (jiao > 0)
                    result.Append(NumMap[jiao]).Append("角");
                if (fen > 0)
                    result.Append(NumMap[fen]).Append("分");
            }
            else
            {
                result.Append("整");
            }

            string final = result.ToString()
                .Replace("零角", "")
                .Replace("零售", "零")
                .Replace("角分", "分")
                .Replace("零分", "分")
                .Replace("零零", "零")
                .Trim().TrimStart('零');

            if (final.EndsWith("元"))
                final += "整";

            return final;
        }

        private static string ConvertIntegerToChinese(long number)
        {
            if (number == 0)
                return "零";

            var result = new StringBuilder();
            int levelIndex = 0;

            while (number > 0)
            {
                var thousandPart = number % 10000;
                number /= 10000;

                if (thousandPart > 0)
                {
                    string partStr = ConvertThousand(thousandPart);
                    string levelStr = LevelMap[levelIndex];

                    // ✅ 仅当不是“万”、“亿”结尾时插入“零”
                    if (levelIndex > 0 && result.Length > 0 && !result.ToString().EndsWith("万") && !result.ToString().EndsWith("亿") && !result.ToString().EndsWith("万亿"))
                    {
                        result.Insert(0, "零");
                    } 
                    result.Insert(0, partStr + levelStr);
                }

                levelIndex++;
            }

            return result.ToString();
        }

        private static string ConvertThousand(long number)
        {
            var result = new StringBuilder();
            bool[] digitExists = new bool[4]; // 千百十个位是否存在有效数字

            for (int pos = 3; pos >= 0; pos--)
            {
                long digit = (number / (long)Math.Pow(10, pos)) % 10;

                if (digit > 0)
                {
                    // 插入“零”仅当前面有非零位但当前位为零
                    int zeros = 0;
                    for (int i = 3; i > pos; i--)
                    {
                        if ((number / (long)Math.Pow(10, i)) % 10 == 0)
                            zeros++;
                    }

                    for (int i = 0; i < zeros; i++)
                    {
                        if (result.Length == 0 || result[^1] != '零')
                            result.Append("零");
                    }

                    result.Append(NumMap[digit]);
                    if (pos > 0)
                        result.Append(UnitMap[pos]);

                    digitExists[pos] = true;
                }
            }

            string temp = result.ToString().Replace("零零", "零");

            return temp.Length > 0 ? temp : "零";
        }

    }
}
