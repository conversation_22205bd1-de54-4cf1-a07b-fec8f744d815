﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Projects
{
    public class UpdatePaymentStatusInput
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 0:待付款，1：已付款
        /// </summary>
        public int status { get; set; }
        /// <summary>
        /// 付款/认款金额
        /// </summary>
        public decimal value { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        
        public string? payBillNo { get; set; }
    }
}
