﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public  class CreditListExportOutput
    {
        public string? BillCode { get; set; }

        public string? RelateCode { get; set; }

        public string? CustomerName {  get; set; }

        public string? BillDate {  get; set; }

        public  string? SaleSystemName {  get; set; }

        public string? CompanyName {  get; set; }    

        public string? OrderNo { get; set; }

        public string? ServiceName {  get; set; }

        public  string? CreditTypeStr {  get; set; }

        public string? BusinessDeptFullName {  get; set; }

        public string? ProjectName { get; set; }

        public decimal? Value { get; set; }

        public string ReceiveNo {  get; set; }

        public decimal? AbatmentAmount { get; set; }

        public decimal? LeftAmount { get; set; }

        public string? IsSureIncome { get; set; }

        public string? HospitalName { get; set; }

        public string? CustomerPersonName { get; set; }

        public string? CustomerOrderCode {  get; set; }

        public string InvoiceNo {  get; set; }

        public string InvoiceCode {  get; set; }

        public string? InvoiceTime {  get; set; }

        public decimal? InvoiceAmount { get; set; }
        
        public decimal? CreditAmount {  get; set; }

        public string ChangeStr { get; set; }

        public string InvoiceBlueNo { get; set; }

        public string IsCancel {  get; set; }

        public decimal? LossValue { get; set; }


        /// <summary>
        /// 价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 价格来源
        /// </summary>   
        public string? PriceSourceStr
        {
            get
            {
                return PriceSource.HasValue ? PriceSource.GetDescription() : string.Empty;
            }
        }
    }
}
