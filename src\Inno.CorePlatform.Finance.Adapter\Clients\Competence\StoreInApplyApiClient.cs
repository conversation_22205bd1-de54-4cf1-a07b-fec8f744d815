﻿using Dapr.Client;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.StoreInApply;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class StoreInApplyApiClient : BaseDaprApiClient<StoreInApplyApiClient>, IStoreInApplyApiClient
    {
        public StoreInApplyApiClient(DaprClient daprClient, ILogger<StoreInApplyApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        public async Task<StoreInApplyOutputDto> GetById(Guid Id)
        {
            return await InvokeMethodAsync<StoreInApplyOutputDto>(string.Format(AppCenter.StoreInApply_GetById, Id), RequestMethodEnum.GET);
        }

        public async Task<CustomsPaymentInfo> GetCustomsInfoById(Guid id)
        {
            return await InvokeMethodAsync<CustomsPaymentInfo>(string.Format(AppCenter.StoreInApply_GetCostomsInfo, id), RequestMethodEnum.POST);
        }

        public async Task<StoreInApplyGetListOutputDto> GetList(StoreInApplyGetInput input)
        {
            return await InvokeMethodAsync<StoreInApplyGetInput, StoreInApplyGetListOutputDto>(input,GetAppId(), AppCenter.StoreInApply_GetList);
            
        }

        protected override string GetAppId()
        {
            return AppCenter.StoreInApply_APPID;
        }

        /// <summary>
        /// Dapr调用方法header（有入参）
        /// </summary>
        /// <typeparam name="TResponse">返回泛型</typeparam>
        /// <param name="appId">能力中心AppID</param>
        /// <param name="methodName">请求路径</param>
        /// <returns></returns>
        public async Task<TResponse> InvokeMethodAsync<TRequest, TResponse>(TRequest inputParam, string appId, string methodName)
        {
            try
            {
                var requestBody = JsonConvert.SerializeObject(inputParam);
                _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}内容{requestBody}");
                var result = _daprClient.CreateInvokeMethodRequest(appId, methodName, inputParam);
                var res = await _daprClient.InvokeMethodAsync<StoreInApplyDaprOutputDto<TResponse>>(result);

                if (res == null || res.code != 200)
                {
                    _logger.LogInformation($"请求:【AppID】{appId}【MethodName】{methodName}【Message】{res.errorDetail}");
                }
                return res.data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"请求:【AppID】{appId}【MethodName】{methodName}");
                throw new ApplicationException($"调用远程接口时出现错误，请联系管理员" + ex);
            }
        }
    }
}
