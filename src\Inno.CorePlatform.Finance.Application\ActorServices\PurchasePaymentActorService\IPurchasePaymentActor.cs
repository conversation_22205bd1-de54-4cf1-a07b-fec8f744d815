﻿using Dapr.Actors;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ActorServices.PurchasePaymentActorService.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ActorServices.PurchasePaymentActorService
{
    public interface IPurchasePaymentActor:IActor
    {
        Task Excute(EventBusDTO input);
    }
}
