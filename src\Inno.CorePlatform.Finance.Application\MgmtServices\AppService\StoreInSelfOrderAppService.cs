﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.Enums;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Inno.CorePlatform.ServiceClient;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npoi.Mapper;
using Pipelines.Sockets.Unofficial.Arenas;
using Polly;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 经销购货入库
    /// </summary>
    public class StoreInSelfOrderAppService : BaseAppService, IStoreInSelfOrderAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IPurchasePayPlanRepository _purchasePayPlanRepository;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IAbatementRepository _abatementRepository;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IProjectMgntApiClient _projectApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IStoreInApplyApiClient _storeInApplyApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly IBaseAllQueryService<AbatementPo> _abatementQueryService;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IExchangeRateService _exchangeRateService;
        public StoreInSelfOrderAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBDSApiClient bDSApiClient,
            ICodeGenClient codeGenClient,
            IProjectMgntApiClient projectMgntApiClient,
            IDebtDetailRepository debtDetailRepository,
            IPurchasePayPlanRepository purchasePayPlanRepository,
            IAbatementRepository abatementRepository,
            IProjectMgntApiClient projectApiClient,
            IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
            IBaseAllQueryService<AbatementPo> abatementQueryService,
            IKingdeeApiClient kingdeeApiClient,
            IPaymentRepository paymentRepository,
            IInventoryApiClient inventoryApiClient,
            IStoreInApplyApiClient storeInApplyApiClient,
            IDomainEventDispatcher? deDispatcher,
            IPurchaseApiClient purchaseApiClient,
            IAppServiceContextAccessor? contextAccessor,
            IFileGatewayClient fileGatewayClient,
            FinanceDbContext db,
            IExchangeRateService exchangeRateService,
            Func<int, TimeSpan> sleepDurationProvider = null) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._codeGenClient = codeGenClient;
            this._bDSApiClient = bDSApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._purchasePayPlanRepository = purchasePayPlanRepository;
            this._paymentRepository = paymentRepository;
            this._abatementRepository = abatementRepository;
            this._debtDetailRepository = debtDetailRepository;
            this._projectApiClient = projectApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._purchasePayPlanQueryService = purchasePayPlanQueryService;
            this._storeInApplyApiClient = storeInApplyApiClient;
            this._purchaseApiClient = purchaseApiClient;
            this._abatementQueryService = abatementQueryService;
            this._fileGatewayClient = fileGatewayClient;
            this._db = db;
            this._sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
            this._exchangeRateService= exchangeRateService;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
            InventoryStoreInOutput storein = null;
            await retryPolicy.ExecuteAsync(async () =>
            {
                storein = await _inventoryApiClient.QueryStoreInByCode(input.BusinessCode);
                if (storein == null)
                {
                    _subLogService.LogAzure("StoreInSelfOrder",$"{input.BusinessCode}未查询到入库单","经销入库",LogLevelEnum.Error);
                    throw new Exception("未查询到入库单");
                }

                if (storein.storeInDetails == null || !storein.storeInDetails.Any())
                {
                    _subLogService.LogAzure("StoreInSelfOrder",$"{input.BusinessCode}未找到入库明细","经销入库",LogLevelEnum.Error);
                    throw new Exception("操作失败，原因：未找到入库明细");
                }

                if (storein.storeInDetails.Exists(p => !p.unitCost.HasValue))
                {
                    _subLogService.LogAzure("StoreInSelfOrder",$"{input.BusinessCode}入库明细存在含税成本为空的数据","经销入库",LogLevelEnum.Error);
                    throw new Exception("操作失败，原因：入库明细存在含税成本为空的数据");
                }
            });

            var requestBody = JsonConvert.SerializeObject(input);
            var ret = await CreateDebt(storein, input.BusinessSubType, requestBody, input.useBillDate,
                input.IsAutoBill);
            return ret;
        }

        /// <summary>
        /// 应付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateDebt(InventoryStoreInOutput input,
            string classify,
            string preRequestBody,
            bool? useBillDate = false,
            bool? isAutoBill = false)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var check = await base.IsCreatedDebtForBill(input.storeInCode);
            if (check)
            {
                if (isAutoBill.HasValue && isAutoBill.Value)
                {
                    return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                }
                else
                {
                    throw new Exception("该单据已生成过应付");
                }
            }

            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();


            if (input.storeInDetails != null && input.storeInDetails.Any())
            {
                var debts = new List<Debt>();

                #region 获取项目折扣

                //var discount = 100M;
                //var ruleConfigLst = await _projectApiClient.GetRefAccountAndDiscount(new DTOs.Projects.RuleConfigInput
                //{
                //    AgentId = input.agentId.Value,
                //    CompanyId = input.companyId.Value,
                //    Producerid = input.producerId,
                //    ProjectId = input.projectId ?? Guid.Parse("a6cecadb-a93c-4457-ae39-1ea98be88318"),
                //});
                //if (ruleConfigLst != null)
                //{
                //    var discount_pm = ruleConfigLst.FirstOrDefault().DiscountData.ResultMetas.Where(p => p.Code == "cost_discount").FirstOrDefault();
                //    if (discount_pm != null)
                //    {
                //        discount = decimal.Parse(discount_pm.Value);
                //    }
                //}

                #endregion

                //2.先根据采购单分组
                var purchaseorderCodes = input.storeInDetails
                    .GroupBy(p => new { p.purchaseorderCode, p.purchaseOrderId }).Select(p => p.Key).ToList();
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                if (input.storeInDetails.Count(p => !p.projectId.HasValue) > 0)
                {
                    _subLogService.LogAzure("StoreInSelfOrder",$"{input.storeInCode}入库明细中，有没有项目Id的数据，请库存检查","经销入库",LogLevelEnum.Error);
                    return BaseResponseData<int>.Failed(500, "入库明细中，有没有项目Id的数据，请库存检查！");
                }

                var projectIds = input.storeInDetails.Select(p => p.projectId.Value).Distinct().ToList();
                var projects = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var agents = input.agentId.HasValue
                    ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { input.agentId.Value })
                    : new List<AgentBankInfo>();
                int index = 1;
                foreach (var purchaseorderCode in purchaseorderCodes)
                {
                    var purchaseOrder = await _purchaseApiClient.GetByIdAsync(purchaseorderCode.purchaseOrderId.Value);
                    if (purchaseOrder == null)
                    {
                        return BaseResponseData<int>.Failed(500, $"{purchaseorderCode.purchaseorderCode}，未找到采购单");
                    }

                    //3.付款计划明细
                    var debtDetails = new List<DebtDetail>();
                    var storeInDetails = input.storeInDetails
                        .Where(p => p.purchaseorderCode == purchaseorderCode.purchaseorderCode).ToList();

                    var currentProject = projects.First(p => p.Id == storeInDetails.First().projectId);

                    //1.初始化应付单
                    var debt = new Debt
                    {
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        BillCode = $"{input.storeInCode}-{index.ToString().PadLeft(3, '0')}",
                        RelateCode = input.storeInCode,
                        //BillDate = billDate, //DateTimeHelper.LongToDateTime(input.storeInDate), 
                        BillDate = (useBillDate.HasValue && useBillDate.Value
                            ? DateTimeHelper.LongToDateTime(input.storeInDate)
                            : billDate),
                        CompanyId = Guid.Parse(companyInfo.companyId),
                        CompanyName = companyInfo?.companyName,
                        NameCode = companyInfo?.nameCode,
                        CreatedBy = purchaseOrder.CreatedBy ?? "none",
                        CreatedTime = DateTime.UtcNow,
                        ServiceId = storeInDetails.First().businessUnitId,
                        ServiceName = storeInDetails.First().businessUnitName,
                        AgentId = input.agentId.Value,
                        AgentName = input.agentName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        Id = Guid.NewGuid(),
                        DebtType = DebtTypeEnum.selforder,
                        InvoiceStatus = InvoiceStatusEnum.noninvoice,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptFullName = input.businessDeptFullName,
                        BusinessDeptId = input.businessDeptId.ToString(),
                        OrderNo = (input.autoType == 5 || input.autoType == 4) ? input.relateCode : input.storeInCode,
                        ProjectCode = currentProject.Code,
                        ProjectId = currentProject.Id,
                        ProjectName = input.projectName,
                        PurchaseContactNo = purchaseOrder.Contract?.Code,
                        PurchaseCode = purchaseorderCode.purchaseorderCode,
                        ProducerOrderNo = purchaseOrder.ProducerOrderNo,
                        Value = storeInDetails.Sum(p => p.quantity * (p.originCost ?? 0)),
                        RMBAmount = storeInDetails.Sum(p => p.quantity * (p.rmbAmount ?? 0)),
                        CoinCode = string.IsNullOrEmpty(storeInDetails.First().coinAttribute) ||
                                   storeInDetails.First().coinAttribute == "CNY"
                            ? "CNY"
                            : storeInDetails.First().coinAttribute,
                        CoinName = string.IsNullOrEmpty(storeInDetails.First().coinAttribute) ||
                                   storeInDetails.First().coinAttribute == "CNY"
                            ? "人民币"
                            : storeInDetails.First().coinName,
                        Mark = storeInDetails.First().mark,
                        IsInternalTransactions = Utility.IsInternalTransactions(input.relateCodeType)
                    };
                    var debtDetails_temp = await GetDebtDetails(
                        purchaseorderCode.purchaseorderCode,
                        debt,
                        storeInDetails, companyInfo,
                        input.storeInType, input.autoType);

                    debtDetails.AddRange(debtDetails_temp);
                    debt.DebtDetails = debtDetails;
                    debts.Add(debt);
                    var preCount = debt.DebtDetails.Where(p =>
                        p.AccountPeriodType == (int)AccountPeriodTypeEnum.ProbablyPay &&
                        p.Status == DebtDetailStatusEnum.Completed).Count();
                    if (preCount == debt.DebtDetails.Count())
                    {
                        debt.AbatedStatus = AbatedStatusEnum.Abated;
                    }

                    index++;
                }

                var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
                int limit = 500; //一次最多500条
                var productIds = input.storeInDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
                productNameInfoOutputAll.AddRange(productInfo);
                int totalPages = (int)Math.Ceiling((double)total / limit);
                if (totalPages > 1)
                {
                    for (int i = 2; i <= totalPages; i++)
                    {
                        (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                        productNameInfoOutputAll.AddRange(productInfo);
                    }
                }


                List<KingdeeDebt> kingdeeDebts = await InitKindeeDebtsAsync(input, debts, productNameInfoOutputAll);
                _subLogService.LogAzure("PushDebtsToKingdee",$"{input.storeInCode}推送应付到金蝶入参:{JsonConvert.SerializeObject(kingdeeDebts)}","经销入库",LogLevelEnum.Error);
                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, classify, preRequestBody);
                _subLogService.LogAzure("PushDebtsToKingdee",$"{input.storeInCode}推送应付到金蝶返回结果:{JsonConvert.SerializeObject(kingdeeRes)}","经销入库",LogLevelEnum.Error);

                if (!(kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800))
                {
                    ret = BaseResponseData<int>.Failed(500, "生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }

                Task.Run(async () =>
                {
                    foreach (var debt in debts)
                    {
                        foreach (var subitem in debt.DebtDetails)
                        {
                            if (subitem.DebtDetailExcutes != null && subitem.DebtDetailExcutes.Any())
                            {
                                await PushPaymentSettlement_KD(debt, subitem.DebtDetailExcutes);
                            }
                        }
                    }
                }).Wait();
                ret = await base.CreateDebtRang(debts);
                var debtIds = debts.Select(p => p.Id).Distinct().ToList();
                await _debtRepository.RepaireDebtDiff(debtIds);
            }
            else
            {
                ret = BaseResponseData<int>.Failed(500, "没有明细数据！");
            }

            return ret;
        }

        private async Task<List<KingdeeDebt>> InitKindeeDebtsAsync(InventoryStoreInOutput input, List<Debt> debts,
            List<ProductNameInfoOutput> productInfo)
        {
            #region 包装金蝶应付参数

            var kingdeeDebts = new List<KingdeeDebt>();
            foreach (var debt in debts)
            {
                var kingdeeDebt = new KingdeeDebt()
                {
                    asstact_number1 = debt.AgentId.Value,
                    billno = debt.BillCode,
                    bizdate = debt.BillDate.Value,
                    org_number = debt.NameCode,
                    payorg_number = debt.NameCode,
                    billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                    jfzx_business_number = debt.BusinessDeptId,
                    jfzx_order_number = input.storeInCode,
                    jfzx_creator = debt.CreatedBy ?? "none",
                    currency_number = debt.CoinCode ?? "CNY",
                    pricetaxtotal4 = debt.Value,
                };
                var amount = 0m;
                var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
                var storeInDetails =
                    input.storeInDetails.Where(p => p.purchaseorderCode == debt.PurchaseCode).ToList(); //拆单得到数量
                //if (input.storeInType == 13)
                //{
                //    var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                //    {
                //        Effectdate = DateTime.Now,
                //        OrgcurName = debt.CoinName
                //    });
                //    if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                //    {
                //        throw new Exception("操作失败：未获取到汇率");
                //    }
                //    kingdeeDebt.exchangerate = exchange.Data.Excval;
                //}
                storeInDetails.GroupBy(a => new { a.productNameId, a.originCost, a.taxRate, a.tariffAmount })
                    .ForEach(t =>
                    {
                        var d = new KingdeeDebtDetail();
                        d.taxrate = input.storeInType == 13
                            ? 0
                            : (string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY"
                                ? (t.Key.taxRate.HasValue ? t.Key.taxRate.Value : 13M)
                                : 0);

                    

                        d.quantity = t.Sum(b => b.quantity);
                        d.pricetax = t.Key.originCost.HasValue ? t.Key.originCost.Value : 0;
                        d.jfzx_tariff = (t.Key.tariffAmount.HasValue ? t.Key.tariffAmount.Value : 0) * t.Count();
                        d.jfzx_project_number = debt.ProjectCode; //项目单号 预留 
                        var thisProductInfo = productInfo.FirstOrDefault(e => e.productNameId == t.Key.productNameId);
                        if (thisProductInfo != null)
                        {
                            if (thisProductInfo.classificationNewGuid.HasValue)
                            {
                                d.material_number1 = thisProductInfo.classificationNewGuid.Value.ToString();
                            }
                            else
                            {
                                if (thisProductInfo.classificationGuid.HasValue)
                                {
                                    d.material_number1 = thisProductInfo.classificationGuid.Value.ToString();
                                }
                            }
                        }
                        else
                        {
                            throw new Exception($"操作失败，原因：{t.Key.productNameId}没有找对应的品名");
                        }

                        d.jfzx_revisiontype = "";
                        kingdeeDebtDetails.Add(d);

                        //应付不含税单价
                        d.price2 = Math.Round((d.pricetax / (1 + d.taxrate / 100.00M)), 20);
                        amount += d.price2 * d.quantity;
                    });
                //应付不含税总额
                kingdeeDebt.amount2 = Math.Round(amount, 2);
                kingdeeDebt.billEntryModels = kingdeeDebtDetails;
                if (!string.IsNullOrEmpty(input.attachmentFilelds))
                {
                    var fileIds = input.attachmentFilelds.Split(',').Select(p => Guid.Parse(p)).ToArray();
                    var fileDirectUrls = await _fileGatewayClient.GetFileDirectUrlsAsync(new BizFileDirectUrlInput()
                    {
                        Ids = fileIds,
                        PeriodDay = 7,
                    });
                    if (fileDirectUrls != null && fileDirectUrls.Urls != null)
                    {
                        foreach (var url in fileDirectUrls.Urls)
                        {
                            kingdeeDebt.attachmentsModel.Add(new AttachmentsModel
                            {
                                attachmentUrl = url,
                                fileName = Path.GetFileName(url).Split("?")[0]
                            });
                        }
                    }
                }

                kingdeeDebts.Add(kingdeeDebt);
            }

            #endregion

            return kingdeeDebts;
        }

        private async Task<List<DebtDetail>> GetDebtDetails(
            string purchaseCode,
            Debt debt,
            List<StoreInDetailOutput> storeInDetails,
            DaprCompanyInfoOutput? companyInfo,
            int? storeInType,
            int? autoType
        )
        {
            //2.付款计划明细
            var debtDetails = new List<DebtDetail>();
            //获取采购付款计划 
            var purchasePayPlanLstAll = await _purchasePayPlanRepository.GetByPurchaseNo(purchaseCode);
            var purchasePayPlanLst = new List<PurchasePayPlan>();
            if (purchasePayPlanLstAll == null || !purchasePayPlanLstAll.Any())
            {
                throw new Exception($"操作失败：近期订单号：{purchaseCode}，未找到采购计划");
            }

            if (purchasePayPlanLstAll != null)
            {
                foreach (var detail in storeInDetails)
                {
                    var cost = storeInType.HasValue && storeInType.Value == 13 ? detail.originCost : detail.unitCost;
                    var purchasePayPlantemp = purchasePayPlanLstAll
                        .Where(p => p.ProductId.Value == detail.productId && p.Price == cost).ToList();
                    purchasePayPlanLst.AddRange(purchasePayPlantemp);
                }
            }

            if (purchasePayPlanLst == null || !purchasePayPlanLst.Any())
            {
                throw new Exception($"操作失败：近期订单号：{purchaseCode}，未找到采购计划");
            }

            //按账期分组
            var accountPeriodTypeLst = purchasePayPlanLst.GroupBy(p => new { p.AccountPeriodType, p.AccountPeriodDays })
                .Select(p => new
                {
                    p.Key.AccountPeriodType,
                    p.Key.AccountPeriodDays,
                }).ToList();
            var groupBypurchasePayPlan = new List<dynamic>();

            foreach (var item in accountPeriodTypeLst)
            {
                var tempPayPlanLst = purchasePayPlanLst.Distinct().Where(p =>
                    p.AccountPeriodType == item.AccountPeriodType && p.PurchaseCode == purchaseCode &&
                    p.AccountPeriodDays == item.AccountPeriodDays).ToList();
                if (tempPayPlanLst != null && tempPayPlanLst.Any())
                {
                    decimal totalPrice = 0M;
                    foreach (var subItem in tempPayPlanLst)
                    {
                        var subQuantity = storeInDetails
                            .Where(p => p.productId == subItem.ProductId && p.purchaseorderCode == purchaseCode &&
                                        p.originCost == subItem.Price).Select(p => p.quantity).Sum();
                        var radioPrice = subItem.RatioPrice;
                        totalPrice += subQuantity * radioPrice;
                    }

                    totalPrice = Math.Round(totalPrice, 2);
                    groupBypurchasePayPlan.Add(new
                    {
                        item.AccountPeriodType,
                        AccountPeriodDays = tempPayPlanLst.Max(p => p.AccountPeriodDays),
                        ProbablyPayTime = tempPayPlanLst.Max(p => p.ProbablyPayTime),
                        totalPrice,
                    });
                }
            }

            decimal abatementedAmount = 0M; //总冲销额度
            foreach (var item in groupBypurchasePayPlan.OrderByDescending(p => p.AccountPeriodType))
            {
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = debt.BillCode.Split('-')[0],
                    BillType = "DPP",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    DateTime.TryParse(companyInfo.sysMonth, out DateTime billDate);
                    var debtDetail = new DebtDetail
                    {
                        AccountPeriodType = (int)item.AccountPeriodType,
                        Code = outPut.Codes.First(),
                        CreatedBy = "none",
                        CreatedTime = DateTime.Now,
                        Id = Guid.NewGuid(),
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = item.totalPrice,
                        DebtId = debt.Id,
                        PurchaseCode = purchaseCode,
                        AccountPeriodDays = item.AccountPeriodDays,
                    };
                    if (item.AccountPeriodType == AccountPeriodTypeEnum.ProbablyPay) //预付账期 冲销
                    {
                        debtDetail.ProbablyPayTime = DateTime.Now;
                        //获取近期采购付款单
                        var payments = await _paymentRepository.GetPaymentsBy(purchaseCode,
                            new List<PaymentTypeEnum> { PaymentTypeEnum.Prepay });
                        var paymentsCash = payments.Where(p => !string.IsNullOrEmpty(p.Code) &&
                                                               p.AbatedStatus == AbatedStatusEnum.NonAbate &&
                                                               p.AdvancePayMode != AdvancePayModeEnum.UseQuota)
                            .OrderBy(p => p.PaymentDate).ToList();

                        var paymentsQuata = payments.Where(p => string.IsNullOrEmpty(p.Code) &&
                                                                p.AbatedStatus == AbatedStatusEnum.NonAbate &&
                                                                p.AdvancePayMode == AdvancePayModeEnum.UseQuota)
                            .OrderBy(p => p.PaymentDate).ToList();


                        List<Payment> needUpdatePayment = new List<Payment>();
                        var remainPrepay = debtDetail.Value;
                        var abatementCash = 0M;
                        if (paymentsCash != null && paymentsCash.Any())
                        {
                            var paymentCodes = paymentsCash.Select(p => p.Code); //付款单号
                            var abatements =
                                await _abatementQueryService.GetAllListAsync(p =>
                                    paymentCodes.Contains(p.DebtBillCode));
                            (remainPrepay, needUpdatePayment) = await AbatementPayOfPurchaseCode(paymentsCash,
                                debt.BillCode, debtDetail, purchaseCode, abatements);
                        }

                        //应付单使用明细
                        var useDebts = await _db.DebtPaymentUseDetails.Where(p => p.UseCode == purchaseCode)
                            .ToListAsync(); //应付单号
                        if (useDebts != null && useDebts.Any() && remainPrepay > 0)
                        {
                            if (useDebts.Sum(p => p.UseAmount) > 0)
                            {
                                remainPrepay = await AbatementDebtOfPurchaseCode(useDebts, debt.BillCode, debtDetail,
                                    remainPrepay);
                            }
                        }

                        if (debtDetail.DebtDetailExcutes != null && debtDetail.DebtDetailExcutes.Any())
                        {
                            debtDetail.Value = debtDetail.DebtDetailExcutes.Sum(p => p.Value); //校准
                            debtDetail.Status = DebtDetailStatusEnum.Completed;
                        }

                        if (remainPrepay <= 0)
                        {
                            debtDetail.Status = DebtDetailStatusEnum.Completed;
                        }


                        abatementCash = debtDetail.Value - remainPrepay;
                        var index = 1;
                        //if (remainPrepay == debtDetail.Value) //全部额度
                        //{
                        //    debtDetail.Value = 0;
                        //}
                        if (remainPrepay > 0 && paymentsQuata != null && paymentsQuata.Any())
                        {
                            var isOver = false;
                            foreach (var paymentQuata in paymentsQuata)
                            {
                                var debtDetailQuata = new DebtDetail
                                {
                                    AccountPeriodType = (int)item.AccountPeriodType,
                                    AccountPeriodDays = item.AccountPeriodDays,
                                    Code = $"{outPut.Codes.First()}-{index.ToString().PadLeft(3, '0')}",
                                    CreatedBy = "none",
                                    CreatedTime = DateTime.Now,
                                    Id = Guid.NewGuid(),
                                    Status = DebtDetailStatusEnum.WaitExecute,
                                    Value = paymentQuata.Value > remainPrepay ? remainPrepay : paymentQuata.Value,
                                    DebtId = debt.Id,
                                    PurchaseCode = purchaseCode,
                                    ProbablyPayTime = DateTime.Now,
                                };
                                if (paymentQuata.Value > remainPrepay)
                                {
                                    paymentQuata.CreditAmount = paymentQuata.CreditAmount.HasValue
                                        ? paymentQuata.CreditAmount + remainPrepay
                                        : remainPrepay;
                                    remainPrepay = 0;
                                    isOver = true;
                                }
                                else
                                {
                                    paymentQuata.CreditAmount = paymentQuata.CreditAmount.HasValue
                                        ? paymentQuata.CreditAmount + paymentQuata.Value
                                        : paymentQuata.Value;
                                    remainPrepay = remainPrepay - paymentQuata.Value; //剩下预付金额 
                                }

                                needUpdatePayment.Add(paymentQuata);
                                debtDetails.Add(debtDetailQuata);
                                if (isOver)
                                {
                                    break;
                                }

                                index++;
                            }
                        }

                        if (remainPrepay > 0 && remainPrepay != debtDetail.Value)
                        {
                            var debtDetailQuata = new DebtDetail
                            {
                                AccountPeriodType = (int)item.AccountPeriodType,
                                AccountPeriodDays = item.AccountPeriodDays,
                                Code = $"{outPut.Codes.First()}-{index.ToString().PadLeft(3, '0')}",
                                CreatedBy = "none",
                                CreatedTime = DateTime.Now,
                                Id = Guid.NewGuid(),
                                Status = DebtDetailStatusEnum.WaitExecute,
                                Value = remainPrepay,
                                DebtId = debt.Id,
                                PurchaseCode = purchaseCode,
                                ProbablyPayTime = DateTime.Now,
                            };
                            remainPrepay = 0;
                            debtDetails.Add(debtDetailQuata);
                            index++;
                        }

                        if (needUpdatePayment != null && needUpdatePayment.Any())
                        {
                            foreach (var needPayment in needUpdatePayment.DistinctBy(p => p.Id))
                            {
                                await _paymentRepository.UpdateAsync(needPayment);
                            }
                        }
                    }

                    if (item.AccountPeriodType == AccountPeriodTypeEnum.StoreIn) //入库账期
                    {
                        debtDetail.ProbablyPayTime = debt.BillDate.Value.AddDays((int)item.AccountPeriodDays);
                    }
                    else if (item.AccountPeriodType == AccountPeriodTypeEnum.WarrantyPeriod ||
                             item.AccountPeriodType == AccountPeriodTypeEnum.AcceptancePeriod) //入库账期
                    {
                        debtDetail.ProbablyPayTime = item.ProbablyPayTime == null
                            ? DateTime.Now
                            : ((DateTimeOffset)item.ProbablyPayTime).DateTime;
                    }
                    else if ((item.AccountPeriodType == AccountPeriodTypeEnum.Sale ||
                              item.AccountPeriodType == AccountPeriodTypeEnum.Repayment) &&
                             autoType.HasValue &&
                             (autoType == 4 || autoType == 5))
                    {
                        var credits = await _db.Credits.Where(p =>
                            p.OrderNo == debt.OrderNo &&
                            //p.ServiceId == debt.ServiceId &&
                            p.ProjectId == debt.ProjectId && p.Value > 0).ToListAsync();
                        if (credits == null || !credits.Any())
                        {
                            throw new Exception("直放订单包含销售/回款账期未找到应收，请稍后重试");
                        }

                        debtDetail.CreditId = credits.First().Id;
                        debtDetail.OrderNo = credits.First().OrderNo;
                        if (item.AccountPeriodType == AccountPeriodTypeEnum.Sale)
                        {
                            debtDetail.ProbablyPayTime = credits.First().BillDate.Value.AddDays(item.AccountPeriodDays);
                        }
                    }

                    if (debtDetail.Value != 0)
                    {
                        debtDetails.Add(debtDetail);
                    }
                }
                else
                {
                    throw new Exception(outPut.Msg);
                }
            }

            //var debtDetailExcutes = new List<DebtDetailExcute>();
            //foreach (var debtDetail in debtDetails)
            //{
            //    debtDetailExcutes.AddRange(debtDetail.DebtDetailExcutes);
            //}
            //var debtDetailIds = debtDetails.Where(p => p.AccountPeriodType == (int)AccountPeriodTypeEnum.StoreIn).Select(p => p.Id).ToList();
            return debtDetails;
        }

        /// <summary>
        /// 根据采购单冲销付款单
        /// </summary>
        /// <param name="purchaseCode">采购单号</param>
        /// <param name="debtCode">付款单号</param>
        /// <param name="debtDetail">应付付款计划明细</param>
        /// <returns></returns>
        private async Task<(decimal, List<Payment>)> AbatementPayOfPurchaseCode(List<Payment> payments, string debtCode,
            DebtDetail debtDetail, string purchaseCode, List<AbatementPo> abatements)
        {
            var remainPrepay = debtDetail.Value; //剩下预付金额
            var needUpdatePayment = new List<Payment>();
            if (payments != null && payments.Any())
            {
                var isOver = false;
                debtDetail.DebtDetailExcutes = new List<DebtDetailExcute>();
                foreach (var payment in payments)
                {
                    debtDetail.ProbablyPayTime = DateTime.Now; // payment.PaymentDate;
                    var totalPrepay = payment.Value; //预付款 
                    var detailExcutes = abatements.Where(p => p.DebtBillCode == payment.Code); //获取应付付款计划执行明细
                    var paymenttotal = detailExcutes.Select(p => p.Value).Sum(); //已经冲销金额

                    if (totalPrepay - paymenttotal > 0) //还有可冲销金额
                    {
                        var detailExcuteValue = 0M;
                        //预付款-已经冲销金额>=预付明细
                        if (totalPrepay - paymenttotal >= remainPrepay)
                        {
                            //付款计划明细 改为已完成
                            debtDetail.Status = DebtDetailStatusEnum.Completed;
                            detailExcuteValue = remainPrepay;
                            if (totalPrepay == paymenttotal + remainPrepay)
                            {
                                //如果预付额=已付+付款计划执行额度 付款单给为已冲销
                                payment.AbatedStatus = AbatedStatusEnum.Abated;
                                needUpdatePayment.Add(payment);
                            }

                            remainPrepay = 0;
                            isOver = true;
                        }
                        else
                        {
                            //预付款-已经冲销金额=冲销额度
                            detailExcuteValue = totalPrepay - paymenttotal; //冲销额度
                            remainPrepay = remainPrepay - detailExcuteValue; //剩下预付金额

                            payment.AbatedStatus = AbatedStatusEnum.Abated;
                            needUpdatePayment.Add(payment);
                        }

                        //冲销
                        await _abatementRepository.AddAsync(new Abatement
                        {
                            Abtdate = DateTime.Now,
                            CreatedBy = "none",
                            DebtType = "payment",
                            DebtBillCode = payment.Code,
                            CreditType = "debt",
                            CreditBillCode = debtCode,
                            Value = detailExcuteValue,
                        });
                        //付款计划执行明细
                        debtDetail.DebtDetailExcutes.Add(new DebtDetailExcute
                        {
                            Id = Guid.NewGuid(),
                            DebtDetailId = debtDetail.Id,
                            PaymentCode = payment.Code,
                            PaymentDate = payment.PaymentDate.HasValue ? payment.PaymentDate.Value : DateTime.Now,
                            Value = detailExcuteValue,
                            ExcuteType = "payment"
                        });

                        if (isOver)
                        {
                            break;
                        }
                    }
                }
            }

            return (remainPrepay, needUpdatePayment);
        }

        /// <summary>
        /// 根据采购单冲销应付单
        /// </summary>
        /// <param name="remainPrepay">剩下金额</param>
        /// <param name="debtCode">付款单号</param>
        /// <param name="debtDetail">应付付款计划明细</param>
        /// <returns></returns>
        private async Task<decimal> AbatementDebtOfPurchaseCode(List<DebtPaymentUseDetailPo> useDebts, string debtCode,
            DebtDetail debtDetail, decimal remainPrepay)
        {
            if (useDebts != null && useDebts.Any())
            {
                var lessUseDebt = useDebts.Where(p => p.UseAmount < 0).ToList();
                var greaterUseDebt = useDebts.Where(p => p.UseAmount > 0).ToList();
                if (debtDetail.DebtDetailExcutes == null)
                {
                    debtDetail.DebtDetailExcutes = new List<DebtDetailExcute>();
                }

                var debtCodes = greaterUseDebt.Select(p => p.DebtCode).ToList();
                var debts = await _db.Debts.Where(p => debtCodes.Contains(p.BillCode)).AsNoTracking().ToListAsync();
                var credits = await _db.Credits.Where(p => debtCodes.Contains(p.BillCode)).AsNoTracking().ToListAsync();
                foreach (var useDebt in greaterUseDebt)
                {
                    var canUseAmount = useDebts.Where(p => p.DebtCode == useDebt.DebtCode).Sum(p => p.UseAmount);
                    if (canUseAmount <= 0)
                    {
                        continue;
                    }

                    debtDetail.ProbablyPayTime = DateTime.Now;
                    var detailExcuteValue = canUseAmount >= debtDetail.Value ? debtDetail.Value : canUseAmount; //预付款  
                    if (detailExcuteValue > remainPrepay)
                    {
                        detailExcuteValue = remainPrepay;
                    }

                    if (detailExcuteValue > 0)
                    {
                        //付款计划执行明细 
                        var excute = new DebtDetailExcute
                        {
                            Id = Guid.NewGuid(),
                            DebtDetailId = debtDetail.Id,
                            PaymentCode = useDebt.DebtCode,
                            PaymentDate = DateTime.Now,
                            Value = detailExcuteValue,
                            ExcuteType = "debt"
                        };
                        //记录一个负数使用明细
                        await _db.DebtPaymentUseDetails.AddAsync(new DebtPaymentUseDetailPo
                        {
                            CreatedBy = "none",
                            Id = Guid.NewGuid(),
                            UseCode = useDebt.UseCode,
                            UseAmount = -Math.Abs(detailExcuteValue),
                            DebtCode = useDebt.DebtCode,
                            DebtId = debtDetail.DebtId,
                            RelateId = debtDetail.Id,
                        });
                        //冲销
                        await _abatementRepository.AddAsync(new Abatement
                        {
                            Abtdate = DateTime.Now,
                            CreatedBy = "none",
                            DebtType = "debt",
                            DebtBillCode = useDebt.DebtCode,
                            CreditType = "debt",
                            CreditBillCode = debtCode,
                            Value = detailExcuteValue,
                        });
                        var debt = debts.Where(p => p.BillCode == useDebt.DebtCode).FirstOrDefault();
                        if (debt != null)
                        {
                            var AbatementsAmount = _db.Abatements.Where(p => p.DebtBillCode == useDebt.DebtCode)
                                .Sum(p => p.Value);
                            if (Math.Abs(AbatementsAmount) + Math.Abs(detailExcuteValue) >= Math.Abs(debt.Value))
                            {
                                debt.AbatedStatus = AbatedStatusEnum.Abated;
                                _db.Debts.Update(debt);
                            }

                            excute.ExcuteType = "debt";
                        }

                        var credit = credits.Where(p => p.BillCode == useDebt.DebtCode).FirstOrDefault();
                        if (credit != null)
                        {
                            var AbatementsAmount = _db.Abatements.Where(p =>
                                    p.DebtBillCode == useDebt.DebtCode || p.CreditBillCode == useDebt.DebtCode)
                                .Sum(p => p.Value);
                            if (Math.Abs(AbatementsAmount) + Math.Abs(detailExcuteValue) >= Math.Abs(credit.Value))
                            {
                                credit.AbatedStatus = AbatedStatusEnum.Abated;
                                _db.Credits.Update(credit);
                            }

                            excute.ExcuteType = "credit";
                        }

                        debtDetail.DebtDetailExcutes.Add(excute);
                    }

                    remainPrepay = remainPrepay - detailExcuteValue;
                    if (remainPrepay <= 0)
                    {
                        break;
                    }
                }
            }

            return (remainPrepay);
        }

        private async Task PushPaymentSettlement_KD(Debt debt, List<DebtDetailExcute> detailExcutes)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var debtDetailExcutes = detailExcutes.Where(p => p.ExcuteType != "credit").ToList();
            if (debtDetailExcutes != null && debtDetailExcutes.Any())
            {
                var inputs = new List<PaymentSettlementInput>();
                foreach (var excute in debtDetailExcutes)
                {
                    var tempInput = new PaymentSettlementInput();
                    if (debt.Value < 0)
                    {
                        tempInput.mianbillno = excute.PaymentCode;
                        tempInput.mianSettleAmt = debt.Value;
                        tempInput.asstbillno = debt.BillCode;
                        tempInput.asstSettleAmt = debt.Value;
                    }
                    else
                    {
                        tempInput.mianbillno = debt.BillCode;
                        tempInput.mianSettleAmt = excute.Value;
                        tempInput.asstbillno = excute.PaymentCode;
                        tempInput.asstSettleAmt = excute.Value;
                    }

                    if (excute.Value < 0 || excute.ExcuteType == "debt")
                    {
                        tempInput.asstSettleAmt = tempInput.asstSettleAmt * -1;
                    }

                    inputs.Add(tempInput);
                }

                ret = await _kingdeeApiClient.PaymentSettlement(inputs);
            }

            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }

            var creditDetailExcutes = detailExcutes.Where(p => p.ExcuteType == "credit").ToList();
            if (creditDetailExcutes != null && creditDetailExcutes.Any())
            {
                var inputs = new List<PaymentSettlementInput>();
                foreach (var excute in creditDetailExcutes)
                {
                    var tempInput = new PaymentSettlementInput();
                    if (debt.Value < 0)
                    {
                        tempInput.mianbillno = excute.PaymentCode;
                        tempInput.mianSettleAmt = debt.Value;
                        tempInput.asstbillno = debt.BillCode;
                        tempInput.asstSettleAmt = debt.Value;
                    }
                    else
                    {
                        tempInput.mianbillno = debt.BillCode;
                        tempInput.mianSettleAmt = excute.Value;
                        tempInput.asstbillno = excute.PaymentCode;
                        tempInput.asstSettleAmt = excute.Value;
                    }

                    if (excute.Value < 0 || excute.ExcuteType == "debt")
                    {
                        tempInput.asstSettleAmt = tempInput.asstSettleAmt * -1;
                    }

                    inputs.Add(tempInput);
                }

                ret = await _kingdeeApiClient.PayablesOffsetReceivables(inputs);
            }

            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }
        }


        public async Task<BaseResponseData<List<KingdeeDebt>>> GetKindeeDebtParams(EventBusDTO input)
        {
            InventoryStoreInOutput storein = null;
            storein = await _inventoryApiClient.QueryStoreInByCode(input.BusinessCode);
            if (storein == null)
            {
                throw new Exception("未查询到入库单");
            }

            if (storein.storeInDetails == null || !storein.storeInDetails.Any())
            {
                throw new Exception("操作失败，原因：未找到入库明细");
            }

            if (storein.storeInDetails.Exists(p => !p.unitCost.HasValue))
            {
                throw new Exception("操作失败，原因：入库明细存在含税成本为空的数据");
            }

            var requestBody = JsonConvert.SerializeObject(input);
            var ret = await CreateDebtParam(storein, input.BusinessSubType, requestBody, input.useBillDate);
            return ret;
        }


        private async Task<BaseResponseData<List<KingdeeDebt>>> CreateDebtParam(InventoryStoreInOutput input,
            string classify, string preRequestBody,
            bool? useBillDate = false)
        {
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();

            if (input.storeInDetails != null && input.storeInDetails.Any())
            {
                var debts = new List<Debt>();
                //2.先根据采购单分组
                var purchaseorderCodes = input.storeInDetails
                    .GroupBy(p => new { p.purchaseorderCode, p.purchaseOrderId }).Select(p => p.Key).ToList();
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));

                var projectIds = input.storeInDetails.Select(p => p.projectId.Value).Distinct().ToList();
                var projects = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var agents = input.agentId != null
                    ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid> { input.agentId.Value })
                    : null;
                int index = 1;
                foreach (var purchaseorderCode in purchaseorderCodes)
                {
                    var purchaseOrder = await _purchaseApiClient.GetByIdAsync(purchaseorderCode.purchaseOrderId.Value);
                    if (purchaseOrder == null)
                    {
                        throw new Exception($"{purchaseorderCode.purchaseorderCode}，未找到采购单");
                    }

                    //3.付款计划明细
                    var debtDetails = new List<DebtDetail>();
                    var storeInDetails = input.storeInDetails
                        .Where(p => p.purchaseorderCode == purchaseorderCode.purchaseorderCode).ToList();

                    var currentProject = projects.First(p => p.Id == storeInDetails.First().projectId);

                    //1.初始化应付单
                    var debt = new Debt
                    {
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        BillCode = $"{input.storeInCode}-{index.ToString().PadLeft(3, '0')}",
                        RelateCode = input.relateCode,
                        //BillDate = billDate, //DateTimeHelper.LongToDateTime(input.storeInDate), 
                        BillDate = (useBillDate.HasValue && useBillDate.Value
                            ? DateTimeHelper.LongToDateTime(input.storeInDate)
                            : billDate),
                        CompanyId = Guid.Parse(companyInfo.companyId),
                        CompanyName = companyInfo?.companyName,
                        NameCode = companyInfo?.nameCode,
                        CreatedBy = input.storeInBy ?? "none",
                        CreatedTime = DateTime.UtcNow,
                        ServiceId = storeInDetails.First().businessUnitId,
                        ServiceName = storeInDetails.First().businessUnitName,
                        AgentId = input.agentId.Value,
                        AgentName = input.agentName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        Id = Guid.NewGuid(),
                        DebtType = DebtTypeEnum.selforder,
                        InvoiceStatus = InvoiceStatusEnum.noninvoice,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptFullName = input.businessDeptFullName,
                        BusinessDeptId = input.businessDeptId.ToString(),
                        OrderNo = input.storeInCode,
                        ProjectCode = currentProject.Code,
                        ProjectId = currentProject.Id,
                        ProjectName = input.projectName,
                        PurchaseContactNo = purchaseOrder.Contract?.Code,
                        PurchaseCode = purchaseorderCode.purchaseorderCode,
                        ProducerOrderNo = purchaseOrder.ProducerOrderNo,
                        Value = storeInDetails.Sum(p => p.quantity * (p.originCost ?? 0)),
                        RMBAmount = storeInDetails.Sum(p => p.quantity * (p.rmbAmount ?? 0)),
                        CoinCode = string.IsNullOrEmpty(storeInDetails.First().coinAttribute) ||
                                   storeInDetails.First().coinAttribute == "CNY"
                            ? "CNY"
                            : storeInDetails.First().coinAttribute,
                        CoinName = string.IsNullOrEmpty(storeInDetails.First().coinAttribute) ||
                                   storeInDetails.First().coinAttribute == "CNY"
                            ? "人民币"
                            : storeInDetails.First().coinName,
                        Mark = storeInDetails.First().mark
                    };
                    var debtDetails_temp = await GetDebtDetails(purchaseorderCode.purchaseorderCode,
                        debt,
                        storeInDetails,
                        companyInfo,
                        input.storeInType,
                        input.autoType);
                    debtDetails.AddRange(debtDetails_temp);
                    debt.DebtDetails = debtDetails;
                    debts.Add(debt);
                    var preCount = debt.DebtDetails
                        .Where(p => p.AccountPeriodType == (int)AccountPeriodTypeEnum.ProbablyPay).Count();
                    if (preCount == debt.DebtDetails.Count())
                    {
                        debt.AbatedStatus = AbatedStatusEnum.Abated;
                    }

                    index++;
                }

                var productNameInfoOutputAll = new List<ProductNameInfoOutput>();
                int limit = 500; //一次最多500条
                var productIds = input.storeInDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                var (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds);
                productNameInfoOutputAll.AddRange(productInfo);
                int totalPages = (int)Math.Ceiling((double)total / limit);
                if (totalPages > 1)
                {
                    for (int i = 2; i <= totalPages; i++)
                    {
                        (productInfo, total) = await _bDSApiClient.GetProductClassificationPageAsync(productIds, i);
                        productNameInfoOutputAll.AddRange(productInfo);
                    }
                }

                List<KingdeeDebt> kingdeeDebts = await InitKindeeDebtsAsync(input, debts, productNameInfoOutputAll);
                return new BaseResponseData<List<KingdeeDebt>>()
                {
                    Code = CodeStatusEnum.Success,
                    Data = kingdeeDebts
                };
            }

            return new BaseResponseData<List<KingdeeDebt>>()
            {
                Code = CodeStatusEnum.Failed,
                Message = "未获取到上游数据"
            };
        }
    }
}