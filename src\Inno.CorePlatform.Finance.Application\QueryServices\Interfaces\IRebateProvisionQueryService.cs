﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 返利计提查询
    /// </summary>
    public interface IRebateProvisionQueryService
    {
        /// <summary>
        /// 返利计提查询
        /// </summary>
        Task<(List<RebateProvisionItemQueryOutput>, int)> GetListAsync(RebateProvisionItemQueryInput query);
        /// <summary>
        /// 根据返利计提Id查询返利计提明细
        /// </summary>
        Task<(List<RebateProvisionDetailQueryOutput>, int)> GetDetailsByItemIdAsync(RebateProvisionDetailQueryInput query);
        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> Submit(Guid id);
        /// <summary>
        /// 获取页签数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<RebateProvisionItemTabOutput> GetTabCountAsync([FromBody] RebateProvisionItemQueryInput query);
        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<int> Create([FromBody] RebateProvisionItemCreateInput input);
        /// <summary>
        /// 移除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> Remove([FromBody] RebateProvisionDetailQueryInput input);
        Task<int> RemoveDetails([FromBody] RebateProvisionDetailQueryInput input);
        Task<int> AddDetails(List<RebateProvisionDetail> inputs, Guid rebateProvisionItemId);
        Task<BaseResponseData<int>> ImportDetailsData(Guid id,IFormFile file,Guid? userId,string userName);

        /// <summary>
        /// 获取未完成的返利计提单据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<NotFinishBillOutput>> GetNotFinishBillAsync(NotFinishBillQueryInput input);
    }
}
