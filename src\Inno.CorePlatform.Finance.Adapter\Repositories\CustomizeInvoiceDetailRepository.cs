﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CustomizeInvoiceDetailRepository : EfBaseRepository<Guid, CustomizeInvoiceDetail, CustomizeInvoiceDetailPo>, ICustomizeInvoiceDetailRepository
    {
        private FinanceDbContext _db;
        public CustomizeInvoiceDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> AddManyAsync(List<CustomizeInvoiceDetail> lstCiid)
        {
            if (lstCiid.Count <= 0)
            {
                throw new Exception("运营制作开票明细不能为空！");
            }
            var lstCiidPo = lstCiid.Adapt<List<CustomizeInvoiceDetailPo>>();
            await _db.CustomizeInvoiceDetail.AddRangeAsync(lstCiidPo);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public override async Task<int> AddAsync(CustomizeInvoiceDetail root)
        {
            var po = root.Adapt<CustomizeInvoiceDetailPo>();

            _db.CustomizeInvoiceDetail.Add(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();

        }
        public override async Task<int> UpdateAsync(CustomizeInvoiceDetail root)
        {
            var po = root.Adapt<CustomizeInvoiceDetailPo>();

            _db.CustomizeInvoiceDetail.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();

        }

        protected override CustomizeInvoiceDetailPo CreateDeletingPo(Guid id)
        {
            return new CustomizeInvoiceDetailPo { Id = id };
        }

        protected override async Task<CustomizeInvoiceDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            var detailPo = await _db.CustomizeInvoiceDetail.Where(t => t.Id == id).FirstOrDefaultAsync();
            return detailPo.Adapt<CustomizeInvoiceDetailPo>();
        }
        public async Task<CustomizeInvoiceDetail> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.CustomizeInvoiceDetail.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<CustomizeInvoiceDetail>();
        }
        public async Task<List<CustomizeInvoiceDetail>> GetByItemIdAsync(Guid itemId)
        {
            var po = await _db.CustomizeInvoiceDetail.AsNoTracking().Where(t => t.CustomizeInvoiceItemId == itemId).ToListAsync();
            return po.Adapt<List<CustomizeInvoiceDetail>>();
        }

        public async Task<int> UpdateManyAsync(List<CustomizeInvoiceDetail> lstDetail)
        {
            var lstPo = lstDetail.Adapt<List<CustomizeInvoiceDetailPo>>();

            _db.CustomizeInvoiceDetail.UpdateRange(lstPo);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public async Task<int> DeleteByItemIdAsync(Guid itemId)
        {
            var detailPos = await _db.CustomizeInvoiceDetail.Where(t => t.CustomizeInvoiceItemId == itemId).ToListAsync();
            if (detailPos != null && detailPos.Any())
            {

                var subDetails = await _db.CustomizeInvoiceSubDetails.Where(p => p.CustomizeInvoiceItemId == itemId).ToListAsync();
                if (subDetails != null && detailPos.Any())
                {
                    _db.CustomizeInvoiceSubDetails.RemoveRange(subDetails);
                }

                _db.CustomizeInvoiceDetail.RemoveRange(detailPos);
            }
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public async Task<int> DeleteByIdAsync(Guid id)
        {
            var lstPo = await _db.CustomizeInvoiceDetail.Where(t => t.Id == id).ToListAsync();
            _db.CustomizeInvoiceDetail.RemoveRange(lstPo);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
    }
}
