﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 适用于data为空无返回的请求
    /// </summary>
    public interface IPurchaseExcuteApiClient
    {
        /// <summary>
        /// 更改购货修订数量接口 20240929迁移（修复无法正确获取返回参数问题）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int?>> UpdatePurchaseReviseInviceAmount(List<UpdatePurchaseReviseInvoiceAmountInputDto> input);

        /// <summary>
        /// 更新入票数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int?>> UpdateInvoiceQuantity(List<UpdateInvoiceQuantityInput> input);

        /// <summary>
        /// 获取采购子系统配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SubSysRelaQueryOutput?>> GetSubSysRelaConfig(SubSysRelaQueryInput input);

        /// 生成购货修订订单
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int?>> GenerateAdvancePaymentRevise(List<PurchaseDetailsInput> input);
    }
}
