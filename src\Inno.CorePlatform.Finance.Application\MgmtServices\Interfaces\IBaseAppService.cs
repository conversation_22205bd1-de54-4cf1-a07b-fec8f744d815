﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IBaseAppService
    {
        /// <summary>
        /// 生成应付
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> CreateDebt(Debt input);
        Task<BaseResponseData<int>> CreateDebtRang(List<Debt> inputs);
        /// <summary>
        /// 生成应付批量
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> CreateDebtsRoot(List<Debt> debts);
        /// <summary>
        /// 生成应收，并返回该应收的Id
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<Guid>> CreateCredit(CreditDto input);

        /// <summary>
        /// 创建订阅日志
        /// </summary>
        /// <param name="source">订阅来源</param>
        /// <param name="content">内容</param>
        /// <param name="userName">用户名</param>
        /// <param name="isCommit">是否提交</param>
        /// <returns></returns>
        Task CreateSubLog(SubLogSourceEnum source, string content, string userName, string operate,bool isCommit = true);

        Task<int> CreateManyDebts(List<DebtDto> debts);

        /// <summary>
        /// 拉数据
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> PullIn(EventBusDTO input);
    }
}
