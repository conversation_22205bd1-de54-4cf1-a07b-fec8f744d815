﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 服务费订单
    /// </summary>
    public interface ISellServiceFeeService : ISellAppService
    {
        public Task<BaseResponseData<List<KingdeeCredit>>> GetServiceFeeKingdeeCreditParams(EventBusDTO dto);
    }
}
