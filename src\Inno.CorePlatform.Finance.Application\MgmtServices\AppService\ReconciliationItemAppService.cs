﻿using EFCore.BulkExtensions;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class ReconciliationItemAppService : IReconciliationItemAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IReconciliationItemRepository _itemRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bDSApiClient;
        public ReconciliationItemAppService(IReconciliationItemRepository itemRepository,
            FinanceDbContext db,
            IBDSApiClient bDSApiClient,
            IUnitOfWork unitOfWork)
        {
            _db = db;
            _itemRepository = itemRepository;
            _unitOfWork = unitOfWork;
            _bDSApiClient = bDSApiClient;
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Add(ReconciliationItemInput input)
        {
            var res = BaseResponseData<string>.Success("保存成功");
            var entity = new ReconciliationItem
            {
                CompanyId = input.CompanyId,
                SysMonth = input.SysMonth,
                BillDate = DateTime.Now,
                CreatedBy = input.CurrentUser ?? "none",
                CompanyName = input.CompanyName
            };
            entity.Id = Guid.NewGuid();
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.CompanyId.ToString() }
            })).FirstOrDefault();
            if (companyInfo != null && DateTime.Parse(companyInfo.sysMonth).AddMonths(-1) < DateTime.Parse(input.SysMonth))
            {
                return BaseResponseData<string>.Failed(500, $"操作失败，原因：该公司不存在{input.SysMonth}月度的盘点数据");
            }
            //判断是否存在当前月度的公司
            bool Isexits = await _itemRepository.IsExist(entity);
            if (Isexits)
            {
                return BaseResponseData<string>.Failed(500, "保存失败,已经存在当前月度的公司");
            }
            await _itemRepository.AddAsync(entity);
            int count = await _unitOfWork.CommitAsync();
            if (count <= 0)
            {
                return BaseResponseData<string>.Failed(500, "保存失败");
            }
            res.Data = entity.Id.ToString();
            return res;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Del(ReconciliationItemInput input)
        {
            var res = BaseResponseData<string>.Success("操作成功");
            var reconciliationItem = await _db.ReconciliationItem.Where(p => p.Id == input.ReconciliationItemId).FirstOrDefaultAsync();
            if (reconciliationItem != null)
            {

                _db.ReconciliationItem.Remove(reconciliationItem);
                var reconciliationStockDetails = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId).ToListAsync();
                if (reconciliationStockDetails != null && reconciliationStockDetails.Any())
                {
                    _db.ReconciliationStockDetail.RemoveRange(reconciliationStockDetails);
                }
                var reconciliationIncomeDetails = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId).ToListAsync();
                if (reconciliationIncomeDetails != null && reconciliationIncomeDetails.Any())
                {
                    _db.ReconciliationIncomeDetail.RemoveRange(reconciliationIncomeDetails);
                }
                _db.SaveChanges();
            }
            return res;
        }
        /// <summary>
        /// 新增明细数据
        /// </summary> 
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> AddDetails(ReconciliationDetailsInput input)
        {
            var res = BaseResponseData<string>.Success("操作成功");
            var incomeDetails = await _db.ReconciliationIncomeDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId).ToListAsync();
            var stockDetails = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId).ToListAsync();
            if (incomeDetails != null && incomeDetails.Any())
            {
                //_db.ReconciliationIncomeDetail.RemoveRange(incomeDetails);
                await _db.BulkDeleteAsync(incomeDetails);
            }
            if (stockDetails != null && stockDetails.Any())
            {
                //_db.ReconciliationStockDetail.RemoveRange(stockDetails);
                await _db.BulkDeleteAsync(stockDetails);
            }

            if (input.StockDetails != null && input.StockDetails.Any())
            {
                var stockDetailPos = new List<ReconciliationStockDetailPo>();
                foreach (var item in input.StockDetails)
                {
                    var stockDetailPo = item.Adapt<ReconciliationStockDetailPo>();
                    stockDetailPo.Id = Guid.NewGuid();
                    stockDetailPo.ReconciliationItemId = input.ReconciliationItemId;
                    stockDetailPos.Add(stockDetailPo);
                }
                if (stockDetailPos.Any())
                {
                    //await _db.ReconciliationStockDetail.AddRangeAsync(stockDetailPos);
                    await _db.BulkInsertAsync(stockDetailPos);
                }
            }

            if (input.IncomeDetails != null && input.IncomeDetails.Any())
            {
                var incomeDetailPos = new List<ReconciliationIncomeDetailPo>();
                foreach (var item in input.IncomeDetails)
                {
                    var incomeDetailPo = item.Adapt<ReconciliationIncomeDetailPo>();
                    incomeDetailPo.Id = Guid.NewGuid();
                    incomeDetailPo.ReconciliationItemId = input.ReconciliationItemId; 
                    incomeDetailPos.Add(incomeDetailPo);
                }
                if (incomeDetailPos.Any())
                {
                    //await _db.ReconciliationIncomeDetail.AddRangeAsync(incomeDetailPos);
                    await _db.BulkInsertAsync(incomeDetailPos);
                }
            }
     
            int count = await _unitOfWork.CommitAsync();
            
            return res;
        }

    }
}
