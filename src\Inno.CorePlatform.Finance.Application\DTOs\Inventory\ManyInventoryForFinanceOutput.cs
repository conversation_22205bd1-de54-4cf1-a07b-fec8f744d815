using System;
using System.Collections.Generic;
using Inno.CorePlatform.Common.Http;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    #region 基类

    /// <summary>
    /// 多对多勾稽明细项基类
    /// </summary>
    public abstract class ManyInventoryBaseDetailItem
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public string companyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string companyName { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public string agentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string agentName { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string productId { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string productNo { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public string productNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string purchaseOrderCode { get; set; }

        /// <summary>
        /// 批次信息
        /// </summary>
        public List<object> lotInfo { get; set; }

        /// <summary>
        /// 生产商ID
        /// </summary>
        public string producerId { get; set; }

        /// <summary>
        /// 生产商名称
        /// </summary>
        public string producerName { get; set; }
    }

    #endregion

    #region 入库明细输出

    /// <summary>
    /// 多对多勾稽查询入库单明细输出参数
    /// </summary>
    public class ManyStoreInDetailQueryOutput : BaseResponseData<List<ManyStoreInDetailItem>>
    {
    }

    /// <summary>
    /// 多对多勾稽入库单明细项
    /// </summary>
    public class ManyStoreInDetailItem : ManyInventoryBaseDetailItem
    {
        /// <summary>
        /// 入库单号
        /// </summary>
        public string storeInCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxRate { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal unitCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal noUnitCost { get; set; }

        /// <summary>
        /// 入库日期
        /// </summary>
        public long storeInDate { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        public int storeInType { get; set; }

        /// <summary>
        /// 入库类型描述
        /// </summary>
        public string storeInTypeDesc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? quantity { get; set; } = 0;

        /// <summary>
        /// 已入票数量
        /// </summary>
        public decimal? invoiceQuantity { get; set; } = 0;

        /// <summary>
        /// 可入票数量
        /// </summary>
        public decimal canInvoiceQuantity { get; set; }

        /// <summary>
        /// 返利
        /// </summary>
        public decimal rebate { get; set; }

        /// <summary>
        /// 附件字段
        /// </summary>
        public object attachmentFilelds { get; set; }

        /// <summary>
        /// 申请ID
        /// </summary>
        public string applyId { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        public string producerOrderNo { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }
        public string? invoiceQty { get; set; }
        public string? settlementUnitCost { get; set; }
    }

    #endregion

    #region 出库明细输出

    /// <summary>
    /// 多对多勾稽出库单明细项
    /// </summary>
    public class ManyStoreOutDetailItem : ManyInventoryBaseDetailItem
    {
        /// <summary>
        /// 出库单号
        /// </summary>
        public string storeOutCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string taxRate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public long billDate { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        public int storeOutType { get; set; }

        /// <summary>
        /// 出库类型描述
        /// </summary>
        public string storeOutTypeDesc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal quantity { get; set; }

        /// <summary>
        /// 已入票数量
        /// </summary>
        public string invoiceQuantity { get; set; }

        /// <summary>
        /// 结算成本
        /// </summary>
        public string settlementCost { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public string unitCost { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string projectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string projectName { get; set; }
    }

    #endregion

    #region 换货转退货明细输出

    /// <summary>
    /// 换货转退货明细项
    /// </summary>
    public class ManyStoreExchangeBackDetailItem : ManyInventoryBaseDetailItem
    {
        public string? modelNo { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string billCode { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxRate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public long billDate { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int type { get; set; }

        /// <summary>
        /// 类型描述
        /// </summary>
        public string typeDesc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal quantity { get; set; }

        /// <summary>
        /// 已入票数量
        /// </summary>
        public decimal invoiceQuantity { get; set; }

        /// <summary>
        /// 已入票数量（字符串格式）
        /// </summary>
        public string invoiceQty { get; set; }

        /// <summary>
        /// 可入票数量
        /// </summary>
        public decimal canInvoiceQuantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal unitCost { get; set; }

        /// <summary>
        /// 结算成本单价
        /// </summary>
        public string settlementUnitCost { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }
    }

    #endregion
}
