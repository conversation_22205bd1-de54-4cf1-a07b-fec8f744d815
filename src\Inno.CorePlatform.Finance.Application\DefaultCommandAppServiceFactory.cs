﻿using Inno.CorePlatform.Common.DDD;
using Microsoft.Extensions.Configuration;

namespace Inno.CorePlatform.Finance.Application
{

    public interface ICommandAppServiceFactory
    {
    }

    public class DefaultCommandAppServiceFactory : ICommandAppServiceFactory
    {
        private readonly IConfiguration configuration;
        private readonly IAppServiceContextAccessor contextAccessor;

        public DefaultCommandAppServiceFactory(
            IConfiguration configuration,
            IAppServiceContextAccessor contextAccessor)
        {
            this.configuration = configuration;
            this.contextAccessor = contextAccessor;
        } 
    }

}
