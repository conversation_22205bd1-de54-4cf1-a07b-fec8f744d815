﻿using Dapr.Client;
using Inno.CorePlatform.Common.Clients.ApiServices.ProjectManage.Inputs;
using Inno.CorePlatform.Common.Clients.ApiServices.ProjectManage.Outputs;
using Inno.CorePlatform.Common.Clients;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class ProjectApiClient : BaseDaprApiClient<ProjectApiClient>, IProjectMgntApiClient
    {
        public ProjectApiClient(DaprClient daprClient, ILogger<ProjectApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取项目元数据列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ProjectMateInfoOutput>> GetProjectMateList(ProjectMateInfoInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ProjectMateInfoInput, List<ProjectMateInfoOutput>>(input, $"{ApiRoutes.PROJECT_MATE_LIST}");
        }
        public async Task<List<ProjectInfo>> GetProjectInfoByIds(List<Guid>? ids)
        {
            var p = new { ids = ids };
            var res = await InvokeMethodWithQueryObjectAsync<object, List<ProjectInfo>>(p, AppCenter.PM_GetProjectInfo, RequestMethodEnum.POST);
            return res;
        }

        public async Task<List<ProjectInfo>> GetProjectInfoByProjectNo(string projectNo)
        {
            var p = new { ProjectNo = projectNo };
            var res = await InvokeMethodWithQueryObjectAsync<object, List<ProjectInfo>>(p, AppCenter.PM_GetProjectInfo, RequestMethodEnum.POST);
            return res;
        }
        public async Task<List<RuleConfigOutPut>> GetRefAccountAndDiscount(RuleConfigInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<RuleConfigInput, List<RuleConfigOutPut>>(input, AppCenter.PM_GetRefAccountAndDiscount);
        }
        public async Task<List<ProductCostOutput>> GetProductCost(ProductCostInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ProductCostInput, List<ProductCostOutput>>(input, AppCenter.PM_GetProductCostList);
        }
        /// <summary>
        /// 更新费用付款单状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> UpdatePaymentStatus(List<UpdatePaymentStatusInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<UpdatePaymentStatusInput>, string>(input, AppCenter.PM_UpdatePaymentStatus);
        }

        /// <summary>
        /// 根据名称获取项目列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<ProjectOutput>> GetProjectListByNames(Guid? userId, List<string>? names, string functionUri = "")
        {
            var p = new { userId = userId, projectNames = names, functionUri = functionUri };
            var res = await InvokeMethodWithQueryObjectAsync<object, List<ProjectOutput>>(p, AppCenter.PM_GetRunProjectList, RequestMethodEnum.POST);
            return res;
        }

        /// <summary>
        /// 通过项目id获取项目和核算部门
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<List<ProjectAndBusinessDeptInfoOutput>> GetProjectListByIds(List<Guid> ids)
        {
            var p = new { ids = ids, limit = 400, page = 1 };
            var res = await InvokeMethodWithQueryObjectAsync<object, List<ProjectAndBusinessDeptInfoOutput>>(p, AppCenter.PM_GetProjectList, RequestMethodEnum.POST);
            return res;
        }


        /// <summary>
        /// 根据公司获取项目
        /// </summary>
        /// <param name="companyId"></param> 
        /// <returns></returns>
        public async Task<List<ProjectOutput>> GetProjectListByCompanyId(string companyId)
        {
            var input = new GetProjectInfoMetaInput() { CompanyId = companyId };
            var res = await InvokeMethodWithQueryObjectAsync<object, List<ProjectOutput>>(input, AppCenter.PM_GetProjectInfoMeta, RequestMethodEnum.POST);
            return res;
        }
        /// <summary>
        /// 根据公司获取项目
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="businessDeptId"></param>
        /// <returns></returns>
        public async Task<List<ProjectOutput>> GetProjectListByBusinessDeptIds(List<string> businessDeptIds)
        {
            var res = await InvokeMethodWithQueryObjectAsync<List<string>, List<ProjectOutput>>(businessDeptIds, AppCenter.PM_GetProjectListByBusinessDeptIds, RequestMethodEnum.POST);
            return res;
        }
        protected override string GetAppId()
        {
            return AppCenter.PM_APPID;
        }

        public async Task<List<GetProjectInfoByIdOrNameOut>> GetProjectInfoByIdOrName(GetProjectInfoByIdOrNameInput input)
        {
            var res = await InvokeMethodWithQueryObjectAsync<GetProjectInfoByIdOrNameInput, List<GetProjectInfoByIdOrNameOut>>(input, AppCenter.PM_GetProjectInfoByIdOrName, RequestMethodEnum.POST);
            return res;
        }
        /// <summary>
        /// 获取项目负责人
        /// </summary>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        public async Task<List<GetProjectLeaderOutput>> GetProjectLeader(List<Guid> projectIds)
        {
            var res = await InvokeMethodWithQueryObjectAsync<List<Guid>, List<GetProjectLeaderOutput>>(projectIds, AppCenter.PM_GetProjectLeader, RequestMethodEnum.POST);
            return res;
        }
    }
}
