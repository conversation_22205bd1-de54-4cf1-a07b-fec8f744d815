﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.LossRecognition;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 损失确认单操作
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class LossRecognitionController : BaseController
    {
        private readonly ILossRecognitionAppService _LossRecognitionAppService;
        public LossRecognitionController(
            ILossRecognitionAppService LossRecognitionAppService, ISubLogService subLog) : base(subLog)
        {
            this._LossRecognitionAppService = LossRecognitionAppService;
        }
        /// <summary>
        ///提交损失确认单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut("submit")]
        public async Task<BaseResponseData<string>> Submit(Guid? id)
        {
            return await _LossRecognitionAppService.SubmitItemAsync(id, CurrentUser.UserName);
        }
        /// <summary>
        /// 删除损失确认单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("del")]
        public async Task<BaseResponseData<bool>> Del(Guid? id)
        {
            var res = await _LossRecognitionAppService.DeleteItemAsync(id);
            
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }
        /// <summary>
        /// 删除损失确认单详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("deleteDetail")]
        public async Task<BaseResponseData<bool>> DeleteDetail(Guid id)
        {
            var res = await _LossRecognitionAppService.DeleteDetailItemAsync(id);
            
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }
       
       
        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(LossRecognitionAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            ret.Data = await _LossRecognitionAppService.GetAttachFile(input);
            return ret;
        }
        

        /// <summary>
        /// 创建损失确认单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Create")]
       public async Task<BaseResponseData<string>> Create(CreateLossRecognitionInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            if (!input.Id.HasValue)
            {
                return await _LossRecognitionAppService.CreateLossRecognition(input);//新增
            }
            else
            {
                return await _LossRecognitionAppService.UpdateLossRecognition(input);//编辑
            }
            
        }
       
       
    }
    
}
