﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class FinanceSalePaymentPubInput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string SaleNo { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatementAmount { get; set; }

        /// <summary>
        /// 认款单号
        /// </summary>
        public string RecognizeReceiveCode { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary> 
        public string ReceiveCode { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get;  set; }
    }
}
