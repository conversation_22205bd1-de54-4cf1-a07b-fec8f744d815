﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 发票入账单明细查询入参
    /// </summary>
    public class InvoiceReceiptDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 主体Id
        /// </summary>
        public Guid? InvoiceReceiptItemId { get; set; }
        /// <summary>
        /// 主体Ids
        /// </summary>
        public List<Guid?>? InvoiceReceiptItemIds { get; set; }

        public Guid? CompanyId { get; set; }

        public Guid? ServiceId { get; set; }

        public Guid? CustomerId { get; set; }

        public string? InvoiceNo { get; set; }

        public string? CreditCode { get; set; }

        /// <summary>
        /// 排除的id集合
        /// </summary>
        public List<string?>? ExcludeIds { get; set; }
        public string? UserId { get; set; }

        public string? StartDate { get; set; }

        public string? EndDate { get; set; }
    }

    public class InvoiceReceiptAttachFileInput
    {
        public Guid InvoiceReceiptItemId { get; set; }

        public string? AttachFileIds { get; set; }
        public string? AttachFileId { get; set; } 
    }
    public class InvoiceReceiptCancelInput
    {
        public Guid InvoiceReceiptItemId { get; set; }
        public string CreatedBy { get;  set; }
        public StatusEnum? Status { get; set; }

    }
}
