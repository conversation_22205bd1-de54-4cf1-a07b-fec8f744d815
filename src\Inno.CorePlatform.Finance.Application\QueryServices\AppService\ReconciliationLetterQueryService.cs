﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.Metrics;
using System.Linq.Expressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class ReconciliationLetterQueryService : QueryAppService, IReconciliationLetterQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _bDSApiClient;
        protected readonly IPCApiClient _pcApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        /// <summary>
        /// 对账函
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="bDSApiClient"></param>
        /// <param name="pcApiClient"></param>
        public ReconciliationLetterQueryService(IAppServiceContextAccessor? contextAccessor, FinanceDbContext db, IBDSApiClient bDSApiClient, IPCApiClient pcApiClient) : base(contextAccessor)
        {
            this._db = db;
            this._bDSApiClient = bDSApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._pcApiClient = pcApiClient;
        }
        /// <summary>
        /// 对账函列表查询Tab数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<ReconciliationLetterListTabOutput>> GetTabCount(ReconciliationLetterQueryInput query)
        {
            var ret = BaseResponseData<ReconciliationLetterListTabOutput>.Success("操作成功");

            Expression<Func<ReconciliationLetterItemPo, bool>> expAll = z => 1 == 1;
            Expression<Func<ReconciliationLetterItemPo, bool>> expWaitSubmit = z => 1 == 1;
            Expression<Func<ReconciliationLetterItemPo, bool>> expWaitAudit = z => 1 == 1;
            Expression<Func<ReconciliationLetterItemPo, bool>> expRefuse = z => 1 == 1;
            Expression<Func<ReconciliationLetterItemPo, bool>> expComplate = z => 1 == 1;
            Expression<Func<ReconciliationLetterItemPo, bool>> expMy = z => 1 == 1;

            var data = new ReconciliationLetterListTabOutput();
            #region 查询条件
            query.Status = StatusEnum.all;
            expAll = await GetReconciliationLetterExp(query, expAll);
            data.AllCount = await _db.ReconciliationLetterItem.Where(expAll).CountAsync();

            query.Status = StatusEnum.waitSubmit;
            expWaitSubmit = await GetReconciliationLetterExp(query, expWaitSubmit);
            data.WaitSubmitCount = await _db.ReconciliationLetterItem.Where(expWaitSubmit).CountAsync();

            query.Status = StatusEnum.waitAudit;
            expWaitAudit = await GetReconciliationLetterExp(query, expWaitAudit);
            data.WaitAuditCount = await _db.ReconciliationLetterItem.Where(expWaitAudit).CountAsync();

            query.Status = StatusEnum.Refuse;
            expRefuse = await GetReconciliationLetterExp(query, expRefuse);
            data.RefuseCount = await _db.ReconciliationLetterItem.Where(expRefuse).CountAsync();

            query.Status = StatusEnum.Complate;
            expComplate = await GetReconciliationLetterExp(query, expComplate);
            data.ComplateCount = await _db.ReconciliationLetterItem.Where(expComplate).CountAsync();

            query.Status = StatusEnum.My;
            expMy = await GetReconciliationLetterExp(query, expMy);
            data.MyCount = await _db.ReconciliationLetterItem.Where(expMy).CountAsync();
            #endregion


            ret.Data = data;
            return ret;
        }
        /// <summary>
        /// 获取对账函表达式
        /// </summary>
        /// <param name="query"></param>
        /// <param name="exp"></param>
        /// <returns></returns>
        private async Task<Expression<Func<ReconciliationLetterItemPo, bool>>> GetReconciliationLetterExp(ReconciliationLetterQueryInput query, Expression<Func<ReconciliationLetterItemPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            else
            {
                //获取用户数据策略
                var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
                var strategry = await _pcApiClient.GetStrategyAsync(input);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CustomerId.Value));
                            }
                        }
                    }
                }
            }
            if (query.Id != null)
            {
                exp = exp.And(z => z.Id == query.Id);
            }
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))//关键字
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.CustomerName, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.CompanyName, $"%{query.searchKey}%"));
            }
            if (query.DeadlineDateStart != null && query.DeadlineDateEnd != null)//截止日期
            {
                exp = exp.And(z => (z.Deadline != null && z.Deadline >= query.DeadlineDateStart && z.Deadline <= query.DeadlineDateEnd));
            }
            if (query.CompanyId != null)//公司
            {
                exp = exp.And(z => z.CompanyId == query.CompanyId);
            }
            if (query.CustomerId != null)//客户
            {
                exp = exp.And(z => z.CustomerId == query.CustomerId);
            }
            if (query.Status != null && (int)query.Status > -1)//状态
            {
                exp = exp.And(z => z.Status == query.Status);
                if (query.Status == StatusEnum.waitSubmit) //待提交，需控权，只显示当前账户创建的单据
                {
                    exp = exp.And(z => z.CreatedBy == query.CurrentUser);
                }
            }
            if (query.ReconciliationLetterTmp != null && (int)query.ReconciliationLetterTmp > -1)//对账函模版
            {
                exp = exp.And(z => z.ReconciliationLetterTemplate == query.ReconciliationLetterTmp);
            }

            if (!string.IsNullOrWhiteSpace(query.BillCode))//单号
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode ?? "", $"%{query.BillCode}%"));
            }
            return exp;
        }
        /// <summary>
        /// 获取对账函列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<ReconciliationLetterListOutput>, int)> GetListAsync(ReconciliationLetterQueryInput query)
        {
            Expression<Func<ReconciliationLetterItemPo, bool>> exp = z => 1 == 1;

            #region 查询条件 
            exp = await GetReconciliationLetterExp(query, exp);
            #endregion

            IQueryable<ReconciliationLetterItemPo> baseQuery = _db.ReconciliationLetterItem.Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();

            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                for (int i = query.sort.Count - 1; i >= 0; i--)
                {
                    var ss = query.sort[i].Split(',');
                    if (ss.Length > 1 && ss[0] == "billCode")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.BillCode);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.BillCode); }
                    }
                    if (ss.Length > 1 && ss[0] == "deadline")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.Deadline);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.BillDate); }
                    }

                    if (ss.Length > 1 && ss[0] == "arrearsAmount")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.ArrearsAmount);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.ArrearsAmount); }
                    }
                    if (ss.Length > 1 && ss[0] == "createdTime")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.CreatedTime); }
                    }
                }
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<ReconciliationLetterListOutput>()).ToListAsync();
            var companyList = await _bDSApiClient.GetCompanyMetaAsync(new CompanyMetaInput() { ids = list.Select(p => p.CompanyId.Value.ToString()).ToList() });
            foreach (var item in list)//判断是不是法人公司
            {
                if (companyList.Count == 0)
                {
                    item.isCorporateCompany = false;
                    continue;
                }
                if (companyList.Select(p => p.id.ToLower()).ToList().Contains(item.CompanyId.Value.ToString()))
                {
                    item.isCorporateCompany = true;
                }
                else
                {
                    item.isCorporateCompany = false;
                }
            }
            return (list, count);
        }
        /// <summary>
        /// 获取对账函详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<ReconciliationLetterDetailQueryOutput>, int)> GetListDetailAsync(ReconciliationLetterDetailQueryInput query)
        {
            Expression<Func<ReconciliationLetterDetailPo, bool>> exp = z => 1 == 1;
            if (query.ReconciliationLetterItemId != null)
            {
                exp = exp.And(z => z.ReconciliationLetterItemId == query.ReconciliationLetterItemId);
            }
            if (query.Classify != null && (int)query.Classify > 0)
            {
                exp = exp.And(z => z.Classify == query.Classify);//区分类型
            }


            IQueryable<ReconciliationLetterDetailPo> baseQuery = _db.ReconciliationLetterDetails.Where(exp).AsNoTracking();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.BillDate);
            }
            #endregion
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<ReconciliationLetterDetailQueryOutput>()).ToListAsync();
            return (list, count);
        }
        /// <summary>
        /// 获取对账函详情统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<DetailSumCount> GetDetailSumCount(ReconciliationLetterDetailQueryInput input)
        {
            var ret = new DetailSumCount
            {
                NonReceivedValueSum = 0,
                ReceivedValueSum = 0,
                ValueSum = 0,
            };
            var item = await _db.ReconciliationLetterItem.FirstOrDefaultAsync(p => p.Id == input.ReconciliationLetterItemId);
            if (item != null)
            {
                if (item.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product)
                {
                    ret.ValueSum = await _db.ReconciliationLetterProductDetails.Where(p => p.ReconciliationLetterItemId == input.ReconciliationLetterItemId).SumAsync(p => p.Amount);
                }
                else
                {
                    ret.ValueSum = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == input.ReconciliationLetterItemId && p.Classify == input.Classify).SumAsync(p => p.Value);
                    ret.NonReceivedValueSum = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == input.ReconciliationLetterItemId && p.Classify == input.Classify).SumAsync(p => p.NonReceivedValue);
                    ret.ReceivedValueSum = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == input.ReconciliationLetterItemId && p.Classify == input.Classify).SumAsync(p => p.ReceivedValue);
                }
            }
            return ret;
        }



        /// <summary>
        /// 获取对账函货号详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<ReconciliationLetterProductDetailQueryOutput>, int)> GetListProductDetailAsync(ReconciliationLetterDetailQueryInput query)
        {
            Expression<Func<ReconciliationLetterProductDetailPo, bool>> exp = z => 1 == 1;
            if (query.ReconciliationLetterItemId != null)
            {
                exp = exp.And(z => z.ReconciliationLetterItemId == query.ReconciliationLetterItemId);
            }
            IQueryable<ReconciliationLetterProductDetailPo> baseQuery = _db.ReconciliationLetterProductDetails.Where(exp).AsNoTracking();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.BillDate);
            }
            #endregion
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<ReconciliationLetterProductDetailQueryOutput>()).ToListAsync();
            return (list, count);
        }
        public async Task<ReconciliationLetterItemPo> GetLetterItemByIdAsync(Guid id)
        {

            var item = await _db.ReconciliationLetterItem.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
            {
                Names = new List<string> { item.CreatedBy },
                Limit = 1
            });
            var user = userInfos.Data.List.FirstOrDefault(z => z.Name == item.CreatedBy);
            if (user != null)
            {
                item.CreatedBy = user.DisplayName;
            }
            return item;
        }
    }
}
