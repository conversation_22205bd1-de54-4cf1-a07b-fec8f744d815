﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Data.Models;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IAbtmentService
    {
        /// <summary>
        /// 批量付款执行后金蝶系统回传应付执行计划单号和付款单号，生成付款单、冲销记录
        /// </summary>
        /// <param name="lstInput"></param>
        /// <returns></returns>
        Task<int> GenerateAbtAsync(List<GenerateAbtInput> lstInput);

        /// <summary>
        ///  正负应付对冲
        /// </summary>
        /// <param name="debtId">发起冲销的应付单Id</param>
        /// <param name="lstInput">被冲销的应付单列表</param>
        /// <param name="userName">执行人账号</param>
        /// <param name="isPushKingdee">是否推送金蝶</param>
        /// <param name="singleSettle">单次冲销</param>
        /// <returns></returns>
        Task<int> GenerateAbtForDebtAsync(Guid debtId, List<GenerateAbtForDebtInput> lstInput, string userName, bool? singleSettle = null);

        /// <summary>
        /// 应收冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> GenerateAbtForCreditAsync(GenerateAbtForCreditInput input);
        Task OrderPaiedPub(List<GenerateAbtInput> inputs);
        Task<int> GenerateAbtForDebtToCreditAsync(Guid debtId, List<GenerateAbtForDebtInput> lstInput, string userName,bool? singleSettle=null);

        /// <summary>
        /// 负数应付冲收款单
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="input"></param>
        /// <param name="userName"></param>
        /// <param name="singleSettle"></param>
        /// <returns></returns>
        Task<int> GenerateAbtForReceiveAsync(Guid debtId, List<GenerateAbtForDebtInput> input, string userName, bool? singleSettle = null);
        Task ServicePaiedPub(List<DebtPo> debts, List<string> debtBillCodes = null);
        /// <summary>
        /// 拆分后的服务费
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task ServicePaiedPubForSplit(List<DebtPo> debts, List<Guid> detailIds);

        /// <summary>
        /// 正数应付冲付款单
        /// </summary>
        /// <param name="debtId"></param>
        /// <param name="lstInput"></param>
        /// <param name="userName"></param>
        /// <param name="singleSettle"></param>
        /// <returns></returns>
        Task<int> GenerateAbtForPaymentAsync(Guid debtId, List<GenerateAbtForDebtInput> lstInput, string userName, bool? singleSettle = null);
        /// <summary>
        /// 收款冲付款单成功之后加入冲销信息
        /// </summary>
        /// <param name="details"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<int> ReceiveAbtPaymentSave(List<RefundDetailPo> details, string userName);
        Task MinusDebtAbtPurchasePub(Guid debtId, List<GenerateAbtForDebtInput> inputs);


        /// <summary>
        /// 损失确认正负应付冲销
        /// </summary>
        /// <param name="inputs">正负应付冲销入参</param>
        /// <param name="userName"></param>
        /// <returns></returns>
        /// <exception cref="AppServiceException"></exception>
        (List<DebtDetailPo> AddDebtDetails, List<DebtDetailExcutePo> AddDebtDetailExcutes, List<AbatementPo> AddDebtAbatementDetails) LossGenerateAbtForDebtAsync(List<LossAbtForDebtInput> inputs, string userName);
    }
}
