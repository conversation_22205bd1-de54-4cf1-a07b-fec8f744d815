{"uri": "metadata://fam", "type": "app", "label": "财务管理", "children": [{"id": "ReconciliationLetter-view", "uri": "metadata://fam/ReconciliationLetter/index", "type": "route", "label": "对账函管理", "path": "/financeManagement/ReconciliationLetter", "children": []}, {"id": "ReconciliationLetterOA-view", "uri": "metadata://fam/ReconciliationLetterOA/index", "type": "route", "label": "对账函管理OA", "path": "/financeManagement/ReconciliationLetterOA", "children": []}, {"id": "auth", "uri": "metadata://fam/credit-debt-Query", "type": "module", "label": "应收应付查询", "children": [{"id": "creditQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/creditQuery-view", "type": "route", "path": "/financeManagement/creditQuery", "label": "应收清单", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-Credit/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "serviceCreditQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/serviceCreditQuery-view", "type": "route", "path": "/financeManagement/serviceCreditQuery", "label": "应收清单（服务商客户端）", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-ServiceCredit/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "debtQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/debtQuery-view", "type": "route", "path": "/financeManagement/debtQuery", "label": "应付清单", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-Debt/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "serviceDebtQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/serviceDebtQuery-view", "type": "route", "path": "/financeManagement/serviceDebtQuery", "label": "应付清单（服务商客户端）", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-ServiceDebt/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "debtDetailQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/debtDetailQuery-view", "type": "route", "path": "/financeManagement/debtDetailQuery", "label": "付款计划查询"}, {"id": "InputBillQuery-view", "uri": "metadata://fam/credit-debt-Query/routes/InputBillQuery-view", "type": "route", "path": "/financeManagement/InputBillQuery", "label": "进项发票管理"}, {"id": "paymentQuery-view", "uri": "metadata://fam/payment-Query/routes/paymentQuery-view", "type": "route", "path": "/financeManagement/paymentQuery", "label": "付款单清单", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-Payment/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "servicePaymentQuery-view", "uri": "metadata://fam/payment-Query/routes/servicePaymentQuery-view", "type": "route", "path": "/financeManagement/servicePaymentQuery", "label": "付款单清单（服务商客户端）", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-ServicePayment/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "refundApply-view", "uri": "metadata://fam/payment-Query/routes/refundApply-view", "type": "route", "path": "/financeManagement/payment/refundApply", "label": "退款申请查询"}, {"id": "createAndeditRefundApply", "uri": "metadata://fam/refundApply-Query/routes/createAndeditRefundApply", "type": "command", "label": "创建和编辑退款申请"}, {"id": "deleteRefundApply", "uri": "metadata://fam/refundApply-Query/routes/deleteRefundApply", "type": "command", "label": "删除退款申请"}, {"id": "kingdeemock-view", "uri": "metadata://fam/kingdeemock/index", "type": "route", "label": "模拟金蝶回调", "path": "/kingdeemock/index"}, {"id": "confirmReceipt", "uri": "metadata://fam/credit-debt-Query/functions/confirmReceipt", "type": "command", "label": "确认收入"}, {"id": "confirmAbatement", "uri": "metadata://fam/credit-debt-Query/functions/confirmAbatement", "type": "command", "label": "进行冲销"}]}, {"id": "auth", "uri": "metadata://fam/inputbill-deatil-Query", "type": "module", "label": "进项发票查询", "children": [{"id": "inputBillQuery-view", "uri": "metadata://fam/inputbill-deatil-Query/routes/inputBillQuery-view", "type": "route", "path": "/financeManagement/inputBillQuery", "label": "进项发票管理"}, {"id": "inputBillStatisQuery-view", "uri": "metadata://fam/inputbill-deatil-Query/routes/inputBillStatisQuery-view", "type": "route", "path": "/financeManagement/inputBillStatisQuery", "label": "进项发票统计查询"}]}, {"id": "auth", "uri": "metadata://fam/BulkPayment-Query", "type": "module", "label": "批量付款管理", "children": [{"id": "BulkPayment-index", "uri": "metadata://fam/BulkPayment-Query/routes/BulkPayment-Index", "type": "route", "path": "/financeManagement/BulkPayment/index", "label": "批量付款"}, {"id": "BulkPayment-indexoa", "uri": "metadata://fam/BulkPayment-Query/routes/BulkPayment-Indexoa", "type": "route", "path": "/financeManagement/BulkPayment/indexOA", "label": "批量付款OA审批"}, {"id": "add-payment", "uri": "metadata://fam/finance-bulkpayment/functions/add-payment", "type": "command", "label": "创建批量付款"}, {"id": "submit-payment", "uri": "metadata://fam/finance-bulkpayment/functions/submit-payment", "type": "command", "label": "提交批量付款"}, {"id": "del-payment", "uri": "metadata://fam/finance-bulkpayment/functions/del-payment", "type": "command", "label": "删除批量付款单据"}]}, {"id": "auth", "uri": "metadata://fam/CustomizeInvoice", "type": "module", "label": "运营制作开票管理", "children": [{"id": "CustomizeInvoice-Detail", "uri": "metadata://fam/CustomizeInvoice/routes/CustomizeInvoice-Detail", "type": "route", "path": "/financeManagement/CustomizeInvoiceDetail", "label": "运营制作开票（应收）"}, {"id": "CustomizeInvoice-DetailNew", "uri": "metadata://fam/CustomizeInvoice/routes/CustomizeInvoice-DetailNew", "type": "route", "path": "/financeManagement/CustomizeInvoiceDetailNew", "label": "运营制作开票（应收明细）"}, {"id": "CustomizeInvoice-Submit", "uri": "metadata://fam/CustomizeInvoice/routes/CustomizeInvoice-Submit", "type": "route", "path": "/financeManagement/CustomizeInvoiceSubmit", "label": "运营提交开票"}, {"id": "CustomizeInvoice-SubmitOA", "uri": "metadata://fam/CustomizeInvoice/routes/CustomizeInvoice-SubmitOA", "type": "route", "path": "/financeManagement/CustomizeInvoiceSubmitOA", "label": "运营提交开票OA"}, {"id": "PreCustomizeInvoice-List", "uri": "metadata://fam/CustomizeInvoice/routes/PreCustomizeInvoice-List", "type": "route", "path": "/financeManagement/PreCustomizeInvoiceList", "label": "预开票列表"}]}, {"id": "auth", "uri": "metadata://fam/RecognizeReceive", "type": "module", "label": "收款认领", "children": [{"id": "RecognizeReceive-index", "uri": "metadata://fam/RecognizeReceive-index/routes/RecognizeReceive-index", "type": "route", "path": "/financeManagement/RecognizeReceive/index", "label": "收款认领"}, {"id": "RecognizeReceive-serviceIndex", "uri": "metadata://fam/RecognizeReceive-serviceIndex/routes/RecognizeReceive-serviceIndex", "type": "route", "path": "/financeManagement/RecognizeReceive/serviceIndex", "label": "业务单元认款单", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-RecognizeReceive-serviceIndex/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "add-recognize", "uri": "metadata://fam/RecognizeReceive-index/functions/add-recognize", "type": "command", "label": "创建认款"}, {"id": "sureType-recognize", "uri": "metadata://fam/RecognizeReceive-index/functions/sureType-recognize", "type": "command", "label": "指定收款类型"}, {"id": "submit-recognize", "uri": "metadata://fam/RecognizeReceive-index/functions/submit-recognize", "type": "command", "label": "提交认款"}, {"id": "del-recognize", "uri": "metadata://fam/RecognizeReceive-index/functions/del-recognize", "type": "command", "label": "删除认款"}]}, {"id": "dept-warning", "uri": "metadata://fam/dept-warning", "type": "module", "label": "付款账期预警", "path": null, "children": [{"id": "dept-warning", "uri": "metadata://fam/dept-warning/query", "type": "query", "label": "付款账期预警", "path": null, "children": []}]}, {"id": "finance-Inventory", "uri": "metadata://fam/Inventory", "type": "module", "label": "盘点", "path": null, "children": [{"id": "inventory-view", "uri": "metadata://fam/Inventory/index", "type": "route", "label": "盘点管理", "path": "/Inventory/index", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-Inventory/functions/Activate-inventory", "type": "command", "label": "开启盘点"}, {"id": "excute-export", "uri": "metadata://fam/finance-Inventory/functions/setting", "type": "command", "label": "设定完成"}, {"id": "excute-export", "uri": "metadata://fam/finance-Inventory/functions/Inventory", "type": "command", "label": "盘点"}]}, {"id": "InvoiceCredit-view", "uri": "metadata://fam/InvoiceCredit/index", "type": "route", "label": "销项发票查询", "path": "/financeManagement/InvoiceCredit", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-InvoiceCredit/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "Invoices-view", "uri": "metadata://fam/Invoices/index", "type": "route", "label": "发票清单", "path": "/financeManagement/Invoices", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-Invoices/functions/excute-export", "type": "command", "label": "导出数据"}, {"id": "excute-export", "uri": "metadata://fam/finance-Invoices/functions/excute-changeRelationship", "type": "command", "label": "更换应收"}]}, {"id": "ServiceInvoices-view", "uri": "metadata://fam/ServiceInvoices/index", "type": "route", "label": "发票清单（服务商客户端）", "path": "/financeManagement/ServiceInvoices", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-ServiceInvoices/functions/excute-export", "type": "command", "label": "导出数据"}, {"id": "excute-export", "uri": "metadata://fam/finance-ServiceInvoices/functions/excute-changeRelationship", "type": "command", "label": "更换应收"}]}, {"id": "SPDInvoices-view", "uri": "metadata://fam/SPDInvoices/index", "type": "route", "label": "SPD发票清单", "path": "/financeManagement/SPDInvoices", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-SPDInvoices/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "InvoiceFilling-view", "uri": "metadata://fam/InvoiceFilling/index", "type": "route", "label": "发票填报", "path": "/financeManagement/InvoiceFilling", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-InvoiceFilling/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "InvoiceReceipts-view", "uri": "metadata://fam/InvoiceReceipts/index", "type": "route", "label": "发票入账单列表", "path": "/financeManagement/InvoiceReceipts", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-InvoiceReceipts/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "InvoiceReceiptCreate-view", "uri": "metadata://fam/InvoiceReceiptCreate/index", "type": "route", "label": "创建发票入账", "path": "/financeManagement/InvoiceReceiptCreate"}, {"id": "InvoiceReceipts-SubmitOA", "uri": "metadata://fam/financeManagement/InvoiceReceiptDetail", "type": "route", "path": "/financeManagement/InvoiceReceiptDetail", "label": "发票入账OA明细"}, {"id": "RebateProvisionQuery-view", "uri": "metadata://fam/RebateProvisionQuery/index", "type": "route", "label": "返利计提清单", "path": "/financeManagement/RebateProvisionQuery", "children": [{"id": "excute-export", "uri": "metadata://fam/finance-RebateProvisionQuery/functions/excute-export", "type": "command", "label": "导出数据"}]}, {"id": "RedConfirmationFormList-view", "uri": "metadata://fam/RedConfirmationFormList/index", "type": "route", "label": "红字确认单列表", "path": "/financeManagement/RedConfirmationFormList", "children": [{"id": "excute-generate", "uri": "metadata://fam/finance-RedConfirmationFormList/functions/excute-generate", "type": "command", "label": "生成确认单"}]}, {"id": "creditRecord-view", "uri": "metadata://fam/Inventory/creditRecord", "type": "route", "label": "应收盘点管理", "path": "/Inventory/creditRecord"}, {"id": "creditRecordDh-view", "uri": "metadata://fam/Inventory/creditRecordDh", "type": "route", "label": "订货系统应付盘点管理", "path": "/Inventory/creditRecordDh", "children": [{"id": "creditRecordDh-confirm", "uri": "metadata://fam/Inventory-creditRecordDh/functions/creditRecordDh-confirm", "type": "command", "label": "确认盘点"}, {"id": "creditRecordDh-export", "uri": "metadata://fam/Inventory-creditRecordDh/functions/creditRecordDh-export", "type": "command", "label": "导出"}]}, {"id": "debtRecord-view", "uri": "metadata://fam/Inventory/debtRecord", "type": "route", "label": "应付盘点管理", "path": "/Inventory/debtRecord"}, {"id": "paymentRecord-view", "uri": "metadata://fam/Inventory/paymentRecord", "type": "route", "label": "付款盘点管理", "path": "/Inventory/paymentRecord"}, {"id": "advanceRecord-view", "uri": "metadata://fam/Inventory/advanceRecord", "type": "route", "label": "垫资盘点管理", "path": "/Inventory/advanceRecord"}, {"id": "advanceEvaluation-view", "uri": "metadata://fam/Inventory/advanceEvaluation", "type": "route", "label": "垫资运行评价", "path": "/Inventory/advanceEvaluation"}, {"id": "reconciliation-view", "uri": "metadata://fam/Inventory/reconciliation", "type": "route", "label": "对账管理", "path": "/Inventory/reconciliation"}]}, {"id": "finance-BillReport", "uri": "metadata://fam/BillReport", "type": "module", "label": "报表", "path": null, "children": [{"id": "billReport-receive", "uri": "metadata://fam/BillReport/receive", "type": "route", "label": "应收明细", "path": "/BillReport/receive", "children": []}, {"id": "billReport-meet", "uri": "metadata://fam/BillReport/meet", "type": "route", "label": "应付明细", "path": "/BillReport/meet", "children": []}, {"id": "billReport-Paymentdetails", "uri": "metadata://fam/BillReport/paymentdetails", "type": "route", "label": "收款明细", "path": "/BillReport/paymentdetails", "children": []}, {"id": "billReport-BulkPaymentdetails", "uri": "metadata://fam/BillReport/bulkpaymentdetails", "type": "route", "label": "批量付款明细", "path": "/BillReport/bulkpaymentdetails", "children": []}]}]}