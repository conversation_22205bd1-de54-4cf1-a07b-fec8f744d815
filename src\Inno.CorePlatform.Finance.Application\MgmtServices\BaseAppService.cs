﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;

namespace Inno.CorePlatform.Finance.Application.MgmtServices
{
    public abstract class BaseAppService : CommandAppService, IBaseAppService
    {
        private ICreditRepository _creditRepository;
        public IDebtRepository _debtRepository;
        protected ISubLogService _subLogService;
        public IUnitOfWork _unitOfWork;
        public BaseAppService(
        ICreditRepository creditRepository,
        IDebtRepository debtRepository,
        ISubLogService subLogService,
        IUnitOfWork unitOfWork,
        IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor) : base(deDispatcher, contextAccessor)
        {
            this._creditRepository = creditRepository;
            this._debtRepository = debtRepository;
            this._subLogService = subLogService;
            this._unitOfWork = unitOfWork;
        }

        public async Task<BaseResponseData<Guid>> CreateCredit(CreditDto input)
        {
            var ret = BaseResponseData<Guid>.Success("操作成功！");
            if (input != null)
            {
                var creditItem = input.Adapt<Credit>();
                var creditItemId = await _creditRepository.AddAsync(creditItem);
                ret.Data = creditItem.Id;
            }
            return ret;
        }
        public async Task<BaseResponseData<int>> CreateDebtRang(List<Debt> inputs)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (inputs != null)
            {
                await _debtRepository.InertManyAsync(inputs);
                var saveCount = await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        public async Task<BaseResponseData<int>> CreateDebt(Debt input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (input != null)
            {
                await _debtRepository.AddAsync(input);
                var saveCount = await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        public async Task<BaseResponseData<int>> CreateDebtsRoot(List<Debt> debts)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            if (debts != null)
            {
                await _debtRepository.InertManyAsync(debts);
                var saveCount = await _unitOfWork.CommitAsync();
            }
            return ret;

        }
        /// <summary>
        /// 某个业务单据是否已经产生应收
        /// </summary>
        /// <param name="relateCode"></param>
        /// <returns></returns>
        public async Task<bool> IsCreatedCreditForBill(string relateCode)
        {
            return await _creditRepository.IsExistForBill(relateCode);
        }


        /// <summary>
        /// 某个业务单据是否已经产生应付
        /// </summary>
        /// <param name="relateCode"></param>
        /// <returns></returns>
        public async Task<bool> IsCreatedDebtForBill(string relateCode)
        {
            return await _debtRepository.IsExistForBill(relateCode);
        }

        /// <summary>
        /// 创建订阅日志
        /// </summary>
        /// <param name="source">订阅来源</param>
        /// <param name="content">内容</param>
        /// <param name="userName">用户名</param>
        /// <param name="isCommit">是否提交</param>
        /// <returns></returns>
        public async Task CreateSubLog(SubLogSourceEnum source, string content, string userName, string operate, bool isCommit = true)
        {
            await _subLogService.LogAsync(source.ToString(), content, operate);
            //await _subLogRepository.AddAsync(new SubLog
            //{
            //    Id = Guid.NewGuid(),
            //    Source = source.ToString(),
            //    Content = content,
            //    CreatedBy = userName,
            //    CreatedTime = DateTime.Now,
            //    UpdatedBy = userName,
            //    UpdatedTime = DateTime.Now,
            //    Operate = operate
            //});
            //if (isCommit)
            //{
            //    var temp = await _unitOfWork.CommitAsync();
            //}
        }


        public abstract Task<BaseResponseData<int>> PullIn(EventBusDTO input);

        public async Task<int> CreateManyDebts(List<DebtDto> debts)
        {
            var insertList = debts.Adapt<List<Debt>>();
            await _debtRepository.InertManyAsync(insertList);
            return 1;
        }
    }
}