﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    public class BaseQueryInput
    {
        /// <summary>
        /// 当前登录用户Id
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 数据策略权限
        /// </summary>
        public StrategyQueryInput? StrategyQuery { get; set; }
        /// <summary>
        /// OA审批流查询Id
        /// </summary>
        public List<string>? OARequestIds { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string? Type { get; set; }
        /// <summary>
        /// 最后更新人
        /// </summary>
        public string? UpdatedBy { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 创建时间范围
        /// </summary>
        public long BeginCreatedTime { get; set; }
        /// <summary>
        /// 创建时间范围
        /// </summary>
        public long EndCreatedTime { get; set; }
        /// <summary>
        /// 起始更新时间
        /// </summary>
        public long BeginUpdatedTime { get; set; }
        /// <summary>
        /// 终止更新时间
        /// </summary>
        public long EndUpdatedTime { get; set; }
        /// <summary>
        /// 申请状态
        /// </summary>
        public int? Status { get; set; }
        public int? PageIndex { get; set; }
        public int? PageSize { get; set; }
    }
    public class StrategyQueryInput
    {
        public Guid? userId { get; set; }
        public string functionUri { get; set; }
    }
    public class StrategyQueryOutput
    {
        public Dictionary<string, List<string>> RowStrategies { get; set; } = new Dictionary<string, List<string>>();
        public Dictionary<string, string> ColumnStrategies { get; set; } = new Dictionary<string, string>();
    }
}
