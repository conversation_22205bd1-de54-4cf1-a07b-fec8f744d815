﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Linq;
using Org.BouncyCastle.Asn1.Cmp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 退款查询
    /// </summary>
    public class RefundQueryService : QueryAppService, IRefundQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _iBDSApiClient;
        private IPCApiClient _pCApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private ILogger<InvoiceQueryService> _logger;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="iBDSApiClient"></param>
        /// <param name="logisticsApiClient"></param>
        /// <param name="kingdeeApiClient"></param>
        /// <param name="pCApiClient"></param>
        /// <param name="logger"></param>
        public RefundQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db, IUnitOfWork unitOfWork, IBDSApiClient iBDSApiClient,
            ILogisticsApiClient logisticsApiClient,
            IKingdeeApiClient kingdeeApiClient, IPCApiClient pCApiClient,
            ILogger<InvoiceQueryService> logger) :
            base(contextAccessor)
        {
            this._db = db;
            this._iBDSApiClient = iBDSApiClient;
            this._pCApiClient = pCApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._logisticsApiClient = logisticsApiClient;
            this._unitOfWork = unitOfWork;
            this._kingdeeApiClient = kingdeeApiClient;
            this._logger = logger;
        }

        /// <summary>
        /// 查询待提交列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<(List<QueryPaymentRefundOutputData>, int)> GetListAsync(QueryPaymentRefundInput query)
        {
            Expression<Func<RefundItemPo, bool>> exp = z => 1 == 1;
            #region 查询条件 
            exp = await InitExp(query, exp);
            #endregion

            IQueryable<RefundItemPo> baseQuery = _db.RefundItem.Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();

            #region 排序
            baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).AsNoTracking().ToListAsync();

            var ids = list.Select(x => x.Id).ToList();
            var details = await _db.RefundDetails.Where(x => ids.Any(p => p == x.RefundItemId)).AsNoTracking().ToListAsync();

            var retList = new List<QueryPaymentRefundOutputData>();
            foreach (var item in list)
            {
                var single = ConvertToQueryPaymentRefundOutputData(item);
                var currentDetails = details.Where(x => x.RefundItemId == item.Id).ToList();
                var refundEntry = new List<QueryPaymentRefundDetailData>();
                var refundParty = 1;//我方退客商
                foreach (var cd in currentDetails)
                {
                    if (string.IsNullOrEmpty(cd.MinusNumber) && !string.IsNullOrEmpty(cd.ReceiveCode))
                    {
                        refundParty = 0;//客商退我方
                    }
                    refundEntry.Add(new QueryPaymentRefundDetailData
                    {
                        e_payeeamount = cd.RefundMoney.HasValue ? cd.RefundMoney.Value : 0,
                        jfzx_minusnumber = cd.MinusNumber,
                        paymentCode = cd.PaymentCode,
                        paymentAmount = cd.PaymentAmount,
                        receiveCode = cd.ReceiveCode,
                        receiveAmount = cd.ReceiveAmount
                    });
                }
                single.RefundParty = refundParty;
                single.refundEntry = refundEntry;
                retList.Add(single);
            }

            return (retList, count);
        }

        private async Task<Expression<Func<RefundItemPo, bool>>> InitExp(QueryPaymentRefundInput query, Expression<Func<RefundItemPo, bool>> exp)
        {
            if (query.status.HasValue)
            {
                exp = exp.And(x => x.Status == query.status.Value);
                if (query.status == Domain.RefundStatusEnum.Complate)
                {
                    // 没有退款类型的就是客商退我方
                    exp = exp.And(x => string.IsNullOrEmpty(x.RefundType));
                }
            }
            if (!string.IsNullOrEmpty(query.startDate) && !string.IsNullOrEmpty(query.endDate))
            {
                exp = exp.And(x => x.BillDate >= Convert.ToDateTime(query.startDate) && x.BillDate < Convert.ToDateTime(query.endDate).AddDays(1));
            }
            if (query.paymentNum != null && query.paymentNum.Any())
            {
                exp = exp.And(x => query.paymentNum.Any(p => p == x.NameCode));
            }
            if (query.orgNum != null && query.orgNum.Any())
            {
                exp = exp.And(x => query.orgNum.Any(p => p == x.BusinessDeptId));
            }
            if (query.customerNum != null && query.customerNum.Any())
            {
                exp = exp.And(x => x.CustomerId.HasValue && query.customerNum.Any(p => p == x.CustomerId.Value.ToString().ToUpper()));
            }
            if (!string.IsNullOrEmpty(query.searchKey))
            {
                exp = exp.And(x => !string.IsNullOrEmpty(x.BillCode) && query.searchKey.Contains(x.BillCode));
            }
            if (!string.IsNullOrEmpty(query.paidstatus))
            {
                //付款状态
            }
            return exp;
        }

        /// <summary>
        /// 转换
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        private static QueryPaymentRefundOutputData ConvertToQueryPaymentRefundOutputData(RefundItemPo po)
        {
            var refundType = string.Empty;
            if (po.RefundType == "203")
            {
                refundType = "负数应收";
            }
            else if (po.RefundType == "240")
            {
                refundType = "退返利款";
            }
            else if (po.RefundType == "204")
            {
                refundType = "退预收款";
            }
            else if (po.RefundType == "228")
            {
                refundType = "退回收到的押金";
            }
            else if (po.RefundType == "229")
            {
                refundType = "退回收到的保证金";
            }
            else if (po.RefundType == "246")
            {
                refundType = "供应商打错款";
            }
            else if (po.RefundType == "247")
            {
                refundType = "重新支付供应商";
            }
            var e_settlementtype = string.Empty;
            if (po.PayModel == "JSFS01")
            {
                e_settlementtype = "现金";
            }
            else if (po.PayModel == "JSFS02")
            {
                e_settlementtype = "现金支票";
            }
            else if (po.PayModel == "JSFS03")
            {
                e_settlementtype = "转账支票";
            }
            else if (po.PayModel == "JSFS04")
            {
                e_settlementtype = "电汇";
            }
            else if (po.PayModel == "JSFS05")
            {
                e_settlementtype = "信汇";
            }
            else if (po.PayModel == "JSFS06")
            {
                e_settlementtype = "商业承兑汇票";
            }
            else if (po.PayModel == "JSFS07")
            {
                e_settlementtype = "银行承兑汇票";
            }
            else if (po.PayModel == "JSFS08")
            {
                e_settlementtype = "即期信用证";
            }
            else if (po.PayModel == "JSFS09")
            {
                e_settlementtype = "应收票据背书";
            }
            else if (po.PayModel == "JSFS10")
            {
                e_settlementtype = "内部利息结算";
            }
            else if (po.PayModel == "JSFS11")
            {
                e_settlementtype = "集中结算";
            }
            else if (po.PayModel == "JSFS12")
            {
                e_settlementtype = "票据退票";
            }
            else if (po.PayModel == "JSFS-YXFY-01")
            {
                e_settlementtype = "营销费用账户";
            }
            else if (po.PayModel == "JSFS13")
            {
                e_settlementtype = "银企支付";
            }
            else if (po.PayModel == "JSFS15")
            {
                e_settlementtype = "银关通";
            }
            else if (po.PayModel == "JSFS14")
            {
                e_settlementtype = "远期信用证";
            }
            return new QueryPaymentRefundOutputData
            {
                id = po.Id,
                billno = po.BillCode,
                e_payee = po.CustomerName,
                e_payeeNum = po.CustomerId?.ToString().ToUpper(),
                org = po.BusinessDeptFullName,
                orgNum = po.BusinessDeptId,
                payment = po.CompanyName,
                paymentNum = po.NameCode,
                companyId = po.CompanyId?.ToString().ToUpper(),
                e_settlementtype = e_settlementtype,
                e_settlementtypeid = po.PayModel,
                e_payeeaccbanknum = po.BankAccount,
                paymenttype = refundType,
                payeeamount = po.RefundAllMoney,
                applydate = po.BillDate.HasValue ? po.BillDate.Value.ToString("yyyy-MM-dd") : string.Empty, //未生成单号，没有业务日期
                receivablesNumberList = string.IsNullOrEmpty(po.ReceivablesNumber) ? null : new List<string?> { po.ReceivablesNumber },
                billstatus = po.Status == Domain.RefundStatusEnum.waitSubmit ? "待提交" : "已完成",
                paidstatus = "",
                createtime = po.CreatedTime.ToString("yyyy-MM-dd"),
                creator = po.CreatedBy,
                applycause = po.Remark,
                projectCode = po.ProjectCode,
                projectId = po.ProjectId,
                projectName = po.ProjectName,
                nameCode = po.NameCode,
                e_payeetype=po.E_payeetype
            };
        }
    }
}
