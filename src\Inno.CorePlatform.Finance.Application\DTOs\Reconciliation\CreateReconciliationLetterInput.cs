﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 上传会函件入参
    /// </summary>
    public class CreateReconciliationLetterInput
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public  Guid CompanyId {  get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public  Guid CustomerId {  get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime Deadline {  get; set; }
        /// <summary>
        /// 截止日期范围
        /// </summary>
        public List<DateTime?>? DeadlineRange { get; set; }
        /// <summary>
        /// 对账函模板
        /// </summary>
        public ReconciliationLetterEnum ReconciliationLetterTemplate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark {  get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        public string? CurrentUser { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string? BusinessArea {  get; set; } 
        /// <summary>
        /// 操作类型
        /// </summary>
        public string? OperateType {  get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }
    }


}
