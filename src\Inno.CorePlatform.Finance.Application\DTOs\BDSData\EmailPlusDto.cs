﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class SendEmailInput
    {
        /// <summary>
        /// 开票申请单号
        /// </summary>
        public string CustomizeInvoiceCode { get; set; }
        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; }
        /// <summary>
        /// 邮件标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 发送接收人
        /// </summary>
        public List<string> Tos { get; set; } = new List<string>();
    }
    public class EmailPlusDto
    {
        [JsonPropertyName("bcc")]
        public List<string>? Bcc { get; set; } = new List<string>();

        [JsonPropertyName("cc")]
        public List<string>? Cc { get; set; } = new List<string>();

        [JsonPropertyName("companyId")]
        public string CompanyId { get; set; }

        [JsonPropertyName("companyName")]
        public string CompanyName { get; set; }

        [JsonPropertyName("content")]
        public string Content { get; set; }

        [JsonPropertyName("createdBy")]
        public string CreatedBy { get; set; }

        [JsonPropertyName("createdTime")]
        public long CreatedTime { get; set; }

        [JsonPropertyName("customers")]
        public List<IDAndNameInfo> Customers { get; set; } = new List<IDAndNameInfo>();

        [JsonPropertyName("mailType")]
        public string MailType { get; set; }


        [JsonPropertyName("projects")]
        public List<IDAndNameInfo> Projects { get; set; } = new List<IDAndNameInfo>();

        [JsonPropertyName("relatedId")]
        public string RelatedId { get; set; }


        [JsonPropertyName("subject")]
        public string Subject { get; set; }

        [JsonPropertyName("tos")]
        public List<string> Tos { get; set; } = new List<string>();
    }
    public class IDAndNameInfo
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }
    }
    public class EmailPlusResult
    {
        public string Code { get; set; }

        public string Message { get; set; }
    }
}
