﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class AdvanceRecordItemRepository : EfBaseRepository<Guid, AdvanceFundBusinessCheckItem, AdvanceFundBusinessCheckItemPO>, IAdvanceRecordItemRepository
    {
        private FinanceDbContext _db;
        public AdvanceRecordItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }
        public async Task<int> InsetrAdvance(AdvanceFundBusinessCheckItem AdvanceRecordItem, List<AdvanceFundBusinessCheckDetail> AdvanceFundBusinessCheckDetail)
        {
            try
            { 
                var AdvanceRecordItemPo = AdvanceRecordItem.Adapt<AdvanceFundBusinessCheckItemPO>();
                var AdvanceRecordDetailPo = AdvanceFundBusinessCheckDetail.Select(t => t.Adapt<AdvanceFundBusinessCheckDetailPO>()).ToList();
                AdvanceRecordItemPo.AdvanceRecordDetail = AdvanceRecordDetailPo;
                _db.AdvanceFundBusinessCheckItem.Add(AdvanceRecordItemPo);
                return await _db.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        public override async Task<int> UpdateAsync(AdvanceFundBusinessCheckItem root)
        {
            var isExist = await _db.AdvanceFundBusinessCheckItem.AnyAsync(x => x.Id == root.Id);
            if (isExist)
            {
                var po = root.Adapt<AdvanceFundBusinessCheckItemPO>();
                _db.AdvanceFundBusinessCheckItem.Update(po);
                if (UowJoined) return 0;
                return await _db.SaveChangesAsync();
            }
            else
            {
                return 0;
            }
        }

        protected override AdvanceFundBusinessCheckItemPO CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<AdvanceFundBusinessCheckItemPO> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
