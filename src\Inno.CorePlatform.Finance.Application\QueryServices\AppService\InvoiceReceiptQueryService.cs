﻿using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 发票入账单服务
    /// </summary>
    public class InvoiceReceiptQueryService : QueryAppService, IInvoiceReceiptQueryService
    {
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICodeGenClient _codeGenClient;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        private readonly IConfiguration _configuration;
        private readonly IWeaverApiClient _weaverApiClient;
        protected readonly IPCApiClient _pcApiClient;
        private readonly ICoordinateClient _coordinateclient;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="contextAccessor"></param>
        public InvoiceReceiptQueryService(FinanceDbContext db, IUnitOfWork unitOfWork, ICodeGenClient codeGenClient, PortInterfaces.Clients.IBDSApiClient bDSApiClient, IPCApiClient pcApiClient, IAppServiceContextAccessor? contextAccessor, IConfiguration configuration, ICoordinateClient coordinateclient,IWeaverApiClient weaverApiClient) : base(contextAccessor)
        {
            _db = db;
            _appServiceContextAccessor = contextAccessor;
            _unitOfWork = unitOfWork;
            _codeGenClient = codeGenClient;
            _bDSApiClient = bDSApiClient;
            _configuration = configuration;
            _weaverApiClient = weaverApiClient;
            _pcApiClient = pcApiClient;
            _coordinateclient = coordinateclient;
        }

        /// <summary>
        /// 发票入账单主体查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceReceiptItemQueryOutput>, int)> GetListAsync(InvoiceReceiptItemQueryInput input)
        {
            try
            {
                Expression<Func<InvoiceReceiptItemPo, bool>> exp = z => 1 == 1;

                #region 查询条件 
                input.Status ??= StatusEnum.waitSubmit;
                exp = await InitExp(input, exp);
                #endregion

                IQueryable<InvoiceReceiptItemPo> baseQuery = _db.InvoiceReceiptItem.Where(exp).AsNoTracking();
                #region 排序
                {
                    baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
                }
                #endregion
                //总条数
                var count = await baseQuery.CountAsync();
                var sql = baseQuery.ToQueryString();
                //分页
                var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<InvoiceReceiptItemQueryOutput>()).ToListAsync();
                return (list, count);
            }
            catch (Exception ex)
            {

                throw;
            }
        }

        private async Task<Expression<Func<InvoiceReceiptItemPo, bool>>> InitExp(InvoiceReceiptItemQueryInput input, Expression<Func<InvoiceReceiptItemPo, bool>> exp)
        {
            if (input.Status == StatusEnum.My)
            {
                var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.InvoiceReceiptForm.GetDescription()));
                var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                exp = exp.And(t => t.OARequestId != null && t.Status == StatusEnum.waitAudit && oARequestIds.Contains(t.OARequestId));
            }
            else if (input.Status == StatusEnum.waitSubmit)
            {
                exp = exp.And(z => z.Status == StatusEnum.waitSubmit && z.CreatedBy == input.UserName);
            }
            else
            {
                var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                {
                    Names = new List<string> { _appServiceContextAccessor.Get().UserName }
                });
                if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                if (user.Data.List.First().InstitutionType == 4)
                {
                    if (user.Data.List.First().Institutions.Any())
                    {
                        var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                        if (serviceIds != null)
                        {
                            exp = exp.And(t => serviceIds.Contains(t.ServiceId));
                        }
                    }
                }
                else
                {
                    #region 获取用户数据策略
                    //获取用户数据策略
                    var query = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                    var strategry = await _pcApiClient.GetStrategyAsync(query);
                    if (strategry != null)
                    {
                        var rowStrategies = strategry.RowStrategies;
                        if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                        {
                            exp = exp.And(z => 1 != 1);
                            return exp;
                        }
                        foreach (var key in strategry.RowStrategies.Keys)
                        {
                            if (key.ToLower() == "company")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                    exp = exp.And(t => strategList.Contains(t.CompanyId));
                                }
                            }
                            if (key.ToLower() == "customer")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                    exp = exp.And(t => strategList.Contains(t.CustomerId));
                                }
                            }
                            if (key == "service")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                    exp = exp.And(t => strategList.Contains(t.ServiceId));
                                }
                            }
                            if (key == "accountingDept")
                            {
                                if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                                {
                                    var strategList = strategry.RowStrategies[key].ToHashSet();
                                    exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                                }
                            }
                        }
                    }
                    #endregion
                }
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(z => z.CompanyId == input.CompanyId);
            }
            if (input.CustomerId.HasValue)
            {
                exp = exp.And(z => z.CustomerId == input.CustomerId);
            }
            if (!string.IsNullOrEmpty(input.BillDateBeging) && !string.IsNullOrEmpty(input.BillDateEnd))
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(long.Parse(input.BillDateBeging)).AddHours(8);
                var endTime = dateTime.AddMilliseconds(long.Parse(input.BillDateEnd)).AddHours(8);
                exp = exp.And(z => z.BillDate <= endTime && z.BillDate >= startTime);
                //exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(input.BillDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(input.BillDateBeging)));
            }
            if (input.Status.HasValue && input.Status != Domain.StatusEnum.all && input.Status != StatusEnum.My)
            {
                exp = exp.And(z => z.Status == input.Status);
            }
            if (input.CreatedBy != null && input.CreatedBy.Any())
            {
                exp = exp.And(z => input.CreatedBy.Contains(z.CreatedBy));
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                //var ids = _db.InvoiceReceiptDetail.Where(x => x.CreditCode == input.BillCode).Select(z => z.InvoiceReceiptItemId).ToList();
                //exp = exp.And(z => ids.Contains(z.Id));
                exp = exp.And(z => z.BillCode == input.BillCode);
            }
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                exp = exp.And(z => z.BillCode == input.searchKey);
            }
            if (!string.IsNullOrEmpty(input.CreditCode))
            {
                var ids = await _db.InvoiceReceiptDetail.Where(x => x.CreditCode == input.CreditCode).Select(x => x.InvoiceReceiptItemId).ToListAsync();
                exp = exp.And(z => ids.Contains(z.Id));
            }
            if (!string.IsNullOrEmpty(input.InvoiceNo))
            {
                var ids = await _db.InvoiceReceiptDetail.Where(x => x.InvoiceNo == input.InvoiceNo).Select(x => x.InvoiceReceiptItemId).ToListAsync();
                exp = exp.And(z => ids.Contains(z.Id));
            }
            return exp;
        }

        /// <summary>
        /// 根据入账单主体Id查询明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<InvoiceReceiptDetailVoOutput>, int)> GetDetailsByItemIdAsync(InvoiceReceiptDetailQueryInput input)
        {
            var Query = await (from ird in _db.InvoiceReceiptDetail
                               join c in _db.Credits on ird.CreditCode equals c.BillCode
                               select new InvoiceReceiptDetailVoOutput
                               {
                                   Id = ird.Id,
                                   InvoiceReceiptItemId = ird.InvoiceReceiptItemId,
                                   CreditCode = ird.CreditCode,
                                   InvoiceNo = ird.InvoiceNo,
                                   InvoiceCode = ird.InvoiceCode,
                                   InvoiceCheckCode = ird.InvoiceCheckCode,
                                   InvoiceTime = ird.InvoiceTime,
                                   InvoiceAmount = ird.InvoiceAmount,
                                   CreditAmount = c.Value,
                                   IsCancel = ird.IsCancel,
                                   Remark = ird.Remark,
                                   CompanyId = c.CompanyId,
                                   CompanyName = c.CompanyName,
                                   ServiceId = c.ServiceId,
                                   ServiceName = c.ServiceName,
                                   CustomerId = c.CustomerId,
                                   CustomerName = c.CustomerName,
                                   HospitalId = c.HospitalId,
                                   HospitalName = c.HospitalName
                               }).ToListAsync();

            #region 查询条件
            if (input.InvoiceReceiptItemId.HasValue)
            {
                Query = Query.Where(z => z.InvoiceReceiptItemId == input.InvoiceReceiptItemId).ToList();
            }
            else if (input.InvoiceReceiptItemIds != null && input.InvoiceReceiptItemIds.Any())
            {
                Query = Query.Where(z => input.InvoiceReceiptItemIds.ToHashSet().Contains(z.InvoiceReceiptItemId)).ToList();
            }
            else
            {
                Query = Query.Where(z => 1!=1).ToList();
            }
            #endregion
            #region 排序
            {
                Query = Query.OrderByDescending(z => z.CreatedTime).ToList();
            }
            #endregion
            //总条数
            var count = Query.Count();
            //分页
            var list = Query.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
            return (list, count);
        }

        /// <summary>
        /// 发票入账单创建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Create(InvoiceReceiptCreateInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.Id))
                {
                    input.Id = Guid.Empty.ToString();
                }
                var item = _db.InvoiceReceiptItem.FirstOrDefault(x => x.Id == Guid.Parse(input.Id));
                var invoiceReceiptItem = input.Adapt<InvoiceReceiptItemPo>();
                var invoiceCreditStrs = new List<string?>();
                foreach (var single in input.InvoiceDetail)
                {
                    invoiceCreditStrs.Add(single.InvoiceNo + single.CreditCode);
                }
                if (item != null)
                {
                    //编辑
                    if (item.CreatedBy != input.CreatedBy)
                    {
                        return BaseResponseData<string>.Failed(500, "只能操作本人数据");
                    }
                    item.CompanyId = input.CompanyId;
                    item.ServiceId = input.ServiceId;
                    item.BusinessDeptId = input.BusinessDeptId;
                    item.BusinessDeptFullPath = input.BusinessDeptFullPath;
                    item.BusinessDeptFullName = input.BusinessDeptFullName;
                    item.CompanyName = input.CompanyName;
                    item.ServiceName = input.ServiceName;
                    item.CustomerId = input.CustomerId;
                    item.CustomerName = input.CustomerName;
                    item.BackAmountDays = input.BackAmountDays.HasValue ? input.BackAmountDays.Value : 0;
                    item.SaleAccountPeriodDays = input.SaleAccountPeriodDays.HasValue? input.SaleAccountPeriodDays.Value : 0;
                    item.ActualBackAmountDays = input.ActualBackAmountDays;
                    item.CustomerId = input.CustomerId;
                    item.CustomerName = input.CustomerName;
                    item.Remark = input.Remark;
                    item.UpdatedBy = input.CreatedBy;
                    if (input.Attachment.Any())
                    {
                        var attachmentIds = input.Attachment.Select(x => x.attachFileId).ToList();
                        item.AttachFileIds = string.Join(",", attachmentIds);
                    }
                    var list = _db.InvoiceReceiptDetail.Where(x=>x.InvoiceReceiptItemId == Guid.Parse(input.Id)).ToList();
                    if (input.InvoiceDetail != null && input.InvoiceDetail.Any())
                    {
                        if (input.InvoiceDetail.DistinctBy(x => x.InvoiceNo + x.CreditCode).Count() != input.InvoiceDetail.Count())
                        {
                            return BaseResponseData<string>.Failed(500, "不能重复创建同一发票");
                        }
                        var cErrMsg = string.Empty;
                        var creditCodes = input.InvoiceDetail.Select(x => x.CreditCode).Distinct().ToList();
                        var credits = await _db.Credits.Where(x => creditCodes.Contains(x.BillCode)).ToListAsync();
                        foreach (var credit in credits)
                        {
                            if (credit.ServiceId != input.ServiceId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CustomerId != input.CustomerId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CompanyId != input.CompanyId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                        }
                        if (!string.IsNullOrEmpty(cErrMsg))
                        {
                            return BaseResponseData<string>.Failed(500, $"选择的发票中应收{cErrMsg}业务单元、客户或公司id与入帐单不一致");
                        }
                        var invoiceReceiptDetails = input.InvoiceDetail.Adapt<List<InvoiceReceiptDetailPo>>();
                        foreach (var detail in invoiceReceiptDetails)
                        {
                            var single = list.FirstOrDefault(x => x.Id == detail.Id);
                            if (single == null)
                            {
                                detail.InvoiceReceiptItemId = invoiceReceiptItem.Id;
                                detail.CreatedBy = input.CreatedBy;
                                detail.UpdatedBy = input.CreatedBy;
                                await _db.InvoiceReceiptDetail.AddAsync(detail);
                            }
                        }
                    }

                    _db.InvoiceReceiptItem.Update(item);
                }
                else
                {
                    //新增
                    #region 生辰单号
                    input.BusinessArea = "FXBD";
                    var dept = await _bDSApiClient.GetBusinessDeptById(input.BusinessDeptFullPath, "none", Guid.NewGuid().ToString());
                    if (dept != null && dept.Count > 0)
                    {
                        input.BusinessArea = dept[0].Value;
                    }
                    var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { input.CompanyId.ToString() } });
                    var companyInfo = companyInfoOutput?.FirstOrDefault();
                    if (companyInfo == null)
                    {
                        throw new AppServiceException("公司信息不存在");
                    }
                    invoiceReceiptItem.NameCode = companyInfo.nameCode;
                    invoiceReceiptItem.CompanyName = companyInfo.companyName;
                    if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
                    {
                        companyInfo.sysMonth = System.DateTime.Now.ToString("yyyy-MM");
                    }
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = input.BusinessArea,
                        CompanyCode = companyInfo.nameCode,
                        BillType = "OIB",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1
                    });
                    if (outPut.Status)
                    {
                        invoiceReceiptItem.BillCode = outPut.Codes.First();
                    }
                    invoiceReceiptItem.BillDate = System.DateTime.Now;
                    if (input.Attachment.Any())
                    {
                        var attachmentIds = input.Attachment.Select(x => x.attachFileId).ToList();
                        invoiceReceiptItem.AttachFileIds = string.Join(",", attachmentIds);
                    }
                    #endregion

                    invoiceReceiptItem.Id = Guid.NewGuid();
                    invoiceReceiptItem.CreatedBy = input.CreatedBy;
                    await _db.InvoiceReceiptItem.AddAsync(invoiceReceiptItem);
                    if (input.InvoiceDetail != null && input.InvoiceDetail.Any())
                    {
                        if (input.InvoiceDetail.DistinctBy(x => x.InvoiceNo + x.CreditCode).Count() != input.InvoiceDetail.Count())
                        {
                            return BaseResponseData<string>.Failed(500, "不能重复创建同一发票");
                        }
                        var cErrMsg = string.Empty;
                        var creditCodes = input.InvoiceDetail.Select(x => x.CreditCode).Distinct().ToList();
                        var credits = await _db.Credits.Where(x => creditCodes.Contains(x.BillCode)).ToListAsync();
                        foreach (var credit in credits)
                        {
                            if (credit.ServiceId != input.ServiceId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CustomerId != input.CustomerId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CompanyId != input.CompanyId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                        }
                        if (!string.IsNullOrEmpty(cErrMsg))
                        {
                            return BaseResponseData<string>.Failed(500, $"选择的发票中应收{cErrMsg}业务单元、客户或公司id与入帐单不一致");
                        }
                        var invoiceReceiptDetails = input.InvoiceDetail.Adapt<List<InvoiceReceiptDetailPo>>();
                        foreach (var detail in invoiceReceiptDetails)
                        {
                            if (!string.IsNullOrEmpty(detail.InvoiceNo))
                            {
                                detail.InvoiceReceiptItemId = invoiceReceiptItem.Id;
                                detail.CreatedBy = input.CreatedBy;
                                await _db.InvoiceReceiptDetail.AddAsync(detail);
                            }
                        }
                    }
                }

                if (invoiceCreditStrs != null && invoiceCreditStrs.Any())
                {
                    // 使用 HashSet 提高查找效率
                    var invoiceCreditSet = new HashSet<string>(invoiceCreditStrs);

                    // 查询数据库，确保只获取符合条件的数据
                    var invoiceCredits = await _db.InvoiceCredits
                        .Include(x => x.Credit)
                        .Where(x => invoiceCreditSet.Contains(x.InvoiceNo + x.Credit.BillCode))
                        .ToListAsync();

                    // 批量更新 IsInvoiceReceipt 字段
                    foreach (var invoiceCredit in invoiceCredits)
                    {
                        invoiceCredit.IsInvoiceReceipt = 1;
                    }
                }
                var i = await _unitOfWork.CommitAsync();
                return BaseResponseData<string>.Success("操作成功");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 发票入账单主体信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<InvoiceReceiptItemEditOutput>> GetItem([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            if (!input.InvoiceReceiptItemId.HasValue)
            {
                throw new Exception("id为空");
            }
            var model = new InvoiceReceiptItemEditOutput();
            var item = await _db.InvoiceReceiptItem.FirstOrDefaultAsync(x => x.Id == input.InvoiceReceiptItemId);
            if (item != null)
            {
                model = item.Adapt<InvoiceReceiptItemEditOutput>();
                model.InvoiceDetail = _db.InvoiceReceiptDetail.Where(x => x.InvoiceReceiptItemId == input.InvoiceReceiptItemId).Adapt<List<InvoiceReceiptDetailQueryOutput>>();
            }
            return BaseResponseData<InvoiceReceiptItemEditOutput>.Success(model, "查询成功");
        }

        /// <summary>
        /// 发票入账单提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Submit(InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var model = await _db.InvoiceReceiptItem.Include(x=>x.InvoiceReceiptDetails).FirstOrDefaultAsync(x => x.Id == input.InvoiceReceiptItemId);
                if (model != null)
                {
                    if (model.CreatedBy != input.UserId)
                    {
                        return BaseResponseData<string>.Failed(500, "提交失败，非本人创建的发票入账不能提交");
                    }
                    if (model.InvoiceReceiptDetails!=null && model.InvoiceReceiptDetails.Any())
                    {
                        var cErrMsg = string.Empty;
                        var creditCodes = model.InvoiceReceiptDetails.Select(x => x.CreditCode).Distinct().ToList();
                        var credits = await _db.Credits.Where(x => creditCodes.Contains(x.BillCode)).ToListAsync();
                        foreach (var credit in credits)
                        {
                            if (credit.ServiceId != model.ServiceId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CustomerId != model.CustomerId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                            else if (credit.CompanyId != model.CompanyId)
                            {
                                cErrMsg += "[" + credit.BillCode + "]";
                            }
                        }
                        if (!string.IsNullOrEmpty(cErrMsg))
                        {
                            return BaseResponseData<string>.Failed(500, $"详情中应收{cErrMsg}业务单元、客户或公司id与入帐单不一致");
                        }
                    }
                    var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                    {
                        BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                        {
                            Operator = model.CreatedBy,
                            RequestName = $"【财务-发票入账】[{model.BillCode}]-{model.CompanyName}-{model.CustomerName}",
                        },
                        MainData = new Gateway.Common.WeaverOA.MainData
                        {
                            FCreatorID = model.CreatedBy,
                            Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/InvoiceReceiptDetail?id={model.Id}", //PC的Iframe地址,
                            Height_m = 415,
                            Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/invoiceEntryEmbedPage?id={model.Id}",
                        
                            CpDepartment = model.BusinessDeptId,
                            CPcompanyCode = model.NameCode,
                            Condition = System.DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                            Business_id = model.Id.ToString()
                        },
                        OtherParams = new Gateway.Common.WeaverOA.OtherParams
                        {
                            IsNextFlow = 1,
                        }
                    };
                    if (string.IsNullOrEmpty(model.OARequestId))
                    {
                        var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.InvoiceReceiptForm);
                        if (!oaRet.Status)
                        {
                            return BaseResponseData<string>.Failed(500, oaRet.Msg);
                        }
                        model.OARequestId = oaRet.Data.Requestid.ToString();
                    }
                    else
                    {
                        oaInput.BaseInfo.RequestId = int.Parse(model.OARequestId);
                        var oaRet = await _weaverApiClient.SubmitWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.InvoiceReceiptForm);
                        if (!oaRet.Status)
                        {
                            return BaseResponseData<string>.Failed(500, oaRet.Msg);
                        }
                    }
                    model.Status = Domain.StatusEnum.waitAudit;
                    _db.InvoiceReceiptItem.Update(model);
                }
                await _unitOfWork.CommitAsync();
                return BaseResponseData<string>.Success("提交成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 撤回开票单
        /// </summary>
        public async Task<(int, string)> Cancel(InvoiceReceiptCancelInput input)
        {
            try
            {
                var invoiceReceiptItem = await _db.InvoiceReceiptItem.Where(p => p.Id == input.InvoiceReceiptItemId).AsNoTracking().FirstOrDefaultAsync();
                if (invoiceReceiptItem != null)
                {
                    if (invoiceReceiptItem.CreatedBy != input.CreatedBy)
                    {
                        return (0, "操作失败，原因：只能操作本人数据");
                    }
                    var ret = await _weaverApiClient.Withdraw(input.CreatedBy, long.Parse(invoiceReceiptItem.OARequestId), "");
                    if (ret.Status)
                    {
                        invoiceReceiptItem.Status = StatusEnum.waitSubmit;
                        _db.InvoiceReceiptItem.Update(invoiceReceiptItem);
                        await _unitOfWork.CommitAsync();
                        return (1, "操作成功！");
                    }
                    else { return (0, ret.Msg ?? "流程已开始审核，无法撤回！"); }
                }
                else
                {
                    return (0, "操作失败，原因：未找到数据");
                }
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }

        /// <summary>
        /// 撤回开票单
        /// </summary>
        public async Task<(int, string)> ChangeStatus(InvoiceReceiptCancelInput input)
        {
            try
            {
                var invoiceReceiptItem = await _db.InvoiceReceiptItem.Where(p => p.Id == input.InvoiceReceiptItemId).AsNoTracking().FirstOrDefaultAsync();
                if (invoiceReceiptItem != null)
                {
                    if (input.Status.HasValue)
                    {
                        invoiceReceiptItem.Status = input.Status.Value;
                        invoiceReceiptItem.ApprovalTime = System.DateTime.Now;
                        _db.InvoiceReceiptItem.Update(invoiceReceiptItem);
                        await _unitOfWork.CommitAsync();
                        return (1, "操作成功！");
                    }
                    else { return (0, "操作失败！"); }
                }
                else
                {
                    return (0, "操作失败，原因：未找到数据");
                }
            }
            catch (Exception ex)
            {
                return (0, ex.Message);
            }
        }
        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(InvoiceReceiptAttachFileInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var model = await _db.InvoiceReceiptItem.FirstOrDefaultAsync(x => x.Id == input.InvoiceReceiptItemId);
            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(model.AttachFileIds))
            {
                foreach (var fildId in model.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            model.AttachFileIds = newAttachFileIds;
            _db.InvoiceReceiptItem.Update(model);
            await _unitOfWork.CommitAsync();
            ret.Data = newAttachFileIds;
            return ret;
        }


        /// <summary>
        /// 根据InvoiceReceiptItemId标记应付入账
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> MarkDebtDetailsByInvoiceReceiptItemId(Guid id)
        {
            //查询已完成发票入账的应付单
            var debtDetails = await (from iri in _db.InvoiceReceiptItem
                                               join c in _db.Credits on iri.CompanyId equals c.CompanyId
                                               where iri.ServiceId == c.ServiceId && iri.CustomerId == c.CustomerId
                                               join ird in _db.InvoiceReceiptDetail on iri.Id equals ird.InvoiceReceiptItemId
                                               where c.BillCode == ird.CreditCode
                                               join dd in _db.DebtDetails on c.Id equals dd.CreditId
                                               where iri.Status == StatusEnum.Complate && iri.Id == id
                                               select dd).ToListAsync();
            foreach (var item in debtDetails)
            {
                item.IsInvoiceReceipt = true;
            }
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("操作成功！");
        }

        /// <summary>
        /// 根据应收单号标记应付入账
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> MarkDebtDetailsByCreditBillCodes(List<string?> billCodes)
        {
            //查询已完成发票入账的应付单
            var debtDetails = await (from c in _db.Credits
                                     join dd in _db.DebtDetails on c.Id equals dd.CreditId
                                     where billCodes.Contains(c.BillCode)
                                     select dd).ToListAsync();
            foreach (var item in debtDetails)
            {
                item.IsInvoiceReceipt = true;
            }
            return BaseResponseData<string>.Success("操作成功！");
        }

        /// <summary>
        /// 发票入账删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Remove(InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var model = await _db.InvoiceReceiptItem.FirstOrDefaultAsync(x => x.Id == input.InvoiceReceiptItemId);
                if (model != null)
                {
                    if (model.CreatedBy != input.UserId)
                    {
                        return BaseResponseData<string>.Failed(500, "删除失败，非本人创建的发票入账不能删除");
                    }
                    var details = _db.InvoiceReceiptDetail.Where(x => x.InvoiceReceiptItemId == input.InvoiceReceiptItemId);
                    var icStr = details.Select(x => x.InvoiceNo + "$" + x.CreditCode).ToHashSet();
                    if (icStr != null && icStr.Any())
                    {
                        var invoiceCredits = await _db.InvoiceCredits.Include(x => x.Credit).Where(x => x.Credit != null && icStr.ToHashSet().Contains(x.InvoiceNo + "$" + x.Credit.BillCode)).ToListAsync();
                        invoiceCredits.ForEach(x =>
                        {
                            x.IsInvoiceReceipt = 0;
                        });
                    }
                    _db.InvoiceReceiptDetail.RemoveRange(details);
                    _db.InvoiceReceiptItem.Remove(model);
                }
                if (!string.IsNullOrEmpty(model?.OARequestId))
                {
                    //删除OA流程
                    await _weaverApiClient.DelWorkFlow(model.CreatedBy, Convert.ToInt32(model?.OARequestId));
                }
                    await _unitOfWork.CommitAsync();
              
                return BaseResponseData<string>.Success("删除成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 发票入账单下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<InvoiceReceiptItemQueryOutput>> DownLoad(InvoiceReceiptItemQueryInput input)
        {
            try
            {
                Expression<Func<InvoiceReceiptItemPo, bool>> exp = z => 1 == 1;

                #region 查询条件 
                input.Status ??= StatusEnum.waitSubmit;
                exp = await InitExp(input, exp);
                #endregion

                IQueryable<InvoiceReceiptItemPo> baseQuery = _db.InvoiceReceiptItem.Where(exp).AsNoTracking();
                #region 排序
                {
                    baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
                }
                #endregion
                //总条数
                var sql = baseQuery.ToQueryString();

                return await baseQuery.Select(z => z.Adapt<InvoiceReceiptItemQueryOutput>()).ToListAsync();
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 异步导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportByCoordinate(InvoiceReceiptItemQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.UserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_invoiceReceiptsExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.UserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("发票入账清单导出", System.DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }

        /// <summary>
        /// 发票明细删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<string>> DeleteDetails(List<Guid?> ids,string? userId)
        {
            var list = _db.InvoiceReceiptDetail.Where(x => ids.Contains(x.Id)).ToList();
            var icStr = list.Select(x => x.InvoiceNo + "$" + x.CreditCode).ToHashSet();
            if (icStr != null && icStr.Any())
            {
                var invoiceCredits = await _db.InvoiceCredits.Include(x => x.Credit).Where(x => x.Credit != null && icStr.ToHashSet().Contains(x.InvoiceNo + "$" + x.Credit.BillCode)).ToListAsync();
                invoiceCredits.ForEach(x =>
                {
                    x.IsInvoiceReceipt = 0;
                });
            }
            _db.InvoiceReceiptDetail.RemoveRange(list);
            foreach (var item in list)
            {
                if (item.CreatedBy != userId)
                {
                    return BaseResponseData<string>.Failed(500, "删除失败，非本人添加的发票明细不能删除");
                }
            }
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("提交成功");
        }

        /// <summary>
        /// 发票入账页面数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<InvoiceReceiptListTabOutput>> GetTabCount(InvoiceReceiptItemQueryInput query)
        {
            var ret = BaseResponseData<InvoiceReceiptListTabOutput>.Success("操作成功");
            query.Status = StatusEnum.all;
            query.limit = 9999;
            var (list, count) = await GetListAsync(query);
            var data = new InvoiceReceiptListTabOutput();
            var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(query?.UserName ?? default, WorkFlowCode.InvoiceReceiptForm.GetDescription()));
            var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
            data.WaitSubmitCount = list.Where(x => x.Status == StatusEnum.waitSubmit && x.CreatedBy == query.UserName).Count();
            data.WaitAuditCount = list.Where(x => x.Status == StatusEnum.waitAudit).Count();
            data.ComplateCount = list.Where(x => x.Status == StatusEnum.Complate).Count();
            data.AllCount = list.Count();
            query.Status = StatusEnum.My;
            var (mylist, mycount) = await GetListAsync(query);
            data.MyCount = mylist.Where(t => t.OARequestId != null && t.Status == StatusEnum.waitAudit && oARequestIds.Contains(t.OARequestId)).Count();
            ret.Data = data;
            return ret;
        }

        /// <summary>
        /// 根据公司单号获取业务单元
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<InvoiceReceiptsSelectAssemblyOutput>> GetServices(InvoiceReceiptsSelectAssemblyInput input)
        {
            //Expression<Func<AdvanceBusinessApplyPO, bool>> exp = z => 1 == 1;
            //exp = await InitSelectExp(input, exp);
            //IQueryable<AdvanceBusinessApplyPO> baseQuery = _db.AdvanceBusinessApply.Where(exp).AsNoTracking();
            //return await baseQuery.Where(x=>x.CompanyId == input.CompanyId && x.IsInvoice).Select(x=> new InvoiceReceiptsSelectAssemblyOutput
            //{
            //    id = x.ServiceId.ToString(),
            //    name = x.ServiceName
            //}).Distinct().ToListAsync();

            return await (from aba in _db.AdvanceBusinessApply
                          where aba.CompanyId == input.CompanyId
                          select new InvoiceReceiptsSelectAssemblyOutput
                          {
                              id = aba.ServiceId.ToString(),
                              name = aba.ServiceName
                          }).Distinct().ToListAsync();
        }

        /// <summary>
        /// 根据公司单号业务单元获取客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<InvoiceReceiptsSelectAssemblyOutput>> GetCustomers(InvoiceReceiptsSelectAssemblyInput input)
        {
            Expression<Func<AdvanceBusinessApplyPO, bool>> exp = z => 1 == 1;
            exp = await InitSelectExp(input, exp);
            IQueryable<AdvanceBusinessApplyPO> baseQuery = _db.AdvanceBusinessApply.Where(exp).AsNoTracking();
            return await baseQuery.Where(x => x.ServiceId == input.ServiceId && x.CompanyId == input.CompanyId && x.IsInvoice).Select(x => new InvoiceReceiptsSelectAssemblyOutput
            {
                id = x.HospitalId.ToString(),
                name = x.HospitalName
            }).Distinct().ToListAsync();
        }

        private async Task<Expression<Func<AdvanceBusinessApplyPO, bool>>> InitSelectExp(InvoiceReceiptsSelectAssemblyInput input, Expression<Func<AdvanceBusinessApplyPO, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
            }
            #region 获取用户数据策略
            //获取用户数据策略
            var query = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(query);
            if (strategry != null && strategry.RowStrategies.Count > 0)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                {
                    exp = exp.And(z => 1 != 1);
                }
                else
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.HospitalId));
                            }
                        }
                        if (key == "service")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.ServiceId.Value));
                            }
                        }
                        if (key == "accountingDept")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                            }
                        }
                    }
                }
            }
            #endregion
            return exp;
        }

        /// <summary>
        /// 获取回款天数等数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<InvoiceReceiptsDayOutput> GetDayData(InvoiceReceiptsSelectAssemblyInput input)
        {
            return await(from aba in _db.AdvanceBusinessApply
                         where aba.CompanyId == input.CompanyId && aba.ServiceId == input.ServiceId && aba.HospitalId == input.CustomerId
                         select new InvoiceReceiptsDayOutput
                         {
                             BackAmountDays = aba.ReturnMoneyDays,
                             SaleAccountPeriodDays = aba.ProvidePayDays
                         }).FirstOrDefaultAsync();
        }
    }
}
