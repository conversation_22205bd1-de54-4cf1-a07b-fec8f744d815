﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.Services.MatchCache;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.Text.RegularExpressions;

namespace Inno.CorePlatform.Finance.Application.Services.MatchLogic
{
    /// <summary>
    /// 匹配结果
    /// </summary>
    public class MatchResult
    {
        /// <summary>
        /// 是否所有明细都已匹配
        /// </summary>
        public bool AllMatched { get; set; }

        /// <summary>
        /// 匹配的项目
        /// </summary>
        public List<MatchableDocumentItem> MatchedItems { get; set; } = new List<MatchableDocumentItem>();

        /// <summary>
        /// 未匹配的明细
        /// </summary>
        public List<InvoiceDetailItem> UnmatchedDetails { get; set; } = new List<InvoiceDetailItem>();
    }

    /// <summary>
    /// 匹配逻辑服务
    /// </summary>
    public class MatchLogicService
    {
        private readonly IFinanceDbContext _db;
        private readonly IMatchCacheManager _cacheManager;
        private readonly IManyInventoryApiClient _manyInventoryApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IInputBillQueryService _inputBillQueryService;
        private readonly ILogger<MatchLogicService> _logger;

        // 红票业务类型
        private static readonly BusinessType[] RedBusinessTypes = new[]
        {
            BusinessType.DistributionTransfer,
            BusinessType.PurchaseRevision,
            BusinessType.ExchangeToReturn,
            BusinessType.LossRecognition
        };

        // 蓝票业务类型
        private static readonly BusinessType[] BlueBusinessTypes = new[]
        {
            BusinessType.DistributionPurchase,
            BusinessType.ConsignmentToPurchase,
            BusinessType.ServiceFeeProcurement
        };

        // 业务类型数据获取工厂
        private readonly Dictionary<BusinessType, Func<GetMatchableDocumentsRequest, Task<List<MatchableDocumentItem>>>> _businessTypeDataFetchers;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="cacheManager">缓存管理器</param>
        /// <param name="manyInventoryApiClient">多对一勾稽库存能力中心客户端</param>
        /// <param name="purchaseApiClient">采购能力中心客户端</param>
        /// <param name="inputBillQueryService">进项票查询服务</param>
        /// <param name="logger">日志记录器</param>
        public MatchLogicService(
            IFinanceDbContext db,
            IMatchCacheManager cacheManager,
            IManyInventoryApiClient manyInventoryApiClient,
            IPurchaseApiClient purchaseApiClient,
            IInputBillQueryService inputBillQueryService,
            ILogger<MatchLogicService> logger)
        {
            _db = db;
            _cacheManager = cacheManager;
            _manyInventoryApiClient = manyInventoryApiClient;
            _purchaseApiClient = purchaseApiClient;
            _inputBillQueryService = inputBillQueryService;
            _logger = logger;

            // 初始化业务类型数据获取工厂
            _businessTypeDataFetchers = new Dictionary<BusinessType, Func<GetMatchableDocumentsRequest, Task<List<MatchableDocumentItem>>>>
            {
                { BusinessType.DistributionPurchase, FetchDistributionPurchaseData },
                { BusinessType.ConsignmentToPurchase, FetchConsignmentToPurchaseData },
                { BusinessType.ServiceFeeProcurement, FetchServiceFeeProcurementData },
                { BusinessType.DistributionTransfer, FetchDistributionTransferData },
                { BusinessType.PurchaseRevision, FetchPurchaseRevisionData },
                { BusinessType.ExchangeToReturn, FetchExchangeToReturnData },
                { BusinessType.LossRecognition, FetchLossRecognitionData }
            };
        }


        /// <summary>
        /// 执行自动匹配逻辑，先从接口拉取数据并写入缓存，然后依次执行货号维度和品名维度的匹配
        /// </summary>
        /// <param name="redDetails">红票明细</param>
        /// <param name="blueDetails">蓝票明细</param>
        /// <param name="request">请求参数</param>
        private async Task ExecuteAutoMatchLogic(
            List<InvoiceDetailItem> redDetails,
            List<InvoiceDetailItem> blueDetails,
            GetMatchableDocumentsRequest request)
        {
            _logger.LogInformation("开始执行自动匹配逻辑, 红票数量: {RedCount}, 蓝票数量: {BlueCount}",
                redDetails.Count, blueDetails.Count);

            // 1. 预先拉取所需业务类型的数据并缓存
            await PreloadBusinessTypeData(request);

            // 2. 处理红票 - 先货号匹配，再品名匹配，跳过购货修订
            if (redDetails.Count > 0)
            {
                // 过滤掉购货修订和损失确认业务类型
                var autoMatchRedBusinessTypes = RedBusinessTypes.Where(x => x != BusinessType.PurchaseRevision && x != BusinessType.LossRecognition).ToArray();
                await ProcessAutoMatchForTicketType(redDetails, request, autoMatchRedBusinessTypes, false, "红票");
            }

            // 3. 处理蓝票 - 先货号匹配，再品名匹配，跳过服务费
            if (blueDetails.Count > 0)
            {
                // 过滤掉服务费业务类型
                var autoMatchBlueBusinessTypes = BlueBusinessTypes.Where(x => x != BusinessType.ServiceFeeProcurement).ToArray();
                await ProcessAutoMatchForTicketType(blueDetails, request, autoMatchBlueBusinessTypes, true, "蓝票");
            }

            _logger.LogInformation("自动匹配逻辑执行完成");
        }

        /// <summary>
        /// 预先加载所需业务类型的数据并缓存
        /// </summary>
        /// <param name="request">请求参数</param>
        private async Task PreloadBusinessTypeData(GetMatchableDocumentsRequest request)
        {
            _logger.LogInformation("开始预加载业务类型数据");

            // 分析明细，识别红票和蓝票
            var (redDetails, blueDetails) = AnalyzeInvoiceDetails(request.InvoiceDetails);

            // 从缓存中获取查询条件，用于更新接口调用状态
            var queryCondition = _cacheManager.GetQueryConditionFromCache(request.MergeInputBillId);
            if (queryCondition == null)
            {
                // 如果缓存中没有查询条件，使用当前请求作为查询条件
                queryCondition = request;
                _logger.LogInformation("缓存中没有查询条件，使用当前请求作为查询条件");
            }

            // 确保接口更新状态字典已初始化
            if (queryCondition.InterfaceUpdateStatus == null)
            {
                queryCondition.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                _logger.LogInformation("初始化接口更新状态字典");
            }

            // 只有在有红票明细时才加载红票业务类型数据
            if (redDetails.Count > 0)
            {
                _logger.LogInformation("检测到红票明细，加载红票业务类型数据");
                // 过滤掉购货修订和损失确认业务类型，自动匹配不需要考虑购货修订和损失确认
                var autoMatchRedBusinessTypes = RedBusinessTypes.Where(x => x != BusinessType.PurchaseRevision && x != BusinessType.LossRecognition).ToArray();
                foreach (var businessType in autoMatchRedBusinessTypes)
                {
                    _logger.LogInformation("加载红票业务类型数据: {BusinessType}, 支持自动匹配", businessType);

                    var data = await FetchBusinessTypeData(request, businessType);
                    _cacheManager.SetBusinessTypeCache(request.MergeInputBillId, businessType, data);

                    // 更新接口调用状态
                    queryCondition.InterfaceUpdateStatus[businessType] = true;

                    _logger.LogInformation("已加载红票业务类型数据: {BusinessType}, 数据量: {Count}, 已更新接口调用状态",
                        businessType, data.Count);
                }
            }
            else
            {
                _logger.LogInformation("未检测到红票明细，跳过红票业务类型数据加载");
            }

            // 只有在有蓝票明细时才加载蓝票业务类型数据
            if (blueDetails.Count > 0)
            {
                _logger.LogInformation("检测到蓝票明细，加载蓝票业务类型数据");
                // 过滤掉服务费业务类型，自动匹配不需要考虑服务费
                var autoMatchBlueBusinessTypes = BlueBusinessTypes.Where(x => x != BusinessType.ServiceFeeProcurement).ToArray();
                foreach (var businessType in autoMatchBlueBusinessTypes)
                {
                    _logger.LogInformation("加载蓝票业务类型数据: {BusinessType}, 支持自动匹配", businessType);

                    var data = await FetchBusinessTypeData(request, businessType);
                    _cacheManager.SetBusinessTypeCache(request.MergeInputBillId, businessType, data);

                    // 更新接口调用状态
                    queryCondition.InterfaceUpdateStatus[businessType] = true;

                    _logger.LogInformation("已加载蓝票业务类型数据: {BusinessType}, 数据量: {Count}, 已更新接口调用状态",
                        businessType, data.Count);
                }
            }
            else
            {
                _logger.LogInformation("未检测到蓝票明细，跳过蓝票业务类型数据加载");
            }

            // 保存更新后的查询条件到缓存
            _cacheManager.SaveQueryConditionToCache(request.MergeInputBillId, queryCondition);
            _logger.LogInformation("已保存更新后的查询条件到缓存，业务类型数据加载完成");
        }

        /// <summary>
        /// 在自动匹配中处理特定类型的票据
        /// </summary>
        /// <param name="details">票据明细</param>
        /// <param name="request">请求参数</param>
        /// <param name="businessTypes">业务类型数组</param>
        /// <param name="isPositiveQuantity">是否为正数数量（true=蓝票，false=红票）</param>
        /// <param name="ticketTypeName">票据类型名称（用于日志）</param>
        private async Task ProcessAutoMatchForTicketType(
            List<InvoiceDetailItem> details,
            GetMatchableDocumentsRequest request,
            BusinessType[] businessTypes,
            bool isPositiveQuantity,
            string ticketTypeName)
        {
            _logger.LogInformation("开始处理{TicketType}自动匹配, 明细数量: {Count}", ticketTypeName, details.Count);

            // 1. 先使用货号维度匹配
            var productNoRequest = CreateMatchRequest(request, MatchPrecisionEnum.ProductNo);
            var productNoResult = await ProcessDetailsWithResult(details, productNoRequest, isPositiveQuantity, businessTypes);

            // 2. 如果有未匹配的明细，再使用品名维度匹配
            if (productNoResult.UnmatchedDetails.Count > 0)
            {
                _logger.LogInformation("{TicketType}货号维度匹配后未匹配数量: {Count}, 开始品名维度匹配",
                    ticketTypeName, productNoResult.UnmatchedDetails.Count);

                // 为了避免数据重复，需要重新从缓存获取最新的可匹配数据
                // 货号维度匹配后，可匹配数量已经被更新
                var productNameRequest = CreateMatchRequest(request, MatchPrecisionEnum.ProductName);

                // 使用未匹配的明细进行品名维度匹配
                await ProcessDetailsWithResult(productNoResult.UnmatchedDetails, productNameRequest, isPositiveQuantity, businessTypes);
            }
            else
            {
                _logger.LogInformation("{TicketType}货号维度匹配已完全匹配，无需进行品名维度匹配", ticketTypeName);
            }
        }

        /// <summary>
        /// 创建匹配请求对象
        /// </summary>
        /// <param name="baseRequest">基础请求对象</param>
        /// <param name="matchPrecision">匹配维度</param>
        /// <returns>新的请求对象</returns>
        private static GetMatchableDocumentsRequest CreateMatchRequest(GetMatchableDocumentsRequest baseRequest, MatchPrecisionEnum matchPrecision)
        {
            return new GetMatchableDocumentsRequest
            {
                MergeInputBillId = baseRequest.MergeInputBillId,
                MergeInputBillDetailId = baseRequest.MergeInputBillDetailId,
                CompanyId = baseRequest.CompanyId,
                StartDate = baseRequest.StartDate,
                EndDate = baseRequest.EndDate,
                AgentId = baseRequest.AgentId,
                MatchType = baseRequest.MatchType,
                BusinessType = baseRequest.BusinessType,
                MatchPrecision = matchPrecision,
                InvoiceDetails = baseRequest.InvoiceDetails
            };
        }

        /// <summary>
        /// 执行匹配逻辑
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>获取可勾稽单据列表响应</returns>
        public async Task<GetMatchableDocumentsResponse> ExecuteMatchLogic(GetMatchableDocumentsRequest request)
        {
            _logger.LogInformation("开始执行匹配逻辑, MergeInputBillId: {MergeInputBillId}",
                request.MergeInputBillId);

            try
            {
                // 查询合并进项发票并初始化请求参数
                var mergeInputBill = await GetAndInitializeMergeInputBill(request);

                // 分析明细，识别红票和蓝票
                var (redDetails, blueDetails) = AnalyzeInvoiceDetails(request.InvoiceDetails);
                _logger.LogInformation("分析明细完成: 红票数量={RedCount}, 蓝票数量={BlueCount}",
                    redDetails.Count, blueDetails.Count);

                // 保存查询条件到Redis缓存
                _logger.LogInformation("保存查询条件到Redis缓存, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                _cacheManager.SaveQueryConditionToCache(request.MergeInputBillId, request);

                // 执行匹配逻辑
                if (request.MatchPrecision == MatchPrecisionEnum.ProductName || request.MatchPrecision == MatchPrecisionEnum.ProductNo)
                {
                    // 如果指定了匹配维度，则使用指定维度匹配逻辑
                    _logger.LogInformation("执行指定维度匹配: {MatchPrecision}", request.MatchPrecision);
                    await ExecuteSpecificMatchLogic(redDetails, blueDetails, request);
                }
                else
                {
                    // 如果没有指定匹配维度，则使用自动匹配逻辑
                    _logger.LogInformation("执行自动匹配逻辑");
                    await ExecuteAutoMatchLogic(redDetails, blueDetails, request);
                }

                // 从缓存中获取匹配结果
                var allItems = GetAllMatchedItemsFromCache(request.MergeInputBillId);
                return new GetMatchableDocumentsResponse
                {
                    Items = allItems
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行匹配逻辑失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);

                // 返回空列表而不是抛出异常，避免前端报错
                return new GetMatchableDocumentsResponse
                {
                    Items = new List<MatchableDocumentItem>()
                };
            }
        }

        /// <summary>
        /// 获取并初始化合并进项发票
        /// </summary>
        private async Task<MergeInputBillPo> GetAndInitializeMergeInputBill(GetMatchableDocumentsRequest request)
        {
            // 查询合并进项发票
            var mergeInputBill = await _db.MergeInputBills
                .Include(x => x.MergeInputBillDetail)
                .Where(x => x.Id == request.MergeInputBillId)
                .FirstOrDefaultAsync();

            if (mergeInputBill == null)
            {
                throw new ArgumentException("合并进项发票不存在");
            }

            // 如果请求中没有设置公司ID和供应商ID，则从合并进项发票中获取
            if (request.CompanyId == Guid.Empty)
            {
                request.CompanyId = mergeInputBill.CompanyId;
            }

            if (!request.AgentId.HasValue && mergeInputBill.AgentId.HasValue)
            {
                request.AgentId = mergeInputBill.AgentId;
            }

            // 如果请求中没有发票明细，则从合并进项发票明细中获取
            if (request.InvoiceDetails == null || request.InvoiceDetails.Count == 0)
            {
                // 确保 MergeInputBillDetail 不为 null
                if (mergeInputBill.MergeInputBillDetail != null)
                {
                    request.InvoiceDetails = mergeInputBill.MergeInputBillDetail.Select(x => new InvoiceDetailItem
                    {
                        Id = x.Id,
                        InvoiceName = x.ProductName,
                        ProductName = ParseProductName(x.ProductName),
                        ProductNo = x.ProductNo,
                        Specification = x.ProductNo, // 规格对应货号
                        Quantity = x.Quantity,
                        TaxCost = x.TaxCost,
                        TaxRate = x.TaxRate
                    }).ToList();
                }
                else
                {
                    throw new ApplicationException("开票明细不能为空");
                }
            }

            return mergeInputBill;
        }

        /// <summary>
        /// 分析发票明细，识别红票和蓝票
        /// </summary>
        private static (List<InvoiceDetailItem> redDetails, List<InvoiceDetailItem> blueDetails) AnalyzeInvoiceDetails(List<InvoiceDetailItem> details)
        {
            if (details == null || details.Count == 0)
            {
                return (new List<InvoiceDetailItem>(), new List<InvoiceDetailItem>());
            }

            var redDetails = details.Where(x => x.Quantity < 0).ToList();
            var blueDetails = details.Where(x => x.Quantity > 0).ToList(); // 不包含数量为0的明细

            return (redDetails, blueDetails);
        }

        /// <summary>
        /// 执行指定匹配维度的匹配逻辑
        /// </summary>
        private async Task ExecuteSpecificMatchLogic(
            List<InvoiceDetailItem> redDetails,
            List<InvoiceDetailItem> blueDetails,
            GetMatchableDocumentsRequest request)
        {
            // 创建一个新的请求对象
            var matchRequest = CreateMatchRequest(request, request.MatchPrecision);

            // 处理红票
            if (redDetails.Count > 0)
            {
                await ProcessRedDetailsWithResult(redDetails, matchRequest);
            }

            // 处理蓝票
            if (blueDetails.Count > 0)
            {
                await ProcessBlueDetailsWithResult(blueDetails, matchRequest);
            }
        }

        /// <summary>
        /// 从缓存中获取所有匹配项
        /// </summary>
        private List<MatchableDocumentItem> GetAllMatchedItemsFromCache(Guid mergeInputBillId)
        {
            var allItems = new List<MatchableDocumentItem>();

            try
            {
                // 获取红票业务类型的匹配项目
                foreach (var businessType in RedBusinessTypes)
                {
                    var result = _cacheManager.GetMatchResultFromCache(mergeInputBillId, businessType);
                    if (result != null && result.MatchedItems != null && result.MatchedItems.Count > 0)
                    {
                        allItems.AddRange(result.MatchedItems);
                    }
                }

                // 获取蓝票业务类型的匹配项目
                foreach (var businessType in BlueBusinessTypes)
                {
                    var result = _cacheManager.GetMatchResultFromCache(mergeInputBillId, businessType);
                    if (result != null && result.MatchedItems != null && result.MatchedItems.Count > 0)
                    {
                        allItems.AddRange(result.MatchedItems);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从缓存中获取匹配项失败: {ErrorMessage}", ex.Message);
                // 出现异常时返回空列表
            }

            return allItems;
        }

        /// <summary>
        /// 处理票据匹配，返回匹配结果
        /// </summary>
        /// <param name="details">票据明细</param>
        /// <param name="request">请求参数</param>
        /// <param name="isPositiveQuantity">是否为正数数量（true=蓝票，false=红票）</param>
        /// <param name="businessTypes">要处理的业务类型数组</param>
        /// <returns>匹配结果，包含匹配项目和未匹配明细</returns>
        private async Task<MatchResult> ProcessDetailsWithResult(
            List<InvoiceDetailItem> details,
            GetMatchableDocumentsRequest request,
            bool isPositiveQuantity,
            BusinessType[] businessTypes)
        {
            string ticketType = isPositiveQuantity ? "蓝票" : "红票";
            _logger.LogInformation("开始处理{TicketType}匹配, 明细数量: {Count}, 匹配维度: {MatchPrecision}",
                ticketType, details.Count, request.MatchPrecision);

            var allItems = new List<MatchableDocumentItem>();

            // 从缓存中获取所有业务类型的数据
            foreach (var businessType in businessTypes)
            {
                var data = await GetBusinessTypeData(request, businessType);
                allItems.AddRange(data);
            }
            var result = MatchDetails(details, allItems, request, isPositiveQuantity);

            // 如果没有匹配结果，创建一个空的结果
            if (result == null)
            {
                result = new MatchResult { MatchedItems = allItems, UnmatchedDetails = details };
            }

            // 注意：不需要在这里保存匹配结果到缓存，因为MatchDetails方法已经处理了缓存保存

            _logger.LogInformation("{TicketType}匹配完成, 匹配项数量: {MatchedCount}, 未匹配明细数量: {UnmatchedCount}",
                ticketType, result.MatchedItems.Count, result.UnmatchedDetails.Count);
            return result;
        }

        /// <summary>
        /// 处理红票匹配，返回匹配结果
        /// </summary>
        /// <param name="redDetails">红票明细</param>
        /// <param name="request">请求参数</param>
        /// <returns>匹配结果，包含匹配项目和未匹配明细</returns>
        private Task<MatchResult> ProcessRedDetailsWithResult(
            List<InvoiceDetailItem> redDetails,
            GetMatchableDocumentsRequest request)
        {
            return ProcessDetailsWithResult(redDetails, request, false, RedBusinessTypes);
        }

        /// <summary>
        /// 处理蓝票匹配，返回匹配结果
        /// </summary>
        /// <param name="blueDetails">蓝票明细</param>
        /// <param name="request">请求参数</param>
        /// <returns>匹配结果，包含匹配项目和未匹配明细</returns>
        private Task<MatchResult> ProcessBlueDetailsWithResult(
            List<InvoiceDetailItem> blueDetails,
            GetMatchableDocumentsRequest request)
        {
            return ProcessDetailsWithResult(blueDetails, request, true, BlueBusinessTypes);
        }



        /// <summary>
        /// 获取业务类型数据
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>业务类型数据</returns>
        private Task<List<MatchableDocumentItem>> GetBusinessTypeData(GetMatchableDocumentsRequest request, BusinessType businessType)
        {
            try
            {
                // 从缓存中获取数据
                var cacheData = _cacheManager.GetBusinessTypeCache(request.MergeInputBillId, businessType);
                if (cacheData != null && cacheData.Count > 0)
                {
                    return Task.FromResult(cacheData);
                }

                // 如果缓存中没有数据，返回空列表
                // 在ExecuteAutoMatchLogic方法中已经预先从接口拉取数据并写入缓存
                return Task.FromResult(new List<MatchableDocumentItem>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取业务类型数据失败, 业务类型: {BusinessType}, 错误: {ErrorMessage}",
                    businessType, ex.Message);
                // 返回空列表而不是抛出异常
                return Task.FromResult(new List<MatchableDocumentItem>());
            }
        }

        /// <summary>
        /// 调用接口获取业务类型数据
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>业务类型数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchBusinessTypeData(GetMatchableDocumentsRequest request, BusinessType businessType)
        {
            try
            {
                // 使用工厂模式获取对应的数据获取方法
                if (!_businessTypeDataFetchers.TryGetValue(businessType, out var fetcher))
                {
                    _logger.LogWarning("不支持的业务类型: {BusinessType}", businessType);
                    return new List<MatchableDocumentItem>();
                }

                // 调用对应的数据获取方法
                var result = await fetcher(request);

                // 确保结果不为null
                if (result == null)
                {
                    result = new List<MatchableDocumentItem>();
                }

                // 记录接口返回结果
                _logger.LogInformation("接口返回结果: 业务类型={BusinessType}, 数据数量={Count}",
                    businessType, result.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取业务类型数据失败, 业务类型: {BusinessType}, 错误: {ErrorMessage}",
                    businessType, ex.Message);
                // 返回空列表而不是抛出异常
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 从外部接口获取业务类型数据（公共方法，供其他服务调用）
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>业务类型数据</returns>
        public async Task<List<MatchableDocumentItem>> FetchBusinessTypeDataAsync(GetMatchableDocumentsRequest request, BusinessType businessType)
        {
            _logger.LogInformation("开始从外部接口获取业务类型数据, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                request.MergeInputBillId, businessType);

            // 调用接口获取数据
            var result = await FetchBusinessTypeData(request, businessType);

            // 从缓存中获取查询条件，用于更新接口调用状态
            var queryCondition = _cacheManager.GetQueryConditionFromCache(request.MergeInputBillId);
            if (queryCondition == null)
            {
                // 如果缓存中没有查询条件，使用当前请求作为查询条件
                queryCondition = request;
                _logger.LogInformation("缓存中没有查询条件，使用当前请求作为查询条件");
            }

            // 确保接口更新状态字典已初始化
            if (queryCondition.InterfaceUpdateStatus == null)
            {
                queryCondition.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                _logger.LogInformation("初始化接口更新状态字典");
            }

            // 更新接口调用状态
            queryCondition.InterfaceUpdateStatus[businessType] = true;

            // 保存更新后的查询条件到缓存
            _cacheManager.SaveQueryConditionToCache(request.MergeInputBillId, queryCondition);

            // 设置匹配状态缓存
            _cacheManager.SetMatchStatusCache(request.MergeInputBillId, businessType, true);

            _logger.LogInformation("已更新接口调用状态, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                request.MergeInputBillId, businessType);

            return result;
        }



        /// <summary>
        /// 执行明细匹配，并返回匹配结果
        /// </summary>
        /// <param name="details">需要匹配的明细列表</param>
        /// <param name="matchableItems">可匹配的项目列表</param>
        /// <param name="request">请求参数</param>
        /// <param name="isPositiveQuantity">是否匹配正数数量（true=蓝票，false=红票）</param>
        /// <param name="matchPrecision">匹配精度，如果为null则使用request中的匹配精度</param>
        /// <returns>匹配结果</returns>
        private MatchResult MatchDetails(
            List<InvoiceDetailItem> details,
            List<MatchableDocumentItem> matchableItems,
            GetMatchableDocumentsRequest request,
            bool isPositiveQuantity,
            MatchPrecisionEnum? matchPrecision = null)
        {
            // 1. 初始化参数和结果对象
            var actualMatchPrecision = matchPrecision ?? request.MatchPrecision;
            string ticketType = isPositiveQuantity ? "蓝票" : "红票";
            _logger.LogInformation("执行{TicketType}匹配, 明细数量: {DetailCount}, 匹配维度: {MatchPrecision}",
                ticketType, details?.Count ?? 0, actualMatchPrecision);

            // 初始化匹配结果
            var result = new MatchResult
            {
                AllMatched = false,
                MatchedItems = new List<MatchableDocumentItem>(),
                UnmatchedDetails = new List<InvoiceDetailItem>()
            };

            // 如果没有明细或可匹配项，直接返回
            if (details == null || details.Count == 0 || matchableItems == null || matchableItems.Count == 0)
            {
                result.UnmatchedDetails = details ?? new List<InvoiceDetailItem>();
                return result;
            }

            // 2. 准备匹配数据
            // 创建明细的副本，以便在匹配过程中修改而不影响原始数据
            var detailsCopy = details.Select(d => new InvoiceDetailItem
            {
                Id = d.Id,
                InvoiceName = d.InvoiceName,
                ProductName = d.ProductName,
                ProductNo = d.ProductNo,
                Specification = d.Specification,
                Quantity = d.Quantity,
                TaxCost = d.TaxCost,
                TaxRate = d.TaxRate
            }).ToList();

            // 创建可匹配项的副本，以便在匹配过程中修改而不影响原始数据
            var matchableItemsCopy = new List<MatchableDocumentItem>(matchableItems);

            // 存储匹配结果，用于保存到数据库
            var matchSubmitDetails = new List<MergeInputBillSubmitDetailPo>();

            // 3. 执行匹配
            foreach (var detail in detailsCopy)
            {
                // 跳过不符合票据类型的明细或数量为0的明细
                if ((isPositiveQuantity && detail.Quantity <= 0) || (!isPositiveQuantity && detail.Quantity >= 0))
                {
                    continue;
                }

                // 匹配单个明细
                var (detailMatchedItems, detailSubmitDetails, isFullyMatched, remainingQuantity) = MatchSingleDetail(
                    detail,
                    matchableItemsCopy,
                    request.MergeInputBillId,
                    isPositiveQuantity,
                    actualMatchPrecision);

                // 添加匹配结果
                result.MatchedItems.AddRange(detailMatchedItems);
                matchSubmitDetails.AddRange(detailSubmitDetails);

                // 如果未完全匹配，添加到未匹配明细列表
                if (!isFullyMatched && remainingQuantity > 0)
                {
                    // 创建未匹配明细
                    var unmatchedDetail = new InvoiceDetailItem
                    {
                        Id = detail.Id,
                        InvoiceName = detail.InvoiceName,
                        ProductName = detail.ProductName,
                        ProductNo = detail.ProductNo,
                        Specification = detail.Specification,
                        Quantity = isPositiveQuantity ? remainingQuantity : -remainingQuantity, // 保持原始符号
                        TaxCost = detail.TaxCost,
                        TaxRate = detail.TaxRate
                    };

                    result.UnmatchedDetails.Add(unmatchedDetail);
                }
            }

            // 4. 设置是否全部匹配
            result.AllMatched = result.UnmatchedDetails.Count == 0;

            // 5. 记录匹配结果
            _logger.LogInformation("匹配结果: {TicketType}, 匹配项数量: {MatchedCount}, 未匹配明细数量: {UnmatchedCount}",
                ticketType, result.MatchedItems.Count, result.UnmatchedDetails.Count);

            // 6. 保存匹配结果到数据库
            SaveMatchResultsToDatabase(matchSubmitDetails);

            // 7. 更新缓存中的对应记录
            UpdateMatchableItemsCache(request.MergeInputBillId, matchableItemsCopy, isPositiveQuantity);

            // 8. 保存匹配结果到缓存，按业务类型分组
            SaveMatchResultToCache(request.MergeInputBillId, result, isPositiveQuantity);

            return result;
        }

        /// <summary>
        /// 匹配单个明细
        /// </summary>
        /// <param name="detail">明细</param>
        /// <param name="matchableItems">可匹配项列表</param>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="isPositiveQuantity">是否为正数数量（蓝票）</param>
        /// <param name="matchPrecision">匹配精度</param>
        /// <returns>匹配结果元组：(匹配项列表, 提交明细列表, 是否完全匹配, 剩余未匹配数量)</returns>
        private (List<MatchableDocumentItem> matchedItems, List<MergeInputBillSubmitDetailPo> submitDetails, bool isFullyMatched, decimal remainingQuantity)
            MatchSingleDetail(
                InvoiceDetailItem detail,
                List<MatchableDocumentItem> matchableItems,
                Guid mergeInputBillId,
                bool isPositiveQuantity,
                MatchPrecisionEnum matchPrecision)
        {
            // 0. 检查数量是否为整数，如果不是则跳过匹配
            if (detail.Quantity != Math.Round(detail.Quantity, 0))
            {
                _logger.LogInformation("跳过非整数数量的明细: {DetailId}, 数量: {Quantity}", detail.Id, detail.Quantity);
                return (new List<MatchableDocumentItem>(), new List<MergeInputBillSubmitDetailPo>(), false, Math.Abs(detail.Quantity));
            }

            // 1. 准备匹配数据
            // 从开票名称中提取品名，并去除前后空格
            string productName = !string.IsNullOrEmpty(detail.ProductName)
                ? detail.ProductName.Trim()
                : ParseProductName(detail.InvoiceName).Trim();

            // 确定货号，优先使用 ProductNo，如果为空则使用 Specification，并去除前后空格
            string productNo = !string.IsNullOrEmpty(detail.ProductNo)
                ? detail.ProductNo.Trim()
                : (detail.Specification?.Trim() ?? string.Empty);

            // 2. 筛选可匹配项
            var filteredItems = FilterMatchableItems(
                matchableItems,
                detail,
                isPositiveQuantity,
                matchPrecision,
                productName,
                productNo);

            // 3. 执行匹配
            var matchedItems = new List<MatchableDocumentItem>();
            var submitDetails = new List<MergeInputBillSubmitDetailPo>();
            decimal remainingQuantity = Math.Abs(detail.Quantity);
            bool isFullyMatched = false;

            // 创建一个字典，用于跟踪每个MatchKey对应的匹配数量
            var matchQuantityByKey = new Dictionary<string, decimal>();

            // 第一步：执行匹配逻辑，计算每个明细的匹配数量
            foreach (var item in filteredItems)
            {
                if (remainingQuantity <= 0)
                {
                    isFullyMatched = true;
                    break;
                }

                // 计算匹配数量 - 考虑符号信息
                // 使用 RemainingQuantity 而不是 AvailableQuantity 进行比较
                decimal remainingItemQuantity = item.RemainingQuantity;

                // 对于红票业务类型，remainingItemQuantity应该是负数
                // 对于蓝票业务类型，remainingItemQuantity应该是正数
                bool isItemQuantityNegative = remainingItemQuantity < 0;
                bool isItemRedInvoice = IsRedInvoiceBusinessType(item.BusinessType);

                // 如果符号不一致，跳过此项
                if ((isPositiveQuantity && isItemRedInvoice && isItemQuantityNegative) ||
                    (!isPositiveQuantity && !isItemRedInvoice && !isItemQuantityNegative))
                {
                    continue;
                }

                // 使用绝对值比较数量大小，但保留符号信息
                decimal absRemainingItemQuantity = Math.Abs(remainingItemQuantity);
                decimal absRemainingQuantity = Math.Abs(remainingQuantity);

                // 取两者中的最小值
                decimal absMatchQuantity = Math.Min(absRemainingQuantity, absRemainingItemQuantity);

                // 更新剩余数量
                remainingQuantity = remainingQuantity - absMatchQuantity;

                // 检查remainingQuantity是否小于0，如果是则抛出异常
                if (remainingQuantity < 0)
                {
                    _logger.LogError("匹配数量计算错误，剩余数量小于0: DetailId={DetailId}, 原始值={OriginalValue}",
                        detail.Id, remainingQuantity);
                    throw new InvalidOperationException($"匹配数量计算错误，剩余数量小于0。DetailId: {detail.Id}, 原始值: {remainingQuantity}");
                }



                // 更新匹配项
                item.MatchPrecision = matchPrecision;
                item.MergeInputBillDetailId = detail.Id;

                // 保持原始数量，不在这里转换为负数
                // 只在保存到MergeInputBillSubmitDetail时才转换为负数
                decimal currentMatchQuantity = absMatchQuantity;

                item.CurrentMatchQuantity = currentMatchQuantity; // 设置本次匹配数量

                item.MatchQuantity += currentMatchQuantity;

                // 记录匹配数量 - 使用CurrentMatchQuantity
                if (!string.IsNullOrEmpty(item.MatchKey))
                {
                    matchQuantityByKey[item.MatchKey] = currentMatchQuantity;
                }

                matchedItems.Add(item);

                // 创建提交明细，使用 currentMatchQuantity 作为 CurrentMatchQuantity
                var submitDetail = CreateSubmitDetail(item, detail.Id, mergeInputBillId, matchPrecision, currentMatchQuantity);
                submitDetails.Add(submitDetail);
            }

            // 设置是否完全匹配
            if (remainingQuantity <= 0)
            {
                isFullyMatched = true;
            }

            return (matchedItems, submitDetails, isFullyMatched, remainingQuantity);
        }

        /// <summary>
        /// 创建提交明细
        /// </summary>
        /// <param name="item">匹配项</param>
        /// <param name="detailId">明细ID</param>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="matchPrecision">匹配精度</param>
        /// <param name="currentMatchQuantity">本次匹配数量</param>
        /// <returns>提交明细</returns>
        private static MergeInputBillSubmitDetailPo CreateSubmitDetail(
            MatchableDocumentItem item,
            Guid detailId,
            Guid mergeInputBillId,
            MatchPrecisionEnum matchPrecision,
            decimal currentMatchQuantity)
        {
            // 计算金额字段
            decimal? noTaxAmount = null;
            decimal? totalAmount = null;
            decimal? matchedAmount = null;

            // 对于经销调出和换货转退货业务类型，确保MatchQuantity为负数
            decimal dbMatchQuantity = currentMatchQuantity;
            if ((item.BusinessType == BusinessType.DistributionTransfer ||
                 item.BusinessType == BusinessType.ExchangeToReturn) &&
                dbMatchQuantity > 0)
            {
                // 如果是红票业务类型且当前匹配数量为正数，转换为负数
                // 只在保存到MergeInputBillSubmitDetail时转换为负数
                dbMatchQuantity = -Math.Abs(dbMatchQuantity);
            }

            // 正常计算金额（不做四舍五入处理）
            noTaxAmount = dbMatchQuantity * item.NoTaxCost;
            totalAmount = dbMatchQuantity * item.TaxCost;

            // 匹配金额 = 匹配数量 × 含税单价（不做四舍五入处理）
            matchedAmount = dbMatchQuantity * item.TaxCost;

            return new MergeInputBillSubmitDetailPo
            {
                Id = Guid.NewGuid(),
                MergeInputBillId = mergeInputBillId,
                MergeInputBillDetailId = detailId,
                // 设置累计数量
                Quantity = item.Quantity,
                // 使用处理后的匹配数量
                MatchQuantity = dbMatchQuantity,
                // 设置ReceivedNumber为接口返回的已匹配数量或金额
                ReceivedNumber = item.InvoicedQuantity,
                BusinessType = (int)item.BusinessType,
                BussinessItemCode = item.BusinessItemCode,
                MatchPrecision = matchPrecision,
                ProductName = item.ProductName,
                ProductNo = item.ProductNo,
                ProductId = item.ProductId,
                ProductNameId = item.ProductNameId,
                ProducerId = item.ProducerId,
                ProducerName = item.ProducerName,
                ProducerOrderNo = item.ProducerOrderNo,
                PurchaseOrderCode = item.PurchaseOrderCode,
                // 确保采购合同单号被正确复制
                ContractNo = item.ContractNo,
                Model = item.Model,
                Specification = item.Specification,
                NoTaxCost = item.NoTaxCost,
                TaxCost = item.TaxCost,
                TaxRate = item.TaxRate,
                // 税额 = 含税金额 - 不含税金额，使用累计匹配数量计算，保留4位小数
                // 注意：使用dbMatchQuantity，确保金额计算与保存到数据库的匹配数量一致
                TaxAmount = Math.Round(item.TaxCost * dbMatchQuantity - item.NoTaxCost * dbMatchQuantity, 4),
                // 新增字段 - 使用计算好的noTaxAmount和totalAmount
                NoTaxAmount = noTaxAmount.Value,
                TotalAmount = totalAmount.Value,
                MatchedAmount = matchedAmount.Value,
                BussinessDate = item.BusinessDate.Date, // 只保留日期部分，格式为 YYYY-MM-DD
                CreatedBy = "System",
                CreatedTime = DateTimeOffset.Now,
                MatchKey = item.MatchKey
            };
        }

        /// <summary>
        /// 保存匹配结果到数据库
        /// </summary>
        /// <param name="matchSubmitDetails">匹配提交明细列表</param>
        private void SaveMatchResultsToDatabase(List<MergeInputBillSubmitDetailPo> matchSubmitDetails)
        {
            if (matchSubmitDetails.Count == 0)
            {
                return;
            }

            try
            {
                _db.MergeInputBillSubmitDetails.AddRange(matchSubmitDetails);
                _db.SaveChanges();
                _logger.LogInformation("成功保存{Count}条匹配结果到数据库", matchSubmitDetails.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存匹配结果到数据库失败: {ErrorMessage}", ex.Message);
            }
        }



        #region 缓存处理方法

        /// <summary>
        /// 保存匹配结果到缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="result">匹配结果</param>
        /// <param name="isPositiveQuantity">是否为正数数量（蓝票）</param>
        private void SaveMatchResultToCache(Guid mergeInputBillId, MatchResult result, bool isPositiveQuantity)
        {
            if (result == null || result.MatchedItems == null || result.MatchedItems.Count == 0)
            {
                _logger.LogInformation("没有匹配结果需要保存到缓存");
                return;
            }

            // 根据票据类型确定需要更新的业务类型
            var businessTypes = isPositiveQuantity ? BlueBusinessTypes : RedBusinessTypes;

            // 保存匹配结果到缓存，按业务类型分组
            foreach (var businessType in businessTypes)
            {
                // 筛选当前业务类型的匹配项
                var businessTypeItems = result.MatchedItems
                    .Where(x => x.BusinessType == businessType)
                    .ToList();

                // 创建该业务类型的匹配结果
                var businessTypeResult = new MatchResult
                {
                    MatchedItems = businessTypeItems,
                    UnmatchedDetails = result.UnmatchedDetails,
                    AllMatched = result.AllMatched
                };

                // 保存到缓存
                _cacheManager.SaveMatchResultToCache(mergeInputBillId, businessType, businessTypeResult);
            }
        }

        /// <summary>
        /// 更新可匹配项缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="matchableItems">可匹配项列表</param>
        /// <param name="isPositiveQuantity">是否为正数数量（蓝票）</param>
        private void UpdateMatchableItemsCache(Guid mergeInputBillId, List<MatchableDocumentItem> matchableItems, bool isPositiveQuantity)
        {
            if (matchableItems == null || matchableItems.Count == 0)
            {
                _logger.LogInformation("没有可匹配项需要更新缓存");
                return;
            }

            // 根据票据类型确定需要更新的业务类型
            var businessTypes = isPositiveQuantity ? BlueBusinessTypes : RedBusinessTypes;

            // 按业务类型分组可匹配项，保留所有项目，并按业务类型排序（服务费排到最后）
            var groupedItems = matchableItems
                .GroupBy(x => x.BusinessType)
                .OrderBy(g => g.Key == BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key)
                .ToDictionary(g => g.Key, g => g.ToList());

            // 更新每种业务类型的缓存
            foreach (var businessType in businessTypes)
            {
                if (groupedItems.TryGetValue(businessType, out var businessTypeItems))
                {
                    _logger.LogInformation("更新缓存: 业务类型={BusinessType}, 项数量={ItemCount}",
                        businessType, businessTypeItems.Count);

                    // 获取当前缓存中的数据
                    var currentCacheItems = _cacheManager.GetBusinessTypeCache(mergeInputBillId, businessType);

                    // 如果缓存中没有数据，直接使用当前的数据
                    if (currentCacheItems == null || currentCacheItems.Count == 0)
                    {
                        _cacheManager.SetBusinessTypeCache(mergeInputBillId, businessType, businessTypeItems);
                        continue;
                    }

                    // 创建一个字典，用于快速查找MatchKey对应的缓存项
                    var cacheItemsByMatchKey = currentCacheItems
                        .Where(x => !string.IsNullOrEmpty(x.MatchKey))
                        .ToDictionary(x => x.MatchKey, x => x);

                    // 更新缓存项的MatchQuantity
                    foreach (var item in businessTypeItems)
                    {
                        if (!string.IsNullOrEmpty(item.MatchKey) && cacheItemsByMatchKey.TryGetValue(item.MatchKey, out var cacheItem))
                        {
                            // 更新缓存项的MatchQuantity
                            // 注意：这里不应该累加，而是直接使用当前值，因为在 MatchSingleDetail 方法中已经累加过了
                            // 如果这里再次累加，会导致数据翻倍
                            cacheItem.MatchQuantity = item.MatchQuantity;

                            // 检查RemainingQuantity是否小于0，如果是则抛出异常
                            if (cacheItem.RemainingQuantity < 0)
                            {
                                // 记录错误日志
                                _logger.LogError("匹配数量超过可用数量: MatchKey={MatchKey}, 可用数量={AvailableQuantity}, 匹配数量={MatchQuantity}, 剩余数量={RemainingQuantity}",
                                    cacheItem.MatchKey, cacheItem.Quantity, cacheItem.MatchQuantity, cacheItem.RemainingQuantity);

                                // 抛出异常，中断处理
                                throw new InvalidOperationException($"匹配数量超过可用数量，请检查匹配数据。MatchKey: {cacheItem.MatchKey}, 可用数量: {cacheItem.Quantity}, 匹配数量: {cacheItem.MatchQuantity}");
                            }

                            // 重置CurrentMatchQuantity，因为这是本次匹配的临时值
                            item.CurrentMatchQuantity = 0;
                        }
                        else
                        {
                            // 如果缓存中没有对应的项，则添加到缓存中
                            currentCacheItems.Add(item);
                        }
                    }

                    // 更新缓存
                    _cacheManager.SetBusinessTypeCache(mergeInputBillId, businessType, currentCacheItems);
                }
                else
                {
                    // 如果当前业务类型没有匹配项，则保留原有缓存
                    var currentCacheItems = _cacheManager.GetBusinessTypeCache(mergeInputBillId, businessType);
                    if (currentCacheItems != null && currentCacheItems.Count > 0)
                    {
                        _logger.LogInformation("业务类型 {BusinessType} 保留原有缓存，项数量={ItemCount}",
                            businessType, currentCacheItems.Count);
                    }
                    else
                    {
                        _logger.LogInformation("业务类型 {BusinessType} 无项，设置空列表", businessType);
                        _cacheManager.SetBusinessTypeCache(mergeInputBillId, businessType, new List<MatchableDocumentItem>());
                    }
                }
            }
        }

        #endregion



        /// <summary>
        /// 筛选可匹配项
        /// </summary>
        /// <param name="matchableItems">可匹配项列表</param>
        /// <param name="detail">发票明细</param>
        /// <param name="isPositiveQuantity">是否为正数数量（蓝票）</param>
        /// <param name="matchPrecision">匹配精度</param>
        /// <param name="productName">品名</param>
        /// <param name="productNo">货号</param>
        /// <returns>筛选后的可匹配项列表</returns>
        private static List<MatchableDocumentItem> FilterMatchableItems(
            List<MatchableDocumentItem> matchableItems,
            InvoiceDetailItem detail,
            bool isPositiveQuantity,
            MatchPrecisionEnum matchPrecision,
            string productName,
            string productNo)
        {
            // 1. 参数验证
            if (matchableItems == null || matchableItems.Count == 0)
                return new List<MatchableDocumentItem>();

            // 去除前后空格
            string trimmedProductName = productName?.Trim() ?? string.Empty;
            string trimmedProductNo = productNo?.Trim() ?? string.Empty;

            // 如果品名和货号都为空，则返回原始列表
            if (string.IsNullOrEmpty(trimmedProductName) && string.IsNullOrEmpty(trimmedProductNo))
                return matchableItems.ToList();

            // 2. 应用基本筛选条件
            var filteredItems = matchableItems
                // 数量条件 - 根据票据类型和业务类型筛选
                .Where(x =>
                {
                    // 跳过数量为0或小于0的明细
                    if (x.RemainingQuantity <= 0)
                        return false;

                    // 跳过数量不为整数的明细
                    if (x.RemainingQuantity != Math.Round(x.RemainingQuantity, 0))
                        return false;

                    return true;
                })
                // 价格和税率条件 - 匹配含税单价和税率
                .Where(x => x.TaxCost == detail.TaxCost && x.TaxRate == detail.TaxRate)
                .ToList();

            // 3. 应用匹配维度筛选条件
            List<MatchableDocumentItem> result;
            if (matchPrecision == MatchPrecisionEnum.ProductName && !string.IsNullOrEmpty(trimmedProductName))
            {
                // 品名维度匹配
                result = filteredItems
                    .Where(x => !string.IsNullOrEmpty(x.ProductName) &&
                              ParseProductName(x.ProductName).Trim().Contains(trimmedProductName))
                    .ToList();
            }
            else if (matchPrecision == MatchPrecisionEnum.ProductNo && !string.IsNullOrEmpty(trimmedProductNo))
            {
                // 货号维度匹配
                result = filteredItems
                    .Where(x => !string.IsNullOrEmpty(x.ProductNo) &&
                              x.ProductNo.Trim().Contains(trimmedProductNo))
                    .ToList();
            }
            else
            {
                // 默认返回空列表
                result = new List<MatchableDocumentItem>();
            }

            // 4. 按业务明细单号从小到大排序
            return result.OrderBy(x=>x.BusinessType).ThenBy(x => x.BusinessItemCode).ToList();
        }

        /// <summary>
        /// 判断是否为红票业务类型
        /// </summary>
        /// <param name="businessType">业务类型</param>
        /// <returns>是否为红票业务类型</returns>
        private static bool IsRedInvoiceBusinessType(BusinessType businessType)
        {
            // 经销调出和换货转退货是红票业务类型
            // 购货修订是特殊业务类型，不应该被视为红票业务类型
            return businessType == BusinessType.DistributionTransfer ||
                   businessType == BusinessType.ExchangeToReturn;
        }



        // 缓存正则表达式实例，提高性能
        private static readonly Regex ChineseCharactersRegex = new("[^\\p{IsCJKUnifiedIdeographs}]", RegexOptions.Compiled);

        /// <summary>
        /// 从开票名称中提取品名
        /// </summary>
        /// <param name="invoiceName">开票名称（格式为 *医疗器械/其它*品名）</param>
        /// <returns>提取出的品名</returns>
        private static string ParseProductName(string invoiceName)
        {
            if (string.IsNullOrEmpty(invoiceName))
            {
                return string.Empty;
            }

            // 开票名称格式为 *医疗器械/其它*品名
            // 需要提取出品名部分
            int starIndex = invoiceName.IndexOf('*');
            if (starIndex >= 0)
            {
                int secondStarIndex = invoiceName.IndexOf('*', starIndex + 1);
                if (secondStarIndex > starIndex)
                {
                    // 提取第二个*后面的内容作为品名
                    invoiceName = invoiceName[(secondStarIndex + 1)..].Trim();
                }
            }

            // 使用正则表达式去除非中文字符，只保留中文汉字
            // \p{IsCJKUnifiedIdeographs} 匹配所有中文汉字
            string result = ChineseCharactersRegex.Replace(invoiceName, "");

            // 如果结果为空，返回原始名称
            return string.IsNullOrEmpty(result) ? invoiceName : result;
        }

        #region 模拟接口调用



        /// <summary>
        /// 转换经销购货入库数据
        /// </summary>
        private static List<MatchableDocumentItem> ConvertDistributionPurchaseItems(List<ManyStoreInDetailItem> items)
        {
            return items.Select(item =>
            {
                decimal quantity = item.quantity ?? 0;
                // 使用 invoiceQty 而不是 invoiceQuantity
                decimal invoicedQuantity = 0;
                if (decimal.TryParse(item.invoiceQty, out decimal parsedInvoiceQty))
                {
                    invoicedQuantity = parsedInvoiceQty;
                }
                else if (item.invoiceQuantity.HasValue && item.invoiceQuantity > 0)
                {
                    invoicedQuantity = item.invoiceQuantity.Value;
                }

                // 使用结算成本 settlementUnitCost 而不是最新含税成本 unitCost
                decimal taxCost = 0;
                if (item.settlementUnitCost != null && decimal.TryParse(item.settlementUnitCost, out decimal parsedSettlementUnitCost))
                {
                    taxCost = parsedSettlementUnitCost;
                }
                else
                {
                    taxCost = item.unitCost;
                }

                decimal taxRate = item.taxRate;
                // 使用中国标准时间（UTC+8）处理时间戳，避免日期偏差
                DateTime businessDate = DateTimeOffset.FromUnixTimeMilliseconds(item.storeInDate)
                    .ToOffset(TimeSpan.FromHours(8)) // 明确使用UTC+8时区
                    .DateTime.Date; // 只保留日期部分，格式为 YYYY-MM-DD

                // 尝试解析ProductId、ProductNameId和ProducerId
                Guid? productId = null;
                Guid? productNameId = null;
                Guid? producerId = null;

                if (!string.IsNullOrEmpty(item.productId) && Guid.TryParse(item.productId, out Guid parsedProductId))
                {
                    productId = parsedProductId;
                }

                if (!string.IsNullOrEmpty(item.productNameId) && Guid.TryParse(item.productNameId, out Guid parsedProductNameId))
                {
                    productNameId = parsedProductNameId;
                }

                if (!string.IsNullOrEmpty(item.producerId) && Guid.TryParse(item.producerId, out Guid parsedProducerId))
                {
                    producerId = parsedProducerId;
                }

                // 使用带参构造函数创建对象
                var docItem = new MatchableDocumentItem(
                    BusinessType.DistributionPurchase,
                    item.storeInCode,
                    businessDate,
                    item.productName,
                    item.productNo,
                    quantity,
                    taxCost,
                    taxRate,
                    invoicedQuantity,
                    productId,
                    productNameId
                );

                // 设置其他属性
                docItem.BusinessItemCode = item.storeInCode; // 使用入库单号作为业务明细单号
                docItem.Specification = item.specification ?? string.Empty;
                docItem.Model = item.model ?? string.Empty;
                docItem.PurchaseOrderCode = item.purchaseOrderCode;
                docItem.ProducerOrderNo = item.producerOrderNo;
                docItem.ProducerId = producerId;
                docItem.ProducerName = item.producerName;

                return docItem;
            }).ToList();
        }



        /// <summary>
        /// 转换经销调出数据（出库单）
        /// </summary>
        /// <remarks>
        /// 注意：经销调出是红票业务类型，但在页面上显示为正数，在保存到数据库时转换为负数。
        /// 使用quantityModifier=1保持原始数量为正数，在页面上显示。
        /// 在保存到数据库时，会根据业务类型将数量转换为负数。
        /// MatchQuantity字段也应该保存为负数，表示已匹配的数量。
        /// </remarks>
        private static List<MatchableDocumentItem> ConvertDistributionTransferOutItems(
            List<ManyStoreOutDetailItem> items,
            decimal quantityModifier)
        {
            return items.Select(item =>
            {
                // 将字符串类型的数值转换为decimal
                decimal quantity = item.quantity;
                decimal invoicedQuantity = string.IsNullOrEmpty(item.invoiceQuantity) ? 0 : decimal.Parse(item.invoiceQuantity);
                decimal taxCost = string.IsNullOrEmpty(item.unitCost) ? 0 : decimal.Parse(item.unitCost);
                decimal taxRate = string.IsNullOrEmpty(item.taxRate) ? 0 : decimal.Parse(item.taxRate);
                // 使用中国标准时间（UTC+8）处理时间戳，避免日期偏差
                DateTime businessDate = DateTimeOffset.FromUnixTimeMilliseconds(item.billDate)
                    .ToOffset(TimeSpan.FromHours(8)) // 明确使用UTC+8时区
                    .DateTime.Date; // 只保留日期部分，格式为 YYYY-MM-DD

                // 尝试解析ProductId和ProductNameId
                Guid? productId = null;
                Guid? productNameId = null;
                Guid? producerId = null;

                if (!string.IsNullOrEmpty(item.productId) && Guid.TryParse(item.productId, out Guid parsedProductId))
                {
                    productId = parsedProductId;
                }

                if (!string.IsNullOrEmpty(item.productNameId) && Guid.TryParse(item.productNameId, out Guid parsedProductNameId))
                {
                    productNameId = parsedProductNameId;
                }

                if (!string.IsNullOrEmpty(item.producerId) && Guid.TryParse(item.producerId, out Guid parsedProducerId))
                {
                    producerId = parsedProducerId;
                }

                // 使用带参构造函数创建对象
                var docItem = new MatchableDocumentItem(
                    BusinessType.DistributionTransfer,
                    item.storeOutCode, // 对于经销调出，使用storeOutCode作为业务单号
                    businessDate,
                    item.productName,
                    item.productNo,
                    quantity * quantityModifier, // 应用数量修改器（通常为-1）
                    taxCost,
                    taxRate,
                    invoicedQuantity,
                    productId,
                    productNameId
                );

                // 设置其他属性
                docItem.BusinessItemCode = item.storeOutCode; // 对于经销调出，使用storeOutCode作为业务明细单号
                docItem.Specification = item.specification ?? string.Empty;
                docItem.Model = item.model ?? string.Empty;
                docItem.PurchaseOrderCode = item.purchaseOrderCode;

                // 设置生产商相关信息
                docItem.ProducerId = producerId;
                docItem.ProducerName = item.producerName;

                return docItem;
            }).ToList();
        }

        /// <summary>
        /// 转换换货转退货数据
        /// </summary>
        /// <remarks>
        /// 注意：换货转退货是红票业务类型，但在页面上显示为正数，在保存到数据库时转换为负数。
        /// 使用quantityModifier=1保持原始数量为正数，在页面上显示。
        /// 在保存到数据库时，会根据业务类型将数量转换为负数。
        /// MatchQuantity字段也应该保存为负数，表示已匹配的数量。
        /// </remarks>
        private static List<MatchableDocumentItem> ConvertExchangeToReturnItems(
            List<ManyStoreExchangeBackDetailItem> items,
            decimal quantityModifier,
            Func<ManyStoreExchangeBackDetailItem, string>? businessItemCodeSelector)
        {
            // 如果没有指定业务明细单号选择器，默认使用purchaseOrderCode
            businessItemCodeSelector ??= item => item.purchaseOrderCode;

            return items.Select(item =>
            {
                decimal quantity = item.quantity;

                // 使用 invoiceQty 而不是 invoiceQuantity
                decimal invoicedQuantity = 0;
                if (!string.IsNullOrEmpty(item.invoiceQty) && decimal.TryParse(item.invoiceQty, out decimal parsedInvoiceQty))
                {
                    invoicedQuantity = parsedInvoiceQty;
                }
                else
                {
                    invoicedQuantity = item.invoiceQuantity;
                }

                // 使用结算成本 settlementUnitCost 而不是最新含税成本 unitCost
                decimal taxCost = 0;
                if (!string.IsNullOrEmpty(item.settlementUnitCost) && decimal.TryParse(item.settlementUnitCost, out decimal parsedSettlementUnitCost))
                {
                    taxCost = parsedSettlementUnitCost;
                }
                else
                {
                    taxCost = item.unitCost;
                }

                decimal taxRate = item.taxRate;
                // 使用中国标准时间（UTC+8）处理时间戳，避免日期偏差
                DateTime businessDate = DateTimeOffset.FromUnixTimeMilliseconds(item.billDate)
                    .ToOffset(TimeSpan.FromHours(8)) // 明确使用UTC+8时区
                    .DateTime.Date; // 只保留日期部分，格式为 YYYY-MM-DD

                // 尝试解析ProductId、ProductNameId和ProducerId
                Guid? productId = null;
                Guid? productNameId = null;
                Guid? producerId = null;

                if (!string.IsNullOrEmpty(item.productId) && Guid.TryParse(item.productId, out Guid parsedProductId))
                {
                    productId = parsedProductId;
                }

                if (!string.IsNullOrEmpty(item.productNameId) && Guid.TryParse(item.productNameId, out Guid parsedProductNameId))
                {
                    productNameId = parsedProductNameId;
                }

                if (!string.IsNullOrEmpty(item.producerId) && Guid.TryParse(item.producerId, out Guid parsedProducerId))
                {
                    producerId = parsedProducerId;
                }

                // 使用带参构造函数创建对象
                var docItem = new MatchableDocumentItem(
                    BusinessType.ExchangeToReturn,
                    item.billCode,
                    businessDate,
                    item.productName,
                    item.productNo,
                    quantity * quantityModifier, // 应用数量修改器（通常为-1）
                    taxCost,
                    taxRate,
                    invoicedQuantity,
                    productId,
                    productNameId
                );

                // 设置其他属性
                docItem.BusinessItemCode = businessItemCodeSelector(item); // 使用选择器获取业务明细单号
                docItem.Specification = item.specification ?? string.Empty;
                docItem.Model = item.modelNo ?? string.Empty; // 使用modelNo字段而不是model字段
                docItem.PurchaseOrderCode = item.purchaseOrderCode;
                docItem.ProducerId = producerId;
                docItem.ProducerName = item.producerName;

                return docItem;
            }).ToList();
        }



        /// <summary>
        /// 调用经销购货入库接口
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>经销购货入库数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchDistributionPurchaseData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 使用通用方法创建查询参数
                var input = CreateStoreInDetailQueryInput(request);

                // 调用库存能力中心接口
                var result = await _manyInventoryApiClient.QueryManyToManyStoreInDetailForFinance(input);

                if (result == null || result.Data == null || result.Data.Count == 0)
                {
                    return new List<MatchableDocumentItem>();
                }

                // 转换为统一的MatchableDocumentItem格式
                return ConvertDistributionPurchaseItems(result.Data);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取经销购货入库数据失败: {ErrorMessage}", ex.Message);
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 调用寄售转购货接口
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>寄售转购货数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchConsignmentToPurchaseData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 使用通用方法创建查询参数
                var input = CreateQueryInput(request);

                // 调用采购能力中心接口
                var result = await _purchaseApiClient.GetConsignToPurchaseDetailGroupForInvoice(input);

                if (result == null || result.Count == 0)
                {
                    return new List<MatchableDocumentItem>();
                }

                // 转换为统一的MatchableDocumentItem格式
                return result.Select(item =>
                {
                    decimal quantity = item.quantity;
                    decimal invoicedQuantity = item.invoiceQuantity;
                    decimal canInvoiceQuantity = item.canInvoiceQuantity;
                    decimal taxCost = item.unitCost;
                    decimal taxRate = item.taxRate;

                    // 使用采购订单号作为业务单号
                    string businessCode = item.purchaseOrderCode;

                    // 尝试解析ProductId、ProductNameId和ProducerId
                    Guid? productId = null;
                    Guid? productNameId = null;
                    Guid? producerId = null;

                    if (!string.IsNullOrEmpty(item.productId) && Guid.TryParse(item.productId, out Guid parsedProductId))
                    {
                        productId = parsedProductId;
                    }

                    if (!string.IsNullOrEmpty(item.productNameId) && Guid.TryParse(item.productNameId, out Guid parsedProductNameId))
                    {
                        productNameId = parsedProductNameId;
                    }

                    if (!string.IsNullOrEmpty(item.producerId) && Guid.TryParse(item.producerId, out Guid parsedProducerId))
                    {
                        producerId = parsedProducerId;
                    }

                    // 使用带参构造函数创建对象
                    var docItem = new MatchableDocumentItem(
                        BusinessType.ConsignmentToPurchase,
                        businessCode,
                        // 使用查询参数中的开始时间，如果没有则使用当前时间
                        item.billDate,
                        item.productName,
                        item.productNo,
                        quantity,
                        taxCost,
                        taxRate,
                        invoicedQuantity,
                        productId,
                        productNameId
                    );

                    // 设置其他属性
                    // 使用业务单号作为业务明细单号，这是主要的排序和匹配依据
                    docItem.BusinessItemCode = businessCode;

                    // 使用规格和型号
                    docItem.Specification = item.spec ?? string.Empty;
                    docItem.Model = item.model ?? string.Empty;

                    // 设置采购订单号
                    docItem.PurchaseOrderCode = "";

                    // 设置数量和已入票数量，AvailableQuantity会自动计算
                    docItem.Quantity = quantity;
                    docItem.InvoicedQuantity = invoicedQuantity;

                    // 设置生产商相关信息
                    docItem.ProducerId = producerId;
                    docItem.ProducerName = item.producerName;

                    return docItem;
                }).ToList();
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取寄售转购货数据失败: {ErrorMessage}", ex.Message);
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 调用服务费采购接口 - 只使用时间、公司和供应商条件
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>服务费采购数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchServiceFeeProcurementData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 创建服务费查询参数 - 只使用时间、公司和供应商条件
                var storeInDetailQueryInput = new StoreInDetailQueryInput
                {
                    agentId = request.AgentId,
                    companyId = request.CompanyId,
                    // 不使用明细信息，只传递ID
                    InputBillId = request.MergeInputBillId,
                    page = 1,
                    limit = 1000 // 设置较大的值以获取所有数据
                };

                // 如果有时间范围，转换为时间戳
                if (request.StartDate.HasValue && request.EndDate.HasValue)
                {
                    storeInDetailQueryInput.storeInDateStart = request.StartDate.Value.ToUnixTimeMilliseconds().ToString();
                    storeInDetailQueryInput.storeInDateEnd = request.EndDate.Value.ToUnixTimeMilliseconds().ToString();
                }

                // 调用服务费查询接口
                var (serviceFeeData, totalCount) = await _inputBillQueryService.GetDebtsByType(storeInDetailQueryInput, DebtTypeEnum.servicefee);

                _logger.LogInformation("获取服务费采购数据成功, 总数: {TotalCount}", totalCount);

                // 转换为 MatchableDocumentItem 列表
                var result = new List<MatchableDocumentItem>();

                foreach (var item in serviceFeeData)
                {
                    // 计算可用数量
                    decimal availableAmount = item.PolyQuantity - item.PolyInvoiceQuantity;

                    // 计算税率
                    decimal taxRate = item.TaxRate.HasValue ? item.TaxRate.Value : 0;

                    decimal taxCost = availableAmount;

                    // 创建 MatchableDocumentItem 对象
                    var docItem = new MatchableDocumentItem(
                        BusinessType.ServiceFeeProcurement,
                        item.StoreInItemCode,
                        DateTimeOffset.FromUnixTimeMilliseconds((long)item.StoreInDate)
                            .ToOffset(TimeSpan.FromHours(8)) // 明确使用UTC+8时区
                            .DateTime.Date, // 只保留日期部分，格式为 YYYY-MM-DD
                         "",
                        "服务费",
                        1,
                        taxCost,
                        taxRate,
                        0,
                        item.ProductId,
                        null); // 服务费没有ProductNameId

                    // 设置其他属性
                    docItem.BusinessItemCode = item.StoreInItemCode;

                    // 设置采购合同单号
                    docItem.ContractNo = item.PurchaseContactNo;

                    result.Add(docItem);
                }

                return result;
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取服务费采购数据失败: {ErrorMessage}", ex.Message);
                throw new ApplicationException($"获取服务费采购数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 调用经销调出接口
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>经销调出数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchDistributionTransferData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 使用多对多出库查询参数创建方法
                var input = CreateManyToManyStoreOutQueryInput(request);

                // 调用库存能力中心接口
                var result = await _manyInventoryApiClient.QueryManyToManyStoreOutDetailForFinance(input);

                if (result == null || result.Data == null || result.Data.Count == 0)
                {
                    return new List<MatchableDocumentItem>();
                }

                // 转换为统一的MatchableDocumentItem格式
                // 对于经销调出业务类型，使用storeOutCode作为业务单号
                // 使用1作为quantityModifier，保持原始数量为正数，在页面上显示
                return ConvertDistributionTransferOutItems(result.Data, 1);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取经销调出数据失败: {ErrorMessage}", ex.Message);
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 调用购货修订接口
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>购货修订数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchPurchaseRevisionData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 使用通用方法创建查询参数
                var input = CreateQueryInput(request);

                // 调用采购能力中心接口
                var result = await _purchaseApiClient.GetPurchaseRevisionForFinanceInvoice(input);

                if (result == null || result.Count == 0)
                {
                    return new List<MatchableDocumentItem>();
                }

                // 转换为统一的MatchableDocumentItem格式
                return result.Select(item =>
                {
                    // 获取quantity，已经是decimal类型
                    decimal quantity = item.quantity;

                    // 由于GetReviseOrderForFinanceInvoiceOutput没有invoiceQuantity字段，设置为0
                    decimal invoicedQuantity = 0;

                    // 对于购货修订，直接使用可入票金额作为含税单价，数量在MatchableDocumentItem构造函数中会设置为1
                    decimal taxCost = item.canInvoiceAmount;

                    // 获取taxRate，已经是decimal类型
                    decimal taxRate = item.taxRate;

                    // 使用采购订单号作为业务单号
                    string businessCode = item.purchaseOrderCode;

                    // 尝试解析ProductId、ProductNameId和ProducerId
                    Guid? productId = null;
                    Guid? productNameId = null;
                    Guid? producerId = null;

                    if (!string.IsNullOrEmpty(item.productId) && Guid.TryParse(item.productId, out Guid parsedProductId))
                    {
                        productId = parsedProductId;
                    }

                    if (!string.IsNullOrEmpty(item.productNameId) && Guid.TryParse(item.productNameId, out Guid parsedProductNameId))
                    {
                        productNameId = parsedProductNameId;
                    }

                    if (!string.IsNullOrEmpty(item.producerId) && Guid.TryParse(item.producerId, out Guid parsedProducerId))
                    {
                        producerId = parsedProducerId;
                    }

                    // 使用带参构造函数创建对象
                    var docItem = new MatchableDocumentItem(
                        BusinessType.PurchaseRevision,
                        businessCode,
                        // 使用单据日期，只保留日期部分，格式为 YYYY-MM-DD
                        // 由于item.billDate已经是DateTime类型，不需要时区转换
                        item.billDate.Date.AddHours(8), // 添加8小时确保日期正确
                        item.productName,
                        item.productNo,
                        quantity,
                        taxCost,
                        taxRate,
                        invoicedQuantity,
                        productId,
                        productNameId
                    );

                    // 设置其他属性
                    // 使用业务单号作为业务明细单号，这是主要的排序和匹配依据
                    docItem.BusinessItemCode = businessCode;

                    // 使用规格和型号
                    docItem.Specification = item.spec ?? string.Empty;
                    docItem.Model = item.model ?? string.Empty;

                    // 设置采购订单号
                    docItem.PurchaseOrderCode = "";

                    // 设置生产商相关信息
                    docItem.ProducerId = producerId;
                    docItem.ProducerName = item.producerName;

                    // 公司和供应商信息不需要设置，因为MatchableDocumentItem类没有这些属性

                    return docItem;
                }).ToList();
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取购货修订数据失败: {ErrorMessage}", ex.Message);
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 调用换货转退货接口
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>换货转退货数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchExchangeToReturnData(GetMatchableDocumentsRequest request)
        {
            try
            {
                // 使用多对多换货转退货查询参数创建方法
                var input = CreateManyToManyStoreExchangeBackQueryInput(request);

                // 调用库存能力中心接口
                var result = await _manyInventoryApiClient.QueryManyToManyStoreExchangeBackDetailForFinance(input);

                if (result == null || result.Data == null || result.Data.Count == 0)
                {
                    return new List<MatchableDocumentItem>();
                }

                // 转换为统一的MatchableDocumentItem格式
                // 使用1作为quantityModifier，保持原始数量为正数，在页面上显示
                // 修改：使用billCode作为businessItemCode，确保换货转退货业务类型的businessItemCode字段被正确设置
                return ConvertExchangeToReturnItems(result.Data, 1, item => item.billCode);
            }
            catch (Exception ex)
            {
                // 记录错误日志
                _logger.LogError(ex, "获取换货转退货数据失败: {ErrorMessage}", ex.Message);
                return new List<MatchableDocumentItem>();
            }
        }

        /// <summary>
        /// 获取损失确认数据 - 从Debt表查询损失确认类型的应付单
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>损失确认数据</returns>
        private async Task<List<MatchableDocumentItem>> FetchLossRecognitionData(GetMatchableDocumentsRequest request)
        {
            try
            {
                _logger.LogInformation("FetchLossRecognitionData - 开始查询损失确认数据, CompanyId: {CompanyId}, SupplierId: {SupplierId}",
                    request.CompanyId, request.AgentId);

                // 创建损失确认查询参数 - 参照服务费的查询逻辑
                var storeInDetailQueryInput = new StoreInDetailQueryInput
                {
                    agentId = request.AgentId,
                    companyId = request.CompanyId,
                    // 不使用明细信息，只传递ID
                    InputBillId = request.MergeInputBillId,
                    page = 1,
                    limit = 1000 // 设置较大的值以获取所有数据
                };

                // 如果有时间范围，转换为时间戳
                if (request.StartDate.HasValue && request.EndDate.HasValue)
                {
                    storeInDetailQueryInput.storeInDateStart = request.StartDate.Value.ToUnixTimeMilliseconds().ToString();
                    storeInDetailQueryInput.storeInDateEnd = request.EndDate.Value.ToUnixTimeMilliseconds().ToString();
                }

                // 调用损失确认查询接口
                var (lossRecognitionData, totalCount) = await _inputBillQueryService.GetDebtsByType(storeInDetailQueryInput, DebtTypeEnum.lossrecognition);

                _logger.LogInformation("FetchLossRecognitionData - 获取损失确认数据成功, 总数: {TotalCount}", totalCount);

                // 转换为 MatchableDocumentItem 列表
                var result = new List<MatchableDocumentItem>();

                foreach (var item in lossRecognitionData)
                {
                    // 计算可用数量
                    decimal availableAmount = item.PolyQuantity - item.PolyInvoiceQuantity;

                    // 计算税率
                    decimal taxRate = item.TaxRate.HasValue ? item.TaxRate.Value : 0;

                    // 缓存中保持原始金额，写入mergeinputbillsubmitdetail时才转换成负数
                    decimal taxCost = availableAmount;

                    // 创建 MatchableDocumentItem 对象
                    var docItem = new MatchableDocumentItem(
                        BusinessType.LossRecognition,
                        item.StoreInItemCode,
                        DateTimeOffset.FromUnixTimeMilliseconds((long)item.StoreInDate)
                            .ToOffset(TimeSpan.FromHours(8)) // 明确使用UTC+8时区
                            .DateTime.Date, // 只保留日期部分，格式为 YYYY-MM-DD
                        "",
                        "",
                        1,
                        taxCost, // 原始金额，写入mergeinputbillsubmitdetail时才转换成负数
                        taxRate,
                        0,
                        item.ProductId,
                        null); // 损失确认没有ProductNameId

                    // 设置其他属性
                    docItem.BusinessItemCode = item.StoreInItemCode;
                    result.Add(docItem);

                    _logger.LogInformation("FetchLossRecognitionData - 添加损失确认项目: 单号={StoreInItemCode}, 可用金额={AvailableAmount}, 税率={TaxRate}, 含税单价={TaxCost}",
                        item.StoreInItemCode, availableAmount, taxRate, taxCost);
                }

                _logger.LogInformation("FetchLossRecognitionData - 损失确认数据查询完成, 返回数据量: {Count}", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "FetchLossRecognitionData - 查询损失确认数据失败, 错误: {ErrorMessage}", ex.Message);
                throw new ApplicationException($"获取损失确认数据失败: {ex.Message}");
            }
        }

        #endregion

        /// <summary>
        /// 创建查询参数 - 只使用时间、公司和供应商条件(购货修订和寄售转购货)
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>查询参数</returns>
        private static ManyStoreInDetailQueryInput CreateQueryInput(GetMatchableDocumentsRequest request)
        {
            // 创建查询参数 - 只使用时间、公司和供应商条件
            var input = new ManyStoreInDetailQueryInput
            {
                companyId = request.CompanyId,
                agentId = request.AgentId,
                typeList = new List<int> { 1 },
                // 不使用明细信息
                detailInfos = new List<DetailInfo>()
            };

            // 直接使用请求中的时间参数，不做额外处理
            if (request.StartDate.HasValue)
            {
                input.startTime = request.StartDate.Value.AddHours(8);
            }

            if (request.EndDate.HasValue)
            {
                input.endTime = request.EndDate.Value.AddHours(8);
            }

            return input;
        }


        /// <summary>
        /// 创建多对多出库查询参数 - 只使用时间、公司和供应商条件
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>多对多出库查询参数</returns>
        private static ManyStoreOutDetailQueryInput CreateManyToManyStoreOutQueryInput(GetMatchableDocumentsRequest request)
        {
            // 创建查询参数 - 只使用时间、公司和供应商条件
            var input = new ManyStoreOutDetailQueryInput
            {
                companyId = request.CompanyId,
                agentId = request.AgentId,
                typeList = new List<int> { 1, 6, 19 },
                Mark = 0
            };

            if (request.StartDate.HasValue)
            {
                input.billCodeDateStart = request.StartDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            if (request.EndDate.HasValue)
            {
                input.billCodeDateEnd = request.EndDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            return input;
        }

        /// <summary>
        /// 创建多对多换货转退货查询参数 - 只使用时间、公司和供应商条件
        /// </summary>
        /// <param name="request">获取可勾稽单据列表请求</param>
        /// <returns>多对多换货转退货查询参数</returns>
        private static ManyStoreExchangeBackDetailQueryInput CreateManyToManyStoreExchangeBackQueryInput(GetMatchableDocumentsRequest request)
        {
            // 创建查询参数 - 只使用时间、公司和供应商条件
            var input = new ManyStoreExchangeBackDetailQueryInput
            {
                companyId = request.CompanyId,
                agentId = request.AgentId,
                // 不使用明细信息
                detailInfos = new List<DetailInfo>()
            };

            // 直接使用请求中的时间参数，不做额外处理
            if (request.StartDate.HasValue)
            {
                input.billCodeDateStart = request.StartDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            if (request.EndDate.HasValue)
            {
                input.billCodeDateEnd = request.EndDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            return input;
        }
        private static ManyStoreInDetailQueryInput CreateStoreInDetailQueryInput(GetMatchableDocumentsRequest request)
        {
            // 创建查询参数 - 只使用时间、公司和供应商条件
            var input = new ManyStoreInDetailQueryInput
            {
                companyId = request.CompanyId,
                agentId = request.AgentId,
                // 不使用明细信息
                detailInfos = new List<DetailInfo>()
            };

            // 直接使用请求中的时间参数，不做额外处理
            if (request.StartDate.HasValue)
            {
                input.billCodeDateStart = request.StartDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            if (request.EndDate.HasValue)
            {
                input.billCodeDateEnd = request.EndDate.Value.ToUnixTimeMilliseconds(); // 转换为毫秒
            }

            return input;
        }

    }
}
