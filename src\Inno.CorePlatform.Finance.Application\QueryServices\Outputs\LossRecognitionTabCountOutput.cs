﻿using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class LossRecognitionTabCountOutput
    {
        /// <summary>
        /// 全部-1
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 待提交0
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 待审核1
        /// </summary>
        public int WaitAuditCount { get; set; }
        /// <summary>
        /// 已拒绝66
        /// </summary>
        public int RefuseCount { get; set; }
        /// <summary>
        /// 已完成99
        /// </summary>
        public int ComplateCount { get; set; }
        /// <summary>
        /// 我的5000
        /// </summary>
        public int MyCount { get; set; }

    }
    public class LossRecognitionItemListOutput : BasePo
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(200)]
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 应收单合计金额
        /// </summary>
      
        public decimal CreditAmount { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum CreditType { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? StatusStr
        {
            get
            {
                return Status.HasValue? Status.Value.GetDescription() : "";
            }
        }
        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// 详情
        /// </summary>
        public List<LossRecognitionDetailQueryOutput>? Details { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary> 
        public string? CreditBillCode { get; set; }
    }
}
