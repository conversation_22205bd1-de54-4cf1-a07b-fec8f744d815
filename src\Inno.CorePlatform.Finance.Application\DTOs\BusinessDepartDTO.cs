﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    /// <summary>
    /// 核算部门基类
    /// </summary>
    public class BusinessDepartDTO
    {
        /// <summary>
        /// 核算部门id
        /// </summary>
        public int? businessDeptId { get; set; }

        /// <summary>
        /// 核算部门名称（包含父级及所有父级名称）
        /// </summary>
        public string? businessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门路径（包含父级及所有父级路径）
        /// </summary>
        public string? businessDeptFullPath { get; set; }
    }
}
