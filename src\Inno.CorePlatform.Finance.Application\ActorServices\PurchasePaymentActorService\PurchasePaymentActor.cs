﻿using Dapr.Actors.Runtime;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.ActorServices.PurchasePaymentActorService.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.IO.RecyclableMemoryStreamManager;

namespace Inno.CorePlatform.Finance.Application.ActorServices.PurchasePaymentActorService
{
    public class PurchasePaymentActor : Actor, IPurchasePaymentActor
    {
        private IServiceProvider _serviceProvider;
        private readonly IGeneratePurchaseCreatedNotifyHandlerFactory _generatePurchaseCreatedNotifyHandlerFactory;
        private EventBusDTO Content { get; set; }
        private readonly DaprClient _daprClient;
        private readonly ILogger<PurchasePaymentActor> _logger;
        public PurchasePaymentActor(ActorHost host, IServiceProvider serviceProvider, IGeneratePurchaseCreatedNotifyHandlerFactory generatePurchaseCreatedNotifyHandlerFactory, DaprClient daprClient, ILogger<PurchasePaymentActor> logger) : base(host)
        {
            _serviceProvider = serviceProvider;
            _generatePurchaseCreatedNotifyHandlerFactory = generatePurchaseCreatedNotifyHandlerFactory;
            _daprClient = daprClient;
            _logger = logger;
        }

        public async Task Excute(EventBusDTO input)
        {
            Content = input;
            try
            {
                var purchaseCreatedNotifyHandler = new HandPurchaseCreatedNotify(_generatePurchaseCreatedNotifyHandlerFactory.GetInstance(input));
                await purchaseCreatedNotifyHandler.Hand(input, _serviceProvider);
            }
            catch (Exception ex)
            {
                string jsonStr = new PurchasePaymentActorEdaErrorMsgErr
                {
                    Code = input.BusinessCode,
                    PaymentCodes = input.PaymentCodes,
                    PurchaseOrderId = input.BusinessId,
                    PurchaseType = 1,
                    RelateId = input.RelateId
                }.ToJson();
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "purchase-all-created",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/PurchaseSub/Created"  //重试的回调方法路由 
                });
            }
        }
        protected override Task OnActivateAsync()
        {
            _logger.LogInformation($"PurchasePaymentActor被激活，采购单号：{Content?.BusinessCode}，时间：{DateTimeOffset.UtcNow:yyyy-MM-dd HH:mm:ss:ffffff}");
            return base.OnActivateAsync();
        }
        protected override Task OnActorMethodFailedAsync(ActorMethodContext actorMethodContext, Exception e)
        {
            _logger.LogInformation($"PurchasePaymentActor执行失败，采购单号：{Content?.BusinessCode}，参数：{Content?.ToJson()}，时间：{DateTimeOffset.UtcNow:yyyy-MM-dd HH:mm:ss:ffffff}");
            return base.OnActorMethodFailedAsync(actorMethodContext, e);
        }
        protected override Task OnPostActorMethodAsync(ActorMethodContext actorMethodContext)
        {
            _logger.LogInformation($"PurchasePaymentActor执行成功，采购单号：{Content?.BusinessCode}，时间：{DateTimeOffset.UtcNow:yyyy-MM-dd HH:mm:ss:ffffff}");
            return base.OnPostActorMethodAsync(actorMethodContext);
        }
        protected override Task OnDeactivateAsync()
        {
            _logger.LogInformation($"PurchasePaymentActor被停用，采购单号：{Content?.BusinessCode}，时间：{DateTimeOffset.UtcNow:yyyy-MM-dd HH:mm:ss:ffffff}");
            return base.OnDeactivateAsync();
        }
    }

    public record PurchasePaymentActorEdaErrorMsgErr
    {
        public Guid? RelateId { get; set; }
        /// <summary>
        /// 采购订单
        /// </summary>
        public Guid? PurchaseOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 采购类型 1-采购定单，2-寄售转购货，3经销购货修订
        /// </summary>
        public int? PurchaseType { get; set; }
        /// <summary>
        /// 付款单号集合，已逗号隔开
        /// </summary>
        public string? PaymentCodes { get; set; }
    }
}
