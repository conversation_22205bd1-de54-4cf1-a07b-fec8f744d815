﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using Microsoft.AspNetCore.Mvc;
using NPOI.POIFS.Crypt.Dsig.Facets;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Backend.Filters;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 进项发票操作
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InputBillExecuteController : BaseController
    {
        private readonly IInputBillAppService _inputBillAppService;
        private readonly ILogger<InputBillExecuteController> _logger;
        public override bool EnableParameterLogging { get; set; } = true;
        /// <summary>
        /// 进项发票各种操作
        /// </summary>
        public InputBillExecuteController(IInputBillAppService inputBillAppService, ILogger<InputBillExecuteController> logger, ISubLogService subLog) : base(subLog)
        {
            this._inputBillAppService = inputBillAppService;
            this._logger = logger;
        }

        /// <summary>
        /// 生成进项发票和进项发票详情-金蝶回调地址
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreateInputBill")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> CreateInputBill([FromBody] InputBillInputDTo input)
        {
            try
            {

                var res = await _inputBillAppService.CreateInputBill(input);
                if (res > 0)
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "提交成功"
                    };
                }
                else
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.ParamFailed,
                        Message = "提交失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 生成提交详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreateInputBillSubmit")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> CreateInputBillSubmit([FromBody] PloInputilDetail input)
        {
            try
            {
                var res = await _inputBillAppService.CreateInputBillSubmit(input.input, input.InputBillId, input.IsAdd);
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "提交成功"
                };
            }
            catch (Exception ex)
            {

                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 删除发票明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteBillSbumit")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> DeleteBillSubumit(InputBillDetailDeleteInput input)
        {
            try
            {
                var res = await _inputBillAppService.DeleteBillSubumit(input);
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "删除成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }
        /// <summary>
        /// 提交发票
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        [HttpPost("SubmitInputBill")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> SubmitInputBill([FromBody] List<Guid> guids)
        {
            try
            {
                var userName = CurrentUser.UserName;
                var res = await _inputBillAppService.SubmitInputBill(guids, userName);
                if (res == 0)
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "提交失败"
                    };
                }
                else
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "提交成功"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 忽略发票
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        [HttpPost("IgnoreBill")]
        public async Task<BaseResponseData<object>> IgnoreBill([FromBody] List<Guid> guids)
        {
            try
            { 
                var res = await _inputBillAppService.IgnoreBill(guids);
                if (res == 0)
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "提交失败"
                    };
                }
                else
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "提交成功"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 恢复发票
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        [HttpPost("RestoreBill")]
        public async Task<BaseResponseData<object>> RestoreBill([FromBody] List<Guid> guids)
        {
            try
            {
                var res = await _inputBillAppService.RestoreBill(guids);
                if (res == 0)
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "提交失败"
                    };
                }
                else
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "提交成功"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 平尾差
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        [HttpPost("EliminatingErrors")]
        [SkipLogging]
        public async Task<BaseResponseData<object>> EliminatingErrors(Guid id)
        {
            try
            {
                var res = await _inputBillAppService.EliminatingErrors(id);
                if (res == 0)
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "失败"
                    };
                }
                else
                {
                    return new BaseResponseData<object>
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "成功"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<object>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                };
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="inputBillId">发票ID</param>
        /// <param name="codes">单号集合</param>
        /// <param name="type">单据类型，1-寄售转购货单，2-入库单</param>
        /// <returns></returns>
        [HttpPost("AddDetailByCodes")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> AddDetailByCodes(AddByCodeInput data)
        {
            return await _inputBillAppService.AddDetailByCodes(data.InputBillId,data.Codes, data.Type);
        }

        /// <summary>
        /// 按Excel导入
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost("ExportInvoiceForBusinessBill")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> ExportInvoiceForBusinessBill(IFormFile file)
        {
            return await _inputBillAppService.ImportInvoiceForBusinessBill(file,false);
        }
        /// <summary>
        /// 按Excel导入，然后提交
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost("ExportInvoiceForBusinessBillAndSubmit")]
        public async Task<BaseResponseData<int>> ExportInvoiceForBusinessBillAndSubmit(IFormFile file)
        {
            return await _inputBillAppService.ImportInvoiceForBusinessBill(file,true);
        }

        /// <summary>
        /// 批量导入（N对N）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost("BatchExportInvoiceForBusinessBill")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> BatchExportInvoiceForBusinessBill(IFormFile file)
        {
            return await _inputBillAppService.BatchImportInvoiceForBusinessBill(file);
        }

        /// <summary>
        /// 批量导入（下载返回信息文件）
        /// </summary>
        /// <returns></returns>
        [HttpPost("BatchExportInvoiceForBusinessBillExcel")]
        [SkipLogging]
        public async Task<BaseResponseData<string>> BatchExportInvoiceForBusinessBillExcel(IFormFile file)
        {
            try
            {
                return await _inputBillAppService.BatchImportInvoiceForBusinessBillExcel(file);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 取消勾稽进项票
        /// </summary>
        /// <returns></returns>
        [HttpPost("CancelBill")]
        [SkipLogging]
        public async Task<BaseResponseData<int>> CancelBill(Guid id)
        {
            try
            {
                var userName = CurrentUser.UserName;
                return await _inputBillAppService.CancelBill(id, userName);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }

    public class AddByCodeInput 
    {
        public Guid InputBillId { get; set; }
        public List<string> Codes { get; set; }

        public int Type { get; set; } = 1;
    }
}

