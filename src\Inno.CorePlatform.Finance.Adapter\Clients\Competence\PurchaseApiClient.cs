﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class PurchaseApiClient : BaseDaprApiClient<PurchaseApiClient>, IPurchaseApiClient
    {
        public PurchaseApiClient(DaprClient daprClient, ILogger<PurchaseApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<PurchaseOutPut> GetByIdAsync(Guid id)
        {
            return await InvokeMethodAsync<PurchaseOutPut>(string.Format(AppCenter.Purchase_GetById, id), RequestMethodEnum.GET);
        }
        public async Task<PurchaseQueryInfoSimpleOutput> GetSimpleByCode(string code)
        {
            return await InvokeMethodAsync<PurchaseQueryInfoSimpleOutput>(string.Format(AppCenter.Purchase_GetSimpleByCode, code), RequestMethodEnum.POST);
        }
        public async Task<PurchaseDataOutput> GetList(PurchaseDataInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<PurchaseDataInput, PurchaseDataOutput>(input, AppCenter.Purchase_List, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 寄售转购货明细分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BasePageResult<ConsignToPurchaseDetailGroupOutput>> GetConsignToPurchaseDetailGroup(ConsignToPurchaseDetailGroupInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ConsignToPurchaseDetailGroupInput, BasePageResult<ConsignToPurchaseDetailGroupOutput>>(input, AppCenter.Purchase_GetConsignToPurchaseDetailGroup, RequestMethodEnum.POST);
        }
        /// <summary>
        /// 更新入票数量
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<int?> UpdateInvoiceQuantity(List<UpdateInvoiceQuantityInput> input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<List<UpdateInvoiceQuantityInput>, int?>(
                    input,
                    AppCenter.Purchase_UpdateInvoiceQuantity,
                    RequestMethodEnum.POST);

                // 记录接口返回结果
                _logger.LogInformation("UpdateInvoiceQuantity - 调用采购能力中心更新入票数量接口返回结果: {Result}", result);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdateInvoiceQuantity - 调用采购能力中心更新入票数量接口异常, 错误: {ErrorMessage}", ex.Message);
                throw new Exception($"调用采购能力中心更新入票数量接口异常: {ex.Message}", ex);
            }
        }
        protected override string GetAppId()
        {
            return AppCenter.Purchase_APPID;
        }

        /// <summary>
        /// 获取待入票的购货修订
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BasePageResult<PurchaseReviseForInputBill>> GetPurchaseReviseForInputBills(PurchaseReviseForInputBillQueryDto query)
        {
            return await InvokeMethodWithQueryObjectAsync<PurchaseReviseForInputBillQueryDto, BasePageResult<PurchaseReviseForInputBill>>(query, AppCenter.PurchaseReviseForInputBillQuery, RequestMethodEnum.POST);
        }

        public async Task<BaseResponseData<bool>> UpdatePurchaseReviseInviceAmount(List<UpdatePurchaseReviseInvoiceAmountInputDto> input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<List<UpdatePurchaseReviseInvoiceAmountInputDto>, int?>(
                    input,
                    AppCenter.PurchaseReviseInputBillUpdate,
                    RequestMethodEnum.POST);

                // 记录接口返回结果
                _logger.LogInformation("UpdatePurchaseReviseInviceAmount - 调用采购能力中心更新购货修订入票金额接口返回结果: {Result}", result);

                // 检查返回结果
                if (result == null)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新购货修订入票金额接口返回结果为空",
                        Data = false
                    };
                }

                // 检查返回的状态码
                if (result == 500)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新购货修订入票金额接口失败，接口返回状态码500",
                        Data = false
                    };
                }

                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "调用采购能力中心更新购货修订入票金额接口成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdatePurchaseReviseInviceAmount - 调用采购能力中心更新购货修订入票金额接口异常, 错误: {ErrorMessage}", ex.Message);
                throw new Exception($"调用采购能力中心更新购货修订入票金额接口异常: {ex.Message}", ex);
            }
        }

        public async Task<ServicePurchaseQueryInfoOutput> ServicePurchaseGetById(Guid id)
        {
            return await InvokeMethodAsync<ServicePurchaseQueryInfoOutput>(string.Format(AppCenter.Purchase_ServiceGetById, id), RequestMethodEnum.GET);
        }
        public async Task<List<RebateProvisionOfPurchaseOutput>> GetRebateProvision(RebateProvisionOfPurchaseInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<RebateProvisionOfPurchaseInput, List<RebateProvisionOfPurchaseOutput>>(input, AppCenter.Purchase_QueryDraftRebateSettlement, RequestMethodEnum.POST);
        }

        public async Task<PurchaseOrderOutput> GetOrderDetailsByPurchaseCode(QueryOrderDetailsByPurchaseCodeInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<QueryOrderDetailsByPurchaseCodeInput, PurchaseOrderOutput>(input, AppCenter.Purchase_QueryOrderDetailByPurchaseCodes, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 获取经销购货修订已收部分数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BasePageResult<ReconciliationOutput>> GetRevisionPurchaseOrderList(ReconciliationInput input)
        {
            try
            {
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, BasePageResult<ReconciliationOutput>>(input, AppCenter.GetRevisionPurchaseOrderList, RequestMethodEnum.POST);

            }
            catch (Exception)
            {

                throw new Exception("调用采购-获取经销购货修订已收部分数据失败");
            }
        }
        #region 进项票多对多
        /// <summary>
        /// 财务进项票多对多专用-获取寄售转购货明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>寄售转购货明细列表</returns>
        public async Task<List<ConsignToPurchaseDetailDto>> GetConsignToPurchaseDetailGroupForInvoice(ManyStoreInDetailQueryInput input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<ManyStoreInDetailQueryInput, List<ConsignToPurchaseDetailDto>>(input, AppCenter.Purchase_GetConsignToPurchaseDetailGroupForInvoice, RequestMethodEnum.POST);

                // 确保返回的数据不为null
                if (result == null)
                {
                    _logger.LogWarning("GetConsignToPurchaseDetailGroupForInvoice - 接口返回null");
                    return new List<ConsignToPurchaseDetailDto>();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetConsignToPurchaseDetailGroupForInvoice - 调用采购能力中心获取寄售转购货明细接口异常, 错误: {ErrorMessage}", ex.Message);

                // 返回一个空列表，而不是抛出异常
                return new List<ConsignToPurchaseDetailDto>();
            }
        }

        /// <summary>
        /// 财务进项票多对多专用-获取购货修订明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>购货修订明细列表</returns>
        public async Task<List<GetReviseOrderForFinanceInvoiceOutput>> GetPurchaseRevisionForFinanceInvoice(ManyStoreInDetailQueryInput input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<ManyStoreInDetailQueryInput, List<GetReviseOrderForFinanceInvoiceOutput>>(input, AppCenter.Purchase_GetReviseOrderForFinanceInvoice, RequestMethodEnum.POST);

                // 确保返回的数据不为null
                if (result == null)
                {
                    _logger.LogWarning("GetPurchaseRevisionForFinanceInvoice - 接口返回null");
                    return new List<GetReviseOrderForFinanceInvoiceOutput>();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetPurchaseRevisionForFinanceInvoice - 调用采购能力中心获取购货修订明细接口异常, 错误: {ErrorMessage}", ex.Message);

                // 返回一个空列表，而不是抛出异常
                return new List<GetReviseOrderForFinanceInvoiceOutput>();
            }
        }

        /// <summary>
        /// 财务进项票多对多专用-更新寄售转购货
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<BaseResponseData<bool>> UpdateConsignToPurchaseDetailGroupForInvoice(UpdateInvoiceQuantityForFinanceInvoiceInput input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<UpdateInvoiceQuantityForFinanceInvoiceInput, int?>(
                    input,
                    AppCenter.Purchase_UpdateConsignToPurchaseDetailGroupForInvoice,
                    RequestMethodEnum.POST);

                // 记录接口返回结果
                _logger.LogInformation("UpdateConsignToPurchaseDetailGroupForInvoice - 调用采购能力中心更新寄售转购货接口返回结果: {Result}", result);

                // 检查返回结果
                if (result == null)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新寄售转购货接口返回结果为空",
                        Data = false
                    };
                }

                // 检查返回的状态码
                if (result == 500)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新寄售转购货接口失败，接口返回状态码500",
                        Data = false
                    };
                }

                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "调用采购能力中心更新寄售转购货接口成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdateConsignToPurchaseDetailGroupForInvoice - 调用采购能力中心更新寄售转购货接口异常, 错误: {ErrorMessage}", ex.Message);
                throw new Exception($"调用采购能力中心更新寄售转购货接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 财务进项票多对多专用-更新购货修订入票金额
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<BaseResponseData<bool>> UpdateReviseOrderForFinanceInvoice(UpdateInvoiceAmountForFinanceInvoiceInput input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<UpdateInvoiceAmountForFinanceInvoiceInput, int?>(
                    input,
                    AppCenter.Purchase_UpdateReviseOrderForFinanceInvoice,
                    RequestMethodEnum.POST);
                _logger.LogInformation("UpdateConsignToPurchaseDetailGroupForInvoice - 调用采购能力中心更新购货修订接口返回结果: {Result}", result);

                // 检查返回结果
                if (result == null)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新购货修订接口返回结果为空",
                        Data = false
                    };
                }

                // 检查返回的状态码
                if (result == 500)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心更新购货修订接口失败，接口返回状态码500",
                        Data = false
                    };
                }

                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "调用采购能力中心更新购货修订接口成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "UpdateReviseOrderForFinanceInvoice - 调用采购能力中心更新购货修订入票金额接口异常, 错误: {ErrorMessage}", ex.Message);
                throw new Exception($"调用采购能力中心更新购货修订入票金额接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除采购预付记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> DeleteAdvancePayById(DeleteAdvancePayInput input)
        {
            try
            {
                var result = await InvokeMethodAsync<int?>(string.Format(AppCenter.Purchase_DeleteAdvancePayById, input.Id), RequestMethodEnum.POST);

                return new BaseResponseData<int>()
                {
                    Data = result ?? 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用采购能力中心删除预付记录接口异常, 错误: {ErrorMessage}", ex.Message);
                throw;
            }
        }
        #endregion

        /// <summary>
        /// 生成购货修订订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> GenerateAdvancePaymentRevise(List<PurchaseDetailsInput> input)
        {
            try
            {
                var result = await InvokeMethodWithQueryObjectAsync<List<PurchaseDetailsInput>, int?>(input, AppCenter.Purchase_GenerateAdvancePaymentRevise, RequestMethodEnum.POST);
                // 检查返回结果
                if (result == null)
                {
                    return new BaseResponseData<int>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心生成购货修订接口返回结果为空",
                        Data = 0
                    };
                }

                // 检查返回的状态码
                if (result == 500)
                {
                    return new BaseResponseData<int>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "调用采购能力中心生成购货修订接口失败，接口返回状态码500",
                        Data = 0
                    };
                }

                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<int>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "调用采购能力中心生成购货修订接口成功",
                    Data = result.HasValue ? result.Value : 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用采购能力中心生成购货修订接口异常, 错误: {ErrorMessage}", ex.Message);
                throw;
            }
        }
    }
}
