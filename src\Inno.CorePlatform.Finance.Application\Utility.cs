﻿
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Newtonsoft.Json;
using System.Security.Cryptography;
using System.Text;

namespace Inno.CorePlatform.Finance.Application
{
    /// <summary>
    /// 帮助类
    /// </summary>
    public class Utility
    {
        public static DateTimeOffset ConvertToOffSet(long timeSpan)
        {
            var date = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(timeSpan).ToLocalTime();
            return DateTime.SpecifyKind(date, DateTimeKind.Local);
        }
        public static DateTime ConvertDate(long timeSpan)
            => new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(timeSpan).ToLocalTime();

        /// <summary>
        /// 是否内部交易
        /// </summary>
        public static bool? IsInternalTransactions(int? relateCodeType)
        {
            //GROUP_RATE_APPLY(101, "集团寄售入库"),
            //GROUP_RETURN_APPLY(102, "集团寄售退回"),
            //GROUP_RETURN_UNQUALIFIED_APPLY(103, "集团寄售退回不合格"),
            //GROUP_ALLOT_APPLY(104, "集团调拨"),
            //GROUP_IN_APPLY(105, "集团调入"),
            //GROUP_OUT_APPLY(106, "集团调出"),
            //GROUP_SALE_RETURN_APPLY(107, "集团销售调回"),
            //GROUP_BAD_BREAKER(108, "集团销毁出库"),
            //GROUP_BAD_RETURN(109, "集团不合格品退货"),
            //StockGroupConsignmentToPurchase(110, "库存集团寄售转购货"),
            //TempGroupConsignmentToPurchase(111, "暂存集团寄售转购货"),
            //GROUP_BAD_RETURN(112, "集团调入-经销"),
            //GroupStoreInApplyPurchase(112, "集团调入-经销"),
            //GroupStore0utApplyPurchase(113, "集团调出-经销"),
            //GroupPurchaseUnqualifiedApply(114, "集团经销不合格品退货"),
            return null;
            if (!relateCodeType.HasValue)
            {
                return false;
            }
            var internalRelateCodeType = new List<int>() { 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114 };
            return internalRelateCodeType.Contains(relateCodeType.Value);

        }
        public static string GetCleanedCellString(object cellValue)
        {
            if (cellValue == null)
                return string.Empty;

            string value = cellValue.ToString().TrimEnd().TrimStart().Replace("\t", "").Replace("\n", "").Replace("\t", "");

            // 去除所有空白字符（包括 \n \r \t 等）
            return value;
        }

        /// <summary>
        /// 获取法人公司的开票规格
        /// </summary>
        /// <param name="product"></param>
        /// <param name="companyId"></param>
        /// <param name="orginDetailProductNo"></param>
        /// <returns></returns>
        public static async Task<string> GetCompanySpecificationAsync(ProductNoOuput product, string companyId, string orginDetailProductNo, IBDSApiClient bDSApiClient)
        {
            var specification = "";
            //获取公司的开票规格默认值
            var companyInfoOutput = await bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { companyId } });
            var companyInfo = companyInfoOutput?.FirstOrDefault();
            if (companyInfo == null || companyInfo.InvoiceSpec == null)
            {
                specification = product != null && !string.IsNullOrEmpty(product.productNo) ? product.productNo : orginDetailProductNo;
            }
            else if (companyInfo.InvoiceSpec == "product")
            {
                specification = product != null && !string.IsNullOrEmpty(product.productNo) ? product.productNo : orginDetailProductNo;
            }
            else if (companyInfo.InvoiceSpec == "specification")
            {
                specification = product != null && !string.IsNullOrEmpty(product.specification) ? product.specification : orginDetailProductNo;
            }
            else if (companyInfo.InvoiceSpec == "model")
            {
                specification = product != null && !string.IsNullOrEmpty(product.model) ? product.model : orginDetailProductNo;
            }
            return specification;
        }
        public static string GenerateMd5Hash(object input)
        {
            var json = JsonConvert.SerializeObject(input);
            using (var md5 = MD5.Create())
            {
                var inputBytes = Encoding.ASCII.GetBytes(json);
                var hashBytes = md5.ComputeHash(inputBytes);
                var sb = new StringBuilder();
                foreach (var t in hashBytes)
                {
                    sb.Append(t.ToString("X2"));
                }
                return sb.ToString();
            }
        }
    }
}
