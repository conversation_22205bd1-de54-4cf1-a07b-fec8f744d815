﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 发票入账服务接口
    /// </summary>
    public interface IInvoiceReceiptQueryService
    {
        /// <summary>
        /// 发票入账单主体查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<InvoiceReceiptItemQueryOutput>,int)> GetListAsync(InvoiceReceiptItemQueryInput input);
        /// <summary>
        /// 根据入账单主体Id查询明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<InvoiceReceiptDetailVoOutput>, int)> GetDetailsByItemIdAsync(InvoiceReceiptDetailQueryInput input);
        /// <summary>
        /// 发票明细删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeleteDetails(List<Guid?> ids,string? userId);
        /// <summary>
        /// 发票入账单创建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> Create(InvoiceReceiptCreateInput input);
        /// <summary>
        /// 发票入账编辑查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<InvoiceReceiptItemEditOutput>> GetItem([FromBody] InvoiceReceiptDetailQueryInput input);
        /// <summary>
        /// 发票入账单提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> Submit(InvoiceReceiptDetailQueryInput input);

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeleteAttachFileIds(InvoiceReceiptAttachFileInput input);
        /// <summary>
        /// 发票入账单删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> Remove(InvoiceReceiptDetailQueryInput input);
        /// <summary>
        /// 发票入账单下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<InvoiceReceiptItemQueryOutput>> DownLoad(InvoiceReceiptItemQueryInput input);
        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportByCoordinate([FromBody] InvoiceReceiptItemQueryInput query);
        Task<(int, string)> Cancel(InvoiceReceiptCancelInput input);
        Task<(int, string)> ChangeStatus(InvoiceReceiptCancelInput input);

        /// <summary>
        /// 根据InvoiceReceiptItemId标记应付入账
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> MarkDebtDetailsByInvoiceReceiptItemId(Guid id);

        /// <summary>
        /// 根据应收单号标记应付入账
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> MarkDebtDetailsByCreditBillCodes(List<string?> billCodes);
        /// <summary>
        /// 清单页签数量
        /// </summary>
        /// <param name="query"></param>
        Task<BaseResponseData<InvoiceReceiptListTabOutput>> GetTabCount(InvoiceReceiptItemQueryInput query);
        /// <summary>
        /// 根据公司单号获取业务单元
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<InvoiceReceiptsSelectAssemblyOutput>> GetServices(InvoiceReceiptsSelectAssemblyInput input);
        /// <summary>
        /// 根据公司单号业务单元获取客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<InvoiceReceiptsSelectAssemblyOutput>> GetCustomers(InvoiceReceiptsSelectAssemblyInput input);
        /// <summary>
        /// 获取回款天数等数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InvoiceReceiptsDayOutput> GetDayData(InvoiceReceiptsSelectAssemblyInput input);
        
    }
}
