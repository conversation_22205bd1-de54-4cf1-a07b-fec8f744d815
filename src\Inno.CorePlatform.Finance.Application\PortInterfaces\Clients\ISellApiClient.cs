﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface ISellApiClient
    {
        Task<List<CustomerProductBillingOutput>> GetCustomerProductBillingAsync(CustomerProductBillingInput input);
        Task<List<PreSaleListForProjectOutput>> GetPreSaleListForProjectAsync(QueryPreSaleListForProjectInput input);
        Task<SaleOutput> GetTempSaleByCodeAsync(string code);
        Task<PageResponse<PageQueryForFinancesOutput>> PageQueryForFinancesAsync(PageQueryForFinancesInput input);
        Task<List<ReconciliationOutput>> SaleForReconciliation(ReconciliationInput input);
        Task<List<ReconciliationOutput>> SaleForReconciliation_revise(ReconciliationInput input);
        Task<List<ReconciliationOutput>> SaleForReconciliation_follow(ReconciliationInput input);
        Task<List<ReconciliationOutput>> SaleForReconciliation_service(ReconciliationInput input);
        Task<List<SaleOutput>> GetSaleList(GetSaleListInput input);
        Task<List<RebateProvisionOfSaleOutput>> GetRebateProvision(RebateProvisionOfSaleInput input);
        Task<SaleOutput> GetSaleByCode(string code);
        Task<List<TempInventoryDetailOutput>> GetTempDetailsBySaleDetailIds(GetTempDetailsBySaleDetailIdsInput input);
        /// <summary>
        /// 销售提供webapi接口给财务，获取是否包含预收账期，是否驳回
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SaleAdvancePeriodInfoOutput>> GetSaleAdvancePeriodInfo(SaleAdvancePeriodInfoInput input);
        Task<List<GetSaleDetailsForFinanceOutput>> GetSaleDetailsForFinanceAsync(GetSaleDetailsForFinanceInput input);
    }
}
