﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 对账入参
    /// </summary>
    public class ReconciliationItemInput : BaseQuery
    {
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 月度如2023-12
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 穿透Id
        /// </summary>
        public Guid? ReconciliationItemId { get; set; }

        public string? CurrentUser { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        public Guid? UserId { get; set; }
    }

    public class ReconciliationItemExportInput
    {
        public Guid ReconciliationItemId { get; set; }
        public string CompanyName { get; set; }
        public string? BillTypeStr { get; set; }
    }
}
