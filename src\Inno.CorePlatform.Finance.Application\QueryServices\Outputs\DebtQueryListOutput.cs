﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using System.ComponentModel.DataAnnotations;
using System.Data;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 应付查询，出参
    /// </summary>
    public class DebtQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public string? BillDateStr
        {
            get
            {
                return BillDate.HasValue ? BillDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }

        /// <summary>
        /// 单据日期时间戳
        /// </summary>
        public long? BillDateTimestamp { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }


        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 应付类型
        /// </summary>
        public int? DebtType { get; set; }

        /// <summary>
        /// 应付类型
        /// </summary>
        public string TypeStr
        {
            get
            {
                return DebtType.HasValue ? ((DebtTypeEnum)DebtType).GetDescription() : "";
            }
        }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public int? AbatedStatus { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        public int InvoiceStatus { get; set; }
        /// <summary>
        /// 应付值 金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 预付款日期
        /// </summary>
        public DateTime? ProbablyPayTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; } = "none";
        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreatedByName { get; set; }
        /// <summary>
        /// 相关联应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }
        /// <summary>
        /// 相关联应收单Code
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 相关联应收单
        /// </summary>
        public CreditPo? CreditItemPo { get; set; }
        /// <summary>
        /// 相关联应付单详情
        /// </summary>
        public List<DebtDetailItem>? DebtDetails { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 应付余额
        /// </summary>
        public decimal LeftAmount
        {
            get
            {
                if (Value > 0)
                {
                    return Math.Abs(Value) - AbatmentAmount;
                }
                else
                {
                    return 0 - (Math.Abs(Value) - AbatmentAmount);
                }
            }
        }

        /// <summary>
        /// 本次冲销金额
        /// </summary>
        public decimal ThisAbatmentAmount { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary> 
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 进项票发票号
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 币种代码 
        /// </summary> 
        public string? CoinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>
        [MaxLength(200)]
        public string? CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary> 
        public decimal? RMBAmount { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary> 
        public string? PurchaseContactNo { get; set; }


        /// <summary>
        /// 项目Id
        /// </summary> 
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>  
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary> 
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 自动单据类型
        /// </summary>
        public string? AutoType { get; set; }

        /// <summary>
        ///  损失供应商承担金额
        /// </summary>
        public decimal? LossAgentBearValue { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? LossAgentStr { get { return LossAgentBearValue != null && LossAgentBearValue != 0 ? "是" : "否"; } }
    }

    /// <summary>
    /// 应付计划查询，出参
    /// </summary>
    public class DebtDetailQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }

        /// <summary>
        /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public string AccountPeriodTypeStr
        {
            get
            {
                return AccountPeriodType.GetDescription();
            }
        }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal? Discount { get; set; }
        [Comment("基础折扣")]
        public decimal? DistributionDiscount { get; set; }

        [Comment("供应链金融折扣")]
        public decimal? FinanceDiscount { get; set; }

        [Comment("SPD折扣")]
        public decimal? SpdDiscount { get; set; }

        [Comment("税率折扣")]
        public decimal? TaxDiscount { get; set; }

        [Comment("厂家折扣")]
        public decimal? CostDiscount { get; set; }
        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }

        /// <summary>
        ///  应付单
        /// </summary>
        public DebtQueryListOutput? Debt { get; set; }

        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }

        /// <summary>
        ///  应收单
        /// </summary>
        public CreditPo? Credit { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }


        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 预付款日期
        /// </summary>  
        public DateTime? ProbablyPayTime { get; set; }

        /// <summary>
        /// 预付款日期
        /// </summary>  
        public string ProbablyPayTimeStr
        {
            get
            {

                return ProbablyPayTime.HasValue ? ProbablyPayTime.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }

        /// <summary>
        /// 逾期天数
        /// </summary>
        public int OverdueDays
        {
            get
            {
                var ret = 0;
                if (ProbablyPayTime.HasValue)
                {
                    ret = (DateTime.Now - ProbablyPayTime.Value).Days;
                }
                return ret;
            }
        }
        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 结算方式 
        /// </summary>  
        public string? Settletype { get; set; }

        /// <summary>
        /// 到期日 
        /// </summary>  
        public DateTime? DraftBillExpireDate { get; set; }

        /// <summary>
        /// 回款日期
        /// </summary>  
        public DateTime? BackPayTime { get; set; }

        /// <summary>
        /// 修改后预计付款日期
        /// </summary>  
        public DateTime? AccountingPeriodDate { get; set; }
        /// <summary>
        /// 修改后预计付款日期
        /// </summary>  
        public string AccountingPeriodDateStr
        {
            get
            {
                return AccountingPeriodDate.HasValue ? AccountingPeriodDate.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }
        /// <summary>
        /// OA审批id
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        [Comment("附件Ids")]
        public string? AttachFileIds { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? UpdatedBy { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string? UpdatedTime { get; set; }

        /// <summary>
        /// 是否关联到批量付款
        /// </summary>
        public bool? IsRelateBatchPayment { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 账期天数
        /// </summary>
        public decimal? AccountPeriodDays { get; set; }
    }

    /// <summary>
    /// 应付执行明细查询，出参
    /// </summary>
    public class DebtDetailExcuteQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 应付执行计划Id
        /// </summary>
        public Guid DebtDetailId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int? AccountPeriodType { get; set; }
    }

    /// <summary>
    /// 创建批量付款——选取明细
    /// </summary>
    public class DebtDetailBulkOutput
    {
        /// <summary>
        ///Id
        /// </summary>
        public Guid? DebtDetilId { get; set; }
        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }
        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtCode { get; set; }
        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }
        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public string AccountPeriodTypeStr
        {
            get
            {
                return AccountPeriodType.GetDescription();
            }
        }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 预付款日期
        /// </summary>
        public DateTime? ProbablyPayTime { get; set; }
        /// <summary>
        /// 预付款日期
        /// </summary>
        public string ProbablyPayTimeStr
        {
            get
            {
                return ProbablyPayTime.HasValue ? ProbablyPayTime.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }
        /// <summary>
        /// 公司（付款单位）
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Id（付款单位）
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public string BillDateStr
        {
            get
            {
                return BillDate.HasValue ? BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 应收单位名称(应付供应商)
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        public string PaymentCode { get; set; } = "";
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinName { get; set; }
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>   
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary> 
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        public string? ReceiveCode { get; set; }
        public DateTime? BackPayTime { get; set; }
        public string BackPayTimeStr
        {
            get
            {
                if (BackPayTime.HasValue)
                {
                    return BackPayTime.Value.ToString("yyyy-MM-dd");
                }
                else
                {
                    return string.Empty;
                }
            }
        }

        /// <summary>
        /// SPD发票入账状态
        /// </summary>
        public int InvoiceReceiptStatus { get; set; }

        /// <summary>
        /// SPD发票入账状态
        /// </summary>
        public string InvoiceReceiptStatusStr
        {
            get
            {
                return InvoiceReceiptStatus == 99 ? "已入账" : "未入账";
            }
        }

        /// <summary>
        /// 认款日期
        /// </summary>
        public string RecognizeDate { get; set; }

        /// <summary>
        /// 收款单日期
        /// </summary>
        public string ReceiveDate { get; set; }
        /// <summary>
        /// 结算方式
        /// </summary>
        public string? Settletype { get; set; }
        /// <summary>
        /// 到期日
        /// </summary>
        public DateTime? DraftBillExpireDate { get; set; }
        /// <summary>
        /// 进项票金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 终端客户Id
        /// </summary>
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端客户名称
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 是否开（进项）票
        /// </summary>
        public bool? IsHaveInvoice { get; set; }

        /// <summary>
        /// 是否开（进项）票
        /// </summary>
        public string? IsHaveInvoiceStr
        {
            get
            {
                return IsHaveInvoice.HasValue && IsHaveInvoice.Value ? "已开票" : "未开票";
            }
        }

        /// <summary>
        /// 进项票发票号
        /// </summary>
        public string? InvoiceCodes { get; set; }
    }

    /// <summary>
    /// 批量付款单查询
    /// </summary>
    public class DebtDetailBulkQuery : BaseQuery
    {
        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }
        /// <summary>
        /// 公司（付款单位）
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 批量付款Id
        /// </summary>
        public Guid? PaymentAutoItemId { get; set; }
        /// <summary>
        ///创建=0；重新选取=1;添加明细=2；
        /// </summary>
        public int? CreatedType { get; set; }
        public string? DepartId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? StorIncode { get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentName { get; set; }


        /// <summary>
        /// 供应商Ids
        /// </summary>
        public List<Guid?>? AgentIds { get; set; }

        public Guid? UserId { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? customerId { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public string? BusinessUnitName { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 入账标识
        /// </summary>
        public string? InvoiceReceiptStatus { get; set; }

        /// <summary>
        /// 预计付款开始日期
        /// </summary>
        public DateTime? ProbablyPayTimeStart { get; set; }
        /// <summary>
        /// 预计付款结束日期
        /// </summary>
        public DateTime? ProbablyPayTimeEnd { get; set; }

        /// <summary>
        /// 终端客户Id
        /// </summary>
        public List<string>? HospitalId { get; set; }

        /// <summary>
        /// 当前登录人
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 是否内部供应商1=内部供应商，0/null=外部供应商
        /// </summary>
        public int? IsInnerAgent { get; set; }

        /// <summary>
        /// 收款单开始日期
        /// </summary>
        public DateTime? ReceiveDateStart { get; set; }
        /// <summary>
        /// 收款单结束日期
        /// </summary>
        public DateTime? ReceiveDateEnd { get; set; }

        /// <summary>
        /// 开票标识
        /// </summary>
        public bool? InvoiceStatus { get; set; }
    }

    /// <summary>
    /// 应付冲销，出参
    /// </summary>
    public class DebtAbatmentOutput { }

    /// <summary>
    /// 进行冲销列表
    /// </summary>
    public class AvailableAbatmentsOutput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        ///  金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 预付使用金额
        /// </summary>
        public decimal? UseAmount { get; set; }
        /// <summary>
        /// 本次冲销金额
        /// </summary>
        public decimal ThisAbatmentAmount { get; set; }

        /// <summary>
        /// 应付余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value) - AbatmentAmount; } }

        /// <summary>
        /// 应付类型
        /// </summary>
        public DebtTypeEnum? DebtType { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }
        /// <summary>
        /// 应付类型
        /// </summary>
        public string TypeStr
        {
            get
            {
                if (DebtType.HasValue)
                {

                    return DebtType.GetDescription();
                }
                else if (CreditType.HasValue)
                {

                    return CreditType.GetDescription();
                }
                else
                {
                    return "";
                }
            }
        }

        /// <summary>
        /// 采购单号
        /// </summary> 
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 是否对应冲销应付
        /// </summary>
        public bool DefaultCheck { get; set; } = false;

        /// <summary>
        /// 原单标识
        /// </summary>
        public bool IsOriginal { get; set; } = false;
    }

    /// <summary>
    /// 应付单详情
    /// </summary>
    public class DebtDetailItem
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }


        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }


        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }



        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }


        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 预付款日期
        /// </summary>  
        public DateTime? ProbablyPayTime { get; set; }

        /// <summary>
        /// 逾期天数
        /// </summary>
        public int OverdueDays
        {
            get
            {
                var ret = 0;
                if (ProbablyPayTime.HasValue)
                {
                    ret = (DateTime.Now - ProbablyPayTime.Value).Days;
                }
                return ret;
            }
        }
        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 结算方式 
        /// </summary>  
        public string? Settletype { get; set; }

        /// <summary>
        /// 到期日 
        /// </summary>  
        public DateTime? DraftBillExpireDate { get; set; }


    }

    /// <summary>
    /// 应收部分信息（公司客户认款单id以及应收单号）
    /// </summary>
    public class CreditPartInfo
    {
        /// <summary>
        /// 应收单id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public string? ServiceId { get; set; }

        /// <summary>
        /// 采购子系统是否隐藏配置
        /// </summary>
        public bool HiddenSaleAccountInfo { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class InputBillForDebtOutput
    {
        /// <summary>
        /// DebtId
        /// </summary>
        public Guid? DebtId { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? StoreInItemCode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string? InvoiceNumber { get; set; }
    }
}
