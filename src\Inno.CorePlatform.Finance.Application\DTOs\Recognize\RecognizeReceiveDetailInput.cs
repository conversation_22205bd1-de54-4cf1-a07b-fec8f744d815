﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class RecognizeReceiveDetailInput
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 发票号/订单号
        /// </summary>    
        public string? Code { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public int? Type { get; set; }
        /// <summary>
        /// 认款时间
        /// </summary>
        public DateTime? RecognizeDate { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal? Value { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool? IsSkip { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }
        /// <summary>
        /// 认款人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }

        /// <summary>
        /// 项目id
        /// </summary>    
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目单号
        /// </summary>    
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>    
        public string? ProjectName { get; set; }
        /// <summary>
        /// 认款单类型（1:货款,2:暂收款）
        /// </summary>
        public RecognizeReceiveClassifyEnum? ClassifyType { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public int? CollectionType { get; set; }

        /// <summary>
        /// 是否第三方回款客户
        /// </summary>
        public bool IsReturnCustomer { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>    
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>    
        public string? CustomerName { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>    
        public decimal? TotalAmount { get; set; }
    }

    public class RecognizeReceiveDetailDelDto
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 认款详情Ids
        /// </summary>
        public List<Guid> Ids { get; set; }
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 批量保存认款单明细入参
    /// </summary>
    public class RecognizeReceiveDetailsInput
    {
        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 认款单类型（1:货款,2:暂收款）
        /// </summary>
        public RecognizeReceiveClassifyEnum? ClassifyType { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public int? Type { get; set; }
        /// <summary>
        /// 批量单据
        /// </summary>
        public List<RecognizeReceiveDetails> List { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// 批量单据
    /// </summary>
    public class RecognizeReceiveDetails
    {
        /// <summary>
        /// 发票/订单/初始应收Id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 业务单号（发票号/订单号/应收单号）
        /// </summary>    
        public string? BusinessId { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal? Amount { get; set; }
        /// <summary>
        /// 可认款金额
        /// </summary>     
        public decimal? CanAmount { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool? IsSkip { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? remark { get; set; }
        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>    
        public Guid ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 终端客户id
        /// </summary>
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端客户
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 项目单号
        /// </summary>
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public int? Type { get; set; }
        /// <summary>
        /// 客户（订单）
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 客户（订单）
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 认款应收具体信息
        /// </summary>
        public List<CreditInfo>? CreditInfo { get; set; }
    }

    /// <summary>
    /// 前端查询认款明细对应应收入参
    /// </summary>
    public class CreditInfoQueryInput
    {
        /// <summary>
        /// 应收id
        /// </summary>
        public Guid? CreditId { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>    
        public string? BillCode { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditTypeStr { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 认款明细code
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int Type { get; set; }
    }

    public class RecognizeReceiveDetailsOfInvoiceInput
    {

        /// <summary>
        /// 编辑时使用
        /// </summary>
        public Guid? RecognizeReceiveItemId { get; set; }
        public List<ReceiveInvoice> ReceiveInvoices { get; set; }
    }
    public class ReceiveInvoice
    {
        /// <summary>
        /// 发票号/订单号
        /// </summary>    
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>     
        public decimal? Amount { get; set; }
    }

    /// <summary>
    /// 保存上游回款日期入参
    /// </summary>
    public class SaveBackTimeVo
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 明细数据
        /// </summary>
        public List<RecognizeReceiveDetailOutput>? Details { get; set; }
    }

    /// <summary>
    /// 撤销认款明细
    /// </summary>
    public class PartCancelInput
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 明细数据
        /// </summary>
        public List<Guid>? DetailIds { get; set; }
        /// <summary>
        /// 当前登录人
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 撤销暂收款明细
    /// </summary>
    public class PartCancelTempInput
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid ItemId { get; set; }
        /// <summary>
        /// 明细数据
        /// </summary>
        public List<CancelTempDetail?>? Details { get; set; }
        /// <summary>
        /// 当前登录人
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 暂收款撤销明细入参
    /// </summary>
    public class CancelTempDetail
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 已转货款金额（暂收款独有）
        /// </summary>
        public decimal? UseValue { get; set; }

        /// <summary>
        /// 撤销金额（暂收款独有）
        /// </summary>
        public decimal? CancelValue { get; set; }

        /// <summary>
        /// 余额（暂收款独有）
        /// </summary>
        public decimal? SurplusValue { get; set; }

        /// <summary>
        /// 可撤销金额（暂收款独有）
        /// </summary>
        public decimal? CurrentValue { get; set; }
    }

    /// <summary>
    /// 撤销认款通知销售入参
    /// </summary>
    public class CancelReceiptInput
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        public string? RecognizeCode { get; set; }

        /// <summary>
        /// 销售订单号集合
        /// </summary>
        public List<string>? SaleCodes { get; set; }
    }

    /// <summary>
    /// 导出明细
    /// </summary>
    public class ExportDetailsInput : BaseQuery
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 当前操作人
        /// </summary>
        public string? CurrentUserName { get; set; }

        /// <summary>
        /// 当前操作人
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int? Type { get; set; }
    }
}
