<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Dapr.AspNetCore" Version="1.14.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.5" />
		<PackageReference Include="Polly" Version="7.2.4" />
		<PackageReference Include="System.Linq.Dynamic.Core" Version="1.3.1" /> 
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Application\Inno.CorePlatform.Finance.Application.csproj" />
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Data\Inno.CorePlatform.Finance.Data.csproj" />
	</ItemGroup>

</Project>
