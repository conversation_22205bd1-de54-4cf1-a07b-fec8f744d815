﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Namotion.Reflection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class StageSurgeryAppService : BaseAppService, IStageSurgeryAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        public readonly ISginyApiClient _sginyApiClient;
        public StageSurgeryAppService(ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork unitOfWork,
            IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IKingdeeApiClient kingdeeApiClient,
            ISginyApiClient sginyApiClient,
            Func<int, TimeSpan> sleepDurationProvider = null) :
            base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            _inventoryApiClient = inventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _sginyApiClient = sginyApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            if (input.BusinessId.HasValue)
            {
                var data = await _sginyApiClient.StageSurgery_SelectById(new() { Id = input.BusinessId.Value });
                if (data != null)
                {
                    //暂存出库,单头
                    InventoryStoreInOutput inventoryStoreInOutput = new()
                    {
                        storeInCode = data.stageSurgeryNo,
                        applyCode = data.stageSurgeryNo,
                        storeInDate = data.billDate.HasValue ? data.billDate.Value : 0, 
                        customerId = data.hospitalId,
                        customerName = data.hospital,
                        companyId = data.companyId,
                        companyName = data.companyName,
                        businessDeptId = data.businessDeptId,
                        remark = data.remark,
                        storeInBy = data.createdBy,
                        storeInType = 4,//出库类型默认写暂存出库（4）
                        storeInDetails = new() { },
                    };

                    // 明细数据
                    if (data.detailRes != null && data.detailRes.Any())
                    {
                        foreach (var item in data.detailRes)
                        {
                            inventoryStoreInOutput.storeInDetails.Add(new()
                            {
                                projectId = item.projectId,
                                productId = item.productId.HasValue ? item.productId.Value : Guid.Empty,
                                productNo = item.productNo,
                                productNameId = item.productNameId,
                                productName = item.productName,
                                unitCost = item.unitCost,
                                standardUnitCost = item.standardUnitCost,
                                taxRate = item.taxRate,
                                quantity = item.confirmQuantity,
                                specification = item.specification,
                                agentId = item.agentId,
                                mark = item.mark,

                            });
                        }
                    }
                    var requestBody = JsonConvert.SerializeObject(input);
                    return await PushToKingdee(inventoryStoreInOutput, data.accountingDateTime,input.BusinessSubType, requestBody);
                }
                else
                {
                    return BaseResponseData<int>.Failed(0, "调用跟台 - 根据id查询跟台手术数据失败");
                }
            }
            else
            {
                return BaseResponseData<int>.Failed(0, "业务单据id不能为空！");
            }
        }
        private async Task<BaseResponseData<int>> PushToKingdee(
            InventoryStoreInOutput input,
            long accountingDateTime,
            string classify,
            string preRequestBody,
            bool? useBillDate = false)
        {
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            var inputKD = new HoldStockRemovalInput()
            {
                billno = input.storeInCode,
                jfzx_date = DateTimeHelper.LongToDateTime(input.storeInDate),
                jfzx_tallydate = DateTimeHelper.LongToDateTime(accountingDateTime),
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = companyInfo.companyName,
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = input.storeInBy ?? "none",
                StoreOutType = input.storeInType.Value,
                jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
            };
            if (input.storeInDetails != null && input.storeInDetails.Any())
            {
                var projectIds = input.storeInDetails.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);

                var productNameIds = input.storeInDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);

                inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
                var fk_jfzx_totalsalescost = 0m;
                foreach (var storeInDetail in input.storeInDetails)
                {
                    var thisProject = projectInfo.FirstOrDefault(t => t.Id == storeInDetail.projectId);
                    var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == storeInDetail.productNameId);
                    var jfzx_material = Guid.Empty;
                    if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationNewGuid.Value;
                    }
                    else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationGuid.Value;
                    }
                    if (storeInDetail.mark == 1 && storeInDetail.standardUnitCost == null)
                    {
                        throw new Exception("操作失败：原因，mark=1但是标准成本没有值");
                    }
                    var taxCost = storeInDetail.mark == 0 || storeInDetail.mark == 3 ? storeInDetail.unitCost.Value : storeInDetail.standardUnitCost.Value;
                    //var taxCost = storeInDetail.unitCost.Value;
                    var noTaxCost = Math.Round(taxCost / (1 + storeInDetail.taxRate.Value / 100.00M), 2);//不含税成本
                                                                                                         //销售成本总额
                    var noTaxCost10 = Math.Round(taxCost / (1 + storeInDetail.taxRate.Value / 100.00M), 10);//不含税成本
                    if (storeInDetail.mark == 0 || storeInDetail.mark == 3)
                    {
                        fk_jfzx_totalsalescost += noTaxCost10 * storeInDetail.quantity;
                    }
                    else
                    {
                        fk_jfzx_totalsalescost += storeInDetail.standardUnitCost.Value * storeInDetail.quantity;
                    }
                  
                    var detailInfo = new HoldStockRemovalDetail
                    {
                        jfzx_count = storeInDetail.quantity,
                        jfzx_material = jfzx_material.ToString().ToUpper(),
                        jfzx_model = storeInDetail.specification,
                        jfzx_projectnos = thisProject?.Code,
                        jfzx_suppliers = storeInDetail.agentId.Value.ToString().ToUpper(),
                        Mark = storeInDetail.mark,
                        jfzx_unitprice = storeInDetail.mark == 0 || storeInDetail.mark == 3 ? noTaxCost : storeInDetail.standardUnitCost.Value,
                
                    };
                    detailInfo.jfzx_sellingcost = detailInfo.jfzx_unitprice * storeInDetail.quantity;
                    if (detailInfo.jfzx_sellingcost != 0)
                    {
                        inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                    }
                }
                inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
                if (inputKD.holdStockRemovalEntrysModel.Count > 0)
                {
                    var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(new List<HoldStockRemovalInput> { inputKD }, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success)
                    {
                    }
                    else
                    {
                        throw new Exception(kingdeeRes.Message);
                    }
                }
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "订阅入库事件出错，原因：没有明细数据");
            }
            return BaseResponseData<int>.Success("操作成功");
        }
    }
}
