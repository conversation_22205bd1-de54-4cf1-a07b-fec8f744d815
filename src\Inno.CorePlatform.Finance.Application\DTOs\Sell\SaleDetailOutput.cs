﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class SaleDetailOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 订单Id
        /// </summary>
        public Guid? SaleId { get; set; }


        /// <summary>
        /// 订单Id
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 业务单元编号
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Code
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 产品类型（0=经销，1=寄售,2=集团寄售，3=集团经销)
        /// </summary>
        public MarkEnum? Mark { get; set; }

        /// <summary>
        /// 产品类型名称（0 经销 1 寄售)
        /// </summary>
        public string MarkName
        {
            get
            {
                return Mark.GetDescription();
            }
        }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 货号名称
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// 所属厂家ID
        /// </summary>
        public Guid ProducerId { get; set; }

        /// <summary>
        /// 所属厂家
        /// </summary>
        public string ProducerName { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string PackUnit { get; set; }

        /// <summary>
        /// 四级产品分类
        /// </summary>
        public string ProductTypeDesc { get; set; }

        /// <summary>
        /// 产品描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 转单数量
        /// </summary>
        public int TransferNum { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal OriginalPrice { get; set; }

        /// <summary>
        /// 优惠后单价
        /// </summary>
        public decimal Price { get; set; }


        /// <summary>
        /// 产品标准成本
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// 实际成本
        /// </summary>
        public decimal ActualCost { get; set; }

        /// <summary>
        /// 成本合计
        /// </summary>
        public decimal CostTotal
        {
            get
            {
                return ActualCost * Quantity;
            }
        }

        /// <summary>
        /// 售价合计
        /// </summary>
        public decimal Amount
        {
            get
            {
                return OriginalPrice * Quantity - DiscountAmount;
            }
        }

        /// <summary>
        /// 成本税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 销售税率
        /// </summary>
        public decimal? SalesTaxRate { get; set; }

        /// <summary>
        /// 项目标准销售价格
        /// </summary>
        public decimal? SaleStandardCost { get; set; }

        /// <summary>
        /// 修订明细ID
        /// </summary>
        public Guid? OriginalId { get; set; }

        public Guid? BatchId { get; set; }
        /// <summary>
        /// -1 原始修订明细 0 修订后明细
        /// </summary>
        public int IndexType { get; set; }
        /// 修订单价合计
        /// </summary>
        public decimal? TotalRevisePrice { get; set; }

        /// <summary>
        /// 修订范围
        /// </summary>
        public ReviseRangeEnum? ReviseRange { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public SaleDetailTypeEnum Type { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName
        {
            get
            {
                return Type.GetDescription();
            }
        }

        /// <summary>
        /// 条码信息
        /// </summary>
        public BarCodeInfo? BarCodeInfo { get; set; }

        /// <summary>
        /// 优惠券ID
        /// </summary>
        public Guid? CouponId { get; set; }

        /// <summary>
        /// 优惠金额
        /// </summary>
        public decimal DiscountAmount { get; set; } = 0;

        /// <summary>
        /// 活动ID
        /// </summary>
        public Guid? PromotionalActivitiesId { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGift { get; set; } = false;
        /// <summary>
        /// 货品类型
        /// </summary>
        public ProductTypeEnum ProductType { get; set; }
        /// <summary>
        /// 消费者单价（旺店通）
        /// </summary>
        public decimal? ConsumerPrice { get; set; }

        /// <summary>
        /// 积分红包单价（旺店通）
        /// </summary>
        public decimal? PlatformCouponPrice { get; set; }
    }
    /// <summary>
    /// 货品类型
    /// </summary>
    public enum ProductTypeEnum
    {
        /// <summary>
        /// 普通
        /// </summary>
        Normal = 0,

        /// <summary>
        /// 赠品
        /// </summary>
        Gift = 1,

        /// <summary>
        ///  兑换
        /// </summary>
        Exchange = 2
    }
    public enum SaleDetailTypeEnum
    {
        /// <summary>
        /// 正常
        /// </summary>
        [Description("正常")]
        Normal = 0,

        /// <summary>
        /// 选择
        /// </summary>
        [Description("选择")]
        Selected = 1,

        /// <summary>
        /// 换货
        /// </summary>
        [Description("换货")]
        HH = 2,

        /// <summary>
        /// 虚开
        /// </summary>
        [Description("虚开")]
        XK = 3
    }

    public record class BarCodeInfo(string? BarCode, string? BeginNo, string? LotNo, DateTime? ValidDate, string? ProductBarCode, string? Interval);

    public class Discount
    {
        /// <summary>
        /// 折扣名
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 折扣编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 折扣比例
        /// </summary>
        public decimal Value { get; set; }

    }

    public class AccountPeriod
    {
        /// <summary>
        /// 账期名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 账期类型
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 账期天数
        /// </summary>
        public int Day { get; set; }

        /// <summary>
        /// 账期比例
        /// </summary>
        public decimal Value { get; set; }


    }
}
