using System;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    #region 基类

    /// <summary>
    /// 多对多勾稽查询基础输入参数
    /// </summary>
    public abstract class ManyInventoryBaseQueryInput
    {
        /// <summary>
        /// 库存能力中心对应的开始时间（时间戳格式）
        /// </summary>
        public long? billCodeDateStart { get; set; }
        
        /// <summary>
        /// 库存能力中心对应的结束时间（时间戳格式）
        /// </summary>
        public long? billCodeDateEnd { get; set; }
        
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid companyId { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? agentId { get; set; }

        /// <summary>
        /// 类型列表
        /// </summary>
        public List<int> typeList { get; set; }

        /// <summary>
        /// 明细信息列表
        /// </summary>
        public List<DetailInfo> detailInfos { get; set; }
    }

    /// <summary>
    /// 明细信息
    /// </summary>
    public class DetailInfo
    {
        /// <summary>
        /// 产品名称ID
        /// </summary>
        public string productNameId { get; set; }

        /// <summary>
        /// 单位成本
        /// </summary>
        public string unitCost { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string taxRate { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string productId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? productName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
    }

    /// <summary>
    /// 多对多勾稽更新基础输入参数
    /// </summary>
    public abstract class ManyInventoryBaseUpdateInput
    {
        /// <summary>
        /// 进项发票明细
        /// </summary>
        public List<ManyInputInvoiceDetail> inputInvoiceDetails { get; set; }

        /// <summary>
        /// 发票日期
        /// </summary>
        public long invoiceDate { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNumber { get; set; }

        /// <summary>
        /// 发票类型字符串
        /// </summary>
        public string invoiceTypeStr { get; set; }
    }

    /// <summary>
    /// 进项发票明细
    /// </summary>
    public class ManyInputInvoiceDetail
    {
        /// <summary>
        /// 业务单号
        /// </summary>
        public string businessCode { get; set; }

        /// <summary>
        /// 当前入票数量
        /// </summary>
        public decimal currentInvoiceQuantity { get; set; }

        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal invoiceAmount { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public string productNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxRate { get; set; }

        /// <summary>
        /// 单位成本
        /// </summary>
        public decimal unitCost { get; set; }
    }

    #endregion

    #region 查询输入参数

    /// <summary>
    /// 多对多勾稽查询入库单明细输入参数
    /// </summary>
    public class ManyStoreInDetailQueryInput : ManyInventoryBaseQueryInput
    {
        /// <summary>
        /// 采购能力中心对应的开始时间
        /// </summary>
        public DateTimeOffset? startTime { get; set; }
        
        /// <summary>
        /// 采购能力中心对应的结束时间
        /// </summary>
        public DateTimeOffset? endTime { get; set; }
    }

    /// <summary>
    /// 多对多勾稽查询出库单明细输入参数
    /// </summary>
    public class ManyStoreOutDetailQueryInput : ManyInventoryBaseQueryInput
    {
        /// <summary>
        /// 标记（0=经销，1=寄售）
        /// </summary>
        public int Mark { get; set; } = 0;
    }

    /// <summary>
    /// 多对多勾稽查询换货转退货明细输入参数
    /// </summary>
    public class ManyStoreExchangeBackDetailQueryInput : ManyInventoryBaseQueryInput
    {
        // 继承基类的所有属性，无需额外添加
    }

    #endregion

    #region 更新输入参数

    /// <summary>
    /// 多对多勾稽更新入库单明细参数
    /// </summary>
    public class ManyStoreInUpdateInput : ManyInventoryBaseUpdateInput
    {
        // 继承基类的所有属性，无需额外添加
    }

    /// <summary>
    /// 多对多勾稽更新出库单明细参数
    /// </summary>
    public class ManyStoreOutUpdateInput : ManyInventoryBaseUpdateInput
    {
        // 继承基类的所有属性，无需额外添加
    }

    /// <summary>
    /// 多对多勾稽更新换货转退货单明细参数
    /// </summary>
    public class ManyStoreExchangeBackUpdateInput : ManyInventoryBaseUpdateInput
    {
        // 继承基类的所有属性，无需额外添加
    }

    #endregion

    #region 撤销输入参数

    /// <summary>
    /// 撤销入库单明细参数
    /// </summary>
    public class RevokeInventoryStoreInDetail
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public List<string> invoiceNumber { get; set; }
    }

    /// <summary>
    /// 撤销明细参数
    /// </summary>
    public class RevokeInventoryDetail
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNumber { get; set; }
    }

    #endregion
}
