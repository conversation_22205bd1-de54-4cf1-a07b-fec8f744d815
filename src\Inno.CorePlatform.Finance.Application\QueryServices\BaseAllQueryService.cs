﻿using Inno.CorePlatform.Finance.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    /// <summary>
    /// 能够查询所有数据表的接口
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public class BaseAllQueryService<TEntity> : IBaseAllQueryService<TEntity> where TEntity : class
    {
        protected readonly FinanceDbContext _dbContext;

        /// <summary>
        /// 通过构造函数注入得到数据上下文对象实例
        /// </summary>
        /// <param name="dbContext"></param>
        public BaseAllQueryService(FinanceDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null)
        {
            try
            {
                var query = _dbContext.Set<TEntity>().AsNoTracking();
                if (includeTableNames != null)
                {
                    foreach (var t in includeTableNames)
                    {
                        query = query.In(t);
                    }
                }
                return await query.FirstOrDefaultAsync(predicate);
            }
            catch
            {
                throw;
            }
        }

        public async Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null, Expression<Func<TEntity, TEntity>> SelectColumn = null)
        {
            try
            {
                var query = _dbContext.Set<TEntity>().AsQueryable();
                if (includeTableNames != null)
                {
                    foreach (var t in includeTableNames)
                    {
                        query = query.In(t);
                    }
                }
                query = query.Where(predicate);
                if (SelectColumn == null)
                {
                    return await query.AsNoTracking().ToListAsync();
                }
                else
                {
                    return await query.Select(SelectColumn).AsNoTracking().ToListAsync();
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        public Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null, bool isTracking = true, Expression<Func<TEntity, TEntity>> SelectColumn = null)
        {
            throw new NotImplementedException();
        }

        public IQueryable<TEntity> GetIQueryable(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null)
        {
            var query = _dbContext.Set<TEntity>().AsNoTracking().AsQueryable();
            if (includeTableNames != null)
            {
                foreach (var t in includeTableNames)
                {
                    query = query.In(t);
                }
            }
            query = query.Where(predicate);
            return query;
        }

        public async Task<bool> IsExistAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null)
        {
            try
            {
                var query = _dbContext.Set<TEntity>().AsQueryable();

                if (includeTableNames != null)
                {
                    foreach (var t in includeTableNames)
                    {
                        query = query.In(t);
                    }
                }

                return await query.AllAsync(predicate);
            }
            catch (Exception)
            {
                throw;
            }
        }
    }

    public static class IQueryableExtensions
    {
        /// <summary>
        /// 导航属性，参数：导航属性名称字符串，支持多表查询
        /// 多级导航属性：“属性名.属性名”  用‘.’连接
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <param name="Properts"></param>
        /// <returns></returns>
        public static IQueryable<T> In<T>(this IQueryable<T> obj, params string[] Properts) where T : class
        {
            IQueryable<T> data = obj;
            foreach (var prop in Properts)
            {
                data = data.Include(prop);
            }
            return data;
        }
    }
}
