﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    /// <summary>
    /// 查询基类，带page页码，limit 页面大小,Keyword关键字，
    /// </summary>
    public class BaseQuery
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int page { get; set; } = 1;
        /// <summary>
        /// 页面大小
        /// </summary>
        public int limit { get; set; } = 10;
        /// <summary>
        /// 关键字
        /// </summary>
        public string? searchKey { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public List<string>? sort { get; set; }
    }
}
