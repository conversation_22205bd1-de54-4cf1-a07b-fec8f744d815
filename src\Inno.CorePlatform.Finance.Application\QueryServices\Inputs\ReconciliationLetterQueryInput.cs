﻿using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 对账函查询，入参
    /// </summary>
    public class ReconciliationLetterQueryInput : BaseQuery
    {
        /// <summary>
        /// 截止日期 开始
        /// </summary>
        public long? DeadlineDateS { get; set; }
        /// <summary>
        /// 截止日期 开始
        /// </summary>
        public DateTime? DeadlineDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return DeadlineDateS != null ? new DateTime(tricks_1970 + long.Parse(DeadlineDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 截止日期 结束
        /// </summary>
        public long? DeadlineDateE { get; set; }
        /// <summary>
        /// 截止日期 结束
        /// </summary>
        public DateTime? DeadlineDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return DeadlineDateE != null ? new DateTime(tricks_1970 + long.Parse(DeadlineDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 申请单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }


        /// <summary>
        /// 客户
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 对账函模板
        /// </summary>
        public ReconciliationLetterEnum? ReconciliationLetterTmp { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }

        public Guid UserId { get; set; }
        /// <summary>
        /// 当前用户名
        /// </summary>
        public string? CurrentUser {  get; set; }
        /// <summary>
        /// id
        /// </summary>
        public Guid? Id { get; set; }
    }
}
