﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class InvoiceReceiptItemQueryInput : BaseQuery
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 单据开始日期
        /// </summary>
        public string? BillDateBeging { get; set; }

        /// <summary>
        /// 单据结束日期
        /// </summary>
        public string? BillDateEnd { get; set; }

        /// <summary>
        /// 单据号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 发票对账单状态
        /// </summary>
        public StatusEnum? Status { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public List<string?>? CreatedBy { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
    }

    /// <summary>
    /// 发票入账创建入参
    /// </summary>
    public class InvoiceReceiptCreateInput
    {
        public string? Id { get; set; }
        /// <summary>
        /// 事业部编码
        /// </summary>
        public string? BusinessArea { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        public Object? NewDepart { get; set; }
        public Object? Companys { get; set; }
        public Object? Customers { get; set; }
        public Object? Services { get; set; }
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; }
        public Guid CustomerId { get; set; }
        public string CustomerName { get; set; }
        public Guid ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int? BackAmountDays { get; set; }
        public int? SaleAccountPeriodDays { get; set; }
        public int? ActualBackAmountDays { get; set; }
        public string? Remark { get; set; }
        public List<AttachmentInput> Attachment { get; set; }
        public List<InvoiceReceiptDetailQueryOutput> InvoiceDetail { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; } = "none";
    }

    /// <summary>
    /// 附件信息
    /// </summary>
    public class AttachmentInput
    {
        public string? attachFileId { get; set; }
        public string? attachFileName { get; set; }
        public int? attachFileSize { get; set; }
    }

    /// <summary>
    /// 发票入账查询客户入参
    /// </summary>
    public class InvoiceReceiptsSelectAssemblyInput
    {
        public Guid? UserId { get; set; }
        public string? UserName { get; set; }
        public string? BusinessDeptId { get; set; }
        public Guid? CompanyId { get; set; }
        public Guid? ServiceId { get; set; }
        public Guid? CustomerId { get; set; }
    }
}
