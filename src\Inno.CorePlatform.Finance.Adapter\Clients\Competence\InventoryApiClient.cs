﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class InventoryApiClient : BaseDaprApiClient<InventoryApiClient>, IInventoryApiClient
    {
        public InventoryApiClient(DaprClient daprClient, ILogger<InventoryApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }


        public async Task<InventoryStoreInOutput> QueryStoreInByCode(string storeInCode)
        {
            return await InvokeMethodAsync<InventoryStoreInOutput>(string.Format(AppCenter.Inventory_QueryStoreInByCode, storeInCode), RequestMethodEnum.POST);
        }

        public async Task<List<InventoryStoreInOutput>> QueryStoreInByCodes(List<string> storeInCodes)
        {
            return await InvokeMethodWithQueryObjectAsync<object, List<InventoryStoreInOutput>>(storeInCodes, AppCenter.Inventory_QueryStoreInByCodes);
        }

        public async Task<InventoryRespon<InventoryStoreInOutput>> QueryStoreInByCompany(StoreInDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<StoreInDetailQueryInput, InventoryRespon<InventoryStoreInOutput>>(input, AppCenter.Inventory_QueryStoreInForFinance, RequestMethodEnum.POST);
        }

        public async Task<InventoryRespon<InventoryStoreInOutput>> QueryDetailForInputInvoice(StoreInDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<StoreInDetailQueryInput, InventoryRespon<InventoryStoreInOutput>>(input, AppCenter.Inventory_QueryDetailForInputInvoice, RequestMethodEnum.POST);
        }

        public async Task<InventoryStoreOutOutput> QueryStoreOutByCode(string storeOutCode)
        {
            return await InvokeMethodAsync<InventoryStoreOutOutput>(string.Format(AppCenter.Inventory_QueryStoreOutByCode, storeOutCode), RequestMethodEnum.POST);
        }

        public async Task<List<InventoryStoreOutOutput>> QueryStoreOutByCodes(List<string?>? storeOutCodes)
        {
            return await InvokeMethodWithQueryObjectAsync<object, List<InventoryStoreOutOutput>>(storeOutCodes, AppCenter.Inventory_QueryStoreOutByCodes);
        }

        public async Task<List<TraceCodeOutput>> QueryTraceInfoByCodes(List<string> codes)
        {
            var param = new { traceCodes = codes };
            //return await InvokeMethodAsync<List<TraceCodeOutput>>(string.Format(AppCenter.Invertory_QueryTraceInfo, codes));
            return await InvokeMethodWithQueryObjectAsync<object, List<TraceCodeOutput>>(param, AppCenter.Invertory_QueryTraceInfo);
        }

        public async Task<InventoryRespon<object>> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreInUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateDetailsForFinance, RequestMethodEnum.POST);
        }

        #region 经销调出
        public async Task<InventoryRespon<StoreOutDetailForFinanceOutput>> QueryStoreOutByCompany(StoreInDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<StoreInDetailQueryInput, InventoryRespon<StoreOutDetailForFinanceOutput>>(input, AppCenter.Inventory_QueryStoreOutForFinance, RequestMethodEnum.POST);
        }
        public async Task<InventoryRespon<object>> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreOutUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateStoreOutDetailsForFinance, RequestMethodEnum.POST);
        }

        public async Task<InventoryRespon<object>> UpdateInvoiceInfoRevoke(List<InventoryStoreOutUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreOutUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateInvoiceInfoRevokee, RequestMethodEnum.POST);
        }

        #endregion
        protected override string GetAppId()
        {
            return AppCenter.Inventory_APPID;
        }

        public async Task<List<Guid>> GetNoStoreRoomCompanys(List<Guid> companyIds)
        {
            return await InvokeMethodWithQueryObjectAsync<List<Guid>, List<Guid>>(companyIds, AppCenter.NoStoreRoomCompanys, RequestMethodEnum.POST);
        }

        public async Task<CheckDataWithKingdeeOutputDto> QueryTempStoreOutToKingdee(CheckDataWithKingdeeInputDto input)
        {
            return await InvokeMethodWithQueryObjectAsync<CheckDataWithKingdeeInputDto, CheckDataWithKingdeeOutputDto>(input, AppCenter.selectStoreOutByCompanyId, RequestMethodEnum.POST);
        }
        public async Task<InventoryStoreExchangeBackOutput> QueryByCodeForFinance(string billCode)
        {
            return await InvokeMethodAsync<InventoryStoreExchangeBackOutput>(string.Format(AppCenter.Inventory_QueryByCodeForFinance, billCode), RequestMethodEnum.POST);
        }
        public async Task<WaybillInfoDetailsOutput> WaybillInfoValidForFinance(WaybillInfoDetailsInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<WaybillInfoDetailsInput, WaybillInfoDetailsOutput>(input,AppCenter.WaybillInfoValidForFinance, RequestMethodEnum.POST);
        }
        /// <summary>
        /// 查询销售调回明细对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<QuerySaleRecallDetailRecOutput>> QuerySaleRecallDetailRec(QuerySaleRecallDetailRecInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<QuerySaleRecallDetailRecInput, List<QuerySaleRecallDetailRecOutput>>(input, AppCenter.Inventory_QuerySaleRecallDetailRec);
        }
    }

    public class InventoryApiClientOfResponseData : BaseDaprApiClient<InventoryApiClientOfResponseData>, IInventoryApiClientOfResponseData
    {
        public InventoryApiClientOfResponseData(DaprClient daprClient, ILogger<InventoryApiClientOfResponseData> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<BaseResponseData<string>> CreateExchangeInventory(ExchangeInventoryCreateRequestDto request)
        {
            // 如果请求中包含用户信息，使用指定用户信息的方法
            if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserName))
            {
                return await InvokeMethodWithQueryObjectAndUserAsync<ExchangeInventoryCreateRequestDto, BaseResponseData<string>>(
                    request, AppCenter.ExchangeInventory, request.UserId, request.UserName, RequestMethodEnum.POST);
            }
            else
            {
                return await InvokeMethodWithQueryObjectAsync<ExchangeInventoryCreateRequestDto, BaseResponseData<string>>(
                    request, AppCenter.ExchangeInventory, RequestMethodEnum.POST);
            }
        }

        public async Task<BaseResponseData<string>> CreateSureIncomeInventory(SureIncomeInventoryCreateRequestDto request)
        {
            // 如果请求中包含用户信息，使用指定用户信息的方法
            if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserName))
            {
                return await InvokeMethodWithQueryObjectAndUserAsync<SureIncomeInventoryCreateRequestDto, BaseResponseData<string>>(
                    request, AppCenter.SureIncomeInventory, request.UserId, request.UserName, RequestMethodEnum.POST);
            }
            else
            {
                return await InvokeMethodWithQueryObjectAsync<SureIncomeInventoryCreateRequestDto, BaseResponseData<string>>(
                    request, AppCenter.SureIncomeInventory, RequestMethodEnum.POST);
            }
        }
        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }
        protected override string GetAppId()
        {
            return AppCenter.Inventory_APPID;
        }
    }
}
