namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Services
{
    /// <summary>
    /// Dapr 事件发布服务接口
    /// </summary>
    public interface IDaprEventPublisher
    {
        /// <summary>
        /// 发布事件到指定主题
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="topicName">主题名称</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task PublishAsync<T>(string topicName, T eventData, CancellationToken cancellationToken = default);

        /// <summary>
        /// 发布事件到指定主题（带元数据）
        /// </summary>
        /// <typeparam name="T">事件数据类型</typeparam>
        /// <param name="topicName">主题名称</param>
        /// <param name="eventData">事件数据</param>
        /// <param name="metadata">元数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns></returns>
        Task PublishAsync<T>(string topicName, T eventData, Dictionary<string, string>? metadata, CancellationToken cancellationToken = default);
    }
}
