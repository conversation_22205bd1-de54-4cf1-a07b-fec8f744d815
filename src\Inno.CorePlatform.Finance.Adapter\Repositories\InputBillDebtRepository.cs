﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    internal class InputBillDebtRepository : EfBaseRepository<Guid, InputBillDebt, InputBillDebtPo>, IInputBillDebtRepository
    {
        private FinanceDbContext db;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbContext"></param>
        public InputBillDebtRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            db = dbContext;
        }

        public override Task<int> UpdateAsync(InputBillDebt root)
        {
            throw new NotImplementedException();
        }

        protected override InputBillDebtPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<InputBillDebtPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public async Task<int> AddManyAsync(List<InputBillDebt> inputBillDebts)
        {
            var lstPo = inputBillDebts.Adapt<List<InputBillDebtPo>>();
            await db.InputBillDebt.AddRangeAsync(lstPo);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }
    }
}
