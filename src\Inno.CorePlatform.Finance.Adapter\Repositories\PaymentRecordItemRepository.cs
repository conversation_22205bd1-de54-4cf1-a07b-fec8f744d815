﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PaymentRecordItemRepository : EfBaseRepository<Guid, PaymentRecordItem, PaymentRecordItemPo>, IPaymentRecordItemRepository
    {
        private FinanceDbContext _db;
        public PaymentRecordItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> InsetrCredit(PaymentRecordItem PaymentRecordItem, List<PaymentRecordDetail> PaymentRecordDetail)
        {
            var PaymentRecordItemPo = PaymentRecordItem.Adapt<PaymentRecordItemPo>();
            var PaymentRecordDetailPo = PaymentRecordDetail.Adapt<List<PaymentRecordDetailPo>>();
            PaymentRecordItemPo.PaymentRecordDetail = PaymentRecordDetailPo;
            _db.PaymentRecordItems.Add(PaymentRecordItemPo);
            return await _db.SaveChangesAsync();
        }

        public override async Task<int> UpdateAsync(PaymentRecordItem root)
        {
            var isExist = await _db.PaymentRecordItems.AnyAsync(x => x.Id == root.Id);
            if (isExist)
            {
                var po = root.Adapt<PaymentRecordItemPo>();
                _db.PaymentRecordItems.Update(po);
                if (UowJoined) return 0;
                return await _db.SaveChangesAsync();
            }
            else
            {
                return 0;
            }
        }

        protected override PaymentRecordItemPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<PaymentRecordItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
