﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Data.Utilities;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Inno.CorePlatform.Sell.Application.Extensions;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using OfficeOpenXml;
using System.Linq.Expressions;
using System.Security.Cryptography;
using System.Text;


namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 运营制作开票明细
    /// </summary>
    public class CustomizeInvoiceQueryService : QueryAppService, ICustomizeInvoiceQueryService
    {
        private readonly IPCApiClient _pCApiClient;
        private readonly FinanceDbContext _db;
        private readonly ISellApiClient _sellApiClient;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICodeGenClient _codeGenClient;
        private readonly ICustomizeInvoiceItemRepository _customizeInvoiceItemRepository;
        private readonly ICustomizeInvoiceDetailRepository _customizeInvoiceDetailRepository;
        private readonly ICreditQueryService _creditQueryService;
        private readonly IConfiguration _configuration;
        private readonly CorePlatform.Common.Clients.Interfaces.ICoordinateClient _coordinateclient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IICApiClient _icapiClient;
        private readonly IEasyCachingProvider _easyCaching;

        public static List<CreditTypeEnum?> _creditTypeMarkLst = new List<CreditTypeEnum?>{
            CreditTypeEnum.revise,
            CreditTypeEnum.origin,
            CreditTypeEnum._return,
            CreditTypeEnum.sale,
            CreditTypeEnum.outstore,
            CreditTypeEnum.purchase,
            CreditTypeEnum.rebate,
            CreditTypeEnum.equipment
        };
        public CustomizeInvoiceQueryService(
            FinanceDbContext db,
            ISellApiClient sellApiClient,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IWeaverApiClient weaverApiClient,
            IPCApiClient pCApiClient,
            IUnitOfWork unitOfWork,
            ICodeGenClient codeGenClient,
            IConfiguration configuration,
            CorePlatform.Common.Clients.Interfaces.ICoordinateClient coordinateclient,
            ICustomizeInvoiceItemRepository customizeInvoiceItemRepository,
            ICustomizeInvoiceDetailRepository customizeInvoiceDetailRepository,
            ICreditQueryService creditQueryService,
            IICApiClient icapiClient,
            IEasyCachingProvider easyCaching,
            IAppServiceContextAccessor? contextAccessor) : base(contextAccessor)
        {
            _db = db;
            _sellApiClient = sellApiClient;
            _inventoryApiClient = inventoryApiClient;
            _bDSApiClient = bDSApiClient;
            _weaverApiClient = weaverApiClient;
            _pCApiClient = pCApiClient;
            _unitOfWork = unitOfWork;
            _codeGenClient = codeGenClient;
            _customizeInvoiceItemRepository = customizeInvoiceItemRepository;
            _customizeInvoiceDetailRepository = customizeInvoiceDetailRepository;
            _creditQueryService = creditQueryService;
            _icapiClient = icapiClient;
            _configuration = configuration;
            _coordinateclient = coordinateclient;
            _appServiceContextAccessor = contextAccessor;
            _easyCaching = easyCaching;
        }
        /// <summary>
        /// 获取原始开票明细查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>  
        public async Task<(List<OriginDetailOutput>, int)> GetOriginDetailAsync(OriginDetailQueryInput input)
        {
            var cacheKey = $"GetCreditDetails_{Utility.GenerateMd5Hash(JsonConvert.SerializeObject(input))}";
            var cachedResult = await _easyCaching.GetAsync<List<OriginDetailOutput>>(cacheKey);
            if (cachedResult != null && cachedResult.Value != null)
            {
                return (cachedResult.Value, cachedResult.Value.Count);
            }
            try
            {
                var list = await GetCreditDetails(input);
                // 设置缓存，有效期为5秒
                await _easyCaching.SetAsync(cacheKey, list, TimeSpan.FromSeconds(5));
                return (list, list.Count);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

       
        private async Task<List<OriginDetailOutput>> GetCreditDetails(OriginDetailQueryInput input)
        {
            try
            {
                // 最终返回集合
                var retList = new List<OriginDetailOutput>();
                List<OriginDetailOutput> originDetailOutput_List = new List<OriginDetailOutput>();
                var credits = await _db.Credits.Include(p => p.CreditDetails).Where(p => input.CreditBillCodes != null && input.CreditBillCodes.ToHashSet().Contains(p.BillCode)).ToListAsync();
                var saleCustomerProducts = new List<SelectProductInvoicesOutput>();

                List<OriginDetailOutput> reviseDetailOutputLst = new List<OriginDetailOutput>();
                //按关联业务单号 拉取
                if (input.RelateCodes != null && input.RelateCodes.Any())
                {
                    int index = 0;
                    var creditIds = credits.Select(p => p.Id).ToHashSet();
                    var existsCreditDetails = await _db.CreditDetails.Where(p => creditIds.Contains(p.CreditId)).OrderBy(p => p.CreditId).ToListAsync();
                    var existsCreditIds = existsCreditDetails.Select(p => p.CreditId).Distinct().ToList();
                    var existsCredits = credits.Where(p => existsCreditIds.Contains(p.Id)).ToList();
                    var existsRelateCodes = existsCredits.Select(p => p.RelateCode).Distinct().ToList();

                    var relateCodes = credits.Select(p => p.RelateCode).ToList();
                    foreach (var item in existsRelateCodes)
                    {
                        var indexTemp = relateCodes.IndexOf(item);
                        if (indexTemp > -1)
                        {
                            relateCodes.RemoveAt(indexTemp);
                        }
                    }
                    var invoiceAll = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value) && p.IsCancel != true).ToListAsync();

                    var productIds = new List<Guid>();
                    var productNos = new List<string>();
                    #region 销售出库
                    SaleOutput saleOut = new SaleOutput();
                    var saleOuts = new List<SaleOutput>();
                    var saleCodes = relateCodes.Where(p => p.Contains("SA")).Distinct().ToList();
                    if (saleCodes.Any())
                    {
                        saleOuts = await _sellApiClient.GetSaleList(new GetSaleListInput
                        {
                            BillCodes = saleCodes,
                            PageNum = 1,
                            PageSize = int.MaxValue
                        });
                        var productLstNos = saleOuts.Select(p => p.SaleDetails.Select(s => s.ProductNo)).ToList();
                        foreach (var item in productLstNos)
                        {
                            productNos.AddRange(item);
                        }
                        var productLstIds = saleOuts.Select(p => p.SaleDetails.Select(s => s.ProductId)).ToList();
                        foreach (var item in productLstIds)
                        {
                            productIds.AddRange(item);
                        }
                    }
                    #endregion

                    #region 出库
                    InventoryStoreOutOutput storeout = new InventoryStoreOutOutput();
                    var storeOuts = new List<InventoryStoreOutOutput>();
                    var storeOutCodes = relateCodes.Where(p => p.Contains("SO")).Distinct().ToList();
                    if (storeOutCodes.Any())
                    {
                        storeOuts = await _inventoryApiClient.QueryStoreOutByCodes(storeOutCodes);
                        var productLstNos = storeOuts.Select(p => p.Details.Select(s => s.productNo)).ToList();
                        foreach (var item in productLstNos)
                        {
                            productNos.AddRange(item);
                        }

                        var productLstIds = storeOuts.Select(p => p.Details.Select(s => s.productId.Value)).ToList();
                        foreach (var item in productLstIds)
                        {
                            productIds.AddRange(item);
                        }
                    }
                    #endregion

                    #region 入库
                    InventoryStoreInOutput storein = new InventoryStoreInOutput();
                    var storeIns = new List<InventoryStoreInOutput>();
                    var refundStoreInDetail = new List<GetRefundStoreInDetailOutput>();
                    var storeInCodes = relateCodes.Where(p => p.Contains("SI")).Distinct().ToList();
                    if (storeInCodes.Any())
                    {
                        storeIns = await _inventoryApiClient.QueryStoreInByCodes(storeInCodes);

                        var productLstNos = storeIns.Select(p => p.storeInDetails.Select(s => s.productNo)).ToList();
                        foreach (var item in productLstNos)
                        {
                            productNos.AddRange(item);
                        }
                        var productLstIds = storeIns.Select(p => p.storeInDetails.Select(s => s.productId)).ToList();
                        foreach (var item in productLstIds)
                        {
                            productIds.AddRange(item);
                        }
                        foreach (var item in storeIns)
                        {
                            if (!string.IsNullOrEmpty(item.relateCode))
                            {
                                var refund = await _icapiClient.GetRefundStoreInDetail(item.relateCode);
                                refundStoreInDetail.Add(refund);
                            }
                        }
                    }
                    #endregion

                    #region 销售开票名称
                    if (existsCreditDetails.Any())
                    {
                        var existsProductIds = existsCreditDetails.Select(p => p.ProductId.Value).Distinct().ToList();
                        var existsProductNos = existsCreditDetails.Select(p => p.ProductNo).Distinct().ToList();
                        productIds.AddRange(existsProductIds);
                        productNos.AddRange(existsProductNos);
                    }
                    if (productIds.Any())
                    {
                        var selectProductInvoicesInputData = new List<SelectProductInvoicesInputData>();
                        foreach (var productId in productIds)
                        {
                            selectProductInvoicesInputData.Add(new SelectProductInvoicesInputData
                            {
                                ProductId = productId,
                                CustomerId = credits.First().CustomerId,
                            });
                        }
                        saleCustomerProducts = await _bDSApiClient.SelectProductInvoices(new SelectProductInvoicesInput
                        {
                            CompanyId = credits.First().CompanyId,
                            List = selectProductInvoicesInputData
                        });
                    }
                    #endregion

                    #region 原始包装规格
                    productNos = productNos.Where(p => !string.IsNullOrEmpty(p)).Distinct().ToList();
                    var products = await _bDSApiClient.GetByNos(productNos, credits.First().CompanyId.ToString());
                    #endregion

                    var customizeInvoiceCancelDetails = await _db.CustomizeInvoiceDetail.Include(p => p.CustomizeInvoiceItem).Where(p => (input.CreditBillCodes != null && input.CreditBillCodes.ToHashSet().Contains(p.CreditBillCode)) && p.CustomizeInvoiceItem.Status == CustomizeInvoiceStatusEnum.Cancel).ToListAsync();

                    foreach (var credit in credits)
                    {
                        if (existsCreditDetails.Any(p => p.CreditId == credit.Id))
                        {
                            continue;
                        }
                        var relateCode = credit.RelateCode;
                        var invoices = invoiceAll.Where(p => p.CreditId == credit.Id).ToList();
                        if (!string.IsNullOrEmpty(relateCode))
                        {
                            //根据RelateCode单号，获取已制作开票产品数量 
                            var ismark = _creditTypeMarkLst.Contains(credit.CreditType);
                            if (relateCode.ToLower().IndexOf("sa") > -1)
                            {
                                //暂存核销,订单修订事件,查询上游单据 
                                saleOut = saleOuts.FirstOrDefault(p => p.BillCode == relateCode);
                                if (saleOut != null && saleOut.SaleDetails != null && saleOut.SaleDetails.Any())
                                {
                                    var details = saleOut.SaleDetails.Where(p => ismark ? (p.Mark == MarkEnum.Park || p.Mark == MarkEnum.GroupStoreIn) : p.Mark == MarkEnum.Normal).ToList();
                                    if (credit.Mark.HasValue)
                                    {
                                        details = saleOut.SaleDetails.Where(p => p.Mark == (MarkEnum)credit.Mark).ToList();
                                    }
                                    details = details.Where(p => p.ServiceId == credit.ServiceId).ToList();
                                    if (credit.ProjectId.HasValue)
                                    {
                                        details = details.Where(p => p.ProjectId == credit.ProjectId).ToList();
                                    }
                                    if (credit.CreditType == CreditTypeEnum.selfrevise || credit.CreditType == CreditTypeEnum.revise || credit.CreditType == CreditTypeEnum.servicefeerevise)
                                    {
                                        if (credit.Value != details.Sum(p => p.Amount))
                                        {
                                            var OriginalGroup = details.GroupBy(p => new { p.OriginalId, p.BatchId }).ToList();
                                            if (credit.Value > 0)
                                            {
                                                details = OriginalGroup.Where(p => p.Sum(p => p.Quantity * p.Price) > 0).SelectMany(p => p).ToList();
                                            }
                                            else
                                            {
                                                details = OriginalGroup.Where(p => p.Sum(p => p.Quantity * p.Price) < 0).SelectMany(p => p).ToList();
                                            }
                                        }
                                    }

                                    foreach (var orginDetail in details.OrderByDescending(p => p.ProductNo).ThenBy(p => p.Quantity))
                                    {
                                        OriginDetailOutput originDetailOutput_Model = new OriginDetailOutput();
                                        SelectProductInvoicesOutput customerProduct = null;
                                        var product = products.FirstOrDefault(x => x.productNo == orginDetail.ProductNo);
                                        if (saleCustomerProducts != null && saleCustomerProducts.Any())
                                        {
                                            customerProduct = saleCustomerProducts.FirstOrDefault(p => p.ProductId == orginDetail.ProductId);
                                        }

                                        //排除已开票数量
                                        decimal dqty = orginDetail.Quantity;
                                        var originalCost = orginDetail.ActualCost;
                                        var price = (credit.CreditType == CreditTypeEnum._return || credit.CreditType == CreditTypeEnum.selfreturn) && orginDetail.Price > 0 ? -decimal.Parse(orginDetail.Price.ToString("F4")) : decimal.Parse(orginDetail.Price.ToString("F4"));
                                        //如果是旺店通price取对应的平台金额
                                        if (saleOut.SourceName == "旺店通" && saleOut.SaleType != SaleTypeEnum.SaleRevise)
                                        {
                                            if (credit.CreditSaleSubType == CreditSaleSubTypeEnum.personal)
                                            {
                                                price = orginDetail.ConsumerPrice.HasValue ? orginDetail.ConsumerPrice.Value : 0;
                                            }
                                            else if (credit.CreditSaleSubType == CreditSaleSubTypeEnum.platform)
                                            {
                                                price = orginDetail.PlatformCouponPrice.HasValue ? orginDetail.PlatformCouponPrice.Value : 0;
                                                orginDetail.OriginalPrice = Math.Abs(price);
                                                originalCost = 0;//红包的原始成本默认为0
                                            }
                                        }

                                        //修订应收
                                        originDetailOutput_Model.ProductNo = orginDetail.ProductNo;
                                        originDetailOutput_Model.OriginDetailId = Guid.NewGuid().ToString();
                                        originDetailOutput_Model.OriginProductName = orginDetail.ProductName;
                                        originDetailOutput_Model.OriginSpecification = orginDetail.ProductNo;
                                        originDetailOutput_Model.Price = Math.Abs(price);
                                        originDetailOutput_Model.OriginalPrice = Math.Abs(orginDetail.OriginalPrice);
                                        originDetailOutput_Model.OriginalCost = originalCost;
                                        originDetailOutput_Model.Quantity = price < 0 || orginDetail.IndexType == -1 ? -dqty : dqty;//价格<0时，价格永远为正数，数量变为负数
                                        originDetailOutput_Model.TaxRate = orginDetail.SalesTaxRate;
                                        originDetailOutput_Model.CreditBillCode = credit.BillCode;
                                        originDetailOutput_Model.CustomerId = credit.CustomerId.HasValue ? credit.CustomerId.ToString() : "";
                                        originDetailOutput_Model.CustomerName = credit.CustomerName;
                                        originDetailOutput_Model.RelateCode = relateCode;
                                        originDetailOutput_Model.OrderNo = credit.OrderNo;
                                        originDetailOutput_Model.OriginalId = orginDetail.OriginalId;
                                        originDetailOutput_Model.BatchId = orginDetail.BatchId;
                                        originDetailOutput_Model.TaxTypeNo = "";
                                        originDetailOutput_Model.ProductId = orginDetail.ProductId;
                                        originDetailOutput_Model.AgentId = orginDetail.AgentId.ToString();
                                        originDetailOutput_Model.IFHighValue = product?.IfHighValue;
                                        originDetailOutput_Model.OriginalPackSpec = product != null ? product.packDes : string.Empty;
                                        originDetailOutput_Model.AgentId = orginDetail.AgentId.ToString();
                                        if (credit.CreditType == CreditTypeEnum.selfrevise || credit.CreditType == CreditTypeEnum.revise || credit.CreditType == CreditTypeEnum.servicefeerevise)
                                        {
                                            originDetailOutput_Model.BatchId = orginDetail.BatchId;
                                            reviseDetailOutputLst.Add(originDetailOutput_Model);
                                        }
                                        else
                                        {
                                            originDetailOutput_Model.SaleDetailId = orginDetail.Id;
                                            originDetailOutput_List.Add(originDetailOutput_Model);
                                        }
                                        //销售子系统取值，未配置的从基础数据取值
                                        originDetailOutput_Model.ProductName = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceName) ? (product != null && !string.IsNullOrEmpty(product.productName) ? product.productName : orginDetail.ProductName) : customerProduct.InvoiceName;
                                        originDetailOutput_Model.PackUnit = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceUnit) ? (product != null ? product.packUnitDesc : orginDetail.PackUnit) : customerProduct.InvoiceUnit;
                                        originDetailOutput_Model.OriginPackUnit = product != null ? product.packUnitDesc : string.Empty;
                                        originDetailOutput_Model.Specification = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec) ? product.productNo : customerProduct.InvoiceSpec;
                                        originDetailOutput_Model.Value = originDetailOutput_Model.Price * originDetailOutput_Model.Quantity;
                                        originDetailOutput_Model.ServiceId = orginDetail.ServiceId;
                                        originDetailOutput_Model.ProjectId = orginDetail.ProjectId;
                                        originDetailOutput_Model.PriceSource = saleOut.PriceSourceType == PriceSourceEnum.OPERATION_APPLY ? null : saleOut.PriceSourceType;//暂未处理
                                    }

                                }
                            }
                            else if (relateCode.ToLower().IndexOf("so") > -1)
                            {
                                //出库事件,查询上游单据
                                storeout = storeOuts.FirstOrDefault(x => x.storeOutCode == relateCode);
                                if (storeout != null && storeout.Details != null && storeout.Details.Any())
                                {
                                    var details = storeout.Details.Where(p => ismark ? (p.mark == (int)MarkEnum.Park || p.mark == (int)MarkEnum.GroupStoreIn) : p.mark == 0).ToList();
                                    if (credit.Mark.HasValue)
                                    {
                                        details = storeout.Details.Where(p => p.mark == credit.Mark.Value).ToList();
                                    }
                                    details = details.Where(p => p.businessUnitId == credit.ServiceId).ToList();
                                    if (credit.ProjectId.HasValue)
                                    {
                                        details = details.Where(p => p.projectId == credit.ProjectId).ToList();
                                    }
                                    foreach (var orginDetail in details.OrderByDescending(p => p.productNo).OrderByDescending(p => p.quantity)) //遍历原始明细
                                    {
                                        SelectProductInvoicesOutput customerProduct = null;
                                        //原始包装规格
                                        var productno = products.FirstOrDefault(x => x.productNo == orginDetail.productNo);
                                        if (saleCustomerProducts != null && saleCustomerProducts.Any())
                                        {
                                            customerProduct = saleCustomerProducts.FirstOrDefault(p => p.ProductId == orginDetail.productId);
                                        }

                                        //排除已开票数量
                                        decimal dqty = orginDetail.quantity;
                                        var price = (credit.CreditType == CreditTypeEnum._return || credit.CreditType == CreditTypeEnum.selfreturn) && orginDetail.price > 0 ? -decimal.Parse(orginDetail.price.Value.ToString("F4")) : decimal.Parse(orginDetail.price.Value.ToString("F4"));
                                        originDetailOutput_List.Add(new OriginDetailOutput()
                                        {
                                            OriginDetailId = orginDetail.id.ToString().ToUpper(),
                                            ProductNo = orginDetail.productNo,
                                            OriginProductName = orginDetail.productName,
                                            ProductName = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceName) ? (productno != null && !string.IsNullOrEmpty(productno.productName) ? productno.productName : orginDetail.productName) : customerProduct.InvoiceName,
                                            PackUnit = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceUnit) ? (productno == null ? "" : productno.packUnitDesc) : customerProduct.InvoiceUnit,
                                            OriginPackUnit = productno == null ? "" : productno.packUnitDesc,
                                            Specification = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec) ? orginDetail.productNo : customerProduct.InvoiceSpec,
                                            OriginSpecification = orginDetail.productNo,
                                            OriginalPrice = orginDetail.originalPrice.HasValue ? Math.Abs(orginDetail.originalPrice.Value) : Math.Abs(price),
                                            Price = Math.Abs(price),
                                            Quantity = price < 0 ? -dqty : dqty, //价格<0时，价格永远为正数，数量变为负数
                                            TaxRate = orginDetail.priceTaxRate, //销售税率
                                            CreditBillCode = credit.BillCode,
                                            CustomerId = credit.CustomerId.HasValue ? credit.CustomerId.ToString() : "",
                                            CustomerName = credit.CustomerName,
                                            RelateCode = relateCode,
                                            OrderNo = credit.OrderNo,
                                            TaxTypeNo = "",
                                            OriginalPackSpec = productno != null ? productno.packDes : string.Empty,
                                            ProductId = orginDetail.productId,
                                            AgentId = orginDetail.agentId.ToString(),
                                            IFHighValue = productno?.IfHighValue,
                                            Value = Math.Abs(price) * (price < 0 ? -dqty : dqty),
                                            SaleDetailId = orginDetail.saleDetailId,
                                            ServiceId = orginDetail.businessUnitId,
                                            ProjectId = orginDetail.projectId,
                                            OriginalCost = orginDetail.originCost,
                                            PriceSource = orginDetail.priceSource == PriceSourceEnum.OPERATION_APPLY ? null : orginDetail.priceSource,
                                        });
                                    }
                                }
                            }
                            else if (relateCode.ToLower().IndexOf("si") > -1)
                            {
                                //销售调回入库,查询上游单据 
                                storein = storeIns.FirstOrDefault(x => x.storeInCode == relateCode);
                                if (storein != null && storein.storeInDetails != null && storein.storeInDetails.Any())
                                {
                                    var details = storein.storeInDetails.Where(p => ismark ? (p.mark == (int)MarkEnum.Park || p.mark == (int)MarkEnum.GroupStoreIn) : p.mark == 0).ToList();
                                    if (credit.Mark.HasValue)
                                    {
                                        details = storein.storeInDetails.Where(p => p.mark == credit.Mark.Value).ToList();
                                    }
                                    details = details.Where(p => p.businessUnitId == credit.ServiceId).ToList();
                                    if (credit.ProjectId.HasValue)
                                    {
                                        details = details.Where(p => p.projectId == credit.ProjectId).ToList();
                                    }
                                    if (!string.IsNullOrEmpty(credit.SaleCode))
                                    {
                                        details = details.Where(p => p.saleCode == credit.SaleCode).ToList();
                                    }
                                    var customizeInvoiceCancelDetail = customizeInvoiceCancelDetails.Where(p => p.CreditBillCode == credit.BillCode).FirstOrDefault();

                                    foreach (var orginDetail in details.OrderByDescending(p => p.productNo).OrderByDescending(p => p.quantity))
                                    {
                                        SelectProductInvoicesOutput customerProduct = null;
                                        //原始包装规格
                                        var productno = products.FirstOrDefault(x => x.productNo == orginDetail.productNo);
                                        if (saleCustomerProducts != null && saleCustomerProducts.Any())
                                        {
                                            customerProduct = saleCustomerProducts.FirstOrDefault(p => p.ProductId == orginDetail.productId);
                                        }
                                        //排除已开票数量
                                        decimal dqty = orginDetail.quantity;
                                        var price = (credit.CreditType == CreditTypeEnum._return || credit.CreditType == CreditTypeEnum.selfreturn) && orginDetail.price > 0 ? -decimal.Parse(orginDetail.price.Value.ToString("F4")) : decimal.Parse(orginDetail.price.Value.ToString("F4"));
                                        if (storein.relateCodeType == 16)
                                        {
                                            //旺店通
                                            price = 0;
                                            var tempRefundStoreIn = refundStoreInDetail.FirstOrDefault(w => w.storeInCode == storein.relateCode);
                                            var tempRefundStoreInDetail = tempRefundStoreIn?.items.FirstOrDefault(w => w.TempInventoryDetailId == orginDetail.relateId);
                                            if (tempRefundStoreInDetail != null)
                                            {
                                                if (credit.CreditSaleSubType == CreditSaleSubTypeEnum.personal)
                                                {
                                                    price = -tempRefundStoreInDetail.consumerPrice;

                                                }
                                                else if (credit.CreditSaleSubType == CreditSaleSubTypeEnum.platform)
                                                {
                                                    price = -tempRefundStoreInDetail.platformCouponPrice;
                                                    orginDetail.originCost = 0;//红包的原始成本默认为0
                                                }
                                            }
                                        }
                                        originDetailOutput_List.Add(new OriginDetailOutput()
                                        {
                                            OriginDetailId = orginDetail.storeInDetailId.ToUpper(),
                                            OriginProductName = orginDetail.productName,
                                            ProductNo = orginDetail.productNo,
                                            ProductName = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceName) ? (productno != null && !string.IsNullOrEmpty(productno.productName) ? productno.productName : orginDetail.productName) : customerProduct.InvoiceName,
                                            PackUnit = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceUnit) ? (productno == null ? "" : productno.packUnitDesc) : customerProduct.InvoiceUnit,
                                            OriginPackUnit = productno == null ? "" : productno.packUnitDesc,
                                            Specification = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec) ? orginDetail.productNo : customerProduct.InvoiceSpec,
                                            OriginSpecification = orginDetail.productNo,
                                            OriginalPrice = orginDetail.originalPrice.HasValue ? Math.Abs(orginDetail.originalPrice.Value) : Math.Abs(price),
                                            Price = Math.Abs(price),
                                            Quantity = price < 0 ? -dqty : dqty, //价格<0时，价格永远为正数，数量变为负数
                                            TaxRate = orginDetail.salesTaxRate,
                                            CreditBillCode = credit.BillCode,
                                            CustomerId = credit.CustomerId.HasValue ? credit.CustomerId.ToString() : "",
                                            CustomerName = credit.CustomerName,
                                            RelateCode = relateCode,
                                            OrderNo = credit.OrderNo,
                                            TaxTypeNo = "",
                                            ProductId = orginDetail.productId,
                                            SaleDetailId = orginDetail.saleDetailId,
                                            //原始包装规格
                                            OriginalPackSpec = productno != null ? productno.packDes : string.Empty,
                                            AgentId = orginDetail.agentId.ToString(),
                                            IFHighValue = productno?.IfHighValue,
                                            Value = Math.Abs(price) * (price < 0 ? -dqty : dqty),
                                            ServiceId = orginDetail.businessUnitId,
                                            ProjectId = orginDetail.projectId,
                                            OriginalCost = orginDetail.originCost,
                                        });
                                    }

                                }
                            }
                        }
                        index++;
                    }

                    if (originDetailOutput_List != null && originDetailOutput_List.Any())
                    {
                        var retLst = new List<OriginDetailOutput>();
                        var groupLst = originDetailOutput_List
                                        .GroupBy(p => new { p.CreditBillCode, p.ProductNo, p.Specification, p.TaxRate, p.Price, p.OriginalPrice, p.ProductId, p.SaleDetailId, p.OriginalCost })
                                        .Select(p => new
                                        {
                                            p.Key.CreditBillCode,
                                            p.Key.ProductNo,
                                            p.Key.TaxRate,
                                            p.Key.Price,
                                            p.Key.OriginalPrice,
                                            p.Key.OriginalCost,
                                            p.Key.Specification,
                                            p.Key.ProductId,
                                            p.Key.SaleDetailId,
                                            Quantity = p.Sum(x => x.Quantity)

                                        }).ToList();
                        foreach (var g in groupLst)
                        {
                            var orginDetails = originDetailOutput_List.Where(p => p.CreditBillCode == g.CreditBillCode
                                                                                  && p.ProductNo == g.ProductNo
                                                                                  && p.TaxRate == g.TaxRate
                                                                                  && p.Price == g.Price
                                                                                  && p.OriginalPrice == g.OriginalPrice
                                                                                  && p.OriginalCost == g.OriginalCost
                                                                                  && p.SaleDetailId == g.SaleDetailId).ToList();
                            var orginDetail = orginDetails.First();
                            retLst.Add(new OriginDetailOutput
                            {
                                CreditBillCode = g.CreditBillCode,
                                ProductNo = g.ProductNo,
                                TaxRate = g.TaxRate,
                                Price = g.Price,
                                OriginalPrice = g.OriginalPrice,
                                OriginalCost = g.OriginalCost,
                                Quantity = g.Quantity,
                                OriginalId = orginDetail.OriginalId,
                                OriginDetailId = Guid.NewGuid().ToString(),
                                CompanyId = orginDetail.CompanyId,
                                CompanyName = orginDetail.CompanyName,
                                CustomerId = orginDetail.CustomerId,
                                CustomerName = orginDetail.CustomerName,
                                OriginProductName = orginDetail.OriginProductName,
                                ProductName = orginDetail.ProductName,
                                PackUnit = orginDetail.PackUnit,
                                OriginPackUnit = orginDetail.OriginPackUnit,
                                Specification = g.Specification,
                                OriginSpecification = orginDetail.OriginSpecification,
                                RelateCode = orginDetail.RelateCode,
                                OrderNo = orginDetail.OrderNo,
                                TaxTypeNo = "",
                                ProductId = g.ProductId,
                                OriginalPackSpec = orginDetail.OriginalPackSpec,
                                AgentId = string.Join(',', orginDetails.Select(p => p.AgentId).Distinct()),
                                IFHighValue = orginDetail.IFHighValue,
                                NoInvoiceAmount = Math.Round(g.Price * g.Quantity, 2),
                                Value = Math.Round(g.Price * g.Quantity, 2),
                                SaleDetailId = g.SaleDetailId,
                                BatchId = orginDetail.BatchId,
                                ProjectId = orginDetail.ProjectId,
                                ServiceId = orginDetail.ServiceId,
                                PriceSource = orginDetail.PriceSource,
                            });
                        }
                        if (reviseDetailOutputLst != null && reviseDetailOutputLst.Any())
                        {
                            var reviseDetailOutputLstTemp = new List<OriginDetailOutput>();
                            MargeRevise(reviseDetailOutputLst, reviseDetailOutputLstTemp);
                            retLst.AddRange(reviseDetailOutputLstTemp);
                        }
                        retList = retLst;
                    }
                    else if (reviseDetailOutputLst != null && reviseDetailOutputLst.Any())
                    {
                        var reviseDetailOutputLstTemp = new List<OriginDetailOutput>();
                        MargeRevise(reviseDetailOutputLst, reviseDetailOutputLstTemp);
                        retList = reviseDetailOutputLstTemp;
                    }
                    else
                    {
                        retList = originDetailOutput_List;
                    }
                    var creditDetails = new List<CreditDetailPo>();
                    var existingCreditDetails = await _db.CreditDetails
                           .Where(cd => creditIds.Contains(cd.Id))
                           .ToListAsync();
                    foreach (var item in retList)
                    {
                        var credit = credits.FirstOrDefault(x => x.BillCode == item.CreditBillCode);
                        if (credit == null)
                        {
                            continue;
                        }
                        else
                        {
                            //如果应收明细存在，则跳过
                            if (credit.CreditDetails != null && credit.CreditDetails.Count > 0)
                            {
                                continue;
                            }
                        }
                        if (existingCreditDetails != null && existingCreditDetails.Count > 0)
                        { 
                            // ✅ 从数据库重新查询是否存在 CreditDetails
                            var existingCreditDetailsTemp = existingCreditDetails
                                .Where(cd => cd.CreditId == credit.Id)
                                .ToList(); 
                            if (existingCreditDetailsTemp != null && existingCreditDetailsTemp.Count > 0)
                            {
                                continue;// 已有明细，跳过插入
                            }
                        }
                        var creditDetail = item.Adapt<CreditDetailPo>();
                        creditDetail.CreditId = credit.Id;
                        creditDetail.NoInvoiceAmount = Math.Round(item.Quantity * item.Price, 2);
                        creditDetail.Amount = creditDetail.NoInvoiceAmount;
                        creditDetail.PackSpec = item.OriginalPackSpec;
                        creditDetail.ProductName = item.OriginProductName;
                        creditDetail.PackUnit = item.OriginPackUnit;
                        creditDetail.Specification = item.Specification;
                        creditDetails.Add(creditDetail);
                        if (credit.CreditType == CreditTypeEnum.servicefee && credit.SaleSource == SaleSourceEnum.Spd)
                        {
                            //服务费应收替换规格型号SPD字符串
                            var specification = item.Specification != null ? item.Specification.ToUpper() : string.Empty;
                            if (specification.Contains("-SPD"))
                            {
                                specification = specification.Replace("-SPD", "");
                                item.Specification = specification;
                            }
                            item.ProductName = string.IsNullOrEmpty(item.ProductName) ? "" : item.ProductName.Replace("*信息技术服务*", "");
                        }
                    }
                    if (creditDetails.Any())
                    {
                        RepaireCreditDiff(creditDetails, credits);
                        await _db.CreditDetails.AddRangeAsync(creditDetails);
                        await _db.SaveChangesAsync();
                    }
                    var noexistsCreditDetailCodes = retList.Select(p => p.CreditBillCode).Distinct();
                    var existsCreditDetailTemp = await _db.CreditDetails.Include(p => p.Credit).Where(p => noexistsCreditDetailCodes.Contains(p.Credit.BillCode)).ToListAsync();
                    existsCreditDetails.AddRange(existsCreditDetailTemp);

                    if (existsCreditDetails.Any())
                    {
                        string specification = string.Empty;
                        retList = new List<OriginDetailOutput>();
                        existsCreditDetails = existsCreditDetails.Where(p => Math.Abs(p.NoInvoiceAmount.Value) > 0 || p.Amount == 0).ToList();
                        foreach (var item in existsCreditDetails)
                        {
                            var originDetailOutput = item.Adapt<OriginDetailOutput>();
                            SelectProductInvoicesOutput customerProduct = null;
                            if (saleCustomerProducts != null && saleCustomerProducts.Any())
                            {
                                customerProduct = saleCustomerProducts.FirstOrDefault(p => p.ProductId == item.ProductId);
                                //if (customerProduct.SaleTax.HasValue)
                                //{
                                //    originDetailOutput.TaxRate = customerProduct.SaleTax.Value;
                                //}
                            }
                            var product = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                            if (customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec))
                            {
                                //获取公司的开票规格默认值
                                specification = await Utility.GetCompanySpecificationAsync(product, credits.First().CompanyId.ToString(), item.ProductNo, _bDSApiClient);
                            }
                            originDetailOutput.OriginProductName = item.ProductName;
                            originDetailOutput.OriginalPackSpec = product != null ? product.packDes : string.Empty;
                            originDetailOutput.OriginSpecification = product == null ? "" : product.productNo;

                            //销售子系统取值，未配置的从基础数据取值
                            originDetailOutput.ProductName = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceName) ? (product != null && !string.IsNullOrEmpty(product.productName) ? product.productName : item.ProductName) : customerProduct.InvoiceName;
                            originDetailOutput.PackUnit = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceUnit) ? (product != null ? product.packUnitDesc : item.PackUnit) : customerProduct.InvoiceUnit;
                            originDetailOutput.OriginPackUnit = product != null ? product.packUnitDesc : string.Empty;
                            originDetailOutput.Specification = customerProduct == null || string.IsNullOrEmpty(customerProduct.InvoiceSpec) ? specification : customerProduct.InvoiceSpec;

                            originDetailOutput.NoInvoiceAmount = item.NoInvoiceAmount;
                            originDetailOutput.Value = item.Price * item.Quantity;
                            originDetailOutput.CreditDetailId = item.Id;
                            originDetailOutput.CustomerName = item.Credit.CustomerName;
                            retList.Add(originDetailOutput);
                        }
                    }
                }
                return retList;
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅暂存核销事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }
        private static void MargeRevise(List<OriginDetailOutput> reviseDetailOutputLst, List<OriginDetailOutput> reviseDetailOutputLstTemp)
        {
            var reviseDetailOutputGroupLst = reviseDetailOutputLst.GroupBy(p => new { p.OriginalId, p.CreditBillCode, p.BatchId }).ToList();
            foreach (var item in reviseDetailOutputGroupLst)
            {
                var temp = item.First().DeepClone();
                var subLst = item.ToList().OrderBy(p => p.Price).ToList();
                var totalAmount = subLst.Sum(p => p.Price * p.Quantity);
                temp.Quantity = Math.Abs(temp.Quantity);
                temp.Price = totalAmount / temp.Quantity;
                temp.NoInvoiceAmount = Math.Round(temp.Price * temp.Quantity, 2);
                reviseDetailOutputLstTemp.Add(temp);

            }
        }

        /// <summary>
        /// 尾差处理
        /// </summary>
        /// <param name="creditDetails">应收明细</param>
        /// <param name="credits">应收集合</param>
        /// <returns></returns>
        public void RepaireCreditDiff(List<CreditDetailPo> creditDetails, List<CreditPo> credits)
        {
            var creditGroup = creditDetails.GroupBy(p => p.CreditId);

            foreach (var item in creditGroup)
            {
                var creditDetailAmount = item.ToList().Sum(p => p.Amount.Value);
                var credit = credits.FirstOrDefault(p => p.Id == item.Key);
                if (credit != null)
                {
                    var diffAmount = creditDetailAmount - credit.Value;
                    if (diffAmount != 0)
                    {
                        item.ToList().First().Amount = creditDetails.First().Amount - diffAmount;
                        item.ToList().First().NoInvoiceAmount = creditDetails.First().NoInvoiceAmount - diffAmount;
                    }
                }
            }
        }

        /// <summary>
        /// 获取开票单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        public async Task<(List<CustomizeInvoiceItemOutput>, int)> GetCustomizeInvoiceItem(CustomizeInvoiceItemQueryInput input)
        {
            //条件
            Expression<Func<CustomizeInvoiceItemPo, bool>> exp = await InitExp(input);
            IQueryable<CustomizeInvoiceItemPo> baseQuery = _db.CustomizeInvoiceItem.Where(exp).AsNoTracking();

            //排序 
            if (input.sort != null && input.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CustomizeInvoiceItemPo>(input.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<CustomizeInvoiceItemOutput>()).ToListAsync();

            return (list, count);
        }
        private async Task<Expression<Func<CustomizeInvoiceItemPo, bool>>> InitExp(CustomizeInvoiceItemQueryInput input)
        {
            Expression<Func<CustomizeInvoiceItemPo, bool>> exp = z => 1 == 1;

            if (input.CustomizeInvoiceClassifyId != null)
            {
                exp = exp.And(z => z.CustomizeInvoiceClassifyId == input.CustomizeInvoiceClassifyId);
            }
            else
            {
                exp = exp.And(z => 1 != 1);
            }
            if (input.Status != null && input.Status != CustomizeInvoiceStatusEnum.WaitSubmit && input.Status != CustomizeInvoiceStatusEnum.Auditing && input.Status > 0)
            {
                if ((int)input.Status == 5000)
                {
                    var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.CustomizeInvoiceForm.GetDescription()));
                    var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                    exp = exp.And(c => oARequestIds.Contains(c.OARequestId) && c.Status == CustomizeInvoiceStatusEnum.Auditing && c.OARequestId != null);
                }
                else
                {
                    if (input.Status == CustomizeInvoiceStatusEnum.Completed)
                    {
                        exp = exp.And(c => c.Status == CustomizeInvoiceStatusEnum.Completed);
                    }
                    else
                    {
                        if (input.Status != CustomizeInvoiceStatusEnum.WaitInvoice)
                        {
                            exp = exp.And(c => c.Status != CustomizeInvoiceStatusEnum.Completed);
                        }

                    }
                }
            }

            return exp;
        }
        public async Task<BaseResponseData<List<CustomizeInvoiceItemDownLoadOutput>>> DownLoadAsync(CustomizeInvoiceClassifyQueryInput input)
        {
            var ret = BaseResponseData<List<CustomizeInvoiceItemDownLoadOutput>>.Success("操作成功！");
            //条件
            input.Status = null;
            Expression<Func<CustomizeInvoiceClassifyPo, bool>> expClassify = await InitClassifyExpAsync(input);
            //var classifyIds = await _db.CustomizeInvoiceClassify.Where(expClassify).AsNoTracking().Select(p => p.Id).ToListAsync();

            var baseQuery = from c in _db.CustomizeInvoiceClassify.Where(expClassify).AsNoTracking()
                            join ci in _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail)
                            on c.Id equals ci.CustomizeInvoiceClassifyId
                            where (input.InvoiceStatus.HasValue ? ci.Status == input.InvoiceStatus : true)
                            select ci;
            //IQueryable<CustomizeInvoiceItemPo> baseQuery = _db.CustomizeInvoiceItem.Include(p => p.CustomizeInvoiceDetail).Where(p => classifyIds.Contains(p.CustomizeInvoiceClassifyId.Value)).AsNoTracking();
            //排序 
            if (input.sort != null && input.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CustomizeInvoiceItemPo>(input.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Select(z => z.Adapt<CustomizeInvoiceItemDownLoadOutput>()).ToListAsync();
            ret.Data = list;
            return ret;
        }
        public async Task<CustomizeInvoiceItemTabOutput> GetTabCountAsync(CustomizeInvoiceItemQueryInput input)
        {
            Expression<Func<CustomizeInvoiceItemPo, bool>> exp = await InitExp(input);
            var query = _db.CustomizeInvoiceItem.Where(exp).AsNoTracking();

            //var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.CustomizeInvoiceForm.GetDescription()));
            //var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
            var res = await query.AsNoTracking().ToListAsync();
            var result = new CustomizeInvoiceItemTabOutput
            {
                AllCount = res.Count(),
                //AuditingCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing).Count(),
                WaitInvoiceCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.WaitInvoice).Count(),
                //WaitSubmitCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.WaitSubmit).Count(),
                InvoicedCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Completed).Count(),
                CancelCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Cancel).Count(),
                //MyAuditCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing &&
                //                              t.OARequestId != null &&
                //                              oARequestIds.Contains(t.OARequestId)).Count(),
            };
            return result;
        }
        /// <summary>
        /// 获取开票明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<(List<CustomizeInvoiceDetailOutput>, int)> GetCustomizeInvoiceDetail(CustomizeInvoiceDetailQueryInput input)
        {
            var baseQuery = _db.CustomizeInvoiceDetail.Where(c => c.CustomizeInvoiceItemId == input.CustomizeInvoiceItemId).AsNoTracking();

            var customizeInvoiceItem = await _db.CustomizeInvoiceItem.Where(p => p.Id == input.CustomizeInvoiceItemId).FirstOrDefaultAsync();
            if (customizeInvoiceItem == null)
            {
                throw new ApplicationException("没有找到对应的申开单数据");
            }
            //排序
            baseQuery = baseQuery.OrderBy(z => z.Sort).ThenByDescending(z => z.CreatedTime).ThenByDescending(p => p.Price * p.Quantity);
            //总条数
            var count = baseQuery.Count();
            var taxClassCodes = await _bDSApiClient.GetAllTaxClassCode();

            //分页
            var list = await baseQuery.Select(z => z.Adapt<CustomizeInvoiceDetailOutput>()).ToListAsync();
            var customizeInvoiceDetailIds = list.Select(p => p.Id).ToHashSet();
            var discountList = list.Where(p => p.Tag == "折扣行").ToList();
            if (input.Opt == 2) //红冲明细
            {
                list = list.Where(p => p.Tag != "折扣行").ToList();
            }
            var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new CompanyMetaInfosInput
            {
                nameCodeEq = customizeInvoiceItem.NameCode
            });
            var customizeInvoiceDetailReds = await _db.CustomizeInvoiceDetail.Include(p => p.CustomizeInvoiceItem).Where(p =>
                                                                                                                p.RelateId.HasValue &&
                                                                                                                customizeInvoiceDetailIds.Contains(p.RelateId.Value) &&
                                                                                                                p.CustomizeInvoiceItem.Status != CustomizeInvoiceStatusEnum.Cancel).ToListAsync();

            for (int i = 1; i <= list.Count; i++)
            {
                if (input.Opt == 2)//红冲明细
                {
                    if (discountList != null && discountList.Count() > 0)
                    {
                        var discountRow = discountList.Where(p => p.ParentId == list[i - 1].Id).FirstOrDefault();
                        if (discountRow != null)
                        {
                            var taxRate = list[i - 1].TaxRate / 100;
                            list[i - 1].Price -= discountRow.Price;
                            list[i - 1].Value = list[i - 1].Value + discountRow.Value;
                            list[i - 1].TaxAmount = (list[i - 1].Value / (1 + taxRate) * taxRate);
                        }
                    }

                    var customizeInvoiceCreditRedTemps = customizeInvoiceDetailReds.Where(p => p.RelateId == list[i - 1].Id).ToList();
                    if (customizeInvoiceCreditRedTemps != null)
                    {
                        list[i - 1].InvoiceAmount = list[i - 1].Value - customizeInvoiceCreditRedTemps.Sum(p => Math.Abs(p.Value));
                        if (customizeInvoiceItem.IsNoRedConfirm == 1)
                        {
                            list[i - 1].Value = list[i - 1].InvoiceAmount.Value;
                        }

                    }
                }
                if (!string.IsNullOrEmpty(list[i - 1].CustomizeInvoiceIndex) && list[i - 1].CustomizeInvoiceIndex == "0")
                {
                    list[i - 1].CustomizeInvoiceIndex = i.ToString();
                }
                if (list[i - 1].Sort == 0)
                {
                    list[i - 1].Sort = i;
                }
                var dataDictionary = taxClassCodes.Where(p => p.number == list[i - 1].TaxTypeNo).FirstOrDefault();
                if (dataDictionary != null)
                {
                    list[i - 1].TaxTypeNoStr = $"{list[i - 1].TaxTypeNo}({dataDictionary.name})";
                }
                var companyTaxRate = 0M;
                if (company != null && company.Any() && !string.IsNullOrEmpty(company.First().taxRate))
                {
                    companyTaxRate = decimal.Parse(company.First().taxRate);
                }
                list[i - 1].CompanyTaxRate = companyTaxRate;
            }
            return (list, count);
        }

        /// <summary>
        /// 根据订单号获取开票明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<(List<CustomizeInvoiceDetailOutput>, int)> GetCustomizeInvoiceDetailByOrderNo(string orderNo)
        {
            var baseQuery = _db.CustomizeInvoiceDetail.Where(c => c.OrderNo == orderNo).AsNoTracking();
            //排序
            baseQuery = baseQuery.OrderBy(z => z.CreatedTime).ThenByDescending(p => p.Price * p.Quantity);
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Select(z => z.Adapt<CustomizeInvoiceDetailOutput>()).ToListAsync();
            return (list, count);
        }

        public async Task<CustomizeInvoiceItem> GetById(Guid id)
        {
            var ret = await _db.CustomizeInvoiceItem.FirstOrDefaultAsync(p => p.Id == id);
            return ret.Adapt<CustomizeInvoiceItem>();
        }

        #region 分类
        /// <summary>
        /// 获取开票分类单列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        public async Task<(List<CustomizeInvoiceClassifyOutput>, int)> GetCustomizeInvoiceClassify(CustomizeInvoiceClassifyQueryInput input)
        {
            //条件 ;
            Expression<Func<CustomizeInvoiceClassifyPo, bool>> exp = await InitClassifyExpAsync(input);
            IQueryable<CustomizeInvoiceClassifyPo> baseQuery = _db.CustomizeInvoiceClassify.Where(exp).AsNoTracking();
            //排序 
            if (input.sort != null && input.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<CustomizeInvoiceClassifyPo>(input.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }

            //分页
            var query = baseQuery.Select(z => new CustomizeInvoiceClassifyOutput
            {
                Id = z.Id,
                CreatedBy = z.CreatedBy,
                BillCode = z.BillCode,
                //Count = _db.CustomizeInvoiceItem.Count(p => p.CustomizeInvoiceClassifyId == z.Id),
                CompanyId = z.CompanyId,
                CompanyName = z.CompanyName,
                CustomerId = z.CustomerId,
                CustomerName = z.CustomerName,
                SaleSystemName = z.SaleSystemName,
                Status = z.Status,
                //CustomizeInvoiceItemCodes = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.Code).ToList(),
                //CustomizeInvoiceItemStatus = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.Status).ToList(),
                //RelationCodes = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.RelationCode).ToList(),
                //CreditBillCodes = (from item in _db.CustomizeInvoiceItem join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where item.CustomizeInvoiceClassifyId == z.Id select detail.CreditBillCode).ToList(),
                CreatedTime = z.CreatedTime,
                OARequestId = z.OARequestId,
                //TotalAmount = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Sum(p => p.InvoiceTotalAmount),
                AttachFileIds = z.AttachFileIds,
                CustomerEmail = z.CustomerEmail,
                Classify = z.Classify,
                RelationCode = z.RelationCode,
                Remark = z.Remark,
                CreditSaleSubType = z.CreditSaleSubType
            });
            if (!string.IsNullOrEmpty(input.CustomizeInvoiceItemCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.Code == input.CustomizeInvoiceItemCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.RelationCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.RelationCode == input.RelationCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.CreditCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where EF.Functions.Contains(detail.CreditBillCode, input.CreditCode) select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where EF.Functions.Contains(detail.OrderNo, input.OrderNo) select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            //if (!string.IsNullOrEmpty(input.CreditCode))
            //{
            //    query = query.Where(p => p.CreditBillCodes.Contains(input.CreditCode));
            //}
            if (input.InvoiceStatus.HasValue && input.InvoiceStatus != 0)
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify
                                  join item in _db.CustomizeInvoiceItem
                                  on clas.Id equals item.CustomizeInvoiceClassifyId
                                  where item.Status == input.InvoiceStatus &&
                                        (input.CompanyId.HasValue ? clas.CompanyId == input.CompanyId : true) &&
                                        (input.CustomerId.HasValue ? clas.CustomerId == input.CustomerId : true) &&
                                        (input.createdBy != null && input.createdBy.Any() ? input.createdBy.Contains(clas.CreatedBy) : true) &&
                                        (input.billDate1.HasValue ? clas.CreatedTime >= input.billDate1.Value : true) &&
                                        (input.billDate2.HasValue ? clas.CreatedTime <= input.billDate2.Value : true) 
                                  select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            if (input.IsAttachment.HasValue)
            {
                if (input.IsAttachment.Value == 1)
                {
                    query = query.Where(x => !string.IsNullOrEmpty(x.AttachFileIds));
                }
                else
                {
                    query = query.Where(x => string.IsNullOrEmpty(x.AttachFileIds));
                }
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                query = query.Where(x => x.BillCode == input.BillCode);
            }
            if (input.Classify.HasValue)
            {
                query = query.Where(x => x.Classify == input.Classify);
            }
            //总条数
            var count = await query.CountAsync();
            var list = await query.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
            var itemIds = list.Select(x => x.Id).ToList();
            var items = _db.CustomizeInvoiceItem.Where(p => itemIds.Contains(p.CustomizeInvoiceClassifyId.Value)).ToList();
            foreach (var clas in list)
            {
                var datas = items.Where(x => x.CustomizeInvoiceClassifyId == clas.Id).ToList();
                clas.Count = datas.Count();
                clas.TotalAmount = datas.Sum(p => p.InvoiceTotalAmount);
            }
            return (list, count);
        }

        private async Task<Expression<Func<CustomizeInvoiceClassifyPo, bool>>> InitClassifyExpAsync(CustomizeInvoiceClassifyQueryInput input)
        {
            Expression<Func<CustomizeInvoiceClassifyPo, bool>> exp = z => z.Status != CustomizeInvoiceStatusEnum.Cancel;
            if (input.CustomerId.HasValue)
            {
                exp = exp.And(c => c.CustomerId == input.CustomerId);
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(z => z.CompanyId == input.CompanyId);
            }
            if (input.Status == null || (int)input.Status != 5000)
            {
                var strategyInput = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }

                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId));
                            }
                        }

                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CustomerId));
                            }
                        }

                    }
                }
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                exp = exp.And(z => z.BillCode == input.BillCode);
            }
            if (!string.IsNullOrEmpty(input.RelationCodeOfClassify))
            {
                exp = exp.And(z => z.RelationCode == input.RelationCodeOfClassify);
            }
            if (input.Id.HasValue)
            {
                exp = exp.And(z => z.Id == input.Id);
            }
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                exp = exp.And(z => z.BillCode.Contains(input.searchKey));
            }
            if (input.createdBy != null && input.createdBy.Any())
            {
                exp = exp.And(z => input.createdBy.Contains(z.CreatedBy));
            }
            if (input.billDate1.HasValue)
            {
                exp = exp.And(z => z.CreatedTime >= input.billDate1);
            }
            if (input.billDate2.HasValue)
            {
                exp = exp.And(z => z.CreatedTime <= input.billDate2);
            }
            if (!string.IsNullOrEmpty(input.SaleSystemName))
            {
                exp = exp.And(z => z.SaleSystemName.Contains(input.SaleSystemName));
            }
            if (input.Status != null && input.Status >= 0)
            {
                if ((int)input.Status == 5000)
                {
                    var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.CustomizeInvoiceForm.GetDescription()));
                    var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                    exp = exp.And(c => c.OARequestId != null && oARequestIds.Contains(c.OARequestId) && c.Status == CustomizeInvoiceStatusEnum.Auditing);
                }
                else
                {
                    if (input.Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                    {
                        exp = exp.And(c => c.CreatedBy == input.UserName);
                    }
                    exp = exp.And(c => c.Status == input.Status);
                }
            }
            return exp;
        }

        public async Task<CustomizeInvoiceItemTabOutput> GetClassifyTabCountAsync(CustomizeInvoiceClassifyQueryInput input)
        {
            input.Status = (CustomizeInvoiceStatusEnum?)-1;
            Expression<Func<CustomizeInvoiceClassifyPo, bool>> exp = await InitClassifyExpAsync(input);
            var baseQuery = _db.CustomizeInvoiceClassify.Where(exp).AsNoTracking();


            var query = baseQuery.Select(z => new CustomizeInvoiceClassifyOutput
            {
                Id = z.Id,
                //CustomizeInvoiceItemStatus = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.Status).ToList(),
                //CustomizeInvoiceItemCodes = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.Code).ToList(),
                //RelationCodes = _db.CustomizeInvoiceItem.Where(p => p.CustomizeInvoiceClassifyId == z.Id).Select(p => p.RelationCode).ToList(),
                //CreditBillCodes = (from item in _db.CustomizeInvoiceItem join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where item.CustomizeInvoiceClassifyId == z.Id select detail.CreditBillCode).ToList(),
                BillCode = z.BillCode,
                AttachFileIds = z.AttachFileIds,
                Status = z.Status,
                CreatedBy = z.CreatedBy,
                OARequestId = z.OARequestId,
                Classify = z.Classify
            });

            if (!string.IsNullOrEmpty(input.CustomizeInvoiceItemCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.Code == input.CustomizeInvoiceItemCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.RelationCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.RelationCode == input.RelationCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.CreditCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where detail.CreditBillCode.Contains(input.CreditCode) select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (input.InvoiceStatus.HasValue && input.InvoiceStatus != 0)
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify
                                  join item in _db.CustomizeInvoiceItem
                                  on clas.Id equals item.CustomizeInvoiceClassifyId
                                  where item.Status == input.InvoiceStatus &&
                                        (input.CompanyId.HasValue ? clas.CompanyId == input.CompanyId : true) &&
                                        (input.CustomerId.HasValue ? clas.CustomerId == input.CustomerId : true) &&
                                        (input.createdBy != null && input.createdBy.Any() ? input.createdBy.Contains(clas.CreatedBy) : true) &&
                                        (input.billDate1.HasValue ? clas.CreatedTime >= input.billDate1.Value : true) &&
                                        (input.billDate2.HasValue ? clas.CreatedTime <= input.billDate2.Value : true)
                                  select clas.Id).ToListAsync();
                query = query.Where(p => cIds.ToHashSet().Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where detail.OrderNo.Contains(input.OrderNo) select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (input.IsAttachment.HasValue)
            {
                if (input.IsAttachment.Value == 1)
                {
                    query = query.Where(x => !string.IsNullOrEmpty(x.AttachFileIds));
                }
                else
                {
                    query = query.Where(x => string.IsNullOrEmpty(x.AttachFileIds));
                }
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                query = query.Where(x => x.BillCode == input.BillCode);
            }
            if (input.Classify.HasValue)
            {
                query = query.Where(x => x.Classify == input.Classify);
            }
            var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(input?.UserName ?? default, WorkFlowCode.CustomizeInvoiceForm.GetDescription()));
            var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
            var res = await query.AsNoTracking().ToListAsync();
            var result = new CustomizeInvoiceItemTabOutput
            {
                AllCount = res.Count(),
                AuditingCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing).Count(),
                //WaitSubmitCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.WaitSubmit ).Count(),
                WaitSubmitCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.WaitSubmit && t.CreatedBy == input.UserName).Count(),
                WaitInvoiceCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.WaitInvoice).Count(),
                //InvoicedCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Completed).Count(),
                //CancelCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Cancel).Count(),
                //MyAuditCount = _db.CustomizeInvoiceClassify.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing &&
                //                              t.OARequestId != null &&
                //                              oARequestIds.Contains(t.OARequestId)).Count(),
                MyAuditCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing &&
                                              t.OARequestId != null &&
                                              oARequestIds.Contains(t.OARequestId)).Count(),
                //MyAuditCount = res.Where(t => t.Status == CustomizeInvoiceStatusEnum.Auditing).Count(),
            };

            return result;
        }

        /// <summary>
        /// 获取附件id集合
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<string>>> GetAttachFileIds(CustomizeInvoiceClassifyQueryInput input)
        {
            //条件 ;
            Expression<Func<CustomizeInvoiceClassifyPo, bool>> exp = await InitClassifyExpAsync(input);
            IQueryable<CustomizeInvoiceClassifyPo> baseQuery = _db.CustomizeInvoiceClassify.Where(exp).AsNoTracking();
            var query = baseQuery.Select(z => new CustomizeInvoiceClassifyOutput
            {
                Id = z.Id,
                BillCode = z.BillCode,
                AttachFileIds = z.AttachFileIds,
            });
            if (!string.IsNullOrEmpty(input.CustomizeInvoiceItemCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.Code == input.CustomizeInvoiceItemCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.RelationCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.RelationCode == input.RelationCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.CreditCode))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where detail.CreditBillCode == input.CreditCode select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId join detail in _db.CustomizeInvoiceDetail on item.Id equals detail.CustomizeInvoiceItemId where detail.OrderNo == input.OrderNo select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (input.InvoiceStatus.HasValue && input.InvoiceStatus != 0)
            {
                var cIds = await (from clas in _db.CustomizeInvoiceClassify join item in _db.CustomizeInvoiceItem on clas.Id equals item.CustomizeInvoiceClassifyId where item.Status == input.InvoiceStatus select clas.Id).ToListAsync();
                query = query.Where(p => cIds.Contains(p.Id));
            }
            if (!string.IsNullOrEmpty(input.BillCode))
            {
                query = query.Where(x => x.BillCode == input.BillCode);
            }
            query = query.Where(x => !string.IsNullOrEmpty(x.AttachFileIds));
            var list = await query.ToListAsync();
            var ids = new List<string>();
            foreach (var item in list)
            {
                if (!string.IsNullOrEmpty(item.AttachFileIds))
                {
                    string[] stringArray = item.AttachFileIds.Split(',');
                    foreach (var str in stringArray)
                    {
                        if (!string.IsNullOrEmpty(str))
                        {
                            ids.Add(str);
                        }
                    }
                }
            }
            ids = ids.Distinct().ToList();
            return BaseResponseData<List<string>>.Success(ids);
        }
        #endregion

        /// <summary>
        /// 获取客户邮箱
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<string> GetCustomerEmail([FromBody] CustomizeInvoiceItemQueryInput input)
        {
            var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                id = input.CustomerId
            });
            // 基础数据提供字段尚未明确，后续可能需要改动email
            return customer == null ? string.Empty : customer.email;
        }

        /// <summary>
        /// 获取预开票列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<PreCustomizeInvoiceListOutput>, int)> GetPreCustomizeInvoiceList(PreCustomizeInvoiceListInput input)
        {
            Expression<Func<PreCustomizeInvoiceItemPo, bool>> exp = z => 1 == 1;
            var strategyInput = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            exp = await InitExp(input, exp, strategry);
            IQueryable<PreCustomizeInvoiceItemPo> baseQuery = _db.PreCustomizeInvoiceItem.Where(exp).AsNoTracking();
            //排序 
            if (input.sort != null && input.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<PreCustomizeInvoiceItemPo>(input.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<PreCustomizeInvoiceListOutput>()).ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 预开票权限过滤
        /// </summary>
        /// <param name="input"></param>
        /// <param name="exp"></param>
        /// <param name="strategry"></param>
        /// <returns></returns>
        private async Task<Expression<Func<PreCustomizeInvoiceItemPo, bool>>> InitExp(PreCustomizeInvoiceListInput input
            , Expression<Func<PreCustomizeInvoiceItemPo, bool>> exp, StrategyQueryOutput? strategry)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                else
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }
                        if (key.ToLower() == "accountingdept")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CustomerId.Value));
                            }
                        }
                    }
                }
            }

            if (!string.IsNullOrEmpty(input.ProjectName))
            {
                exp = exp.And(x => !string.IsNullOrEmpty(x.ProjectName) && x.ProjectName.Contains(input.ProjectName));
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(x => x.CompanyId == input.CompanyId);
            }
            if (input.CustomerId.HasValue)
            {
                exp = exp.And(x => x.CustomerId == input.CustomerId);
            }
            if (!string.IsNullOrEmpty(input.BusinessDeptId))
            {
                exp = exp.And(x => x.BusinessDeptId == input.BusinessDeptId);
            }
            if (!string.IsNullOrEmpty(input.OrderNo))
            {
                var ids = await _db.PreCustomizeInvoiceDetails.Where(x => x.OrderNo == input.OrderNo).Select(x => x.PreCustomizeInvoiceItemId).ToListAsync();
                exp = exp.And(x => ids.Contains(x.Id));
            }
            if (input.CreatedBy != null && input.CreatedBy.Any())
            {
                exp = exp.And(x => input.CreatedBy.Contains(x.CreatedBy));
            }
            if (!string.IsNullOrEmpty(input.CreatedDateBeging) && !string.IsNullOrEmpty(input.CreatedDateEnd))
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(long.Parse(input.CreatedDateBeging)).AddHours(8);
                var endTime = dateTime.AddMilliseconds(long.Parse(input.CreatedDateEnd)).AddHours(8);
                exp = exp.And(x => x.CreatedTime <= endTime && x.CreatedTime >= startTime);
            }
            if (input.Status.HasValue && input.Status.Value != PreCustomizeInvoiceItemStatusEnum.all)
            {
                exp = exp.And(x => x.Status == input.Status);
                if (input.Status.HasValue && input.Status.Value == PreCustomizeInvoiceItemStatusEnum.waitSubmit)
                {
                    exp = exp.And(x => x.CreatedBy == input.UserName);
                }
            }
            if (!string.IsNullOrEmpty(input.Code))
            {
                exp = exp.And(x => x.Code == input.Code);
            }
            return exp;
        }

        /// <summary>
        /// 获取预开票列表页签数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<PreCustomizeInvoiceListTabOutput>> GetPreCustomizeInvoiceTabCount(PreCustomizeInvoiceListInput query)
        {
            var ret = BaseResponseData<PreCustomizeInvoiceListTabOutput>.Success("操作成功");
            query.Status = PreCustomizeInvoiceItemStatusEnum.all;
            query.limit = 9999;
            var (list, count) = await GetPreCustomizeInvoiceList(query);
            var data = new PreCustomizeInvoiceListTabOutput();
            data.WaitSubmitCount = list.Where(x => x.Status == PreCustomizeInvoiceItemStatusEnum.waitSubmit && x.CreatedBy == query.UserName).Count();
            data.WaitAuditCount = list.Where(x => x.Status == PreCustomizeInvoiceItemStatusEnum.waitAudit).Count();
            data.ComplateCount = list.Where(x => x.Status == PreCustomizeInvoiceItemStatusEnum.Complate).Count();
            data.AllCount = list.Count();
            ret.Data = data;
            return ret;
        }

        /// <summary>
        /// 获取预开票明细列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<PreCustomizeInvoiceDetailsOutput>, int)> GetPreCustomizeInvoiceDetails(PreCustomizeInvoiceDetailsInput input)
        {
            Expression<Func<PreCustomizeInvoiceDetailPo, bool>> exp = z => 1 == 1;
            if (input.PreCustomizeInvoiceItemId.HasValue)
            {
                exp = exp.And(x => x.PreCustomizeInvoiceItemId == input.PreCustomizeInvoiceItemId);
            }
            else
            {
                return (new List<PreCustomizeInvoiceDetailsOutput>(), 0);
            }
            if (!string.IsNullOrEmpty(input.ProductName))
            {
                exp = exp.And(x => !string.IsNullOrEmpty(x.ProductName) && x.ProductName.Contains(input.ProductName));
            }
            IQueryable<PreCustomizeInvoiceDetailPo> baseQuery = _db.PreCustomizeInvoiceDetails.Where(exp).AsNoTracking();
            //排序 
            if (input.sort != null && input.sort.Any())
            {
                baseQuery = baseQuery.OrderByDefault<PreCustomizeInvoiceDetailPo>(input.sort);
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<PreCustomizeInvoiceDetailsOutput>()).ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 预开票申请提交
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> CreatePreCustomizeInvoice(SubmitPreCustomizeInvoiceInput input)
        {
            var model = new PreCustomizeInvoiceItemPo();
            model.ProjectCode = input.Project != null ? input.Project.code : string.Empty;
            model.ProjectName = input.Project != null ? input.Project.name : string.Empty;
            model.ProjectId = input.ProjectId;
            model.BusinessDeptFullPath = input.newDepart != null ? input.newDepart.path : string.Empty;
            model.BusinessDeptFullName = input.newDepart != null ? input.newDepart.fullName : string.Empty;
            model.BusinessDeptId = input.newDepart != null ? input.newDepart.id : string.Empty;
            model.CompanyId = input.CompanyId.HasValue ? input.CompanyId.Value : input.Company.id;
            model.CompanyName = input.Company != null ? input.Company.name : string.Empty;
            model.NameCode = input.Company != null && input.Company.extraInfo != null ? input.Company.extraInfo.nameCode : string.Empty;
            model.CustomerId = input.CustomerId;
            model.CustomerName = input.Customers != null ? input.Customers.name : string.Empty;
            model.Status = PreCustomizeInvoiceItemStatusEnum.waitSubmit;
            model.Value = input.amount.HasValue ? input.amount.Value : 0;
            model.CreatedBy = input.createdBy ??= "none";

            //生成单号
            var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { model.CompanyId.ToString() } });
            var companyInfo = companyInfoOutput?.FirstOrDefault();
            if (companyInfo == null)
            {
                throw new AppServiceException("公司信息不存在");
            }
            else
            {
                if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
                {
                    companyInfo.sysMonth = DateTime.Now.ToString("yyyy-MM");
                }
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = input.newDepart != null ? input.newDepart.BusinessArea ??= "FXBD" : "FXBD",
                    BillType = "PCI",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    model.Code = outPut.Codes.First();
                    var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                    DateTime.TryParse(sysMonth, out DateTime billDate);
                    model.BillDate = billDate;
                }
                else
                {
                    throw new ApplicationException($"生成Code失败，{outPut.Msg}");
                }
                if (string.IsNullOrEmpty(outPut.Codes[0]))
                {
                    throw new AppServiceException("单号生成异常，请重试！");
                }
            }
            await _db.PreCustomizeInvoiceItem.AddAsync(model);
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("申请成功");
        }

        /// <summary>
        /// 删除预开票申请
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> DeletePreCustomizeInvoice(Guid id)
        {
            var model = await _db.PreCustomizeInvoiceItem.Where(x => x.Id == id).FirstOrDefaultAsync();
            if (model == null)
            {
                return BaseResponseData<string>.Failed(500, "单据不存在或已被删除");
            }
            if (model.Status != PreCustomizeInvoiceItemStatusEnum.waitSubmit)
            {
                return BaseResponseData<string>.Failed(500, "只能删除待提交的单据");
            }
            var details = await _db.PreCustomizeInvoiceDetails.Where(x => x.PreCustomizeInvoiceItemId == id).ToListAsync();
            _db.PreCustomizeInvoiceDetails.RemoveRange(details);
            _db.PreCustomizeInvoiceItem.Remove(model);
            await _unitOfWork.CommitAsync();
            return BaseResponseData<string>.Success("删除成功");
        }

        /// <summary>
        /// 导入预开票明细
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ExportPreCustomizeInvoiceDetails(IFormFile file, Guid? id)
        {
            if (!id.HasValue)
            {
                return BaseResponseData<int>.Failed(500, "未关联主单");
            }
            var model = await _db.PreCustomizeInvoiceItem.Where(x => x.Id == id).FirstOrDefaultAsync();
            if (model == null)
            {
                return BaseResponseData<int>.Failed(500, "单据不存在或已被删除");
            }
            if (model.Status != PreCustomizeInvoiceItemStatusEnum.waitSubmit)
            {
                return BaseResponseData<int>.Failed(500, "状态为待提交时才可导入");
            }
            if (file == null)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "请上传导入文件"
                };
            }
            try
            {
                // 已有详情明细
                var dellist = await _db.PreCustomizeInvoiceDetails.Where(x => x.PreCustomizeInvoiceItemId == id).ToListAsync();
                Stream stream = file.OpenReadStream();
                var excelData = new List<PreCustomizeInvoiceDetailPo>();
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                    int rowCount = worksheet.Dimension.Rows;
                    if (rowCount <= 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "请填写Excel文件中的数据"
                        };
                    }
                    var taxClassCodes = await _bDSApiClient.GetAllTaxClassCode();
                    //var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("TaxClassCode");
                    for (var row = 2; row <= rowCount; row++)
                    {
                        #region 校验格式以及必填项
                        // 开票名称数据必填
                        if (worksheet.Cells[row, 1].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行开票名称信息"
                            };
                        }
                        string productName = worksheet.Cells[row, 1].Value.ToString();
                        // 计量单位数据必填
                        if (worksheet.Cells[row, 2].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行计量单位信息"
                            };
                        }
                        // 规格型号数据必填
                        if (worksheet.Cells[row, 3].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行规格型号信息"
                            };
                        }
                        // 数量数据必填
                        decimal number = 0;
                        if (worksheet.Cells[row, 4].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行数量信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 4].Value.ToString(), out number);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的数量"
                                };
                            }
                        }
                        // 单价数据必填
                        decimal price = 0;
                        if (worksheet.Cells[row, 5].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行单价信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out price);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的单价"
                                };
                            }
                        }
                        // 税率数据必填
                        decimal taxRate = 0;
                        if (worksheet.Cells[row, 6].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行税率信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 6].Value.ToString(), out taxRate);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的税率"
                                };
                            }
                        }
                        // 税收分类编码数据必填
                        if (worksheet.Cells[row, 7].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行税收分类编码"
                            };
                        }
                        // 税收分类编码校验
                        var dataDictionary = taxClassCodes.Where(p => p.number.Equals(worksheet.Cells[row, 7].Value.ToString())).FirstOrDefault();
                        if (dataDictionary == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"第{row}行税收分类编码错误"
                            };
                        }
                        // 订单号数据必填
                        if (worksheet.Cells[row, 8].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行订单号信息"
                            };
                        }
                        // 行性质数据必填
                        if (worksheet.Cells[row, 9].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行行性质信息"
                            };
                        }
                        string tag = worksheet.Cells[row, 9].Value.ToString();
                        if (tag == "折扣行")
                        {
                            if (price < 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"第{row}行折扣行单价必须为正数"
                                };
                            }
                            if (number > 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"第{row}行折扣行数量必须为负数"
                                };
                            }
                        }
                        #endregion

                        #region 装填参数
                        var detail = new PreCustomizeInvoiceDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            PreCustomizeInvoiceItemId = id.HasValue ? id.Value : Guid.Empty,
                            ProductName = productName,
                            PackUnit = worksheet.Cells[row, 2].Value.ToString(),
                            Specification = worksheet.Cells[row, 3].Value.ToString(),
                            Quantity = number,
                            Price = price,
                            Value = price * number,
                            TaxRate = taxRate,
                            TaxTypeNo = worksheet.Cells[row, 7].Value.ToString(),
                            TaxAmount = getTaxAmount(price, taxRate),
                            RelateCode = string.Empty,
                            OrderNo = worksheet.Cells[row, 8].Value.ToString(),
                            ProductId = null,
                            ProductNo = string.Empty,
                            Tag = worksheet.Cells[row, 9].Value.ToString(),
                        };
                        excelData.Add(detail);
                        #endregion
                    }

                    if (excelData.Count != rowCount - 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "Excel中存在单元格为空的数据，请检查"
                        };
                    }

                    if (excelData.Any())
                    {
                        // 校验金额
                        var amount = excelData.Sum(x => x.Value);
                        if (amount != model.Value)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = "导入明细总金额不等于预开票金额，请检查"
                            };
                        }
                        // 先删后增
                        _db.PreCustomizeInvoiceDetails.RemoveRange(dellist);
                        _db.PreCustomizeInvoiceDetails.AddRange(excelData);
                        await _unitOfWork.CommitAsync();
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Success,
                            Message = "导入成功"
                        };
                    }
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "导入失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "模板有误或数据错误");
            }
        }

        /// <summary>
        /// 获取税额
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="taxRate"></param>
        /// <returns></returns>
        private decimal? getTaxAmount(decimal? amount, decimal? taxRate)
        {
            return amount / (1 + taxRate / 100) * (taxRate / 100);
        }

        /// <summary>
        /// 提交预开票明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SubmitPreCustomizeInvoice(Guid? id)
        {
            if (!id.HasValue)
            {
                return BaseResponseData<int>.Failed(500, "未关联主单");
            }
            var model = await _db.PreCustomizeInvoiceItem.Where(x => x.Id == id).FirstOrDefaultAsync();
            if (model == null)
            {
                return BaseResponseData<int>.Failed(500, "单据不存在或已被删除");
            }
            if (model.Status != PreCustomizeInvoiceItemStatusEnum.waitSubmit)
            {
                return BaseResponseData<int>.Failed(500, "状态为待提交时才可提交");
            }
            try
            {
                // 更改状态为已提交
                model.Status = PreCustomizeInvoiceItemStatusEnum.waitAudit;
                // 获取明细
                var details = await _db.PreCustomizeInvoiceDetails.Where(x => x.PreCustomizeInvoiceItemId == id).ToListAsync();
                if (details == null || !details.Any())
                {
                    return BaseResponseData<int>.Failed(500, "无明细不可提交");
                }
                _db.PreCustomizeInvoiceItem.Update(model);
                #region 创建申开单等信息
                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = model.CustomerId.HasValue ? model.CustomerId.ToString() : Guid.Empty.ToString()
                });
                //生成单号
                var companyId = model.CompanyId.HasValue ? model.CompanyId.ToString() : string.Empty;
                var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { companyId } });
                var companyInfo = companyInfoOutput?.FirstOrDefault();
                if (companyInfo == null)
                {
                    return BaseResponseData<int>.Failed(500, "未找到公司");
                }
                var code = model.Code;
                var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                {
                    BusinessArea = code.Split('-')[0],
                    BillType = "CI",
                    SysMonth = companyInfo.sysMonth,
                    DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                    Num = 1,
                    CompanyCode = companyInfo.nameCode
                });
                if (outPut.Status)
                {
                    var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = code.Split('-')[0],
                        BillType = "CIC",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    var classifyId = Guid.NewGuid();
                    //添加分类
                    var invoiceClassify = new CustomizeInvoiceClassifyPo
                    {
                        Id = classifyId,
                        BillCode = outPutClassify.Codes.First(),
                        CompanyId = Guid.Parse(companyInfo.companyId),
                        CompanyName = model.CompanyName,
                        CustomerId = model.CustomerId.HasValue ? model.CustomerId.Value : Guid.Empty,
                        CustomerName = model.CustomerName,
                        CreatedBy = model.CreatedBy,
                        Status = CustomizeInvoiceStatusEnum.WaitSubmit,
                        AttachFileIds = string.Empty,
                        Classify = CustomizeInvoiceClassifyEnum.Pre,
                        SaleSystemName = string.Empty,
                        RelationCode = model.Code
                    };
                    await _db.CustomizeInvoiceClassify.AddAsync(invoiceClassify);

                    var itemId = Guid.NewGuid();
                    var customizeInvoiceItem = new CustomizeInvoiceItem()
                    {
                        Id = itemId,
                        Code = outPut.Codes.First(),
                        CustomerId = model.CustomerId.HasValue ? model.CustomerId.Value.ToString() : string.Empty,
                        CustomerName = model.CustomerName ??= string.Empty,
                        CompanyId = model.CompanyId,
                        CompanyName = model.CompanyName,
                        BillDate = DateTime.Now,
                        NameCode = model.NameCode,
                        InvoiceTotalAmount = details.Sum(t => t.Value),
                        IsPush = false,
                        IsInvoiced = false,
                        Status = 0,
                        Remark = $"预开票,生成单号{model.Code}",
                        InvoiceType = (InvoiceTypeEnum)(int.Parse(customer?.customerInvoices?.FirstOrDefault(t => t.isInvoiceUnit == 1)?.salesInvoiceDetails ?? "0")),
                        CustomizeInvoiceClassifyId = classifyId
                    };
                    customizeInvoiceItem.CreateBy(model.CreatedBy);
                    await _customizeInvoiceItemRepository.AddAsync(customizeInvoiceItem);

                    // 添加明细
                    var detailList = new List<CustomizeInvoiceDetail>();
                    var customizeInvoiceSubDetails = new List<CustomizeInvoiceSubDetailPo>();
                    foreach (var item in details)
                    {
                        int index = 1;
                        decimal? taxAmount = getTaxAmount(item.Value, item.TaxRate);
                        var detail = new CustomizeInvoiceDetail();
                        detail.Id = Guid.NewGuid();
                        detail.CreditBillCode = string.Empty;
                        detail.ProductName = item.ProductName ??= string.Empty;
                        detail.Quantity = item.Quantity;
                        detail.Price = item.Price;
                        detail.PackUnit = item.PackUnit ??= string.Empty;
                        detail.Specification = item.Specification ??= string.Empty;
                        detail.Tag = item.Tag;
                        detail.Value = item.Value;
                        detail.CustomizeInvoiceItemId = itemId;
                        detail.IsDeleted = false;
                        detail.OrderNo = item.OrderNo;
                        detail.CustomizeInvoiceIndex = index.ToString();
                        detail.TaxAmount = taxAmount.HasValue ? taxAmount.Value : 0;
                        detail.TaxRate = item.TaxRate.HasValue ? item.TaxRate.Value : 0;
                        detail.TaxTypeNo = item.TaxTypeNo;
                        detail.OriginalPrice = item.Price;
                        detail.OriginSpecification = item.Specification;
                        detail.OriginProductName = item.ProductName;
                        detail.CustomizeInvoiceItemId = itemId;
                        detail.CustomerId = model.CustomerId.HasValue ? model.CustomerId.Value.ToString() : string.Empty;
                        detail.CustomerName = model.CustomerName;
                        detail.CustomizeInvoiceIndex = index.ToString();
                        detail.OriginDetailId = string.Empty;
                        detail.RelateCode = item.OrderNo ??= string.Empty;
                        detail.CreatedBy = model.CreatedBy;
                        detail.CreatedTime = DateTimeOffset.UtcNow;
                        detailList.Add(detail);

                        var cisd = new CustomizeInvoiceSubDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            ProductName = item.ProductName ??= string.Empty,
                            Quantity = item.Quantity,
                            Price = item.Price,
                            CreditBillCode = string.Empty,
                            CreatedBy = model.CreatedBy,
                            CreatedTime = DateTimeOffset.UtcNow,
                            PackUnit = item.PackUnit,
                            Specification = item.Specification,
                            OriginProductName = item.ProductName,
                            CustomizeInvoiceItemId = itemId,
                            OriginSpecification = item.Specification
                        };
                        customizeInvoiceSubDetails.Add(cisd);
                        index++;
                    }

                    if (customizeInvoiceSubDetails.Any())
                    {
                        await _db.CustomizeInvoiceSubDetails.AddRangeAsync(customizeInvoiceSubDetails);
                    }
                    if (detailList.Any())
                    {
                        await _customizeInvoiceDetailRepository.AddManyAsync(detailList);
                    }
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Success("成功");
                }
                #endregion

                return BaseResponseData<int>.Failed(500, "生成单号失败");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 导入初始应收开票
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ExportInitCredit(IFormFile file, string? userName)
        {
            if (file == null)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "请上传导入文件"
                };
            }
            try
            {
                // 已有详情明细
                Stream stream = file.OpenReadStream();
                var excelData = new List<InitCreditExport>();
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                    int rowCount = worksheet.Dimension.Rows;
                    if (rowCount <= 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "请填写Excel文件中的数据"
                        };
                    }
                    //var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("TaxClassCode");
                    var taxClassCodes = await _bDSApiClient.GetAllTaxClassCode();
                    for (var row = 2; row <= rowCount; row++)
                    {
                        #region 校验格式以及必填项
                        // 初始应收单号数据必填
                        if (worksheet.Cells[row, 1].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行初始应收单号"
                            };
                        }
                        // 开票名称数据必填
                        if (worksheet.Cells[row, 2].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行开票名称信息"
                            };
                        }
                        string productName = worksheet.Cells[row, 2].Value.ToString();
                        // 计量单位数据必填
                        if (worksheet.Cells[row, 2].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行计量单位信息"
                            };
                        }
                        // 规格型号数据必填
                        if (worksheet.Cells[row, 4].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行规格型号信息"
                            };
                        }
                        // 数量数据必填
                        decimal number = 0;
                        if (worksheet.Cells[row, 5].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行数量信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out number);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的数量"
                                };
                            }
                        }
                        // 单价数据必填
                        decimal price = 0;
                        if (worksheet.Cells[row, 6].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行单价信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 6].Value.ToString(), out price);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的单价"
                                };
                            }
                        }
                        // 税率数据必填
                        decimal taxRate = 0;
                        if (worksheet.Cells[row, 7].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行税率信息"
                            };
                        }
                        else
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 7].Value.ToString(), out taxRate);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的税率"
                                };
                            }
                        }
                        // 税收分类编码数据必填
                        if (worksheet.Cells[row, 8].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行税收分类编码"
                            };
                        }
                        // 税收分类编码校验
                        var dataDictionary = taxClassCodes.Where(p => p.number.Equals(worksheet.Cells[row, 8].Value.ToString())).FirstOrDefault();
                        if (dataDictionary == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"第{row}行税收分类编码错误"
                            };
                        }
                        // 订单号数据必填
                        if (worksheet.Cells[row, 9].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行订单号信息"
                            };
                        }
                        // 行性质数据必填
                        if (worksheet.Cells[row, 10].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行行性质信息"
                            };
                        }

                        string tag = worksheet.Cells[row, 10].Value.ToString();
                        if (tag == "折扣行")
                        {
                            if (price < 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"第{row}行折扣行单价必须为正数"
                                };
                            }
                            if (number > 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"第{row}行折扣行数量必须为负数"
                                };
                            }
                        }
                        #endregion

                        #region 装填参数
                        var detail = new InitCreditExport()
                        {
                            InitCreditNo = worksheet.Cells[row, 1].Value.ToString().TrimStart().TrimEnd(),
                            ProductName = productName,
                            PackUnit = worksheet.Cells[row, 3] == null ? "" : worksheet.Cells[row, 3].Value.ToString(),
                            Specification = worksheet.Cells[row, 4].Value.ToString(),
                            Quantity = number,
                            Price = price,
                            Value = price * number,
                            TaxRate = taxRate,
                            TaxTypeNo = worksheet.Cells[row, 8].Value.ToString(),
                            TaxAmount = getTaxAmount(price, taxRate),
                            RelateCode = string.Empty,
                            OrderNo = worksheet.Cells[row, 9].Value.ToString().TrimStart().TrimEnd(),
                            ProductId = null,
                            ProductNo = string.Empty,
                            Tag = worksheet.Cells[row, 10].Value.ToString(),

                        };
                        excelData.Add(detail);
                        #endregion
                    }

                    if (excelData.Count != rowCount - 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "Excel中存在单元格为空的数据，请检查"
                        };
                    }

                    if (excelData.Any())
                    {
                        //if (excelData.Count != excelData.DistinctBy(x=>x.InitCreditNo).Count())
                        //{
                        //    return new BaseResponseData<int>()
                        //    {
                        //        Code = CodeStatusEnum.Failed,
                        //        Message = "Excel中存在初始应收重复单号，请检查或合并"
                        //    };
                        //}
                        // 校验初始应收等数据
                        var initCreditNos = excelData.Select(x => x.InitCreditNo).ToList();
                        var initCredits = await _db.Credits.Where(x => initCreditNos.Contains(x.BillCode)).ToListAsync();
                        if (initCredits == null || !initCredits.Any())
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = "未在系统中找到任何初始应收单，请检查"
                            };
                        }

                        // 公司信息
                        var groupedByCompany = initCredits.GroupBy(x => new { x.CompanyId }).ToList();
                        if (groupedByCompany.Count != 1)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"导入多个应收单公司不一致"
                            };
                        }
                        // 客户信息
                        var groupedByCustomer = initCredits.GroupBy(x => new { x.CustomerId }).ToList();
                        if (groupedByCustomer.Count != 1)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"导入多个应收单客户不一致"
                            };
                        }

                        // 先做金额公司检查
                        var groupedData = excelData.GroupBy(x => x.InitCreditNo)
                                                  .Select(g => new GroupByInitCreditNoDto
                                                  {
                                                      InitCreditNo = g.Key,
                                                      TotalValue = g.Sum(x => x.Value),
                                                      TotalUsedAmount = 0  //暂赋值0
                                                  })
                                                  .ToList();
                        // 去重
                        initCreditNos = groupedData.Select(x => x.InitCreditNo).ToList();
                        // 判断是否存在重复导入未红冲的单据 -> 支持多次开票，未红冲的金额计算在内，不超过初始应收总金额即可
                        var existsCids = await _db.CustomizeInvoiceDetail.Include(x => x.CustomizeInvoiceItem).Where(x => x.CustomizeInvoiceItem.Status != CustomizeInvoiceStatusEnum.Cancel && initCreditNos.Contains(x.CreditBillCode)).ToListAsync();

                        foreach (var item in groupedData)
                        {
                            // 检测是否已开票金额
                            var existValue = 0M;
                            var existsCurrentCids = existsCids.Where(x => x.CreditBillCode == item.InitCreditNo).ToList();
                            var existsCurrentCiis = existsCurrentCids.Select(x => x.CustomizeInvoiceItem).Distinct().ToList();
                            foreach (var ci in existsCurrentCiis)
                            {
                                if (ci.ChangedStatus != Finance.Data.Enums.CustomizeInvoiceChangedStatusEnum.RedOffset && string.IsNullOrEmpty(ci.RelationCode))
                                {
                                    existValue += existsCids.Where(x => x.CustomizeInvoiceItemId == ci.Id && x.CreditBillCode == item.InitCreditNo).Sum(x => x.Value);
                                }
                            }
                            item.TotalUsedAmount = existValue;
                            var model = initCredits.FirstOrDefault(x => x.BillCode == item.InitCreditNo);
                            if (model == null)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"未找到单号为{item.InitCreditNo}的初始应收单"
                                };
                            }
                            if (model.CreditType != CreditTypeEnum.origin)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"单号为{item.InitCreditNo}的应收类型错误，只能导入初始应收单"
                                };
                            }
                            // 校验应收金额-已开票金额-已开票金额 > 本次导入金额
                            // var currentInvoiceValue = invoiceCredits.Where(x => x.CreditId == model.Id).Sum(x => x.InvoiceAmount);
                            if (model.Value > 0 && item.TotalValue < 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"初始应收单为正数应收，导入总金额不能为负数"
                                };
                            }
                            if (model.Value < 0 && item.TotalValue > 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"初始应收单为负数应收，导入总金额不能为正数"
                                };
                            }
                            if (Math.Abs(model.Value) < Math.Abs(item.TotalValue.Value) + Math.Abs(item.TotalUsedAmount.Value))
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"初始应收单{item.InitCreditNo}导入金额{item.TotalValue}与已开票金额{item.TotalUsedAmount}超出应收总金额{model.Value}"
                                };
                            }
                            if (!model.CompanyId.HasValue)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"初始应收单{item.InitCreditNo}中未绑定公司"
                                };
                            }
                        }

                        // 公司
                        var companyId = initCredits[0].CompanyId.HasValue ? initCredits[0].CompanyId.ToString() : String.Empty;
                        var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                        {
                            ids = new List<string> { companyId }
                        })).FirstOrDefault();
                        if (companyInfo == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"初始应收单{initCredits[0].BillCode}中的公司未找到"
                            };
                        }
                        // 客户
                        var customerId = initCredits[0].CustomerId.HasValue ? initCredits[0].CustomerId.ToString() : String.Empty;
                        var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                        {
                            id = customerId
                        });

                        // 获取明细
                        var details = excelData.ToList();
                        var single = initCredits[0];
                        #region 创建申开单等信息
                        var code = single.BillCode;
                        var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                        {
                            BusinessArea = code.Split('-')[0],
                            BillType = "CI",
                            SysMonth = companyInfo.sysMonth,
                            DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                            Num = 1,
                            CompanyCode = companyInfo.nameCode
                        });
                        if (outPut.Status)
                        {
                            var outPutClassify = await _codeGenClient.ApplyCode(new ApplyCodeInput
                            {
                                BusinessArea = code.Split('-')[0],
                                BillType = "CIC",
                                SysMonth = companyInfo.sysMonth,
                                DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                                Num = 1,
                                CompanyCode = companyInfo.nameCode
                            });
                            var classifyId = Guid.NewGuid();
                            //添加分类
                            var invoiceClassify = new CustomizeInvoiceClassifyPo
                            {
                                Id = classifyId,
                                BillCode = outPutClassify.Codes.First(),
                                CompanyId = Guid.Parse(companyInfo.companyId),
                                CompanyName = single.CompanyName,
                                CustomerId = single.CustomerId.HasValue ? single.CustomerId.Value : Guid.Empty,
                                CustomerName = single.CustomerName,
                                CreatedBy = userName ??= single.CreatedBy,
                                Status = CustomizeInvoiceStatusEnum.WaitSubmit,
                                AttachFileIds = string.Empty,
                                Classify = CustomizeInvoiceClassifyEnum.InitCredit,
                                SaleSystemName = string.Empty,
                                CreatedTime = DateTimeOffset.UtcNow
                            };
                            await _db.CustomizeInvoiceClassify.AddAsync(invoiceClassify);

                            var itemId = Guid.NewGuid();
                            var customizeInvoiceItem = new CustomizeInvoiceItem()
                            {
                                Id = itemId,
                                Code = outPut.Codes.First(),
                                CustomerId = single.CustomerId.HasValue ? single.CustomerId.Value.ToString() : string.Empty,
                                CustomerName = single.CustomerName ??= string.Empty,
                                CompanyId = single.CompanyId,
                                CompanyName = single.CompanyName,
                                BillDate = DateTime.Now,
                                NameCode = single.NameCode,
                                InvoiceTotalAmount = details.Sum(t => t.Value),
                                IsPush = false,
                                IsInvoiced = false,
                                Status = 0,
                                Remark = $"初始应收,生成单号{single.BillCode}",
                                InvoiceType = (InvoiceTypeEnum)(int.Parse(customer?.customerInvoices?.FirstOrDefault(t => t.isInvoiceUnit == 1)?.salesInvoiceDetails ?? "0")),
                                CustomizeInvoiceClassifyId = classifyId,
                                CreatedBy = userName ??= single.CreatedBy,
                                CreatedTime = DateTimeOffset.UtcNow
                            };
                            await _customizeInvoiceItemRepository.AddAsync(customizeInvoiceItem);

                            // 添加明细
                            var detailList = new List<CustomizeInvoiceDetail>();
                            var customizeInvoiceSubDetails = new List<CustomizeInvoiceSubDetailPo>();
                            int index = 0;
                            foreach (var item in details)
                            {
                                decimal? taxAmount = getTaxAmount(item.Value, item.TaxRate);
                                var detail = new CustomizeInvoiceDetail();
                                detail.Id = Guid.NewGuid();
                                detail.ProductName = item.ProductName ??= string.Empty;
                                detail.Quantity = item.Quantity;
                                detail.Price = item.Price;
                                detail.PackUnit = item.PackUnit ??= string.Empty;
                                detail.Specification = item.Specification ??= string.Empty;
                                detail.Tag = item.Tag;
                                detail.Value = item.Value;
                                detail.CustomizeInvoiceItemId = itemId;
                                detail.IsDeleted = false;
                                detail.OrderNo = item.OrderNo;
                                detail.CustomizeInvoiceIndex = index.ToString();
                                detail.Sort = index + 1;
                                detail.TaxAmount = taxAmount.HasValue ? taxAmount.Value : 0;
                                detail.TaxRate = item.TaxRate.HasValue ? item.TaxRate.Value : 0;
                                detail.TaxTypeNo = item.TaxTypeNo;
                                detail.OriginalPrice = item.Price;
                                detail.OriginSpecification = item.Specification;
                                detail.OriginProductName = item.ProductName;
                                detail.CustomizeInvoiceItemId = itemId;
                                detail.CustomerId = single.CustomerId.HasValue ? single.CustomerId.Value.ToString() : string.Empty;
                                detail.CustomerName = single.CustomerName;
                                detail.CreditBillCode = item.InitCreditNo ??= string.Empty;
                                detail.CustomizeInvoiceIndex = index.ToString();
                                detail.OriginDetailId = string.Empty;
                                detail.RelateCode = item.OrderNo ??= string.Empty;
                                detail.CreatedBy = userName ??= single.CreatedBy;
                                detail.CreatedTime = DateTimeOffset.UtcNow;
                                if (detail.Tag == "折扣行")
                                {
                                    detail.ParentId = detailList[index - 1].Id;
                                }
                                detailList.Add(detail);

                                var cisd = new CustomizeInvoiceSubDetailPo()
                                {
                                    Id = Guid.NewGuid(),
                                    ProductName = item.ProductName ??= string.Empty,
                                    Quantity = item.Quantity,
                                    Price = item.Price,
                                    CreditBillCode = item.InitCreditNo ??= string.Empty,
                                    CreatedBy = userName ??= single.CreatedBy,
                                    CreatedTime = DateTimeOffset.UtcNow,
                                    PackUnit = item.PackUnit,
                                    Specification = item.Specification,
                                    OriginProductName = item.ProductName,
                                    CustomizeInvoiceItemId = itemId,
                                    OriginSpecification = item.Specification
                                };
                                customizeInvoiceSubDetails.Add(cisd);
                                index++;
                            }

                            if (customizeInvoiceSubDetails.Any())
                            {
                                await _db.CustomizeInvoiceSubDetails.AddRangeAsync(customizeInvoiceSubDetails);
                            }
                            if (detailList.Any())
                            {
                                await _customizeInvoiceDetailRepository.AddManyAsync(detailList);
                            }
                        }
                        #endregion

                        await _unitOfWork.CommitAsync();
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Success,
                            Message = "导入开票成功"
                        };
                    }
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "导入开票失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "模板有误或数据错误");
            }
        }

        /// <summary>
        /// 导出主单加明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<MemoryStream> ExportCustomizeInvoiceItemAndDetail(OriginDetailQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();

                if (query.CheckData == null || query.CheckData.Count <= 0)
                {
                    return stream;
                }
                var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
                {
                    Names = query.CheckData.Select(p => p.CreatedBy).Distinct().ToList(),
                    Page = 1,
                    Limit = query.CheckData.Select(p => p.CreatedBy).Distinct().ToList().Count()
                });
                var (detailList, detailCount) = await GetOriginDetailAsync(query);
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "关联单号");
                    worksheet.SetValue(1, columnIndex++, "订单号");
                    worksheet.SetValue(1, columnIndex++, "原始订单号");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "客户");
                    worksheet.SetValue(1, columnIndex++, "部门");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "项目名称");
                    worksheet.SetValue(1, columnIndex++, "订货人");
                    worksheet.SetValue(1, columnIndex++, "客户订单号");
                    worksheet.SetValue(1, columnIndex++, "金额");
                    worksheet.SetValue(1, columnIndex++, "已开票金额");
                    worksheet.SetValue(1, columnIndex++, "采购成本金额");
                    worksheet.SetValue(1, columnIndex++, "销售子系统");
                    worksheet.SetValue(1, columnIndex++, "应收类型");
                    worksheet.SetValue(1, columnIndex++, "备注");
                    worksheet.SetValue(1, columnIndex++, "创建日期");
                    worksheet.SetValue(1, columnIndex++, "创建人");
                    //详情表头
                    worksheet.SetValue(1, columnIndex++, "产品名称");
                    worksheet.SetValue(1, columnIndex++, "开票名称");
                    worksheet.SetValue(1, columnIndex++, "原始单位");
                    worksheet.SetValue(1, columnIndex++, "计量单位");
                    worksheet.SetValue(1, columnIndex++, "原始规格");
                    worksheet.SetValue(1, columnIndex++, "原始货号包装规格");
                    worksheet.SetValue(1, columnIndex++, "规格型号");
                    worksheet.SetValue(1, columnIndex++, "数量");
                    worksheet.SetValue(1, columnIndex++, "单价");
                    worksheet.SetValue(1, columnIndex++, "原价");
                    worksheet.SetValue(1, columnIndex++, "金额");
                    worksheet.SetValue(1, columnIndex++, "税率");
                    worksheet.SetValue(1, columnIndex++, "税额");

                    var index = 2;
                    query.CheckData.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.BillCode);
                        worksheet.SetValue(index, columnIndex++, p.RelateCode);
                        worksheet.SetValue(index, columnIndex++, p.OrderNo);
                        worksheet.SetValue(index, columnIndex++, p.OriginOrderNo);
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerName);
                        worksheet.SetValue(index, columnIndex++, p.DeptName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, p.ProjectName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerPersonName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerOrderCode);
                        worksheet.SetValue(index, columnIndex++, p.Value);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceAmount);
                        worksheet.SetValue(index, columnIndex++, p.PurchaseCost);
                        worksheet.SetValue(index, columnIndex++, p.SaleSystemName);
                        worksheet.SetValue(index, columnIndex++, p.CreditTypeStr);
                        worksheet.SetValue(index, columnIndex++, p.Note);
                        worksheet.SetValue(index, columnIndex++, p.CreatedTime.ToString("yyyy-MM-dd"));
                        if (userInfos.Data == null || userInfos.Data.List.Count() == 0 || userInfos.Data.List.Where(z => z.Name == p.CreatedBy).Count() == 0)
                        {
                            worksheet.SetValue(index, columnIndex++, p.CreatedBy);
                        }
                        else
                        {
                            worksheet.SetValue(index, columnIndex++, userInfos.Data.List.Where(z => z.Name == p.CreatedBy).FirstOrDefault().DisplayName);
                        }

                        var details = detailList.Where(z => z.CreditBillCode == p.BillCode && z.OrderNo == p.OrderNo && z.RelateCode == p.RelateCode).ToList();
                        if (details == null || details.Count <= 0)
                        {
                            index++;
                        }
                        else
                        {
                            foreach (var item in details)
                            {
                                var columnIndexSec = columnIndex;
                                worksheet.SetValue(index, columnIndex++, item.OriginProductName);
                                worksheet.SetValue(index, columnIndex++, item.ProductName);
                                worksheet.SetValue(index, columnIndex++, item.OriginPackUnit);
                                worksheet.SetValue(index, columnIndex++, item.PackUnit);
                                worksheet.SetValue(index, columnIndex++, item.OriginSpecification);
                                worksheet.SetValue(index, columnIndex++, item.OriginalPackSpec);
                                worksheet.SetValue(index, columnIndex++, item.Specification);
                                worksheet.SetValue(index, columnIndex++, item.Quantity);
                                worksheet.SetValue(index, columnIndex++, item.Price);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, columnIndex++, item.OriginalPrice);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, columnIndex++, item.Value);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, columnIndex++, item.TaxRate);
                                worksheet.SetValue(index, columnIndex++, item.TaxAmount);
                                worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                                columnIndex = columnIndexSec;
                                index++;
                            }
                        }
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 导出主单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<MemoryStream> ExportCustomizeInvoiceItem(CreditQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                var (list, count) = await _creditQueryService.GetListAsync(query);
                var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
                {
                    Names = list.Select(p => p.CreatedBy).Distinct().ToList(),
                    Page = 1,
                    Limit = list.Select(p => p.CreatedBy).Distinct().ToList().Count()
                });
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "关联单号");
                    worksheet.SetValue(1, columnIndex++, "订单号");
                    worksheet.SetValue(1, columnIndex++, "原始订单号");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "客户");
                    worksheet.SetValue(1, columnIndex++, "部门");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "项目名称");
                    worksheet.SetValue(1, columnIndex++, "订货人");
                    worksheet.SetValue(1, columnIndex++, "客户订单号");
                    worksheet.SetValue(1, columnIndex++, "金额");
                    worksheet.SetValue(1, columnIndex++, "已开票金额");
                    worksheet.SetValue(1, columnIndex++, "采购成本金额");
                    worksheet.SetValue(1, columnIndex++, "销售子系统");
                    worksheet.SetValue(1, columnIndex++, "应收类型");
                    worksheet.SetValue(1, columnIndex++, "备注");
                    worksheet.SetValue(1, columnIndex++, "创建日期");
                    worksheet.SetValue(1, columnIndex++, "创建人");

                    var index = 2;
                    list.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.BillCode);
                        worksheet.SetValue(index, columnIndex++, p.RelateCode);
                        worksheet.SetValue(index, columnIndex++, p.OrderNo);
                        worksheet.SetValue(index, columnIndex++, p.OriginOrderNo);
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerName);
                        worksheet.SetValue(index, columnIndex++, p.DeptName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, p.ProjectName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerPersonName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerOrderCode);
                        worksheet.SetValue(index, columnIndex++, p.Value);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceAmount);
                        worksheet.SetValue(index, columnIndex++, p.PurchaseCost);
                        worksheet.SetValue(index, columnIndex++, p.SaleSystemName);
                        worksheet.SetValue(index, columnIndex++, p.CreditTypeStr);
                        worksheet.SetValue(index, columnIndex++, p.Note);
                        worksheet.SetValue(index, columnIndex++, p.CreatedTime.ToString("yyyy-MM-dd"));
                        if (userInfos.Data == null || userInfos.Data.List.Count() == 0 || userInfos.Data.List.Where(z => z.Name == p.CreatedBy).Count() == 0)
                        {
                            worksheet.SetValue(index, columnIndex++, p.CreatedBy);
                        }
                        else
                        {
                            worksheet.SetValue(index, columnIndex++, userInfos.Data.List.Where(z => z.Name == p.CreatedBy).FirstOrDefault().DisplayName);
                        }

                        index++;
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 获取页面选中的主单导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<MemoryStream> ExportCustomizeInvoiceItemByIds(OriginDetailQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                if (query.CheckData == null || query.CheckData.Count <= 0)
                {
                    return stream;
                }
                var userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
                {
                    Names = query.CheckData.Select(p => p.CreatedBy).Distinct().ToList(),
                    Page = 1,
                    Limit = query.CheckData.Select(p => p.CreatedBy).Distinct().ToList().Count()
                });
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "关联单号");
                    worksheet.SetValue(1, columnIndex++, "订单号");
                    worksheet.SetValue(1, columnIndex++, "原始订单号");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "客户");
                    worksheet.SetValue(1, columnIndex++, "部门");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "项目名称");
                    worksheet.SetValue(1, columnIndex++, "订货人");
                    worksheet.SetValue(1, columnIndex++, "客户订单号");
                    worksheet.SetValue(1, columnIndex++, "金额");
                    worksheet.SetValue(1, columnIndex++, "已开票金额");
                    worksheet.SetValue(1, columnIndex++, "采购成本金额");
                    worksheet.SetValue(1, columnIndex++, "销售子系统");
                    worksheet.SetValue(1, columnIndex++, "应收类型");
                    worksheet.SetValue(1, columnIndex++, "备注");
                    worksheet.SetValue(1, columnIndex++, "创建日期");
                    worksheet.SetValue(1, columnIndex++, "创建人");
                    var index = 2;
                    query.CheckData.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.BillCode);
                        worksheet.SetValue(index, columnIndex++, p.RelateCode);
                        worksheet.SetValue(index, columnIndex++, p.OrderNo);
                        worksheet.SetValue(index, columnIndex++, p.OriginOrderNo);
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerName);
                        worksheet.SetValue(index, columnIndex++, p.DeptName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, p.ProjectName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerPersonName);
                        worksheet.SetValue(index, columnIndex++, p.CustomerOrderCode);
                        worksheet.SetValue(index, columnIndex++, p.Value);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceAmount);
                        worksheet.SetValue(index, columnIndex++, p.PurchaseCost);
                        worksheet.SetValue(index, columnIndex++, p.SaleSystemName);
                        worksheet.SetValue(index, columnIndex++, p.CreditTypeStr);
                        worksheet.SetValue(index, columnIndex++, p.Note);
                        worksheet.SetValue(index, columnIndex++, p.CreatedTime.ToString("yyyy-MM-dd"));
                        if (userInfos.Data == null || userInfos.Data.List.Count() == 0 || userInfos.Data.List.Where(z => z.Name == p.CreatedBy).Count() == 0)
                        {
                            worksheet.SetValue(index, columnIndex++, p.CreatedBy);
                        }
                        else
                        {
                            worksheet.SetValue(index, columnIndex++, userInfos.Data.List.Where(z => z.Name == p.CreatedBy).FirstOrDefault().DisplayName);
                        }
                        index++;
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 运营制作开票协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportCustomizeInvoiceTask(CreditQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DictionaryExtensions.DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_CustomizeInvoiceDetailExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("运营制作开票导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateclient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        /// <summary>
        /// 运营制作开票协调服务导出接口
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<CustomizeInvoiceExportOutput>, int)> CustomizeInvoiceListExportAsync(CreditQueryInput query)
        {

            var list = new List<CreditQueryListOutput>();
            var count = 0;
            (list, count) = await _creditQueryService.GetListAsync(query);
            var retList = new List<CustomizeInvoiceExportOutput>();
            var creditIds = list.Select(t => (Guid?)t.Id).ToList();
            var creditDetails = new List<CreditDetailPo>();
            var userInfos = new ResponseData<UserOutput>();
            if (creditIds != null && creditIds.Count > 0)
            {
                creditDetails = await _db.CreditDetails.Where(p => creditIds.ToHashSet().Contains(p.CreditId)).AsNoTracking().ToListAsync();
                userInfos = await _bDSApiClient.GetUserByNamesAsync(new GetUserInput()
                {
                    Names = list.Select(p => p.CreatedBy).Distinct().ToList(),
                    Page = 1,
                    Limit = list.Select(p => p.CreatedBy).Distinct().ToList().Count()
                });
            }
            foreach (var item in list)
            {

                var detailItem = creditDetails.Where(p => p.CreditId == item.Id).ToList();
                bool isFirstRow = false;
                for (int i = 0; i < detailItem.Count; i++)//
                {
                    if (detailItem[i].NoInvoiceAmount != 0)
                    {
                        var creditItem = new CustomizeInvoiceExportOutput();
                        if (isFirstRow == false)
                        {
                            creditItem = item.Adapt<CustomizeInvoiceExportOutput>();
                            creditItem.IsSureIncome = item.IsSureIncome == 1 ? "是" : "否";
                            creditItem.BillDate = item.BillDate.Value.ToString("yyyy-MM-dd");
                            creditItem.CreatedTime = item.CreatedTime.ToString("yyyy-MM-dd");
                            if (userInfos.Data != null && userInfos.Data.List.Count() != 0 && userInfos.Data.List.Where(z => z.Name == item.CreatedBy).Count() != 0)
                            {
                                creditItem.CreatedBy = userInfos.Data.List.Where(z => z.Name == item.CreatedBy).FirstOrDefault().DisplayName;
                            }
                            //isFirstRow = true;
                            //#116468 【小】运营制作开票界面的导出，单头填充到每一行的明细
                            isFirstRow = false;
                        }
                        creditItem.OriginProductName = detailItem[i].ProductName;
                        creditItem.OriginPackUnit = detailItem[i].PackUnit;
                        creditItem.OriginSpecification = detailItem[i].ProductNo;
                        creditItem.OriginalPackSpec = detailItem[i].PackSpec;
                        creditItem.Quantity = detailItem[i].Quantity;
                        creditItem.Price = detailItem[i].Price;
                        creditItem.OriginalPrice = detailItem[i].OriginalPrice;
                        creditItem.DetailValue = detailItem[i].Amount.Value;
                        creditItem.TaxRate = detailItem[i].TaxRate;
                        creditItem.IFHighValue = detailItem[i].IFHighValue == null || detailItem[i].IFHighValue == 0 ? "否" : "是";
                        creditItem.NoInvoiceAmount = detailItem[i].NoInvoiceAmount;
                        creditItem.PriceSource = detailItem[i].PriceSource;
                        retList.Add(creditItem);
                    }
                }
            }
            return (retList, count);

        }
    }
}
