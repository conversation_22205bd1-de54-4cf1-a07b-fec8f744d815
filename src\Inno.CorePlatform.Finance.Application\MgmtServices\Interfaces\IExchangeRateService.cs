﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IExchangeRateService
    {
        Task<int> Add(ExchangeRateInput input);
         
        Task<GetExchangeRateOutput> GetByExchangeRate(GetExchangeRateInput input);
        /// <summary>
        /// 查询汇率
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<GetExchangeRateOutput>> GetByExchangeRateWithKD(GetExchangeRateInput input);
    }
}
