﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class CustomizeInvoiceExportOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public string? BillDate { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditTypeStr
        {
            get
            {
                string ret = string.Empty;
                if (CreditType != null)
                {
                    return ((CreditTypeEnum)CreditType).GetDescription();
                }
                return ret;
            }
        }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 应收类型
        /// </summary>
        public int DebtorType { get; set; }

        /// <summary>
        /// 完成日期
        /// </summary>
        public DateTime? FinishDate { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public int? InvoiceStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 应收值
        /// </summary>
        public decimal? Value { get; set; }

        ///// <summary>
        ///// 是否自动批量
        ///// </summary>
        //public int? Auto { get; set; }

        ///// <summary>
        ///// 自动批量类型
        ///// </summary>
        //public string? AutoType { get; set; }

        ///// <summary>
        ///// 自动批量类型名称
        ///// </summary>
        //public string? AutoTypeName { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        ///// <summary>
        ///// 是否长期
        ///// </summary>
        //public int? IsLongTerm { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public string? IsSureIncome { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value == null ? 0 : Value.Value) - AbatmentAmount; } }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        public string? CreatedTime { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum? InvoiceType { get; set; }

        /// <summary>
        /// 是否无需开票,1=无需开票，0 or null=需要开票
        /// </summary> 
        public IsNoNeedInvoiceEnum? IsNoNeedInvoice { get; set; }

        /// <summary>
        /// 是否无需开票,1=无需开票，0 or null=需要开票
        /// </summary> 
        public int IsNoNeedInvoiceInt
        {
            get
            {
                return IsNoNeedInvoice.HasValue ? (int)IsNoNeedInvoice : 0;
            }
        }


        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }
        public SaleSourceEnum? SaleSource { get; set; }


        public string? HospitalId { get; set; }

        public string? HospitalName { get; set; }


        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum? SaleType { get; set; }

        /// <summary>
        /// 运输单号
        /// </summary>
        public string? ShipmentCode { get; set; }

        public string? DeptName { get; set; }

        /// <summary>
        /// 签收日期
        /// </summary> 
        public DateTime? IsSureIncomeDate { get; set; }

        /// <summary>
        /// 收入确认模式 有值并且值=1=分期生成
        /// </summary>
        public ServiceConfirmRevenuePlanModeEnum? ServiceConfirmRevenuePlanModeEnum { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 订货人
        /// </summary>
        public string? CustomerPersonName { get; internal set; }

        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? ProbablyBackTime { get; set; }
        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? OverdueDay
        {
            get
            {
                return ProbablyBackTime.HasValue ? (ProbablyBackTime >= DateTime.Today || AbatedStatus == AbatedStatusEnum.Abated ? null : (int)(DateTime.Now - ProbablyBackTime).Value.TotalDays) : null;
            }
        }

        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }



        //详情数据

        /// <summary>
        /// 原明细Id
        /// </summary>
        public string? OriginDetailId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        public string? OriginalPackSpec { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 原始品名（不变）
        /// </summary>
        public string? OriginProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 原始单位
        /// </summary>
        public string? OriginPackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }
        /// <summary>
        /// 原始单价
        /// </summary>
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal DetailValue { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount
        {
            get
            {
                if (TaxRate.HasValue && NoInvoiceAmount.HasValue)
                {

                    var ret = decimal.Parse((Math.Abs(NoInvoiceAmount.Value) - Math.Abs(NoInvoiceAmount.Value) / (1 + (TaxRate / 100))).Value.ToString("F2"));
                    if (Quantity < 0)
                    {
                        ret = -ret;
                    }
                    return ret;
                }
                else
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 原始规格型号
        /// </summary>
        public string? OriginSpecification { get; set; }
        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高价值(0:否,1:是)
        /// </summary>
        public string? IFHighValue { get; set; }
        public decimal? NoInvoiceAmount { get; set; }
        public Guid CreditDetailId { get; set; }
        public Guid? BatchId { get; set; }
        public Guid? SaleDetailId { get; set; }


        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        ///价格来源描述
        /// </summary>   
        public string PriceSourceStr
        {
            get
            {
                if (PriceSource.HasValue)
                {
                    return PriceSource.GetDescription();
                }
                else
                {
                    return "";
                }
            }
        }

    }
}
