﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 上传会函件入参
    /// </summary>
    public class ReconciliationLetterAttachFileInput
    {
        public Guid ReconciliationLetterItemId { get; set; }

        public string? FileIds { get; set; }
        public string? FileId { get; set; }

    }
}
