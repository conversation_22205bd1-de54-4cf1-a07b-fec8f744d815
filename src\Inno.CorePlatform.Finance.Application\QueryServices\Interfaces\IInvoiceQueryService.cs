﻿
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 发票查询
    /// </summary>
    public interface IInvoiceQueryService
    {
        /// <summary>
        /// 发票查询
        /// </summary>
        Task<(List<InvoiceQueryListOutput>, int)> GetListAsync(InvoiceQueryInput query);

        /// <summary>
        /// 发票查询 (按发票号查询，不分页)
        /// </summary>
        Task<(List<InvoiceQueryListOutput>, int)> GetListByInvoiceNoAsync(InvoiceQueryInput query);

        /// <summary>
        /// 发票清单查询
        /// </summary>
        Task<(List<InvoicesQueryListOutput>, int)> GetInvoiceListAsync(InvoicesQueryInput query, bool isExport = false);

        /// <summary>
        /// 发票查询（批量认款）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<InvoicesQueryListOutput>, int)> GetInvoiceListByRecognizeReceiveAsync(InvoicesQueryInput input);

        /// <summary>
        /// 根据应收单号获取冲销记录
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<AbatementByCreditBillCodeQueryOutput>, int)> GetAbatementByCreditBillCode([FromBody] AbamentByCreditBillCodeQueryInput query);

        /// <summary>
        /// 打包发票发送邮箱
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SendEmailInvoices(InvoicesQueryInput query);

        /// <summary>
        /// 获取SPD发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSPDTabCount(InvoicesQueryInput query);

        /// <summary>
        /// 获取阳采发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSunPurchaseTabCount(InvoicesQueryInput query);

        /// <summary>
        /// SPD审核发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> SPDApprove(List<SPDApproveInput> input);

        /// <summary>
        /// 根据发票号获取应收列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<InvoiceCreditsOutput>, int)> GetInvoiceCreditsByInvoiceNo(InvoiceQueryInput query);
        /// <summary>
        /// 根据发票号获取应收列表（SPD版）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<InvoiceCreditsOutput>, int)> GetSPDInvoiceCreditsByInvoiceNo(SPDInvoiceQueryInput query);
        /// <summary>
        /// 获取阳采发票详细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<SunPurchaseInvoiceDetailsOutput>, int)> GetSunPurchaseInvoiceDetailByInvoiceNo([FromBody] InvoicesQueryInput query);
        /// <summary>
        /// 获取可入账的发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<InvoiceReceiptDetailVoOutput>, int)> GetReceiptInvoiceList(InvoiceReceiptDetailQueryInput input);

        Task<InvoicesQueryListOutput> GetInvoiceByInvoiceNo(string invoiceNo);

        Task<(List<InvoiceCreditsOutput>, int)> GetInvoiceCredits(SPDInvoiceQueryInput query);

        Task<List<InvoicesQueryListOutput>> GetInvoiceByInvoiceNos(List<string> invoiceNos);

        /// <summary>
        /// 忽略阳采发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> IgnoreSunPurchaseInvoice(InvoicesQueryInput query);

        /// <summary>
        /// 同步阳采发票状态
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SyncSunPurchaseInvoiceStatus(InvoicesQueryInput query);

        /// <summary>
        /// 编辑阳采发票详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> EditSunPurchaseInvoiceDetail(EditSunPurchaseInvoiceDetailInput query);

        /// <summary>
        /// 提交阳采发票填报
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SubmitSunPurchaseInvoice(SunPurchaseInvoiceSubmitInput query);

        /// <summary>
        /// 获取阳采发票配送编码
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>> GetPsdByPurchaseCodeForPm([FromBody] SunPurchaseInvoiceSubmitInput query);

        /// <summary>
        /// 获取阳采配送单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ShycShipmentDetailOutput>>> SunPurchaseGetFullDetails([FromBody] ShycShipmentDetaillnput query);

        /// <summary>
        /// 获取初始化发票至金蝶列表数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<InvoiceChildInitRequestVo>> GetInitEasInvoices(InitInvoicesInput input);

        /// <summary>
        /// 获取发票金额
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<InvoiceAmountOutput> GetInvoiceAmountAsync(InvoicesQueryInput query);


        /// <summary>
        /// 批量打包发票发送邮箱
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task BatchSendEmailInvoices(BatchDownLoadInvoiceInput query);
        /// <summary>
        /// 获取应收开票金额合计
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<InvoiceCreditsSumOutput> GetInvoiceCreditsSumByInvoiceNo(InvoiceQueryInput query);

    }
}
