﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class InputBillQueryInputDeatil : BaseQuery
    {
        public Guid? InputBillId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }


        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }


        /// <summary>
        /// 入库类型
        /// </summary>
        public int StoreInType { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        public List<StoreInDetailOut>? StoreInDetail = new List<StoreInDetailOut>();

        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInItemCode { get; set; }
        /// <summary>
        /// 标记  true：入库；false：经销调出
        /// </summary>
        public bool? IsStoreIn = true;

        public string? barcode { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public long? produceDate { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int? PolyQuantity { get; set; }
         
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();
    }

}
