﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IAdvancePaymentAppService
    {
        Task<BaseResponseData<string>> AuditApproved(Guid id);
        Task<BaseResponseData<string>> UpdateStatus(Guid id, AdvancePaymentStatusEnum refuse);
    }
}
