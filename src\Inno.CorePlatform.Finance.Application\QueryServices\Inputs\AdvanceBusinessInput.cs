﻿using Inno.CorePlatform.Finance.Application.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class AdvanceBusinessInput: BaseQuery
    {
        public Guid? AdvanceBusinessApplyId { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public string? BillDateS { get; set; }

        public DateTime DateS 
        { 
            get 
            {
                if (!string.IsNullOrEmpty(BillDateS))
                {
                    return DateTimeHelper.LongToDateTime(Convert.ToInt64(BillDateS));
                }
                return DateTime.Now;
            }
        }

        /// <summary>
        /// 结束日期
        /// </summary>
        public string? BillDateE { get; set; }

        public DateTime DateE 
        {
            get
            {
                if (!string.IsNullOrEmpty(BillDateS))
                {
                    return DateTimeHelper.LongToDateTime(Convert.ToInt64(BillDateE));
                }
                return DateTime.Now;
            }
        }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        public Guid? UserId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
    }
}
