﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class SubscriptionPageResponse<T> where T : class
    {
        /// <summary>
        /// 数据
        /// </summary>
        public List<T>? List { get; set; }

        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 已撤销
        /// </summary>
        public int AuditingCount { get; set; }

        /// <summary>
        /// 已完成
        /// </summary>
        public int CompletedCount { get; set; }

        /// <summary>
        /// 审批中
        /// </summary>
        public int WaitExecuteCount { get; set; }

        /// <summary>
        /// 待提交
        /// </summary>
        public int WaitSubmitCount { get; set; }

        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
    }
}
