﻿using Dapr.Client;
using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.Enums;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.LogServices
{
    /// <summary>
    /// 系统日志服务实现类
    /// </summary>
    public class SubLogService : SimpleBaseAppService<SubLogService>, ISubLogService
    {
        private const string LogPrefix = "财务能力中心统一日志记录";
        private readonly FinanceDbContext _db;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="daprClient"></param>
        /// <param name="logger"></param>
        /// <param name="db"></param>
        public SubLogService(DaprClient daprClient, ILogger<SubLogService> logger, FinanceDbContext db) : base(daprClient, logger)
        {
            _db = db;
        }

        /// <summary>
        /// 通用记录日志实现方法
        /// 顺带记录日志到 Auzer 日志系统中
        /// </summary>
        /// <param name="actionName">方法名称</param>
        /// <param name="parametersJson">核心参数</param>
        /// <param name="operate">业务名称</param>
        /// <param name="logLevel">日志级别（默认Infomation）</param>
        /// <returns></returns>
        public async Task LogAsync(string actionName, string parametersJson, string operate = "", LogLevelEnum logLevel = LogLevelEnum.Information)
        {
            LogAzure(actionName, parametersJson, operate, logLevel);
            #if !DEBUG
            await base.SendBoardCast(DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = actionName, Content = parametersJson, Operate = operate });
            #endif
        }

        /// <summary>
        /// 仅记录日志到 Azure 日志系统
        /// </summary>
        /// <param name="actionName">方法名称</param>
        /// <param name="parametersJson">核心参数</param>
        /// <param name="operate">业务名称</param>
        /// <param name="logLevel">日志级别（默认Infomation）</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public void LogAzure(string actionName, string parametersJson, string operate = "", LogLevelEnum logLevel = LogLevelEnum.Information)
        {
            //接收到的广播统一记录Auzer日志
            LogToAzure(actionName, parametersJson, operate, logLevel);
        }

        /// <summary>
        /// 保存日志到数据库
        /// </summary>
        /// <param name="actionName"></param>
        /// <param name="parametersJson"></param>
        /// <param name="operate"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task SaveLogToDB(string? actionName, string? parametersJson, string? operate = "")
        {
            if (!string.IsNullOrEmpty(parametersJson))
            {
                await _db.SubLogs.AddAsync(new SubLogPo
                {
                    Id = Guid.NewGuid(),
                    Source = actionName,
                    Content = parametersJson,
                    Operate = operate,
                    CreatedTime = DateTime.UtcNow,
                    CreatedBy = "SystemLogService",
                    UpdatedTime = DateTime.UtcNow,
                    UpdatedBy = "SystemLogService"
                });
                await _db.SaveChangesAsync();
            }
            
        }

        private void LogToAzure(string actionName, string parametersJson, string operate, LogLevelEnum logLevel)
        {
            var logLevelMapping = new Dictionary<LogLevelEnum, Action<string, Exception?>>
            {
                [LogLevelEnum.Information] = (msg, _) => _logger.LogInformation(msg),
                [LogLevelEnum.Warning] = (msg, _) => _logger.LogWarning(msg),
                [LogLevelEnum.Error] = (msg, _) => _logger.LogError(msg)
            };

            var logMessage = $"{LogPrefix}, actionName:{actionName}, " +
                             $"parametersJson:{parametersJson}, operate:{operate}";

            var loggerAction = logLevelMapping.GetValueOrDefault(
                logLevel,
                (msg, _) => _logger.LogInformation(msg));

            loggerAction(logMessage, null);
        }

    }
}
