﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sginy;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using MathNet.Numerics.LinearAlgebra.Factorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class SginyApiClient : BaseDaprApiClient<SginyApiClient>, ISginyApiClient
    {
        public SginyApiClient(DaprClient daprClient, ILogger<SginyApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }


        public async Task<SginyInventoryCreateOutputDto> CreateSignyInventory(SginyInventoryCreateRequestDto request)
        {
            // 如果请求中包含用户信息，使用指定用户信息的方法
            if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserName))
            {
                return await InvokeMethodWithQueryObjectAndUserAsync<SginyInventoryCreateRequestDto, SginyInventoryCreateOutputDto>(
                    request, AppCenter.CreateSginyInventroy, request.UserId, request.UserName, RequestMethodEnum.POST);
            }
            else
            {
                return await InvokeMethodWithQueryObjectAsync<SginyInventoryCreateRequestDto, SginyInventoryCreateOutputDto>(
                    request, AppCenter.CreateSginyInventroy, RequestMethodEnum.POST);
            }
        }

        /// <summary>
        /// 跟台-财务获取盘点统计数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> FinanceSummary(ReconciliationInput input)
        {
            try
            {
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.FinanceSummary, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用跟台-财务获取盘点统计数据失败");
            }
        }

        public async Task<CheckDataWithKingdeeOutputDto> QueryStageSurgeryToKingdee(CheckDataWithKingdeeInputDto input)
        {
            return await InvokeMethodWithQueryObjectAsync<CheckDataWithKingdeeInputDto, CheckDataWithKingdeeOutputDto>(input, AppCenter.StageSurgeryToKingdee, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 跟台-根据id查询跟台手术数据信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<StageSurgeryOutput> StageSurgery_SelectById(StageSurgeryInput input)
        {
            try
            {
                return await InvokeMethodWithQueryObjectAsync<StageSurgeryInput, StageSurgeryOutput>(input, AppCenter.StageSurgery_SelectById, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用跟台-根据id查询跟台手术数据失败");
            }
        }

        protected override string GetAppId()
        {
            return AppCenter.Sginy_APPID;
        }
    }
}
