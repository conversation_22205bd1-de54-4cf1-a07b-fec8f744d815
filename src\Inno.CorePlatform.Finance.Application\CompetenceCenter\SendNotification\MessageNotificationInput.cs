﻿namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.SendNotification
{
    /// <summary>
    /// 发送消息通知入参
    /// </summary>
    public class MessageNotificationInput
    {
        // <summary>
        /// 来源 如：出库复核 或者 XX能力中心
        /// </summary>
        public string SourceModule { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string MsgContent { get; set; }

        /// <summary>
        /// 消息附件Ids 附件Id，多个逗号分隔
        /// </summary>
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 接收消息的用户
        /// </summary>
        public List<UserInput> lstToUser { get; set; } = new List<UserInput>();

        ///<summary>
        /// 是否要下发到子应用前端处理 默认false
        ///</summary>
        public bool? IsSendMsg { get; set; }
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInput
    {
        // <summary>
        /// 用户账号
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 用户真是姓名
        /// </summary>
        public string TrueName { get; set; }
    }
}
