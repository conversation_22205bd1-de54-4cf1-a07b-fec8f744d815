﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class StoreInDetailQueryOutput
    {

        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string StoreInItemCode { get; set; }
        /// <summary>
        /// 换货转退货单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        public double StoreInDate { get; set; }
        /// <summary>
        /// 换货转退货时间
        /// </summary>
        public double? BillDate { get; set; }
        /// <summary>
        /// 聚合入库数量
        /// </summary>
        public decimal PolyQuantity { get; set; }
        /// <summary>
        ///聚合已入票数量
        /// </summary>
        public decimal PolyInvoiceQuantity { get; set; }

        /// <summary>
        ///聚合未入票数量
        /// </summary>
        public decimal PolyNoInvoiceQuantity { get { return Math.Abs(PolyQuantity) - Math.Abs(PolyInvoiceQuantity); } }
        /// <summary>
        /// 本次入票数量
        /// </summary>
        public decimal ThisQuantity { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal? TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal? NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        //public List<StoreInDetailOut> StoreInDetail { get; set; } = new List<StoreInDetailOut>();

        /// <summary>
        /// 详情
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();

        /// <summary>
        /// 是否已经自动匹配
        /// </summary>
        public bool IsMatch { get; set; } = false;

        /// <summary>
        /// 勾稽的业务类型，1-经销购货入库，2-经销调出，3-寄售转购货，4-购货修订
        /// </summary>
        public int BusinessType { get; set; }
        public int rebate { get; set; } = 0;

        /// <summary>
        /// 已经使用数量
        /// </summary>
        public decimal UseQuantity { get; set; }
        /// <summary>
        /// 已经使用数量
        /// </summary>
        public decimal ShowUseQuantity
        {
            get
            {
                return Math.Abs(UseQuantity) > Math.Abs(PolyQuantity) ? PolyQuantity : UseQuantity;
                //return UseQuantity;
            }
        }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 进项票id
        /// </summary>
        public Guid? inputBillId { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }
    }

    public class StoreInDetailOut
    {
        /// <summary>
        /// 详情Id
        /// </summary>
        public string? StoreInDetailId { get; set; }

        /// <summary>
        /// 追溯id
        /// </summary>
        public string? SysBakId { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 已入票数量数量
        /// </summary>
        public int? InvoiceQuantity { get; set; }
    }
}
