﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Projects
{
    public class RuleConfigOutPut
    {

        public string Name { get; set; }
        /// <summary>
        /// 配置结果-折扣
        /// </summary>
        public DiscountData DiscountData { get; set; }

    }
    public class DiscountData
    {
        public string GroupId { get; set; }
        public string Name { get; set; }
        public List<ResultMeta> ResultMetas { get; set; } = new List<ResultMeta>();
    }
    public class ResultMeta
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Unit { get; set; }
        public string Value { get; set; }
    }

    public class ProjectInfo
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
    }

    public class ProjectOutput
    {
        public Guid? Id { get; set; }
        public string? Code { get; set; }
        public string? Name { get; set; }
    }


    public class GetProjectInfoByIdOrNameOut
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? Id { get; set; }
        
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// 项目编码
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public List<ProjectInfoCompany> Companies { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public ProjectInfoBusinessdept BusinessDept { get; set; }
    }

    public class ProjectInfoBusinessdept
    {
        public string businessDeptFullPath { get; set; }
        public string businessDeptFullName { get; set; }
        public string businessDeptId { get; set; }
    }

    public class ProjectInfoCompany
    {
        public string id { get; set; }
        public string name { get; set; }
    }

    public class ProjectAndBusinessDeptInfoOutput
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string BusinessDeptId { get; set; }
    }
}
