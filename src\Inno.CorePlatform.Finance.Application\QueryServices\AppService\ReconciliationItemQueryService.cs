﻿using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class ReconciliationItemQueryService : IReconciliationItemQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IPCApiClient _pCApiClient;
        public ReconciliationItemQueryService(FinanceDbContext db, IPCApiClient pCApiClient)
        {
            _db = db;
            _pCApiClient = pCApiClient;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationItemOutput>> GetListPages(ReconciliationItemInput input)
        {
            Expression<Func<ReconciliationItemPo, bool>> exp = z => 1 == 1;
            var strategyInput = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null && strategry.RowStrategies.Any())
            {
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    switch (key.ToLower())
                    {
                        case "company":
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                            break;
                    }

                }
            }
            if (!string.IsNullOrEmpty(input.CompanyName))
            {
                exp = exp.And(p => p.CompanyName.Contains(input.CompanyName));
            }
            var list = await _db.ReconciliationItem.Where(exp)
                .OrderByDescending(p => p.SysMonth).ThenBy(p => p.CompanyId)
                .Skip((input.page - 1) * input.limit).Take(input.limit).
                Select(z => new ReconciliationItemOutput
                {
                    CompanyId = z.CompanyId,
                    CompanyName = z.CompanyName,
                    SysMonth = z.SysMonth,
                    BillDate = z.BillDate,
                    CreatedBy = z.CreatedBy,
                    CreatedTime = z.CreatedTime,
                    Id = z.Id
                }).AsNoTracking().ToListAsync();
            return new PageResponse<ReconciliationItemOutput>() { List = list, Total = list.Count };

        }
    }
}
