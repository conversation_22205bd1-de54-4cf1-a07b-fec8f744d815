﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 不涉及财务数据的出库单据
    /// </summary>
    public interface IStoreOutWithoutFinanceAppService : IInventoryAppService
    {
        Task<BaseResponseData<int>> SignShipment(string tempStoreOutCodes);

        Task<BaseResponseData<HoldStockRemovalInput>> GetTempStoreOutKingdeeParams(string storeOutCode);
    }
}
