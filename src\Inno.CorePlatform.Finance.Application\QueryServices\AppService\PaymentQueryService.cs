﻿using Inno.CorePlatform.Common.Clients;
using Inno.CorePlatform.Common.Clients.Interfaces;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Migrations;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System.Linq;
using System.Linq.Expressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 付款单查询服务
    /// </summary>
    public class PaymentQueryService : QueryAppService, IPaymentQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IPCApiClient _pcApiClient;
        private readonly PortInterfaces.Clients.IBDSApiClient _bDSApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IConfiguration _configuration;
        private readonly ICoordinateClient _coordinateClient;
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// 构造
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        public PaymentQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db,
            PortInterfaces.Clients.IBDSApiClient bDSApiClient,
            IConfiguration configuration,
            ICoordinateClient coordinateClient,
            IPCApiClient pcApiClient,
            IUnitOfWork unitOfWork) : base(contextAccessor)
        {
            this._db = db;
            this._pcApiClient = pcApiClient;
            this._bDSApiClient = bDSApiClient;
            this._appServiceContextAccessor = contextAccessor;
            _configuration = configuration;
            _coordinateClient = coordinateClient;
            this._unitOfWork = unitOfWork;
        }

        /// <summary>
        /// 获取列表数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<PaymentQueryListOutput>, int)> GetListAsync(PaymentQueryInput input)
        {

            Expression<Func<PaymentPo, bool>> exp = z => 1 == 1;

            exp = await InitExp(input, exp);
            IQueryable<PaymentPo> baseQuery = _db.Payments.Where(exp).AsNoTracking();
            //var sql = baseQuery.ToQueryString();

            #region 排序
            if (input.sort != null && input.sort.Any())
            {
                for (int i = input.sort.Count - 1; i >= 0; i--)
                {
                    var ss = input.sort[i].Split(',');
                    if (ss.Length > 1 && ss[0] == "code")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.Code);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.Code); }
                    }
                    if (ss.Length > 1 && ss[0] == "billDate")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.BillDate);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.BillDate); }
                    }
                    if (ss.Length > 1 && ss[0] == "paymentDate")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.PaymentDate);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.PaymentDate); }
                    }
                    if (ss.Length > 1 && ss[0] == "value")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.Value);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.Value); }
                    }
                    if (ss.Length > 1 && ss[0] == "CoinName")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.CoinName);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.CoinName); }
                    }
                    if (ss.Length > 1 && ss[0] == "rmbAmount")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.RMBAmount);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.RMBAmount); }
                    }
                    if (ss.Length > 1 && ss[0] == "projectName")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.ProjectName);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.ProjectName); }
                    }
                    if (ss.Length > 1 && ss[0] == "paymentAutoItemCode")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.PaymentAutoItemCode);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.PaymentAutoItemCode); }
                    }
                }
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<PaymentQueryListOutput>()).ToListAsync();

            //获取已冲销金额
            await GetAndFillAbatedValue(list);

            return (list, count);
        }

        private async Task<Expression<Func<PaymentPo, bool>>> InitExp(PaymentQueryInput input, Expression<Func<PaymentPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }


            StrategyQueryOutput strategry = null;
            if (input.UserId != Guid.Empty)
            {
                var strategryq = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                strategry = await _pcApiClient.GetStrategyAsync(strategryq);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                }
            }

            if (strategry != null && strategry.RowStrategies.Any())
            {
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "company")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                        }
                    }
                    if (key.ToLower() == "project")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.Value));
                        }
                    }
                    if (key == "accountingDept")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToHashSet();
                            exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                        }
                    }
                }
            }
            if (user.Data.List.First().InstitutionType == 4)
            {
                if (user.Data.List.First().Institutions.Any())
                {
                    var serviceIds = user.Data.List.First().Institutions.Select(p => p.Id);
                    if (serviceIds != null)
                    {
                        exp = exp.And(t => serviceIds.Contains(t.ServiceId));
                    }
                }
                //if (input.BillDateStart == null && input.BillDateEnd == null)
                //{
                //    DateTime firstDayOfMonth = DateTime.Now.AddDays(1 - DateTime.Now.Day);
                //    DateTime lastDayOfNow = DateTime.Now.AddDays(1);
                //    exp = exp.And(z => (z.BillDate != null && z.BillDate >= firstDayOfMonth && z.BillDate <= lastDayOfNow));
                //}
                if (!string.IsNullOrEmpty(input.CreatedByName))
                {
                    var userRet = await _bDSApiClient.GetSmallUsersByDisplayNames(new List<string> { input.CreatedByName.Trim() });
                    var userNames = userRet.Select(t => t.Name).ToList();
                    if (userNames != null && userNames.Any())
                    {
                        exp = exp.And(z => userNames.ToHashSet().Contains(z.CreatedBy));
                    }
                    else
                    {
                        exp = exp.And(z => false);
                        return exp;
                    }
                }
            }
            #region 查询条件
            if (!string.IsNullOrWhiteSpace(input.searchKey))
            {
                exp = exp.And(z => EF.Functions.Like(z.ServiceName, $"%{input.searchKey}%")
                        || EF.Functions.Like(z.CompanyName, $"%{input.searchKey}%")
                        || EF.Functions.Like(z.PurchaseCode, $"%{input.searchKey}%")
                        || EF.Functions.Like(z.Code, $"%{input.searchKey}%")
                        || EF.Functions.Like(z.AgentName, $"%{input.searchKey}%")
                        || EF.Functions.Like(z.PurchaseContactNo, $"%{input.searchKey}%"));
            }
            if (input.BillDateS.HasValue && input.BillDateE.HasValue)
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(input.BillDateS.Value).AddHours(8);
                var endTime = dateTime.AddMilliseconds(input.BillDateE.Value).AddHours(8).AddDays(1);
                exp = exp.And(z => (z.BillDate >= startTime && z.BillDate < endTime));
            }
            if (input.PaymentDateS.HasValue && input.PaymentDateE.HasValue)
            {
                var dateTime = new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                var startTime = dateTime.AddMilliseconds(input.PaymentDateS.Value).AddHours(8);
                var endTime = dateTime.AddMilliseconds(input.PaymentDateE.Value).AddHours(8);
                exp = exp.And(z => (z.PaymentDate >= startTime && z.PaymentDate <= endTime));
            }
            if (input.ServiceId.HasValue)
            {
                exp = exp.And(z => z.ServiceId == input.ServiceId);
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(z => z.CompanyId == input.CompanyId);
            }


            if (!string.IsNullOrEmpty(input.department))
            {
                exp = exp.And(p => EF.Functions.Like(p.BusinessDeptFullPath, $"%{input.department}%"));
            }
            if (input.AgentId.HasValue)
            {
                exp = exp.And(z => z.AgentId == input.AgentId);
            }
            if (input.AbatedStatus.HasValue)
            {
                exp = exp.And(z => z.AbatedStatus == input.AbatedStatus);
            }
            if (input.Type.HasValue)
            {
                if ((int)input.Type != 999)
                {
                    exp = exp.And(z => z.Type == input.Type);
                }
                else
                {
                    exp = exp.And(z => z.Type == PaymentTypeEnum.Prepay && string.IsNullOrEmpty(z.PurchaseCode));
                }
            }
            if (input.PurchaseCodes != null && input.PurchaseCodes.Any())
            {
                exp = exp.And(z => input.PurchaseCodes.ToHashSet().Contains(z.PurchaseCode));
            }
            if (!string.IsNullOrEmpty(input.projectName))
            {
                exp = exp.And(z => z.ProjectName.Contains(input.projectName));
            }
            if (!string.IsNullOrWhiteSpace(input.PurchaseCode))
            {
                exp = exp.And(z => EF.Functions.Like(z.PurchaseCode, $"%{input.PurchaseCode}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.PurchaseContactNo))
            {
                exp = exp.And(z => EF.Functions.Like(z.PurchaseContactNo, $"%{input.PurchaseContactNo}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.ProducerOrderNo))
            {
                exp = exp.And(z => EF.Functions.Like(z.ProducerOrderNo, $"%{input.ProducerOrderNo}%"));
            }
            if (!string.IsNullOrWhiteSpace(input.Code))
            {
                exp = exp.And(z => EF.Functions.Like(z.Code, $"%{input.Code}%"));
            }
            if (input.Paied == 1)
            {
                exp = exp.And(z => !string.IsNullOrEmpty(z.Code));
            }
            if (input.Paied == 0)
            {
                exp = exp.And(z => string.IsNullOrEmpty(z.Code));
            }
            if (!string.IsNullOrEmpty(input.PaymentAutoItemCode))
            {
                exp = exp.And(z => EF.Functions.Like(z.PaymentAutoItemCode, $"%{input.PaymentAutoItemCode}%"));
            }
            #endregion
            return exp;
        }

        /// <summary>
        /// 付款单单列表查询Tab数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<PaymentQueryListTabOutput>> GetTabCount(PaymentQueryInput input)
        {
            StrategyQueryOutput strategry = null;
            if (input.UserId != Guid.Empty)
            {
                var strategryq = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                strategry = await _pcApiClient.GetStrategyAsync(strategryq);
            }
            var ret = BaseResponseData<PaymentQueryListTabOutput>.Success("操作成功");

            Expression<Func<PaymentPo, bool>> expAll = z => 1 == 1;
            Expression<Func<PaymentPo, bool>> expPaid = z => 1 == 1;
            Expression<Func<PaymentPo, bool>> expNonPaid = z => 1 == 1;

            var data = new PaymentQueryListTabOutput();
            #region 查询条件
            input.Paied = -1;
            expAll = await InitExp(input, expAll);
            data.AllCount = await _db.Payments.Where(expAll).CountAsync();
            input.Paied = 1;
            expPaid = await InitExp(input, expPaid);

            data.PaidCount = await _db.Payments.Where(expPaid).CountAsync();
            input.Paied = 0;
            expNonPaid = await InitExp(input, expNonPaid);

            data.NonPaidCount = await _db.Payments.Where(expNonPaid).CountAsync();
            #endregion


            ret.Data = data;
            return ret;
        }


        /// <summary>
        /// 获取填入冲销金额
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private async Task GetAndFillAbatedValue(List<PaymentQueryListOutput> list)
        {
            var codes = list.Select(c => c.Code).ToList();
            var debtType = "payment";
            var abatementPoList = await _db.Abatements.Where(c => c.DebtType == debtType && codes.Contains(c.DebtBillCode)).ToListAsync();
            if (abatementPoList.Any())
            {

                list.ForEach(c =>
                {
                    c.AbatedValue = abatementPoList.FindAll(a => a.DebtBillCode == c.Code || a.CreditBillCode == c.Code).Sum(c => c.Value);
                });
            }
        }

        /// <summary>
        /// 获取没有采购单号的付款单
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentQueryOutput>> GetPaymentOfNoPurchaseCode(PaymentQueryInputApi input)
        {
            var ret = new List<PaymentQueryOutput>();
            //付款单
            var payments = await _db.Payments.Where(p => string.IsNullOrEmpty(p.PurchaseCode)
                                                        && !string.IsNullOrEmpty(p.Code)
                                                        && p.Type == PaymentTypeEnum.Prepay
                                                        && p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                        && p.AgentId == input.AgentId
                                                        && (p.CompanyId.HasValue ? p.CompanyId == input.companyId : true)
                                                        && (p.ProjectId.HasValue ? p.ProjectId == input.projectId : true)
                                                        && p.CoinCode == input.CoinCode)
                                .OrderBy(p => Math.Abs(p.Value)).ToListAsync();
            if (payments != null && payments.Any())
            {
                var payCodes = payments.Select(p => p.Code);
                var abatements = await _db.Abatements.Where(p => p.DebtType == "payment" && payCodes.Contains(p.DebtBillCode)).ToListAsync();
                var refundDetails = await _db.RefundDetails.Where(p => payCodes.Contains(p.PaymentCode)).ToListAsync();
                var refundItems = new List<RefundItemPo>();
                if (refundDetails != null && refundDetails.Count > 0)
                {
                    var ids = refundDetails.Select(p => p.RefundItemId).ToList();
                    refundItems = await _db.RefundItem.Where(p => ids.Contains(p.Id)).ToListAsync();
                }
                foreach (var payment in payments)
                {
                    var abatement = abatements.Where(p => p.DebtBillCode == payment.Code).ToList();
                    if (abatement != null && abatement.Any())
                    {
                        payment.Value = payment.Value - abatement.Sum(p => p.Value);
                    }
                    var refundDetail = refundDetails.Where(p => p.PaymentCode == payment.Code).FirstOrDefault();
                    if (refundDetail != null)
                    {
                        if (refundItems.Where(p => p.Id == refundDetail.RefundItemId).FirstOrDefault().Status != RefundStatusEnum.waitSubmit)
                        {
                            ret.Add(payment.Adapt<PaymentQueryOutput>());
                        }
                    }
                    else
                    {
                        ret.Add(payment.Adapt<PaymentQueryOutput>());
                    }
                }
            }

            //负数应付
            var minusDebts = await _db.Debts.Where(p => p.Value < 0
                                                        && p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                        && p.DebtType != DebtTypeEnum.servicefee
                                                        && p.AgentId == input.AgentId
                                                        && (p.CompanyId.HasValue ? p.CompanyId == input.companyId : true)
                                                        && (p.ProjectId.HasValue ? p.ProjectId == input.projectId : true)
                                                        && p.CoinCode == input.CoinCode)
                .OrderBy(p => Math.Abs(p.Value)).ToListAsync();

            if (minusDebts != null && minusDebts.Any())
            {
                var debtCodes = minusDebts.Select(p => p.BillCode);
                var abatements = await _db.Abatements.Where(p => p.DebtType == "debt" && debtCodes.Contains(p.DebtBillCode)).ToListAsync();
                var debtPaymentUses = await _db.DebtPaymentUseDetails.Where(p => debtCodes.Contains(p.DebtCode)).ToListAsync();
                foreach (var debt in minusDebts)
                {
                    var debtPaymentUsesTemp = debtPaymentUses.Where(p => p.DebtCode == debt.BillCode).ToList();
                    if (debtPaymentUsesTemp != null && debtPaymentUsesTemp.Any())
                    {
                        debt.Value = Math.Abs(debt.Value) - Math.Abs(debtPaymentUsesTemp.Sum(p => p.UseAmount));
                        debt.Value = debt.Value < 0 ? 0 : debt.Value;
                    }

                    var abatement = abatements.Where(p => p.DebtBillCode == debt.BillCode).ToList();
                    if (abatement != null && abatement.Any())
                    {
                        debt.Value = Math.Abs(debt.Value) - Math.Abs(abatement.Sum(p => p.Value));
                        debt.Value = debt.Value < 0 ? 0 : debt.Value;
                    }


                    if (debt.Value != 0)
                    {
                        ret.Add(new PaymentQueryOutput
                        {
                            Code = debt.BillCode,
                            Id = debt.Id,
                            Value = Math.Abs(debt.Value),
                            Classify = 2
                        });
                    }
                }
            }

            //服务费应收
            var agent = await _bDSApiClient.GetAgentInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { id = input.AgentId.ToString() });
            if (agent != null && agent.Any() && input.CoinCode == "CNY")
            {
                var serverCredits = await _db.Credits.Where(p => p.Value > 0
                                                            && p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                            && p.CreditType == CreditTypeEnum.servicefee
                                                            && p.LatestUniCode == agent[0].socialCode
                                                            && p.InvoiceStatus == InvoiceStatusEnum.invoiced
                                                            && (p.CompanyId.HasValue ? p.CompanyId == input.companyId : true))
                                    .OrderBy(p => Math.Abs(p.Value)).ToListAsync();



                if (serverCredits != null && serverCredits.Any())
                {
                    var creditsCodes = serverCredits.Select(p => p.BillCode);
                    var abatements = await _db.Abatements.Where(p => creditsCodes.Contains(p.DebtBillCode) || creditsCodes.Contains(p.CreditBillCode)).ToListAsync();
                    var creditPaymentUses = await _db.DebtPaymentUseDetails.Where(p => creditsCodes.Contains(p.DebtCode)).ToListAsync();
                    foreach (var credit in serverCredits)
                    {
                        var creditPaymentUsesTemp = creditPaymentUses.Where(p => p.DebtCode == credit.BillCode).ToList();
                        if (creditPaymentUsesTemp != null && creditPaymentUsesTemp.Any())
                        {
                            credit.Value = Math.Abs(credit.Value) - Math.Abs(creditPaymentUsesTemp.Sum(p => p.UseAmount));
                            credit.Value = credit.Value < 0 ? 0 : credit.Value;
                        }

                        var abatement = abatements.Where(p => p.DebtBillCode == credit.BillCode || p.CreditBillCode == credit.BillCode).ToList();
                        if (abatement != null && abatement.Any())
                        {
                            credit.Value = Math.Abs(credit.Value) - Math.Abs(abatement.Sum(p => p.Value));
                            credit.Value = credit.Value < 0 ? 0 : credit.Value;
                        }

                        if (credit.Value != 0)
                        {
                            ret.Add(new PaymentQueryOutput
                            {
                                Code = credit.BillCode,
                                Id = credit.Id,
                                Value = Math.Abs(credit.Value),
                                Classify = 3
                            });
                        }
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 更据采购单号获取现金支付可使用总额度
        /// </summary>
        /// <param name="PurchaseCodes"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<PaymentLimitOutput>>> GetNonUseTotalValue(List<string> PurchaseCodes)
        {
            var ret = BaseResponseData<List<PaymentLimitOutput>>.Success("操作成功");
            if (PurchaseCodes != null && PurchaseCodes.Any())
            {
                var payments = await _db.Payments.Where(p => PurchaseCodes.ToHashSet().Contains(p.PurchaseCode)
                                                         && !string.IsNullOrEmpty(p.Code)
                                                         && p.Type == PaymentTypeEnum.Prepay
                                                         && p.AdvancePayMode != AdvancePayModeEnum.UseQuota
                                                         && p.AbatedStatus == AbatedStatusEnum.NonAbate).ToListAsync();
                if (payments != null && payments.Any())
                {
                    var paymentCodes = payments.Select(p => p.Code).ToList();
                    var debtDetails = await _db.DebtDetailExcutes.Where(p => paymentCodes.Contains(p.PaymentCode)).ToListAsync();
                    var retData = new List<PaymentLimitOutput>();
                    foreach (var code in PurchaseCodes)
                    {
                        var payments_temp = payments.Where(p => p.PurchaseCode == code);
                        retData.Add(new PaymentLimitOutput
                        {
                            PurchaseCode = code,
                            AbatementTotalValue = debtDetails.Where(p => payments_temp.Select(p => p.Code).Contains(p.PaymentCode)).Sum(p => p.Value),
                            PaymentTotalValue = payments_temp.Sum(p => p.Value),
                        });
                    }
                    ret.Data = retData;
                }
            }
            return ret;
        }

        /// <summary>
        /// 根据采购订单获取付款单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<PaymentQueryOutput>> GetByPurchaseCodes(PaymentWebApiInput input)
        {
            if (input == null)
                throw new ArgumentException("参数异常");
            if (input.PurchaseCodes == null)
                throw new ArgumentException("参数异常");

            var query = _db.Payments.Where(p => (input.PurchaseCodes != null && input.PurchaseCodes.ToHashSet().Contains(p.PurchaseCode)));
            if (input.PaymentType != null)
            {
                query = query.Where(p => p.Type == input.PaymentType);
            }
            if (input.IsPaid != null)
            {
                query = query.Where(p => input.IsPaid.Value ? !string.IsNullOrEmpty(p.Code) : string.IsNullOrEmpty(p.Code));
            }
            var payments = await query.ToListAsync();
            return payments.Adapt<List<PaymentQueryOutput>>();
        }
        /// <summary>
        /// 获取列表数据 (通过采购单号)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<PaymentQueryListOutput>, int)> GetListByPurchaseCodeAsync(PaymentQueryInput input)
        {
            //通过采购单号， 获取应付计划中的付款单号
            List<string> pymentCodes = new List<string>();
            var debtDetailList = await _db.DebtDetails.Include(z => z.DebtDetailExcutes).Where(c => c.PurchaseCode == input.PurchaseCode).AsNoTracking().ToListAsync();
            if (debtDetailList != null && debtDetailList.Any())
            {
                foreach (var debtDetail in debtDetailList)
                {
                    if (debtDetail.DebtDetailExcutes != null && debtDetail.DebtDetailExcutes.Any())
                    {
                        foreach (var debtDetailExcute in debtDetail.DebtDetailExcutes)
                        {
                            if (debtDetailExcute != null && debtDetailExcute.PaymentCode != null)
                            {
                                pymentCodes.Add(debtDetailExcute.PaymentCode);
                            }
                        }
                    }
                }
            }

            //通过付款单号 或采购单号，获取付款单信息
            Expression<Func<PaymentPo, bool>> exp = z => 1 == 1;
            exp = exp.And(z => z.Code != null && (pymentCodes.Contains(z.Code) || input.PurchaseCode == z.PurchaseCode));
            if (input.ProjectId.HasValue)
            {
                exp = exp.And(z => z.ProjectId == input.ProjectId);
            }
            IQueryable<PaymentPo> baseQuery = _db.Payments.Where(exp).AsNoTracking();

            #region 排序
            baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<PaymentQueryListOutput>()).ToListAsync();

            //获取已冲销金额
            await GetAndFillAbatedValue(list);
            return (list, count);

        }

        /// <summary>
        /// 根据付款单号获取付款单信息
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public async Task<List<PaymentQueryOutput>> GetByCodes(List<string> codes)
        {
            if (codes == null)
                throw new ArgumentException("参数异常");

            var query = _db.Payments.Where(p => codes.Contains(p.Code));

            var payments = await query.ToListAsync();
            return payments.Adapt<List<PaymentQueryOutput>>();
        }

        /// <summary>
        /// 根据批量付款单号获取终端医院
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public async Task<List<HospitalExcelOutput>> GetHospitalsByPaymentAutoItemCodes(List<string?>? codes)
        {
            var list = await (from pai in _db.PaymentAutoItems
                              join p in _db.Payments on pai.Code equals p.PaymentAutoItemCode
                              join pad in _db.PaymentAutoDetails on pai.Id equals pad.PaymentAutoItemId
                              join dd in _db.DebtDetails on pad.DebtDetilId equals dd.Id
                              join d in _db.Debts on dd.DebtId equals d.Id
                              join c in _db.Credits on dd.CreditId equals c.Id
                              where codes != null ? codes.Contains(p.PaymentAutoItemCode) : 1 != 1
                              select new HospitalExcelOutput
                              {
                                  PaymentAutoItemCode = p.PaymentAutoItemCode,
                                  HospitalName = c.HospitalName,
                                  AgentId = p.AgentId,
                                  Code = p.Code
                              }).ToListAsync();
            return list;
        }

        /// <summary>
        /// 异步导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(PaymentQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_paymentExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("付款单清单导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateClient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 异步导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<PaymentQueryListOutput>, int)> DownloadPayment(PaymentQueryInput input)
        {
            var (list, count) = await GetListAsync(input);
            #region 获取医院数据
            var paymentAutoItemCodes = list.Select(x => x.PaymentAutoItemCode).ToList();
            var hospitals = await GetHospitalsByPaymentAutoItemCodes(paymentAutoItemCodes);
            #endregion
            foreach (var item in list)
            {
                // 终端医院字符串
                string hosStr = string.Empty;
                var hoslists = hospitals.Where(x => x.PaymentAutoItemCode == item.PaymentAutoItemCode && x.AgentId == item.AgentId && x.Code == item.Code).ToList();
                if (hoslists.Any())
                {
                    hosStr = string.Join(",", hoslists.Where(x => !string.IsNullOrEmpty(x.HospitalName)).Select(x => x.HospitalName).ToList());
                }
            }
            return (list, count);
        }

        /// <summary>
        /// 获取付款详情
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentDetailOutput>> GetPaymentPlan(QueryById input)
        {
            if (string.IsNullOrEmpty(input.PaymentCode))
            {
                return new List<PaymentDetailOutput>();
            }

            var query = from d in _db.PaymentAutoDetails
                        join debtdetail in _db.DebtDetails on d.DebtDetilId equals debtdetail.Id into tempdetail
                        from t in tempdetail.DefaultIfEmpty()
                        join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                        from e in tempdebt.DefaultIfEmpty()
                        join crt in _db.Credits on t.CreditId equals crt.Id into crtdebtdetailtemp
                        from crtdebtdetail in crtdebtdetailtemp.DefaultIfEmpty()
                        where d.PaymentCode == input.PaymentCode
                        select new PaymentDetailOutput
                        {
                            Id = d.Id,
                            DebtDetailId = t.Id,
                            AccountPeriodType = t.AccountPeriodType,
                            ServiceName = e.ServiceName,
                            ServiceId = e.ServiceId,
                            AgentId = e.AgentId,
                            AgentName = e.AgentName,
                            CreditBillCode = crtdebtdetail == null ? "" : crtdebtdetail.BillCode,
                            CreditAbatedValue = crtdebtdetail == null ? 0 : crtdebtdetail.Value, //应收金额——应收冲销金额
                            DebtBillCode = e.BillCode,
                            RelateCode = e.RelateCode,
                            DebtAbatedValue = 0,//应付冲销金额——计算
                            DebtDetailValue = t.Value,
                            DebtValue = e.Value,
                            DebtBalance = e.Value - t.Value,
                            CoinName = e.CoinName,
                            Discount = t.CostDiscount,
                            HospitalName = crtdebtdetail == null ? "" : crtdebtdetail.HospitalName,
                            OriginValue = t.OriginValue,
                            ReceiveCode = t.ReceiveCode,
                            CustomerName = crtdebtdetail == null ? "" : crtdebtdetail.CustomerName,
                            ProducerOrderNo = e.ProducerOrderNo,
                            PurchaseContactNo = e.PurchaseContactNo,
                            PaymentCode = d.PaymentCode,
                            OrderNo = t.OrderNo,
                            ProbablyPayTime = t.ProbablyPayTime,
                            CreditId = crtdebtdetail == null ? Guid.Empty : crtdebtdetail.Id,
                            PurchaseCode = e.PurchaseCode,
                            LimitedDiscount = d.LimitedDiscount,
                            OriginOrderNo = e.OriginOrderNo,
                            //InvoiceNo = (from it in _db.InvoiceCredits where it.CreditId == crtdebtdetail.Id select it.InvoiceNo).First(),
                        };

            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                return new List<PaymentDetailOutput>();
            }
            //if (user.Data.List.First().InstitutionType == 2)
            //{

            //}

            StrategyQueryOutput strategry = null;
            if (input.userId != Guid.Empty)
            {
                var strategryq = new StrategyQueryInput() { userId = input.userId, functionUri = "metadata://fam" };
                strategry = await _pcApiClient.GetStrategyAsync(strategryq);
            }

            if (strategry != null && strategry.RowStrategies.Any())
            {
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "service")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                            query = query.Where(t => strategList.Contains(t.ServiceId.Value));
                        }
                    }
                }
            }

            var result = await query.AsNoTracking().ToListAsync();
            //获取已冲销金额
            var debtCodes = result.Select(p => p.DebtBillCode).ToList();
            var abatments = await _db.Abatements.Where(p => debtCodes.Contains(p.DebtBillCode) || debtCodes.Contains(p.CreditBillCode))
                                     .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();

            //获取进项票金额
            var relateCodes = result.Select(p => p.RelateCode).ToList();
            var noTaxAmountList = await _db.InputBillSubmitDetails.Where(p => (debtCodes.Contains(p.StoreInItemCode) || relateCodes.Contains(p.StoreInItemCode)) && p.InputBill.Status == 2).Include(x => x.InputBill).AsNoTracking().ToListAsync();
            var creditIds = result.Select(p => p.CreditId).ToList();
            var credits = await _db.Credits.Where(x => creditIds.Contains(x.Id)).ToListAsync();
            var creditBillCodes = credits.Select(x => x.BillCode).ToList();
            var invoices = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value)).AsNoTracking().ToListAsync();
            //查询发票相关信息
            var invoiceNos = invoices.Select(x => x.InvoiceNo).ToList();
            var invoiceInfos = await _db.InvoiceCredits.Where(x => invoiceNos.Contains(x.InvoiceNo)).AsNoTracking().ToListAsync();
            //查询发票对应应收冲销信息
            var abas = await _db.Abatements.Where(p => creditBillCodes.Contains(p.DebtBillCode) || creditBillCodes.Contains(p.CreditBillCode))
                                     .Select(p => p.Adapt<AbatmentDTO>()).ToListAsync();
            //查询收款信息
            var receiveCodes = result.Select(p => p.ReceiveCode).ToList();
            var rris = await _db.RecognizeReceiveItems.Where(x => receiveCodes.Contains(x.ReceiveCode)).ToListAsync();
            var rrds = await _db.RecognizeReceiveDetails.Where(x => invoiceNos.Contains(x.Code)).ToListAsync();
            var orderNos = result.Select(p => p.OrderNo).ToList();
            var rrdByOrders = await _db.RecognizeReceiveDetails.Where(x => orderNos.Contains(x.Code)).ToListAsync();
            foreach (var item in result)
            {
                var recognizeDate = string.Empty;  //收款认领时间
                var rri = rris.Where(x => x.ReceiveCode == item.ReceiveCode).ToList();
                if (rri != null && rri.Any())
                {
                    item.ReceiveDate = rri[0].ReceiveDate;
                    item.ReceiveAmount = rri[0].ReceiveValue;
                    item.Payer = rri[0].CustomerNme;
                }
                var invoice = invoices.FirstOrDefault(p => p.CreditId == item.CreditId);
                item.InvoiceNo = invoice == null ? "" : string.Join(",", invoices.Where(x => x.CreditId == item.CreditId).Select(x => x.InvoiceNo).ToList());
                item.DebtAbatedValue = abatments.Where(t => t.DebtBillCode == item.DebtBillCode || t.CreditBillCode == item.DebtBillCode).Sum(t => t.Value);
                item.DebtBalance = Math.Abs(item.DebtValue ?? 0) - item.DebtAbatedValue;
                item.InvoiceTime = invoice == null ? "" : invoice.InvoiceTime.Value.ToString("yyyy-MM-dd");
                item.NoTaxAmount = noTaxAmountList.Where(p => p.StoreInItemCode == item.DebtBillCode || p.StoreInItemCode == item.RelateCode).Sum(p => p.NoTaxAmount);
                var list = invoices.Where(x => x.CreditId == item.CreditId).ToList();
                var invoiceInfoLst = new List<InvoiceInfo>();
                foreach (var j in list)
                {
                    var currentInvoice = invoiceInfos.FirstOrDefault(x => x.InvoiceNo == j.InvoiceNo);
                    var currentCreditIds = invoiceInfos.Where(x => x.InvoiceNo == j.InvoiceNo).Select(x => x.CreditId).ToList();
                    var currentCredits = credits.Where(x => currentCreditIds.Contains(x.Id)).ToList();
                    var currentCreditBillCodes = currentCredits.Select(x => x.BillCode).ToList();
                    var currentAbas = abas.Where(p => currentCreditBillCodes.Contains(p.DebtBillCode) || currentCreditBillCodes.Contains(p.CreditBillCode)).ToList();
                    var currentRrds = rrds.Where(x => x.Code == j.InvoiceNo).ToList();
                    if (!string.IsNullOrEmpty(item.InvoiceNo))
                    {
                        System.DateTime? time = currentRrds != null && currentRrds.Any() ? currentRrds[0].RecognizeDate : null;
                        if (time.HasValue)
                        {
                            recognizeDate = time.Value.ToString("yyyy-MM-dd");
                        }
                    }
                    else
                    {
                        var currentRrdByOrders = rrdByOrders.Where(x => x.Code == item.OrderNo).ToList();
                        System.DateTime? time = currentRrdByOrders != null && currentRrdByOrders.Any() ? currentRrdByOrders[0].RecognizeDate : null;
                        if (time.HasValue)
                        {
                            recognizeDate = time.Value.ToString("yyyy-MM-dd");
                        }
                    }
                    invoiceInfoLst.Add(new InvoiceInfo
                    {
                        InvoiceNo = j.InvoiceNo,
                        InvoiceAmount = currentInvoice != null ? currentInvoice.InvoiceAmount : 0,
                        InvoiceTime = j.InvoiceTime,
                        WriteOffAmount = currentRrds.Sum(x => x.Value),
                        RecognizeDate = recognizeDate,
                        Abtdate = currentAbas != null && currentAbas.Any() ? currentAbas[0].Abtdate : null,
                        CreditValueLst = currentCredits.Select(x => x.Value).ToList()
                    });
                }
                item.InvoiceInfoLst = invoiceInfoLst;
            }
            return result;
        }

        /// <summary>
        /// 异步导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportPaymentPlan(QueryById query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"].ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.userId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_paymentPlanExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("付款计划导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var jsonStr = JsonConvert.SerializeObject(tasks);
                var result = await _coordinateClient.AddTasksAsync(tasks);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<List<ExportTaskResDto>>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        private Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }

        /// <summary>
        /// 退款获取游离的付款单
        /// </summary>
        /// <returns></returns>
        public async Task<List<PaymentQueryOutput>> RefundApplyGetPayment(Guid agentId, Guid companyId, string projectCode)
        {
            var ret = new List<PaymentQueryOutput>();
            //付款单
            var payments = await _db.Payments.Where(p => string.IsNullOrEmpty(p.PurchaseCode)
                                                        && !string.IsNullOrEmpty(p.Code)
                                                        && p.Type == PaymentTypeEnum.Prepay
                                                        && p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                        && p.AgentId == agentId
                                                        && (p.CompanyId.HasValue ? p.CompanyId == companyId : true)
                                                         && p.ProjectCode == projectCode
                                                     )
                                .OrderBy(p => Math.Abs(p.Value)).AsNoTracking().ToListAsync();
            if (payments != null && payments.Any())
            {
                var payCodes = payments.Select(p => p.Code);
                var abatements = await _db.Abatements.Where(p => p.DebtType == "payment" && payCodes.Contains(p.DebtBillCode)).ToListAsync();
                foreach (var payment in payments)
                {
                    var abatement = abatements.Where(p => p.DebtBillCode == payment.Code).ToList();
                    if (abatement != null && abatement.Any())
                    {
                        payment.Value = payment.Value - abatement.Sum(p => p.Value);
                    }
                    if (payment.Value > 0)
                    {
                        ret.Add(payment.Adapt<PaymentQueryOutput>());
                    }
                }
            }
            return ret;
        }
        /// <summary>
        /// 获取游离的付款单通过code
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public async Task<List<PaymentQueryOutput>> RefundApplyGetPaymentByCodes(List<string> codes)
        {
            var ret = new List<PaymentQueryOutput>();
            //付款单
            var payments = await _db.Payments.Where(p => string.IsNullOrEmpty(p.PurchaseCode)
                                                        && !string.IsNullOrEmpty(p.Code)
                                                        && codes.Contains(p.Code)
                                                     )
                                .OrderBy(p => Math.Abs(p.Value)).ToListAsync();
            if (payments != null && payments.Any())
            {
                var payCodes = payments.Select(p => p.Code);
                var abatements = await _db.Abatements.Where(p => p.DebtType == "payment" && payCodes.Contains(p.DebtBillCode)).ToListAsync();
                foreach (var payment in payments)
                {
                    var abatement = abatements.Where(p => p.DebtBillCode == payment.Code).ToList();
                    if (abatement != null && abatement.Any())
                    {
                        payment.Value = payment.Value - abatement.Sum(p => p.Value);
                    }
                }
                var paymentQueryOutputs = payments.Adapt<List<PaymentQueryOutput>>();
                ret.AddRange(paymentQueryOutputs);
            }
            return ret;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<Payment> GetById(Guid id)
        {
            var ret = await _db.Payments.FirstOrDefaultAsync(p => p.Id == id);
            return ret.Adapt<Payment>();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<bool>> DeleteAttachFileIds(AddCashDiscountFileInput input)
        {
            var ret = BaseResponseData<bool>.Success("操作成功！");
            var payment = await _db.Payments.AsNoTracking().FirstOrDefaultAsync(c => c.Id == input.Id);

            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(payment.AttachFileIds))
            {
                foreach (var fildId in payment.AttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.AttachFileId.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            payment.AttachFileIds = newAttachFileIds;
            _db.Payments.Update(payment);
            await _unitOfWork.CommitAsync();
            return ret;
        }

        /// <summary>
        /// 通过采购单号和项目Id获取付款单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<PaymentByPurchaseAndProjectOutput>> GetPaymentByPurchaseAndProject(PaymentByPurchaseAndProjectInput input)
        {
            var result = new List<PaymentByPurchaseAndProjectOutput>();

            // 数据源1：从PaymentAutoDetail、DebtDetail、Debt表获取数据，并关联Payment表获取类型
            var query1 = from pad in _db.PaymentAutoDetails
                         join dd in _db.DebtDetails on pad.DebtDetilId equals dd.Id
                         join d in _db.Debts on dd.DebtId equals d.Id
                         join p in _db.Payments on pad.PaymentCode equals p.Code into paymentGroup
                         from payment in paymentGroup.DefaultIfEmpty()
                         where dd.PurchaseCode == input.PurchaseCode && d.ProjectId == input.ProjectId
                         select new PaymentByPurchaseAndProjectOutput
                         {
                             PaymentCode = pad.PaymentCode,
                             PayerCompanyName = d.CompanyName,
                             PayeeAgentName = d.AgentName,
                             PaymentType = payment != null ? payment.Type : PaymentTypeEnum.Normal, // 从Payment表获取实际类型，默认为正常付款
                             ActualPaymentDate = pad.PaymentDate,
                             CoinCode = payment.CoinCode,
                             CoinName = payment.CoinName,
                             CoinAmount = pad.Value,
                             RMBAmount = payment.CoinCode == "CNY" ? pad.Value : (payment != null && payment.Value != 0 ? pad.Value * (payment.RMBAmount.Value / payment.Value) : 0),
                             Balance = pad.Value, // 先设置为原始金额，后续计算冲销
                             PurchaseCode = dd.PurchaseCode,
                             ProducerOrderNo = d.ProducerOrderNo
                         };

            var data1 = await query1.AsNoTracking().ToListAsync();
            result.AddRange(data1);

            // 数据源2：从Payment表直接获取数据
            var query2 = from p in _db.Payments
                         where p.PurchaseCode == input.PurchaseCode && p.ProjectId == input.ProjectId
                         select new PaymentByPurchaseAndProjectOutput
                         {
                             PaymentCode = p.Code,
                             PayerCompanyName = p.CompanyName,
                             PayeeAgentName = p.AgentName,
                             PaymentType = p.Type, // 使用实际的付款单类型
                             ActualPaymentDate = p.PaymentDate,
                             CoinCode = p.CoinCode,
                             CoinName = p.CoinName,
                             CoinAmount = p.Value,
                             RMBAmount = p.RMBAmount ?? 0,
                             Balance = p.Value, // 先设置为原始金额，后续计算冲销
                             PurchaseCode = p.PurchaseCode,
                             ProducerOrderNo = p.ProducerOrderNo
                         };

            var data2 = await query2.AsNoTracking().ToListAsync();
            result.AddRange(data2);

            // 去重处理（基于付款单号）
            result = result.GroupBy(x => x.PaymentCode)
                          .Select(g => g.First())
                          .ToList();

            // 计算余额（原始金额 - 冲销金额）
            if (result.Any())
            {
                var paymentCodes = result.Where(x => !string.IsNullOrEmpty(x.PaymentCode))
                                        .Select(x => x.PaymentCode).ToList();

                // 获取冲销金额，关联debt表并增加项目条件
                var abatements = await (from a in _db.Abatements
                                        join d in _db.Debts on a.CreditBillCode equals d.BillCode into debtGroup
                                        from debt in debtGroup.DefaultIfEmpty()
                                        where paymentCodes.Contains(a.DebtBillCode)
                                              && (debt == null || debt.ProjectId == input.ProjectId)
                                        select a)
                                       .AsNoTracking()
                                       .ToListAsync();

                // 更新余额
                foreach (var item in result)
                {
                    if (!string.IsNullOrEmpty(item.PaymentCode))
                    {
                        var abatedAmount = abatements
                            .Where(a => a.DebtBillCode == item.PaymentCode || a.CreditBillCode == item.PaymentCode)
                            .Sum(a => a.Value);

                        item.Balance = item.CoinAmount - abatedAmount;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 导出付款单付款信息（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<ExportTaskResDto>> ExportPaymentInfoAsync(PaymentQueryInput query)
        {
            try
            {
                var tasks = new List<ExportTaskInputDto>();
                var task = new ExportTaskInputDto();
                task.ExportTaskId = string.Empty;
                task.AppId = _configuration["coordinateAppId"]!.ToString();
                // 查询入参转换为字典
                var queryDic = DtoToDictionary(query);
                var headerDic = new Dictionary<string, object>
                {
                    { "X-Inno-UserId", query.UserId },
                    { "X-Inno-UserName", query.CurrentUserName??=string.Empty }
                };
                //模板已配置，可不填
                task.QueryApiUri = string.Empty;
                task.HeaderInfo = headerDic;
                task.QueryParam = queryDic;
                task.TemplateCode = "fam_paymentInfoExportTemplate";
                var sendUsers = new List<string>
                {
                    (query.CurrentUserName ??= string.Empty)
                };
                task.sendUsers = sendUsers;
                task.BatchNo = Guid.NewGuid().ToString();
                task.FileName = string.Concat("付款单付款信息导出", DateTime.Now.ToString("yyyyMMddHHmmss"));
                task.IgnoreColumns = string.Empty;
                tasks.Add(task);
                var tasksResult = await _coordinateClient.AddTasksAsync(tasks);
                return new BaseResponseData<ExportTaskResDto>()
                {
                    Data = tasksResult.Data?.FirstOrDefault(),
                    Code = tasksResult.Code,
                    Message = tasksResult.Message
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<ExportTaskResDto>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 导出付款单付款信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<List<PaymentPayInfoListOutput>>> DownloadPaymentInfoAsync(PaymentQueryInput input)
        {
            StrategyQueryOutput? strategry = null;
            if (input.UserId != Guid.Empty)
            {
                var strategryq = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                strategry = await _pcApiClient.GetStrategyAsync(strategryq);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                    {
                        return new BaseResponseData<List<PaymentPayInfoListOutput>>()
                        {
                            Total = 0,
                            Data = new List<PaymentPayInfoListOutput>(),
                            Code = CodeStatusEnum.Success,
                        };
                    }
                }
            }

            HashSet<string>? companyIds = null;
            HashSet<string>? projectIds = null;
            HashSet<string>? businessDeptIds = null;
            HashSet<string>? serviceIds = null;
            if (strategry != null && strategry.RowStrategies.Any())
            {
                foreach (var key in strategry.RowStrategies.Keys)
                {
                    if (key.ToLower() == "company")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            companyIds = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                        }
                    }
                    if (key.ToLower() == "project")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            projectIds = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                        }
                    }
                    if (key == "accountingDept")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            businessDeptIds = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                        }
                    }
                    if (key.ToLower() == "service")
                    {
                        if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                        {
                            serviceIds = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToHashSet();
                        }
                    }
                }
            }

            var query = from p in _db.Payments
                        join pad in _db.PaymentAutoDetails.Where(x => x.PaymentCode != null && x.PaymentCode != "")  on p.Code equals pad.PaymentCode into padi
                        from padd in padi.DefaultIfEmpty()
                        join debtdetail in _db.DebtDetails on padd.DebtDetilId equals debtdetail.Id into tempdetail
                        from t in tempdetail.DefaultIfEmpty()
                        join debt in _db.Debts on t.DebtId equals debt.Id into tempdebt
                        from e in tempdebt.DefaultIfEmpty()
                        join crt in _db.Credits on t.CreditId equals crt.Id into crtdebtdetailtemp
                        from crtdebtdetail in crtdebtdetailtemp.DefaultIfEmpty()
                        where 
                                  (companyIds != null && companyIds.Any() ? p.CompanyId.HasValue && companyIds.Contains(p.CompanyId.Value.ToString()) : true)
                              && (projectIds != null && projectIds.Any() ? p.ProjectId.HasValue && projectIds.Contains(p.ProjectId.Value.ToString()) : true)
                              && (businessDeptIds != null && businessDeptIds.Any() ? !string.IsNullOrEmpty(p.BusinessDeptId) && businessDeptIds.Contains(p.BusinessDeptId) : true)
                              && (!string.IsNullOrWhiteSpace(input.searchKey) ? p.ServiceName.Contains(input.searchKey)
                                  || p.Code.Contains(input.searchKey)
                                  || p.CompanyName.Contains(input.searchKey)
                                  || p.PurchaseCode.Contains(input.searchKey)
                                  || p.AgentName.Contains(input.searchKey)
                                  || p.PurchaseContactNo.Contains(input.searchKey) : true)
                              && (input.BillDateS.HasValue && input.BillDateE.HasValue ?
                                  (p.BillDate >= new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(input.BillDateS.Value).AddHours(8) && p.BillDate <= new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(input.BillDateE.Value).AddHours(32)) : true)
                              && (input.PaymentDateS.HasValue && input.PaymentDateE.HasValue ?
                                  (p.PaymentDate >= new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(input.PaymentDateS.Value).AddHours(8) && p.PaymentDate <= new System.DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(input.PaymentDateE.Value).AddHours(32)) : true)
                              && (input.ServiceId.HasValue ? p.ServiceId == input.ServiceId : true)
                              && (input.CompanyId.HasValue ? p.CompanyId == input.CompanyId : true)
                              && (!string.IsNullOrEmpty(input.department) ? !string.IsNullOrEmpty(p.BusinessDeptFullPath) && p.BusinessDeptFullPath.Contains(input.department) : true)
                              && (input.AgentId.HasValue ? p.AgentId == input.AgentId : true)
                              && (input.AbatedStatus.HasValue ? p.AbatedStatus == input.AbatedStatus : true)
                              && (input.Type.HasValue && (int)input.Type.Value != 999 ? p.Type == input.Type : true)
                              && (input.Type.HasValue && (int)input.Type.Value == 999 ? p.Type == PaymentTypeEnum.Prepay && string.IsNullOrEmpty(p.PurchaseCode) : true)
                              && (input.PurchaseCodes != null && input.PurchaseCodes.Any() ? !string.IsNullOrEmpty(p.PurchaseCode) && input.PurchaseCodes.ToHashSet().Contains(p.PurchaseCode) : true)
                              && (!string.IsNullOrEmpty(input.projectName) ? !string.IsNullOrEmpty(p.ProjectName) && p.ProjectName.Contains(input.projectName) : true)
                              && (!string.IsNullOrEmpty(input.PurchaseCode) ? !string.IsNullOrEmpty(p.PurchaseCode) && p.PurchaseCode.Contains(input.PurchaseCode) : true)
                              && (!string.IsNullOrEmpty(input.PurchaseContactNo) ? !string.IsNullOrEmpty(p.PurchaseContactNo) && p.PurchaseContactNo.Contains(input.PurchaseContactNo) : true)
                              && (!string.IsNullOrEmpty(input.ProducerOrderNo) ? !string.IsNullOrEmpty(p.ProducerOrderNo) && p.ProducerOrderNo.Contains(input.ProducerOrderNo) : true)
                              && (!string.IsNullOrEmpty(input.Code) ? !string.IsNullOrEmpty(p.Code) && p.Code.Contains(input.Code) : true)
                              && (input.Paied.HasValue && input.Paied.Value == 1 ? !string.IsNullOrEmpty(p.Code) : true)
                              && (input.Paied.HasValue && input.Paied.Value == 0 ? string.IsNullOrEmpty(p.Code) : true)
                              && (!string.IsNullOrEmpty(input.PaymentAutoItemCode) ? !string.IsNullOrEmpty(p.PaymentAutoItemCode) && p.PaymentAutoItemCode.Contains(input.PaymentAutoItemCode) : true)
                              && (serviceIds != null && serviceIds.Any() ? e.ServiceId.HasValue && serviceIds.Contains(p.ServiceId.Value.ToString()) : true)
                        select new PaymentPayInfoListOutput()
                        {
                            Id = p.Id,
                            Code = p.Code,
                            BillDate = p.BillDate,
                            PaymentDate = p.PaymentDate??DateTime.MinValue,
                            CompanyId = p.CompanyId,
                            CompanyName = p.CompanyName,
                            AgentId = p.AgentId,
                            AgentName = p.AgentName,
                            ServiceId = p.ServiceId,
                            ServiceName = p.ServiceName,
                            CoinCode = p.CoinCode,
                            CoinName = p.CoinName,
                            Value=p.Value,
                            RMBAmount = p.RMBAmount,
                            ProjectId = p.ProjectId,
                            ProjectCode = p.ProjectCode,
                            ProjectName = p.ProjectName,
                            PurchaseCode = p.PurchaseCode,
                            PurchaseContactNo = p.PurchaseContactNo,
                            ProducerOrderNo = p.ProducerOrderNo,
                            CustomerName = p.CustomerName,
                            PaymentAutoItemCode = p.PaymentAutoItemCode,
                            LimitedDiscount = p.LimitedDiscount,
                            CreatedBy=p.CreatedBy,
                            DebtBillCode = e != null ? e.BillCode : "",
                            DebtRelateCode = e != null ? e.RelateCode : "",
                            DebtPurchaseCode = e != null ? e.PurchaseCode : "",
                            DebtPurchaseContactNo = e != null ? e.PurchaseContactNo : "",
                            DebtCoinName = e != null ? e.CoinName : "",
                            DebtAgentName = e != null ? e.AgentName : "",
                            DebtProducerOrderNo = e != null ? e.ProducerOrderNo : "",
                            CreditBillCode = crtdebtdetail != null ? crtdebtdetail.BillCode : "",
                            DebtValue = e != null ? e.Value : (decimal?)null,
                            CreditCustomerName = crtdebtdetail != null ? crtdebtdetail.CustomerName : "",
                            DebtDetailValue = t != null ? t.Value : 0,
                            DebtDiscount = t != null ? t.Discount : null,
                            DebtOriginValue = t != null ? t.OriginValue : null,
                            AccountPeriodType = t != null ? t.AccountPeriodType : null,
                            DebtServiceName = e != null ? e.ServiceName : null,
                            DebtReceiveCode = t != null ? t.ReceiveCode : null,
                            DebtHospitalName = crtdebtdetail != null ? crtdebtdetail.HospitalName : null,
                            DebtOrderNo = t != null ? t.OrderNo : null,
                            DebtOriginOrderNo = e != null ? e.OriginOrderNo : null,
                            DebtCreditId = crtdebtdetail != null ? crtdebtdetail.Id : (Guid?)null
                        };

            var total = await query.AsNoTracking().CountAsync();
            var result = await query.AsNoTracking().Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

            #region 获取医院数据
            var paymentAutoItemCodes = result.Select(x => x.PaymentAutoItemCode).ToList();
            var hospitals = await GetHospitalsByPaymentAutoItemCodes(paymentAutoItemCodes);
            #endregion

            //获取已冲销金额
            var paymentCodes = result.Where(p => !string.IsNullOrEmpty(p.Code)).Select(p => p.Code).ToList();
            var debtCodes = result.Where(p=>!string.IsNullOrEmpty(p.DebtBillCode)).Select(p => p.DebtBillCode).ToList();
            // 分别查询 DebtBillCode 和 CreditBillCode 的 Abatement 记录
            var paymentAbatements = await _db.Abatements
                .Where(p => paymentCodes.Contains(p.DebtBillCode))
                .Select(p => p.Adapt<AbatmentDTO>())
                .ToListAsync();
            
            var debtAbatements =await _db.Abatements
                .Where(p => debtCodes.Contains(p.CreditBillCode))
                .Select(p => p.Adapt<AbatmentDTO>())
                .ToListAsync();

            //获取进项票金额
            var relateCodes = result.Select(p => p.DebtRelateCode).ToList();
            var noTaxAmountList = await _db.InputBillSubmitDetails.Where(p => (debtCodes.Contains(p.StoreInItemCode) || relateCodes.Contains(p.StoreInItemCode)) && p.InputBill.Status == 2).Include(x => x.InputBill).AsNoTracking().ToListAsync();
            var creditIds = result.Select(p => p.DebtCreditId).Distinct().ToList();
            var invoices = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value)).AsNoTracking().ToListAsync();
            //查询发票相关信息
            var invoiceNos = invoices.Select(x => x.InvoiceNo).ToList();
            var invoiceInfos = await _db.InvoiceCredits.Where(x => invoiceNos.Contains(x.InvoiceNo)).AsNoTracking().ToListAsync();

            foreach (var item in result)
            {
                // 终端医院字符串
                string hosStr = string.Empty;
                var hoslists = hospitals.Where(x => x.PaymentAutoItemCode == item.PaymentAutoItemCode && x.AgentId == item.AgentId && x.Code == item.Code).ToList();
                if (hoslists.Any())
                {
                    hosStr = string.Join(",", hoslists.Where(x => !string.IsNullOrEmpty(x.HospitalName)).Select(x => x.HospitalName).ToList());
                }

                var invoice = invoices.FirstOrDefault(p => p.CreditId == item.DebtCreditId);
                item.AbatedValue=paymentAbatements.Where(t => t.DebtBillCode == item.Code).Sum(t => t.Value);
                item.DebtInvoiceNo = invoice == null ? "" : string.Join(",", invoices.Where(x => x.CreditId == item.DebtCreditId).Select(x => x.InvoiceNo).ToList());
                item.DebtAbatedValue = debtAbatements.Where(t => t.DebtBillCode == item.DebtBillCode || t.CreditBillCode == item.DebtBillCode).Sum(t => t.Value);
                item.DebtBalance = Math.Abs(item.DebtValue ?? 0) - item.DebtAbatedValue;
                item.DebtInvoiceTime = invoice == null ? "" : invoice.InvoiceTime.Value.ToString("yyyy-MM-dd");
                item.DebtNoTaxAmount = noTaxAmountList.Where(p => p.StoreInItemCode == item.DebtBillCode || p.StoreInItemCode == item.DebtRelateCode).Sum(p => p.NoTaxAmount);
            }

            return new BaseResponseData<List<PaymentPayInfoListOutput>>()
            {
                Code = CodeStatusEnum.Success,
                Data = result,
                Total = total
            };
        }
    }
}
