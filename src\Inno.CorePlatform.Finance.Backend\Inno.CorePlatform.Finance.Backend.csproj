﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<DocumentationFile>..\..\docxml\backend.xml</DocumentationFile>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<UserSecretsId>************************************</UserSecretsId>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npoi.Mapper" Version="6.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.7.0" />
		<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.2" />
		<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
		<PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Adapter\Inno.CorePlatform.Finance.Adapter.csproj" />
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Application\Inno.CorePlatform.Finance.Application.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="Properties\launchSettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
