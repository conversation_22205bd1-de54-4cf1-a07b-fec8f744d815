﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter
{
    public class SelectReceiptInfoInput
    {
        public string? id { get; set; }
    }
    public class BDSBaseInput
    {
        public List<string>? ids { get; set; }
        public string? id { get; set; }
        public string? userId { get; set; }
        public string? userName { get; set; }
        public string? name { get; set; }
        public List<string?>? names { get; set; }
        public int limit { get; set; } = 1000;

    }
    /// <summary>
    /// 可用状态
    /// </summary>
    public class BaseEnableInput : BDSBaseInput
    {

        public int? status { get; set; } = 1;
    }
}
