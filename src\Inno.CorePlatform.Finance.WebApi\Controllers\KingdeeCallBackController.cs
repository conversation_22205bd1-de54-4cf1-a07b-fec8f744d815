﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Refund;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 财务能力中心金蝶回调
    /// </summary>
    [ApiController]
    [Route("api/KingdeeCallBack")]
    public class KingdeeCallBackController : BaseController
    {
        public override bool EnableParameterLogging { get; set; } = true;

        private readonly IInvoiceCreditAppService _invoiceCreditAppService;
        private readonly IAbtmentService _abtmentService;
        private readonly IPaymentAppService _paymentAppService;
        private readonly IInputBillAppService _inputBillAppService;
        private readonly IRecognizeReceiveAppService _recognizeReceiveService;
        private readonly ICustomizeInvoiceAppService _customizeInvoiceAppService;
        private readonly IBaseAppService _baseAppService;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IEquipmentsApiClient _equipmentsApiClient;
        private readonly IRefundAppService _refundAppService;
        private readonly IKingdeeCallbackValidationService _kingdeeCallbackValidationService;
        private readonly ILogger<KingdeeCallBackController> _logger;

        public KingdeeCallBackController(
            IPaymentAppService paymentAppService,
            IAbtmentService abtmentService,
            IInvoiceCreditAppService invoiceCreditAppService,
            IInputBillAppService inputBillAppService,
            ICustomizeInvoiceAppService customizeInvoiceAppService,
            IRecognizeReceiveAppService recognizeReceiveService,
            IEasyCachingProvider easyCaching,
            IProjectMgntApiClient projectMgntApiClient,
            IEquipmentsApiClient equipmentsApiClient,
            IRefundAppService refundAppService,
            IBaseAppService baseAppService,
            ISubLogService subLog,
            IKingdeeCallbackValidationService kingdeeCallbackValidationService,
            ILogger<KingdeeCallBackController> logger) : base(subLog)
        {
            this._easyCaching = easyCaching;
            this._paymentAppService = paymentAppService;
            this._invoiceCreditAppService = invoiceCreditAppService;
            this._abtmentService = abtmentService;
            this._inputBillAppService = inputBillAppService;
            this._recognizeReceiveService = recognizeReceiveService;
            this._customizeInvoiceAppService = customizeInvoiceAppService;
            this._baseAppService = baseAppService;
            this._projectMgntApiClient = projectMgntApiClient;
            this._equipmentsApiClient = equipmentsApiClient;
            _refundAppService = refundAppService;
            _kingdeeCallbackValidationService = kingdeeCallbackValidationService;
            _logger = logger;
        }

        /// <summary>
        /// 创建进项发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("createInputbill")]
        [OperationLog("创建进项发票-金蝶回调")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<BaseResponseData<int>> CreateInputBill(InputBillInputDTo input)
        {
            if (string.IsNullOrEmpty(input.CompanyCode))
            {
                return Failed<int>("公司Code不能为空");
            }
            try
            {
                var res = await _inputBillAppService.CreateInputBill(input);
                if (res <= 0)
                {
                    return Failed(res, "创建进项发票失败");
                }
                return Success(res, "创建进项发票成功");
            }
            catch (Exception ex)
            {
                return Failed<int>(ex.Message);
            }

        }

        /// <summary>
        /// 创建销项发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("createinvoice")]
        [OperationLog("创建销项发票-金蝶回调")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<BaseResponseData<int>> CreateInvoiceAsync(InvoiceCreditInput input)
        {
            try
            {
                if (string.IsNullOrEmpty(input.CustomizeInvoiceCode))
                {
                    return Failed<int>("CustomizeInvoiceCode不能为空");
                }
                else
                {
                    await _invoiceCreditAppService.CreateInvoiceCredit(input);
                    return Success<int>("操作成功");
                }
            }
            catch (Exception ex)
            {
                return Failed<int>(ex.Message);
            }
            
        }

        /// <summary>
        /// 创建付款单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("createpayment")]
        [OperationLog("创建付款单-金蝶回调")]
        public async Task<BaseResponseData<decimal>> CreatePaymentAsync(List<KingdeePaymentInput> input)
        {
            var ret = BaseResponseData<decimal>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "createpayment_" + input[0].Code;
            if (!string.IsNullOrEmpty(input[0].PurchaseCode))
            {
                cachekey += input[0].PurchaseCode;
            }
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "创建付款单-金蝶回调");
                    ret = await _paymentAppService.ChangeByKingdeeAsync(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<decimal>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 退款付款结果通知
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("refundpayment")]
        [OperationLog("退款付款结果通知-金蝶回调")]
        public async Task<BaseResponseData<decimal>> RefundPaymentAsync(List<KingdeeRefundPaymentInput> input)
        {
            var ret = BaseResponseData<decimal>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "refundpayment_" + input.First().PayBillNo;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "退款付款结果通知-金蝶回调");
                    var inputs = new List<UpdatePaymentStatusInput>();
                    foreach (var item in input)
                    {
                        inputs.Add(new UpdatePaymentStatusInput
                        {
                            billno = item.PayApplicationBillNo,
                            payBillNo = item.PayBillNo,
                            status = 1,
                        });
                    }

                    var retStr = await _projectMgntApiClient.UpdatePaymentStatus(inputs);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<decimal>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        ///  批量付款金蝶回调接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("generateabt")]
        [OperationLog("批量付款-金蝶回调")]
        public async Task<BaseResponseData<int>> GenerateAbtAsync(List<GenerateAbtInput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "generateabt_" + input.First().PaymentCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, "1", TimeSpan.FromSeconds(120));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "批量付款-金蝶回调");
                    if (input == null || !input.Any())
                    {
                        ret = BaseResponseData<int>.Failed(500, "入参不能为空");
                    }
                    else if (input.Count(p => p.Value <= 0) > 0)
                    {
                        ret = BaseResponseData<int>.Failed(500, "存在冲销金额<=0的数据");
                    }
                    else
                    {
                        // 检查盘点状态，如果正在盘点则抛出异常
                        await _kingdeeCallbackValidationService.CheckInventoryStatusForBatchPayment(input, "批量付款冲销");

                        if (await _abtmentService.GenerateAbtAsync(input) < 0)
                            ret = BaseResponseData<int>.Failed(500, "操作失败");
                    }
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        ///  应收冲销金蝶回调接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("generateabtforcredit")]
        [OperationLog("应收冲销-金蝶回调")]
        public async Task<BaseResponseData<int>> GenerateAbtForCreditAsync(GenerateAbtForCreditInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "generateabtforcredit_" + input.Code + "_" + (input.lstAbtDetail != null && input.lstAbtDetail.Any() ? input.lstAbtDetail[0].BillCode : string.Empty);
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "应收冲销-金蝶回调");
                    if (string.IsNullOrEmpty(input.Code))
                    {
                        ret = BaseResponseData<int>.Failed(500, "Code不能为空");
                    }
                    else if (string.IsNullOrEmpty(input.userName))
                    {
                        ret = BaseResponseData<int>.Failed(500, "userName不能为空");
                    }
                    else if (string.IsNullOrEmpty(input.BillType))
                    {
                        ret = BaseResponseData<int>.Failed(500, "BillType不能为空");
                    }
                    else if (string.IsNullOrEmpty(input.RecognizeReceiveCode) && input.BillType == "receive")
                    {
                        ret = BaseResponseData<int>.Failed(500, "RecognizeReceiveCode不能为空");
                    }
                    else
                    {
                        // 检查盘点状态，如果正在盘点则抛出异常
                        await _kingdeeCallbackValidationService.CheckInventoryStatusForCredit(input, "应收冲销");

                        if (await _abtmentService.GenerateAbtForCreditAsync(input) <= 0)
                            ret = BaseResponseData<int>.Failed(500, "操作失败");
                    }
                    _easyCaching.Remove(cachekey);
                }
                else
                {
                    ret = BaseResponseData<int>.Failed(500, "存在并发操作");
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 认款-金蝶驳回
        /// </summary>
        /// <param name="input">收款单号</param>
        /// <returns></returns>
        [HttpPost("recognize/cancel")]
        [OperationLog("驳回认款-金蝶回调")]
        public async Task<BaseResponseData<int>> Cancel([FromBody] RecognizeReceiveCancelInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "vrecognize_cancel_" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "驳回认款-金蝶回调");
                    var ketInput = new CancelReceiptInput();
                    ketInput.RecognizeCode = input.Code;
                    var result = await _recognizeReceiveService.Cancel(ketInput);
                    if (result < 0)
                    {
                        ret = BaseResponseData<int>.Failed(500, "状态修改失败");
                    }
                    else
                    {
                        ret = new BaseResponseData<int>
                        {
                            Code = CodeStatusEnum.Success,
                            Data = result
                        };
                    }
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;

        }

        /// <summary>
        /// 认款-金蝶审核通过
        /// </summary>
        /// <param name="input">收款单号</param>
        /// <returns></returns>
        [HttpPost("recognize/approve")]
        [OperationLog("通过认款-金蝶回调")]
        public async Task<BaseResponseData<int>> Approve([FromBody] RecognizeReceiveApproveInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "recognize_approve" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "通过认款-金蝶回调");
                    var result = await _recognizeReceiveService.Approve(input);
                    // #65465 核心平台认款经金蝶审核通过之后推送至商务平台
                    // #76710【SPD认款接口】需要给SPD传发票认款信息，9月15日前上线
                    if (result > 0)
                    {
                        var push = await _recognizeReceiveService.PushBusiness(input);
                        if (push.code != 0)
                        {
                            _easyCaching.Remove(cachekey);
                            ret = BaseResponseData<int>.Success("金蝶审核通过，状态修改成功。推送SPD发生错误：" + push.msg);
                            return ret;
                        }
                    }
                    if (result < 0)
                    {
                        ret = BaseResponseData<int>.Failed(500, "状态修改失败");
                    }
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                //ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 开票-金蝶审核通知
        /// </summary>
        /// <param name="input">票单号</param>
        /// <returns></returns>
        [HttpPost("CustomizeInvoiceApprove")]
        [OperationLog("开票-金蝶回调")]
        public async Task<BaseResponseData<int>> CustomizeInvoiceApprove([FromBody] KingdeeCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "customizeInvoiceApprove" + input.billNo;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "开票-金蝶回调");
                    ret = await _customizeInvoiceAppService.Approve(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 销项发票作废【非跨月】
        /// </summary>
        /// <param name="input">票单号</param>
        /// <returns></returns>
        [HttpPost("CancelCustomizeInvoice")]
        [OperationLog("销项发票作废【非跨月】-金蝶回调")]
        public async Task<BaseResponseData<int>> CancelCustomizeInvoice([FromBody] KingdeeCancelCustomizeInvoiceInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "cancelCustomizeInvoice" + input.billNo;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "销项发票作废【非跨月】-金蝶回调");
                    ret = await _invoiceCreditAppService.CancelCustomizeInvoice(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        ///  收款(退款)与预付单冲销
        /// </summary>
        /// <param name="input">参数</param>
        /// <returns></returns>
        [HttpPost("AbatementPayment")]
        [OperationLog("收款(退款)与预付单冲销-金蝶回调")]
        public async Task<BaseResponseData<int>> AbatementPayment([FromBody] KingdeeAbatementPaymentInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "abatementPayment" + input.AbatementCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "收款(退款)与预付单冲销-金蝶回调");

                    // 检查盘点状态，如果正在盘点则抛出异常
                    await _kingdeeCallbackValidationService.CheckInventoryStatusForPayment(input, "收款(退款)与预付单冲销");

                    ret = await _paymentAppService.AbatementPayment(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }

            return ret;
        }

        /// <summary>
        ///  退款与应付单冲销
        /// </summary>
        /// <param name="input">参数</param>
        /// <returns></returns>
        [HttpPost("ReturnAbatementDebt")]
        [OperationLog("退款与应付单冲销-金蝶回调")]
        public async Task<BaseResponseData<int>> ReturnAbatementDebt([FromBody] KingdeeAbatementDebtInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "abatementPayment" + input.AbatementCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "退款与应付单冲销-金蝶回调");

                    // 检查盘点状态，如果正在盘点则抛出异常
                    await _kingdeeCallbackValidationService.CheckInventoryStatusForDebt(input, "退款与应付单冲销");

                    ret = await _paymentAppService.ReturnAbatementDebt(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }

            return ret;
        }

        /// <summary>
        /// 信用证支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreditPay")]
        [OperationLog("信用证支付-金蝶回调")]
        public async Task<BaseResponseData<int>> CreditPay([FromBody] KindeeCreditPayInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "CreditPay" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "信用证支付-金蝶回调");
                    if (string.IsNullOrEmpty(input.Code))
                    {
                        ret = BaseResponseData<int>.Failed(500, "Code不能为空");
                    }
                    else if (string.IsNullOrEmpty(input.PurchaseCode))
                    {
                        ret = BaseResponseData<int>.Failed(500, "PurchaseCode不能为空");
                    }
                    else
                    {
                        ret = await _paymentAppService.CreditPay(input);
                    }
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 固定资产折旧(设备投放修订)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("UpdateEquipment")]
        [OperationLog("固定资产折旧-金蝶回调")]
        public async Task<BaseResponseData<string>> UpdateEquipment([FromBody] List<EquipmentInput> input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "UpdateEquipment" + input.First().equipmentCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "固定资产折旧-金蝶回调");
                    ret = await _equipmentsApiClient.UpdateEquipmentForPut(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<string>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 创建游离的付款单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreateNotProducerCodePayment")]
        [OperationLog("创建游离的付款单-金蝶回调")]
        public async Task<BaseResponseData<string>> CreateNotProducerCodePayment([FromBody] PaymentInputOfKd input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "CreateNotProducerCodePayment" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "创建游离的付款单-金蝶回调");
                    //todo
                    ret = await _paymentAppService.CreateNotProducerCodePayment(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<string>.Failed(500, ex.Message);
            }
            return ret;
        }
        /// <summary>
        /// 更新贴现日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("UpdateDiscountDate")]
        [OperationLog("更新贴现日期-金蝶回调")]
        public async Task<BaseResponseData<int>> UpdateDiscountDate([FromBody] KingdeeUpdateDiscountDateInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "UpdateDiscountDate" + input.ReceiveCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "更新贴现日期-金蝶回调");
                    ret = await _recognizeReceiveService.UpdateDiscountDate(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 退款状态金蝶回传
        /// </summary>
        /// <param name="input">收款单号</param>
        /// <returns></returns>
        [HttpPost("refundStateSynchronization")]
        [OperationLog("退款状态同步-金蝶回调")]
        public async Task<BaseResponseData<int>> RefundStateSynchronization([FromBody] RefundStateSynchronizationInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "refundStateSynchronization_" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    //await _baseAppService.CreateSubLog(SubLogSourceEnum.KingdeeCallBack, jsonStr, "admin", "退款状态同步-金蝶回调");
                    ret = await _refundAppService.UpdateRefundState(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }


        /// <summary>
        /// 删除付款申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("DeleteFund")]
        [OperationLog("删除付款申请-金蝶回调")]
        public async Task<BaseResponseData<int>> FundDelete(FundDeleteInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "DeleteFund" + input.Code;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    if (string.IsNullOrEmpty(input.Code))
                    {
                        ret = BaseResponseData<int>.Failed(500, "Code不能为空");
                    }
                    else
                    {
                        ret = await _paymentAppService.FundDelete(input);
                    }
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            return ret;
        }

        /// <summary>
        /// 发送邮件-金蝶回调
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns> 
        [HttpPost("SendEmail")]
        [OperationLog("发送邮件-金蝶回调")]
        public async Task<BaseResponseData<string>> SendEmailAsync(SendEmailInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功");
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "SendEmail" + input.CustomizeInvoiceCode;
            var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
            try
            {
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    ret = await _customizeInvoiceAppService.SendEmailAsync(input);
                    _easyCaching.Remove(cachekey);
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                ret = BaseResponseData<string>.Failed(500, ex.Message);
            }
            return ret;
        }
    }
}
