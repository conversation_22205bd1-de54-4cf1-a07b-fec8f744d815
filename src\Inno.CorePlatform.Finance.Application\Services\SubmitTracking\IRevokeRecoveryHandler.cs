using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitTracking
{
    /// <summary>
    /// 撤销恢复处理器接口，用于定义撤销操作失败时的恢复方法
    /// </summary>
    public interface IRevokeRecoveryHandler
    {
        /// <summary>
        /// 恢复金蝶撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>恢复结果</returns>
        Task<KingdeeApiResult> RecoverKingdeeRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string currentUserName);

        /// <summary>
        /// 恢复经销购货入库撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverDistributionPurchaseRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复寄售转购货撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverConsignmentToPurchaseRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复服务费采购撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverServiceFeeProcurementRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复经销调出撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverDistributionTransferRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复购货修订撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverPurchaseRevisionRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复换货转退货撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverExchangeToReturnRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 恢复损失确认撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        Task<BaseResponseData<bool>> RecoverLossRecognitionRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);
    }
}
