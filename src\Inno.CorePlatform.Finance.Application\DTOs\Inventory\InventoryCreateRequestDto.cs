using System;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    /// <summary>
    /// 盘点创建请求基础类
    /// </summary>
    public class InventoryCreateRequestDto
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 系统月度 (格式: yyyy-MM)
        /// </summary>
        public string SysMonth { get; set; }

        /// <summary>
        /// 操作用户ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// 操作用户名
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 换货盘点创建请求
    /// </summary>
    public class ExchangeInventoryCreateRequestDto : InventoryCreateRequestDto
    {
    }

    /// <summary>
    /// 待确认收入盘点创建请求
    /// </summary>
    public class SureIncomeInventoryCreateRequestDto : InventoryCreateRequestDto
    {
    }

    /// <summary>
    /// 暂存盘点创建请求
    /// </summary>
    public class TinyInventoryCreateRequestDto : InventoryCreateRequestDto
    {
    }

    /// <summary>
    /// 跟台盘点创建请求
    /// </summary>
    public class SginyInventoryCreateRequestDto : InventoryCreateRequestDto
    {
    }
}
