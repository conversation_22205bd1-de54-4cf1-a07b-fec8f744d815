﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
        /// <summary>
        /// 对账函打印内容
        /// </summary>
    public class ReconciliationLetterPrintInput
    {
        /// <summary>
        /// 
        /// </summary>
        public string? code { get; set; }
        /// <summary>
        /// 模版的code
        /// </summary>
        public string templateName { get; set; }
        /// <summary>
        /// 模板的类型(上一级)
        /// </summary>
        public string templateType {  get; set; }

        public string fileType { get; set; }
        /// <summary>
        /// 分类 0:上游;1:下游 默认1
        /// </summary>
        public int classification {  get; set; }
        public string companyId {  get; set; }
        /// <summary>
        /// 
        /// </summary>
        public LetterAndInvoiceParams @params { get; set; }

        public List<LetterInvoinceList> list { get; set; }

    }
    public class LetterAndInvoiceParams: LetterParams
    {
        public string? valuesum {  get; set; }

        public string? receivedvaluesum {  get; set; } 

        public string? nonreceivedvaluesum { get; set; }   
    }

    public class LetterParams 
    {
        public string? customername {  get; set; }

        public string? deadlinedate { get; set; }

        public string? chinesemoney {  get; set; }

        public string? money { get; set; }

        public string? companyname {  get; set; }
    }
    public class LetterInvoinceList 
    {
        public string? billdate {  get; set; }

        public string? billcode {  get; set; }

        public string? receivedvalue {  get; set; }

        public string? value {  get; set; }

        public string? nonreceivedvalue {  get; set; }

        public string? remark {  get; set; }
    }
}
