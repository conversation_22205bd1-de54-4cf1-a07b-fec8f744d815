﻿using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Debts
{
    public class DebtDto : Debt
    {
    }
    public class InputBillOfDebt
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string BillCode { get; set; }
        public DateTimeOffset BillDate { get; set; }
        public decimal Value { get; set; }
        public decimal? InvoiceAmount { get; set; }
        public decimal CanInvoiceAmount
        {
            get
            {
                return Value - (InvoiceAmount.HasValue ? InvoiceAmount.Value : 0);
            }
        }
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get;  set; }
    }
}
