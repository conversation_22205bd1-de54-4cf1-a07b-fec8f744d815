﻿using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.Interfaces;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency
{
    /// <summary>
    /// 默认的幂等性键生成器，直接从请求头中获取Idempotency-Key。
    /// </summary>
    public class DefaultIdempotencyKeyGenerator : IIdempotencyKeyGenerator
    {
        public string GenerateKey(ActionExecutingContext context)
        {
            if (!context.HttpContext.Request.Headers.TryGetValue("Idempotency-Key", out var key) ||
                string.IsNullOrEmpty(key))
            {
                throw new InvalidOperationException("Idempotency-Key header is required");
            }
            return key.ToString();
        }
    }
}
