﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.IC
{
    public class SPDInvoiceInput
    {
        /// <summary>
        /// 供应商发票申请单号
        /// </summary>
        [JsonProperty("apply_code", NullValueHandling = NullValueHandling.Ignore)]
        public string? apply_code { get; set; }

        /// <summary>
        /// Spd配送单号
        /// </summary>
        [JsonProperty("distribute_code", NullValueHandling = NullValueHandling.Ignore)]
        public string? distribute_code { get; set; }
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 供应商开票集合
        /// </summary>
        public List<SPDInvoiceitemInput> invoice_items { get; set; }
    }

    public class SPDInvoiceitemInput
    {
        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime billing_date { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoice_num { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal price { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string? invoice_code { get; set; }

        /// <summary>
        /// 校验码后六位/开具金额
        /// 专用发票时填写开具金额，
        /// 普通发票填写检验码后六位,
        /// 全电发票(无发票代码)填写价税合计金额 
        /// </summary>
        public string? invoice_check_code_price { get; set; }

        /// <summary>
        /// 供应商开票明细
        /// </summary>
        public List<SPDInvoiceDetailInput> details { get; set; }
        public string remark { get; set; }
    }


    public class SPDInvoiceDetailInput
    {
        /// <summary>
        /// 数量
        /// </summary>
        public decimal count { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal price { get; set; }

        /// <summary>
        /// 品规id
        /// </summary>
        public string? product_id { get; set; }
        /// <summary>
        /// 退还单号
        /// </summary>
        public string? ret_code { get; set; }
        public string? consume_code { get; set; }
        public string? product_no { get; set; }
    }

    public class SPDQueryAmountInput
    {
        public List<string?>? codes { get; set; }
    }

    /// <summary>
    /// 获取SPD发票详情
    /// </summary>
    public class SpdInvoiceApplyDetailInput
    {
        /// <summary>
        /// 三方单号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid CustomerId { get; set; }
    }

    /// <summary>
    /// 提交至安贞入参
    /// </summary>
    public class UploadInvoiceAZDto
    {
        /// <summary>
        /// 详情
        /// </summary>
        public List<UploadInvoiceAZData> invoiceList { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        public Guid CustomerId { get; set; }
    }
    /// <summary>
    /// 安贞入参详情
    /// </summary>
    public class UploadInvoiceAZData
    {
        /// <summary>
        /// 对账单ID
        /// </summary>
        public string bill_id { get; set; }

        /// <summary>
        /// 对账明细ID
        /// </summary>
        public string bill_item_id { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string product_id { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string invoice_no { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string Invoice_code { get; set; }

        /// <summary>
        /// 开票日期(yyyy-mm-dd)
        /// </summary>
        public string Invoice_date { get; set; }

        /// <summary>
        /// 发票总金额 
        /// </summary>
        public decimal goods_amount { get; set; }

        /// <summary>
        /// 数量 
        /// </summary>
        public decimal? quantity { get; set; }

        /// <summary>
        /// 单价 
        /// </summary>
        public decimal? price { get; set; }

        /// <summary>
        /// 扣率（默认100%） 
        /// </summary>
        public decimal? discount { get; set; }

        /// <summary>
        /// 开票企业ID
        /// </summary>
        public string? saler_id { get; set; }

        /// <summary>
        /// 开票企业名称
        /// </summary>
        public string? saler_name { get; set; }

        /// <summary>
        /// 结算企业ID
        /// </summary>
        public string buyer_id { get; set; }

        /// <summary>
        /// 结算企业名称（医院
        /// </summary>
        public string buyer_name { get; set; }
        public string? product_no { get; set; }
    }

    /// <summary>
    /// 提交至利群入参
    /// </summary>
    public class UploadInvoiceVanxDto
    {
        public List<UploadInvoiceVanxData> detailline { get; set; }
        public Guid CompanyId { get; set; }
        public Guid CustomerId { get; set; }
    }
    /// <summary>
    /// 提交至利群详情
    /// </summary>
    public class UploadInvoiceVanxData
    {
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invno { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invcode { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string invdate { get; set; }
        /// <summary>
        /// 开票总金额
        /// </summary>
        public string invamt { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string invname { get; set; }
        /// <summary>
        /// 开票企业名称
        /// </summary>
        public string supname { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remarks { get; set; }
        /// <summary>
        /// 发票文件ID
        /// </summary>
        public string fileid { get; set; }
        /// <summary>
        /// 结算单主单号
        /// </summary>
        public string setacd { get; set; }
        /// <summary>
        /// 结算单明细号
        /// </summary>
        public string setadcd { get; set; }
        /// <summary>
        /// 开票数量
        /// </summary>
        public string invqty { get; set; }
        /// <summary>
        /// 开票单价
        /// </summary>
        public string invtaxprice { get; set; }
        /// <summary>
        /// 开票明细金额
        /// </summary>
        public string invtaxamt { get; set; }
    }

    /// <summary>
    /// 华东医院提交发票入参
    /// </summary>
    public class UploadInvoiceYiDaoItem
    {
        /// <summary>
        /// 发票类型
        /// </summary>
        public string invoiceType { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string invoiceCode { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        public string invoiceAmount { get; set; }
        /// <summary>
        /// 发票日期
        /// </summary>
        public string invoiceDate { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        public string invoiceStatus { get; set; }
        /// <summary>
        /// 开票类型
        /// </summary>
        public string invoiceTaxType { get; set; }
        /// <summary>
        /// 配送单号
        /// </summary>
        public string deliveryNo { get; set; }
        /// <summary>
        /// 配送明细行号
        /// </summary>
        public string deliveryDetailNo { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        public string goodsCode { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        public Guid ProductId { get; set; }
        /// <summary>
        /// 销售单号(唯一)
        /// </summary>
        public string saleOrderno { get; set; }
        /// <summary>
        /// 开票数量
        /// </summary>
        public decimal invNum { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string memo1 { get; set; }
        public string? ProductNo { get; set; }
    }



    public class UploadInvoiceYiDaoDto
    {
        /// <summary>
        /// 数据
        /// </summary>
        public List<UploadInvoiceYiDaoItem> list { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        public Guid CustomerId { get; set; }
    }
}
