﻿using Inno.CorePlatform.Finance.Data.Migrations;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class RecognizeReceiveDetailOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 发票号/订单号
        /// </summary> 
        [MaxLength(200)]
        public string Code { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public string? TypeDescription { get { return ((RecognizeTypeEnums)this.Type).GetDescription(); } }
        /// <summary>
        /// 认款时间
        /// </summary>
        public DateTime RecognizeDate { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>
        [Precision(18, 4)]
        public decimal Value { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool? IsSkip { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public string? IsSkipStr
        {
            get
            {
                return IsSkip.HasValue && IsSkip.Value ? "是" : "否";
            }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }
        /// <summary>
        /// 认款人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 付款单位Id
        /// </summary> 
        public string? CustomerId { get; set; }

        private string customerName;
        /// <summary>
        /// 付款单位名称
        /// </summary> 
        public string? CustomerName
        {
            get
            {
                if (string.IsNullOrEmpty(CustomerNme))
                {
                    return customerName;
                }
                else
                {
                    return CustomerNme;
                }
            }
            set { customerName = value; }
        }

        /// <summary>
        /// 付款单位名称
        /// </summary> 
        public string? CustomerNme { get; set; }

        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }
        /// <summary>
        /// 细分类型
        /// </summary>
        public string? ClassifyDescription { get { return this.Classify.HasValue ? this.Classify.GetDescription() : ""; } }
        /// <summary>
        /// 项目id
        /// </summary>    
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目单号
        /// </summary>    
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 终端客户id
        /// </summary>    
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端客户
        /// </summary>    
        public string? HospitalName { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>    
        public string? ProjectName { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>    
        public int? CollectionType { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary> 
        public string? ServiceName { get; set; }
        /// <summary>
        /// 认款单关联Id
        /// </summary>    
        public Guid? RecognizeReceiveItemId { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>    
        public decimal? TotalAmount { get; set; }
        /// <summary>
        /// 发票号/订单号/应收日期
        /// </summary> 
        public string? CodeTime { get; set; }

        /// <summary>
        /// 回款显示日期
        /// </summary>
        public DateTime? BackDateTime { get; set; }

        /// <summary>
        /// 应收明细
        /// </summary>
        public List<CreditInfo>? CreditInfo { get; set; }

        /// <summary>
        /// 票面金额
        /// </summary>    
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 明细状态
        /// </summary>
        public RecognizeReceiveDetailEnum? Status { get; set; }

        /// <summary>
        /// 明细状态
        /// </summary>
        public string? StatusDescription
        {
            get
            {
                return Status.HasValue ? Status.GetDescription() : "正常";
            }
        }

        /// <summary>
        /// 已转货款金额（暂收款独有）
        /// </summary>
        public decimal? UseValue { get; set; }

        /// <summary>
        /// 撤销金额（暂收款独有）
        /// </summary>
        public decimal? CancelValue { get; set; }

        /// <summary>
        /// 余额（暂收款独有）
        /// </summary>
        public decimal? SurplusValue { get; set; }

        /// <summary>
        /// 可撤销金额（暂收款独有）
        /// </summary>
        public decimal? CurrentValue { get; set; }
    }

    /// <summary>
    /// 导出认款明细
    /// </summary>
    public class RecognizeReceiveDetailExportOutput : RecognizeReceiveDetailOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditTypeStr
        {
            get
            {
                return CreditType.HasValue ? CreditType.GetDescription() : string.Empty;
            }
        }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 项目名称 
        /// </summary> 
        public string? ProjectNameByCredit { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary> 
        public string? ServiceNameByCredit { get; set; }
        /// <summary>
        /// 应收值
        /// </summary>
        public decimal? CreditValue { get; set; }
        /// <summary>
        /// 本次认款金额
        /// </summary>
        public decimal? CurrentValueByCredit { get; set; }
    }
}
