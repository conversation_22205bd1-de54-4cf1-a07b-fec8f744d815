﻿using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService.PaymentOrderHandService
{
    /// <summary>
    /// 购货修订付款单处理
    /// </summary>
    public class PurchaseRevisePaymentOrderHandler : PaymentOrderHandler
    {
        public PurchaseRevisePaymentOrderHandler(decimal totalPreAmount, string code, IServiceProvider serviceProvider, PurchaseOutPut purchaseOutPut, AdvancePayOutput currentAdvancePay) : 
            base(totalPreAmount, code, serviceProvider,purchaseOutPut, currentAdvancePay)
        {
        }

        public override async Task<decimal> HandPaymentOrder()
        {
            //本次处理金额
            decimal remainintAmount = TotalPreAmount-HandedAmount;
            var _dbContext = _serviceProvider.GetService<FinanceDbContext>();
            var debts = await _dbContext.Debts.Where(p => Code == p.BillCode && p.AbatedStatus == AbatedStatusEnum.NonAbate).OrderByDescending(p => p.Value).ToListAsync();
            var debtPaymentUses = await _dbContext.DebtPaymentUseDetails.Where(p => Code == p.DebtCode).ToListAsync();
            var abatements = await _dbContext.Abatements.Where(p => p.DebtType == "debt" && Code == p.DebtBillCode).ToListAsync();
            if (debts != null && debts.Count > 0)
            {
                foreach (var item in debts)
                {
                    //已被使用金额
                    var useTotalAmount = debtPaymentUses.Where(p => p.DebtCode == item.BillCode).Sum(p => p.UseAmount);
                    var allowUseAmount = Math.Abs(item.Value) - useTotalAmount;
                    //扣掉冲销金额
                    var abatement = abatements.Where(p => p.DebtBillCode == item.BillCode).ToList();
                    if (abatement != null && abatement.Any())
                    {
                        allowUseAmount = allowUseAmount - abatement.Sum(p => p.Value);
                    }

                    if (allowUseAmount > 0)
                    {
                        decimal currentUseAmount = 0;
                        if (allowUseAmount >= remainintAmount)
                        {//剩下允许使用金额大于本次要处理的金额
                            HandedAmount += remainintAmount;
                            currentUseAmount = remainintAmount;
                        }
                        else
                        {
                            HandedAmount += allowUseAmount;
                            currentUseAmount = allowUseAmount;
                        }
                        _dbContext.DebtPaymentUseDetails.Add(new DebtPaymentUseDetailPo
                        {
                            Id = Guid.NewGuid(),
                            CreatedBy = "none",
                            CreatedTime = DateTimeOffset.Now,
                            DebtCode = item.BillCode,
                            DebtId = item.Id,
                            UseAmount = currentUseAmount,
                            UseCode = CurrentPurchaseOrder.Code,
                            RelateId = CurrentAdvancePay.Id
                        });
                    }
                }
            }
            if (NextHandler != null && TotalPreAmount - HandedAmount > 0)
            {
                NextHandler.TotalPreAmount = TotalPreAmount;
                NextHandler.HandedAmount = HandedAmount;
                if (ListPushKdPaymentUseInfos != null && NextHandler.ListPushKdPaymentUseInfos != null)
                {
                    NextHandler.ListPushKdPaymentUseInfos.AddRange(ListPushKdPaymentUseInfos);
                }
                return await NextHandler.HandPaymentOrder();
            }
            else
            {
                return TotalPreAmount - HandedAmount;
            }
        }
    }
}
