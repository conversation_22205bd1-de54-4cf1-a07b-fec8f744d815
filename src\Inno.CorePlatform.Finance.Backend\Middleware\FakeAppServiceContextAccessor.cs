﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Backend.Controllers;

namespace Inno.CorePlatform.Finance.Backend.Middleware
{
    public class FakeAppServiceContextAccessor : IAppServiceContextAccessor
    {
        private readonly IHttpContextAccessor httpContextAccessor;

        public FakeAppServiceContextAccessor(IHttpContextAccessor httpContextAccessor)
        {
            this.httpContextAccessor = httpContextAccessor;
        }
        public AppServiceContext Get()
        {
            var context = httpContextAccessor.HttpContext;
            return new AppServiceContext
            {
                //默认身份
                //UserId = new Guid("2cf0026d-29d4-44be-aff3-fdbd9ad9d7bb"),
                //UserName = "libiyao"
                //UserId = new Guid("b2bfeca0-6590-415c-b426-8446b8c23a39"),
                //UserName = "libiyao"
                //UserId = new Guid("d5284d37-d966-4b79-b935-6be0cf991e42"),
                //UserName = "xiejianhong"
                //服务商客户端QA
                //UserId = new Guid("1130ad2f-d6e0-4c4e-b5e5-b9288fbed7bc"),
                //UserName = "s_xiejh0713-001"
                //UserId = new Guid("5dca6cc5-361e-414f-9876-fc46a34f70ef"),
                //UserName = "s_test_moon"
                //UserId = new Guid("eef67352-c609-474a-804d-0e7b53cf0073"),
                //UserName = "s_test-invoice"
                //服务商客户端UAT
                //UserId = new Guid("1ec6beea-4403-47ac-9bf5-08150d1eb98c"),
                //UserName = "s_moon"
                //UserId = new Guid("631f21d8-e183-454a-9959-738a45af1820"),
                //UserName = "s_service_xiejh_all"
                //UserId = new Guid("31f37042-b1fa-447d-be52-fb9106a5fb93"),
                //UserName = "s_moon-all"
                //dev
                UserId = new Guid("5EBB84F4-CC10-4103-B72E-D8B40D784665"),
                UserName = "shenpengfei"
            };
        }
    }
}
