﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Inputs
{
    public class ProjectListInput : BDSBaseInput
    {
        /// <summary>
        /// 是否需要规则
        /// </summary>
        public bool? IsNeedRuleconfig { get; set; } = true;
        /// <summary>
        /// 项目名称
        /// </summary>
        public string projectName { get; set; }
    }
}
