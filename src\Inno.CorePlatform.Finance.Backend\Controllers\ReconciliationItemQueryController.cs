﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationItemQueryController : BaseController
    {
        public readonly IStoreApiClient _storeApiClient;
        public readonly IPurchaseApiClient _purchaseApiClient;
        public readonly ISellApiClient _sellApiClient;
        public readonly ITinyApiClient _tinyApiClient;
        public readonly ISginyApiClient _sginyApiClient;
        public readonly IReconciliationItemQueryService _reconciliationItemQueryService;
        public readonly IReconciliationItemAppService _reconciliationItemAppService;
        public readonly IReconciliationIncomeQueryService _reconciliationIncomeQueryService;
        public readonly IReconciliationStockQueryService _reconciliationStockQueryService;
        public readonly IPCApiClient _pCApiClient;

        public ReconciliationItemQueryController(
            IStoreApiClient storeApiClient,
            ISellApiClient sellApiClient,
            ITinyApiClient tinyApiClient,
            IPCApiClient pCApiClient,
            IPurchaseApiClient purchaseApiClient,
            ISginyApiClient sginyApiClient,
            IReconciliationIncomeQueryService reconciliationIncomeQueryService,
            IReconciliationStockQueryService reconciliationStockQueryService,
            IReconciliationItemAppService reconciliationItemAppService,
            IReconciliationItemQueryService reconciliationItemQueryService, ISubLogService subLog) : base(subLog)
        {
            this._storeApiClient = storeApiClient;
            this._reconciliationItemQueryService = reconciliationItemQueryService;
            this._purchaseApiClient = purchaseApiClient;
            this._sellApiClient = sellApiClient;
            this._tinyApiClient = tinyApiClient;
            this._sginyApiClient = sginyApiClient;
            this._pCApiClient = pCApiClient;
            this._reconciliationItemAppService = reconciliationItemAppService;
            this._reconciliationIncomeQueryService = reconciliationIncomeQueryService;
            this._reconciliationStockQueryService = reconciliationStockQueryService;
        }

        #region 接口调试
        /// <summary>
        /// 库存-获取存货对账信息数据
        /// </summary> 
        /// <returns></returns>
        [HttpGet("QueryReconForFm")]
        public async Task<QueryReconForFmOutput> QueryReconForFm()
        {
            return await _storeApiClient.QueryReconForFm(new Application.DTOs.Reconciliation.ReconciliationInput
            {
                CompanyId = Guid.Parse("1b848187-8675-49fe-94f7-90bfb498bd81"),
                SysMonth = "2023-09"
            });
        }
        /// <summary>
        /// 销售-对账成本数据
        /// </summary> 
        /// <returns></returns>
        [HttpGet("SaleForReconciliation")]
        public async Task<IList<ReconciliationOutput>> SaleForReconciliation()
        {
            return await _sellApiClient.SaleForReconciliation_revise(new Application.DTOs.Reconciliation.ReconciliationInput
            {
                CompanyId = Guid.Parse("65B22DE2-8C1D-4947-9022-4FCB0692CE09"),
                SysMonth = "2024-01",
                BillType = 5
            });
        }

        /// <summary>
        /// 采购-获取经销购货修订已收部分数据
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetRevisionPurchaseOrderList")]
        public async Task<BasePageResult<ReconciliationOutput>> GetRevisionPurchaseOrderList()
        {
            return await _purchaseApiClient.GetRevisionPurchaseOrderList(new Application.DTOs.Reconciliation.ReconciliationInput
            {
                CompanyId = Guid.Parse("1b848187-8675-49fe-94f7-90bfb498bd81"),
                SysMonth = "2023-09"
            });
        }

        /// <summary>
        /// 暂存-财务成本统计
        /// </summary> 
        /// <returns></returns>
        [HttpGet("FinanceCostStatistics")]
        public async Task<List<ReconciliationOutput>> FinanceCostStatistics()
        {
            return await _tinyApiClient.FinanceCostStatistics(new Application.DTOs.Reconciliation.ReconciliationInput
            {
                CompanyId = Guid.Parse("1b848187-8675-49fe-94f7-90bfb498bd81"),
                SysMonth = "2023-09"
            });
        }

        /// <summary>
        ///跟台-财务获取盘点统计数据
        /// </summary> 
        /// <returns></returns>
        [HttpGet("FinanceSummary")]
        public async Task<List<ReconciliationOutput>> FinanceSummary()
        {
            return await _sginyApiClient.FinanceSummary(new Application.DTOs.Reconciliation.ReconciliationInput
            {
                CompanyId = Guid.Parse("1b848187-8675-49fe-94f7-90bfb498bd81"),
                SysMonth = "2023-09"
            });
        }
        #endregion

        /// <summary>
        /// 拉取远程数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("PullRemoteData")]
        public async Task<BaseResponseData<string>> PullRemoteData(ReconciliationInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var reconciliationStockDetails = new List<ReconciliationOutput>();
            var reconciliationIncomeDetails = new List<ReconciliationOutput>();

            try
            {
                // 提取公共方法：设置基础属性
                void SetBaseProperties(IEnumerable<ReconciliationOutput> items, string billTypeStr, int? mark = null)
                {
                    if (items == null || !items.Any()) return;
                    foreach (var item in items)
                    {
                        item.BillTypeStr = billTypeStr;
                        item.CompanyName = input.CompanyName;
                        if (mark.HasValue)
                        {
                            item.Mark = mark.Value;
                        }
                    }
                    reconciliationStockDetails.AddRange(items);
                }

                // 库存-获取存货对账信息数据
                var reconForFmOutput = await _storeApiClient.QueryReconForFm(input);
                if (reconForFmOutput != null)
                {
                    SetBaseProperties(reconForFmOutput.consignSendGoods, "发出商品(寄售)");
                    SetBaseProperties(reconForFmOutput.consignStoreGoods, "库存商品(寄售)");
                    SetBaseProperties(reconForFmOutput.sellSendGoods, "发出商品(经销+进口经销)");
                    SetBaseProperties(reconForFmOutput.sellStoreGoods, "库存商品(经销)");
                    SetBaseProperties(reconForFmOutput.sellStoreImportGoods, "库存商品(进口)");
                }

                // 跟台-财务获取盘点统计数据-存货
                var reconciliations_sginy = await _sginyApiClient.FinanceSummary(input);
                if (reconciliations_sginy != null && reconciliations_sginy.Any())
                {
                    reconciliations_sginy.ForEach(p => p.CompanyName = input.CompanyName);
                    reconciliationStockDetails.AddRange(reconciliations_sginy);
                }

                // 暂存-财务成本统计-存货
                var reconciliations_tiny = await _tinyApiClient.FinanceCostStatistics(input);
                if (reconciliations_tiny != null && reconciliations_tiny.Any())
                {
                    reconciliations_tiny.ForEach(p =>
                    {
                        p.BillTypeStr = p.Mark == 1 ? "发出商品(寄售)" : "发出商品(经销+进口经销)";
                        p.CompanyName = input.CompanyName;
                    });
                    reconciliationStockDetails.AddRange(reconciliations_tiny);
                }

                // 库存-查询收入成本对账
                var reconciliationOutputs = await _storeApiClient.QueryIncomeCostRec(input);
                if (reconciliationOutputs != null)
                {
                    if (reconciliationOutputs.receivableCosts.Any())
                    {
                        reconciliationOutputs.receivableCosts.ForEach(p => p.CompanyName = input.CompanyName);
                        reconciliationIncomeDetails.AddRange(reconciliationOutputs.receivableCosts);
                    }

                    if (reconciliationOutputs.sellSendGoods.Any())
                    {
                        reconciliationOutputs.sellSendGoods.ForEach(p =>
                        {
                            p.CompanyName = input.CompanyName;
                            p.ChangeAmount = p.CostOfNoTax;
                            p.Mark = 0;
                        });
                        reconciliationStockDetails.AddRange(reconciliationOutputs.sellSendGoods);
                    }

                    if (reconciliationOutputs.consignSendGoods.Any())
                    {
                        reconciliationOutputs.consignSendGoods.ForEach(p =>
                        {
                            p.CompanyName = input.CompanyName;
                            p.ChangeAmount = p.CostOfNoTax;
                            p.Mark = 1;
                        });
                        reconciliationStockDetails.AddRange(reconciliationOutputs.consignSendGoods);
                    }
                }

                // 销售暂存核销-查询收入成本对账-暂存核销
                var reconciliations_sale = await _sellApiClient.SaleForReconciliation(input);
                if (reconciliations_sale != null && reconciliations_sale.Any())
                {
                    reconciliations_sale.ForEach(p =>
                    {
                        p.CompanyName = input.CompanyName;
                        p.ChangeAmount = p.CostOfNoTax;
                    });
                    reconciliationStockDetails.AddRange(reconciliations_sale);
                    reconciliationIncomeDetails.AddRange(reconciliations_sale);
                }

                // 销售订单修订-查询收入成本对账
                var reconciliations_salerevise = await _sellApiClient.SaleForReconciliation_revise(input);
                if (reconciliations_salerevise != null && reconciliations_salerevise.Any())
                {
                    reconciliations_salerevise.ForEach(p =>
                    {
                        p.Cost = 0;
                        p.CostOfNoTax = 0;
                        p.CompanyName = input.CompanyName;
                    });
                    reconciliationIncomeDetails.AddRange(reconciliations_salerevise);
                }

                // 销售跟台核销-查询收入成本对账(跟台核销-发生额)
                var reconciliations_follow = await _sellApiClient.SaleForReconciliation_follow(input);
                if (reconciliations_follow != null && reconciliations_follow.Any())
                {
                    reconciliations_follow.ForEach(p =>
                    {
                        p.CompanyName = input.CompanyName;
                        p.ChangeAmount = p.CostOfNoTax;
                    });
                    reconciliationStockDetails.AddRange(reconciliations_follow);
                    reconciliationIncomeDetails.AddRange(reconciliations_follow);
                }

                // 销售服务费-查询收入成本对账
                var reconciliations_service = await _sellApiClient.SaleForReconciliation_service(input);
                if (reconciliations_service != null && reconciliations_service.Any())
                {
                    reconciliations_service.ForEach(p =>
                    {
                        p.CompanyName = input.CompanyName;
                        p.ChangeAmount = p.CostOfNoTax;
                    });
                    reconciliationStockDetails.AddRange(reconciliations_service);
                    reconciliationIncomeDetails.AddRange(reconciliations_service);
                }

                // 采购-获取经销购货修订已收部分数据-收入成本(发生额)
                var revisionPurchaseOrderList = await _purchaseApiClient.GetRevisionPurchaseOrderList(input);
                if (revisionPurchaseOrderList != null && revisionPurchaseOrderList.List.Any())
                {
                    revisionPurchaseOrderList.List.ForEach(p =>
                    {
                        p.BillTypeStr = "采购" + p.BillTypeStr;
                        p.CompanyName = input.CompanyName;
                        p.ChangeAmount = p.InventoryChangeAmountOfNoTax + p.CostOfNoTax;
                    });

                    reconciliationIncomeDetails.AddRange(revisionPurchaseOrderList.List.Where(p => p.CostOfNoTax.HasValue && p.CostOfNoTax.Value != 0));
                    reconciliationStockDetails.AddRange(revisionPurchaseOrderList.List);
                }

                // 处理收入明细
                var detailInput = new ReconciliationDetailsInput
                {
                    ReconciliationItemId = input.ReconciliationItemId,
                };

                if (reconciliationIncomeDetails.Any())
                {
                    reconciliationIncomeDetails.ForEach(p =>
                    {
                        if (!string.IsNullOrEmpty(p.BillTypeStr) &&
                            (p.BillTypeStr.Equals("销售调回") || p.BillTypeStr.Equals("退货入库") || p.BillTypeStr.Equals("销售换入")))
                        {
                            p.Income = p.Income > 0 ? -p.Income : p.Income;
                            p.IncomeOfNoTax = p.IncomeOfNoTax > 0 ? -p.IncomeOfNoTax : p.IncomeOfNoTax;
                            p.Cost = p.Cost > 0 ? -p.Cost : p.Cost;
                            p.CostOfNoTax = p.CostOfNoTax > 0 ? -p.CostOfNoTax : p.CostOfNoTax;
                        }
                    });
                    detailInput.IncomeDetails = reconciliationIncomeDetails;
                }

                if (reconciliationStockDetails.Any())
                {
                    detailInput.StockDetails = reconciliationStockDetails;
                }

                ret = await _reconciliationItemAppService.AddDetails(detailInput);
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，原因：" + ex.Message);
            }
        }


        [HttpPost("GetList")]
        public async Task<BaseResponseData<PageResponse<ReconciliationItemOutput>>> GetListPages([FromBody] ReconciliationItemInput input)
        {
            var strategyInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    return new BaseResponseData<PageResponse<ReconciliationItemOutput>>
                    {
                        Data = new PageResponse<ReconciliationItemOutput>
                        {
                            List = new List<ReconciliationItemOutput>(),
                            Total = 0
                        }
                    };
                }
            }
            input.UserId = CurrentUser.Id;
            var result = await _reconciliationItemQueryService.GetListPages(input);
            var res = new BaseResponseData<PageResponse<ReconciliationItemOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationItemOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetExportList")]
        public async Task<IActionResult> GetExportList([FromBody] ReconciliationItemExportInput input)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var stream = new MemoryStream();
                using (var package = new ExcelPackage(stream))
                {
                    #region 收入成本-公司 
                    var worksheet1 = package.Workbook.Worksheets.Add("收入成本-公司");
                    worksheet1.Cells[1, 1].Value = "公司";
                    worksheet1.Cells[1, 2].Value = "含税收入";
                    worksheet1.Cells[1, 3].Value = "不含税收入";
                    worksheet1.Cells[1, 4].Value = "含税成本";
                    worksheet1.Cells[1, 5].Value = "不含税成本";

                    var companyData = await _reconciliationIncomeQueryService.GetListExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (companyData.List != null && companyData.List.Any())
                    {
                        int row = 2;
                        foreach (var item in companyData.List)
                        {
                            worksheet1.Cells[row, 2].Style.Numberformat.Format = "#,##0.00";
                            worksheet1.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";
                            worksheet1.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                            worksheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";

                            worksheet1.Cells[row, 1].Value = input.CompanyName;
                            worksheet1.Cells[row, 2].Value = item.Income.HasValue ? decimal.Parse(item.Income.Value.ToString("#0.00")) : 0;
                            worksheet1.Cells[row, 3].Value = item.IncomeOfNoTax.HasValue ? decimal.Parse(item.IncomeOfNoTax.Value.ToString("#0.00")) : 0;
                            worksheet1.Cells[row, 4].Value = item.Cost.HasValue ? decimal.Parse(item.Cost.Value.ToString("#0.00")) : 0;
                            worksheet1.Cells[row, 5].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet1.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 收入成本-客户 
                    var worksheet2 = package.Workbook.Worksheets.Add("收入成本-客户");
                    worksheet2.Cells[1, 1].Value = "公司";
                    worksheet2.Cells[1, 2].Value = "客户";
                    worksheet2.Cells[1, 3].Value = "含税收入";
                    worksheet2.Cells[1, 4].Value = "不含税收入";
                    worksheet2.Cells[1, 5].Value = "含税成本";
                    worksheet2.Cells[1, 6].Value = "不含税成本";

                    var customerData = await _reconciliationIncomeQueryService.GetListOfCustomerExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (customerData.List != null && customerData.List.Any())
                    {
                        int row = 2;
                        foreach (var item in customerData.List)
                        {
                            worksheet2.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";
                            worksheet2.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                            worksheet2.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                            worksheet2.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";

                            worksheet2.Cells[row, 1].Value = input.CompanyName;
                            worksheet2.Cells[row, 2].Value = item.CustomerName;
                            worksheet2.Cells[row, 3].Value = item.Income.HasValue ? decimal.Parse(item.Income.Value.ToString("#0.00")) : 0;
                            worksheet2.Cells[row, 4].Value = item.IncomeOfNoTax.HasValue ? decimal.Parse(item.IncomeOfNoTax.Value.ToString("#0.00")) : 0;
                            worksheet2.Cells[row, 5].Value = item.Cost.HasValue ? decimal.Parse(item.Cost.Value.ToString("#0.00")) : 0;
                            worksheet2.Cells[row, 6].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet2.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 收入成本-订单
                    var worksheet3 = package.Workbook.Worksheets.Add("收入成本-订单");
                    worksheet3.Cells[1, 1].Value = "公司";
                    worksheet3.Cells[1, 2].Value = "客户";
                    worksheet3.Cells[1, 3].Value = "业务类型";
                    worksheet3.Cells[1, 4].Value = "业务单据号";
                    worksheet3.Cells[1, 5].Value = "订单号";
                    worksheet3.Cells[1, 6].Value = "供应商";
                    worksheet3.Cells[1, 7].Value = "含税收入";
                    worksheet3.Cells[1, 8].Value = "不含税收入";
                    worksheet3.Cells[1, 9].Value = "含税成本";
                    worksheet3.Cells[1, 10].Value = "不含税成本";

                    var billData = await _reconciliationIncomeQueryService.GetListOfBillExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (billData.List != null && billData.List.Any())
                    {
                        int row = 2;
                        foreach (var item in billData.List)
                        {
                            worksheet3.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                            worksheet3.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                            worksheet3.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                            worksheet3.Cells[row, 10].Style.Numberformat.Format = "#,##0.00";

                            worksheet3.Cells[row, 1].Value = input.CompanyName;
                            worksheet3.Cells[row, 2].Value = item.CustomerName;
                            worksheet3.Cells[row, 3].Value = item.BillTypeStr;
                            worksheet3.Cells[row, 4].Value = item.SaleOrderNo;
                            worksheet3.Cells[row, 5].Value = item.OrderNo;
                            worksheet3.Cells[row, 6].Value = item.AgentName;
                            worksheet3.Cells[row, 7].Value = item.Income.HasValue ? decimal.Parse(item.Income.Value.ToString("#0.00")) : 0;
                            worksheet3.Cells[row, 8].Value = item.IncomeOfNoTax.HasValue ? decimal.Parse(item.IncomeOfNoTax.Value.ToString("#0.00")) : 0;
                            worksheet3.Cells[row, 9].Value = item.Cost.HasValue ? decimal.Parse(item.Cost.Value.ToString("#0.00")) : 0;
                            worksheet3.Cells[row, 10].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet3.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 收入成本-订单供应商 
                    var worksheet4 = package.Workbook.Worksheets.Add("收入成本-订单供应商");
                    worksheet4.Cells[1, 1].Value = "公司";
                    worksheet4.Cells[1, 2].Value = "供应商";
                    worksheet4.Cells[1, 3].Value = "业务类型";
                    worksheet4.Cells[1, 4].Value = "业务单据号";
                    worksheet4.Cells[1, 5].Value = "订单号";
                    worksheet4.Cells[1, 6].Value = "含税收入";
                    worksheet4.Cells[1, 7].Value = "不含税收入";
                    worksheet4.Cells[1, 8].Value = "含税成本";
                    worksheet4.Cells[1, 9].Value = "不含税成本";

                    var agentBillData = await _reconciliationIncomeQueryService.GetListOfAgentBillExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (agentBillData.List != null && agentBillData.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentBillData.List)
                        {
                            worksheet4.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                            worksheet4.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                            worksheet4.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                            worksheet4.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";

                            worksheet4.Cells[row, 1].Value = input.CompanyName;
                            worksheet4.Cells[row, 2].Value = item.AgentName;
                            worksheet4.Cells[row, 3].Value = item.BillTypeStr;
                            worksheet4.Cells[row, 4].Value = item.SaleOrderNo;
                            worksheet4.Cells[row, 5].Value = item.OrderNo;
                            worksheet4.Cells[row, 6].Value = item.Income.HasValue ? decimal.Parse(item.Income.Value.ToString("#0.00")) : 0;
                            worksheet4.Cells[row, 7].Value = item.IncomeOfNoTax.HasValue ? decimal.Parse(item.IncomeOfNoTax.Value.ToString("#0.00")) : 0;
                            worksheet4.Cells[row, 8].Value = item.Cost.HasValue ? decimal.Parse(item.Cost.Value.ToString("#0.00")) : 0;
                            worksheet4.Cells[row, 9].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet4.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-公司 
                    var worksheet5 = package.Workbook.Worksheets.Add("存货-公司");
                    worksheet5.Cells[1, 1].Value = "公司";
                    worksheet5.Cells[1, 2].Value = "科目";
                    worksheet5.Cells[1, 3].Value = "不含税金额";

                    var companyDataStock = await _reconciliationStockQueryService.GetListOfCompanyExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (companyDataStock.List != null && companyDataStock.List.Any())
                    {
                        int row = 2;
                        foreach (var item in companyDataStock.List)
                        {
                            worksheet5.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";

                            worksheet5.Cells[row, 1].Value = input.CompanyName;
                            worksheet5.Cells[row, 2].Value = item.BillTypeStr;
                            worksheet5.Cells[row, 3].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet5.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-发出商品(寄售)
                    var worksheet6 = package.Workbook.Worksheets.Add("存货-发出商品(寄售)");
                    worksheet6.Cells[1, 1].Value = "公司";
                    worksheet6.Cells[1, 2].Value = "客户";
                    worksheet6.Cells[1, 3].Value = "供应商";
                    worksheet6.Cells[1, 4].Value = "不含税金额";

                    var agentDataStock1 = await _reconciliationStockQueryService.GetListOfAgentCustomerExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId,
                        BillTypeStr = "发出商品(寄售)"
                    });
                    if (agentDataStock1.List != null && agentDataStock1.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentDataStock1.List)
                        {
                            worksheet6.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";

                            worksheet6.Cells[row, 1].Value = input.CompanyName;
                            worksheet6.Cells[row, 2].Value = item.CustomerName;
                            worksheet6.Cells[row, 3].Value = item.AgentName;
                            worksheet6.Cells[row, 4].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet6.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-库存商品(寄售)
                    var worksheet7 = package.Workbook.Worksheets.Add("存货-库存商品(寄售)");
                    worksheet7.Cells[1, 1].Value = "公司";
                    worksheet7.Cells[1, 2].Value = "供应商";
                    worksheet7.Cells[1, 3].Value = "不含税金额";

                    var agentDataStock2 = await _reconciliationStockQueryService.GetListOfAgentExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId,
                        BillTypeStr = "库存商品(寄售)"
                    });
                    if (agentDataStock2.List != null && agentDataStock2.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentDataStock2.List)
                        {
                            worksheet7.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";

                            worksheet7.Cells[row, 1].Value = input.CompanyName;
                            worksheet7.Cells[row, 2].Value = item.AgentName;
                            worksheet7.Cells[row, 3].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet7.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-发出商品(经销+进口经销)
                    var worksheet8 = package.Workbook.Worksheets.Add("存货-发出商品(经销+进口经销)");
                    worksheet8.Cells[1, 1].Value = "公司";
                    worksheet8.Cells[1, 2].Value = "客户";
                    worksheet8.Cells[1, 3].Value = "供应商";
                    worksheet8.Cells[1, 4].Value = "不含税金额";

                    var agentDataStock3 = await _reconciliationStockQueryService.GetListOfAgentCustomerExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId,
                        BillTypeStr = "发出商品(经销+进口经销)"
                    });
                    if (agentDataStock3.List != null && agentDataStock3.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentDataStock3.List)
                        {
                            worksheet8.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";

                            worksheet8.Cells[row, 1].Value = input.CompanyName;
                            worksheet8.Cells[row, 2].Value = item.CustomerName;
                            worksheet8.Cells[row, 3].Value = item.AgentName;
                            worksheet8.Cells[row, 4].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet8.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-库存商品(经销)
                    var worksheet9 = package.Workbook.Worksheets.Add("存货-库存商品(经销)");
                    worksheet9.Cells[1, 1].Value = "公司";
                    worksheet9.Cells[1, 2].Value = "供应商";
                    worksheet9.Cells[1, 3].Value = "不含税金额";

                    var agentDataStock4 = await _reconciliationStockQueryService.GetListOfAgentExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId,
                        BillTypeStr = "库存商品(经销)"
                    });
                    if (agentDataStock4.List != null && agentDataStock4.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentDataStock4.List)
                        {
                            worksheet9.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";

                            worksheet9.Cells[row, 1].Value = input.CompanyName;
                            worksheet9.Cells[row, 2].Value = item.AgentName;
                            worksheet9.Cells[row, 3].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet9.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-库存商品(进口)
                    var worksheet10 = package.Workbook.Worksheets.Add("存货-库存商品(进口)");
                    worksheet10.Cells[1, 1].Value = "公司";
                    worksheet10.Cells[1, 2].Value = "供应商";
                    worksheet10.Cells[1, 3].Value = "不含税金额";

                    var agentDataStock5 = await _reconciliationStockQueryService.GetListOfAgentExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId,
                        BillTypeStr = "库存商品(进口)"
                    });
                    if (agentDataStock5.List != null && agentDataStock5.List.Any())
                    {
                        int row = 2;
                        foreach (var item in agentDataStock5.List)
                        {
                            worksheet10.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";

                            worksheet10.Cells[row, 1].Value = input.CompanyName;
                            worksheet10.Cells[row, 2].Value = item.AgentName;
                            worksheet10.Cells[row, 3].Value = item.CostOfNoTax.HasValue ? decimal.Parse(item.CostOfNoTax.Value.ToString("#0.00")) : 0;
                            row++;
                        }
                    }
                    worksheet10.Row(1).Style.Font.Bold = true;
                    #endregion

                    #region 存货-发生额
                    var worksheet11 = package.Workbook.Worksheets.Add("存货-发生额");
                    worksheet11.Cells[1, 1].Value = "公司";
                    worksheet11.Cells[1, 2].Value = "供应商";
                    worksheet11.Cells[1, 3].Value = "客户";
                    worksheet11.Cells[1, 4].Value = "业务类型";
                    worksheet11.Cells[1, 5].Value = "业务单据号";
                    worksheet11.Cells[1, 6].Value = "订单号";
                    worksheet11.Cells[1, 7].Value = "不含税金额";
                    worksheet11.Cells[1, 8].Value = "单据类型";
                    worksheet11.Cells[1, 9].Value = "标准成本";

                    var changeAmountDataStock = await _reconciliationStockQueryService.GetListOfChangeAmountExport(new ReconciliationItemExportInput
                    {
                        ReconciliationItemId = input.ReconciliationItemId
                    });
                    if (changeAmountDataStock.List != null && changeAmountDataStock.List.Any())
                    {
                        int row = 2;
                        foreach (var item in changeAmountDataStock.List)
                        {
                            worksheet11.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                            worksheet11.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";

                            worksheet11.Cells[row, 1].Value = input.CompanyName;
                            worksheet11.Cells[row, 2].Value = item.AgentName;
                            worksheet11.Cells[row, 3].Value = item.CustomerName;
                            worksheet11.Cells[row, 4].Value = item.BillTypeStr;
                            worksheet11.Cells[row, 5].Value = item.SaleOrderNo;
                            worksheet11.Cells[row, 6].Value = item.OrderNo;
                            worksheet11.Cells[row, 7].Value = item.ChangeAmount.HasValue ? decimal.Parse(item.ChangeAmount.Value.ToString("#0.00")) : 0;
                            worksheet11.Cells[row, 8].Value = item.Mark == 0 || item.Mark == 3 ? "经销" : "寄售";
                            worksheet11.Cells[row, 9].Value = item.StandardUnitCost;
                            row++;
                        }
                    }
                    worksheet11.Row(1).Style.Font.Bold = true;
                    #endregion 

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
