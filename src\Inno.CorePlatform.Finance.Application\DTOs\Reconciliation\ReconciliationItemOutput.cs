﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 返回列表
    /// </summary>
    public class ReconciliationItemOutput
    {
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 月度如2023-12
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? BillDate { get; set; }

        public DateTimeOffset CreatedTime { get; set; } = DateTimeOffset.UtcNow;

        [MaxLength(100)]
        public string CreatedBy { get; set; } = "none";

        /// <summary>
        /// 
        /// </summary>
        public Guid? Id { get; set; }
    }

}
