﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Abatment
{
    public class GenerateAbtForCreditInput
    {
        /// <summary>
        /// 执行冲销的发起单号 
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 单据类型 receive表示收款单，credit表示应收单，arpaysettle 表示退款单
        /// </summary>
        public string BillType { get; set; }

        /// <summary>
        /// 发起冲销的单据的单据金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 认款单号
        /// </summary>
        public string? RecognizeReceiveCode { get; set; }
        public string? userName { get; set; }
        /// <summary>
        /// 冲销的明细数据
        /// </summary>
        public List<CreditWaitAbtSubmitAbatement> lstAbtDetail { get; set; } = new List<CreditWaitAbtSubmitAbatement>();

    }

    public class CreditWaitAbtSubmitAbatement
    {
        /// <summary>
        /// 被冲销的应收单号 
        /// </summary>
        public string BillCode { get; set; }


        /// <summary>
        /// 冲销的金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 明细单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 认款明细类型
        /// </summary>
        public int? Classify { get; set; }
    }
}
