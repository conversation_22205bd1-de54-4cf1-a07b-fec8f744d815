﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class RecognizeReceiveItemRepository : EfBaseRepository<Guid, RecognizeReceiveItem, RecognizeReceiveItemPo>, IRecognizeReceiveItemRepository
    {
        private readonly FinanceDbContext _db;
        public RecognizeReceiveItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override async Task<int> UpdateAsync(RecognizeReceiveItem root)
        {
            var isExist = await _db.RecognizeReceiveItems.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new AppServiceException("认款单不存在！");
            }

            var po = root.Adapt<RecognizeReceiveItemPo>();

            _db.RecognizeReceiveItems.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();

        }

        public override async Task<int> DeleteAsync(Guid id)
        {
            var isExist = await _db.RecognizeReceiveItems.AnyAsync(x => x.Id == id);
            if (isExist == false)
            {
                throw new AppServiceException("认款单不存在！");
            }
            var po = await GetPoWithIncludeAsync(id);

            if (po.Status != (int)Domain.RecognizeReceiveItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("认款单不是临时草稿状态，无法删除");
            }

            if (po.RecognizeReceiveDetails.Count > 0)
            {
                _db.RecognizeReceiveDetails.RemoveRange(po.RecognizeReceiveDetails);
            }
            if (po.RecognizeReceiveTempDetails.Count > 0)
            {
                _db.RecognizeReceiveTempDetails.RemoveRange(po.RecognizeReceiveTempDetails);
            }

            _db.RecognizeReceiveItems.Remove(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        protected override RecognizeReceiveItemPo CreateDeletingPo(Guid id)
        {
            return new RecognizeReceiveItemPo { Id = id };
        }

        protected override async Task<RecognizeReceiveItemPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<RecognizeReceiveItem> GetWithNoTrackAsync(Guid id)
        {
                var po = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
                return po.Adapt<RecognizeReceiveItem>();
        }
        public async Task<RecognizeReceiveItem> GetWithNoTrackAsyncByReceiveCode(string code)
        {
            var po = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails).Include(x => x.RecognizeReceiveTempDetails).AsNoTracking().FirstOrDefaultAsync(t => t.Code == code);
            return po.Adapt<RecognizeReceiveItem>();
        }

        public async Task<int> UpdateManyAsync(List<RecognizeReceiveItem> lstItem)
        {
            var lstPo = lstItem.Adapt<List<RecognizeReceiveItemPo>>();

            _db.RecognizeReceiveItems.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }
        public async Task<bool> IsExist(String receiveCode)
        {
            var res = _db.RecognizeReceiveItems.Where(t => t.ReceiveCode == receiveCode && t.Status != RecognizeReceiveItemStatusEnum.Completed && t.Status != RecognizeReceiveItemStatusEnum.Canceled);
            return await res.AnyAsync();
        }
    }
}
