﻿using Inno.CorePlatform.Common.CompetenceCenter.Enums;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Data.Enums;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Newtonsoft.Json;
using Npoi.Mapper;
using NPOI.Util;
using Polly;
using RelateCodeTypeEnums = Inno.CorePlatform.Common.CompetenceCenter.Enums.RelateCodeTypeEnums;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 经销调出
    /// </summary>
    public class StoreOutNormalReturnAppService : BaseAppService, IStoreOutNormalReturnAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IStoreInApplyApiClient _storeInApplyApiClient;
        public StoreOutNormalReturnAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            IPurchaseApiClient purchaseApiClient,
            IAppServiceContextAccessor? contextAccessor,
            IStoreInApplyApiClient storeInApplyApiClient,
            Func<int, TimeSpan> sleepDurationProvider = null
            ) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._bDSApiClient = bDSApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._purchaseApiClient = purchaseApiClient;
            this._sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
            this._storeInApplyApiClient = storeInApplyApiClient;
        }


        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreOutOutput storeout = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);

                    if (storeout == null || !storeout.Details.Any())
                    {
                        throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                    if (string.IsNullOrEmpty(storeout.checker))
                    {
                        throw new Exception("该单据没有复核人");
                    }
                });
                var requestBody = JsonConvert.SerializeObject(input);

                var ret = await CreateDebtForPurchaseReturnStoreOut(storeout, input.BusinessSubType, requestBody, input.useBillDate, input.IsAutoBill);
                return ret;
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应付代码出错");
            }
        }
        /// <summary>
        /// 经销采退应付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateDebtForPurchaseReturnStoreOut(InventoryStoreOutOutput input,
            string classify,
            string preRequestBody,
            bool? useBillDate = false,
            bool? isAutoBill = false)
        {
            var check = await base.IsCreatedDebtForBill(input.storeOutCode);
            if (check)
            {
                if (isAutoBill.HasValue && isAutoBill.Value)
                {
                    return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                }
                else
                {
                    throw new Exception("该单据已生成过应付");
                }
            }
            var ret = BaseResponseData<int>.Success("操作成功");
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            if (companyInfo != null)
            {
                var serviceIds = input.Details.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
                var services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                {
                    ids = serviceIds.Select(p => p.ToString()).ToList()
                });
                var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var groupDetails = input.Details.GroupBy(p => new { p.businessUnitId, p.projectId, p.purchaseOrderCode, p.agentId });
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                var agents = input.agentId != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { input.agentId.Value }) : null;

                string? orderNo = null;
                if (input.relateCodeType.HasValue && input.relateCodeType == (int)RelateCodeTypeEnums.ReturnSalesApply && !string.IsNullOrEmpty(input.relateCode))
                {
                    var storein = await _storeInApplyApiClient.GetList(new DTOs.StoreInApply.StoreInApplyGetInput()
                    {
                        code = input.relateCode,
                        arrivalStatusEnums = new List<int>()
                    });
                    if (storein != null && storein.list.Any() && !string.IsNullOrEmpty(storein.list.FirstOrDefault()?.relateCode))
                    {
                        if (storein.list.First().relateCode.Length > 190)
                        {
                            orderNo = storein.list.First().relateCode.Substring(0, 190);
                        }
                        else
                        {
                            orderNo = storein.list.First().relateCode;
                        }
                    }
                }
                var index = 1;
                var insertList = new List<DebtDto>();
                var kingdeeDebts = new List<KingdeeDebt>();
                foreach (var g in groupDetails)
                {
                    PurchaseQueryInfoSimpleOutput purchaseOrder = null;
                    if (!string.IsNullOrEmpty(g.Key.purchaseOrderCode))
                    {
                        purchaseOrder = await _purchaseApiClient.GetSimpleByCode(g.Key.purchaseOrderCode);
                    }
                    var thisProjectInfo = projectInfo.FirstOrDefault(t => t.Id == g.Key.projectId);
                    var coinName = string.IsNullOrEmpty(g.First().coinAttribute) || g.First().coinAttribute == "CNY" ? "CNY" : g.First().coinAttribute;
                    var debt = new DebtDto
                    {
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                        BillDate = (useBillDate.HasValue && useBillDate.Value ? DateTimeHelper.LongToDateTime(input.billDate) : billDate),
                        CreatedBy = purchaseOrder == null ? input.checker : purchaseOrder.CreatedBy,
                        CreatedTime = DateTimeOffset.UtcNow,
                        Id = Guid.NewGuid(),
                        Value = Math.Round(g.Sum(p => p.quantity * ((p.settlementCost.HasValue ? p.settlementCost : (coinName == "CNY" ? p.stockUnitCost : p.originCost)) ?? 0)), 2),
                        AgentId = g.Key.agentId,
                        AgentName = input.agentName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        CompanyId = input.companyId,
                        CompanyName = companyInfo.companyName,
                        RelateCode = input.storeOutCode,
                        DebtType = DebtTypeEnum.selfreturn,
                        NameCode = companyInfo.nameCode,
                        ServiceId = g.Key.businessUnitId,
                        BusinessDeptFullName = input.businessDeptFullName,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptId = input.businessDeptId.ToString(),
                        ProjectId = g.Key.projectId,
                        ProjectCode = thisProjectInfo?.Code,
                        ProjectName = thisProjectInfo?.Name,
                        OrderNo = orderNo ?? g.Key.purchaseOrderCode,
                        PurchaseCode = g.Key.purchaseOrderCode,
                        ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                        PurchaseContactNo = purchaseOrder?.Contract?.Code,
                        RMBAmount = -(g.Sum(p => p.quantity * (p.rmbAmount ?? 0))),
                        CoinCode = coinName,
                        CoinName = string.IsNullOrEmpty(g.First().coinName) || g.First().coinAttribute == "CNY" ? "人民币" : g.First().coinName,
                        Mark = input.Details.First().mark,
                        IsInternalTransactions = Utility.IsInternalTransactions(input.relateCodeType),
                    };
                    if (g.Key.businessUnitId.HasValue)
                    {
                        debt.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.Value.ToString().ToLower())?.name;
                    }
                    if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                    {
                        debt.RebateNo = purchaseOrder.RelateCode;
                    }
                    if (debt.Value > 0)
                    {
                        debt.Value = -debt.Value;
                    }
                    if (debt.Value < 0 && debt.RMBAmount > 0)
                    {
                        debt.RMBAmount = -debt.RMBAmount;
                    }
                    if (debt.Value != 0)
                    {
                        insertList.Add(debt);

                        #region 包装金蝶应付参数
                        InitKingdeeDebt(productNameInfos, kingdeeDebts, g, debt, input);
                        #endregion
                    }

                    index++;
                }

                if (kingdeeDebts.Count > 0)
                {
                    var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        if (insertList != null && insertList.Count > 0)
                        {
                            await base.CreateManyDebts(insertList);
                            await _unitOfWork.CommitAsync();
                            var debtIds = insertList.Select(p => p.Id).Distinct().ToList();
                            await _debtRepository.RepaireDebtDiff(debtIds);
                        }
                    }
                    else
                    {
                        throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }

                #region 处理凭证
                if (input.Details.Any(p => p.settlementCost.HasValue) && !input.Details.All(p => Math.Abs(p.stockUnitCost ?? 0) == Math.Abs(p.settlementCost ?? 0)))
                {
                    await PushToKingdee(input, productNameInfos, projectInfo, classify, preRequestBody);
                }
                #endregion
            }
            else
            {
                throw new Exception("未找到对应公司公司");
            }
            return ret;
        }

        private static void InitKingdeeDebt(List<ProductNameInfoOutput> productNameInfos, List<KingdeeDebt> kingdeeDebts, IGrouping<object, Detail> g, DebtDto debt, InventoryStoreOutOutput input)
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = input.storeOutCode, //debt.PurchaseCode,
                jfzx_creator = debt.CreatedBy ?? "none",
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                currency_number = debt.CoinCode ?? "CNY",
                pricetaxtotal4 = debt.Value,
            };
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
            var amount = 0m;
            g.ToList().GroupBy(a => new { a.productId, a.originCost, a.taxRate, a.rmbAmount }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                d.taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? t.Key.taxRate.Value : 0;
                d.quantity = t.Sum(b => b.quantity) * -1;

                if (t.FirstOrDefault()?.settlementCost.HasValue==true)
                {
                    d.pricetax = Math.Abs(t.FirstOrDefault().settlementCost.Value);
                }
                else if(t.Key.originCost.HasValue)
                {
                    d.pricetax = Math.Abs(t.Key.originCost.Value);
                }
                else
                {
                    throw new Exception("originCost为空，请库存检查");
                }
                d.e_amountbaseMany = Math.Abs(t.Key.rmbAmount.HasValue ? t.Key.rmbAmount.Value * Math.Abs(d.quantity) : 0);
                if (kingdeeDebt.pricetaxtotal4 < 0)
                {
                    d.e_amountbaseMany = -Math.Abs(d.e_amountbaseMany);
                }
                d.jfzx_project_number = debt.ProjectCode;//项目单号 预留
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == t.First().productNameId);
                if (thisProductInfo.classificationNewGuid.HasValue)
                {
                    d.material_number1 = thisProductInfo.classificationNewGuid.ToString();
                }
                else
                {
                    d.material_number1 = thisProductInfo.classificationGuid.ToString();
                }
                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Abs(Math.Round((d.pricetax / (1 + d.taxrate / 100.00M)), 20));
                amount += d.price2 * d.quantity;
            });
            //应付不含税总额
            kingdeeDebt.amount2 = kingdeeDebt.pricetaxtotal4 < 0 ? -Math.Abs(Math.Round(amount, 2)) : Math.Abs(Math.Round(amount, 2));
            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);

        }

        private async Task<BaseResponseData<int>> PushToKingdee(
            InventoryStoreOutOutput input,
            List<ProductNameInfoOutput> productNameInfos,
            List<ProjectInfo> projectInfos,
            string classify,
            string preRequestBody)
        {

            var inputKD = new HoldStockRemovalInput()
            {
                billno = input.storeOutCode,
                jfzx_date = DateTimeHelper.LongToDateTime(input.billDate),
                jfzx_tallydate = DateTimeHelper.LongToDateTime(input.billDate),
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = input.companyName,
                jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = input.createdBy ?? "none",
                StoreOutType = 20
            };
            if (!string.IsNullOrEmpty(input.signSysMonth))
            {
                inputKD.jfzx_tallydate = DateTime.Parse(input.signSysMonth);
            }
            inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
            var details = input.Details;
            var fk_jfzx_totalsalescost = 0m;

            foreach (var detail in details)
            {
                var thisProject = projectInfos.FirstOrDefault(t => t.Id == detail.projectId);
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == detail.productNameId);
                var jfzx_material = Guid.Empty;
                if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationNewGuid.Value;
                }
                else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationGuid.Value;
                }
                //stockunitCost-unitCost
                var taxCost = detail.mark == 0 || detail.mark == 3 ? (detail.stockUnitCost.Value - Math.Abs(detail.unitCost.Value)) : detail.standardUnitCost.Value - Math.Abs(detail.unitCost.Value);
                //var taxCost = detail.unitCost.Value;
                var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                //销售成本总额
                var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                if (detail.mark == 0 || detail.mark == 3)
                {
                    fk_jfzx_totalsalescost += noTaxCost10 * detail.quantity;
                }
                else
                {
                    fk_jfzx_totalsalescost += detail.standardUnitCost.Value * detail.quantity;
                }
                var detailInfo = new HoldStockRemovalDetail
                {
                    jfzx_count = detail.quantity,
                    jfzx_material = jfzx_material.ToString().ToUpper(),
                    jfzx_model = detail.specification,
                    jfzx_projectnos = thisProject?.Code,
                    Mark = detail.mark,
                    jfzx_suppliers = detail.agentId.Value.ToString().ToUpper(),
                    jfzx_unitprice = detail.mark == 0 || detail.mark == 3 ? noTaxCost : detail.standardUnitCost.Value - Math.Abs(detail.unitCost.Value),
                };
                detailInfo.jfzx_sellingcost = detailInfo.jfzx_unitprice * detail.quantity;
                if (detail.stockUnitCost != detail.settlementCost && detailInfo.jfzx_unitprice != 0)
                {
                    inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                }

            }
            inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
            if (inputKD.holdStockRemovalEntrysModel.Count > 0)
            {
                var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(new List<HoldStockRemovalInput> { inputKD }, classify, preRequestBody);
                if (kingdeeRes.Code != CodeStatusEnum.Success)
                {
                    throw new Exception(kingdeeRes.Message);
                }
            }
            return BaseResponseData<int>.Success("操作成功");
        }
    }
}
