﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 查询采购子系统配置返回
    /// </summary>
    public class SubSysRelaQueryOutput
    {
        public List<ListItem>? List { get; set; }
        public List<ListItem>? Data { get; set; }
        public Footer? Footer { get; set; }
        public int? Total { get; set; }
    }

    public class ListItem
    {
        public Guid? Id { get; set; }
        public DateTime? CreatedTime { get; set; }
        public DateTime? UpdatedTime { get; set; }
        public DateTime? DeletedTime { get; set; }
        public string? CreatedBy { get; set; }
        public string? DisplayCreateBy { get; set; }
        public string? UpdatedBy { get; set; }
        public string? DisplayUpdatedBy { get; set; }
        public string? DeletedBy { get; set; }
        public bool? IsDeleted { get; set; }
        public int? Status { get; set; }
        public string? StatusName { get; set; }
        public string? CompanyId { get; set; }
        public string? AgentId { get; set; }
        public string? ServiceId { get; set; }
        public string? CustomerId { get; set; }
        public string? DeptId { get; set; }
        public AuditControls? AuditControls { get; set; }
        public List<ProductNameAuth>? ProductNameAuths { get; set; }
    }


    public class AuditControls
    {
        public int? PurchaseOrder { get; set; }
        public int? WriteOffOrder { get; set; }
        public int? PurchaseBack { get; set; }
        public int? HospitalSend { get; set; }
        public int? TempStorePlate { get; set; }
        public int? TempStoreReturnApply { get; set; }
        public int? ReturnStoreInApply { get; set; }
        public int? InvoiceSend { get; set; }
        public int? SaleOrder { get; set; }
        public int? TempStoreOutApply { get; set; }
        public int? TempStoreTransfer { get; set; }
        public int? BTypeReturnApply { get; set; }
        public int? ChangeStoreInOrder { get; set; }
        public int? ConsignStoreInOrder { get; set; }
        public int? ConsignOutOrder { get; set; }
        public int? PuaStoreInOrder { get; set; }
        public int? SaleReturnOrder { get; set; }
        public int? BTypeSellStoreOut { get; set; }
        public int? BTypeTempStoreOut { get; set; }
        public int? FollowPlatform { get; set; }
        public int? DeliveryOrder { get; set; }
        public bool? PurchaseOrderAudit { get; set; }
        public bool? PurchaseStoreInAudit { get; set; }
        public bool? ConsignStoreInAudit { get; set; }
        public bool? PurchaseStoreOutAudti { get; set; }
        public bool? ConsignStoureOutAudit { get; set; }
        public bool? TempStoreOutAudit { get; set; }
        public bool? SaleReturnAudit { get; set; }
        public bool? BTypeReturnAudit { get; set; }
        public bool? ReturnStoreInAudit { get; set; }
        public bool? TempStoreReturnAudit { get; set; }
        public bool? FollowPlatToTempStoreAudit { get; set; }
        public bool? TempStoreTransferAudit { get; set; }
        public bool? FollowPlatToSaleAudit { get; set; }
        public bool? BTypeTempStoreOutAudit { get; set; }
        public bool? ChangeStoreInAuth { get; set; }
        public bool? SellStoreOutAudit { get; set; }
        public bool? TempSellStoreOutAudit { get; set; }
        public bool? BTypeSellStoreOutAudit { get; set; }
        public bool? TempStorePlateAudit { get; set; }

        public bool? HiddenSaleAccountInfo { get; set; }
    }

    public class ProductNameAuth
    {
        public string? ProducerId { get; set; }
        public string? ProductNameId { get; set; }
    }

    public class Footer
    {
        public int? TotalQty { get; set; }
        public int? TotalStoreInQty { get; set; }
        public int? TotalReceiveQty { get; set; }
        public int? TotalOriginCost { get; set; }
        public int? TotalCost { get; set; }
        public int? TotalUnIncludeTaxRateAmount { get; set; }
        public int? TotalTaxRateAmount { get; set; }
        public int? TotalSaleAmount { get; set; }
        public int? TotalUnArrivedQty { get; set; }
        public int? TotalPaymentAmount { get; set; }
        public int? TotalInvoiceQty { get; set; }
        public int? TotalUnArrivedAmount { get; set; }
        public int? TotalRebateAmont { get; set; }
        public int? OverAgentTotalQty { get; set; }
        public int? OverTotalAmount { get; set; }
        public int? ActualPayTotalAmount { get; set; }
        public int? NotArrivalAmount { get; set; }
        public int? TotalPurchaseAmount { get; set; }
        public int? TotalFinanceAmount { get; set; }
        public int? TotalDebtBalance { get; set; }
        public int? TotalDebtValue { get; set; }
        public int? TotalOriginValue { get; set; }
        public int? TotalPurchaseBackAmount { get; set; }
        public int? TotalFinancePaymentAmount { get; set; }
        public int? TotalReviseAmount { get; set; }
    }
    
}
