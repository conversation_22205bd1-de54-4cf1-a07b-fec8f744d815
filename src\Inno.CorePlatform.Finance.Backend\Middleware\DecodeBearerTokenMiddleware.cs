﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Inno.CorePlatform.Finance.Backend.Middleware
{
    public class DecodeBearerTokenMiddleware
    {
        private readonly RequestDelegate _next;

        public DecodeBearerTokenMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            string strAuthHeader = context.Request.Headers["Authorization"];

            if (string.IsNullOrWhiteSpace(strAuthHeader) == false && (strAuthHeader.StartsWith("Bearer") || strAuthHeader.StartsWith("bearer")))
            {
                string jwt = strAuthHeader.Substring("Bearer ".Length).Trim();
                var handler = new JwtSecurityTokenHandler();
                var token = handler.ReadJwtToken(jwt);
                var cid = new ClaimsIdentity(token.Claims, "Custom", "upn", "role");
                context.User = new ClaimsPrincipal(cid);
            }

            await _next(context);
        }
    }

    public static class DecodeBearerTokenMiddlewareExtensions
    {
        public static IApplicationBuilder UseDecodeBearerToken(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<DecodeBearerTokenMiddleware>();
        }
    }
}
