﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.DTOs
{
    /// <summary>
    /// 缓存响应
    /// </summary>
    public class CachedResponse
    {
        public object Value { get; set; }
        public int StatusCode { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
