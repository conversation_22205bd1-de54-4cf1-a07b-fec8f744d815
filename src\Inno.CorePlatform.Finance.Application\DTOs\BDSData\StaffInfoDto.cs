﻿
using Inno.CorePlatform.Finance.Domain.ValueObjects;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    /// <summary>
    /// 员工信息
    /// </summary>
    public class StaffInfoDto
    {
        public string? careerDeptId { get; set; }
        public string? careerDeptName { get; set; }
        public string? careerDeptShortName { get; set; }
        public string? deptId { get; set; }
        public string? deptName { get; set; }
        public string? careerBizDeptId { get; set; }
        public string? deptShortName { get; set; }
        public string? email { get; set; }
        public string id { get; set; }
        public string mobile { get; set; }
        public string personId { get; set; }
        public string staffId { get; set; }
        public string staffName { get; set; }
        public string userId { get; set; }
        public string userName { get; set; }

    }

    public class StaffInfo
    {
        /// <summary>
        /// 拓展用
        /// </summary>
        public object ExtraInfo { get; set; }
        public string Id { get; set; }
        public string Name { get; set; }
    }
    public class UserOutput
    {
        /// <summary>
        /// 用户Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 用户名
        /// </summary> 
        public string? Name { get; set; }
        /// <summary>
        /// 显示名称
        /// </summary> 
        public string? DisplayName { get; set; }

        /// <summary>
        /// 员工Id
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 手机号
        /// </summary> 
        public string? MfaPhoneNumber { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string? MfaWxWorkId { get; set; }


        /// <summary>
        /// 邮箱
        /// </summary>
        public string? Email { get; set; }
        /// <summary>
        ///  1: '客户', 2: '供应商', 3: '厂家', 4: '业务单元' 
        /// </summary>
        public int? InstitutionType { get; set; }
        public List<Institution>? Institutions { get; set; }

    }

    /// <summary>
    /// 根据用户真实姓名查询用户
    /// </summary>
    public class SmallUserQueryOutput
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
    }

    public class Institution : BaseValueObject
    {

    }
}
