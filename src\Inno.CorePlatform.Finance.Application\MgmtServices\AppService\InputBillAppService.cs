﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class InputBillAppService : IInputBillAppService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IInputBillRepository _inputBillRepository;
        private readonly IInputBillDetailRepository _inputBillDetailRepository;
        private readonly IAppServiceContextAccessor? _contextAccessor;
        private readonly IBaseAllQueryService<InputBillPo> _queryInputBillPo;
        private readonly IBaseAllQueryService<InputBillSubmitDetailPo> _queryInputBillSubmitDetail;
        private readonly IBaseAllQueryService<InputBillSubmitDetailQuantityPo> _queryInputBillSubmitDetailQuantity;
        private readonly IInputBillSubmitDetailQuantityRepository _inputBillSubmitDetailQuantityRepository;
        private readonly IInputBillSubmitDetailRepository _inputBillSubmitDetailRepository;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IInventoryExcuteApiClient _inventoryExcuteApiClient;
        private readonly IApplyBFFService _applyBFFService;
        private readonly ILogger<InputBillAppService> _Logger;
        private Dictionary<Guid, decimal> _keyValueDicinvoiceQuantity = new Dictionary<Guid, decimal>();
        private Dictionary<Guid, decimal> _keyValueDicinvoiceQuantityOut = new Dictionary<Guid, decimal>();
        private readonly IBaseAllQueryService<DebtPo> _debtQueryService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IInputBillDebtRepository _inputBillDebtRepository;
        private readonly IBaseAllQueryService<InputBillDebtPo> _inputBillDebtQuery;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IPurchaseExcuteApiClient _purchaseExcuteApiClient;
        private DaprClient _daprClient;
        private readonly FinanceDbContext _db;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IBDSApiClient _bDSApiClient;
        public InputBillAppService(
             IUnitOfWork unitOfWork,
             IInputBillRepository inputBillRepository,
             IInputBillDetailRepository inputBillDetailRepository, IAppServiceContextAccessor? contextAccessor,
             IBaseAllQueryService<InputBillSubmitDetailPo> queryInputBillSubmitDetail,
             IBaseAllQueryService<InputBillSubmitDetailQuantityPo> queryInputBillSubmitDetailQuantity,
             IInputBillSubmitDetailQuantityRepository inputBillSubmitDetailQuantityRepository,
             IInputBillSubmitDetailRepository inputBillSubmitDetailRepository,
             IInventoryApiClient inventoryApiClient,
             IInventoryExcuteApiClient inventoryExcuteApiClient,
             IBaseAllQueryService<InputBillPo> queryInputBillPo,
             ILogger<InputBillAppService> Logger,
             IApplyBFFService applyBFFService,
             IBaseAllQueryService<DebtPo> debtQueryService,
             IKingdeeApiClient kingdeeApiClient,
             IInputBillDebtRepository inputBillDebtRepository,
             IBaseAllQueryService<InputBillDebtPo> inputBillDebtQuery,
             IPurchaseApiClient purchaseApiClient,
             DaprClient daprClient,
             IFileGatewayClient fileGatewayClient,
             IPurchaseExcuteApiClient purchaseExcuteApiClient,
             IBDSApiClient bDSApiClient,
             FinanceDbContext db
            )
        {
            this._unitOfWork = unitOfWork;
            this._inputBillRepository = inputBillRepository;
            this._inputBillDetailRepository = inputBillDetailRepository;
            this._contextAccessor = contextAccessor;
            this._queryInputBillSubmitDetail = queryInputBillSubmitDetail;
            this._queryInputBillSubmitDetailQuantity = queryInputBillSubmitDetailQuantity;
            this._inputBillSubmitDetailQuantityRepository = inputBillSubmitDetailQuantityRepository;
            this._inputBillSubmitDetailRepository = inputBillSubmitDetailRepository;
            this._inventoryApiClient = inventoryApiClient;
            this._queryInputBillPo = queryInputBillPo;
            this._Logger = Logger;
            this._applyBFFService = applyBFFService;
            this._debtQueryService = debtQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._inputBillDebtRepository = inputBillDebtRepository;
            this._inputBillDebtQuery = inputBillDebtQuery;
            this._purchaseApiClient = purchaseApiClient;
            this._db = db;
            this._fileGatewayClient = fileGatewayClient;
            _inventoryExcuteApiClient = inventoryExcuteApiClient;
            _purchaseExcuteApiClient = purchaseExcuteApiClient;
            _daprClient = daprClient;
            _bDSApiClient = bDSApiClient;
        }
        public async Task<int> CreateInputBill(InputBillInputDTo input)
        {
            #region 校验
            //发票号 和 发票代码只能推一次
            if (!string.IsNullOrEmpty(input.InvoiceNumber))
            {
                bool ishasInvoiceNumber = await _queryInputBillPo.FirstOrDefaultAsync(x => x.InvoiceNumber == input.InvoiceNumber && x.AgentId == input.AgentId) == null ? false : true;
                if (ishasInvoiceNumber)
                {
                    throw new Exception("参数InvoiceNumber,发票号已生成过");
                }
            }

            //if (input.InputBillDetail == null || !input.InputBillDetail.Any())
            //{
            //    throw new Exception("进项票明细为空");
            //}

            if (!new List<int>() { 1, 2 }.Contains(input.Type))
            {
                throw new Exception("参数Type，票据类型只能为 1,普票 2，专票 默认普票");
            }
            //调bds查询公司信息通过公司简称
            var companyInfo = await _applyBFFService.GetCompanyMetaInfosAsync(new CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput() { nameCodeEq = input.CompanyCode });
            if (!companyInfo.Any())
            {
                throw new Exception($"公司编码{input.CompanyCode}无对应的公司信息");
            }

            // 验证并修正供应商信息
            await ValidateAndCorrectSupplierInfoAsync(input);
            #endregion

            var id = Guid.NewGuid();

            var _inputBill = input.Adapt<InputBill>();
            _inputBill.CompanyId = new Guid(companyInfo.First().companyId);
            _inputBill.CompanName = companyInfo.First().companyName;
            _inputBill.Id = id;
            _inputBill.Status = 1;

            var _inputBillDetail = input.InputBillDetail.Adapt<List<InputBillDetail>>();
            _inputBillDetail.ForEach(t =>
            {
                t.Id = Guid.NewGuid();
                t.InputBillId = id;
            });

            await _inputBillRepository.AddAsync(_inputBill);
            await _inputBillDetailRepository.AddRangeAsync(_inputBillDetail);

            var excecount = await _unitOfWork.CommitAsync();
            return excecount;
        }

        /// <summary>
        /// 分配发票数量
        /// </summary>
        /// <param name="lotInfos">数据聚合明细</param>
        /// <param name="invoQuantity">本次入发票数</param>
        /// <param name="inputBillSubmitId">提交发票数量明细关联Id</param>
        /// <returns></returns>
        private async Task<List<InputBillSubmitDetailQuantity>> AllocationInvoiceQuantity(List<LotInfo> lotInfos, decimal invoQuantity, Guid inputBillSubmitId, Guid inputBillId)
        {
            invoQuantity = Math.Abs(invoQuantity);
            lotInfos = lotInfos.OrderBy(p => p.quantity).ToList();
            var storeInDetailIds = lotInfos.Where(x => !string.IsNullOrEmpty(x.storeInDetailId)).Select(p => Guid.Parse(p.storeInDetailId)).ToList();
            var quantityLstExists = new List<InputBillSubmitDetailQuantityPo>();
            if (storeInDetailIds != null && storeInDetailIds.Any())
            {
                quantityLstExists = await _db.InputBillSubmitDeatilQuantitys.Where(p => p.InputBillSubmitDetail.InputBill.Status != 2 && storeInDetailIds.ToHashSet().Contains(p.StoreInDetailId) && p.InputBillSubmitDetail.InputBillId != inputBillId).ToListAsync();
            }
            var totallotInfoQuantity = lotInfos.Sum(p => p.quantity);
            var lockQuantity = quantityLstExists.Sum(p => p.Quantity);
            if (totallotInfoQuantity - lockQuantity < invoQuantity)
            {
                throw new Exception($"本次入票数量超出实际数量");
            }
            //根据入库票数分配详情修改数量
            List<InputBillSubmitDetailQuantity> InputBillSubmitDetailQuantitylist = new List<InputBillSubmitDetailQuantity>();
            foreach (var item in lotInfos)
            {
                if (item != null && item.quantity > 0)
                {
                    var model = new InputBillSubmitDetailQuantity();
                    //该明细已经存在的入票记录
                    var quantityExistList = quantityLstExists.Where(p => p.StoreInDetailId == Guid.Parse(item.storeInDetailId)).ToList();
                    var quantityExist = quantityExistList.Sum(p => p.Quantity);
                    model.StoreInDetailId = Guid.Parse(item.storeInDetailId);
                    //可入票数=入库数量-入票数-已入票数量
                    var resQuantity = item.quantity.Value - (item.invoiceQuantity ?? 0) - quantityExist;
                    if (resQuantity <= 0) continue;

                    //本次入票数大于可入票数
                    if (invoQuantity > resQuantity)
                    {
                        invoQuantity = invoQuantity - resQuantity;
                        model.Quantity = resQuantity;
                    }
                    //本次入票数小于或等于可入票数
                    else
                    {
                        model.Quantity = invoQuantity;
                        invoQuantity = 0;
                    }
                    model.Id = Guid.NewGuid();
                    model.InputBillSubmitDetailId = inputBillSubmitId;
                    InputBillSubmitDetailQuantitylist.Add(model);
                }

            }
            return InputBillSubmitDetailQuantitylist;
        }

        /// <summary>
        /// 一个发票单提交入库单详情    (新增修改提交)
        /// </summary>
        /// <param name="input"></param>
        /// <param name="InputBillId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<int> CreateInputBillSubmit(List<StoreInDetaiSubmitInput> input, Guid InputBillId, bool IsAdd = false)
        {
            var inputBill = await _db.InputBills.FirstOrDefaultAsync(x => x.Id == InputBillId);
            if (inputBill == null)
            {
                throw new Exception($"进项票不存在或已被删除");
            }
            if (inputBill.Status != 1)
            {
                throw new Exception($"进项票当前状态不支持添加明细");
            }
            var inputOfHasQuantity = input.Where(p => p.ThisQuantity != 0).ToList();
            var inventoryStoreInUpdateDetails = new List<InventoryStoreInUpdateDetail>();
            var submitDetails = new List<InputBillSubmitDetail>();
            #region 这样查询不唯一
            //var productNoLst = input.Select(x => x.ProductNo).ToList();
            //var productIdLst = input.Select(x => x.ProductId).ToList();
            //var StoreInItemCodeLst = input.Select(x => x.StoreInItemCode).ToList();
            //var productNameIdLst = input.Select(x => x.ProductNameId).ToList();
            //var ProductNameLst = input.Select(x => x.ProductName).ToList();

            //查询这些提交的是否有存在 存在就删除
            //var submitDetailPos = await _queryInputBillSubmitDetail.GetAllListAsync(x => x.InputBillId == InputBillId
            //&& productNoLst.Contains(x.ProductNo)
            //&& productIdLst.Contains(x.ProductId)
            //&& StoreInItemCodeLst.Contains(x.StoreInItemCode)
            //&& productNameIdLst.Contains(x.ProductNameId)
            //&& ProductNameLst.Contains(x.ProductName)
            //, new List<string>() { "InputBillSubmitDetailQuantity" });
            #endregion
            var existsExp = inputOfHasQuantity.Select(x => x.ProductNo + "$" + x.ProductId.ToString() + "$" + x.StoreInItemCode + "$" + x.ProductNameId.ToString()).ToList();

            var existsAllExp = input.Select(x => x.ProductNo + "$" + x.ProductId.ToString() + "$" + x.StoreInItemCode + "$" + x.ProductNameId.ToString()).ToList();

            var submitDetailPos = await _queryInputBillSubmitDetail.GetAllListAsync(x => x.InputBillId == InputBillId
            && existsExp.Contains(x.ProductNo + "$" + x.ProductId.ToString() + "$" + x.StoreInItemCode + "$" + x.ProductNameId.ToString())
            , new List<string>() { "InputBillSubmitDetailQuantity" });

            var detailPoAll = await _queryInputBillSubmitDetail.GetAllListAsync(x => x.InputBillId == InputBillId
            && existsAllExp.Contains(x.ProductNo + "$" + x.ProductId.ToString() + "$" + x.StoreInItemCode + "$" + x.ProductNameId.ToString())
            , new List<string>() { "InputBillSubmitDetailQuantity" });


            var removeDetailIds = detailPoAll.Select(x => x.Id).ToList();
            var submitDetailQuantityPos = submitDetailPos.Select(x => x.InputBillSubmitDetailQuantity).ToList();
            List<InputBillSubmitDetailQuantityPo> InputBillSubmitDetailQuantityPolist = new List<InputBillSubmitDetailQuantityPo>();
            submitDetailQuantityPos.ForEach(x =>
            {
                InputBillSubmitDetailQuantityPolist.AddRange(x);
            });
            foreach (var inputItem in inputOfHasQuantity)
            {
                //提交详情
                #region 赋值
                var inputBillSubmit = new InputBillSubmitDetail();
                inputBillSubmit.OriginalId = inputItem.Id;
                inputBillSubmit.Id = Guid.NewGuid();
                inputBillSubmit.ProductId = inputItem.BusinessType == 5 ? Guid.Empty : inputItem.ProductId;
                inputBillSubmit.ProductNameId = inputItem.BusinessType == 5 ? Guid.Empty : inputItem.ProductNameId;
                inputBillSubmit.ProductName = inputItem.BusinessType == 5 ? "服务费" : inputItem.ProductName;
                inputBillSubmit.ProductNo = inputItem.BusinessType == 5 ? "服务费" : inputItem.ProductNo;
                inputBillSubmit.StoreInItemCode = inputItem.StoreInItemCode;
                //inputBillSubmit.Quantity = inputItem.BusinessType == 4 ? 1 : int.Parse(inputItem.ThisQuantity.ToString());
                inputBillSubmit.Quantity = inputItem.BusinessType == 4 || inputItem.BusinessType == 5 ? 1 : inputItem.ThisQuantity;
                if (inputItem.BusinessType == 2)
                {
                    inputBillSubmit.Quantity = Math.Abs(inputBillSubmit.Quantity.Value);
                }
                inputBillSubmit.ReceivedNumber = inputItem.BusinessType == 4 || inputItem.BusinessType == 5 ? 1 : inputItem.PolyInvoiceQuantity.Value;
                inputBillSubmit.TaxCost = inputItem.BusinessType == 4 || inputItem.BusinessType == 5 ? inputItem.ThisQuantity : inputItem.TaxCost;
                //inputBillSubmit.NoTaxCost = inputItem.BusinessType == 4 ? inputItem.ThisQuantity / (1 + inputItem.TaxRate / 100.00M) : inputBillSubmit.TaxCost - inputItem.NoTaxCost ?? Math.Round(inputItem.TaxCost.Value / (1 + (inputItem.TaxRate.Value / 100)), 10);
                if (inputItem.BusinessType == 4 || inputItem.BusinessType == 5)
                {
                    inputBillSubmit.NoTaxCost = inputItem.ThisQuantity / (1 + inputItem.TaxRate / 100.00M);
                }
                else
                {
                    if (inputItem.NoTaxCost.HasValue)
                    {
                        inputBillSubmit.NoTaxCost = inputItem.NoTaxCost;
                    }
                    else
                    {
                        inputBillSubmit.NoTaxCost = Math.Round(inputItem.TaxCost.Value / (1 + (inputItem.TaxRate.Value / 100)), 10);
                    }
                }
                inputBillSubmit.NoTaxAmount = inputItem.BusinessType == 4 || inputItem.BusinessType == 5 ? inputItem.ThisQuantity : inputBillSubmit.Quantity * inputBillSubmit.TaxCost;
                inputBillSubmit.TaxRate = inputItem.TaxRate;
                inputBillSubmit.TaxAmount = inputBillSubmit.NoTaxAmount / (1 + inputItem.TaxRate / 100) * (inputItem.TaxRate / 100);
                inputBillSubmit.InputBillId = InputBillId;
                inputBillSubmit.BusinessType = inputItem.BusinessType;
                if (inputBillSubmit.BusinessType == 2)
                {
                    inputBillSubmit.Quantity *= -1;
                    //inputBillSubmit.NoTaxCost *= -1;
                    inputBillSubmit.NoTaxAmount *= -1;
                    inputBillSubmit.TaxAmount *= -1;
                }
                var lotInfo = inputItem.LotInfo;
                if (lotInfo != null && lotInfo.Any())
                {
                    inputBillSubmit.ProducerOrderNo = inputItem.LotInfo[0].producerOrderNo;
                }
                #endregion
                //新增入票数量分配
                if (inputItem.BusinessType != 4 && inputItem.BusinessType != 5)
                {
                    inputBillSubmit.InputBillSubmitDetailQuantity = await AllocationInvoiceQuantity(
                                                                        inputItem.LotInfo,
                                                                        //int.Parse(inputItem.ThisQuantity.ToString()),
                                                                        inputItem.ThisQuantity,
                                                                        inputBillSubmit.Id,
                                                                        inputBillSubmit.InputBillId
                                                                    );
                }
                submitDetails.Add(inputBillSubmit);
            }
            //删除本来存在的
            await _inputBillSubmitDetailRepository.DeteteManyAsync(removeDetailIds);
            //再新增进去 数据一致性
            await _inputBillSubmitDetailRepository.AddManyAsync(submitDetails);
            var excecount = await _unitOfWork.CommitAsync();
            //await InitInputBill(InputBillId, true);
            return excecount;
        }

        public async Task<int> DeleteBillSubumit(InputBillDetailDeleteInput input)
        {
            var submitDetailPos = await _queryInputBillSubmitDetail.GetAllListAsync(x =>
            //x.InputBillId == inputBillId &&
            //storeInCodes.Contains(x.StoreInItemCode),
            input.DetailIds.Contains(x.Id),
            new List<string>() { "InputBill", "InputBillSubmitDetailQuantity" });
            if (!submitDetailPos.Any() || submitDetailPos.FirstOrDefault().InputBill.Status != 1)
            {
                throw new Exception("未找到数据或已提交的发票无法删除明细");
            }
            var guids2 = submitDetailPos.Select(x => x.Id).ToList();

            var submitDetailQuantityPos = submitDetailPos.Select(x => x.InputBillSubmitDetailQuantity).ToList();
            var guids1 = new List<Guid>();

            List<InventoryStoreInUpdateDetail> inventoryStoreInUpdateDetails = new List<InventoryStoreInUpdateDetail>();
            foreach (var item in submitDetailQuantityPos)
            {
                guids1.AddRange(item.Select(x => x.Id));
                foreach (var itemeDetai in item)
                {
                    inventoryStoreInUpdateDetails.Add(new InventoryStoreInUpdateDetail { invoiceQuantity = 0, storeInDetailId = itemeDetai.StoreInDetailId.ToString(), });
                }

            }
            await _inputBillSubmitDetailQuantityRepository.DeteteManyAsync(guids1);
            await _inputBillSubmitDetailRepository.DeteteManyAsync(guids2);
            var excecount = 0;
            excecount = await _unitOfWork.CommitAsync();
            //if (input.inputBillId.HasValue)
            //{
            //    await InitInputBill(input.inputBillId.Value, true);
            //}
            return excecount;

        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        public async Task InitInputBill(Guid guid,bool isSubmit = false)
        {

            var inputBillPos = await _queryInputBillPo.FirstOrDefaultAsync(x => x.Id == guid, new List<string>() { "InputBillSubmitDetail" });
            var submitDetailPos = inputBillPos.InputBillSubmitDetail;
            if (submitDetailPos != null && submitDetailPos.Count > 0)
            {
                inputBillPos.TaxAmount = submitDetailPos.Sum(x => x.TaxAmount);
                inputBillPos.NotaxAmount = submitDetailPos.Sum(x => x.NoTaxAmount);
                inputBillPos.Amount = inputBillPos.TaxAmount + inputBillPos.NotaxAmount;
            }
            var inputBill = inputBillPos.Adapt<InputBill>();
            await _inputBillRepository.UpdateAsync(inputBill);
            if (isSubmit)
            {
                await _unitOfWork.CommitAsync();
            }
        }
        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        public async Task<int> SubmitInputBill(List<Guid> guids, string userName)
        {
            #region 基础校验
            if (guids == null || !guids.Any())
            {
                throw new Exception("参数有误！");
            }
            if (guids == null || !guids.Any())
            {
                throw new Exception("参数有误！");
            }
            var inputId = guids.First();
            var inventoryStoreInUpdateDetails = new List<InventoryStoreInUpdateDetail>();    //入库集合
            var inventoryStoreOutUpdateDetails = new List<InventoryStoreOutUpdateDetail>();  //出库集合
            var inputBillPos = await _db.InputBills.Include(p => p.InputBillSubmitDetail).ThenInclude(p => p.InputBillSubmitDetailQuantity).Where(x => x.Id == inputId).AsNoTracking().ToListAsync();
            if (inputBillPos == null || !inputBillPos.Any())
            {
                throw new Exception("进项票不存在或已被删除！");
            }
            // 查询发票信息
            var invoice = inputBillPos.First();
            if (invoice == null)
            {
                throw new Exception("进项票不存在或已被删除！");
            }
            // 状态校验
            if (invoice.Status != 1)
            {
                throw new Exception("操作失败，原因：当前状态不能提交");
            }
            // 获取当前发票时间
            DateTime localDateTime = invoice.BillTime;
            // 将时间转换为协调世界时（UTC）
            DateTime utcDateTime = localDateTime.ToUniversalTime();
            // 计算自1970年1月1日以来的总毫秒数（即UTC时间戳）
            long invoiceDate = (long)(utcDateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;

            var inputBillSubmitDetails = invoice.InputBillSubmitDetail.ToList();

            //如果没有发票详情直接更新发回
            if (inputBillSubmitDetails.Count == 0)
            {
                throw new Exception("操作失败，原因：无发票明细不能提交");
            }

            //验证进项发票金额和选的发票是否对的上，差异在1以上的，不允许提交
            if (Math.Abs((Math.Abs(inputBillPos.First().Amount) - Math.Abs(inputBillSubmitDetails.Sum(x => x.NoTaxAmount)))) > 1)
            {
                throw new Exception("操作失败，原因：添加的发票明细金额之和不等于进项发票的总金额");
            }

            //验证金蝶详情和系统内详情税率是否一致
            var inputBillDetails = await _db.InputBillDetails.Where(x => x.InputBillId == inputId).AsNoTracking().ToListAsync();
            // 判断税率个数
            var submitDetailTaxRates = inputBillSubmitDetails.Select(x => x.TaxRate).Distinct().ToList();
            var detailTaxRates = inputBillDetails.Select(x => x.TaxRate).Distinct().ToList();
            if (invoice.Type == 2 && submitDetailTaxRates.Count == 0)
            {
                throw new Exception("操作失败，原因：无发票明细不能提交");
            }
            //var excludeServiceFee = inputBillSubmitDetails.Where(x => x.ProductName != "服务费").Select(x => x.TaxRate).Distinct().ToList();
            //if (invoice.Type == 2 && detailTaxRates.Count() > 0 && excludeServiceFee.Count() > 0 && excludeServiceFee.Count() != detailTaxRates.Count())
            //{
            //    throw new Exception("操作失败，原因：提交明细与金蝶税率不一致，无法提交");
            //}
            //if (invoice.Type == 2 && excludeServiceFee.Count() > 0)
            //{
            //    var submitDetailTaxRate = excludeServiceFee.FirstOrDefault();
            //    var detailTaxRate = detailTaxRates.FirstOrDefault();
            //    if (invoice.Type == 2 && submitDetailTaxRate != detailTaxRate)
            //    {
            //        throw new Exception("操作失败，原因：提交明细与金蝶税率不一致，无法提交");
            //    }
            //}
            #endregion

            #region 各明细容器
            //库存出入库明细数据
            var submitDetails_Inventory = inputBillSubmitDetails.Where(p => p.BusinessType == 1 || p.BusinessType == 2).ToList();
            //采购寄售转购货明细数据
            var submitDetails_PUA = inputBillSubmitDetails.Where(p => p.BusinessType == 3).ToList();
            //采购购货修订明细数据
            var submitDetailsRevise = inputBillSubmitDetails.Where(p => p.BusinessType == 4).ToList();
            //核心服务费明细数据
            var submitDetails_Debt = inputBillSubmitDetails.Where(p => p.BusinessType == 5).ToList();
            //数量明细数据（从内存中取，节约查询成本）
            var inputBillSubmitDetailsQuntitys = new List<InputBillSubmitDetailQuantityPo>();
            foreach (var detail in inputBillSubmitDetails)
            {
                if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any())
                {
                    inputBillSubmitDetailsQuntitys.AddRange(detail.InputBillSubmitDetailQuantity);
                }
            }
            #endregion

            #region 库存数量校验
            //实时校验库存数据
            if (submitDetails_Inventory != null && submitDetails_Inventory.Any())
            {
                var storeins = submitDetails_Inventory.Where(p => p.BusinessType == 1).ToList();
                if (storeins.Any())
                {
                    //校验入库数据
                    var storeinInput = new StoreInDetailQueryInput();
                    storeinInput.IsStoreIn = true;
                    storeinInput.storeInCodes = storeins.Select(x => x.StoreInItemCode).Distinct().ToList();
                    var LotInfos = new List<LotInfo>();
                    foreach (var item in storeins)
                    {
                        var lotInfoList = inputBillSubmitDetailsQuntitys.Where(t => t.InputBillSubmitDetailId == item.Id).Select(t => new LotInfo()
                        {
                            thisinvoiceQuantity = t.Quantity,
                            storeInDetailId = t.StoreInDetailId.ToString(),

                        }).ToList();
                        LotInfos.AddRange(lotInfoList);
                    }
                    var (dataIsContbool, msg) = await SubmitInvioQuntityDataConsistent(storeinInput, LotInfos);
                    if (!dataIsContbool)
                    {
                        throw new Exception(msg);
                    }
                }
                var storeouts = submitDetails_Inventory.Where(p => p.BusinessType == 2).ToList();
                if (storeouts.Any())
                {
                    //校验出库数据
                    var storeinInput = new StoreInDetailQueryInput();
                    storeinInput.IsStoreIn = false;
                    storeinInput.storeInCodes = storeouts.Select(x => x.StoreInItemCode).Distinct().ToList();
                    var LotInfos = new List<LotInfo>();
                    foreach (var item in storeouts)
                    {
                        var lotInfoList = inputBillSubmitDetailsQuntitys.Where(t => t.InputBillSubmitDetailId == item.Id).Select(t => new LotInfo()
                        {
                            thisinvoiceQuantity = t.Quantity,
                            storeInDetailId = t.StoreInDetailId.ToString(),

                        }).ToList();
                        LotInfos.AddRange(lotInfoList);
                    }
                    var (dataIsContbool, msg) = await SubmitInvioQuntityDataConsistent(storeinInput, LotInfos);
                    if (!dataIsContbool)
                    {
                        throw new Exception(msg);
                    }
                }
            }
            #endregion

            #region 提交到金蝶凭证 进项发票提交到金蝶
            // 获取系统月度
            inputBillPos.ForEach(x =>
            {
                x.Status = 2;
                x.UpdatedBy = _contextAccessor?.Get().UserName;
                x.UpdatedTime = DateTime.Now;
                // 重新提交时清除取消勾稽的时间和状态
                x.CancelReconciliationTime = null;
                x.IsCancelledReconciliation = null;
            });
            var inputBills = inputBillPos.Adapt<List<InputBill>>();
            var conpanyId = inputBills.FirstOrDefault().CompanyId.ToString();
            var sysMonth = await _bDSApiClient.GetSystemMonth(conpanyId);
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM-dd");
            await SubmitInputBillToKindee(inputBillSubmitDetails, inputBills, userName, sysMonth);
            #endregion

            #region 修改提交库存能力中心
            if (submitDetails_Inventory != null && submitDetails_Inventory.Any())
            {
                var ibsdqs = new List<InputBillSubmitDetailQuantityPo>();
                foreach (var item in submitDetails_Inventory)
                {
                    if (item.InputBillSubmitDetailQuantity != null && item.InputBillSubmitDetailQuantity.Any())
                    {
                        ibsdqs.AddRange(item.InputBillSubmitDetailQuantity);
                    }
                }
                foreach (var model in ibsdqs)
                {
                    var single = inputBillSubmitDetails.FirstOrDefault(x => x.Id == model.InputBillSubmitDetailId);
                    decimal quantity = single != null ? single.Quantity : 0;
                    decimal invoiceAmt = single != null ? single.NoTaxAmount : 0;
                    string invoiceType = invoice.Type == 1 ? "普票" : "专票";
                    if (model.InputBillSubmitDetail.BusinessType == 1)
                    {
                        //已入发票数量
                        if (!_keyValueDicinvoiceQuantity.ContainsKey(model.StoreInDetailId))
                        {
                            await RollBackSubmitInputBill(1, string.Format($"【库存】经销入库校验失败，原因：{model.StoreInDetailId}在库存返回接口中不存在"), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                            throw new Exception($"【库存】失败，原因:{model.StoreInDetailId}在库存返回接口中不存在，请联系管理员");
                        }

                        // 0926 入库库存取值currentInvoiceQuantity进行修改值
                        var invoiceQuantity = _keyValueDicinvoiceQuantity[model.StoreInDetailId];
                        inventoryStoreInUpdateDetails.Add(new InventoryStoreInUpdateDetail() { invoiceQuantity = model.Quantity + invoiceQuantity, storeInDetailId = model.StoreInDetailId.ToString(), invoiceNumber = inputBillPos.First().InvoiceNumber, currentInvoiceQuantity = model.Quantity, invoiceAmount = invoiceAmt, InvoiceTypeStr = invoiceType, InvoiceDate = invoiceDate });
                    }
                    else
                    {
                        //已入发票数量
                        if (!_keyValueDicinvoiceQuantityOut.ContainsKey(model.StoreInDetailId))
                        {
                            await RollBackSubmitInputBill(1, string.Format($"【库存】经销出库校验失败，原因：{model.StoreInDetailId}在库存返回接口中不存在"), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                            throw new Exception($"【库存】失败，原因:{model.StoreInDetailId}在库存返回接口中不存在，请联系管理员");
                        }

                        // 0926 出库库存取值invoiceQuantity进行修改值
                        var invoiceQuantity = _keyValueDicinvoiceQuantityOut[model.StoreInDetailId];
                        inventoryStoreOutUpdateDetails.Add(new InventoryStoreOutUpdateDetail() { invoiceQuantity = model.Quantity, id = model.StoreInDetailId.ToString(), invoiceNumber = inputBillPos.First().InvoiceNumber, currentInvoiceQuantity = quantity, invoiceAmount = invoiceAmt, InvoiceTypeStr = invoiceType, InvoiceDate = invoiceDate });
                    }
                }
                if (inventoryStoreInUpdateDetails.Any())
                {
                    try
                    {
                        var siRet = await _inventoryExcuteApiClient.UpdateStoreInDetail(inventoryStoreInUpdateDetails);
                        if (siRet.code != 200)
                        {
                            throw new Exception(siRet.message);
                        }
                    }
                    catch (Exception ex)
                    {
                        // rollback
                        await RollBackSubmitInputBill(1, string.Concat("【库存】经销入库入库失败，原因：", ex.Message), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                        throw new Exception("【库存】经销入库入库失败，原因：" + ex.Message);
                    }
                }
                if (inventoryStoreOutUpdateDetails.Any())
                {
                    try
                    {
                        var soRet = await _inventoryExcuteApiClient.UpdateStoreOutDetail(inventoryStoreOutUpdateDetails);
                        if (soRet.code != 200)
                        {
                            throw new Exception(soRet.message);
                        }
                    }
                    catch (Exception ex)
                    {
                        // rollback
                        await RollBackSubmitInputBill(2, string.Concat("【库存】经销调出入库失败，原因：", ex.Message), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                        throw new Exception("【库存】经销调出入库失败，原因：" + ex.Message);
                    }
                }
            }
            #endregion

            #region 寄售转购货
            if (submitDetails_PUA != null && submitDetails_PUA.Any())
            {
                var updateQuantiyPuaInput = new List<DTOs.Purchase.UpdateInvoiceQuantityInput>();
                foreach (var item in submitDetails_PUA)
                {
                    foreach (var subitem in item.InputBillSubmitDetailQuantity)
                    {
                        updateQuantiyPuaInput.Add(new DTOs.Purchase.UpdateInvoiceQuantityInput
                        {
                            InvoiceNumber = subitem.Quantity,
                            PurchaseDetailId = subitem.StoreInDetailId
                        });
                    }
                }
                try
                {
                    var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                    if (purRet.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception(purRet.Message);
                    }
                }
                catch (Exception ex)
                {
                    // rollback
                    var str = string.Concat("【采购】寄售转购货入票失败，原因：", ex.Message);
                    await RollBackSubmitInputBill(3, str, inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                    throw new Exception("【采购】寄售转购货入票失败，原因：" + ex.Message);
                }
            }
            #endregion

            #region 购货修订
            if (submitDetailsRevise.Any())
            {
                //更新购货修订入票信息
                var input = submitDetailsRevise.Select(p => new UpdatePurchaseReviseInvoiceAmountInputDto
                {
                    purchaseOrderCode = p.StoreInItemCode,
                    invoiceAmount = Math.Abs(p.NoTaxAmount),  //提交传入正数
                    productId = p.ProductId,
                    taxRate = p.TaxRate
                }).ToList();
                try
                {
                    var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(input);
                    if (retpuchase.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception(retpuchase.Message);
                    }
                }
                catch (Exception ex)
                {
                    // rollback
                    await RollBackSubmitInputBill(4, string.Concat("【采购】更新购货修订失败，原因：", ex.Message), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                    throw new Exception("【采购】更新购货修订失败，请联系管理员" + ex.Message);
                }
            }
            #endregion

            #region 服务费
            if (submitDetails_Debt.Any())
            {
                var debtCode = submitDetails_Debt.Select(p => p.StoreInItemCode).ToList();
                //更新Debt入票数
                var debts = await _db.Debts.Where(p => debtCode.Contains(p.BillCode)).ToListAsync();
                foreach (var debt in debts)
                {
                    var noTaxAmount = submitDetails_Debt.Where(p => p.StoreInItemCode == debt.BillCode).Sum(p => p.NoTaxAmount);
                    var invoiceAmount = debt.InvoiceAmount.HasValue ? debt.InvoiceAmount.Value : 0;
                    if (Math.Abs(invoiceAmount) + Math.Abs(noTaxAmount) > Math.Abs(debt.Value))
                    {
                        await RollBackSubmitInputBill(5, string.Format("【核心】服务费校验失败"), inputBillSubmitDetails, invoice.InvoiceNumber, userName, sysMonth);
                        throw new Exception($"服务费{submitDetails_Debt.FirstOrDefault().StoreInItemCode}的入票金额{invoiceAmount + noTaxAmount}大于应付金额{debt.Value}");
                    }
                    debt.InvoiceAmount = debt.InvoiceAmount.HasValue ? debt.InvoiceAmount.Value + noTaxAmount : noTaxAmount;
                }
            }
            #endregion

            #region 全部通过更改状态
            // 全部通过更改状态
            int i = await _db.SaveChangesAsync();
            return 1;
            #endregion
        }

        /// <summary>
        /// 根据进项发票号删除inputBillDebt数据
        /// </summary>
        /// <param name="invoiceNo"></param>
        /// <returns></returns>
        public async Task DeleteInputBillDebt(string invoiceNo)
        {
            var inputBill = await _db.InputBills.FirstOrDefaultAsync(x => x.InvoiceNumber == invoiceNo);
            if (inputBill != null)
            {
                inputBill.Status = 1;
                _db.InputBills.Update(inputBill);
                var inputBillDebts = await _db.InputBillDebt.Where(p => p.InputBillId == inputBill.Id).ToListAsync();
                if (inputBillDebts.Any())
                {
                    _db.InputBillDebt.RemoveRange(inputBillDebts);
                }
                await _unitOfWork.CommitAsync();
            }
        }

        /// <summary>
        /// 回滚广播事件调用
        /// </summary>
        /// <param name="step">当前运行中步骤</param>
        /// <param name="errMsg">错误信息</param>
        /// <param name="details">明细</param>
        /// <param name="invoiceNumber">进项票号</param>
        private async Task RollBackSubmitInputBill(int step, string errMsg, List<InputBillSubmitDetailPo> details, string invoiceNumber, string user, string sysMonth)
        {
            var inputBillSumbitDetailQuantities = new List<InputBillSubmitDetailQuantityPo>();
            foreach (var detail in details)
            {
                if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any())
                {
                    inputBillSumbitDetailQuantities.AddRange(detail.InputBillSubmitDetailQuantity);
                }
            }
            // 金蝶接口入参
            var inputBillUnassignInput = new InputBillUnassignInput
            {
                invoiceno = invoiceNumber,
                user = user,
                associatedDate = sysMonth
            };
            // 入库接口入参
            var invoiceNumbers = new List<string> { invoiceNumber };
            // 经销调出接口入参
            var inventoryStoreOutUpdateDetails = new List<InventoryStoreOutUpdateDetail>();
            // 寄售转购货接口入参 pua
            var updateQuantiyPuaInput = new List<UpdateInvoiceQuantityInput>();
            // 购货修订接口入参
            var updatePurchaseReviseInviceAmountInput = new List<UpdatePurchaseReviseInvoiceAmountInputDto>();
            // 组装参数
            foreach (var detail in details)
            {
                // 更新库存出库
                if (detail.BusinessType == 2)
                {
                    var currentIbsdq = inputBillSumbitDetailQuantities.Where(x => x.InputBillSubmitDetailId == detail.Id).ToList();
                    foreach (var subitem in currentIbsdq)
                    {
                        inventoryStoreOutUpdateDetails.Add(new InventoryStoreOutUpdateDetail() { invoiceQuantity = Math.Abs(subitem.Quantity), id = subitem.StoreInDetailId.ToString() });
                    }
                }
                // 寄售转购货
                if (detail.BusinessType == 3)
                {
                    var currentIbsdq = inputBillSumbitDetailQuantities.Where(x => x.InputBillSubmitDetailId == detail.Id).ToList();
                    foreach (var subitem in currentIbsdq)
                    {
                        updateQuantiyPuaInput.Add(new UpdateInvoiceQuantityInput
                        {
                            InvoiceNumber = 0 - subitem.Quantity,  //负数
                            PurchaseDetailId = subitem.StoreInDetailId
                        });
                    }
                }
                // 购货修订
                if (detail.BusinessType == 4)
                {
                    var invoiceAmount = detail.NoTaxAmount;
                    if (detail.NoTaxAmount > 0)
                    {
                        invoiceAmount = 0 - detail.NoTaxAmount;
                    }
                    var ghxd = new UpdatePurchaseReviseInvoiceAmountInputDto
                    {
                        purchaseOrderCode = detail.StoreInItemCode,
                        invoiceAmount = invoiceAmount, //取消勾稽应取负数
                        productId = detail.ProductId,
                        taxRate = detail.TaxRate
                    };
                    updatePurchaseReviseInviceAmountInput.Add(ghxd);
                }
                // other continue
            }
            bool isDelInputBillDebt = false;
            if (step > 0)
            {
                //回滚金蝶事件
                try
                {
                    var kingRet = await _kingdeeApiClient.InputBillUnassign(inputBillUnassignInput);
                    if (kingRet.Code != CodeStatusEnum.Success)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-inputBillUnassign", inputBillUnassignInput);
                    }
                    else
                    {
                        isDelInputBillDebt = true;
                    }
                }
                catch (Exception)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-inputBillUnassign", inputBillUnassignInput);
                }
            }
            if (step > 1)
            {
                //回滚入库事件
                try
                {
                    isDelInputBillDebt = true;
                    var kcRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(invoiceNumbers);
                    if (kcRet == null || kcRet.code != 200)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceInfoRevokeRK", invoiceNumbers);
                    }
                }
                catch (Exception)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceInfoRevokeRK", invoiceNumbers);
                }
            }
            if (step > 2)
            {
                //回滚经销调出
                try
                {
                    isDelInputBillDebt = true;
                    var soRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(inventoryStoreOutUpdateDetails);
                    if (soRet == null || soRet.code != 200)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceInfoRevokeDC", inventoryStoreOutUpdateDetails);
                    }
                }
                catch (Exception)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceInfoRevokeDC", inventoryStoreOutUpdateDetails);
                }
            }
            if (step > 3)
            {
                //回滚寄售转购货
                try
                {
                    isDelInputBillDebt = true;
                    var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                    if (purRet.Code != CodeStatusEnum.Success)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceQuantity", updateQuantiyPuaInput);
                    }
                }
                catch (Exception)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceQuantity", updateQuantiyPuaInput);
                }
            }
            if (step > 4)
            {
                //回滚购货修订
                try
                {
                    isDelInputBillDebt = true;
                    var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(updatePurchaseReviseInviceAmountInput);
                    if (retpuchase.Code != CodeStatusEnum.Success)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updatePurchaseReviseInviceAmount", updatePurchaseReviseInviceAmountInput);
                    }
                }
                catch (Exception)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updatePurchaseReviseInviceAmount", updatePurchaseReviseInviceAmountInput);
                }
            }

            if (details.Any() && isDelInputBillDebt)
            {
                await DeleteInputBillDebt(invoiceNumber);
            }
        }

        /// <summary>
        /// 忽略发票
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<int> IgnoreBill(List<Guid> ids)
        {
            var ret = 0;
            if (ids == null || !ids.Any())
            {
                throw new Exception("参数有误！");
            }
            var inputBills = await _queryInputBillPo.GetAllListAsync(p => ids.Contains(p.Id));
            if (inputBills != null && inputBills.Any())
            {
                if (inputBills.Exists(p => p.Status == 2))
                {
                    throw new Exception("操作失败，原因：已提交的进项发票不能忽略！");
                }
                var invoiceIds = inputBills.Select(p => p.Id).ToList();
                var hasDetail = await _queryInputBillSubmitDetail.GetAllListAsync(p => invoiceIds.Contains(p.InputBillId));
                if (hasDetail.Any())
                {
                    throw new Exception("已存在明细，无法忽略发票");
                }
                inputBills.ForEach(p => p.Status = 9);
                var inputBillDebts = await _db.InputBillDebt.Where(p => ids.Contains(p.InputBillId)).ToListAsync();
                if (inputBillDebts != null && inputBillDebts.Any())
                {
                    _db.InputBillDebt.RemoveRange(inputBillDebts);
                }
                await _inputBillRepository.UpdateManyAsync(inputBills.Adapt<List<InputBill>>());
                ret = await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        /// <summary>
        /// 恢复发票
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<int> RestoreBill(List<Guid> ids)
        {
            var ret = 0;
            if (ids == null || !ids.Any())
            {
                throw new Exception("参数有误！");
            }
            var inputBills = await _queryInputBillPo.GetAllListAsync(p => ids.Contains(p.Id));
            if (inputBills != null && inputBills.Any())
            {
                if (inputBills.Exists(p => p.Status != 9))
                {
                    throw new Exception("操作失败，原因：其它状态的进项发票不能恢复！");
                }
                var invoiceIds = inputBills.Select(p => p.Id).ToList();
                var hasDetail = await _queryInputBillSubmitDetail.GetAllListAsync(p => invoiceIds.Contains(p.InputBillId));
                if (hasDetail.Any())
                {
                    throw new Exception("已存在明细，无法恢复发票");
                }
                inputBills.ForEach(p => p.Status = 1);
                //var inputBillDebts = await _db.InputBillDebt.Where(p => ids.Contains(p.InputBillId)).ToListAsync();
                //if (inputBillDebts != null && inputBillDebts.Any())
                //{
                //    _db.InputBillDebt.RemoveRange(inputBillDebts);
                //}
                await _inputBillRepository.UpdateManyAsync(inputBills.Adapt<List<InputBill>>());
                ret = await _unitOfWork.CommitAsync();
            }
            return ret;
        }

        /// <summary>
        /// 提交至金蝶
        /// </summary>
        /// <param name="InputBillSubmitDetails"></param>
        /// <param name="isStoreIn"></param>
        /// <param name="inputBills"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task SubmitInputBillToKindee(List<InputBillSubmitDetailPo> InputBillSubmitDetails, List<InputBill> inputBills, string userName, string sysMonth)
        {
            var kingdeeData = new KingdeeInputBillSubmitDto();
            kingdeeData.invoiceno = inputBills.FirstOrDefault().InvoiceNumber;
            var thisDetails = InputBillSubmitDetails.Where(p => p.InputBillId == inputBills.FirstOrDefault().Id).ToList();
            //var codes = thisDetails.Where(p => p.BusinessType == 1 || p.BusinessType == 2).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到入库单号
            var kingdeeDetails = new List<KingdeeInputBillSubmitDetailDto>();
            var newInputBillDebts = new List<InputBillDebt>();

            #region 入库
            var inDetails = thisDetails.Where(p => p.BusinessType == 1).ToList(); //找到入库明细
            var incodes = thisDetails.Where(p => p.BusinessType == 1).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到入库单号
            if (incodes.Any())
            {
                var debts = await _debtQueryService.GetIQueryable(p => incodes.Contains(p.RelateCode) && p.Value != 0 && p.AutoType != "98" && p.AutoType != "99").OrderBy(p => p.RelateCode).ThenByDescending(p => Math.Abs(p.Value)).ToListAsync();//找到对应的应付
                var debtCodes = debts.Select(p => p.BillCode).Distinct().ToList();//找到应付单集合
                var inputBillDebts = await _inputBillDebtQuery.GetIQueryable(p => debtCodes.Contains(p.DebtCode)).ToListAsync(); //找到已经开过的应付
                                                                                                                                 // var totalAmount = thisDetails.Where(p => p.BusinessType == 1 || p.BusinessType == 2).Sum(q => q.Quantity * q.TaxCost); //计算提交总额
                var totalAmount = thisDetails.Where(p => p.BusinessType == 1).Sum(q => q.NoTaxAmount);
                var usedAmount = 0.00m;
                var index = 0;
                var useRateList = new List<PayBillRateStoreInItemCode>();
                foreach (var debt in debts)
                {
                    var isStoreIn = debt.BillCode.Contains("SI");
                    //if (index != 0 && debts[index - 1].RelateCode != debt.RelateCode)
                    //{
                    //    usedAmount = 0M; //重置为0
                    //}
                    usedAmount = kingdeeDetails.Where(p => p.relateCode == debt.RelateCode).Sum(p => p.f_usedamt); //重置为0  从金蝶集合中取

                    if (totalAmount <= 0)
                    {
                        break;
                    }
                    var thisExistAmount = inputBillDebts.Where(p => p.DebtCode == debt.BillCode).Sum(q => q.DebtAmount);
                    //if (thisExistAmount >= Math.Abs(debt.Value))//当前应付的金额已经勾完了
                    //{
                    //    continue;
                    //}
                    var thisAmount = thisDetails.Where(t => t.StoreInItemCode == debt.RelateCode).Sum(q => q.NoTaxAmount);//找到明细中入库相同入库单号的总额
                    //thisAmount = thisAmount > totalAmount ? totalAmount : thisAmount;
                    thisAmount = decimal.Parse(thisAmount.ToString("F2")); //保留2问小数
                    thisAmount -= Math.Abs(usedAmount);
                    if (thisAmount <= 0)
                    {
                        continue;
                    }
                    var avilableAmount = Math.Abs(thisAmount) > Math.Abs(debt.Value) - thisExistAmount ? Math.Abs(debt.Value) - thisExistAmount : Math.Abs(thisAmount);
                    //var avilableAmount2 = Math.Abs(thisAmount) > Math.Abs(debt.Value) - thisExistAmount ? thisAmount - thisExistAmount : Math.Abs(thisAmount);
                    //var avilableAmount1 =  Math.Abs(thisAmount);
                    var kibsd = new KingdeeInputBillSubmitDetailDto();
                    if (Math.Abs(thisAmount) > avilableAmount && avilableAmount > 0)
                    {
                        kibsd.finNum = debt.BillCode;
                        kibsd.f_usedamt = debt.Value > 0 ? avilableAmount : -avilableAmount;
                        kibsd.relateCode = debt.RelateCode;
                        kibsd.user = userName;
                        kibsd.associatedDate = sysMonth;
                        newInputBillDebts.Add(new InputBillDebt()
                        {
                            Id = Guid.NewGuid(),
                            DebtAmount = avilableAmount,
                            DebtCode = debt.BillCode,
                            InputBillId = inputBills.FirstOrDefault().Id,
                        });
                        totalAmount -= avilableAmount;
                        usedAmount += avilableAmount;
                    }
                    else
                    {
                        kibsd.finNum = debt.BillCode;
                        kibsd.f_usedamt = isStoreIn ? thisAmount : -thisAmount;
                        kibsd.relateCode = debt.RelateCode;
                        kibsd.user = userName;
                        kibsd.associatedDate = sysMonth;
                        newInputBillDebts.Add(new InputBillDebt()
                        {
                            Id = Guid.NewGuid(),
                            DebtAmount = Math.Abs(thisAmount),
                            DebtCode = debt.BillCode,
                            InputBillId = inputBills.FirstOrDefault().Id,
                        });
                        totalAmount -= Math.Abs(thisAmount);
                        usedAmount += Math.Abs(thisAmount);
                    }

                    //获取当前应付对应入库明细
                    var currentInDetails = inDetails.Where(p => p.StoreInItemCode == debt.RelateCode).ToList();
                    var payBillRateList = new List<PayBillRate>();
                    var debtByRelateCode = debts.Where(x => x.RelateCode == debt.RelateCode).ToList();
                    if (debtByRelateCode.Count() == 1)
                    {
                        //仅存在一个应付
                        payBillRateList = currentInDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                        {
                            usedAmount = g.Sum(x => x.NoTaxAmount),
                            taxRate = g.Key
                        }).ToList();
                    }
                    else
                    {
                        //存在多个需分配
                        var rateList = currentInDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                        {
                            usedAmount = g.Sum(x => x.NoTaxAmount),
                            taxRate = g.Key
                        }).ToList();
                        var usedamt = Math.Abs(kibsd.f_usedamt);
                        foreach (var rl in rateList)
                        {
                            var usedRateAmt = useRateList.Where(x => x.taxRate == rl.taxRate && x.storeInItemCode == kibsd.relateCode).Sum(x => x.usedAmount);
                            var surplusUsedAmount = rl.usedAmount - (usedRateAmt.HasValue ? usedRateAmt.Value : 0);
                            if (usedamt >= surplusUsedAmount)
                            {
                                if (surplusUsedAmount == 0)
                                {
                                    continue;
                                }
                                payBillRateList.Add(new PayBillRate
                                {
                                    usedAmount = rl.usedAmount > 0 ? surplusUsedAmount : -surplusUsedAmount,
                                    taxRate = rl.taxRate
                                });
                                usedamt -= (surplusUsedAmount.HasValue ? surplusUsedAmount.Value : 0);
                                useRateList.Add(new PayBillRateStoreInItemCode
                                {
                                    usedAmount = surplusUsedAmount,
                                    taxRate = rl.taxRate,
                                    storeInItemCode = kibsd.relateCode
                                });
                            }
                            else
                            {
                                payBillRateList.Add(new PayBillRate
                                {
                                    usedAmount = rl.usedAmount > 0 ? usedamt : -usedamt,
                                    taxRate = rl.taxRate
                                });
                                useRateList.Add(new PayBillRateStoreInItemCode
                                {
                                    usedAmount = usedamt,
                                    taxRate = rl.taxRate,
                                    storeInItemCode = kibsd.relateCode
                                });
                                break;
                            }
                        }
                    }
                    //payBillRateList.ForEach(x =>
                    //{
                    //    x.usedAmount = x.usedAmount.HasValue ? Math.Round(x.usedAmount.Value, 2) : 0;
                    //});
                    //kibsd.payBillRateList = payBillRateList;
                    kingdeeDetails.Add(kibsd);
                    index++;
                }
            }
            #endregion

            #region 出库
            var outDetails = thisDetails.Where(p => p.BusinessType == 2).ToList(); //找到出库明细
            var outcodes = thisDetails.Where(p => p.BusinessType == 2).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到入库单号
            if (outcodes.Any())
            {
                var debts = await _debtQueryService.GetIQueryable(p => outcodes.Contains(p.RelateCode) && p.Value != 0 && p.AutoType != "98" && p.AutoType != "99").OrderBy(p => p.RelateCode).ThenByDescending(p => Math.Abs(p.Value)).ToListAsync();//找到对应的应付
                var debtCodes = debts.Select(p => p.BillCode).Distinct().ToList();//找到应付单集合
                var debtRelateCodes = debts.Select(p => p.RelateCode).Distinct().ToList();
                var debtRelates = await _db.Debts.Where(x => debtRelateCodes.Contains(x.RelateCode) && x.Value != 0 && x.AutoType != "98" && x.AutoType != "99").ToListAsync();
                var inputBillDebts = await _inputBillDebtQuery.GetIQueryable(p => debtCodes.Contains(p.DebtCode)).ToListAsync(); //找到已经开过的应付
                                                                                                                                 // var totalAmount = thisDetails.Where(p => p.BusinessType == 1 || p.BusinessType == 2).Sum(q => q.Quantity * q.TaxCost); //计算提交总额
                var totalAmount = thisDetails.Where(p => p.BusinessType == 2).Sum(q => Math.Abs(q.NoTaxAmount));
                var usedAmount = 0.00m;
                var index = 0;
                var useRateList = new List<PayBillRateStoreInItemCode>();
                foreach (var debt in debts)
                {
                    var isStoreIn = debt.BillCode.Contains("SI");
                    //if (index != 0 && debts[index - 1].RelateCode != debt.RelateCode)
                    //{
                    //    usedAmount = 0M; //重置为0
                    //}
                    usedAmount = kingdeeDetails.Where(p => p.relateCode == debt.RelateCode).Sum(p => p.f_usedamt); //重置为0  从金蝶集合中取

                    if (totalAmount <= 0)
                    {
                        break;
                    }
                    var thisExistAmount = inputBillDebts.Where(p => p.DebtCode == debt.BillCode).Sum(q => q.DebtAmount);
                    if (thisExistAmount >= Math.Abs(debt.Value))//当前应付的金额已经勾完了
                    {
                        continue;
                    }
                    var thisAmount = thisDetails.Where(t => t.StoreInItemCode == debt.RelateCode).Sum(q => Math.Abs(q.NoTaxAmount));//找到明细中入库相同入库单号的总额
                    //thisAmount = thisAmount > totalAmount ? totalAmount : thisAmount;
                    thisAmount = decimal.Parse(thisAmount.ToString("F2")); //保留2问小数
                    thisAmount -= Math.Abs(usedAmount);
                    if (thisAmount <= 0)
                    {
                        continue;
                    }
                    // 当前关联应付
                    var currentRelateDebts = debtRelates.Where(x => x.RelateCode == debt.RelateCode).ToList();
                    var total = thisAmount;
                    var lastTotalAvilableAmount = 0M;
                    foreach (var relateDebt in currentRelateDebts)
                    {
                        var kibsd = new KingdeeInputBillSubmitDetailDto();
                        thisAmount = total - lastTotalAvilableAmount;
                        if (total - lastTotalAvilableAmount == 0)
                        {
                            // 金额已足够
                            break;
                        }
                        var avilableAmount = Math.Abs(thisAmount) > Math.Abs(relateDebt.Value) - thisExistAmount ? Math.Abs(relateDebt.Value) - thisExistAmount : Math.Abs(thisAmount);
                        if (Math.Abs(thisAmount) > avilableAmount && avilableAmount > 0)
                        {
                            var exists = kingdeeDetails.FirstOrDefault(x => x.finNum == relateDebt.BillCode);
                            if (exists != null)
                            {
                                exists.f_usedamt += (relateDebt.Value > 0 ? avilableAmount : -avilableAmount);
                                continue;
                            }
                            kibsd.finNum = relateDebt.BillCode;
                            kibsd.f_usedamt = relateDebt.Value > 0 ? avilableAmount : -avilableAmount;
                            kibsd.relateCode = relateDebt.RelateCode;
                            kibsd.user = userName;
                            kibsd.associatedDate = sysMonth;
                            newInputBillDebts.Add(new InputBillDebt()
                            {
                                Id = Guid.NewGuid(),
                                DebtAmount = avilableAmount,
                                DebtCode = relateDebt.BillCode,
                                InputBillId = inputBills.FirstOrDefault().Id,
                            });
                            totalAmount -= avilableAmount;
                            usedAmount += avilableAmount;
                            lastTotalAvilableAmount += avilableAmount;
                        }
                        else
                        {
                            var exists = kingdeeDetails.FirstOrDefault(x => x.finNum == relateDebt.BillCode);
                            if (exists != null)
                            {
                                exists.f_usedamt += isStoreIn ? thisAmount : -thisAmount;
                                continue;
                            }
                            kibsd.finNum = relateDebt.BillCode;
                            kibsd.f_usedamt = isStoreIn ? thisAmount : -thisAmount;
                            kibsd.relateCode = relateDebt.RelateCode;
                            kibsd.user = userName;
                            kibsd.associatedDate = sysMonth;
                            newInputBillDebts.Add(new InputBillDebt()
                            {
                                Id = Guid.NewGuid(),
                                DebtAmount = Math.Abs(thisAmount),
                                DebtCode = relateDebt.BillCode,
                                InputBillId = inputBills.FirstOrDefault().Id,
                            });
                            totalAmount -= Math.Abs(thisAmount);
                            usedAmount += Math.Abs(thisAmount);
                            lastTotalAvilableAmount += thisAmount;
                        }

                        //获取当前应付对应出库明细
                        var currentOutDetails = outDetails.Where(p => p.StoreInItemCode == debt.RelateCode).ToList();
                        var payBillRateList = new List<PayBillRate>();
                        var debtByRelateCode = currentRelateDebts.Where(x => x.RelateCode == kibsd.relateCode).ToList();
                        if (debtByRelateCode.Count() == 1)
                        {
                            //仅存在一个应付
                            payBillRateList = currentOutDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                            {
                                usedAmount = g.Sum(x => x.NoTaxAmount),
                                taxRate = g.Key
                            }).ToList();
                        }
                        else
                        {
                            //存在多个需分配
                            var rateList = currentOutDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                            {
                                usedAmount = g.Sum(x => x.NoTaxAmount),
                                taxRate = g.Key
                            }).ToList();
                            var usedamt = Math.Abs(kibsd.f_usedamt);
                            foreach (var rl in rateList)
                            {
                                var usedRateAmt = useRateList.Where(x => x.taxRate == rl.taxRate && x.storeInItemCode == kibsd.relateCode).Sum(x => x.usedAmount);
                                var surplusUsedAmount = (rl.usedAmount.HasValue ? Math.Abs(rl.usedAmount.Value) : 0) - usedRateAmt;
                                if (usedamt >= (surplusUsedAmount.HasValue? Math.Abs(surplusUsedAmount.Value) : 0))
                                {
                                    if (surplusUsedAmount == 0)
                                    {
                                        continue;
                                    }
                                    payBillRateList.Add(new PayBillRate
                                    {
                                        usedAmount = rl.usedAmount > 0 ? surplusUsedAmount : -surplusUsedAmount,
                                        taxRate = rl.taxRate
                                    });
                                    usedamt -= (surplusUsedAmount.HasValue ? surplusUsedAmount.Value : 0);
                                    useRateList.Add(new PayBillRateStoreInItemCode
                                    {
                                        usedAmount = surplusUsedAmount,
                                        taxRate = rl.taxRate,
                                        storeInItemCode = kibsd.relateCode
                                    });
                                }
                                else
                                {
                                    payBillRateList.Add(new PayBillRate
                                    {
                                        usedAmount = rl.usedAmount > 0 ? usedamt : -usedamt,
                                        taxRate = rl.taxRate
                                    });
                                    useRateList.Add(new PayBillRateStoreInItemCode
                                    {
                                        usedAmount = usedamt,
                                        taxRate = rl.taxRate,
                                        storeInItemCode = kibsd.relateCode
                                    });
                                    break;
                                }
                            }
                        }
                        //kibsd.payBillRateList = payBillRateList;
                        kingdeeDetails.Add(kibsd);
                    }
                    index++;
                }
            }
            #endregion

            #region 换货转退货
            var erDetails = thisDetails.Where(p => p.BusinessType == 6).ToList(); //找到换货明细
            var ercodes = thisDetails.Where(p => p.BusinessType == 6).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到换货转退货单号
            if (ercodes.Any())
            {
                var debts = await _debtQueryService.GetIQueryable(p => ercodes.Contains(p.RelateCode) && p.Value != 0 && p.AutoType != "98" && p.AutoType != "99").OrderBy(p => p.RelateCode).ThenByDescending(p => Math.Abs(p.Value)).ToListAsync();//找到对应的应付
                var debtCodes = debts.Select(p => p.BillCode).Distinct().ToList();//找到应付单集合
                var inputBillDebts = await _inputBillDebtQuery.GetIQueryable(p => debtCodes.Contains(p.DebtCode)).ToListAsync(); //找到已经开过的应付
                var totalAmount = thisDetails.Where(p => p.BusinessType == 6).Sum(q => q.NoTaxAmount);
                var usedAmount = 0.00m;
                var index = 0;
                var useRateList = new List<PayBillRateStoreInItemCode>();
                foreach (var debt in debts)
                {
                    usedAmount = kingdeeDetails.Where(p => p.relateCode == debt.RelateCode).Sum(p => p.f_usedamt); //重置为0  从金蝶集合中取
                    var thisExistAmount = inputBillDebts.Where(p => p.DebtCode == debt.BillCode).Sum(q => q.DebtAmount);
                    var thisAmount = thisDetails.Where(t => t.StoreInItemCode == debt.RelateCode).Sum(q => q.NoTaxAmount);//找到明细中换货相同换货单号的总额
                    thisAmount = decimal.Parse(thisAmount.ToString("F2")); //保留2位小数
                    thisAmount -= usedAmount;
                    var exists = kingdeeDetails.FirstOrDefault(x => x.finNum == debt.BillCode);
                    if (exists != null)
                    {
                        exists.f_usedamt += thisAmount;
                        continue;
                    }
                    var kibsd = new KingdeeInputBillSubmitDetailDto()
                    {
                        finNum = debt.BillCode,
                        f_usedamt = thisAmount,
                        relateCode = debt.RelateCode,
                        user = userName,
                        associatedDate = sysMonth
                    };
                    newInputBillDebts.Add(new InputBillDebt()
                    {
                        Id = Guid.NewGuid(),
                        DebtAmount = Math.Abs(thisAmount),
                        DebtCode = debt.BillCode,
                        InputBillId = inputBills.FirstOrDefault().Id,
                    });
                    totalAmount -= thisAmount;
                    usedAmount += Math.Abs(thisAmount);

                    //获取当前应付对应明细
                    var currentErDetails = erDetails.Where(p => p.StoreInItemCode == debt.RelateCode).ToList();
                    var payBillRateList = new List<PayBillRate>();
                    var debtByRelateCode = debts.Where(x => x.RelateCode == debt.RelateCode).ToList();
                    if (debtByRelateCode.Count() == 1)
                    {
                        //仅存在一个应付
                        payBillRateList = currentErDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                        {
                            usedAmount = g.Sum(x => x.NoTaxAmount),
                            taxRate = g.Key
                        }).ToList();
                    }
                    else
                    {
                        //存在多个需分配
                        var rateList = currentErDetails.GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                        {
                            usedAmount = g.Sum(x => x.NoTaxAmount),
                            taxRate = g.Key
                        }).ToList();
                        var usedamt = Math.Abs(kibsd.f_usedamt);
                        foreach (var rl in rateList)
                        {
                            var usedRateAmt = useRateList.Where(x => x.taxRate == rl.taxRate && x.storeInItemCode == kibsd.relateCode).Sum(x => x.usedAmount);
                            var surplusUsedAmount = rl.usedAmount - (usedRateAmt.HasValue ? usedRateAmt.Value : 0);
                            if (usedamt >= surplusUsedAmount)
                            {
                                if (surplusUsedAmount == 0)
                                {
                                    continue;
                                }
                                payBillRateList.Add(new PayBillRate
                                {
                                    usedAmount = rl.usedAmount > 0 ? surplusUsedAmount : -surplusUsedAmount,
                                    taxRate = rl.taxRate
                                });
                                usedamt -= (surplusUsedAmount.HasValue ? surplusUsedAmount.Value : 0);
                                useRateList.Add(new PayBillRateStoreInItemCode
                                {
                                    usedAmount = surplusUsedAmount,
                                    taxRate = rl.taxRate,
                                    storeInItemCode = kibsd.relateCode
                                });
                            }
                            else
                            {
                                payBillRateList.Add(new PayBillRate
                                {
                                    usedAmount = rl.usedAmount > 0 ? usedamt : -usedamt,
                                    taxRate = rl.taxRate
                                });
                                useRateList.Add(new PayBillRateStoreInItemCode
                                {
                                    usedAmount = usedamt,
                                    taxRate = rl.taxRate,
                                    storeInItemCode = kibsd.relateCode
                                });
                                break;
                            }
                        }
                    }
                    //kibsd.payBillRateList = payBillRateList;
                    kingdeeDetails.Add(kibsd);
                    index++;
                }
            }
            #endregion

            #region Other（寄售转购货、订单修订、服务费）
            var puaDetails = InputBillSubmitDetails.Where(p => p.BusinessType == 3 || p.BusinessType == 4 || p.BusinessType == 5).ToList();
            foreach (var item in puaDetails)
            {
                var exists = kingdeeDetails.FirstOrDefault(x => x.finNum == item.StoreInItemCode);
                if (exists != null)
                {
                    exists.f_usedamt += decimal.Parse(item.NoTaxAmount.ToString("F2"));
                    continue;
                }
                var payBillRateList = puaDetails.Where(x => x.StoreInItemCode == item.StoreInItemCode).GroupBy(x => x.TaxRate).Select(g => new PayBillRate
                {
                    usedAmount = g.Sum(x => x.NoTaxAmount),
                    taxRate = g.Key
                }).ToList();
                kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                {
                    finNum = item.StoreInItemCode,
                    f_usedamt = decimal.Parse(item.NoTaxAmount.ToString("F2")),
                    user = userName,
                    associatedDate = sysMonth,
                    //payBillRateList = payBillRateList
                });
            }
            #endregion

            kingdeeData.finentry = kingdeeDetails;
            var kingdeeRes = await _kingdeeApiClient.SubInputBillToKingdee(kingdeeData);
            if (kingdeeRes.Code == CodeStatusEnum.Success)
            {
                _inputBillRepository.UowJoined = false;
                await _inputBillRepository.UpdateManyAsync(inputBills);
                if (newInputBillDebts.Any())
                {
                    _inputBillDebtRepository.UowJoined = false;
                    await _inputBillDebtRepository.AddManyAsync(newInputBillDebts);
                }
            }
            else
            {
                throw new Exception("提交失败进项勾稽到金蝶失败，原因：" + kingdeeRes.Message);
            }
        }
        public async Task<bool> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> inventoryStoreInUpdateDetails)
        {
            try
            {
                var siRet = await _inventoryExcuteApiClient.UpdateStoreInDetail(inventoryStoreInUpdateDetails);
                if (siRet.code == 200)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "进项发票提交更新入库失败");
                return false;
            }
        }
        private async Task<bool> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> inventoryStoreOutUpdateDetails)
        {
            try
            {
                var soRet = await _inventoryExcuteApiClient.UpdateStoreOutDetail(inventoryStoreOutUpdateDetails);
                if (soRet.code == 200)
                {
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "进项发票提交更新出库失败");
                return false;
            }
        }
        public async Task<(bool, string)> SubmitInvioQuntityDataConsistent(StoreInDetailQueryInput query, List<LotInfo> LotInfos)
        {
            // 0928 查询全部库存数据
            query.supposedType = 2;
            query.limit = 1000000;
            if (query.IsStoreIn == true)
            {
                _keyValueDicinvoiceQuantity.Clear();
                var res = await _inventoryApiClient.QueryStoreInByCompany(query);
                string msg = string.Empty;
                if (res == null || res.list == null || res.list.Count == 0)
                {
                    msg = $"入库{string.Join(",", query.storeInCodes)}查询入库清单，无数据";
                    return (false, msg);
                }
                foreach (var item in res.list)
                {
                    foreach (var itemDetail in item.LotInfo)
                    {
                        foreach (var itemInput in LotInfos)
                        {
                            if (!_keyValueDicinvoiceQuantity.Keys.Contains(new Guid(itemDetail.storeInDetailId)))
                            {
                                _keyValueDicinvoiceQuantity.Add(new Guid(itemDetail.storeInDetailId), itemDetail.invoiceQuantity ?? 0);
                            }

                            if (new Guid(itemDetail.storeInDetailId) == new Guid(itemInput.storeInDetailId))
                            {
                                //当前入库明细可以入发票数
                                var canInvoiceQuantity = itemDetail.quantity - itemDetail.invoiceQuantity;
                                if (itemInput.thisinvoiceQuantity > canInvoiceQuantity)
                                {
                                    msg = $"货号:{itemInput.productNo ??= itemDetail.productNo},的入库发票数量{itemInput.thisinvoiceQuantity}与库存的可入发票数量{canInvoiceQuantity}比较,超出数量.";
                                    return (false, msg);
                                }

                            }
                        }
                    }
                }

                return (true, msg);
            }
            else
            {
                _keyValueDicinvoiceQuantityOut.Clear();
                query.storeOutTypeList = new List<int> { 1, 6, 19 };
                query.storeOutCodes = query.storeInCodes;
                var res = await _inventoryApiClient.QueryStoreOutByCompany(query);
                string msg = string.Empty;
                if (res == null || res.list == null || res.list.Count == 0)
                {
                    msg = $"经销出库{string.Join(",", query.storeInCodes)}查询出库清单，无数据";
                    return (false, msg);
                }
                foreach (var item in res.list)
                {
                    foreach (var itemDetail in item.LotInfo)
                    {
                        foreach (var itemInput in LotInfos)
                        {
                            if (!_keyValueDicinvoiceQuantityOut.Keys.Contains(new Guid(itemDetail.id)))
                            {
                                _keyValueDicinvoiceQuantityOut.Add(new Guid(itemDetail.id), itemDetail.invoiceQuantity ?? 0);
                            }

                            if (new Guid(itemDetail.id) == new Guid(itemInput.storeInDetailId))
                            {
                                //当前入库明细可以入发票数
                                var canInvoiceQuantity = itemDetail.quantity - itemDetail.invoiceQuantity;
                                if (itemInput.thisinvoiceQuantity > canInvoiceQuantity)
                                {
                                    msg = $"货号:{item.ProductNo ??= itemDetail.productNo},的入库发票数量{itemInput.thisinvoiceQuantity}与库存的可入发票数量{canInvoiceQuantity}比较,超出数量.";
                                    return (false, msg);
                                }

                            }
                        }
                    }
                }

                return (true, msg);
            }
        }

        /// <summary>
        /// 平尾差
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<int> EliminatingErrors(Guid id)
        {
            var inputBill = await _queryInputBillPo.FirstOrDefaultAsync(p => p.Id == id);
            if (inputBill == null)
            {
                throw new Exception("未找到数据！");
            }
            var inputDetails = await _queryInputBillSubmitDetail.GetAllListAsync(x => x.InputBillId == id);
            var taxAmountOfDetails = Math.Round(inputDetails.Sum(p => p.TaxAmount), 2);
            var noTaxAmountOfDetails = Math.Round(inputDetails.Sum(p => p.NoTaxAmount), 2);
            var taxAmount = Math.Round(inputBill.TaxAmount, 2);
            var noTaxAmount = Math.Round(inputBill.Amount, 2);
            if (inputDetails != null && inputDetails.Any())
            {
                var detail = inputDetails.FirstOrDefault();
                if (noTaxAmount != noTaxAmountOfDetails)
                {
                    detail.NoTaxAmount = detail.NoTaxAmount + (noTaxAmount - noTaxAmountOfDetails);
                }
                if (taxAmount != taxAmountOfDetails)
                {
                    detail.TaxAmount = detail.TaxAmount + (taxAmount - taxAmountOfDetails);
                }
                await _inputBillSubmitDetailRepository.UpdateAsync(detail.Adapt<InputBillSubmitDetail>());
            }
            else
            {
                throw new Exception("未找到明细数据！");
            }

            var excecount = await _unitOfWork.CommitAsync();
            return excecount;
        }

        public async Task<BaseResponseData<int>> AddDetailByCodes(Guid inputBillId, List<string> codes, int type = 1)
        {
            #region 校验
            if (codes == null || codes.Count == 0)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "请输入单号"
                };
            }
            var enableCodes = codes.Select(p => p.Contains("-PUA-") || p.Contains("-SI-")).ToList();
            if (enableCodes.Count != codes.Count)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "只能导入寄售转购货单或经销购货入库单"
                };
            }
            if (codes.Any(p => p.Contains("-PUA-")) && codes.Any(p => p.Contains("-SI-")))
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "不能同时添加寄售转购货单和入库单"
                };
            }
            if (codes.Any(p => p.Contains("-PUA-")))
            {
                type = 1;
            }
            if (codes.Any(p => p.Contains("-SI-")))
            {
                type = 2;
            }


            var invoice = await _queryInputBillPo.FirstOrDefaultAsync(p => p.Id == inputBillId);
            if (invoice == null || invoice.Status == 2)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "未找到发票数据或发票已提交"
                };
            }
            if (invoice.Status != 1)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "只能导入临时草稿的发票数据"
                };
            }
            var exsitDetail = await _queryInputBillSubmitDetail.GetAllListAsync(p => p.InputBillId == invoice.Id);
            if (exsitDetail.Count != 0)
            {
                // 存在明细，覆盖
                _db.InputBillSubmitDetails.RemoveRange(exsitDetail);
            }
            var exsits = await _queryInputBillSubmitDetail.GetAllListAsync(p => codes.Contains(p.StoreInItemCode) && p.InputBillId != invoice.Id, new List<string>() { "InputBill" });
            var existCodes = exsits.Select(p => p.StoreInItemCode).Distinct().ToList();
            if (existCodes.Count > 0)
            {
                var existInvoices = exsits.Select(p => p.InputBill.InvoiceNumber).Distinct().ToList();
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"以下单据已在其他发票中添加，无法使用此功能，单据号:{string.Join(",", existCodes)},存在的发票号：{string.Join(",", existInvoices)}"
                };
            }
            #endregion
            if (type == 1)
            {
                var puaInput = new ConsignToPurchaseDetailGroupInput
                {
                    AgentId = invoice.AgentId.ToString(),
                    CompanyId = invoice.CompanyId.ToString(),
                    pageIndex = 1,
                    pageSize = 100000,
                    StrategyQuery = null,
                    InvoiceStatus = 0,
                    Codes = codes
                };
                var pageResult = await _purchaseApiClient.GetConsignToPurchaseDetailGroup(puaInput);
                if (pageResult.Data.Count == 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"未查询到业务数据"
                    };
                }
                var hasQuantity = pageResult.Data.Where(p => p.InvoiceQuantity > 0).Select(p => p.Code).Distinct().ToList();
                if (hasQuantity.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"以下单据已有勾稽发票，无法使用此功能：{string.Join(",", hasQuantity)}"
                    };
                }
                var details = new List<InputBillSubmitDetail>();
                pageResult.Data.ForEach(p =>
                {
                    var d = new InputBillSubmitDetail()
                    {
                        Id = Guid.NewGuid(),
                        BusinessType = 3,
                        CreatedBy = "none",
                        CreatedTime = DateTime.Now,
                        InputBillId = invoice.Id,
                        NoTaxAmount = p.Quantity * p.UnitCost,
                        NoTaxCost = Math.Round(p.UnitCost / (1 + (p.TaxRate / 100)), 10),
                        Quantity = p.Quantity,
                        ProductId = p.ProductId,
                        ProductName = p.ProductName,
                        ProductNameId = Guid.Parse(p.ProductNameId),
                        ProductNo = p.ProductNo,
                        ReceivedNumber = p.Quantity,
                        StoreInDate = p.BillDate.UtcDateTime,
                        StoreInItemCode = p.Code,
                        TaxCost = p.UnitCost,
                        TaxRate = p.TaxRate,
                    };
                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                    d.InputBillSubmitDetailQuantity = p.Details.Select(t => new InputBillSubmitDetailQuantity()
                    {
                        Id = Guid.NewGuid(),
                        InputBillSubmitDetailId = d.Id,
                        CreatedBy = "none",
                        CreatedTime = DateTime.Now,
                        Quantity = t.Quantity,
                        StoreInDetailId = t.PurchaseDetailId,
                    }).ToList();
                    details.Add(d);
                });
                await _inputBillSubmitDetailRepository.AddManyAsync(details);
                var excecount = await _unitOfWork.CommitAsync();
            }
            else
            {
                var queryInput = new StoreInDetailQueryInput()
                {
                    agentId = invoice.AgentId,
                    companyId = invoice.CompanyId,
                    storeInCodes = codes,
                    page = 1,
                    limit = 1000000
                };
                var resultList = await _inventoryApiClient.QueryStoreInByCompany(queryInput);
                if (resultList.list.Count == 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"未查询到业务数据"
                    };
                }
                var hasQuantity = resultList.list.Where(p => p.invoiceQuantity > 0).Select(p => p.storeInCode).Distinct().ToList();
                if (hasQuantity.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"以下单据已有勾稽发票，无法使用此功能：{string.Join(",", hasQuantity)}"
                    };
                }
                var details = new List<InputBillSubmitDetail>();
                resultList.list.ForEach(p =>
                {
                    var d = new InputBillSubmitDetail()
                    {
                        Id = Guid.NewGuid(),
                        BusinessType = 1,
                        CreatedBy = "none",
                        CreatedTime = DateTime.Now,
                        InputBillId = invoice.Id,
                        NoTaxAmount = p.quantity * p.unitCost,
                        NoTaxCost = Math.Round(p.unitCost.Value / (1 + (p.taxRate.Value / 100)), 10),
                        Quantity = p.quantity,
                        ProductId = p.productId,
                        ProductName = p.productName,
                        ProductNameId = p.productNameId,
                        ProductNo = p.productNo,
                        ReceivedNumber = p.quantity.Value,
                        StoreInDate = DateTimeHelper.LongToDateTime(p.storeInDate),
                        StoreInItemCode = p.storeInCode,
                        TaxCost = p.unitCost,
                        TaxRate = p.taxRate,
                    };
                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                    d.InputBillSubmitDetailQuantity = p.LotInfo.Select(t => new InputBillSubmitDetailQuantity()
                    {
                        Id = Guid.NewGuid(),
                        InputBillSubmitDetailId = d.Id,
                        CreatedBy = "none",
                        CreatedTime = DateTime.Now,
                        Quantity = t.quantity,
                        StoreInDetailId = Guid.Parse(t.storeInDetailId),
                    }).ToList();
                    details.Add(d);
                });
                await _inputBillSubmitDetailRepository.AddManyAsync(details);
                var excecount = await _unitOfWork.CommitAsync();
            }
            return new BaseResponseData<int>()
            {
                Code = CodeStatusEnum.Success,
                Message = "导入成功"
            };
        }
        /// <summary>
        /// 按Excel导入
        /// </summary>
        /// <param name="file"></param>
        /// <param name="isSubmit">是否提交</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ImportInvoiceForBusinessBill(IFormFile file, bool isSubmit)
        {
            if (file == null)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "请上传导入文件"
                };
            }

            Stream stream = file.OpenReadStream();
            var excelData = new List<InputBillImportDto>();
            using (ExcelPackage package = new ExcelPackage(stream))
            {
                ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                int rowCount = 0;
                try
                {
                    rowCount = worksheet.Dimension.Rows;
                }
                catch (Exception)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请导入正确的Excel模板"
                    };
                }
                if (rowCount <= 1)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请填写Excel文件中的数据"
                    };
                }
                for (var row = 2; row <= rowCount; row++)
                {
                    var c = worksheet.Cells[row, 1].Value;
                    var i = worksheet.Cells[row, 2].Value;
                    var b = worksheet.Cells[row, 3].Value;
                    if (c == null || i == null || b == null)
                    {
                        continue;
                    }
                    excelData.Add(new InputBillImportDto()
                    {
                        CompanyName = c.ToString(),
                        InvoiceNo = i.ToString(),
                        BillCode = b.ToString()
                    });
                }
                if (excelData.Count != rowCount - 1)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "Excel中存在单元格为空的数据，请检查"
                    };
                }
            }
            var codes = excelData.Select(p => p.BillCode).Distinct().ToList();
            #region 校验
            var enableCodes = codes.Select(p => p.Contains("-PUA-") || p.Contains("-SI-")).ToList();
            if (enableCodes.Count != codes.Count)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "只能导入寄售转购货单或经销购货入库单"
                };
            }
            if (codes.Any(p => p.Contains("-PUA-")) && codes.Any(p => p.Contains("-SI-")))
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "不能同时添加寄售转购货单和入库单"
                };
            }
            var company = excelData.Select(p => p.CompanyName).Distinct().ToList();
            var invoiceNos = excelData.Select(p => p.InvoiceNo).Distinct().ToList();
            if (company.Count > 1)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "一次只能导入一个公司的数据"
                };
            }
            if (invoiceNos.Count != excelData.Count)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "发票存在重复数据，无法导入"
                };
            }
            if (codes.Count != excelData.Count)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "业务单据存在重复数据，无法导入"
                };
            }
            var invoices = await _queryInputBillPo.GetAllListAsync(p => p.Status == 1 && company.Contains(p.CompanName) && invoiceNos.Contains(p.InvoiceNumber));
            var existInvocices = invoices.Select(p => p.InvoiceNumber).ToList();
            var except = invoiceNos.Except(existInvocices).ToList();
            if (except.Count > 0)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"查询到的发票数与导入的发票数不一致，不存在或已提交的发票号：{string.Join(",", except)}"
                };
            }
            var agents = invoices.Select(p => p.AgentId).Distinct().ToList();
            if (agents.Count > 1)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = "一次只能导入一个供应商的数据"
                };
            }

            var invoceIds = invoices.Select(p => p.Id).ToList();
            var existDetailQuery = _queryInputBillSubmitDetail.GetIQueryable(p => invoceIds.Contains(p.InputBillId), new List<string>() { "InputBill" });
            var existDetail = await existDetailQuery.Select(p => p.InputBill.InvoiceNumber).ToListAsync();
            existDetail = existDetail.Distinct().ToList();
            if (existDetail.Count > 0)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"导入的发票中已存在添加了明细的发票，无法导入，发票号：{string.Join(",", existDetail)}"
                };
            }
            #endregion
            var type = 1;
            if (codes.Any(p => p.Contains("-PUA-")))
            {
                type = 1;
            }
            if (codes.Any(p => p.Contains("-SI-")))
            {
                type = 2;
            }
            var agentId = invoices.First().AgentId;
            var companyId = invoices.First().CompanyId;
            if (type == 1)
            {
                var puaInput = new ConsignToPurchaseDetailGroupInput
                {
                    AgentId = agentId.ToString(),
                    CompanyId = companyId.ToString(),
                    pageIndex = 1,
                    pageSize = 100000,
                    StrategyQuery = null,
                    InvoiceStatus = 0,
                    Codes = codes
                };
                var pageResult = await _purchaseApiClient.GetConsignToPurchaseDetailGroup(puaInput);
                var data = pageResult.Data;
                var existCodes = data.Select(p => p.Code).Distinct().ToList();
                var exceptCodes = codes.Except(existCodes).ToList();
                if (exceptCodes.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"查询到的业务单据数与导入的单据数不一致，不存在或已勾稽发票的单号为：{string.Join(",", exceptCodes)}"
                    };
                }
                var details = new List<InputBillSubmitDetail>();
                var msg = new List<string>();
                foreach (var p in excelData)
                {
                    var thisData = data.Where(t => t.Code == p.BillCode).ToList();
                    var hasQuantity = thisData.Where(p => p.InvoiceQuantity > 0).Select(p => p.Code).Distinct().ToList();
                    if (hasQuantity.Count > 0)
                    {
                        msg.AddRange(hasQuantity);
                        continue;
                    }
                    var thisInvoice = invoices.FirstOrDefault(t => t.InvoiceNumber == p.InvoiceNo);
                    thisData.ForEach(p =>
                    {
                        var d = new InputBillSubmitDetail()
                        {
                            Id = Guid.NewGuid(),
                            BusinessType = 3,
                            CreatedBy = "none",
                            CreatedTime = DateTime.Now,
                            InputBillId = thisInvoice.Id,
                            NoTaxAmount = p.Quantity * p.UnitCost,
                            NoTaxCost = Math.Round(p.UnitCost / (1 + (p.TaxRate / 100)), 10),
                            Quantity = p.Quantity,
                            ProductId = p.ProductId,
                            ProductName = p.ProductName,
                            ProductNameId = Guid.Parse(p.ProductNameId),
                            ProductNo = p.ProductNo,
                            ReceivedNumber = p.Quantity,
                            StoreInDate = p.BillDate.UtcDateTime,
                            StoreInItemCode = p.Code,
                            TaxCost = p.UnitCost,
                            TaxRate = p.TaxRate,
                        };
                        d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                        d.InputBillSubmitDetailQuantity = p.Details.Select(t => new InputBillSubmitDetailQuantity()
                        {
                            Id = Guid.NewGuid(),
                            InputBillSubmitDetailId = d.Id,
                            CreatedBy = "none",
                            CreatedTime = DateTime.Now,
                            Quantity = t.Quantity,
                            StoreInDetailId = t.PurchaseDetailId,
                        }).ToList();
                        details.Add(d);
                    });
                };
                if (msg.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"以下单据已有勾稽发票，无法使用此功能：{string.Join(",", msg)}"
                    };
                }
                #region 验证进项发票金额和选的发票是否对的上，差异在1以上的，不允许导入提交
                if (isSubmit)
                {
                    if (Math.Abs((Math.Abs(invoices.First().Amount) - Math.Abs(details.Sum(x => x.NoTaxAmount.Value)))) > 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "操作失败，原因：添加的发票明细金额之和不等于进项发票的总金额"
                        };
                    }
                }
                #endregion
                await _inputBillSubmitDetailRepository.AddManyAsync(details);
                var excecount = await _unitOfWork.CommitAsync();
            }
            else
            {
                var queryInput = new StoreInDetailQueryInput()
                {
                    agentId = agentId,
                    companyId = companyId,
                    storeInCodes = codes,
                    page = 1,
                    limit = 1000000
                };
                var resultList = await _inventoryApiClient.QueryStoreInByCompany(queryInput);
                var data = resultList.list;
                var existCodes = data.Select(p => p.storeInCode).Distinct().ToList();
                var exceptCodes = codes.Except(existCodes).ToList();
                if (exceptCodes.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"查询到的业务单据数与导入的单据数不一致，不存在或已勾稽发票的单号为：{string.Join(",", exceptCodes)}"
                    };
                }
                var details = new List<InputBillSubmitDetail>();
                var msg = new List<string>();
                foreach (var p in excelData)
                {
                    var thisData = data.Where(t => t.storeInCode == p.BillCode).ToList();
                    var hasQuantity = thisData.Where(p => p.invoiceQuantity > 0).Select(p => p.storeInCode).Distinct().ToList();
                    if (hasQuantity.Count > 0)
                    {
                        msg.AddRange(hasQuantity);
                        continue;
                    }
                    var thisInvoice = invoices.FirstOrDefault(t => t.InvoiceNumber == p.InvoiceNo);
                    thisData.ForEach(p =>
                    {
                        var d = new InputBillSubmitDetail()
                        {
                            Id = Guid.NewGuid(),
                            BusinessType = 1,
                            CreatedBy = "none",
                            CreatedTime = DateTime.Now,
                            InputBillId = thisInvoice.Id,
                            NoTaxAmount = p.quantity * p.unitCost,
                            NoTaxCost = Math.Round(p.unitCost.Value / (1 + (p.taxRate.Value / 100)), 10),
                            Quantity = p.quantity,
                            ProductId = p.productId,
                            ProductName = p.productName,
                            ProductNameId = p.productNameId,
                            ProductNo = p.productNo,
                            ReceivedNumber = p.quantity.Value,
                            StoreInDate = DateTimeHelper.LongToDateTime(p.storeInDate),
                            StoreInItemCode = p.storeInCode,
                            TaxCost = p.unitCost,
                            TaxRate = p.taxRate,
                        };
                        d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                        d.InputBillSubmitDetailQuantity = p.LotInfo.Select(t => new InputBillSubmitDetailQuantity()
                        {
                            Id = Guid.NewGuid(),
                            InputBillSubmitDetailId = d.Id,
                            CreatedBy = "none",
                            CreatedTime = DateTime.Now,
                            Quantity = t.quantity,
                            StoreInDetailId = Guid.Parse(t.storeInDetailId),
                        }).ToList();
                        details.Add(d);
                    });
                }
                if (msg.Count > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"以下单据已有勾稽发票，无法使用此功能：{string.Join(",", msg)}"
                    };
                }
                #region 验证进项发票金额和选的发票是否对的上，差异在1以上的，不允许导入提交
                if (isSubmit)
                {
                    if (Math.Abs((Math.Abs(invoices.First().Amount) - Math.Abs(details.Sum(x => x.NoTaxAmount.Value)))) > 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "操作失败，原因：添加的发票明细金额之和不等于进项发票的总金额"
                        };
                    }
                }
                #endregion
                await _inputBillSubmitDetailRepository.AddManyAsync(details);
                var excecount = await _unitOfWork.CommitAsync();
            }

            return new BaseResponseData<int>()
            {
                Code = CodeStatusEnum.Success,
                Message = "导入成功"
            };
        }

        /// <summary>
        /// N对N批量导入（bug同步修改，功能正常，已弃用）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> BatchImportInvoiceForBusinessBill(IFormFile file)
        {
            //行数
            int index = 1;
            try
            {
                if (file == null)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请上传导入文件"
                    };
                }

                Stream stream = file.OpenReadStream();
                var excelData = new List<InputBillBatchImportDto>();
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                    int rowCount = worksheet.Dimension.Rows;
                    if (rowCount <= 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "请填写Excel文件中的数据"
                        };
                    }
                    for (var row = 2; row <= rowCount; row++)
                    {
                        var c = worksheet.Cells[row, 1].Value;
                        var i = worksheet.Cells[row, 2].Value;
                        var b = worksheet.Cells[row, 3].Value;
                        var p = worksheet.Cells[row, 4].Value;
                        var n = worksheet.Cells[row, 5].Value;
                        if (c == null || i == null || b == null)
                        {
                            continue;
                        }
                        var billCode = b.ToString();
                        if (billCode != null && !billCode.Contains("-PUA-") && !billCode.Contains("-SI-") && !billCode.Contains("-SO-"))
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"第{row}行的业务单号错误，只能导入经销入库、经销调出、购货修订单"
                            };
                        }
                        decimal number = 0;
                        if (worksheet.Cells[row, 5].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out number);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的数量"
                                };
                            }
                        }
                        excelData.Add(new InputBillBatchImportDto()
                        {
                            CompanyName = c.ToString(),
                            InvoiceNo = i.ToString(),
                            BillCode = b.ToString(),
                            ProductNo = p != null ? p.ToString() : string.Empty,
                            Number = n != null ? Convert.ToDecimal(n.ToString()) : 0,
                        });
                    }
                    if (excelData.Count != rowCount - 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "Excel中存在单元格为空的数据，请检查"
                        };
                    }

                    // 检查是否有重复的数据
                    var duplicates = excelData.GroupBy(item => new { item.CompanyName, item.InvoiceNo, item.BillCode })
                                         .Where(group => group.Count() > 1)
                                         .Select(group => group.Key);

                    if (duplicates.Any())
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "Excel中存在相同公司、发票、单号的数据，请检查"
                        };
                    }

                    var codes = excelData.Select(p => p.BillCode).Distinct().ToList();
                    #region 校验
                    var enableCodes = codes.Where(p => p.Contains("-PUA-") || p.Contains("-SO-") || p.Contains("-SI-")).ToList();
                    if (enableCodes.Count != codes.Count)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "只能导入经销入库、经销调出、购货修订单"
                        };
                    }
                    //if (codes.Any(p => p.Contains("-PUA-")) && codes.Any(p => p.Contains("-SI-")) && codes.Any(p => p.Contains("-SO-")))
                    //{
                    //    return new BaseResponseData<int>()
                    //    {
                    //        Code = CodeStatusEnum.Failed,
                    //        Message = "只能添加一种类型的单"
                    //    };
                    //}
                    var company = excelData.Select(p => p.CompanyName).Distinct().ToList();
                    //if (company.Count > 1)
                    //{
                    //    return new BaseResponseData<int>()
                    //    {
                    //        Code = CodeStatusEnum.Failed,
                    //        Message = "一次只能导入一个公司的数据"
                    //    };
                    //}
                    #endregion
                    var invoiceNos = excelData.Select(p => p.InvoiceNo).Distinct().ToList();
                    //进项发票p.Status == 1 &&
                    var inputbills = await _queryInputBillPo.GetAllListAsync(p => company.Contains(p.CompanName) && invoiceNos.Contains(p.InvoiceNumber));
                    var inputbillIds = inputbills.Select(x => x.Id).ToList();
                    //进项发票详情
                    var inputbillSubmitDetails = await _db.InputBillSubmitDetails.Include(x => x.InputBillSubmitDetailQuantity).Where(x => inputbillIds.Contains(x.InputBillId)).AsNoTracking().ToListAsync();
                    //var inputbillSubmitDetailIds = inputbillSubmitDetails.Select(x => x.Id).ToList();
                    //进项发票详情数量
                    //var inputbillSubmitDetailQuantitys = await _db.InputBillSubmitDeatilQuantitys.Where(x => inputbillSubmitDetailIds.Contains(x.InputBillSubmitDetailId)).AsNoTracking().ToListAsync();
                    //分类型codes
                    var puaCodes = codes.Where(x => x.Contains("-PUA-")).ToList();
                    var siCodes = codes.Where(x => x.Contains("-SI-")).ToList();
                    var soCodes = codes.Where(x => x.Contains("-SO-")).ToList();

                    //待操作的集合
                    var addDetails = new List<InputBillSubmitDetail>();
                    var delDetailIds = new List<Guid>();

                    #region 购货修订
                    if (codes.Any(p => p.Contains("-PUA-")))
                    {
                        // 购货修订的没有数量一说，他只有金额
                        // 购货修订跟其他三个类型都不一样的，他也没有税额
                        var query = new PurchaseReviseForInputBillQueryDto()
                        {
                            pageIndex = 1,
                            pageSize = 100000,
                            codes = puaCodes
                        };
                        var pageResult = await _purchaseApiClient.GetPurchaseReviseForInputBills(query);
                        var data = pageResult.List;
                        var puaExcelData = excelData.Where(x => puaCodes.Contains(x.BillCode)).ToList();
                        foreach (var item in puaExcelData)
                        {
                            index = excelData.FindIndex(x => x.BillCode == item.BillCode);
                            if (index == -1)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"获取行数失败，未找到业务单号为{item.BillCode}的行数"
                                };
                            }
                            index += 1;
                            if (!string.IsNullOrEmpty(item.ProductNo) && item.Number == 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"购货修订单本次入票数量不能为0，业务单号{item.BillCode}，发票号{item.InvoiceNo}，公司{item.CompanyName}，货号{item.ProductNo}"
                                };
                            }
                            // 查询单条进项发票
                            var single = inputbills.FirstOrDefault(x => x.InvoiceNumber == item.InvoiceNo && x.CompanName == item.CompanyName);
                            if (single != null)
                            {
                                if (single.Status == 2)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已提交状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                if (single.Status == 9)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已忽略状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = data.Where(x => x.agentName == single.AgentName && x.companyName == item.CompanyName && x.purchaseOrderCode == item.BillCode).ToList();
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}未找到导入数据，请检查"
                                    };
                                }
                                if (!string.IsNullOrEmpty(item.ProductNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.productNo == item.ProductNo).ToList();
                                }
                                var queryDetailCodess = queryDetails.Select(x => x.purchaseOrderCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    if (p.reviseAmount < 0 && item.Number > 0)
                                    {
                                        continue;
                                        //return new BaseResponseData<int>()
                                        //{
                                        //    Code = CodeStatusEnum.Failed,
                                        //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}入票数错误，请检查"
                                        //};
                                    }
                                    if (p.reviseAmount > 0 && item.Number < 0)
                                    {
                                        continue;
                                        //return new BaseResponseData<int>()
                                        //{
                                        //    Code = CodeStatusEnum.Failed,
                                        //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}入票数错误，请检查！"
                                        //};
                                    }
                                    // 大于则导入剩下数量
                                    var finishNumber = item.Number > p.canInvoiceAmount ? p.canInvoiceAmount : item.Number;
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 4;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxAmount = finishNumber;
                                    d.NoTaxCost = finishNumber.HasValue ? Math.Round(finishNumber.Value / (1 + p.taxRate / 100.00M), 4) : 0;
                                    d.Quantity = 1;
                                    d.ProductId = p.productId;
                                    d.ProductName = p.productName;
                                    d.ProductNameId = p.productNameId;
                                    d.ProductNo = p.productNo;
                                    d.ReceivedNumber = 1;
                                    d.StoreInDate = p.billDate.UtcDateTime;
                                    d.StoreInItemCode = p.purchaseOrderCode;
                                    d.TaxCost = finishNumber;
                                    d.TaxRate = p.taxRate;
                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost); ;
                                    addDetails.Add(d);
                                }
                            }
                        }
                    }
                    #endregion

                    #region 经销调出
                    if (codes.Any(p => p.Contains("-SO-")))
                    {
                        // 经销调出
                        var soInput = new StoreInDetailQueryInput
                        {
                            page = 1,
                            limit = 100000,
                            storeInCodes = soCodes,
                            storeOutTypeList = new List<int> { 1, 6, 19 }
                        };
                        var resultList = await _inventoryApiClient.QueryStoreOutByCompany(soInput);
                        var data = resultList.list;
                        var soExcelData = excelData.Where(x => soCodes.Contains(x.BillCode)).ToList();
                        foreach (var item in soExcelData)
                        {
                            index = excelData.FindIndex(x => x.BillCode == item.BillCode);
                            if (index == -1)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"获取行数失败，未找到业务单号为{item.BillCode}的行数"
                                };
                            }
                            index += 1;
                            if (item.Number <= 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"经销调出本次入票数量至少大于0，业务单号{item.BillCode}，发票号{item.InvoiceNo}，公司{item.CompanyName}，货号{item.ProductNo}"
                                };
                            }
                            // 查询单条进项发票
                            var single = inputbills.FirstOrDefault(x => x.InvoiceNumber == item.InvoiceNo && x.CompanName == item.CompanyName);
                            if (single != null)
                            {
                                if (single.Status == 2)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已提交状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                if (single.Status == 9)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已忽略状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = data.Where(x => x.AgentName == single.AgentName && x.CompanyName == item.CompanyName && x.StoreOutCode == item.BillCode).ToList();
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}未找到导入数据，请检查"
                                    };
                                }
                                if (!string.IsNullOrEmpty(item.ProductNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.ProductNo == item.ProductNo).ToList();
                                }
                                var queryDetailCodess = queryDetails.Select(x => x.StoreOutCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 2;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxCost = p.SettlementCost / (1 + p.TaxRate / 100);
                                    d.Quantity = item.Number > 0 ? 0 - item.Number : item.Number;//经销调出使用负数
                                    d.NoTaxAmount = d.Quantity * p.SettlementCost;
                                    d.ProductId = !string.IsNullOrEmpty(p.ProductId) ? Guid.Parse(p.ProductId) : null;
                                    d.ProductName = p.ProductName;
                                    d.ProductNameId = !string.IsNullOrEmpty(p.ProductNameId) ? Guid.Parse(p.ProductNameId) : null;
                                    d.ProductNo = p.ProductNo;
                                    d.StoreInDate = DateTimeOffset.FromUnixTimeMilliseconds(p.BillDate).UtcDateTime;
                                    d.StoreInItemCode = p.StoreOutCode;
                                    d.TaxCost = p.SettlementCost;
                                    d.TaxRate = p.TaxRate;

                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                                    if (p.LotInfo != null)
                                    {
                                        var firstLotInfo = p.LotInfo[0];
                                        d.ReceivedNumber = firstLotInfo.invoiceQuantity.HasValue ? firstLotInfo.invoiceQuantity.Value : 0;
                                        var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
                                        foreach (var t in p.LotInfo)
                                        {
                                            var canQuantity = t.quantity - t.invoiceQuantity;
                                            if (canQuantity == 0)
                                            {
                                                continue;
                                                //return new BaseResponseData<int>()
                                                //{
                                                //    Code = CodeStatusEnum.Failed,
                                                //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}已全部入票，当前可入发票数0！"
                                                //};
                                            }
                                            //检查入票数
                                            if (item.Number > canQuantity)
                                            {
                                                continue;
                                                //return new BaseResponseData<int>()
                                                //{
                                                //    Code = CodeStatusEnum.Failed,
                                                //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}可入发票数不足，当前可入发票数{canQuantity}！"
                                                //};
                                            }
                                            // 大于则导入剩下数量
                                            var finishNumber = item.Number > canQuantity ? canQuantity : item.Number;
                                            var ibsdq = new InputBillSubmitDetailQuantity();
                                            ibsdq.Id = Guid.NewGuid();
                                            ibsdq.InputBillSubmitDetailId = d.Id;
                                            ibsdq.CreatedBy = "none";
                                            ibsdq.CreatedTime = DateTime.Now;
                                            ibsdq.Quantity = Math.Abs(finishNumber.Value);
                                            ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.id) ? Guid.Parse(t.id) : Guid.Empty;
                                            inputBillSubmitDetailQuantities.Add(ibsdq);
                                        }
                                        d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
                                    }
                                    addDetails.Add(d);
                                }
                            }
                        }
                    }
                    #endregion

                    #region 经销购货入库单
                    if (codes.Any(p => p.Contains("-SI-")))
                    {
                        // 经销购货入库单
                        var puaInput = new StoreInDetailQueryInput
                        {
                            storeInCodes = siCodes
                        };
                        var resultList = await _inventoryApiClient.QueryStoreInByCompany(puaInput);
                        var data = resultList.list;
                        var siExcelData = excelData.Where(x => siCodes.Contains(x.BillCode)).ToList();
                        foreach (var item in siExcelData)
                        {
                            index = excelData.FindIndex(x => x.BillCode == item.BillCode);
                            if (index == -1)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"获取行数失败，未找到业务单号为{item.BillCode}的行数"
                                };
                            }
                            index += 1;
                            if (item.Number <= 0)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"经销购货入库单本次入票数量至少大于0，业务单号{item.BillCode}，发票号{item.InvoiceNo}，公司{item.CompanyName}，货号{item.ProductNo}"
                                };
                            }
                            // 查询单条进项发票
                            var single = inputbills.FirstOrDefault(x => x.InvoiceNumber == item.InvoiceNo && x.CompanName == item.CompanyName);
                            if (single != null)
                            {
                                if (single.Status == 2)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已提交状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                if (single.Status == 9)
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行已忽略状态的发票，不允许导入。发票号{item.InvoiceNo}"
                                    };
                                }
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = data.Where(x => x.agentId == single.AgentId && x.companyId == single.CompanyId && x.storeInCode == item.BillCode).ToList();
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    return new BaseResponseData<int>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}未找到导入数据，请检查"
                                    };
                                }
                                if (!string.IsNullOrEmpty(item.ProductNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.productNo == item.ProductNo).ToList();
                                }
                                var queryDetailProductNos = queryDetails.Select(x => x.storeInCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailProductNos.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 1;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxAmount = item.Number * p.unitCost;
                                    d.NoTaxCost = p.unitCost / (1 + p.taxRate / 100);
                                    d.Quantity = item.Number;
                                    d.ProductId = p.productId;
                                    d.ProductName = p.productName;
                                    d.ProductNameId = p.productNameId;
                                    d.ProductNo = p.productNo;
                                    d.ReceivedNumber = item.Number.HasValue ? item.Number.Value : 0;
                                    d.StoreInDate = DateTimeHelper.LongToDateTime(p.storeInDate);
                                    d.StoreInItemCode = p.storeInCode;
                                    d.TaxCost = p.unitCost;
                                    d.TaxRate = p.taxRate;

                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                                    if (p.LotInfo != null)
                                    {
                                        var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
                                        foreach (var t in p.LotInfo)
                                        {
                                            var canQuantity = t.quantity - t.invoiceQuantity;
                                            if (canQuantity == 0)
                                            {
                                                continue;
                                                //return new BaseResponseData<int>()
                                                //{
                                                //    Code = CodeStatusEnum.Failed,
                                                //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}已全部入票，当前可入发票数0！"
                                                //};
                                            }
                                            //检查入票数
                                            if (item.Number > canQuantity)
                                            {
                                                continue;
                                                //return new BaseResponseData<int>()
                                                //{
                                                //    Code = CodeStatusEnum.Failed,
                                                //    Message = $"第{index}行发票号{item.InvoiceNo}，业务单号{item.BillCode}，货号{item.ProductNo}可入发票数不足，当前可入发票数{canQuantity}！"
                                                //};
                                            }
                                            // 大于则导入剩下数量
                                            var finishNumber = item.Number > canQuantity ? canQuantity : item.Number;
                                            var ibsdq = new InputBillSubmitDetailQuantity();
                                            ibsdq.Id = Guid.NewGuid();
                                            ibsdq.InputBillSubmitDetailId = d.Id;
                                            ibsdq.CreatedBy = "none";
                                            ibsdq.CreatedTime = DateTime.Now;
                                            ibsdq.Quantity = finishNumber;
                                            ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.storeInDetailId) ? Guid.Parse(t.storeInDetailId) : Guid.Empty;
                                            inputBillSubmitDetailQuantities.Add(ibsdq);
                                        }
                                        d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
                                    }
                                    addDetails.Add(d);
                                }
                            }
                            else
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"上传失败，第{index}行根据发票号{item.InvoiceNo}、公司{item.CompanyName}未找到对应进项票信息"
                                };
                            }
                        }
                    }
                    #endregion

                    if (delDetailIds.Count > 0)
                    {
                        // 删除
                        await _inputBillSubmitDetailRepository.DeteteManyAsync(delDetailIds);
                    }
                    if (addDetails.Count > 0)
                    {
                        // 添加
                        await _inputBillSubmitDetailRepository.AddManyAsync(addDetails);
                        var excecount = await _unitOfWork.CommitAsync();
                    }
                }

                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Success,
                    Message = "导入成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"上传失败，第{index}行错误信息：模板错误或导入数据未找到，请检查Excel中的内容"
                };
            }
        }

        #region #80696 Begin
        /// <summary>
        /// N对N批量导入（返回错误信息文件）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>

        //public async Task<BaseResponseData<string>> BatchImportInvoiceForBusinessBillExcel(IFormFile file)
        //{
        //    try
        //    {
        //        if (file == null || file.Length == 0)
        //        {
        //            return new BaseResponseData<string>()
        //            {
        //                Code = CodeStatusEnum.Failed,
        //                Message = $"请填写Excel文件中的数据"
        //            };
        //        }
        //        // 仅取参数集合
        //        var excelData = new List<InputBillBatchImportDto>();

        //        // 打开文件的流
        //        var stream = file.OpenReadStream();
        //        using (ExcelPackage package = new ExcelPackage(stream))
        //        {
        //            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
        //            ExcelWorksheet worksheet = package.Workbook.Worksheets[0];

        //            #region 基础校验
        //            int rowCount = worksheet.Dimension.Rows;
        //            if (rowCount <= 1)
        //            {
        //                return new BaseResponseData<string>()
        //                {
        //                    Code = CodeStatusEnum.Failed,
        //                    Message = $"请填写Excel文件中的数据"
        //                };
        //            }
        //            for (var row = 2; row <= rowCount; row++)
        //            {
        //                var c = worksheet.Cells[row, 1].Value;
        //                var i = worksheet.Cells[row, 2].Value;
        //                var b = worksheet.Cells[row, 3].Value;
        //                var p = worksheet.Cells[row, 4].Value;
        //                var n = worksheet.Cells[row, 5].Value;
        //                if (c == null || i == null || b == null || p == null || n == null)
        //                {
        //                    continue;
        //                }
        //                var billCode = b.ToString();
        //                if (billCode != null && !billCode.Contains("-PUA-") && !billCode.Contains("-SI-") && !billCode.Contains("-SO-"))
        //                {
        //                    return new BaseResponseData<string>()
        //                    {
        //                        Code = CodeStatusEnum.Failed,
        //                        Message = $"只能导入经销入库、经销调出、购货修订单"
        //                    };
        //                }
        //                decimal number = 0;
        //                if (worksheet.Cells[row, 5].Value != null)
        //                {
        //                    bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out number);
        //                    if (!isValid)
        //                    {
        //                        return new BaseResponseData<string>()
        //                        {
        //                            Code = CodeStatusEnum.Failed,
        //                            Message = $"数量信息错误"
        //                        };
        //                    }
        //                }
        //                excelData.Add(new InputBillBatchImportDto()
        //                {
        //                    CompanyName = c.ToString(),
        //                    InvoiceNo = i.ToString(),
        //                    BillCode = b.ToString(),
        //                    ProductNo = p.ToString(),
        //                    Number = Convert.ToDecimal(n.ToString())
        //                });
        //            }

        //            if (excelData.Count != rowCount - 1)
        //            {
        //                return new BaseResponseData<string>()
        //                {
        //                    Code = CodeStatusEnum.Failed,
        //                    Message = $"Excel中存在单元格为空的数据，请检查"
        //                };
        //            }
        //            // 检查是否有重复的数据
        //            var duplicates = excelData.GroupBy(item => new { item.CompanyName, item.InvoiceNo, item.ProductNo, item.BillCode })
        //                                 .Where(group => group.Count() > 1)
        //                                 .Select(group => group.Key);

        //            if (duplicates.Any())
        //            {
        //                return new BaseResponseData<string>()
        //                {
        //                    Code = CodeStatusEnum.Failed,
        //                    Message = $"Excel中存在相同公司、发票、单号以及货号的数据，请检查"
        //                };
        //            }
        //            var codes = excelData.Select(p => p.BillCode).Distinct().ToList();
        //            #endregion

        //            #region 取值集合
        //            //公司集合
        //            var company = excelData.Select(p => p.CompanyName).Distinct().ToList();
        //            //发票号集合
        //            var invoiceNos = excelData.Select(p => p.InvoiceNo).Distinct().ToList();
        //            //进项发票p.Status == 1 &&
        //            var inputbills = await _queryInputBillPo.GetAllListAsync(p => company.Contains(p.CompanName) && invoiceNos.Contains(p.InvoiceNumber));
        //            var inputbillIds = inputbills.Select(x => x.Id).ToList();
        //            //进项发票详情
        //            var inputbillSubmitDetails = await _db.InputBillSubmitDetails.Where(x => inputbillIds.Contains(x.InputBillId)).AsNoTracking().ToListAsync();
        //            var inputbillSubmitDetailIds = inputbillSubmitDetails.Select(x => x.Id).ToList();
        //            //进项发票详情数量
        //            var inputbillSubmitDetailQuantitys = await _db.InputBillSubmitDeatilQuantitys.Where(x => inputbillSubmitDetailIds.Contains(x.InputBillSubmitDetailId)).AsNoTracking().ToListAsync();
        //            //分类型codes
        //            var puaCodes = codes.Where(x => x.Contains("-PUA-")).ToList();
        //            var siCodes = codes.Where(x => x.Contains("-SI-")).ToList();
        //            var soCodes = codes.Where(x => x.Contains("-SO-")).ToList();

        //            //待操作的集合
        //            var addDetails = new List<InputBillSubmitDetail>();
        //            var delDetailIds = new List<Guid>();
        //            #endregion

        //            #region 各类型单查询
        //            //购货修订
        //            var puaResultData = new List<PurchaseReviseForInputBill>();
        //            if (codes.Any(p => p.Contains("-PUA-")))
        //            {
        //                var query = new PurchaseReviseForInputBillQueryDto()
        //                {
        //                    pageIndex = 1,
        //                    pageSize = 100000,
        //                    codes = puaCodes
        //                };
        //                var pageResult = await _purchaseApiClient.GetPurchaseReviseForInputBills(query);
        //                puaResultData = pageResult.List;
        //            }

        //            // 经销调出
        //            var soResultData = new List<StoreOutDetailForFinanceOutput>();
        //            if (codes.Any(p => p.Contains("-SO-")))
        //            {
        //                var soInput = new StoreInDetailQueryInput
        //                {
        //                    page = 1,
        //                    limit = 100000,
        //                    storeInCodes = soCodes,
        //                    storeOutTypeList = new List<int> { 1, 6 }
        //                };
        //                var resultList = await _inventoryApiClient.QueryStoreOutByCompany(soInput);
        //                soResultData = resultList.list;
        //            }

        //            // 经销购货入库单
        //            var siResultData = new List<InventoryStoreInOutput>();
        //            if (codes.Any(p => p.Contains("-SI-")))
        //            {
        //                var puaInput = new StoreInDetailQueryInput
        //                {
        //                    storeInCodes = siCodes
        //                };
        //                var resultList = await _inventoryApiClient.QueryStoreInByCompany(puaInput);
        //                siResultData = resultList.list;
        //            }
        //            #endregion

        //            #region excel遍历校验
        //            var errExcelData = new List<InputBillBatchImportDto>();
        //            for (var row = 2; row <= rowCount; row++)
        //            {
        //                var companyName = worksheet.Cells[row, 1].Value.ToString();
        //                var invoiceNo = worksheet.Cells[row, 2].Value.ToString();
        //                var billCode = worksheet.Cells[row, 3].Value.ToString();
        //                var productNo = worksheet.Cells[row, 4].Value.ToString();
        //                var number = Convert.ToDecimal(worksheet.Cells[row, 5].Value);

        //                //判断
        //                if (string.IsNullOrEmpty(billCode))
        //                {
        //                    errExcelData.Add(new InputBillBatchImportDto()
        //                    {
        //                        CompanyName = companyName,
        //                        InvoiceNo = invoiceNo,
        //                        BillCode = billCode,
        //                        ProductNo = productNo,
        //                        Number = number,
        //                        ErrMessage = "业务单号为空"
        //                    });
        //                    continue;
        //                }
        //                else
        //                {
        //                    // 查询单条进项发票
        //                    var single = inputbills.FirstOrDefault(x => x.InvoiceNumber == invoiceNo && x.CompanName == companyName);
        //                    if (single == null)
        //                    {
        //                        errExcelData.Add(new InputBillBatchImportDto()
        //                        {
        //                            CompanyName = companyName,
        //                            InvoiceNo = invoiceNo,
        //                            BillCode = billCode,
        //                            ProductNo = productNo,
        //                            Number = number,
        //                            ErrMessage = "未找到进项票"
        //                        });
        //                        continue;
        //                    }
        //                    if (single.Status == 2)
        //                    {
        //                        errExcelData.Add(new InputBillBatchImportDto()
        //                        {
        //                            CompanyName = companyName,
        //                            InvoiceNo = invoiceNo,
        //                            BillCode = billCode,
        //                            ProductNo = productNo,
        //                            Number = number,
        //                            ErrMessage = "已提交状态的发票，不允许导入"
        //                        });
        //                        continue;
        //                    }
        //                    if (single.Status == 9)
        //                    {
        //                        errExcelData.Add(new InputBillBatchImportDto()
        //                        {
        //                            CompanyName = companyName,
        //                            InvoiceNo = invoiceNo,
        //                            BillCode = billCode,
        //                            ProductNo = productNo,
        //                            Number = number,
        //                            ErrMessage = "已忽略状态的发票，不允许导入"
        //                        });
        //                        continue;
        //                    }
        //                    if (billCode.Contains("-PUA-"))
        //                    {
        //                        if (number == 0)
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "购货修订单本次入票数量不能为0"
        //                            });
        //                            continue;
        //                        }
        //                        // 当前详情数据
        //                        var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
        //                        // 查询导入的明细数据
        //                        var queryDetails = puaResultData.Where(x => x.agentName == single.AgentName && x.companyName == companyName && x.purchaseOrderCode == billCode && x.productNo == productNo).ToList();
        //                        if (queryDetails == null || !queryDetails.Any())
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "未找到导入数据，请检查"
        //                            });
        //                            continue;
        //                        }
        //                        var queryDetailCodess = queryDetails.Select(x => x.purchaseOrderCode).ToList();
        //                        // 导入中已存在的详情数据 -> 清除
        //                        var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
        //                        // 导入中已存在的详情数量数据 -> 清除
        //                        var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
        //                        delDetailIds.AddRange(existsDetailIds);
        //                        // 整理导入明细
        //                        foreach (var p in queryDetails)
        //                        {
        //                            if (p.reviseAmount < 0 && number > 0)
        //                            {
        //                                errExcelData.Add(new InputBillBatchImportDto()
        //                                {
        //                                    CompanyName = companyName,
        //                                    InvoiceNo = invoiceNo,
        //                                    BillCode = billCode,
        //                                    ProductNo = productNo,
        //                                    Number = number,
        //                                    ErrMessage = "入票数错误"
        //                                });
        //                                continue;
        //                            }
        //                            if (p.reviseAmount > 0 && number < 0)
        //                            {
        //                                errExcelData.Add(new InputBillBatchImportDto()
        //                                {
        //                                    CompanyName = companyName,
        //                                    InvoiceNo = invoiceNo,
        //                                    BillCode = billCode,
        //                                    ProductNo = productNo,
        //                                    Number = number,
        //                                    ErrMessage = "入票数错误"
        //                                });
        //                                continue;
        //                            }
        //                            var d = new InputBillSubmitDetail();
        //                            d.Id = Guid.NewGuid();
        //                            d.BusinessType = 4;
        //                            d.CreatedBy = "none";
        //                            d.CreatedTime = DateTime.Now;
        //                            d.InputBillId = single.Id;
        //                            d.NoTaxAmount = number;
        //                            d.NoTaxCost = Math.Round(number / (1 + p.taxRate / 100.00M), 4);
        //                            d.Quantity = 1;
        //                            d.ProductId = p.productId;
        //                            d.ProductName = p.productName;
        //                            d.ProductNameId = p.productNameId;
        //                            d.ProductNo = p.productNo;
        //                            d.ReceivedNumber = 1;
        //                            d.StoreInDate = p.billDate.UtcDateTime;
        //                            d.StoreInItemCode = p.purchaseOrderCode;
        //                            d.TaxCost = number;
        //                            d.TaxRate = p.taxRate;
        //                            d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost); ;
        //                            addDetails.Add(d);
        //                        }
        //                        errExcelData.Add(new InputBillBatchImportDto()
        //                        {
        //                            CompanyName = companyName,
        //                            InvoiceNo = invoiceNo,
        //                            BillCode = billCode,
        //                            ProductNo = productNo,
        //                            Number = number,
        //                            ErrMessage = string.Empty
        //                        });
        //                    }
        //                    else if (billCode.Contains("-SO-"))
        //                    {
        //                        if (number <= 0)
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "入票数错误"
        //                            });
        //                            continue;
        //                        }
        //                        // 当前详情数据
        //                        var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
        //                        // 查询导入的明细数据
        //                        var queryDetails = soResultData.Where(x => x.AgentName == single.AgentName && x.CompanyName == companyName && x.StoreOutCode == billCode && x.ProductNo == productNo).ToList();
        //                        if (queryDetails == null || !queryDetails.Any())
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "未找到导入数据"
        //                            });
        //                            continue;
        //                        }
        //                        var queryDetailCodess = queryDetails.Select(x => x.StoreOutCode).ToList();
        //                        // 导入中已存在的详情数据 -> 清除
        //                        var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
        //                        // 导入中已存在的详情数量数据 -> 清除
        //                        var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
        //                        delDetailIds.AddRange(existsDetailIds);
        //                        var errDicValue = string.Empty;
        //                        // 整理导入明细
        //                        foreach (var p in queryDetails)
        //                        {
        //                            var d = new InputBillSubmitDetail();
        //                            d.Id = Guid.NewGuid();
        //                            d.BusinessType = 2;
        //                            d.CreatedBy = "none";
        //                            d.CreatedTime = DateTime.Now;
        //                            d.InputBillId = single.Id;
        //                            d.NoTaxCost = p.UnitCost / (1 + p.TaxRate / 100);
        //                            d.Quantity = number > 0 ? 0 - number : number;
        //                            d.NoTaxAmount = d.Quantity * p.UnitCost;
        //                            d.ProductId = !string.IsNullOrEmpty(p.ProductId) ? Guid.Parse(p.ProductId) : null;
        //                            d.ProductName = p.ProductName;
        //                            d.ProductNameId = !string.IsNullOrEmpty(p.ProductNameId) ? Guid.Parse(p.ProductNameId) : null;
        //                            d.ProductNo = p.ProductNo;
        //                            d.ReceivedNumber = number > 0 ? 0 - number : number; //经销调出使用负数
        //                            d.StoreInDate = DateTimeOffset.FromUnixTimeMilliseconds(p.BillDate).UtcDateTime;
        //                            d.StoreInItemCode = p.StoreOutCode;
        //                            d.TaxCost = p.UnitCost;
        //                            d.TaxRate = p.TaxRate;

        //                            d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
        //                            if (p.LotInfo != null)
        //                            {
        //                                var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
        //                                foreach (var t in p.LotInfo)
        //                                {
        //                                    d.ProducerOrderNo = t.producerOrderNo;
        //                                    var canQuantity = t.quantity - t.invoiceQuantity;
        //                                    if (canQuantity == 0)
        //                                    {
        //                                        errDicValue += "当前可入发票数0！";
        //                                        continue;
        //                                    }
        //                                    //检查入票数
        //                                    if (number > canQuantity)
        //                                    {
        //                                        errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}";
        //                                        continue;
        //                                    }
        //                                    var ibsdq = new InputBillSubmitDetailQuantity();
        //                                    ibsdq.Id = Guid.NewGuid();
        //                                    ibsdq.InputBillSubmitDetailId = d.Id;
        //                                    ibsdq.CreatedBy = "none";
        //                                    ibsdq.CreatedTime = DateTime.Now;
        //                                    ibsdq.Quantity = Math.Abs(number);
        //                                    ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.id) ? Guid.Parse(t.id) : Guid.Empty;
        //                                    inputBillSubmitDetailQuantities.Add(ibsdq);
        //                                }
        //                                d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
        //                            }
        //                            addDetails.Add(d);
        //                        }
        //                        if (!string.IsNullOrEmpty(errDicValue))
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = errDicValue
        //                            });
        //                            continue;
        //                        }
        //                        else
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = string.Empty
        //                            });
        //                        }
        //                    }
        //                    else if (billCode.Contains("-SI-"))
        //                    {
        //                        if (number <= 0)
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "入票数错误"
        //                            });
        //                            continue;
        //                        }
        //                        // 当前详情数据
        //                        var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
        //                        // 查询导入的明细数据
        //                        var queryDetails = siResultData.Where(x => x.agentId == single.AgentId && x.companyId == single.CompanyId && x.storeInCode == billCode && x.productNo == productNo).ToList();
        //                        if (queryDetails == null || !queryDetails.Any())
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = "未找到导入数据"
        //                            });
        //                            continue;
        //                        }
        //                        var queryDetailProductNos = queryDetails.Select(x => x.storeInCode).ToList();
        //                        // 导入中已存在的详情数据 -> 清除
        //                        var existsDetails = currentDetails.Where(x => queryDetailProductNos.Contains(x.StoreInItemCode)).ToList();
        //                        // 导入中已存在的详情数量数据 -> 清除
        //                        var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
        //                        delDetailIds.AddRange(existsDetailIds);
        //                        var errDicValue = string.Empty;
        //                        // 整理导入明细
        //                        foreach (var p in queryDetails)
        //                        {
        //                            var d = new InputBillSubmitDetail();
        //                            d.Id = Guid.NewGuid();
        //                            d.BusinessType = 1;
        //                            d.CreatedBy = "none";
        //                            d.CreatedTime = DateTime.Now;
        //                            d.InputBillId = single.Id;
        //                            d.NoTaxAmount = number * p.unitCost;
        //                            d.NoTaxCost = p.unitCost / (1 + p.taxRate / 100);
        //                            d.Quantity = number;
        //                            d.ProductId = p.productId;
        //                            d.ProductName = p.productName;
        //                            d.ProductNameId = p.productNameId;
        //                            d.ProductNo = p.productNo;
        //                            d.ReceivedNumber = number;
        //                            d.StoreInDate = DateTimeHelper.LongToDateTime(p.storeInDate);
        //                            d.StoreInItemCode = p.storeInCode;
        //                            d.TaxCost = p.unitCost;
        //                            d.TaxRate = p.taxRate;

        //                            d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
        //                            if (p.LotInfo != null)
        //                            {
        //                                var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
        //                                foreach (var t in p.LotInfo)
        //                                {
        //                                    d.ProducerOrderNo = t.producerOrderNo;
        //                                    var canQuantity = t.quantity - t.invoiceQuantity;
        //                                    if (canQuantity == 0)
        //                                    {
        //                                        errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}！！";
        //                                        continue;
        //                                    }
        //                                    if (canQuantity < number)
        //                                    {
        //                                        errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}！！";
        //                                        continue;
        //                                    }
        //                                    var ibsdq = new InputBillSubmitDetailQuantity();
        //                                    ibsdq.Id = Guid.NewGuid();
        //                                    ibsdq.InputBillSubmitDetailId = d.Id;
        //                                    ibsdq.CreatedBy = "none";
        //                                    ibsdq.CreatedTime = DateTime.Now;
        //                                    ibsdq.Quantity = number;
        //                                    ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.storeInDetailId) ? Guid.Parse(t.storeInDetailId) : Guid.Empty;
        //                                    inputBillSubmitDetailQuantities.Add(ibsdq);
        //                                }
        //                                d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
        //                            }
        //                            addDetails.Add(d);
        //                        }
        //                        if (!string.IsNullOrEmpty(errDicValue))
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = errDicValue
        //                            });
        //                            continue;
        //                        }
        //                        else
        //                        {
        //                            errExcelData.Add(new InputBillBatchImportDto()
        //                            {
        //                                CompanyName = companyName,
        //                                InvoiceNo = invoiceNo,
        //                                BillCode = billCode,
        //                                ProductNo = productNo,
        //                                Number = number,
        //                                ErrMessage = string.Empty
        //                            });
        //                        }
        //                    }
        //                    else
        //                    {
        //                        errExcelData.Add(new InputBillBatchImportDto()
        //                        {
        //                            CompanyName = companyName,
        //                            InvoiceNo = invoiceNo,
        //                            BillCode = billCode,
        //                            ProductNo = productNo,
        //                            Number = number,
        //                            ErrMessage = "业务单号错误，只能导入经销入库、经销调出、购货修订单"
        //                        });
        //                        continue;
        //                    }
        //                }
        //            }
        //            #endregion

        //            #region 错误消息
        //            var err = errExcelData.Where(x => !string.IsNullOrEmpty(x.ErrMessage)).ToList();
        //            if (errExcelData.Any() && err.Count > 0)
        //            {
        //                // 写入Excel上传
        //                var errStream = new MemoryStream();
        //                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        //                using (var errPackage = new ExcelPackage(errStream))
        //                {
        //                    var columnIndex = 1;
        //                    var errWorksheet = errPackage.Workbook.Worksheets.Add("进项发票批量导入明细错误信息");
        //                    errWorksheet.SetValue(1, columnIndex++, "公司名称");
        //                    errWorksheet.SetValue(1, columnIndex++, "发票号");
        //                    errWorksheet.SetValue(1, columnIndex++, "业务单号");
        //                    errWorksheet.SetValue(1, columnIndex++, "货号");
        //                    errWorksheet.SetValue(1, columnIndex++, "数量");
        //                    errWorksheet.SetValue(1, columnIndex++, "错误信息");
        //                    var index = 2;
        //                    errExcelData.ForEach(p =>
        //                    {
        //                        columnIndex = 1;
        //                        errWorksheet.SetValue(index, columnIndex++, p.CompanyName);
        //                        errWorksheet.SetValue(index, columnIndex++, p.InvoiceNo);
        //                        errWorksheet.SetValue(index, columnIndex++, p.BillCode);
        //                        errWorksheet.SetValue(index, columnIndex++, p.ProductNo);
        //                        errWorksheet.SetValue(index, columnIndex++, p.Number);
        //                        errWorksheet.SetValue(index, columnIndex++, p.ErrMessage);
        //                        index++;
        //                    });

        //                    errPackage.SaveAs(errStream);
        //                    MemoryStream msFailReport = new MemoryStream(errPackage.GetAsByteArray());
        //                    //var content = new BinaryData(errStream.GetBuffer());
        //                    var fileIdout = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "进项发票批量导入明细错误信息.xlsx");
        //                    return new BaseResponseData<string>()
        //                    {
        //                        Code = CodeStatusEnum.ParamFailed,
        //                        Data = fileIdout.ToString(),
        //                        Message = $"上传失败，请下载错误信息文件查看详细"
        //                    };
        //                }
        //            }
        //            #endregion

        //            #region 执行存储
        //            if (err.Count == 0)
        //            {
        //                if (delDetailIds.Count > 0)
        //                {
        //                    // 删除
        //                    await _inputBillSubmitDetailRepository.DeteteManyAsync(delDetailIds);
        //                }
        //                if (addDetails.Count > 0)
        //                {
        //                    // 添加
        //                    await _inputBillSubmitDetailRepository.AddManyAsync(addDetails);
        //                    var excecount = await _unitOfWork.CommitAsync();
        //                }
        //            }
        //            #endregion

        //            return new BaseResponseData<string>()
        //            {
        //                Code = CodeStatusEnum.Success,
        //                Message = $"导入成功"
        //            };
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        return new BaseResponseData<string>()
        //        {
        //            Code = CodeStatusEnum.Failed,
        //            Message = $"上传失败，错误信息：模板错误或导入数据未找到，请检查Excel中的内容"
        //        };
        //    }
        //}
        #endregion

        #region #80696 推迟至11月hotfix，暂未通过测试
        /// <summary>
        /// N对N批量导入（返回错误信息文件）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>

        public async Task<BaseResponseData<string>> BatchImportInvoiceForBusinessBillExcel(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return new BaseResponseData<string>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"请填写Excel文件中的数据"
                    };
                }
                // 仅取参数集合
                var excelData = new List<InputBillBatchImportDto>();

                // 打开文件的流
                var stream = file.OpenReadStream();
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];

                    #region 基础校验
                    int rowCount = worksheet.Dimension.Rows;
                    if (rowCount <= 1)
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"请填写Excel文件中的数据"
                        };
                    }
                    for (var row = 2; row <= rowCount; row++)
                    {
                        var c = worksheet.Cells[row, 1].Value;
                        var i = worksheet.Cells[row, 2].Value;
                        var b = worksheet.Cells[row, 3].Value;
                        var p = worksheet.Cells[row, 4].Value;
                        var n = worksheet.Cells[row, 5].Value;
                        if (c == null || i == null || b == null)
                        {
                            continue;
                        }
                        var billCode = b.ToString();
                        if (billCode != null && !billCode.Contains("-PUA-") && !billCode.Contains("-SI-") && !billCode.Contains("-SO-"))
                        {
                            return new BaseResponseData<string>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"只能导入经销入库、经销调出、购货修订单"
                            };
                        }
                        if (worksheet.Cells[row, 5].Value != null)
                        {
                            if (billCode != null && !billCode.Contains("-SI-") && !billCode.Contains("-SO-"))
                            {
                                int number = 0;
                                bool isValid = int.TryParse(worksheet.Cells[row, 5].Value.ToString(), out number);
                                if (!isValid)
                                {
                                    return new BaseResponseData<string>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"经销入库、经销调出只能填入整数"
                                    };
                                }
                            }
                            if (billCode != null && !billCode.Contains("-PUA-"))
                            {
                                decimal number = 0;
                                bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out number);
                                if (!isValid)
                                {
                                    return new BaseResponseData<string>()
                                    {
                                        Code = CodeStatusEnum.Failed,
                                        Message = $"数量信息错误"
                                    };
                                }
                            }
                        }
                        excelData.Add(new InputBillBatchImportDto()
                        {
                            CompanyName = c.ToString(),
                            InvoiceNo = i.ToString(),
                            BillCode = b.ToString(),
                            ProductNo = p != null ? p.ToString() : string.Empty,
                            Number = n != null ? Convert.ToDecimal(n.ToString()) : null
                        });
                    }

                    if (excelData.Count != rowCount - 1)
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"Excel中存在单元格为空的数据，请检查"
                        };
                    }
                    // 检查是否有重复的数据
                    var duplicates = excelData.GroupBy(item => new { item.CompanyName, item.InvoiceNo, item.ProductNo, item.BillCode })
                                         .Where(group => group.Count() > 1)
                                         .Select(group => group.Key);

                    if (duplicates.Any())
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"Excel中存在相同公司、发票、单号以及货号的数据，请检查"
                        };
                    }

                    var duplicateByBillCodes = excelData.Where(x => !x.Number.HasValue && string.IsNullOrEmpty(x.ProductNo)).GroupBy(o => o.BillCode).Where(g => g.Count() > 1).ToList();

                    var cnt = excelData.Count(x => x.Number.HasValue && !string.IsNullOrEmpty(x.ProductNo));
                    if (cnt > 0 && cnt != excelData.Count())
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"请完整填写货号和入票数量或不填（不填默认导入剩余货号的入票数量）"
                        };
                    }

                    var codes = excelData.Select(p => p.BillCode).Distinct().ToList();
                    #endregion

                    #region 取值集合
                    //公司集合
                    var company = excelData.Select(p => p.CompanyName).Distinct().ToList();
                    //发票号集合
                    var invoiceNos = excelData.Select(p => p.InvoiceNo).Distinct().ToList();
                    //进项发票p.Status == 1 &&
                    var inputbills = await _queryInputBillPo.GetAllListAsync(p => company.Contains(p.CompanName) && invoiceNos.Contains(p.InvoiceNumber));
                    var inputbillIds = inputbills.Select(x => x.Id).ToList();
                    //进项发票详情
                    var inputbillSubmitDetails = await _db.InputBillSubmitDetails.Where(x => codes.Contains(x.StoreInItemCode)).AsNoTracking().ToListAsync();
                    var inputbillSubmitDetailIds = inputbillSubmitDetails.Select(x => x.Id).ToList();
                    //进项发票详情数量
                    var inputbillSubmitDetailQuantitys = await _db.InputBillSubmitDeatilQuantitys.Where(x => inputbillSubmitDetailIds.Contains(x.InputBillSubmitDetailId)).AsNoTracking().ToListAsync();
                    //分类型codes
                    var puaCodes = codes.Where(x => x.Contains("-PUA-")).ToList();
                    var siCodes = codes.Where(x => x.Contains("-SI-")).ToList();
                    var soCodes = codes.Where(x => x.Contains("-SO-")).ToList();

                    //待操作的集合
                    var addDetails = new List<InputBillSubmitDetail>();
                    var delDetailIds = new List<Guid>();
                    #endregion

                    #region 各类型单查询
                    //购货修订
                    var puaResultData = new List<PurchaseReviseForInputBill>();
                    if (codes.Any(p => p.Contains("-PUA-")))
                    {
                        var query = new PurchaseReviseForInputBillQueryDto()
                        {
                            pageIndex = 1,
                            pageSize = 100000,
                            codes = puaCodes
                        };
                        var pageResult = await _purchaseApiClient.GetPurchaseReviseForInputBills(query);
                        puaResultData = pageResult.List;
                    }

                    // 经销调出
                    var soResultData = new List<StoreOutDetailForFinanceOutput>();
                    if (codes.Any(p => p.Contains("-SO-")))
                    {
                        var soInput = new StoreInDetailQueryInput
                        {
                            page = 1,
                            limit = 100000,
                            storeInCodes = soCodes,
                            storeOutTypeList = new List<int> { 1, 6, 19 }
                        };
                        var resultList = await _inventoryApiClient.QueryStoreOutByCompany(soInput);
                        soResultData = resultList.list;
                    }

                    // 经销购货入库单
                    var siResultData = new List<InventoryStoreInOutput>();
                    if (codes.Any(p => p.Contains("-SI-")))
                    {
                        var puaInput = new StoreInDetailQueryInput
                        {
                            storeInCodes = siCodes
                        };
                        puaInput.page = 1;
                        puaInput.limit = 100000;
                        var resultList = await _inventoryApiClient.QueryStoreInByCompany(puaInput);
                        siResultData = resultList.list;
                    }
                    #endregion

                    #region excel遍历校验
                    // 记录单号货号已用数量
                    var verifyBox = new List<PollCheckByInvoiceDto>();
                    var errExcelData = new List<InputBillBatchImportDto>();
                    for (var row = 2; row <= rowCount; row++)
                    {
                        var companyName = worksheet.Cells[row, 1].Value.ToString();
                        var invoiceNo = worksheet.Cells[row, 2].Value.ToString();
                        var billCode = worksheet.Cells[row, 3].Value.ToString();
                        var productNo = worksheet.Cells[row, 4].Value != null ? worksheet.Cells[row, 4].Value.ToString() : string.Empty;
                        decimal? number = worksheet.Cells[row, 5].Value != null ? Convert.ToDecimal(worksheet.Cells[row, 5].Value) : null;
                        decimal? errNumber = worksheet.Cells[row, 5].Value != null ? Convert.ToDecimal(worksheet.Cells[row, 5].Value) : null;
                        // 存在业务单号重复的数据
                        if (duplicateByBillCodes.Any())
                        {
                            var isexist = duplicateByBillCodes.Where(x => x.Key == billCode).ToList();
                            if (isexist != null && isexist.Any())
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = errNumber,
                                    ErrMessage = "存在一个业务单号对应多个发票的信息，请核对单号或者填写到具体的货号和入票数"
                                });
                                continue;
                            }
                        }

                        //判断
                        if (string.IsNullOrEmpty(billCode))
                        {
                            errExcelData.Add(new InputBillBatchImportDto()
                            {
                                CompanyName = companyName,
                                InvoiceNo = invoiceNo,
                                BillCode = billCode,
                                ProductNo = productNo,
                                Number = errNumber,
                                ErrMessage = "业务单号为空"
                            });
                            continue;
                        }
                        else
                        {
                            // 查询单条进项发票
                            var single = inputbills.FirstOrDefault(x => x.InvoiceNumber == invoiceNo && x.CompanName == companyName);
                            if (single == null)
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = errNumber,
                                    ErrMessage = "进项票发票号或公司名错误"
                                });
                                continue;
                            }
                            if (single.Status == 2)
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = errNumber,
                                    ErrMessage = "已提交状态的发票，不允许导入"
                                });
                                continue;
                            }
                            if (single.Status == 9)
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = errNumber,
                                    ErrMessage = "已忽略状态的发票，不允许导入"
                                });
                                continue;
                            }
                            if (single.Status != 1)
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = errNumber,
                                    ErrMessage = "状态错误，临时草稿状态的发票方可导入"
                                });
                                continue;
                            }
                            if (billCode.Contains("-PUA-"))
                            {
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = puaResultData.Where(x => x.agentName == single.AgentName && x.companyName == companyName && x.purchaseOrderCode == billCode).ToList();
                                if (!string.IsNullOrEmpty(productNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.productNo == productNo).ToList();
                                }
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = "未找到导入数据，请检查"
                                    });
                                    continue;
                                }
                                else
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = string.Empty
                                    });
                                }
                                var queryDetailCodess = queryDetails.Select(x => x.purchaseOrderCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                var checkedArray = new List<PollCheckDto>();
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    var canQuantity = p.canInvoiceAmount;
                                    var currentQuantity = currentDetails.Where(x => x.StoreInItemCode == p.purchaseOrderCode && x.ProductNo == p.productNo).Sum(x => (x.Quantity * x.NoTaxAmount));
                                    var existsQuantity = inputbillSubmitDetails.Where(x => x.ProductNo == p.productNo && x.StoreInItemCode == p.purchaseOrderCode).Sum(x => (x.Quantity * x.NoTaxAmount));
                                    existsQuantity = Math.Abs(existsQuantity);
                                    // 辅助校验
                                    var assistantCheck = checkedArray.FirstOrDefault(x => x.ProductNo == p.productNo && x.BillCode == p.purchaseOrderCode);
                                    decimal finishNumber = 0;
                                    // 可入票数量 = 库存返回数量 - 除本进项票之外的锁定数量
                                    if (number.HasValue && Math.Abs(number.Value) <= canQuantity + currentQuantity - existsQuantity)
                                    {
                                        finishNumber = number.Value;
                                        // 第一个货号入票数足够，则不需要计算其它明细数量
                                        number = 0M;
                                    }
                                    else
                                    {
                                        if (number.HasValue)
                                        {
                                            canQuantity = number.Value < canQuantity + currentQuantity - existsQuantity ? number.Value : canQuantity;
                                            finishNumber = canQuantity + currentQuantity - existsQuantity;
                                            number -= canQuantity;
                                        }
                                        else
                                        {
                                            finishNumber = canQuantity + currentQuantity - existsQuantity;
                                        }
                                        if (assistantCheck == null)
                                        {
                                            decimal checkNumber = existsQuantity - currentQuantity > canQuantity ? canQuantity : existsQuantity - currentQuantity;
                                            checkedArray.Add(new PollCheckDto
                                            {
                                                ProductNo = p.productNo,
                                                BillCode = p.purchaseOrderCode,
                                                Number = checkNumber
                                            });
                                        }
                                    }
                                    if (assistantCheck != null)
                                    {
                                        finishNumber += assistantCheck.Number;
                                    }
                                    //if (finishNumber <= 0)
                                    //{
                                    //    continue;
                                    //}
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 4;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxAmount = p.reviseAmount;
                                    d.NoTaxCost = Math.Round(p.reviseAmount / (1 + p.taxRate / 100.00M), 4);
                                    d.Quantity = 1;
                                    d.ProductId = p.productId;
                                    d.ProductName = p.productName;
                                    d.ProductNameId = p.productNameId;
                                    d.ProductNo = p.productNo;
                                    d.ReceivedNumber = 1;
                                    d.StoreInDate = p.billDate.UtcDateTime;
                                    d.StoreInItemCode = p.purchaseOrderCode;
                                    d.TaxCost = p.reviseAmount;
                                    d.TaxRate = p.taxRate;
                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost); ;
                                    addDetails.Add(d);
                                }
                            }
                            else if (billCode.Contains("-SO-"))
                            {
                                if (number <= 0)
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = "入票数错误"
                                    });
                                    continue;
                                }
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = soResultData.Where(x => x.AgentName == single.AgentName && x.CompanyName == companyName && x.StoreOutCode == billCode).ToList();
                                if (!string.IsNullOrEmpty(productNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.ProductNo == productNo).ToList();
                                }
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = "未找到导入数据"
                                    });
                                    continue;
                                }
                                var queryDetailCodess = queryDetails.Select(x => x.StoreOutCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailCodess.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                // 已经校验过的货号，单号以及数量集合
                                var checkedArray = new List<PollCheckDto>();
                                var errDicValue = string.Empty;
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    var canQuantity = p.Quantity - p.InvoiceQuantity;
                                    // 获取在本次导入中已使用数
                                    var boxes = verifyBox.Where(x => x.ProductNo == p.ProductNo && x.BillCode == p.StoreOutCode).ToList();
                                    if (boxes != null && boxes.Any())
                                    {
                                        var otherBoxes = boxes.Where(x => x.InvoiceNo != invoiceNo && !x.IsClear).ToList();
                                        if (otherBoxes != null && otherBoxes.Any())
                                        {
                                            // 去掉其它发票占用的使用数
                                            canQuantity -= otherBoxes.Sum(x => x.Number);
                                            otherBoxes.ForEach(x =>
                                            {
                                                x.IsClear = true;
                                            });
                                        }
                                        var currentBoxes = boxes.Where(x => x.InvoiceNo == invoiceNo).ToList();
                                        if (currentBoxes != null && currentBoxes.Any() && number <= 0)
                                        {
                                            canQuantity -= currentBoxes.Sum(x => x.Number);
                                        }
                                    }
                                    // 计算剩余可入票的数量（去掉覆盖导入以及锁定的数量）
                                    var currentQuantity = currentDetails.Where(x => x.ProductNo == p.ProductNo && x.StoreInItemCode == p.StoreOutCode).Sum(x => x.Quantity);
                                    var existsQuantity = inputbillSubmitDetails.Where(x => x.ProductNo == p.ProductNo && x.StoreInItemCode == p.StoreOutCode).Sum(x => x.Quantity);
                                    // 出库使用绝对值查询已锁定数量
                                    existsQuantity = Math.Abs(existsQuantity);
                                    // 辅助校验
                                    var assistantCheck = checkedArray.FirstOrDefault(x => x.ProductNo == p.ProductNo && x.BillCode == p.StoreOutCode);
                                    decimal finishNumber = 0;
                                    // 可入票数量 = 库存返回数量 - 除本进项票之外的锁定数量
                                    if (number.HasValue && number <= canQuantity + currentQuantity - existsQuantity)
                                    {
                                        finishNumber = number.Value;
                                        // 第一个货号入票数足够，则不需要计算其它明细数量
                                        number = 0M;
                                    }
                                    else
                                    {
                                        if (number.HasValue)
                                        {
                                            canQuantity = number < canQuantity + currentQuantity - existsQuantity ? number : canQuantity;
                                            finishNumber = canQuantity.HasValue ? canQuantity.Value + currentQuantity - existsQuantity : 0;
                                            number -= canQuantity;
                                        }
                                        else
                                        {
                                            finishNumber = canQuantity.HasValue ? canQuantity.Value + currentQuantity - existsQuantity : 0;
                                        }
                                        if (assistantCheck == null)
                                        {
                                            decimal checkNumber = existsQuantity - currentQuantity > canQuantity ? (canQuantity.HasValue ? canQuantity.Value : 0) : existsQuantity - currentQuantity;
                                            checkedArray.Add(new PollCheckDto
                                            {
                                                ProductNo = p.ProductNo,
                                                BillCode = p.StoreOutCode,
                                                Number = checkNumber
                                            });
                                        }
                                    }
                                    if (assistantCheck != null)
                                    {
                                        finishNumber += assistantCheck.Number;
                                    }
                                    if (finishNumber <= 0)
                                    {
                                        if (number != 0)
                                        {
                                            errDicValue += $"分配数量不足!";
                                        }
                                        continue;
                                    }
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 2;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxCost = p.SettlementCost / (1 + p.TaxRate / 100);
                                    d.Quantity = finishNumber > 0 ? 0 - finishNumber : finishNumber;
                                    d.NoTaxAmount = d.Quantity * p.SettlementCost;
                                    d.ProductId = !string.IsNullOrEmpty(p.ProductId) ? Guid.Parse(p.ProductId) : null;
                                    d.ProductName = p.ProductName;
                                    d.ProductNameId = !string.IsNullOrEmpty(p.ProductNameId) ? Guid.Parse(p.ProductNameId) : null;
                                    d.ProductNo = p.ProductNo;
                                    d.ReceivedNumber = finishNumber > 0 ? 0 - finishNumber : finishNumber; //经销调出使用负数
                                    d.StoreInDate = DateTimeOffset.FromUnixTimeMilliseconds(p.BillDate).UtcDateTime;
                                    d.StoreInItemCode = p.StoreOutCode;
                                    d.TaxCost = p.SettlementCost;
                                    d.TaxRate = p.TaxRate;

                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                                    if (p.LotInfo != null)
                                    {
                                        var useQuantity = 0m;
                                        var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
                                        foreach (var t in p.LotInfo)
                                        {
                                            d.ProducerOrderNo = t.producerOrderNo;
                                            canQuantity = t.quantity - t.invoiceQuantity;
                                            if (canQuantity == 0)
                                            {
                                                errDicValue += "当前可入发票数0！";
                                                continue;
                                            }
                                            var quantity = canQuantity < finishNumber ? canQuantity : finishNumber - useQuantity;
                                            if (quantity <= 0)
                                            {
                                                continue;
                                            }
                                            useQuantity += quantity.HasValue ? quantity.Value : 0;
                                            //检查入票数
                                            //if (number > canQuantity)
                                            //{
                                            //    errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}";
                                            //    continue;
                                            //}
                                            var ibsdq = new InputBillSubmitDetailQuantity();
                                            ibsdq.Id = Guid.NewGuid();
                                            ibsdq.InputBillSubmitDetailId = d.Id;
                                            ibsdq.CreatedBy = "none";
                                            ibsdq.CreatedTime = DateTime.Now;
                                            ibsdq.Quantity = quantity;
                                            ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.id) ? Guid.Parse(t.id) : Guid.Empty;
                                            inputBillSubmitDetailQuantities.Add(ibsdq);
                                        }
                                        d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
                                    }
                                    verifyBox.Add(new PollCheckByInvoiceDto
                                    {
                                        InvoiceNo = invoiceNo,
                                        ProductNo = p.ProductNo,
                                        BillCode = p.StoreOutCode,
                                        Number = finishNumber,
                                        IsClear = false
                                    });
                                    addDetails.Add(d);
                                }
                                if (!string.IsNullOrEmpty(errDicValue))
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = errDicValue
                                    });
                                    continue;
                                }
                                else
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = string.Empty
                                    });
                                }
                            }
                            else if (billCode.Contains("-SI-"))
                            {
                                if (number <= 0)
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = "入票数错误"
                                    });
                                    continue;
                                }
                                // 当前详情数据
                                var currentDetails = inputbillSubmitDetails.Where(x => x.InputBillId == single.Id).ToList();
                                // 查询导入的明细数据
                                var queryDetails = siResultData.Where(x => x.agentId == single.AgentId && x.companyId == single.CompanyId && x.storeInCode == billCode).ToList();
                                if (!string.IsNullOrEmpty(productNo))
                                {
                                    queryDetails = queryDetails.Where(x => x.productNo == productNo).ToList();
                                }
                                if (queryDetails == null || !queryDetails.Any())
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = "未找到导入数据"
                                    });
                                    continue;
                                }
                                var queryDetailProductNos = queryDetails.Select(x => x.storeInCode).ToList();
                                // 导入中已存在的详情数据 -> 清除
                                var existsDetails = currentDetails.Where(x => queryDetailProductNos.Contains(x.StoreInItemCode)).ToList();
                                // 导入中已存在的详情数量数据 -> 清除
                                var existsDetailIds = existsDetails.Select(x => x.Id).ToList();
                                delDetailIds.AddRange(existsDetailIds);
                                var errDicValue = string.Empty;
                                // 已经校验过的货号，单号以及数量集合
                                var checkedArray = new List<PollCheckDto>();
                                // 整理导入明细
                                foreach (var p in queryDetails)
                                {
                                    var canQuantity = p.quantity - p.invoiceQuantity;
                                    // 获取在本次导入中已使用数
                                    var boxes = verifyBox.Where(x => x.ProductNo == p.productNo && x.BillCode == p.storeInCode).ToList();
                                    if (boxes != null && boxes.Any())
                                    {
                                        var otherBoxes = boxes.Where(x => x.InvoiceNo != invoiceNo && !x.IsClear).ToList();
                                        if (otherBoxes != null && otherBoxes.Any())
                                        {
                                            // 去掉其它发票占用的使用数
                                            canQuantity -= otherBoxes.Sum(x => x.Number);
                                            otherBoxes.ForEach(x =>
                                            {
                                                x.IsClear = true;
                                            });
                                        }
                                        var currentBoxes = boxes.Where(x => x.InvoiceNo == invoiceNo).ToList();
                                        if (currentBoxes != null && currentBoxes.Any() && number <= 0)
                                        {
                                            canQuantity -= currentBoxes.Sum(x => x.Number);
                                        }
                                    }
                                    // 计算剩余可入票的数量（去掉覆盖导入以及锁定的数量）
                                    var currentQuantity = currentDetails.Where(x => x.ProductNo == p.productNo && x.StoreInItemCode == p.storeInCode).Sum(x => x.Quantity);
                                    var existsQuantity = inputbillSubmitDetails.Where(x => x.ProductNo == p.productNo && x.StoreInItemCode == p.storeInCode).Sum(x => x.Quantity);
                                    // 辅助校验
                                    var assistantCheck = checkedArray.FirstOrDefault(x => x.ProductNo == p.productNo && x.BillCode == p.storeInCode);
                                    decimal finishNumber = 0;
                                    // 可入票数量 = 库存返回数量 - 除本进项票之外的锁定数量
                                    if (number.HasValue && number <= canQuantity + currentQuantity - existsQuantity)
                                    {
                                        finishNumber = number.Value;
                                        // 第一个货号入票数足够，则不需要计算其它明细数量
                                        number = 0M;
                                    }
                                    else
                                    {
                                        if (number.HasValue)
                                        {
                                            canQuantity = number < canQuantity + currentQuantity - existsQuantity ? number : canQuantity;
                                            finishNumber = canQuantity.HasValue ? canQuantity.Value + currentQuantity - existsQuantity : 0;
                                            number -= canQuantity;
                                        }
                                        else
                                        {
                                            finishNumber = canQuantity.HasValue ? canQuantity.Value + currentQuantity - existsQuantity : 0;
                                        }
                                        if (assistantCheck == null)
                                        {
                                            decimal checkNumber = existsQuantity - currentQuantity > canQuantity ? (canQuantity.HasValue ? canQuantity.Value : 0) : existsQuantity - currentQuantity;
                                            checkedArray.Add(new PollCheckDto
                                            {
                                                ProductNo = p.productNo,
                                                BillCode = p.storeInCode,
                                                Number = checkNumber
                                            });
                                        }
                                    }
                                    if (assistantCheck != null)
                                    {
                                        finishNumber += assistantCheck.Number;
                                    }
                                    if (finishNumber <= 0)
                                    {
                                        if (number != 0)
                                        {
                                            errDicValue += $"分配数量不足!";
                                        }
                                        continue;
                                    }
                                    var d = new InputBillSubmitDetail();
                                    d.Id = Guid.NewGuid();
                                    d.BusinessType = 1;
                                    d.CreatedBy = "none";
                                    d.CreatedTime = DateTime.Now;
                                    d.InputBillId = single.Id;
                                    d.NoTaxAmount = finishNumber * p.unitCost;
                                    d.NoTaxCost = p.unitCost / (1 + p.taxRate / 100);
                                    d.Quantity = finishNumber;
                                    d.ProductId = p.productId;
                                    d.ProductName = p.productName;
                                    d.ProductNameId = p.productNameId;
                                    d.ProductNo = p.productNo;
                                    d.ReceivedNumber = finishNumber;
                                    d.StoreInDate = DateTimeHelper.LongToDateTime(p.storeInDate);
                                    d.StoreInItemCode = p.storeInCode;
                                    d.TaxCost = p.unitCost;
                                    d.TaxRate = p.taxRate;

                                    d.TaxAmount = d.Quantity * (d.TaxCost - d.NoTaxCost);
                                    if (p.LotInfo != null)
                                    {
                                        var inputBillSubmitDetailQuantities = new List<InputBillSubmitDetailQuantity>();
                                        var useQuantity = 0m;
                                        foreach (var t in p.LotInfo)
                                        {
                                            d.ProducerOrderNo = t.producerOrderNo;
                                            canQuantity = t.quantity - t.invoiceQuantity;
                                            if (canQuantity == 0)
                                            {
                                                errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}！！";
                                                continue;
                                            }
                                            var quantity = canQuantity < finishNumber ? canQuantity : finishNumber - useQuantity;
                                            if (quantity <= 0)
                                            {
                                                continue;
                                            }
                                            useQuantity += quantity.HasValue ? quantity.Value : 0;
                                            //if (canQuantity < number)
                                            //{
                                            //    errDicValue += $"可入发票数不足，当前可入发票数{canQuantity}！！";
                                            //    continue;
                                            //}
                                            var ibsdq = new InputBillSubmitDetailQuantity();
                                            ibsdq.Id = Guid.NewGuid();
                                            ibsdq.InputBillSubmitDetailId = d.Id;
                                            ibsdq.CreatedBy = "none";
                                            ibsdq.CreatedTime = DateTime.Now;
                                            ibsdq.Quantity = quantity;
                                            ibsdq.StoreInDetailId = !string.IsNullOrEmpty(t.storeInDetailId) ? Guid.Parse(t.storeInDetailId) : Guid.Empty;
                                            inputBillSubmitDetailQuantities.Add(ibsdq);
                                        }
                                        d.InputBillSubmitDetailQuantity = inputBillSubmitDetailQuantities.Count > 0 ? inputBillSubmitDetailQuantities : null;
                                    }
                                    verifyBox.Add(new PollCheckByInvoiceDto
                                    {
                                        InvoiceNo = invoiceNo,
                                        ProductNo = p.productNo,
                                        BillCode = p.storeInCode,
                                        Number = finishNumber,
                                        IsClear = false
                                    });
                                    addDetails.Add(d);
                                }
                                if (!string.IsNullOrEmpty(errDicValue))
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = errDicValue
                                    });
                                    continue;
                                }
                                else
                                {
                                    errExcelData.Add(new InputBillBatchImportDto()
                                    {
                                        CompanyName = companyName,
                                        InvoiceNo = invoiceNo,
                                        BillCode = billCode,
                                        ProductNo = productNo,
                                        Number = errNumber,
                                        ErrMessage = string.Empty
                                    });
                                }
                            }
                            else
                            {
                                errExcelData.Add(new InputBillBatchImportDto()
                                {
                                    CompanyName = companyName,
                                    InvoiceNo = invoiceNo,
                                    BillCode = billCode,
                                    ProductNo = productNo,
                                    Number = number,
                                    ErrMessage = "业务单号错误，只能导入经销入库、经销调出、购货修订单"
                                });
                                continue;
                            }
                        }
                    }
                    #endregion

                    #region 错误消息
                    var fileIdout = Guid.Empty;
                    var err = errExcelData.Where(x => !string.IsNullOrEmpty(x.ErrMessage)).ToList();
                    if (errExcelData.Any() && err.Count > 0)
                    {
                        // 写入Excel上传
                        var errStream = new MemoryStream();
                        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                        using (var errPackage = new ExcelPackage(errStream))
                        {
                            var columnIndex = 1;
                            var errWorksheet = errPackage.Workbook.Worksheets.Add("进项发票批量导入明细错误信息");
                            errWorksheet.SetValue(1, columnIndex++, "公司名称");
                            errWorksheet.SetValue(1, columnIndex++, "发票号");
                            errWorksheet.SetValue(1, columnIndex++, "业务单号");
                            errWorksheet.SetValue(1, columnIndex++, "货号");
                            errWorksheet.SetValue(1, columnIndex++, "数量");
                            errWorksheet.SetValue(1, columnIndex++, "错误信息");
                            var index = 2;
                            errExcelData.ForEach(p =>
                            {
                                columnIndex = 1;
                                errWorksheet.SetValue(index, columnIndex++, p.CompanyName);
                                errWorksheet.SetValue(index, columnIndex++, p.InvoiceNo);
                                errWorksheet.SetValue(index, columnIndex++, p.BillCode);
                                errWorksheet.SetValue(index, columnIndex++, p.ProductNo);
                                errWorksheet.SetValue(index, columnIndex++, p.Number);
                                errWorksheet.SetValue(index, columnIndex++, p.ErrMessage);
                                index++;
                            });

                            errPackage.SaveAs(errStream);
                            MemoryStream msFailReport = new MemoryStream(errPackage.GetAsByteArray());
                            //var content = new BinaryData(errStream.GetBuffer());
                            fileIdout = await _fileGatewayClient.UploadTempFileContentAsync(msFailReport, "进项发票批量导入明细错误信息.xlsx");
                        }
                    }
                    #endregion

                    #region 执行存储
                    //if (err.Count == 0)
                    //{

                    //}
                    if (delDetailIds.Count > 0)
                    {
                        // 删除
                        await _inputBillSubmitDetailRepository.DeteteManyAsync(delDetailIds);
                    }
                    if (addDetails.Count > 0)
                    {
                        // 添加
                        await _inputBillSubmitDetailRepository.AddManyAsync(addDetails);
                        var excecount = await _unitOfWork.CommitAsync();
                    }
                    #endregion

                    if (errExcelData.Any() && err.Count > 0)
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Success,
                            Data = fileIdout.ToString(),
                            Message = $"导入部分成功，请下载错误信息文件查看详细"
                        };
                    }
                    else
                    {
                        return new BaseResponseData<string>()
                        {
                            Code = CodeStatusEnum.Success,
                            Message = $"导入成功"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<string>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"上传失败，错误信息：模板错误或导入数据未找到，请检查Excel中的内容"
                };
            }
        }
        #endregion

        /// <summary>
        /// 取消勾稽进项票
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelBill(Guid id, string userName)
        {
            var inputBill = await _db.InputBills.FirstOrDefaultAsync(x => x.Id == id);
            if (inputBill == null)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"请选择要取消勾稽的进项票"
                };
            }
            if (inputBill.Status != 2)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"已提交状态的进项票才能取消勾稽"
                };
            }

            // 检查进项票是否在多对多勾稽中
            var mergeInputBillRelation = await _db.MergeInputBillRelations
                .Include(x => x.MergeInputBill)
                .FirstOrDefaultAsync(x => x.InputBillId == id);

            if (mergeInputBillRelation != null)
            {
                var mergeInputBill = mergeInputBillRelation.MergeInputBill;
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"该进项票已在多对多勾稽中（合并单号：{mergeInputBill.MergeInvoiceNumber}），请先在多对多勾稽页面取消勾稽后再操作"
                };
            }

            // 检查进项票是否有提交明细数据
            var hasInputBillSubmitDetails = await _db.InputBillSubmitDetails
                .AnyAsync(x => x.InputBillId == id);

            if (!hasInputBillSubmitDetails)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"该进项票没有勾稽明细，无需取消勾稽"
                };
            }
            // 判断当月进项票才能撤回
            //if (inputBill.BillTime.Month != DateTime.Now.Month)
            //{
            //    return new BaseResponseData<int>()
            //    {
            //        Code = CodeStatusEnum.Failed,
            //        Message = $"当月进项票才能撤回"
            //    };
            //}
            // 进项票金蝶明细
            var inputBillSumbitDetails = await _db.InputBillSubmitDetails.Include(x => x.InputBillSubmitDetailQuantity).Where(x => x.InputBillId == id).AsNoTracking().ToListAsync();
            // 采购接口入参 pua
            var updateQuantiyPuaInput = new List<UpdateInvoiceQuantityInput>();
            // 库存接口入参
            var isPushKc = false;
            var invoiceNumbers = new List<string> { inputBill.InvoiceNumber };
            // 修改入票金额接口入参
            var updatePurchaseReviseInviceAmountInput = new List<UpdatePurchaseReviseInvoiceAmountInputDto>();
            // 更新库存出库接口入参
            var inventoryStoreOutUpdateDetails = new List<InventoryStoreOutUpdateDetail>();  //出库集合
            // 金蝶接口入参
            var inputBillUnassignInput = new InputBillUnassignInput();
            // 组装参数
            foreach (var detail in inputBillSumbitDetails)
            {
                // 是否推送库存入库
                if (detail.BusinessType == 1)
                {
                    isPushKc = true;
                }
                // 更新库存出库
                if (detail.BusinessType == 2)
                {
                    var currentIbsdq = detail.InputBillSubmitDetailQuantity;
                    if (currentIbsdq != null && currentIbsdq.Any())
                    {
                        foreach (var subitem in currentIbsdq)
                        {
                            inventoryStoreOutUpdateDetails.Add(new InventoryStoreOutUpdateDetail() { invoiceQuantity = Math.Abs(subitem.Quantity), id = subitem.StoreInDetailId.ToString() });
                        }
                    }
                }
                // 寄售转购货
                if (detail.BusinessType == 3)
                {
                    var currentIbsdq = detail.InputBillSubmitDetailQuantity;
                    if (currentIbsdq != null && currentIbsdq.Any())
                    {
                        foreach (var subitem in currentIbsdq)
                        {
                            updateQuantiyPuaInput.Add(new UpdateInvoiceQuantityInput
                            {
                                InvoiceNumber = 0 - subitem.Quantity,  //负数
                                PurchaseDetailId = subitem.StoreInDetailId
                            });
                        }
                    }
                }
                // 购货修订
                if (detail.BusinessType == 4)
                {
                    var invoiceAmount = detail.NoTaxAmount;
                    if (detail.NoTaxAmount > 0)
                    {
                        invoiceAmount = 0 - detail.NoTaxAmount;
                    }
                    var ghxd = new UpdatePurchaseReviseInvoiceAmountInputDto
                    {
                        purchaseOrderCode = detail.StoreInItemCode,
                        invoiceAmount = invoiceAmount, //取消勾稽应取负数
                        productId = detail.ProductId,
                        taxRate = detail.TaxRate
                    };
                    updatePurchaseReviseInviceAmountInput.Add(ghxd);
                }
                // other continue
            }
            // 调用接口
            // 获取系统月度
            var conpanyId = inputBill.CompanyId.ToString();
            var sysMonth = await _bDSApiClient.GetSystemMonth(conpanyId);
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM-dd");
            // 金蝶
            inputBillUnassignInput.invoiceno = inputBill.InvoiceNumber;
            inputBillUnassignInput.user = userName;
            inputBillUnassignInput.associatedDate = sysMonth;
            var kingRet = await _kingdeeApiClient.InputBillUnassign(inputBillUnassignInput);
            if (kingRet.Code != CodeStatusEnum.Success)
            {
                return kingRet;
            }
            // 库存 入库
            if (isPushKc)
            {
                try
                {
                    var kcRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(invoiceNumbers);
                    if (kcRet == null || kcRet.code != 200)
                    {
                        throw new Exception("库存入库接口异常：" + kcRet != null ? kcRet.message : string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    string errMsg = string.Concat("更新入库票数失败", ex.Message);
                    await RollBackCancelInputBill(1, errMsg, inputBillSumbitDetails, inputBill.InvoiceNumber, userName, sysMonth);
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = errMsg
                    };
                }
            }
            // 库存 出库（经销调出）
            if (inventoryStoreOutUpdateDetails.Any())
            {
                try
                {
                    var soRet = await _inventoryExcuteApiClient.UpdateInvoiceInfoRevoke(inventoryStoreOutUpdateDetails);
                    if (soRet == null || soRet.code != 200)
                    {
                        throw new Exception("库存调出接口异常：" + soRet != null ? soRet.message : string.Empty);
                    }
                }
                catch (Exception ex)
                {
                    //回滚
                    string errMsg = string.Concat("更新出库失败", ex.Message);
                    await RollBackCancelInputBill(2, errMsg, inputBillSumbitDetails, inputBill.InvoiceNumber, userName, sysMonth);
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = errMsg
                    };
                }
            }
            // 采购
            if (updateQuantiyPuaInput.Any())
            {
                try
                {
                    var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                    if (purRet.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception("采购寄售转购货接口异常：" + purRet.Message);
                    }
                }
                catch (Exception ex)
                {
                    //回滚
                    string errMsg = string.Concat("更新采购票数失败，原因：【采购】", ex.Message);
                    await RollBackCancelInputBill(3, errMsg, inputBillSumbitDetails, inputBill.InvoiceNumber, userName, sysMonth);
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = errMsg
                    };
                }
            }
            //更新购货修订入票信息
            if (updatePurchaseReviseInviceAmountInput != null && updatePurchaseReviseInviceAmountInput.Any())
            {
                try
                {
                    var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(updatePurchaseReviseInviceAmountInput);
                    if (retpuchase.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception("采购购货修订接口异常：" + retpuchase.Message);
                    }
                }
                catch (Exception ex)
                {
                    //回滚
                    string errMsg = string.Concat("更新购货修订入票金额失败，原因：【采购】", ex.Message);
                    await RollBackCancelInputBill(4, errMsg, inputBillSumbitDetails, inputBill.InvoiceNumber, userName, sysMonth);
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = errMsg
                    };
                }
            }
            // 更改状态并记录取消勾稽信息
            inputBill.Status = 1;
            inputBill.CancelReconciliationTime = DateTimeHelper.GetCurrentDate();
            inputBill.IsCancelledReconciliation = true;
            _db.InputBills.Update(inputBill);
            var submitDetails_Debt = inputBillSumbitDetails.Where(x => x.BusinessType == 5).ToList();
            // 更新Debt入票数
            if (submitDetails_Debt.Any())
            {
                var debtCode = submitDetails_Debt.Select(p => p.StoreInItemCode).ToList();
                //更新Debt入票数
                var debts = await _db.Debts.Where(p => debtCode.Contains(p.BillCode)).ToListAsync();
                foreach (var debt in debts)
                {
                    var noTaxAmount = submitDetails_Debt.Where(p => p.StoreInItemCode == debt.BillCode).Sum(p => p.NoTaxAmount);
                    if (debt.InvoiceAmount.HasValue && noTaxAmount > debt.InvoiceAmount)
                    {
                        await RollBackCancelInputBill(5, string.Format("【核心】服务费校验失败"), inputBillSumbitDetails, inputBill.InvoiceNumber, userName, sysMonth);
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"服务费{submitDetails_Debt.FirstOrDefault().StoreInItemCode}的入票金额{noTaxAmount}大于占用金额{debt.InvoiceAmount}"
                        };
                    }
                    debt.InvoiceAmount = debt.InvoiceAmount.HasValue ? debt.InvoiceAmount.Value - noTaxAmount : 0;
                    if (debt.InvoiceAmount < 0)
                    {
                        debt.InvoiceAmount = 0;
                    }
                }
            }
            var inputBillDebts = await _db.InputBillDebt.Where(p => p.InputBillId == inputBill.Id).ToListAsync();
            if (inputBillDebts.Any())
            {
                _db.InputBillDebt.RemoveRange(inputBillDebts);
            }
            await _unitOfWork.CommitAsync();

            return new BaseResponseData<int>()
            {
                Code = CodeStatusEnum.Success,
                Message = $"取消勾稽成功"
            };
        }

        /// <summary>
        /// 回滚广播事件调用
        /// </summary>
        /// <param name="step">当前运行中步骤</param>
        /// <param name="errMsg">错误信息</param>
        /// <param name="details">明细</param>
        /// <param name="invoiceNumber">进项票号</param>
        private async Task RollBackCancelInputBill(int step, string errMsg, List<InputBillSubmitDetailPo> details, string invoiceNumber, string user, string sysMonth)
        {
            var inputBill = await _db.InputBills.FirstOrDefaultAsync(x => x.InvoiceNumber == invoiceNumber);
            if (inputBill == null)
            {
                return;
            }
            var inputBillSumbitDetailQuantities = new List<InputBillSubmitDetailQuantityPo>();
            foreach (var detail in details)
            {
                if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any())
                {
                    inputBillSumbitDetailQuantities.AddRange(detail.InputBillSubmitDetailQuantity);
                }
            }
            // 金蝶接口入参
            var kingdeeData = new KingdeeInputBillSubmitDto();
            kingdeeData.invoiceno = invoiceNumber;
            var kingdeeDetails = new List<KingdeeInputBillSubmitDetailDto>();
            // 入库接口入参
            var inventoryStoreInUpdateDetails = new List<InventoryStoreInUpdateDetail>();
            // 获取当前发票时间
            DateTime localDateTime = inputBill.BillTime;
            DateTime utcDateTime = localDateTime.ToUniversalTime();
            long invoiceDate = (long)(utcDateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
            // 经销调出接口入参
            var inventoryStoreOutUpdateDetails = new List<InventoryStoreOutUpdateDetail>();
            // 寄售转购货接口入参 pua
            var updateQuantiyPuaInput = new List<UpdateInvoiceQuantityInput>();
            // 购货修订接口入参
            var updatePurchaseReviseInviceAmountInput = new List<UpdatePurchaseReviseInvoiceAmountInputDto>();
            // 组装参数
            foreach (var model in inputBillSumbitDetailQuantities)
            {
                var single = details.FirstOrDefault(x => x.Id == model.InputBillSubmitDetailId);
                if (single == null)
                {
                    continue;
                }
                decimal quantity = single != null ? single.Quantity : 0;
                decimal invoiceAmt = single != null ? single.NoTaxAmount : 0;
                string invoiceType = inputBill.Type == 1 ? "普票" : "专票";
                if (single.BusinessType == 1)
                {
                    // 入库
                    inventoryStoreInUpdateDetails.Add(new InventoryStoreInUpdateDetail() { invoiceQuantity = model.Quantity, storeInDetailId = model.StoreInDetailId.ToString(), invoiceNumber = invoiceNumber, currentInvoiceQuantity = model.Quantity, invoiceAmount = invoiceAmt, InvoiceTypeStr = invoiceType, InvoiceDate = invoiceDate });
                }
                else
                {
                    // 经销调出
                    inventoryStoreOutUpdateDetails.Add(new InventoryStoreOutUpdateDetail() { invoiceQuantity = model.Quantity, id = model.StoreInDetailId.ToString(), invoiceNumber = invoiceNumber, currentInvoiceQuantity = quantity, invoiceAmount = invoiceAmt, InvoiceTypeStr = invoiceType, InvoiceDate = invoiceDate });
                }
            }
            // 寄售转购货
            var submitDetails_PUA = details.Where(p => p.BusinessType == 3).ToList();
            foreach (var item in submitDetails_PUA)
            {
                if (item.InputBillSubmitDetailQuantity != null && item.InputBillSubmitDetailQuantity.Any())
                {
                    foreach (var subitem in item.InputBillSubmitDetailQuantity)
                    {
                        updateQuantiyPuaInput.Add(new DTOs.Purchase.UpdateInvoiceQuantityInput
                        {
                            InvoiceNumber = subitem.Quantity,
                            PurchaseDetailId = subitem.StoreInDetailId
                        });
                    }
                }
            }
            // 购货修订
            var submitDetailsRevise = details.Where(p => p.BusinessType == 4).ToList();
            if (submitDetailsRevise.Any())
            {
                //更新购货修订入票信息
                updatePurchaseReviseInviceAmountInput = submitDetailsRevise.Select(p => new UpdatePurchaseReviseInvoiceAmountInputDto
                {
                    purchaseOrderCode = p.StoreInItemCode,
                    invoiceAmount = Math.Abs(p.NoTaxAmount),  //提交传入正数
                    productId = p.ProductId,
                    taxRate = p.TaxRate
                }).ToList();
            }
            if (step > 0)
            {
                //回滚金蝶事件
                var newInputBillDebts = new List<InputBillDebt>();
                var incodes = details.Where(p => p.BusinessType == 1).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到入库单号
                if (incodes.Any())
                {
                    var inputBillDebts = await _inputBillDebtQuery.GetIQueryable(p => p.InputBillId == inputBill.Id).AsNoTracking().ToListAsync();
                    if (inputBillDebts != null && inputBillDebts.Any())
                    {
                        _db.InputBillDebt.RemoveRange(inputBillDebts);
                    }
                    var debts = await _debtQueryService.GetIQueryable(p => incodes.Contains(p.RelateCode) && p.Value != 0).OrderBy(p => p.RelateCode).ThenByDescending(p => Math.Abs(p.Value)).ToListAsync();//找到对应的应付
                    var debtCodes = debts.Select(p => p.BillCode).Distinct().ToList();//找到应付单集合
                    var totalAmount = details.Where(p => p.BusinessType == 1).Sum(q => q.NoTaxAmount);
                    var usedAmount = 0.00m;
                    var index = 0;
                    foreach (var debt in debts)
                    {
                        var isStoreIn = debt.BillCode.Contains("SI");
                        usedAmount = kingdeeDetails.Where(p => p.relateCode == debt.RelateCode).Sum(p => p.f_usedamt); //重置为0  从金蝶集合中取

                        if (totalAmount <= 0)
                        {
                            break;
                        }
                        var thisExistAmount = 0;
                        var thisAmount = details.Where(t => t.StoreInItemCode == debt.RelateCode).Sum(q => q.NoTaxAmount);//找到明细中入库相同入库单号的总额
                        thisAmount = decimal.Parse(thisAmount.ToString("F2")); //保留2问小数
                        thisAmount -= Math.Abs(usedAmount);
                        if (thisAmount <= 0)
                        {
                            continue;
                        }
                        var avilableAmount = Math.Abs(thisAmount) > Math.Abs(debt.Value) - thisExistAmount ? Math.Abs(debt.Value) - thisExistAmount : Math.Abs(thisAmount);
                        if (Math.Abs(thisAmount) > avilableAmount && avilableAmount > 0)
                        {
                            kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                            {
                                finNum = debt.BillCode,
                                f_usedamt = debt.Value > 0 ? avilableAmount : -avilableAmount,
                                relateCode = debt.RelateCode,
                                user = user,
                                associatedDate = sysMonth
                            });
                            newInputBillDebts.Add(new InputBillDebt()
                            {
                                Id = Guid.NewGuid(),
                                DebtAmount = avilableAmount,
                                DebtCode = debt.BillCode,
                                InputBillId = inputBill.Id,
                            });
                            totalAmount -= avilableAmount;
                            usedAmount += avilableAmount;
                        }
                        else
                        {
                            kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                            {
                                finNum = debt.BillCode,
                                f_usedamt = isStoreIn ? thisAmount : -thisAmount,
                                relateCode = debt.RelateCode,
                                user = user,
                                associatedDate = sysMonth
                            });
                            newInputBillDebts.Add(new InputBillDebt()
                            {
                                Id = Guid.NewGuid(),
                                DebtAmount = Math.Abs(thisAmount),
                                DebtCode = debt.BillCode,
                                InputBillId = inputBill.Id,
                            });
                            totalAmount -= Math.Abs(thisAmount);
                            usedAmount += Math.Abs(thisAmount);
                        }
                        index++;
                    }
                }
                var outcodes = details.Where(p => p.BusinessType == 2).Select(p => p.StoreInItemCode).Distinct().ToList(); //找到入库单号
                if (outcodes.Any())
                {
                    var inputBillDebts = await _inputBillDebtQuery.GetIQueryable(p => p.InputBillId == inputBill.Id).AsNoTracking().ToListAsync();
                    if (inputBillDebts != null && inputBillDebts.Any())
                    {
                        _db.InputBillDebt.RemoveRange(inputBillDebts);
                    }
                    var debts = await _debtQueryService.GetIQueryable(p => outcodes.Contains(p.RelateCode) && p.Value != 0).OrderBy(p => p.RelateCode).ThenByDescending(p => Math.Abs(p.Value)).ToListAsync();//找到对应的应付
                    var debtCodes = debts.Select(p => p.BillCode).Distinct().ToList();//找到应付单集合
                    var debtRelateCodes = debts.Select(p => p.RelateCode).Distinct().ToList();
                    var debtRelates = await _db.Debts.Where(x => debtRelateCodes.Contains(x.RelateCode) && x.Value != 0).ToListAsync();

                    var totalAmount = details.Where(p => p.BusinessType == 2).Sum(q => Math.Abs(q.NoTaxAmount));
                    var usedAmount = 0.00m;
                    var index = 0;
                    foreach (var debt in debts)
                    {
                        var isStoreIn = debt.BillCode.Contains("SI");
                        usedAmount = kingdeeDetails.Where(p => p.relateCode == debt.RelateCode).Sum(p => p.f_usedamt); //重置为0  从金蝶集合中取

                        if (totalAmount <= 0)
                        {
                            break;
                        }
                        var thisExistAmount = 0;
                        if (thisExistAmount >= Math.Abs(debt.Value))//当前应付的金额已经勾完了
                        {
                            continue;
                        }
                        var thisAmount = details.Where(t => t.StoreInItemCode == debt.RelateCode).Sum(q => Math.Abs(q.NoTaxAmount));//找到明细中入库相同入库单号的总额
                                                                                                                                    //thisAmount = thisAmount > totalAmount ? totalAmount : thisAmount;
                        thisAmount = decimal.Parse(thisAmount.ToString("F2")); //保留2问小数
                        thisAmount -= Math.Abs(usedAmount);
                        if (thisAmount <= 0)
                        {
                            continue;
                        }
                        // 当前关联应付
                        var currentRelateDebts = debtRelates.Where(x => x.RelateCode == debt.RelateCode).ToList();
                        var total = thisAmount;
                        var lastTotalAvilableAmount = 0M;
                        foreach (var relateDebt in currentRelateDebts)
                        {
                            thisAmount = total - lastTotalAvilableAmount;
                            if (total - lastTotalAvilableAmount == 0)
                            {
                                // 金额已足够
                                break;
                            }
                            var avilableAmount = Math.Abs(thisAmount) > Math.Abs(relateDebt.Value) - thisExistAmount ? Math.Abs(relateDebt.Value) - thisExistAmount : Math.Abs(thisAmount);
                            if (Math.Abs(thisAmount) > avilableAmount && avilableAmount > 0)
                            {
                                kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                                {
                                    finNum = relateDebt.BillCode,
                                    f_usedamt = relateDebt.Value > 0 ? avilableAmount : -avilableAmount,
                                    relateCode = relateDebt.RelateCode,
                                    user = user,
                                    associatedDate = sysMonth
                                });
                                newInputBillDebts.Add(new InputBillDebt()
                                {
                                    Id = Guid.NewGuid(),
                                    DebtAmount = avilableAmount,
                                    DebtCode = relateDebt.BillCode,
                                    InputBillId = inputBill.Id,
                                });
                                totalAmount -= avilableAmount;
                                usedAmount += avilableAmount;
                                lastTotalAvilableAmount += avilableAmount;
                            }
                            else
                            {
                                kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                                {
                                    finNum = relateDebt.BillCode,
                                    f_usedamt = isStoreIn ? thisAmount : -thisAmount,
                                    relateCode = relateDebt.RelateCode,
                                    user = user,
                                    associatedDate = sysMonth
                                });
                                newInputBillDebts.Add(new InputBillDebt()
                                {
                                    Id = Guid.NewGuid(),
                                    DebtAmount = Math.Abs(thisAmount),
                                    DebtCode = relateDebt.BillCode,
                                    InputBillId = inputBill.Id,
                                });
                                totalAmount -= Math.Abs(thisAmount);
                                usedAmount += Math.Abs(thisAmount);
                                lastTotalAvilableAmount += thisAmount;
                            }
                        }
                        index++;
                    }
                }
                var puaDetails = details.Where(p => p.BusinessType == 3 || p.BusinessType == 4 || p.BusinessType == 5).ToList();
                foreach (var item in puaDetails)
                {
                    kingdeeDetails.Add(new KingdeeInputBillSubmitDetailDto()
                    {
                        finNum = item.StoreInItemCode,
                        f_usedamt = decimal.Parse(item.NoTaxAmount.ToString("F2")),
                        user = user,
                        associatedDate = sysMonth
                    });
                }
                kingdeeData.finentry = kingdeeDetails;
                var kingdeeRes = await _kingdeeApiClient.SubInputBillToKingdee(kingdeeData);
                if (kingdeeRes.Code != CodeStatusEnum.Success)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-subInputBillToKingdee", kingdeeData);
                }
            }
            if (step > 1)
            {
                //回滚入库事件
                if (inventoryStoreInUpdateDetails.Any())
                {
                    var siRet = await _inventoryExcuteApiClient.UpdateStoreInDetail(inventoryStoreInUpdateDetails);
                    if (siRet == null || siRet.code != 200)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateStoreInDetail", inventoryStoreInUpdateDetails);
                    }
                }
            }
            if (step > 2)
            {
                //回滚经销调出
                if (inventoryStoreOutUpdateDetails.Any())
                {
                    var soRet = await _inventoryExcuteApiClient.UpdateStoreOutDetail(inventoryStoreOutUpdateDetails);
                    if (soRet == null || soRet.code != 200)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateStoreOutDetail", inventoryStoreOutUpdateDetails);
                    }
                }
            }
            if (step > 3)
            {
                //回滚寄售转购货
                var purRet = await _purchaseExcuteApiClient.UpdateInvoiceQuantity(updateQuantiyPuaInput);
                if (purRet.Code != CodeStatusEnum.Success)
                {
                    await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updateInvoiceQuantityQx", updateQuantiyPuaInput);
                }
            }
            if (step > 4)
            {
                //回滚购货修订
                if (updatePurchaseReviseInviceAmountInput.Any())
                {
                    var retpuchase = await _purchaseExcuteApiClient.UpdatePurchaseReviseInviceAmount(updatePurchaseReviseInviceAmountInput);
                    if (retpuchase.Code != CodeStatusEnum.Success)
                    {
                        await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-updatePurchaseReviseInviceAmountQx", updatePurchaseReviseInviceAmountInput);
                    }
                }
            }
        }

        /// <summary>
        /// 验证并修正供应商信息
        /// </summary>
        /// <param name="input">进项票输入</param>
        /// <returns></returns>
        private async Task ValidateAndCorrectSupplierInfoAsync(InputBillInputDTo input)
        {
            try
            {
                // 1. 首先验证当前的AgentId是否存在
                var currentSupplier = await _bDSApiClient.GetAgentInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = input.AgentId.ToString()
                });

                if (currentSupplier?.Any() == true)
                {
                    var supplier = currentSupplier.First();

                    // 检查名称是否一致
                    if (!string.Equals(supplier.agentName, input.AgentName, StringComparison.OrdinalIgnoreCase))
                    {
                        // 供应商名称不一致，可能是更名，记录日志并使用最新名称
                        _Logger.LogWarning("供应商名称不一致，可能已更名。进项票名称: {InputName}, 系统名称: {SystemName}, 供应商ID: {AgentId}",
                            input.AgentName, supplier.agentName, input.AgentId);

                        input.AgentName = supplier.agentName; // 使用系统中的最新名称
                    }

                    // 检查纳税人登记号是否一致
                    if (!string.IsNullOrEmpty(input.SaleDutyNumber) && input.SaleDutyNumber != "暂无" &&
                        !string.Equals(supplier.socialCode, input.SaleDutyNumber, StringComparison.OrdinalIgnoreCase))
                    {
                        // 纳税人登记号不一致，记录错误
                        _Logger.LogError("纳税人登记号不一致。进项票税号: {InputTaxNumber}, 系统税号: {SystemTaxNumber}, 供应商: {SupplierName}",
                            input.SaleDutyNumber, supplier.socialCode, supplier.agentName);

                        throw new Exception($"供应商【{input.AgentName}】的纳税人登记号不一致。进项票税号: {input.SaleDutyNumber}, 系统税号: {supplier.socialCode}，请检查进项票信息或联系管理员更新基础数据");
                    }

                    return; // 验证通过
                }
                // 如果提供了纳税人登记号，尝试通过税号查找
                if (!string.IsNullOrEmpty(input.SaleDutyNumber) && input.SaleDutyNumber != "暂无")
                {
                    // 注意：这里需要基础数据中心支持按纳税人登记号查询的接口
                    // 目前暂时跳过这个逻辑，记录日志
                    _Logger.LogWarning("无法通过纳税人登记号查询供应商（需要基础数据中心支持）。税号: {TaxNumber}, 供应商: {SupplierName}",
                        input.SaleDutyNumber, input.AgentName);
                }

                // 都没找到，抛出异常
                throw new Exception($"未找到供应商【{input.AgentName}】（ID: {input.AgentId}），请检查供应商信息是否正确或联系管理员在基础数据中心添加该供应商");
            }
            catch (Exception ex)
            {
                _Logger.LogError(ex, "验证供应商信息时发生异常。供应商: {SupplierName}, ID: {AgentId}", input.AgentName, input.AgentId);
                throw;
            }
        }
    }
}
