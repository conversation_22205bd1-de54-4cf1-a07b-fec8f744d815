﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    /// <summary>
    /// InventoryStoreExchangeBackOutput
    /// </summary>
    public class InventoryStoreExchangeBackOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string billCode { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public long? billDate { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? companyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string companyName { get; set; }
        /// <summary>
        /// 业务单元id
        /// </summary>
        public Guid? businessUnitId { get; set; }
        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string businessUnitName { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid agentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string agentName { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid projectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string projectName { get; set; }
        /// <summary>
        /// 事业部id
        /// </summary>
        public string businessDeptId { get; set; }
        /// <summary>
        /// 事业部全路径
        /// </summary>
        public string businessDeptFullPath { get; set; }
        /// <summary>
        /// 事业部全名路径名称
        /// </summary>
        public string businessDeptFullName { get; set; }
        /// <summary>
        /// 事业部简称/编号
        /// </summary>
        public string businessDeptShortName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 状态  0草稿 1待审核 2已完成
        /// </summary>
        public int status { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? createdBy { get; set; }
        /// <summary>
        /// 明细集合
        /// </summary>
        public List<InventoryStoreExchangeBackDetailOutput> details { get; set; }
    }

    /// <summary>
    /// 明细
    /// </summary>
    public class InventoryStoreExchangeBackDetailOutput
    {
        /// <summary>
        /// id
        /// </summary>
        public string id { get; set; }
        /// <summary>
        /// 主表id
        /// </summary>
        public string mainId { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string billCode { get; set; }
        /// <summary>
        /// 追溯码
        /// </summary>
        public string traceCode { get; set; }
        /// <summary>
        /// 换货出库单号
        /// </summary>
        public string storeOutCode { get; set; }
        /// <summary>
        /// 换货明细id
        /// </summary>
        public string exchangeDetailId { get; set; }
        /// <summary>
        /// 产品id
        /// </summary>
        public Guid productId { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int quantity { get; set; }
        /// <summary>
        /// 结算成本
        /// </summary>
        public decimal? settlementUnitCost { get; set; }

        /// <summary>
        /// 结算税率
        /// </summary>
        public decimal? settlementTaxRate { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string productNo { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string lotNo { get; set; }
        /// <summary>
        /// 条码
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        public string? sn { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        public long produceDate { get; set; }
        /// <summary>
        /// 有效期/失效日
        /// </summary>
        public long validDate { get; set; }
        /// <summary>
        /// 币种字典code
        /// </summary>
        public string coinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>
        public string coinName { get; set; }
        /// <summary>
        /// 币种代码
        /// </summary>
        public string? coinAttr { get; set; }
        /// <summary>
        /// 原币结算成本
        /// </summary>
        public decimal? settlementUnitCostOrigin { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? modelNo { get; set; }

        /// <summary>
        /// 最新含税成本
        /// </summary>
        public decimal? unitCost { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public Guid? businessUnitId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? businessUnitName { get; set; }
    }
}


