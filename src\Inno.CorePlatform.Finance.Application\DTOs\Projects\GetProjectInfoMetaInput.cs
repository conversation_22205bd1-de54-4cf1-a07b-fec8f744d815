﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Projects
{
    public class GetProjectInfoMetaInput
    {
        public string? Id { get; set; }
        public List<string>? Ids { get; set; }
        public string? Name{ get; set; }
        public List<string>? Names { get; set; }
        public string? CompanyId { get; set; }
        public StrategyQuery? StrategyQuery { get; set; }
        public int? Status { get; set; }
        public string? BusinessDeptId { get; set; }
        public int? Page { get; set; }
        public int? Limit { get; set; }
        public string? UserName { get; set; }
        public string? Type { get; set; }
        public string? SubType { get; set; }
        public bool? TransferPurchase { get; set; }
        public bool? UsedTransferPurchase { get; set; }
    }

    public class StrategyQuery
    {
        public string? UserId { get; set; }
        public string?  FunctionUri { get; set; }
    }
}
