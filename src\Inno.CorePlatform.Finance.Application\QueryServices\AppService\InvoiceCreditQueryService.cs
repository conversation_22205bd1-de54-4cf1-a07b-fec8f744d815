﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using Mapster;
using MathNet.Numerics.Optimization.LineSearch;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Index.HPRtree;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.X9;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using YamlDotNet.Core;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class InvoiceCreditQueryService : QueryAppService, IInvoiceCreditQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="iBDSApiClient"></param>
        public InvoiceCreditQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db, IBDSApiClient iBDSApiClient) :
            base(contextAccessor)
        {
            this._db = db;
        }

        public async Task<List<InvoiceCredit>> GetInvoiceCredits(List<string> codes)
        {
            var invoices = await _db.InvoiceCredits.Include(p => p.Credit).Where(p => codes.Contains(p.Credit.RelateCode)).ToListAsync();
            return invoices.Adapt<List<InvoiceCredit>>();
        }

        /// <summary>
        /// 根据发票号获取订单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<OrderInfoForInvoiceNoOutput>> GetOrderInfoForInvoiceNo(OrderInfoForInvoiceNoInput input)
        {
            var list = new List<OrderInfoForInvoiceNoOutput>();
            if (input.invoiceNos == null || !input.invoiceNos.Any())
            {
                return list;
            }
            var credits = await (from c in _db.Credits
                                 join ic in _db.InvoiceCredits on c.Id equals ic.CreditId
                                 where (c.CreditType == Domain.CreditTypeEnum.sale || c.CreditType == Domain.CreditTypeEnum.selforder) && !string.IsNullOrEmpty(ic.InvoiceNo) && input.invoiceNos.Contains(ic.InvoiceNo)
                                 select new
                                 {
                                     c.Id,
                                     ic.InvoiceNo,
                                     c.OrderNo,
                                     c.RelateCode,
                                     c.SaleSource,
                                     c.CreditType,
                                     c.CustomerId
                                 }).ToListAsync();
            if (input.customerId.HasValue)
            {
                credits = credits.Where(x => x.CustomerId == input.customerId).ToList();
            }
            if (credits.Any())
            {
                foreach (var item in credits)
                {
                    var orderInfo = new OrderInfoForInvoiceNoOutput();
                    orderInfo.InvoiceNo = item.InvoiceNo;
                    // 默认取订单号
                    orderInfo.OrderNo = item.OrderNo;
                    if (item.SaleSource == Domain.SaleSourceEnum.Spd && item.CreditType == Domain.CreditTypeEnum.servicefee)
                    {
                        // SPD服务费取关联单号
                        orderInfo.OrderNo = item.RelateCode;
                    }
                    if (!string.IsNullOrEmpty(item.RelateCode) && item.RelateCode.Contains("-SO-"))
                    {
                        orderInfo.Classify = 2;
                    }
                    if (!string.IsNullOrEmpty(item.RelateCode) && item.RelateCode.Contains("-SA-"))
                    {
                        orderInfo.Classify = 1;
                    }
                    orderInfo.RelateCode = item.RelateCode;
                    list.Add(orderInfo);
                }
            }
            return list;
        }

        public async Task<InvoiceCredit> GetInvoiceCreditByInvoiceNo(string invoiceNo)
        {
            var invoices = await _db.InvoiceCredits.Include(p => p.Credit).Where(p => p.InvoiceNo == invoiceNo).FirstOrDefaultAsync();
            return invoices.Adapt<InvoiceCredit>();
        }

        public async Task<List<Invoice>> GetInvoices(InvoiceQueryWebApiInput input)
        {
            Expression<Func<InvoicePo, bool>> exp = z => 1 == 1;
            if (input.InvoiceNos != null && input.InvoiceNos.Any())
            {
                exp = exp.And(p => input.InvoiceNos.Contains(p.InvoiceNo));
            }
            if (!string.IsNullOrEmpty(input.CustomizeInvoiceCode))
            {
                exp = exp.And(p => input.CustomizeInvoiceCode.Equals(p.CustomizeInvoiceCode));
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(p => input.CompanyId.Equals(p.CompanyId));
            }
            //开票时间范围查询
            if (input.InvoiceTimeStart.HasValue && input.InvoiceTimeEnd.HasValue)
            {
                exp = exp.And(p => p.InvoiceTime > input.InvoiceTimeStart.Value && p.InvoiceTime < input.InvoiceTimeEnd.Value);
            }

            var query = from i in _db.Invoices.Where(exp)
                        join iv in _db.InvoiceCredits on i.InvoiceNo equals iv.InvoiceNo
                        join c in _db.Credits on iv.CreditId equals c.Id
                        select new Invoice
                        {
                            Id = i.Id,
                            InvoiceNo = i.InvoiceNo,
                            InvoiceCode = i.InvoiceCode,
                            InvoiceAmount = i.InvoiceAmount,
                            InvoiceCheckCode = i.InvoiceCheckCode,
                            InvoiceTime = i.InvoiceTime,
                            CompanyId = i.CompanyId,
                            CompanyName = i.CompanyName,
                            NameCode = i.NameCode,
                            CustomizeInvoiceCode = i.CustomizeInvoiceCode,
                            Type = i.Type,
                            Remark = i.Remark,
                            CreatedBy = i.CreatedBy,
                            CreatedTime = i.CreatedTime,
                            UpdatedBy = i.UpdatedBy,
                            UpdatedTime = i.UpdatedTime,
                            CustomerName = i.CustomerName,
                            OrderNo = c.OrderNo,
                            RelateCode = c.RelateCode,
                        };
            return await query.Distinct().ToListAsync();
        }

        public async Task<List<GetInvoiceByOrderNosOutput>> GetInvoiceByOrderNos(GetInvoiceByOrderNosInput input)
        {
            Expression<Func<InvoicePo, bool>> exp = z => 1 == 1;
            if (input.InvoiceNos != null && input.InvoiceNos.Any())
            {
                exp = exp.And(p => input.InvoiceNos.Contains(p.InvoiceNo));
            } 
            if (input.IsCancel.HasValue)
            {
                exp = exp.And(p => input.IsCancel == p.IsCancel);
            }
            var query = from i in _db.Invoices.Where(exp)
                        join iv in _db.InvoiceCredits on i.InvoiceNo equals iv.InvoiceNo
                        join c in _db.Credits on iv.CreditId equals c.Id
                        join ci in _db.CustomizeInvoiceItem on i.CustomizeInvoiceCode equals ci.Code into ciGroup
                        from ci in ciGroup.DefaultIfEmpty()
                        where (input.OrderNos != null && input.OrderNos.Any() ?  input.OrderNos.ToHashSet().Contains(c.OrderNo) : true)
                        select new GetInvoiceByOrderNosOutput
                        {
                            InvoiceNo = i.InvoiceNo,
                            Type = i.Type,
                            InvoiceTime = i.InvoiceTime,
                            CustomerName = i.CustomerName,
                            CreditAmount = iv.CreditAmount,
                            InvoiceAmount = iv.InvoiceAmount,
                            TaxAmount = iv.TaxAmount,
                            CustomizeInvoiceCode = i.CustomizeInvoiceCode,
                            CreatedTime = i.CreatedTime,
                            CompanyName = i.CompanyName,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            CreditBillCode = c.BillCode,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            InvoiceCode = i.InvoiceCode,
                            StatusStr = i.IsCancel.HasValue && i.IsCancel == true ? "已作废" : "已开票",
                            Remark = i.Remark,
                            ChangedStatus = !string.IsNullOrEmpty(ci.RelationCode) ? null : ci.ChangedStatus,
                            ProjectName = c.ProjectName
                        };
            var list = await query.Distinct().ToListAsync();
            var query2 = from i in _db.Invoices.Where(exp)
                        join iv in _db.InvoiceCredits on i.InvoiceNo equals iv.InvoiceNo
                        join c in _db.Credits on iv.CreditId equals c.Id
                        join ci in _db.CustomizeInvoiceItem on i.CustomizeInvoiceCode equals ci.Code into ciGroup
                        from ci in ciGroup.DefaultIfEmpty()
                        where (input.OrderNos != null && input.OrderNos.Any() ? input.OrderNos.ToHashSet().Contains(c.RelateCode) : true)
                        select new GetInvoiceByOrderNosOutput
                        {
                            InvoiceNo = i.InvoiceNo,
                            Type = i.Type,
                            InvoiceTime = i.InvoiceTime,
                            CustomerName = i.CustomerName,
                            CreditAmount = iv.CreditAmount,
                            InvoiceAmount = iv.InvoiceAmount,
                            TaxAmount = iv.TaxAmount,
                            CustomizeInvoiceCode = i.CustomizeInvoiceCode,
                            CreatedTime = i.CreatedTime,
                            CompanyName = i.CompanyName,
                            ServiceName = c.ServiceName,
                            BusinessDeptFullName = c.BusinessDeptFullName,
                            CreditBillCode = c.BillCode,
                            RelateCode = c.RelateCode,
                            OrderNo = c.OrderNo,
                            InvoiceCode = i.InvoiceCode,
                            StatusStr = i.IsCancel.HasValue && i.IsCancel == true ? "已作废" : "已开票",
                            Remark = i.Remark,
                            ChangedStatus = !string.IsNullOrEmpty(ci.RelationCode) ? null : ci.ChangedStatus,
                            ProjectName = c.ProjectName
                        }; 
            var list2 = await query.Distinct().ToListAsync();
            return list.Union(list2).ToList();
        }
    }
}
