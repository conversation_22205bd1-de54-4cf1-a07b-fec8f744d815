﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IInventoryMgmAppService
    {
        /// <summary>
        /// 开启盘点
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> StartInventory();

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        Task<(List<InventoryDTO>, int)> GetInventoryList(InventoryQueryDto queryDto);

        /// <summary>
        /// 根据公司ID和系统月度获取盘点记录
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        Task<InventoryDTO?> GetInventoryByCompanyAndMonth(Guid companyId, string sysMonth);

        /// <summary>
        /// 批量根据公司ID和系统月度获取盘点记录
        /// </summary>
        /// <param name="companyIds">公司ID列表</param>
        /// <param name="sysMonth">系统月度</param>
        /// <returns></returns>
        Task<List<InventoryDTO>> GetInventoriesByCompaniesAndMonth(List<Guid> companyIds, string sysMonth);

        /// <summary>
        /// 更新盘点状态
        /// </summary>
        /// <param name="inventoryId">盘点ID</param>
        /// <param name="status">状态</param>
        /// <param name="updatedBy">更新人</param>
        /// <returns></returns>
        Task UpdateInventoryStatus(Guid inventoryId, int status, string? updatedBy = null);

        /// <summary>
        /// 批量更新盘点状态
        /// </summary>
        /// <param name="inventoryIds">盘点ID列表</param>
        /// <param name="status">状态</param>
        /// <param name="updatedBy">更新人</param>
        /// <returns>更新成功的数量</returns>
        Task<int> BatchUpdateInventoryStatus(List<Guid> inventoryIds, int status, string? updatedBy = null);

        /// <summary>
        ///
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UpdateStoreInventory(StoreInventoryUpdateInputDto dto);


        /// <summary>
        /// 获取公司未完成单据
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<OutInventoryItemOutput>>> GetNoFinishBillForInventory(OutInventoryItemInput input);

        /// <summary>
        /// 获取某公司的盘点状态
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<BaseResponseData<InventoryInfoForCompanyDto>> GetInventoryStatus(Guid companyId);

        /// <summary>
        /// 生成其他盘点数据
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CreateOtherCheck(Guid companyId, Guid userId, string userName);

        /// <summary>
        /// 完成总盘点
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> FinishInventory(Guid inventoryItemId, string? userName = null);

        /// <summary>
        /// 删除库存盘点单（单个）
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DeleteStoreInventory(StoreInventoryDeleteInputDto dto);

        /// <summary>
        /// 是否盘点期间
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> InventoryCheck(Guid companyId);

        /// <summary>
        /// 批量更新盘点单字段
        /// </summary>
        /// <param name="inventoryItemId">盘点单ID</param>
        /// <param name="fieldUpdates">字段更新字典</param>
        /// <returns></returns>
        Task UpdateInventoryItemFields(Guid inventoryItemId, Dictionary<string, string> fieldUpdates);

        /// <summary>
        /// 更新盘点单字段
        /// </summary>
        /// <param name="inventoryItemId">盘点单ID</param>
        /// <param name="fieldName">字段名称</param>
        /// <param name="fieldValue">字段值</param>
        /// <returns></returns>
        Task UpdateInventoryItemField(Guid inventoryItemId, string fieldName, string fieldValue);

        /// <summary>
        /// 应收盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        Task<(int, string)> CreditRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName);

        /// <summary>
        /// 已签收待开票盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        Task<(int, string)> ReceivedNoInvoiceInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName);

        /// <summary>
        /// 应付盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        Task<(int, string)> DebtRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName);

        /// <summary>
        /// 付款盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        Task<(int, string)> PaymentRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName);

        /// <summary>
        /// 垫资盘点
        /// </summary>
        /// <param name="companyId">公司id</param>
        /// <param name="sysmonth">盘底月份</param>
        /// <param name="compnyCode">公司Code</param>
        /// <param name="compnyName">公司名称</param>
        /// <param name="userName">用户名</param>
        /// <returns></returns>
        Task<(int, string)> AdvanceRecordInventory(Guid companyId, string sysmonth, string compnyCode, string compnyName, string userName);
    }
}
