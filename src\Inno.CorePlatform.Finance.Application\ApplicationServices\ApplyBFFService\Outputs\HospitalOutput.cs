﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs
{
    public class HospitalOutput : BaseOutput
    {
        public List<HospitalPersonOutput> Persons { get; set; }
    }
    public class HospitalDeptOutput : BaseOutput
    {
        public HospitalDeptOutput(DaprHospitalDeptOutput d)
        {
            this.Id = d.customerDeptId;
            this.Name = d.deptName;
            if (d.deptAddressList != null && d.deptAddressList.Count > 0)
            {
                var displayAddress = d.deptAddressList[0];
                this.Address = $"{displayAddress.provinceDesc}{displayAddress.cityDesc}{displayAddress.countyDesc}{displayAddress.customerAddress}";
            }
        }
        public string Address { get; set; }
    }
    public class HospitalPersonOutput : BaseOutput
    {
        public HospitalPersonOutput()
        {

        }
        public HospitalPersonOutput(DaprHospitalPersonOutput p, DaprHospitalDeptOutput d)
        {
            this.Id = p.customerDeptStaffId;
            this.Name = p.name;
            this.Telephone = p.telephone;
            this.Department = new HospitalDeptOutput(d);
        }
        public string Telephone { get; set; }
        public HospitalDeptOutput Department { get; set; }
    }
}
