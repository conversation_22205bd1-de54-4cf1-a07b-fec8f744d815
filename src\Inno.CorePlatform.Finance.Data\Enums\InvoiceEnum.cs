﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Data.Enums
{ 
    public enum CustomizeInvoiceChangedStatusEnum
    {
        [Description("整单红冲")]
        RedOffset = 1,

        [Description("部分红冲")]
        PartRedOffset = 2,
    }

    public enum CustomizeInvoiceRelationTypeEunm
    {
        [Description("整单红冲")]
        RedOffset = 1,
        [Description("部分红冲")]
        PartRedOffset = 2
    }

}
