﻿using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Domain;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 付款单列表查询输出类
    /// </summary>
    public class PaymentQueryListOutput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public string BillDateStr
        {
            get
            {
                return BillDate.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 厂家单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentName { get; set; }


        /// <summary>
        /// 付款单类型
        /// </summary>
        public PaymentTypeEnum Type { get; set; }

        /// <summary>
        /// 付款单类型展示
        /// </summary>
        public string TypeDisplay
        {
            get
            {
                return Type.GetDescription();
            }
        }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedValue { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal RemainValue
        {
            get
            {
                return Value - AbatedValue;
            }
        }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public string PaymentDateStr
        {
            get
            {
                return PaymentDate != DateTime.MinValue ? PaymentDate.ToString("yyyy-MM-dd") : "";
            }
        }

        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 冲销状态展示
        /// </summary>
        public string AbatedStatusDisplay
        {
            get
            {
                if (!AbatedStatus.HasValue) return string.Empty;
                return AbatedStatus.GetDescription();
            }
        }

        /// <summary>
        /// 操作人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 币种代码
        /// </summary>
        public string? CoinCode { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>
        public string? CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary>
        public decimal? RMBAmount { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 批量付款单号
        /// </summary>
        public string? PaymentAutoItemCode { get; set; }
        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [MaxLength(500)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// 终端医院字符串
        /// </summary>
        public string? HosStr { get; set; }

        /// <summary>
        /// 现金折扣金额
        /// </summary>
        public decimal? LimitedDiscount { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string AttachFileIds { get; set; }
    }
    public class PaymentQueryListTabOutput
    {
        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 已支付
        /// </summary>
        public int PaidCount { get; set; }
        /// <summary>
        /// 未支付
        /// </summary>
        public int NonPaidCount { get; set; }
    }
    /// <summary>
    /// 付款单列表查询输出类
    /// </summary>
    public class PaymentQueryOutput
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 1：付款单，2：负数应付，3：服务费应收
        /// </summary>
        public int Classify { get; set; } = 1;
        /// <summary>
        /// 原始付款单号
        /// </summary>
        public string? OriginCode { get; set; }

    }

    public class UsedCreditAndPaymentOutput
    {
        public decimal UsedCredit { get; set; }
        public List<PaymentQueryOutput> PaymentQueryOutputs { get; set; }
    }

    /// <summary>
    /// 导出终端医院查询出参
    /// </summary>
    public class HospitalExcelOutput
    {
        public string? PaymentAutoItemCode { get; set; }
        public string? HospitalName { get; set; }

        public Guid? AgentId { get; set; }
        public string? Code { get; set; }
    }

    /// <summary>
    /// 通过采购单号和项目Id获取付款单信息输出参数
    /// </summary>
    public class PaymentByPurchaseAndProjectOutput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 付款单位（公司名称）
        /// </summary>
        public string? PayerCompanyName { get; set; }

        /// <summary>
        /// 收款单位（供应商名称）
        /// </summary>
        public string? PayeeAgentName { get; set; }

        /// <summary>
        /// 付款单类型
        /// </summary>
        public PaymentTypeEnum PaymentType { get; set; }

        /// <summary>
        /// 付款单类型展示
        /// </summary>
        public string PaymentTypeDisplay
        {
            get
            {
                return PaymentType.GetDescription();
            }
        }

        /// <summary>
        /// 实际付款日期
        /// </summary>
        public DateTime? ActualPaymentDate { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinCode { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string? CoinName { get; set; }

        /// <summary>
        /// 币种金额
        /// </summary>
        public decimal CoinAmount { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary>
        public decimal RMBAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 厂家单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }
    }

    /// <summary>
    /// 付款清单付款信息出参
    /// </summary>
    public class PaymentPayInfoListOutput : PaymentQueryListOutput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillCode { get; set; }

        /// <summary>
        /// 应付关联单号
        /// </summary>
        public string? DebtRelateCode { get; set; }

        /// <summary>
        /// 付款信息-采购合同单号
        /// </summary>
        public string? DebtPurchaseContactNo { get; set; }

        /// <summary>
        /// 付款信息-币种
        /// </summary>
        public string? DebtCoinName { get; set; }

        /// <summary>
        /// 付款信息-供应商
        /// </summary>  
        public string? DebtAgentName { get; set; }

        /// <summary>
        /// 付款信息-厂家订单号
        /// </summary>
        public string? DebtProducerOrderNo { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal? DebtValue { get; set; }

        /// <summary>
        /// 付款信息-客户
        /// </summary>
        public string? CreditCustomerName { get; set; }

        /// <summary>
        /// 应付冲销金额
        /// </summary>
        public decimal? DebtAbatedValue { get; set; }

        /// <summary>
        /// 应付余额
        /// </summary>
        public decimal? DebtBalance { get; set; }

        /// <summary>
        /// 本次付款金额
        /// </summary>
        public decimal DebtDetailValue { get; set; }

        /// <summary>
        /// 现金折扣金额
        /// </summary>
        public decimal? LimitedDiscount { get; set; }

        /// <summary>
        ///付款信息- 折扣
        /// </summary>
        public decimal? DebtDiscount { get; set; }

        /// <summary>
        ///付款信息-折前金额
        /// </summary>
        public decimal? DebtOriginValue { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int? AccountPeriodType { get; set; }

        /// <summary>
        /// 账期类型描述
        /// </summary>
        public string? AccountPeriodDescription { get { return this.AccountPeriodType != null ? ((AccountPeriodTypeEnum)this.AccountPeriodType).GetDescription() : ""; } }

        /// <summary>
        ///付款信息-采购单号
        /// </summary>
        public string? DebtPurchaseCode { get; set; }

        /// <summary>
        /// 付款信息-业务单元名称  
        /// </summary>
        public string? DebtServiceName { get; set; }

        /// <summary>
        /// 付款信息-进项票金额
        /// </summary>
        public decimal? DebtNoTaxAmount { get; set; }

        /// <summary>
        /// 付款信息-收款单号
        /// </summary>
        public string? DebtReceiveCode { get; set; }

        /// <summary>
        /// 付款信息-终端医院名称
        /// </summary>
        public string? DebtHospitalName { get; set; }

        /// <summary>
        /// 付款信息-订单号
        /// </summary>
        public string? DebtOrderNo { get; set; }

        /// <summary>
        /// 付款信息-发票号 
        /// </summary>
        public string? DebtInvoiceNo { get; set; }

        /// <summary>
        /// 付款日期-发票日期
        /// </summary>
        public string? DebtInvoiceTime { get; set; }

        /// <summary>
        /// 付款信息-原始订单号
        /// </summary>
        public string? DebtOriginOrderNo { get; set; }

        /// <summary>
        /// 付款信息-应收id
        /// </summary>
        public Guid? DebtCreditId { get; set; }
    }
}
