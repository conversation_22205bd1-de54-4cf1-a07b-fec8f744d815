﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class BatchPaymentController : BaseController
    {
        private readonly IBatchPaymentAppService _batchPaymentAppService;
        private readonly IBulkPaymentQueryService _bulkPaymentQueryService;
        private readonly ILogger<BatchPaymentController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IAbtmentService _abtmentService;
        private readonly IPaymentQueryService _paymentQueryService;
        private readonly IFileGatewayClient _fileGatewayClient;

        public BatchPaymentController(
            IBatchPaymentAppService batchPaymentAppService,
            ILogger<BatchPaymentController> logger,
            IAbtmentService abtmentService,
            IKingdeeApiClient kingdeeApiClient,
            IPaymentQueryService paymentQueryService,
            IBulkPaymentQueryService bulkPaymentQueryService,
            IFileGatewayClient fileGatewayClient, ISubLogService subLog) : base(subLog)
        {
            this._batchPaymentAppService = batchPaymentAppService;
            this._bulkPaymentQueryService = bulkPaymentQueryService;
            this._logger = logger;
            this._abtmentService = abtmentService;
            this._paymentQueryService = paymentQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._fileGatewayClient = fileGatewayClient;
        }
        /// <summary>
        /// 查询结算方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetSettlementType")]
        public async Task<BaseResponseData<List<PaymentSettlementtypeOutput>>> GetSettlementType(SettlementtypeInput input)
        {
            var kindRet = await _kingdeeApiClient.GetSettlementtype(input);
            if (kindRet.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<List<PaymentSettlementtypeOutput>>.Failed(500, kindRet.Message);
            }
            else
            {
                if (kindRet.Data != null && kindRet.Data.rows != null)
                {
                    var lst = kindRet.Data.rows.Where(p => p.enable.Equals("1")).Select(p => new PaymentSettlementtypeOutput
                    {
                        Id = p.number,
                        Name = p.name
                    }).ToList();
                    var ret = BaseResponseData<List<PaymentSettlementtypeOutput>>.Success();
                    ret.Data = lst;
                    return ret;
                }
                return BaseResponseData<List<PaymentSettlementtypeOutput>>.Failed(500, "【金蝶】没有返回任何数据");
            }
        }
        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AddPaymentAutoItem")]
        public async Task<BaseResponseData<bool>> AddPaymentAutoItemAsync([FromBody] PaymentAutoItemInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            input.OperatorId = CurrentUser.Id;
            input.OperatorName = CurrentUser.Name;
            input.UpdatedBy = CurrentUser.UserName;
            var res = await _batchPaymentAppService.CreateAsync(input);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("创建成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "创建失败！");
            }

        }

        /// <summary>
        /// 同步
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SyncClick")]
        public async Task<BaseResponseData<bool>> SyncClick([FromBody] PaymentAutoItemInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            input.OperatorId = CurrentUser.Id;
            input.OperatorName = CurrentUser.Name;
            input.UpdatedBy = CurrentUser.UserName;
            var paymentAutoDetails = await _bulkPaymentQueryService.GetPaymentDetails(new QueryById { Id = input.Id });
            var paymentCodes = string.Join(",", paymentAutoDetails.Select(p => p.PaymentCode).ToList());
            var payments = await _paymentQueryService.GetByCodes(paymentCodes.Split(",").ToList());
            var pubInput = new List<GenerateAbtInput>();
            foreach (var payment in payments)
            {
                pubInput.Add(new GenerateAbtInput
                {
                    Value = payment.Value,
                    PaymentCode = payment.Code,
                    DebtDetilId = paymentAutoDetails.First(p => p.PaymentCode.Contains(payment.Code)).DebtDetailId.Value
                });
            }
            await _abtmentService.OrderPaiedPub(pubInput);
            return BaseResponseData<bool>.Success("操作成功！");


        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id">批量付款单Id</param>
        /// <returns></returns>
        [HttpPut("SubmitPaymentAutoItem")]
        public async Task<BaseResponseData<bool>> SubmitPaymentAutoItemAsync(Guid id)
        {

            var res = await _batchPaymentAppService.SubmitAsync(id, CurrentUser.UserName);
            if (res)
            {
                return BaseResponseData<bool>.Success("提交成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "提交失败！");
            }
        }

        /// <summary>
        /// 撤回批量付款单
        /// </summary>
        /// <param name="id">批量付款单Id</param>
        /// <returns></returns>
        [HttpPut("CancelPaymentAutoItem")]
        public async Task<BaseResponseData<bool>> CancelPaymentAutoItemAsync(Guid id)
        {
            var res = await _batchPaymentAppService.SubmitOrCancelAsync(id, CurrentUser.UserName, 1);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("撤回成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "撤回失败！");
            }
        }

        /// <summary>
        /// 删除批量付款单
        /// </summary>
        /// <param name="ids">批量付款单Id</param>
        /// <returns></returns>
        [HttpDelete("DeletePaymentAutoItem")]
        public async Task<BaseResponseData<bool>> DeletePaymentAutoItemAsync(List<Guid> ids)
        {
            var res = 0;
            foreach (var id in ids)
            {
                res += await _batchPaymentAppService.DeleteAsync(id);
            }

            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }

        /// <summary>
        /// 添加批量付款单明细
        /// </summary>
        /// <param name="lstDetail"></param>
        /// <returns></returns>
        [HttpPost("AddPaymentAutoDetail")]
        public async Task<BaseResponseData<bool>> AddPaymentAutoDetailAsync(List<PaymentAutoDetailInput> lstDetail)
        {

            var res = await _batchPaymentAppService.AddDetailAsync(lstDetail, CurrentUser.UserName);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("添加成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "添加失败！");
            }
        }

        /// <summary>
        /// 重新选取明细
        /// </summary>
        /// <param name="lstDetail">新的明细数据</param>
        /// <returns></returns>
        [HttpPost("ReAddPaymentAutoDetail")]
        public async Task<BaseResponseData<bool>> ReAddPaymentAutoDetailAsync(List<PaymentAutoDetailInput> lstDetail)
        {
            var res = await _batchPaymentAppService.ReAddDetailAsync(lstDetail, CurrentUser.UserName);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("添加成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "添加失败！");
            }
        }

        /// <summary>
        /// 删除批量付款单明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpDelete("DeletePaymentAutoDetail")]
        public async Task<BaseResponseData<bool>> DeletePaymentAutoDetailAsync([FromBody] PaymentAutoDetailDeleteInput input)
        {
            var res = await _batchPaymentAppService.DeleteDetailAsync(input);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }

        /// <summary>
        /// 执行批量付款单
        /// </summary>
        /// <param name="id">批量付款单Id</param>
        /// <returns></returns>
        [HttpPost("ExcutePaymentAutoItem")]
        public async Task<BaseResponseData<bool>> ExcutePaymentAutoItemAsync(Guid id)
        {
            var res = await _batchPaymentAppService.ExcutePaymentAutoItemAsync(id);
            if (res)
            {
                return BaseResponseData<bool>.Success("执行成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "执行失败！");
            }

        }
        /// <summary>
        /// 获取批量付款清单
        /// </summary>
        /// <param name="input">批量付款单查询条件</param>
        /// <returns></returns>
        [HttpPost("getlist")]
        public async Task<BaseResponseData<PageResponse<PaymnetItemOutput>>> GetPaymentItems([FromBody] BulkPaymentQueryInput input)
        {
            input.UserId = CurrentUser.Id.Value;
            input.UserName = CurrentUser.UserName;
            var result = await _bulkPaymentQueryService.GetPaymentItems(input);
            var res = new BaseResponseData<PageResponse<PaymnetItemOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<PaymnetItemOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;

        }
        /// <summary>
        /// 获取批量付款明细
        /// </summary>
        /// <param name="query">批量付款单Id</param>
        /// <returns></returns>
        [HttpPost("getdetail")]
        public async Task<BaseResponseData<PageResponse<PaymentDetailOutput>>> GetPaymentDetails([FromBody] QueryById query)
        {
            var result = await _bulkPaymentQueryService.GetPaymentDetails(query);
            var res = new BaseResponseData<PageResponse<PaymentDetailOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<PaymentDetailOutput>
                {
                    List = result,
                    Total = result.Count
                },
            };
            return res;

        }
        /// <summary>
        /// 获取批量付款明细
        /// </summary>
        /// <param name="query">批量付款单Id</param>
        /// <returns></returns>
        [HttpPost("getdetailaggregation")]
        public async Task<BaseResponseData<PageResponse<PaymentAggratByAgent>>> GetPaymentDetailsAggregation([FromBody] QueryById query)
        {
            var result = await _bulkPaymentQueryService.GetPaymentDetailsAggregation(query.Id);
            var res = new BaseResponseData<PageResponse<PaymentAggratByAgent>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<PaymentAggratByAgent>
                {
                    List = result,
                    Total = result.Count
                },
            };
            return res;

        }
        /// <summary>
        /// 按状态获取分页总数
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<BulkPaymentTabOutput>> GetTabCountAsync([FromBody] BulkPaymentQueryInput input)
        {
            input.UserId = CurrentUser.Id.Value;
            input.UserName = CurrentUser.UserName;
            var model = await _bulkPaymentQueryService.GetTabCountAsync(input);
            return new BaseResponseData<BulkPaymentTabOutput>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }

        /// <summary>
        /// 创建批量付款供应商银行信息
        /// </summary>
        /// <param name="agentBanks"></param>
        /// <returns></returns>
        [HttpPost("CreatePaymentAutoBankInfo")]
        [Obsolete]
        public async Task<BaseResponseData<int>> CreatePaymentAutoBankInfo(List<AgentBankInput> agentBanks)
        {
            return await _batchPaymentAppService.CreatePaymentAutoBankInfos(agentBanks);
        }

        /// <summary>
        /// 创建批量付款供应商银行信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetAgentBank")]
        public async Task<BaseResponseData<int>> SetAgentBank(AgentBankInput input)
        {
            return await _batchPaymentAppService.SetAgentBank(input);
        }
        /// <summary>
        /// 修改备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("UpdateRemark")]
        public async Task<BaseResponseData<int>> UpdateRemark(UpdateRemarkInput input)
        {
            return await _batchPaymentAppService.UpdateRemark(input);
        }
        /// <summary>
        /// 修改附言
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("UpdateTransferDiscourse")]
        public async Task<BaseResponseData<int>> UpdateTransferDiscourse(UpdateTransferDiscourseInput input)
        {
            return await _batchPaymentAppService.UpdateTransferDiscourse(input);
        }

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetExportList")]
        public async Task<IActionResult> GetExportList(List<PaymentExcelInput> list)
        {
            try
            {
                if (list == null || !list.Any())
                {
                    throw new ApplicationException("请选择数据后操作");
                }
                var datas = new List<PaymentExcelOutput>();
                foreach (var item in list)
                {
                    // 循环调用查询，会存在性能问题，但考虑数据量不大，因此忽略
                    var model = item.Adapt<PaymentExcelOutput>();
                    model.Details = await _bulkPaymentQueryService.GetPaymentDetails(new QueryById { Id = model.Id });
                    model.Agents = await _bulkPaymentQueryService.GetPaymentDetailsAggregation(model.Id);
                    datas.Add(model);
                }

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var stream = new MemoryStream();
                using (var package = new ExcelPackage(stream))
                {
                    if (datas.Any())
                    {
                        var ret = BaseResponseData<List<PaymentSettlementtypeOutput>>.Success();
                        var kindresp = new SettlementtypeInput { pageNo = 1, pageSize = 100 };
                        var kindRet = await _kingdeeApiClient.GetSettlementtype(kindresp);
                        if (kindRet.Code == CodeStatusEnum.Success)
                        {
                            if (kindRet.Data != null && kindRet.Data.rows != null)
                            {
                                var lst = kindRet.Data.rows.Where(p => p.enable.Equals("1")).Select(p => new PaymentSettlementtypeOutput
                                {
                                    Id = p.number,
                                    Name = p.name
                                }).ToList();
                                ret.Data = lst;
                            }
                        }

                        var worksheet1 = package.Workbook.Worksheets.Add("批量付款");
                        #region 批量单据信息
                        worksheet1.Cells[1, 1].Value = "批量付款单号";
                        worksheet1.Cells[1, 2].Value = "核算部门";
                        worksheet1.Cells[1, 3].Value = "预计付款日期";
                        worksheet1.Cells[1, 4].Value = "公司";
                        worksheet1.Cells[1, 5].Value = "日期";
                        worksheet1.Cells[1, 6].Value = "总金额";
                        worksheet1.Cells[1, 7].Value = "状态";
                        worksheet1.Cells[1, 8].Value = "备注";
                        worksheet1.Cells[1, 9].Value = "申请人";
                        worksheet1.Cells[1, 10].Value = "供应商";
                        worksheet1.Cells[1, 11].Value = "付款金额";
                        worksheet1.Cells[1, 12].Value = "付款方式";
                        worksheet1.Cells[1, 13].Value = "收款账号";
                        worksheet1.Cells[1, 14].Value = "转账附言";
                        worksheet1.Cells[1, 15].Value = "应付单号";
                        worksheet1.Cells[1, 16].Value = "采购合同单号";
                        worksheet1.Cells[1, 17].Value = "币种";
                        worksheet1.Cells[1, 18].Value = "厂家单号";
                        worksheet1.Cells[1, 19].Value = "应付金额";
                        worksheet1.Cells[1, 20].Value = "应付冲销金额";
                        worksheet1.Cells[1, 21].Value = "应付余额";
                        worksheet1.Cells[1, 22].Value = "本次付款金额";
                        worksheet1.Cells[1, 23].Value = "折扣";
                        worksheet1.Cells[1, 24].Value = "折前金额";
                        worksheet1.Cells[1, 25].Value = "账期类型";
                        worksheet1.Cells[1, 26].Value = "业务单元";
                        worksheet1.Cells[1, 27].Value = "厂家订单号";
                        worksheet1.Cells[1, 28].Value = "采购单号";
                        worksheet1.Cells[1, 29].Value = "应收单号";
                        worksheet1.Cells[1, 30].Value = "收款单号";
                        worksheet1.Cells[1, 31].Value = "医院";
                        worksheet1.Cells[1, 32].Value = "订单号";
                        worksheet1.Cells[1, 33].Value = "发票号";
                        worksheet1.Cells[1, 34].Value = "开票日期";
                        worksheet1.Cells[1, 35].Value = "终端医院";
                        worksheet1.Cells[1, 36].Value = "现金折扣金额";


                        int row = 2;
                        foreach (var item in datas)
                        {
                            if (!item.Agents.Any() && !item.Details.Any())
                            {
                                worksheet1.Cells[row, 1].Value = item.Code;
                                worksheet1.Cells[row, 2].Value = item.BusinessDeptFullName;
                                worksheet1.Cells[row, 3].Value = item.ProbablyPayTime;
                                worksheet1.Cells[row, 4].Value = item.CompanyName;
                                worksheet1.Cells[row, 5].Value = item.BillDate.HasValue ? item.BillDate.Value.ToString("yyyy-MM-dd") : "";
                                worksheet1.Cells[row, 6].Value = item.SumValue.HasValue ? decimal.Parse(item.SumValue.Value.ToString("#0.00")) : 0;
                                worksheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                                worksheet1.Cells[row, 7].Value = item.StatusDescription;
                                worksheet1.Cells[row, 8].Value = item.Remark;
                                worksheet1.Cells[row, 9].Value = item.CreatedByName;
                                row++;
                            }
                            else if (item.Agents.Any())
                            {
                                foreach (var agent in item.Agents)
                                {
                                    var details = item.Details.Where(x => x.AgentId == agent.AgentId).ToList();
                                    if (details.Any())
                                    {
                                        //供应商对应一条付款信息时不重复显示
                                        if (details.Count == 1)
                                        {
                                            var detail = details.FirstOrDefault();
                                            worksheet1.Cells[row, 1].Value = item.Code;
                                            worksheet1.Cells[row, 2].Value = item.BusinessDeptFullName;
                                            worksheet1.Cells[row, 3].Value = detail == null || !detail.ProbablyPayTime.HasValue ? item.ProbablyPayTime : detail.ProbablyPayTime.Value.ToString("yyyy-MM-dd");// item.ProbablyPayTime;
                                            worksheet1.Cells[row, 4].Value = item.CompanyName;
                                            worksheet1.Cells[row, 5].Value = item.BillDate.HasValue ? item.BillDate.Value.ToString("yyyy-MM-dd") : "";
                                            worksheet1.Cells[row, 6].Value = item.SumValue.HasValue ? decimal.Parse(item.SumValue.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 7].Value = item.StatusDescription;
                                            worksheet1.Cells[row, 8].Value = item.Remark;
                                            worksheet1.Cells[row, 9].Value = item.CreatedByName;
                                            var bank = agent.BankInfo.FirstOrDefault();
                                            worksheet1.Cells[row, 10].Value = agent.AgentName;
                                            worksheet1.Cells[row, 11].Value = agent.Sum.HasValue ? decimal.Parse(agent.Sum.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                                            var pay = new PaymentSettlementtypeOutput();
                                            if (bank != null && ret.Data != null && ret.Data.Any())
                                            {
                                                pay = ret.Data.Where(x => x.Id == bank.PayClassify).FirstOrDefault();
                                            }
                                            worksheet1.Cells[row, 12].Value = pay != null ? pay.Name : string.Empty;
                                            worksheet1.Cells[row, 13].Value = bank != null ? string.Concat(bank.Bank, "(", bank.BankNo, ")") : string.Empty;
                                            worksheet1.Cells[row, 14].Value = bank != null ? bank.TransferDiscourse : string.Empty;
                                            worksheet1.Cells[row, 15].Value = detail?.DebtBillCode;
                                            worksheet1.Cells[row, 16].Value = detail?.PurchaseContactNo;
                                            worksheet1.Cells[row, 17].Value = detail?.CoinName;
                                            worksheet1.Cells[row, 18].Value = detail?.ProducerOrderNo;
                                            worksheet1.Cells[row, 19].Value = detail != null && detail.DebtValue.HasValue ? decimal.Parse(detail.DebtValue.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 19].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 20].Value = detail != null && detail.DebtAbatedValue.HasValue ? decimal.Parse(detail.DebtAbatedValue.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 20].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 21].Value = detail.DebtBalance.HasValue ? decimal.Parse(detail.DebtBalance.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 21].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 22].Value = detail.DebtDetailValue;
                                            worksheet1.Cells[row, 22].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 23].Value = detail.Discount.HasValue ? decimal.Parse(detail.Discount.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 23].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 24].Value = detail.OriginValue.HasValue ? decimal.Parse(detail.OriginValue.Value.ToString("#0.00")) : 0;
                                            worksheet1.Cells[row, 24].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, 25].Value = detail.AccountPeriodDescription;
                                            worksheet1.Cells[row, 26].Value = detail.ServiceName;
                                            worksheet1.Cells[row, 27].Value = detail.ProducerOrderNo;
                                            worksheet1.Cells[row, 28].Value = detail.PurchaseCode;
                                            worksheet1.Cells[row, 29].Value = detail.CreditBillCode;
                                            worksheet1.Cells[row, 30].Value = detail.ReceiveCode;
                                            worksheet1.Cells[row, 31].Value = detail.CustomerName;
                                            worksheet1.Cells[row, 32].Value = detail.OrderNo;
                                            worksheet1.Cells[row, 33].Value = detail.InvoiceNo;
                                            worksheet1.Cells[row, 34].Value = detail.InvoiceTime;
                                            worksheet1.Cells[row, 35].Value = detail.HospitalName;
                                            worksheet1.Cells[row, 36].Value = detail.LimitedDiscount;

                                            row++;
                                        }
                                        else
                                        {
                                            foreach (var detail in details)
                                            {
                                                worksheet1.Cells[row, 1].Value = item.Code;
                                                worksheet1.Cells[row, 2].Value = item.BusinessDeptFullName;
                                                worksheet1.Cells[row, 3].Value = detail == null || !detail.ProbablyPayTime.HasValue ? item.ProbablyPayTime : detail.ProbablyPayTime.Value.ToString("yyyy-MM-dd");
                                                worksheet1.Cells[row, 4].Value = item.CompanyName;
                                                worksheet1.Cells[row, 5].Value = item.BillDate.HasValue ? item.BillDate.Value.ToString("yyyy-MM-dd") : "";
                                                worksheet1.Cells[row, 6].Value = item.SumValue.HasValue ? decimal.Parse(item.SumValue.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 7].Value = item.StatusDescription;
                                                worksheet1.Cells[row, 8].Value = item.Remark;
                                                worksheet1.Cells[row, 9].Value = item.CreatedByName;
                                                var bank = agent.BankInfo.FirstOrDefault();
                                                worksheet1.Cells[row, 10].Value = agent.AgentName;
                                                worksheet1.Cells[row, 11].Value = agent.Sum.HasValue ? decimal.Parse(agent.Sum.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 12].Value = string.Empty;
                                                var pay = new PaymentSettlementtypeOutput();
                                                if (bank != null && ret.Data != null && ret.Data.Any())
                                                {
                                                    pay = ret.Data.Where(x => x.Id == bank.PayClassify).FirstOrDefault();
                                                }
                                                worksheet1.Cells[row, 12].Value = pay != null ? pay.Name : string.Empty;
                                                worksheet1.Cells[row, 13].Value = bank != null ? string.Concat(bank.Bank, "(", bank.BankNo, ")") : string.Empty;
                                                worksheet1.Cells[row, 14].Value = bank != null ? bank.TransferDiscourse : string.Empty;
                                                worksheet1.Cells[row, 15].Value = detail != null ? detail.DebtBillCode : string.Empty;
                                                worksheet1.Cells[row, 16].Value = detail != null ? detail.PurchaseContactNo : string.Empty;
                                                worksheet1.Cells[row, 17].Value = detail != null ? detail.CoinName : string.Empty;
                                                worksheet1.Cells[row, 18].Value = detail?.ProducerOrderNo;
                                                worksheet1.Cells[row, 19].Value = detail != null && detail.DebtValue.HasValue ? decimal.Parse(detail.DebtValue.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 19].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 20].Value = detail != null && detail.DebtAbatedValue.HasValue ? decimal.Parse(detail.DebtAbatedValue.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 20].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 21].Value = detail != null && detail.DebtBalance.HasValue ? decimal.Parse(detail.DebtBalance.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 21].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 22].Value = detail?.DebtDetailValue;
                                                worksheet1.Cells[row, 22].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 23].Value = detail != null && detail.Discount.HasValue ? decimal.Parse(detail.Discount.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 23].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 24].Value = detail != null && detail.OriginValue.HasValue ? decimal.Parse(detail.OriginValue.Value.ToString("#0.00")) : 0;
                                                worksheet1.Cells[row, 24].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, 25].Value = detail?.AccountPeriodDescription;
                                                worksheet1.Cells[row, 26].Value = detail?.ServiceName;
                                                worksheet1.Cells[row, 27].Value = detail?.ProducerOrderNo;
                                                worksheet1.Cells[row, 28].Value = detail?.PurchaseCode;
                                                worksheet1.Cells[row, 29].Value = detail?.CreditBillCode;
                                                worksheet1.Cells[row, 30].Value = detail?.ReceiveCode;
                                                worksheet1.Cells[row, 31].Value = detail?.CustomerName;
                                                worksheet1.Cells[row, 32].Value = detail?.OrderNo;
                                                worksheet1.Cells[row, 33].Value = detail?.InvoiceNo;
                                                worksheet1.Cells[row, 34].Value = detail?.InvoiceTime;
                                                worksheet1.Cells[row, 35].Value = detail?.HospitalName;
                                                worksheet1.Cells[row, 36].Value = detail?.LimitedDiscount;

                                                row++;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        worksheet1.Cells[row, 1].Value = item.Code;
                                        worksheet1.Cells[row, 2].Value = item.BusinessDeptFullName;
                                        worksheet1.Cells[row, 3].Value = item.ProbablyPayTime;
                                        worksheet1.Cells[row, 4].Value = item.CompanyName;
                                        worksheet1.Cells[row, 5].Value = item.BillDate.HasValue ? item.BillDate.Value.ToString("yyyy-MM-dd") : "";
                                        worksheet1.Cells[row, 6].Value = item.SumValue.HasValue ? decimal.Parse(item.SumValue.Value.ToString("#0.00")) : 0;
                                        worksheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                                        worksheet1.Cells[row, 7].Value = item.StatusDescription;
                                        worksheet1.Cells[row, 8].Value = item.Remark;
                                        worksheet1.Cells[row, 9].Value = item.CreatedByName;
                                        var bank = agent.BankInfo.FirstOrDefault();
                                        worksheet1.Cells[row, 10].Value = agent.AgentName;
                                        worksheet1.Cells[row, 11].Value = agent.Sum.HasValue ? decimal.Parse(agent.Sum.Value.ToString("#0.00")) : 0;
                                        worksheet1.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                                        var pay = new PaymentSettlementtypeOutput();
                                        if (bank != null && ret.Data != null && ret.Data.Any())
                                        {
                                            pay = ret.Data.Where(x => x.Id == bank.PayClassify).FirstOrDefault();
                                        }
                                        worksheet1.Cells[row, 12].Value = pay != null ? pay.Name : string.Empty;
                                        worksheet1.Cells[row, 13].Value = bank != null ? string.Concat(bank.Bank, "(", bank.BankNo, ")") : string.Empty;
                                        worksheet1.Cells[row, 14].Value = bank != null ? bank.TransferDiscourse : string.Empty;
                                        row++;
                                    }
                                }
                            }
                        }
                        #endregion
                    }
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetExportDetailList")]
        public async Task<IActionResult> GetExportDetailList(List<PaymentExcelInput> list)
        {
            try
            {
                if (list == null || !list.Any())
                {
                    throw new ApplicationException("请选择数据后操作");
                }
                var datas = new List<PaymentExcelOutput>();
                foreach (var item in list)
                {
                    // 循环调用查询，会存在性能问题，但考虑数据量不大，因此忽略
                    var model = item.Adapt<PaymentExcelOutput>();
                    model.Details = await _bulkPaymentQueryService.GetPaymentDetails(new QueryById { Id = model.Id });
                    model.Agents = await _bulkPaymentQueryService.GetPaymentDetailsAggregation(model.Id);
                    datas.Add(model);
                }

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                var stream = new MemoryStream();
                using (var package = new ExcelPackage(stream))
                {
                    if (datas.Any())
                    {
                        var ret = BaseResponseData<List<PaymentSettlementtypeOutput>>.Success();
                        var kindresp = new SettlementtypeInput { pageNo = 1, pageSize = 100 };
                        var kindRet = await _kingdeeApiClient.GetSettlementtype(kindresp);
                        if (kindRet.Code == CodeStatusEnum.Success)
                        {
                            if (kindRet.Data != null && kindRet.Data.rows != null)
                            {
                                var lst = kindRet.Data.rows.Where(p => p.enable.Equals("1")).Select(p => new PaymentSettlementtypeOutput
                                {
                                    Id = p.number,
                                    Name = p.name
                                }).ToList();
                                ret.Data = lst;
                            }
                        }
                        int col = 1;
                        var worksheet1 = package.Workbook.Worksheets.Add("批量付款");
                        #region 批量单据信息
                        worksheet1.Cells[1, col++].Value = "批量付款单号";
                        worksheet1.Cells[1, col++].Value = "应付单号";
                        worksheet1.Cells[1, col++].Value = "采购合同单号";
                        worksheet1.Cells[1, col++].Value = "应付金额";
                        worksheet1.Cells[1, col++].Value = "应付冲销金额";
                        worksheet1.Cells[1, col++].Value = "应付余额";
                        worksheet1.Cells[1, col++].Value = "本次付款金额";
                        worksheet1.Cells[1, col++].Value = "现金折扣金额";
                        worksheet1.Cells[1, col++].Value = "折扣";
                        worksheet1.Cells[1, col++].Value = "折前金额";
                        worksheet1.Cells[1, col++].Value = "账期类型";
                        worksheet1.Cells[1, col++].Value = "业务单元";
                        worksheet1.Cells[1, col++].Value = "厂家单号";
                        worksheet1.Cells[1, col++].Value = "厂家订单号";
                        worksheet1.Cells[1, col++].Value = "采购单号";
                        worksheet1.Cells[1, col++].Value = "供应商";
                        worksheet1.Cells[1, col++].Value = "进项发票号";
                        worksheet1.Cells[1, col++].Value = "进项票金额";
                        worksheet1.Cells[1, col++].Value = "客户";
                        worksheet1.Cells[1, col++].Value = "终端医院";
                        worksheet1.Cells[1, col++].Value = "订单号";
                        worksheet1.Cells[1, col++].Value = "应收单号";
                        worksheet1.Cells[1, col++].Value = "收款单号";

                        worksheet1.Cells[1, col++].Value = "收款日期";
                        worksheet1.Cells[1, col++].Value = "付款单位";
                        worksheet1.Cells[1, col++].Value = "收款金额";
                        worksheet1.Cells[1, col++].Value = "冲销日期";

                        worksheet1.Cells[1, col++].Value = "发票日期";
                        worksheet1.Cells[1, col++].Value = "发票号";
                        worksheet1.Cells[1, col++].Value = "发票金额";
                        worksheet1.Cells[1, col++].Value = "冲销金额";
                        worksheet1.Cells[1, col++].Value = "收款认领时间";
                        worksheet1.Cells[1, col++].Value = "应收单金额";
                        worksheet1.Cells[1, col++].Value = "币种";

                        int row = 2;
                        foreach (var item in datas)
                        {
                            if (!item.Agents.Any() && !item.Details.Any())
                            {
                                col = 1;
                                worksheet1.Cells[row, col++].Value = item.Code;
                                row++;
                            }
                            else if (item.Agents.Any())
                            {
                                foreach (var agent in item.Agents)
                                {
                                    var details = item.Details.Where(x => x.AgentId == agent.AgentId).ToList();
                                    if (details.Any())
                                    {
                                        //供应商对应一条付款信息时不重复显示
                                        if (details.Count == 1)
                                        {
                                            col = 1;
                                            var detail = details.FirstOrDefault();
                                            worksheet1.Cells[row, col++].Value = item.Code;
                                            worksheet1.Cells[row, col++].Value = detail?.DebtBillCode;
                                            worksheet1.Cells[row, col++].Value = detail?.PurchaseContactNo;
                                            worksheet1.Cells[row, col++].Value = detail.DebtValue.HasValue ? decimal.Parse(detail.DebtValue.Value.ToString("#0.00")) : 0;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.DebtAbatedValue.HasValue ? decimal.Parse(detail.DebtAbatedValue.Value.ToString("#0.00")) : 0;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.DebtBalance.HasValue ? decimal.Parse(detail.DebtBalance.Value.ToString("#0.00")) : 0;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.DebtDetailValue;
                                            worksheet1.Cells[row, col++].Value = detail.LimitedDiscount;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.Discount.HasValue ? decimal.Parse(detail.Discount.Value.ToString("#0.00")) : 0;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.OriginValue.HasValue ? decimal.Parse(detail.OriginValue.Value.ToString("#0.00")) : 0;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.AccountPeriodDescription;
                                            worksheet1.Cells[row, col++].Value = detail.ServiceName;
                                            worksheet1.Cells[row, col++].Value = detail.ProducerOrderNo;
                                            worksheet1.Cells[row, col++].Value = detail.ProducerOrderNo;
                                            worksheet1.Cells[row, col++].Value = detail.PurchaseCode;
                                            worksheet1.Cells[row, col++].Value = agent.AgentName;
                                            worksheet1.Cells[row, col++].Value = detail.InvoiceCodes;
                                            worksheet1.Cells[row, col++].Value = detail.NoTaxAmount;
                                            //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                            worksheet1.Cells[row, col++].Value = detail.CustomerName;
                                            worksheet1.Cells[row, col++].Value = detail.HospitalName;
                                            worksheet1.Cells[row, col++].Value = detail.OrderNo;
                                            worksheet1.Cells[row, col++].Value = detail.CreditBillCode;
                                            worksheet1.Cells[row, col++].Value = detail.ReceiveCode;
                                            worksheet1.Cells[row, col++].Value = detail.ReceiveDate.HasValue ? detail.ReceiveDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                                            worksheet1.Cells[row, col++].Value = detail.Payer;
                                            worksheet1.Cells[row, col++].Value = detail.ReceiveAmount;
                                            worksheet1.Cells[row, 34].Value = detail.CoinName;
                                            if (detail.InvoiceInfoLst != null && detail.InvoiceInfoLst.Any())
                                            {
                                                int currentCol = col++;
                                                foreach (var dl in detail.InvoiceInfoLst)
                                                {
                                                    worksheet1.Cells[row, currentCol].Value = dl.Abtdate.HasValue ? dl.Abtdate.Value.ToString("yyyy-MM-dd") : string.Empty;
                                                    worksheet1.Cells[row, currentCol + 1].Value = dl.InvoiceTime.HasValue ? dl.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
                                                    worksheet1.Cells[row, currentCol + 2].Value = dl.InvoiceNo;
                                                    worksheet1.Cells[row, currentCol + 3].Value = dl.InvoiceAmount;
                                                    worksheet1.Cells[row, currentCol + 4].Value = dl.WriteOffAmount;
                                                    worksheet1.Cells[row, currentCol + 5].Value = dl.RecognizeDate;
                                                    if (dl.CreditValueLst != null && dl.CreditValueLst.Any())
                                                    {
                                                        foreach (var c in dl.CreditValueLst)
                                                        {
                                                            worksheet1.Cells[row, currentCol + 6].Value = c;
                                                            worksheet1.Cells[row, currentCol + 7].Value = detail.CoinName;
                                                            row++;
                                                        }
                                                    }
                                                    else
                                                    {
                                                        row++;
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                row++;
                                            }
                                        }
                                        else
                                        {
                                            foreach (var detail in details)
                                            {
                                                col = 1;
                                                worksheet1.Cells[row, col++].Value = item.Code;
                                                worksheet1.Cells[row, col++].Value = detail.DebtBillCode;
                                                worksheet1.Cells[row, col++].Value = detail.PurchaseContactNo;
                                                worksheet1.Cells[row, col++].Value = detail.DebtValue.HasValue ? decimal.Parse(detail.DebtValue.Value.ToString("#0.00")) : 0;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.DebtAbatedValue.HasValue ? decimal.Parse(detail.DebtAbatedValue.Value.ToString("#0.00")) : 0;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.DebtBalance.HasValue ? decimal.Parse(detail.DebtBalance.Value.ToString("#0.00")) : 0;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.DebtDetailValue;
                                                worksheet1.Cells[row, col++].Value = detail.LimitedDiscount;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.Discount.HasValue ? decimal.Parse(detail.Discount.Value.ToString("#0.00")) : 0;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.OriginValue.HasValue ? decimal.Parse(detail.OriginValue.Value.ToString("#0.00")) : 0;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.AccountPeriodDescription;
                                                worksheet1.Cells[row, col++].Value = detail.ServiceName;
                                                worksheet1.Cells[row, col++].Value = detail.ProducerOrderNo;
                                                worksheet1.Cells[row, col++].Value = detail.ProducerOrderNo;
                                                worksheet1.Cells[row, col++].Value = detail.PurchaseCode;
                                                worksheet1.Cells[row, col++].Value = agent.AgentName;
                                                worksheet1.Cells[row, col++].Value = detail.InvoiceCodes;
                                                worksheet1.Cells[row, col++].Value = detail.NoTaxAmount;
                                                //worksheet1.Cells[row, col].Style.Numberformat.Format = "#,##0.00";
                                                worksheet1.Cells[row, col++].Value = detail.CustomerName;
                                                worksheet1.Cells[row, col++].Value = detail.HospitalName;
                                                worksheet1.Cells[row, col++].Value = detail.OrderNo;
                                                worksheet1.Cells[row, col++].Value = detail.CreditBillCode;
                                                worksheet1.Cells[row, col++].Value = detail.ReceiveCode;
                                                worksheet1.Cells[row, col++].Value = detail.ReceiveDate.HasValue ? detail.ReceiveDate.Value.ToString("yyyy-MM-dd") : string.Empty;
                                                worksheet1.Cells[row, col++].Value = detail.Payer;
                                                worksheet1.Cells[row, col++].Value = detail.ReceiveAmount;
                                                worksheet1.Cells[row, 34].Value = detail.CoinName;
                                                if (detail.InvoiceInfoLst != null && detail.InvoiceInfoLst.Any())
                                                {
                                                    int currentCol = col++;
                                                    foreach (var dl in detail.InvoiceInfoLst)
                                                    {
                                                        worksheet1.Cells[row, currentCol].Value = dl.Abtdate.HasValue ? dl.Abtdate.Value.ToString("yyyy-MM-dd") : string.Empty;
                                                        worksheet1.Cells[row, currentCol + 1].Value = dl.InvoiceTime.HasValue ? dl.InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
                                                        worksheet1.Cells[row, currentCol + 2].Value = dl.InvoiceNo;
                                                        worksheet1.Cells[row, currentCol + 3].Value = dl.InvoiceAmount;
                                                        worksheet1.Cells[row, currentCol + 4].Value = dl.WriteOffAmount;
                                                        worksheet1.Cells[row, currentCol + 5].Value = dl.RecognizeDate;
                                                        if (dl.CreditValueLst != null && dl.CreditValueLst.Any())
                                                        {
                                                            foreach (var c in dl.CreditValueLst)
                                                            {
                                                                worksheet1.Cells[row, currentCol + 6].Value = c;
                                                                worksheet1.Cells[row, currentCol + 7].Value = detail.CoinName;
                                                                row++;
                                                            }
                                                        }
                                                        else
                                                        {
                                                            row++;
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    row++;
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        col = 1;
                                        worksheet1.Cells[row, col++].Value = item.Code;
                                        row++;
                                    }
                                }
                            }
                        }
                        #endregion
                    }
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据公司id获取收款单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetReceiveCodesByCompanyId")]
        public async Task<BaseResponseData<List<SelectAssemblyOutput>>> GetReceiveCodesByCompanyId([FromBody] BulkPaymentQueryInput input)
        {
            input.UserId = CurrentUser.Id.Value;
            input.UserName = CurrentUser.UserName;
            var model = await _bulkPaymentQueryService.GetReceiveCodesByCompanyId(input);
            return new BaseResponseData<List<SelectAssemblyOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }
        /// <summary>
        /// 付款信息导入明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExportBatchPaymentDetail")]
        public async Task<BaseResponseData<PaymentExcelDetailOutput>> ExportBatchPaymentDetail([FromBody] PaymentExcelDetailInput input)
        {
            return await _batchPaymentAppService.ExportBatchPaymentDetail(input.FileId, input.PaymentAutoItemId, CurrentUser.UserName);
        }

        /// <summary>
        /// 账期数据导出
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("accountperiod/export")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAccountPeriodTask([FromBody] DebtDetailBulkQuery input)
        {
            input.UserId = CurrentUser.Id;
            input.UserName = CurrentUser.UserName;
            return await _batchPaymentAppService.ExportAccountPeriodTask(input);
        }
        /// <summary>
        /// 批量设置付款方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetAgentPayClassify")]
        public async Task<BaseResponseData<int>> SetAgentPayClassify(List<AgentBankInput> input)
        {
            return await _batchPaymentAppService.SetAgentPayClassifyAsync(input);
        }

        /// <summary>
        /// 批量设置转账附言
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetAgentTransferDiscourse")]
        public async Task<BaseResponseData<int>> SetAgentTransferDiscourse(List<AgentBankInput> input)
        {
            return await _batchPaymentAppService.SetAgentTransferDiscourseAsync(input);
        }

        /// <summary>
        /// 上传现金折扣附件
        /// </summary>
        /// <returns></returns>
        [HttpPost("AddCashDiscountFile")]
        public async Task<BaseResponseData<bool>> AddCashDiscountFileAsync(AddCashDiscountFileInput input)
        {
            return await _batchPaymentAppService.AttachFileIds(input);
        }

        /// <summary>
        /// 设置现金折扣金额
        /// </summary>
        /// <returns></returns>
        [HttpPost("SetDiscountAmount")]
        public async Task<BaseResponseData<bool>> SetDiscountAmountAsync(SetDiscountAmountInput input)
        {
            return await _batchPaymentAppService.SetDiscountAmountAsync(input);
        }

        /// <summary>
        /// 批量设置现金折扣金额
        /// </summary>
        /// <returns></returns>
        [HttpPost("SetBulkDiscountAmount")]
        public async Task<BaseResponseData<bool>> SetBulkDiscountAmountAsync(List<PaymentDetailOutput> input)
        {
            return await _batchPaymentAppService.SetBulkDiscountAmountAsync(input);
        }

        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(AddCashDiscountFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            PaymentAutoAgentBankInfo paymentAutoItem = await _batchPaymentAppService.GetById(input.Id);
            if (!string.IsNullOrEmpty(paymentAutoItem.AttachFileIds))
            {
                var fileIds = paymentAutoItem.AttachFileIds?.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var bizFiles = new List<BizFileUploadOutput>();
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
#if DEBUG
                bizFiles.Add(new BizFileUploadOutput { Id = Guid.Parse(fileIds[0]), Length = 1000, Name = "测试" });
#endif
                ret.Data = bizFiles;

            }
            return ret;
        }

        /// <summary>
        /// 删除供应商附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds")]
        public async Task<BaseResponseData<bool>> DeleteAttachFileIds(AddCashDiscountFileInput input)
        {
            return await _batchPaymentAppService.DeleteAgentAttachFileIds(input);
        }

        /// <summary>
        /// 撤回
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Withdraw")]
        public async Task<BaseResponseData<bool>> WithdrawAsync(WithdrawInput input)
        {
            if (input.Id != Guid.Empty)
            {
                return await _batchPaymentAppService.WithdrawAsync(input.Id, CurrentUser.UserName);
            }

            return BaseResponseData<bool>.Success("操作成功!");
        }
    }
}
