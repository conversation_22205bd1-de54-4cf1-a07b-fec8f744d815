﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class DebtDetailRepository : EfBaseRepository<Guid, DebtDetail, DebtDetailPo>, IDebtDetailRepository
    {
        private readonly FinanceDbContext _db;
        public DebtDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override async Task<int> UpdateAsync(DebtDetail root)
        {
            var isExist = await _db.DebtDetails.AsNoTracking().AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("应付明细不存在！");
            }

            var po = root.Adapt<DebtDetailPo>();

            _db.DebtDetails.Update(po);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }

        public override async Task<int> DeleteAsync(Guid id)
        {
            var isExist = await _db.DebtDetails.AnyAsync(x => x.Id == id);
            if (isExist == false)
            {
                throw new Exception("明细不存在！");
            }
            var po = await GetPoWithIncludeAsync(id);

            if (po.DebtDetailExcutes.Count > 0)
            {
                _db.DebtDetailExcutes.RemoveRange(po.DebtDetailExcutes);
            }

            _db.DebtDetails.Remove(po);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }

        protected override DebtDetailPo CreateDeletingPo(Guid id)
        {
            return new DebtDetailPo { Id = id };
        }

        protected override async Task<DebtDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.DebtDetails.Include(x => x.DebtDetailExcutes).FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<List<DebtDetail>> GetListAsync(List<Guid> lstId)
        {
            var lstPo = await _db.DebtDetails.Include(t => t.Debt)
                                           .Where(x => lstId.Contains(x.Id)).Select(p => new DebtDetail
                                           {
                                               Debt = p.Debt.Adapt<Debt>(),
                                               Value = p.Value,
                                               AccountPeriodType = (int)p.AccountPeriodType,
                                               Code = p.Code,
                                               DebtId = p.DebtId,
                                               Discount = p.Discount,
                                               Id = p.Id,
                                               OriginValue = p.OriginValue,
                                               ProbablyPayTime = p.ProbablyPayTime,
                                               PurchaseCode = p.PurchaseCode,
                                               ReceiveCode = p.ReceiveCode,
                                               Status = p.Status,

                                           }).AsNoTracking().ToListAsync();

            return lstPo;
        }
        public async Task<List<DebtDetail>> GetListForAbtAsync(List<Guid> lstId)
        {
            var lstPo = await _db.DebtDetails.Where(x => lstId.Contains(x.Id)).AsNoTracking().ToListAsync();
            return lstPo.Adapt<List<DebtDetail>>();
        }
        public async Task<List<DebtCodeDetailId>> GetDebtCodeByDetailIdsAsync(List<Guid> detailIds)
        {
            var ret = await (from debt in _db.Debts
                             join debtDetail in _db.DebtDetails
                             on debt.Id equals debtDetail.DebtId
                             where detailIds.Contains(debtDetail.Id)
                             select new DebtCodeDetailId
                             {
                                 DebtCode = debt.BillCode,
                                 DebtDetailId = debtDetail.Id,
                             }).Distinct().ToListAsync();
            return ret;
        }



        public async Task<DebtDetail> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.DebtDetails.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<DebtDetail>();
        }

        public async Task<int> UpdateManyAsync(List<DebtDetail> lstDebtDetail)
        {
            var lstPo = lstDebtDetail.Adapt<List<DebtDetailPo>>();
            lstPo = lstPo.Where(p => p.Value != 0).ToList();
            _db.DebtDetails.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }

        public async Task<int> AddManyAsync(List<DebtDetail> lstDebtDetail)
        {
            var lstPo = lstDebtDetail.Adapt<List<DebtDetailPo>>();
            lstPo = lstPo.Where(p => p.Value != 0).ToList();
            _db.DebtDetails.AddRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();

        }

        public async Task<List<DebtDetailExcute>> GetDebtDetailExcuteBy(string paymentCode)
        {
            var debtDetailExcutePos = await _db.DebtDetailExcutes.Where(p => p.PaymentCode == paymentCode).ToListAsync();
            return debtDetailExcutePos.Adapt<List<DebtDetailExcute>>();
        }

        public async Task<DebtDetail> GetDebtDetail(Guid id)
        {
            var query = _db.DebtDetails.Where(p => p.Id == id).AsNoTracking();
            var res = await query.FirstOrDefaultAsync();
            return res.Adapt<DebtDetail>();
        }
    }
}
