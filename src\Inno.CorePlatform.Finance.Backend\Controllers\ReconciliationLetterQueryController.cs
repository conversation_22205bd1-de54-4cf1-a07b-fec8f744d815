﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 对账函查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationLetterQueryController : BaseController
    {
        public IStoreApiClient _storeApiClient;
        public readonly IReconciliationLetterQueryService _reconciliationLetterQueryService;
        public ReconciliationLetterQueryController(IStoreApiClient storeApiClient, IReconciliationLetterQueryService reconciliationLetterQueryService, ISubLogService subLog) : base(subLog)
        {
            this._storeApiClient = storeApiClient;
            this._reconciliationLetterQueryService = reconciliationLetterQueryService;
        }
        /// <summary>
        /// 获取财务对账函列表数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<ReconciliationLetterListTabOutput>> GetTabCount([FromBody] ReconciliationLetterQueryInput query)
        {
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUser = CurrentUser.UserName;
            return await _reconciliationLetterQueryService.GetTabCount(query);
        }
        /// <summary>
        /// 获取财务对账函列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<ReconciliationLetterListOutput>> GetList([FromBody] ReconciliationLetterQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUser = CurrentUser.UserName;
                var (list, count) = await _reconciliationLetterQueryService.GetListAsync(query);
                return new ResponseData<ReconciliationLetterListOutput>
                {
                    Code = 200,
                    Data = new Data<ReconciliationLetterListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取对账函明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetail")]
        public async Task<ResponseData<ReconciliationLetterDetailQueryOutput>> GetListDetail([FromBody] ReconciliationLetterDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _reconciliationLetterQueryService.GetListDetailAsync(query);
                return new ResponseData<ReconciliationLetterDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<ReconciliationLetterDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取对账函货号明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListProductDetail")]
        public async Task<ResponseData<ReconciliationLetterProductDetailQueryOutput>> GetListProductDetail([FromBody] ReconciliationLetterDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _reconciliationLetterQueryService.GetListProductDetailAsync(query);
                return new ResponseData<ReconciliationLetterProductDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<ReconciliationLetterProductDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取财务对账函明细总和
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetDetailSumCount")]
        public async Task<BaseResponseData<DetailSumCount>> GetDetailSumCount([FromBody] ReconciliationLetterDetailQueryInput input)
        {
            var ret = BaseResponseData<DetailSumCount>.Success("操作成功");
            ret.Data = await _reconciliationLetterQueryService.GetDetailSumCount(input);
            return ret;
        }
    }
}
