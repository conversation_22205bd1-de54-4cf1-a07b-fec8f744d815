﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Inno.CorePlatform.Finance.Application.DTOs.IC
{
    public class CreateInvoiceInput
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string CZLX { get; set; }
        /// <summary>
        /// 医院编码
        /// </summary>
        public string YYBM { get; set; }
        /// <summary>
        /// 配送点编码
        /// </summary>
        public string PSDBM { get; set; }
        /// <summary>
        /// 发票编号
        /// </summary>
        public string FPBH { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string FPDM { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string FPH { get; set; }
        /// <summary>
        /// 校验码
        /// </summary>
        public string? JYM { get; set; }

        /// <summary>
        /// 发票日期
        /// </summary>
        public string FPRQ { get; set; }

        /// <summary>
        /// 发票无税总金额
        /// </summary>
        public decimal FPWSZJE { get; set; }

        /// <summary>
        /// 发票含税总金额
        /// </summary>
        public decimal FPHSZJE { get; set; }

        /// <summary>
        /// 采购类型
        /// </summary>
        public string CGLX { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? FPBZ { get; set; }
        /// <summary>
        /// 记录数
        /// </summary>
        public int JLS { get; set; }
    }
    public class InvoiceDetailInput
    {
        /// <summary>
        /// 配送明细对应的顺序号，同一个配送 单下必须唯一
        /// </summary>
        public string SXH { get; set; }

        /// <summary>
        ///是否无配送发票 0：否；1：是 选择“否”的必须与配送名字一一关 联，选择“是”的则无需与配送明细关联
        /// </summary> 
        public string SFWPSFP { get; set; }

        /// <summary>
        ///无配送发票说明
        /// </summary> 
        public string? WPSFPSM { get; set; }

        /// <summary>
        ///是否冲红 标识是否是由于退货而产生的冲红  发票明细；0：否，1：是
        /// </summary> 
        public string SFCH { get; set; }

        /// <summary>
        ///耗材统编代码
        /// </summary> 
        public string HCTBDM { get; set; }

        /// <summary>
        ///耗材国家规格明细27位码（20位码+7 位规格明细码），若本次采购的耗材  是骨科集采范围内的耗材，则必须填  写27位码
        /// </summary> 
        public string? HCXFDM { get; set; }

        /// <summary>
        ///耗材统编代码在企业本地的编码，如 收产品编码等，多个企业本地代码可 对应到同一耗材统编代码上
        /// </summary> 
        public string? QYBDDM { get; set; }

        /// <summary>
        ///医疗器械具体规格型号的详细说明
        /// </summary> 
        public string? GGXHSM { get; set; }

        /// <summary>
        ///关联明细编号
        /// </summary> 
        public string? GLMXBH { get; set; }

        /// <summary>
        ///销售订单号
        /// </summary> 
        public string? XSDDH { get; set; }

        /// <summary>
        ///生产批号
        /// </summary> 
        public string SCPH { get; set; }

        /// <summary>
        ///生产日期
        /// </summary> 
        public string SCRQ { get; set; }

        /// <summary>
        ///有效日期
        /// </summary> 
        public string YXRQ { get; set; }

        /// <summary>
        ///商品数量
        /// </summary>  
        public decimal SPSL { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>  
        public decimal WSDJ { get; set; }

        /// <summary>
        ///无税单价
        /// </summary>  
        public decimal HSDJ { get; set; }

        /// <summary>
        ///税率
        /// </summary>  
        public decimal SL { get; set; }

        /// <summary>
        ///税额
        /// </summary>  
        public decimal SE { get; set; }

        /// <summary>
        ///含税金额
        /// </summary>  
        public decimal HSJE { get; set; }

        /// <summary>
        ///批发价
        /// </summary>  
        public decimal PFJ { get; set; }

        /// <summary>
        ///零售价
        /// </summary>  
        public decimal LSJ { get; set; }

        /// <summary>
        ///注册证号 食药监批文的注册证号
        /// </summary>  
        public string ZCZH { get; set; }
    }

    public class SunPurchaseDto
    {
        public Guid CompanyId { get; set; }
        public Guid CustomerId { get; set; }
        public CreateInvoiceInput CreateInvoiceInput { get; set; }
        public List<InvoiceDetailInput> InvoiceDetailInput { get; set; }
    }

    /// <summary>
    /// 发票查询入参
    /// </summary>
    public class SearchInvoiceInput
    {
        public Guid CompanyId { get; set; } 
        public Guid CustomerId { get; set; }
        /// <summary>
        /// 起始日期
        /// </summary>
        public string? QSRQ { get; set; }
        /// <summary>
        /// 截止日期
        /// </summary>
        public string? JZRQ { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? FPDM { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string FPH { get; set; }
    }
    /// <summary>
    /// 发票查询出参
    /// </summary>
    public class SearchInvoiceOutput
    {
        /// <summary>
        /// 是否完结 ；‘0’：否，‘1’:是
        /// </summary>
        public string SFWJ { get; set; }

        /// <summary>
        /// 当次最后发票编号
        /// </summary>
        public string DCZHFPBH { get; set; }

        /// <summary>
        /// 记录数
        /// </summary>
        public int JLS { get; set; }
    }

    /// <summary>
    /// 发票查询出参明细
    /// </summary>
    public class SearchInvoiceDetailOutput
    {
        /// <summary>
        /// 发票编号
        /// </summary>
        public string? FPBH { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? FPDM { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? FPH { get; set; }
        /// <summary>
        /// 发票状态 00=待确认,01=已作废,10=未验收,20=已验收,21=已拒收,30=已支付
        /// </summary>
        public string? FPZT { get; set; }
    }
}
