﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CustomizeInvoiceSubDetailRepository : EfBaseRepository<Guid, CustomizeInvoiceSubDetail, CustomizeInvoiceSubDetailPo>, ICustomizeInvoiceSubDetailRepository
    {
        private FinanceDbContext _db;
        public CustomizeInvoiceSubDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> AddManyAsync(List<CustomizeInvoiceSubDetail> lstCiid)
        {
            if (lstCiid != null && lstCiid.Any())
            {

                var lstCiidPo = lstCiid.Adapt<List<CustomizeInvoiceSubDetailPo>>();
                await _db.CustomizeInvoiceSubDetails.AddRangeAsync(lstCiidPo);
                if (UowJoined) return 0;
                return await _db.SaveChangesAsync();
            }
            return 0;
        }
        public override Task<int> UpdateAsync(CustomizeInvoiceSubDetail root)
        {
            throw new NotImplementedException();
        }

        protected override CustomizeInvoiceSubDetailPo CreateDeletingPo(Guid id)
        {
            return new CustomizeInvoiceSubDetailPo { Id = id };
        }

        protected override Task<CustomizeInvoiceSubDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
        public async Task<CustomizeInvoiceSubDetail> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.CustomizeInvoiceDetail.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<CustomizeInvoiceSubDetail>();
        }

        public async Task<int> UpdateManyAsync(List<CustomizeInvoiceSubDetail> lstDetail)
        {
            var lstPo = lstDetail.Adapt<List<CustomizeInvoiceSubDetailPo>>();

            _db.CustomizeInvoiceSubDetails.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }

        public async Task<int> DeleteByDetailIdAsync(Guid detailId)
        {
            var lstPo = await _db.CustomizeInvoiceSubDetails.Where(t => t.CustomizeInvoiceItemId == detailId).ToListAsync();
            _db.CustomizeInvoiceSubDetails.RemoveRange(lstPo);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        public List<CustomizeInvoiceSubDetail> GetByDetailIdsAsync(List<Guid> detailIds)
        {
            //var lstPo = await _db.CustomizeInvoiceSubDetails.Where(t => detailIds.Contains(t.CustomizeInvoiceDetailId)).ToListAsync();
            //return lstPo.Adapt<List<CustomizeInvoiceSubDetail>>();
            throw new NotImplementedException();
        }
    }
}
