﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class RebateProvisionOfSaleInput
    {
        public Guid CompanyId { get; set; }
        public Guid RebateProvisionItemId { get; set; }
    }

    public class RebateProvisionOfSaleOutput
    {
        /// <summary>
        /// 返利类型 A:平移返利, B:指标返利, C:补偿返利	
        /// </summary>
        public string? RebateType { get; set; }

        /// <summary>
        /// 返利期间(摘要)
        /// </summary> 
        public string? PeriodSummary { get; set; }

        /// <summary>
        /// 下家对应金额
        /// </summary> 
        public decimal? NextAmount { get; set; }

        /// <summary>
        /// 下家不含税金额
        /// </summary> 
        public decimal? NextTaxAmount { get; set; }

        /// <summary>
        /// 下家返利方式 A:发票, B:优惠劵
        /// </summary> 
        public string? NextRebateMethod { get; set; }

        /// <summary>
        /// 下家返利发票号/优惠券(针对已结算的) 
        /// </summary> 
        public string? NextInvoiceOrCoupon { get; set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
         
        public Guid ProjectId { get; set; }
         
        public string ProjectName { get; set; } = "";
         
        public string ProjectCode { get; set; } = "";

        /// <summary>
        /// 客户Id
        /// </summary> 
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary> 
        public string? CustomerName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>   
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
    }
}
