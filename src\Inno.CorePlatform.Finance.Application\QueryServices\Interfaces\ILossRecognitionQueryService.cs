﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface ILossRecognitionQueryService
    {
        /// <summary>
        /// 获取损失确认申请tab页数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<LossRecognitionTabCountOutput>> GetTabCount(LossRecognitionQueryInput query);
        /// <summary>
        /// 获取损失确认申请列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<LossRecognitionItemListOutput>, int)> GetListAsync(LossRecognitionQueryInput query);
        /// <summary>
        /// 获取损失确认申请详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<LossRecognitionDetailQueryOutput>, int)> GetListDetailAsync(LossRecognitionDetailQueryInput query);
        /// <summary>
        /// 获取申请单通过id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
       Task<LossRecognitionItemListOutput> GetItemById(Guid? id);
    }
}
