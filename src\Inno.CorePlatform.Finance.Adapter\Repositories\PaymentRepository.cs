﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PaymentRepository : EfBaseRepository<Guid, Payment, PaymentPo>, IPaymentRepository
    {

        private readonly FinanceDbContext _db;
        public PaymentRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> AddManyAsync(List<Payment> lstPayment)
        {
            if (lstPayment.Count <= 0)
            {
                throw new Exception("付款单列表不能为空！");
            }
            await _db.Payments.AddRangeAsync(lstPayment.Adapt<List<PaymentPo>>());
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        public override async Task<int> UpdateAsync(Payment root)
        {
            var isExist = await _db.Payments.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("付款单不存在！");
            }

            var po = root.Adapt<PaymentPo>();

            _db.Payments.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public async Task<List<Payment>> GetPaymentsBy(string purchaseCode, List<PaymentTypeEnum> types)
        {
            return (await _db.Payments.Where(p => p.PurchaseCode == purchaseCode && types.Contains(p.Type)).AsNoTracking().ToListAsync()).Adapt<List<Payment>>();
        }
        public async Task ClearPurchaseCodeOfNonAbate(string purchaseCode)
        {
            var payments = await _db.Payments.Where(p => p.PurchaseCode == purchaseCode && p.AbatedStatus == AbatedStatusEnum.NonAbate).ToListAsync();
            if (payments != null && payments.Any())
            {
                foreach (var payment in payments)
                {
                    payment.PurchaseCode = string.Empty;
                    payment.ProducerOrderNo = string.Empty;
                }
                _db.Payments.UpdateRange(payments);
                if (UowJoined)
                    await _db.SaveChangesAsync();
            }

        }

        protected override PaymentPo CreateDeletingPo(Guid id)
        {
            return new PaymentPo { Id = id };
        }

        protected override async Task<PaymentPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.Payments.FirstOrDefaultAsync(x => x.Id == id);
        }


    }
}
