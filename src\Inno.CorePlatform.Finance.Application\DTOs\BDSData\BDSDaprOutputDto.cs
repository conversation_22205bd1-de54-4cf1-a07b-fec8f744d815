﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    /// <summary>
    /// 基础数据服务的dapr接口返回数据模型
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BDSDaprOutputDto<T>
    {
        /// <summary>
        /// 
        /// </summary>
        public int Code { get; set; }


        public T Data { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Message { get; set; }
        public bool Success { get; set; }
    }
}
