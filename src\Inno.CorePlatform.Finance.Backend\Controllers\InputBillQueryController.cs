﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using NPOI.OpenXmlFormats;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 进项发票查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InputBillQueryController : BaseController
    {
        private readonly IInputBillQueryService _inputBillQueryService;
        private readonly ILogger<InputBillQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IDebtQueryService _debtQueryService;
        /// <summary>
        /// 发票查询
        /// </summary>
        public InputBillQueryController(
            IInputBillQueryService inputBillQueryService,
            IKingdeeApiClient kingdeeApiClient,
            IPCApiClient pCApiClient,
            IDebtQueryService debtQueryService,
            ILogger<InputBillQueryController> logger, ISubLogService subLog) : base(subLog)
        {
            _kingdeeApiClient = kingdeeApiClient;
            _inputBillQueryService = inputBillQueryService;
            _logger = logger;
            _pCApiClient = pCApiClient;
            _debtQueryService = debtQueryService;
        }

        /// <summary>
        /// 获取发票抬头
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<InputBillQueryListOut>> GetList([FromBody] InputBillQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id;
                query.UserName = CurrentUser.UserName;
                var (list, count) = await _inputBillQueryService.GetListInputBillAsync(query);
                return new ResponseData<InputBillQueryListOut>
                {
                    Code = 200,
                    Data = new Data<InputBillQueryListOut>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 导出进项发票数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ExportBill")]
        public async Task<IActionResult> ExportBill([FromBody] InputBillQueryInput query)
        {
            query.page = 1;
            query.limit = int.MaxValue;
            query.UserId = CurrentUser.Id;
            query.UserName = CurrentUser.UserName;
            var stream = await _inputBillQueryService.ExportBill(query);
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        /// <summary>
        /// 获取发票详情-金蝶
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDetailJdList")]
        public async Task<ResponseData<InputBillDeatilJDQueryListOut>> GetDetailJdList([FromBody] InputBillQueryInputDeatil query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _inputBillQueryService.GetListInputBillDetailJdAsync(query);
                return new ResponseData<InputBillDeatilJDQueryListOut>
                {
                    Code = 200,
                    Data = new Data<InputBillDeatilJDQueryListOut>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取发票详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDetailList")]
        public async Task<ResponseData<InputBillDeatilQueryListOut>> GetDetailList([FromBody] InputBillQueryInputDeatil query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _inputBillQueryService.GetListInputBillDetailAsync(query);
                return new ResponseData<InputBillDeatilQueryListOut>
                {
                    Code = 200,
                    Data = new Data<InputBillDeatilQueryListOut>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取进项票金蝶明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInputBillDetailSumJd")]
        public async Task<BaseResponseData<InputBillQuerySumOutput>> GetInputBillDetailSumJd([FromBody] InputBillQueryInputDeatil query)
        {
            var ret = BaseResponseData<InputBillQuerySumOutput>.Success("操作成功");
            try
            {
                var uid = CurrentUser.Id;
                var ouput = await _inputBillQueryService.GetInputBillDetailSumJdAsync(query);
                ret.Data = ouput;
            }
            catch (Exception ex)
            {
                ret= BaseResponseData<InputBillQuerySumOutput>.Failed(500,ex.Message);
                throw;
            }
            return ret;
        }
        /// <summary>
        /// 获取进项票明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInputBillDetailSum")]
        public async Task<BaseResponseData<InputBillQuerySumOutput>> GetInputBillDetailSum([FromBody] InputBillQueryInputDeatil query)
        {
            var ret = BaseResponseData<InputBillQuerySumOutput>.Success("操作成功");
            try
            {
                var uid = CurrentUser.Id;
                var ouput = await _inputBillQueryService.GetInputBillDetailSumAsync(query);
                ret.Data = ouput;
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<InputBillQuerySumOutput>.Failed(500, ex.Message);
                throw;
            }
            return ret;
        }
        /// <summary>
        /// 获取入库单到票明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetStoreInItemCodeReceivedList")]
        public async Task<ResponseData<LotInfo>> GetStoreInItemCodeReceivedList([FromBody] InputBillQueryInputDeatil query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _inputBillQueryService.GetStoreInItemCodeReceivedList(query);
                return new ResponseData<LotInfo>
                {
                    Code = 200,
                    Data = new Data<LotInfo>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取购货入库单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetListStoreInByCompanyAsync")]
        public async Task<ResponseData<StoreInDetailQueryOutput>> GetListStoreInByCompanyAsync([FromBody] StoreInDetailQueryInput input)
        {
            try
            {
                var strategyInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
                var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
                if (strategry != null && strategry.RowStrategies.Count > 0)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        return new ResponseData<StoreInDetailQueryOutput>
                        {
                            Data = new Data<StoreInDetailQueryOutput>()
                        };
                    }
                }

                var uid = CurrentUser.Id;
                input.UserId = uid;
                var (list, count) = (new List<StoreInDetailQueryOutput>(), 0);
                if (input.IsStoreIn == true && input.Classify == 1)
                {
                    (list, count) = await _inputBillQueryService.GetListStoreInByCompanyAsync(input);
                }
                else if (input.IsStoreIn == false && input.Classify == 2)
                {
                    input.storeOutCode = input.storeInCode;
                    (list, count) = await _inputBillQueryService.GetListStoreOutByCompanyAsync(input);
                }
                else if (input.Classify == 3) //寄售转购货 不需要判断IsStoreIn，只需校验Classify=3
                {
                    input.storeOutCode = input.storeInCode; 
                    (list, count) = await _inputBillQueryService.GetConsignToPurchaseDetailGroup(input);
                }
                else if (input.Classify == 4)//购货修订 不需要判断IsStoreIn，只需校验Classify=4
                {
                    input.storeOutCode = input.storeInCode;
                    (list, count) = await _inputBillQueryService.GetPurchaseReviseGroup(input);
                }
                else if (input.Classify == 5)//服务费应付
                {
                    input.storeOutCode = input.storeInCode;
                    (list, count) = await _inputBillQueryService.GetServerDebts(input);
                }
                else if (input.Classify == 6)//换货转退货
                {
                    input.billCode = input.storeInCode;
                    (list, count) = await _inputBillQueryService.GetExchangeToReturn(input);
                }
                //改为异步查询，优化查询效率
                //if (input.Classify == 5)
                //{
                //    list = await _inputBillQueryService.GetUseDebtInvoiceAmount(list, input.InputBillId);
                //}
                //else
                //{

                //    list = await _inputBillQueryService.GetUseQuantity(list, input.InputBillId);
                //}

                // 查询采购合同单号
                if (list != null && list.Any())
                {
                    var purchaseCodes = list.Select(x => x.PurchaseCode).ToList();
                    var debts = await _debtQueryService.GetDebtByPurchaseCode(purchaseCodes);
                    var storeInItemCodes = list.Select(x => x.StoreInItemCode).ToList();
                    debts.AddRange(await _debtQueryService.GetDebtByStoreInItemCode(storeInItemCodes));
                    foreach (var item in list)
                    {
                        var single = debts.FirstOrDefault(x => x.PurchaseCode == item.PurchaseCode && !string.IsNullOrEmpty(x.PurchaseContactNo));
                        if (single == null)
                        {
                            // 根据入库单号/应付单号查询
                            single = debts.FirstOrDefault(x => x.BillCode == item.StoreInItemCode && !string.IsNullOrEmpty(x.PurchaseContactNo)); 
                        }
                        item.PurchaseContactNo = single != null ? single.PurchaseContactNo : string.Empty;
                        //本次入票数量/金额，我理解应该是等于数量-锁定数量-已入票数量
                        //if (input.IsStoreIn == true && input.Classify != 4)
                        //{
                        //    item.ThisQuantity = item.PolyQuantity - item.ShowUseQuantity - item.PolyInvoiceQuantity;
                        //}
                        //else if (input.Classify == 4)
                        //{
                        //    item.ThisQuantity = item.PolyQuantity >= 0 ? (item.PolyQuantity - item.ShowUseQuantity - item.PolyInvoiceQuantity) : (item.PolyQuantity - Math.Abs(item.ShowUseQuantity) + item.PolyInvoiceQuantity);
                        //}
                    }
                }

                return new ResponseData<StoreInDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<StoreInDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("获取购货入库单错误" + ex);
                throw;
            }
        }

        /// <summary>
        /// 获取showUseQuantity
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getShowUseQuantity")]
        public async Task<BaseResponseData<List<StoreInDetailQueryOutput>>> getShowUseQuantity(ShowUseQuantityQueryInput input)
        {
            //改为异步查询，优化查询效率
            var ret = BaseResponseData<List<StoreInDetailQueryOutput>>.Success("获取成功");
            var list = new List<StoreInDetailQueryOutput>();
            if (input.Classify == 5)
            {
                list = await _inputBillQueryService.GetUseDebtInvoiceAmount(input.List, input.InputBillId);
            }
            else
            {
                list = await _inputBillQueryService.GetUseQuantity(input.List, input.InputBillId);
            }
            ret.Data = list;
            return ret;
        }

        /// <summary>
        /// 获取发票详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetputBillListByStoreIncode")]
        public async Task<ResponseData<InputBillDeatilQueryListOut>> GetputBillListByStoreIncode([FromBody] InputBillbyStoreIncode query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _inputBillQueryService.GetputBillListByStoreIncode(query);
                return new ResponseData<InputBillDeatilQueryListOut>
                {
                    Code = 200,
                    Data = new Data<InputBillDeatilQueryListOut>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError("获取获取发票详情错误" + ex);
                throw;
            }
        }

        [HttpGet("GetKDFilePath")]
        public async Task<BaseResponseData<List<QueryInvoiceAttachmentOutput>>> GetKDFilePath(string invoiceNo, string? invoiceCode)
        {
            var ret = await _kingdeeApiClient.QueryInvoiceAttachment(new QueryInvoiceAttachmentInput
            {
                invoiceNo = invoiceNo,
                invoiceCode = invoiceCode ?? "",
                invoiceType = "JX"
            });
            return ret;
        }
        /// <summary>
        /// 获取发票抬头
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListInputBillByDebtCode")]
        public async Task<ResponseData<InputBillQueryListOut>> GetListInputBillByDebtCode([FromBody] InputBillQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id;
                query.UserName = CurrentUser.UserName;
                query.limit = int.MaxValue;
                var (list, count) = await _inputBillQueryService.GetListInputBillByDebtCodeAsync(query);
                return new ResponseData<InputBillQueryListOut>
                {
                    Code = 200,
                    Data = new Data<InputBillQueryListOut>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}

