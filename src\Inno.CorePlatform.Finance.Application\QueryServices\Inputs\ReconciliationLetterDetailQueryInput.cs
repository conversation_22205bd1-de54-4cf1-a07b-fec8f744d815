﻿using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 对账函详情查询，入参
    /// </summary>
    public class ReconciliationLetterDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 对账函Id
        /// </summary>
        public Guid ReconciliationLetterItemId { set; get; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public ReconciliationLetterEnum Classify { get; set; }

    }
}
