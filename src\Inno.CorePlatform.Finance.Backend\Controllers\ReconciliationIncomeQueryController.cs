﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationIncomeQueryController : BaseController
    {
        public IStoreApiClient _storeApiClient;
        public readonly IReconciliationIncomeQueryService _reconciliationIncomeQueryService;
        public ReconciliationIncomeQueryController(IStoreApiClient storeApiClient, IReconciliationIncomeQueryService reconciliationIncomeQueryService, ISubLogService subLog) : base(subLog)
        {
            this._storeApiClient = storeApiClient;
            this._reconciliationIncomeQueryService = reconciliationIncomeQueryService;
        }
        [HttpPost("GetList")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListPages([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationIncomeQueryService.GetListPages(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        [HttpPost("GetListByAgent")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListByAgent([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationIncomeQueryService.GetListByAgent(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        [HttpPost("GetListByCustomer")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListByCustomer([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationIncomeQueryService.GetListByCustomer(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
    }
}
