﻿using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply
{
    public class StoreOutApplyOutput
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = "none";

        /// <summary>
        /// 最后更新人
        /// </summary>
        public string? UpdatedBy { get; set; }


        /// <summary>
        /// 项目Id
        /// </summary>
        public Project? Project { get; set; }
        /// <summary>
        /// 事业部
        /// </summary>
        public BusinessDept? BusinessDept { get; set; }
        public string BusinessDeptName
        {
            get
            {
                if (this.BusinessDept != null && BusinessDept.OrignValue != null && BusinessDept.OrignValue.Count() > 0)
                {

                    var levels = new List<string>();
                    for (int i = 0; i < BusinessDept.OrignValue.Count(); i++)
                    {
                        var attr = BusinessDept.OrignValue[i].Split('_', StringSplitOptions.RemoveEmptyEntries);
                        if (attr.Length > 1)
                        {
                            levels.Add(attr[1]);
                        }
                    }
                    return string.Join("/", levels).TrimEnd('/');
                }
                return string.Empty;
            }

        }
        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }


        /// <summary>
        /// 公司
        /// </summary>
        public Company Company { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public Service? Service { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public Agent Agent { get; set; }
        /// <summary>
        /// 审核者
        /// </summary>
        public Operator? Auditor { get; set; }


        /// <summary>
        /// 换货类型
        /// </summary>
        public bool IsNormal { get; set; } = true;
        public string IsNormalName
        {
            get
            {
                return IsNormal ? "合格" : "不合格";
            }
        }

        /// <summary>
        /// 注册人/备案人
        /// </summary>
        public Producer? Producer { get; internal set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; }
    }


    /// <summary>
    /// 出库签收
    /// </summary>
    public class StoreOutSignDTO 
    {
        public string storeOutCode { get; set; }
        /// <summary>
        /// 确认收入时间
        /// </summary>
        public long sureIncomeDate {  get; set; }
    }

    /// <summary>
    /// 出库申请列表
    /// </summary>
    public class StoreOutApplyListDto
    {
        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 列表
        /// </summary>
        public List<StoreOutApplyListOutput> List { get; set; }
    }

    /// <summary>
    /// 出库申请列表
    /// </summary>
    public class StoreOutApplyListOutput
    {
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }
    }

}
