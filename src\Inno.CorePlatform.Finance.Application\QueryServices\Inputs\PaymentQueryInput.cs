﻿using Inno.CorePlatform.Finance.Domain;
using NPOI.SS.Formula.Functions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 付款单查询输入类
    /// </summary>
    public class PaymentQueryInput : BaseQuery
    {
        public Guid UserId { get; set; }
        /// <summary>
        /// 单据日期开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单据日期结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 实际付款日期开始
        /// </summary>
        public long? PaymentDateS { get; set; }
        /// <summary>
        /// 实际付款日期 开始
        /// </summary>
        public DateTime? PaymentDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return PaymentDateS != null ? new DateTime(tricks_1970 + long.Parse(PaymentDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 实际付款日期结束
        /// </summary>
        public long? PaymentDateE { get; set; }
        /// <summary>
        /// 实际付款日期 结束
        /// </summary>
        public DateTime? PaymentDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return PaymentDateE != null ? new DateTime(tricks_1970 + long.Parse(PaymentDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 付款单位（公司）
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }
        /// <summary>
        /// 收款单位（供应商）
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 付款单类型
        /// </summary>
        public PaymentTypeEnum? Type { get; set; }
        /// <summary>
        /// 付款单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 采购单号集合
        /// </summary>
        public List<string>? PurchaseCodes { get; set; }

        /// <summary>
        /// 厂家单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 1=已支付 反之未支付
        /// </summary>
        public int? Paied { get; set; }
        public string? department { get; set; }
        public string? projectName { get; set; }
        /// <summary>
        /// 批量付款单号
        /// </summary>
        public string? PaymentAutoItemCode { get; set; }
         /// <summary>
         /// 采购合同单号
         /// </summary>
        public string? PurchaseContactNo {  get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? CurrentUserName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedByName { get; set; }

        public Guid? ProjectId { get; set; }
    }

    public class PaymentQueryInputApi
    {
        public Guid AgentId { get; set; }
        public string CoinCode { get; set; }

        public Guid projectId { get; set; }
        public Guid companyId { get; set; }
    }
}
