﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using Newtonsoft.Json;
using Org.BouncyCastle.Bcpg.OpenPgp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using JsonIgnoreAttribute = Newtonsoft.Json.JsonIgnoreAttribute;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    #region 授权相dto类
    public class KingdeeSetting
    {
        public string AppId { get; set; }
        public string AppSecret { get; set; }
        public string Tenantid { get; set; }
        public string AccountId { get; set; }
        public string Language { get; set; } = "zh_CN";
        public string Usertype { get; set; } = "Mobile";
        public string User { get; set; }
        public string Host { get; set; }
    }

    public class KingdeeAuthResultDTO<T> where T : class
    {
        public T data { get; set; }
        public string state { get; set; }
        public bool status { get; set; }
        public Adfwebactivityresponseheaders ADFWebActivityResponseHeaders { get; set; }
        public string effectiveIntegrationRuntime { get; set; }
        public int executionDuration { get; set; }
        public Durationinqueue durationInQueue { get; set; }
        public Billingreference billingReference { get; set; }
    }

    public class AccesstokenData
    {
        public string access_token { get; set; }
        public bool success { get; set; }
        public string error_desc { get; set; }
        public long expire_time { get; set; }
        public string error_code { get; set; }
    }

    public class ApptokenData
    {
        public string app_token { get; set; }
        public bool success { get; set; }
        public string error_desc { get; set; }
        public long expire_time { get; set; }
        public string error_code { get; set; }
    }

    public class Adfwebactivityresponseheaders
    {
        public string TransferEncoding { get; set; }
        public string Connection { get; set; }
        public string traceId { get; set; }
        public string AccessControlAllowOrigin { get; set; }
        public string AccessControlAllowCredentials { get; set; }
        public string Date { get; set; }
        public string Server { get; set; }
        public string ContentType { get; set; }
    }

    public class Durationinqueue
    {
        public int integrationRuntimeQueue { get; set; }
    }

    public class Billingreference
    {
        public string activityType { get; set; }
        public Billableduration[] billableDuration { get; set; }
    }

    public class Billableduration
    {
        public string meterType { get; set; }
        public float duration { get; set; }
        public string unit { get; set; }
    }

    #endregion

    public class KingdeeBusinessResult<T> where T : class
    {
        public T data { get; set; }
        public string errorCode { get; set; }
        public string? message { get; set; }
        public bool status { get; set; }
    }

    public class KingdeeCredit
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public string billtype_number { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string org_number { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime bizdate { get; set; }

        public string asstacttype { get { return "bd_customer"; } }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid asstact_number1 { get; set; }
        public string asstact_number { get { return asstact_number1.ToString().ToUpper(); } }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string recorg_number { get { return org_number; } }
        public string paymode { get { return "CREDIT"; } }
        public string payproperty_number { get { return "1001"; } }
        public string currency_number { get { return "CNY"; } }
        public string exratetable_number { get { return "ERT-01"; } }
        public decimal exchangerate { get { return 1; } }
        public string department_number { get { return recorg_number; } }

        public string salesorg_number { get { return recorg_number; } }
        public bool jfzx_iscofirm { get; set; } = false;
        /// <summary>
        /// 核算部门id
        /// </summary>
        public string jfzx_businessnumber { get; set; }

        /// <summary>
        /// 关联单号(订单号)
        /// </summary>
        public string jfzx_ordernumber { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public string jfzx_serviceid { get; set; }
        public string jfzx_creator { get; set; }

        /// <summary>
        /// 确认收入方式,A-一次性,B-分批
        /// </summary>
        public string confirmManner { get; set; }

        /// <summary>
        /// 明细
        /// </summary>
        public List<KingdeeCreditDetail> billEntryModels { get; set; }

        /// <summary>
        /// 应收不含税总额
        /// </summary>
        public decimal amount { get; set; }

        /// <summary>
        /// 应收含税总额
        /// </summary>
        public decimal recamount { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal jfzx_alltotalcost { get; set; }
        /// <summary>
        /// 是否返利
        /// </summary>
        public bool? jfzx_rebate { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string associatedNumber { get; set; }
        /// <summary>
        /// 新核算部门
        /// </summary>
        public string newBusinessNumber { get; set; }
        /// <summary>
        /// 调整单标识,A-源单,B-冲销单,C-新单
        /// </summary>
        public string adjustmentFlag { get; set; }

    }

    public class KingdeeCreditDetail
    {
        /// <summary>
        /// 物料编码编码
        /// </summary>
        public string? e_material_number1 { get; set; }
        public string e_material_number
        {
            get
            {
                if (!string.IsNullOrEmpty(e_material_number1))
                {

                    return e_material_number1.ToUpper();
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 计量单位
        /// </summary>
        public string e_measureunit_number { get { return "pcs"; } }
        /// <summary>
        /// 数量
        /// </summary>
        public int e_quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal e_unitprice { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal e_taxunitprice { get; set; }

        /// <summary>
        /// 明细总成本
        /// </summary>
        public decimal? jfzx_totalcostMany { get; set; }


        /// <summary>
        /// 明细总成本
        /// </summary>
        public decimal? jfzx_totalcost
        {
            get
            {
                if (jfzx_totalcostMany.HasValue)
                {
                    return Math.Round(jfzx_totalcostMany.Value, 2);
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 折扣方式
        /// </summary>
        public string e_discountmode { get { return "NULL"; } }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal salestaxrate { get; set; }
        public string taxrateid_number
        {
            get
            {
                if (salestaxrate > 0)
                {

                    return $"V{salestaxrate}".Trim('0').Trim('.');
                }
                else
                {
                    return "V0";
                }
            }
        }
        public string jfzx_projectnumber { get; set; }


        /// <summary>
        /// 供应商
        /// </summary>
        public string jfzx_supplier { get; set; }

        /// <summary>
        /// 单位成本(不含税)
        /// </summary>
        public decimal jfzx_unitcost { get; set; }

        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal jfzx_standardtotal { get; set; }
        /// <summary>
        /// 出库类型 A:销售出库，B:暂存核销出库
        /// </summary>
        public string? jfzx_outbound_type { get; set; }



        /// <summary>
        /// 返利类型
        /// </summary>
        public int? jfzx_rebateType { get; set; }

        /// <summary>
        /// 费用项目
        /// </summary>
        public string? e_expenseitem_number { get; set; }
    }

    public class KingdeeCreditAndDebtCallResult
    {
        public List<object> result { get; set; }
        public string failCount { get; set; }
        public string successCount { get; set; }
    }

    public class KingdeeDebt
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 是否冲销应收
        /// </summary>
        public bool hedgeReceivable { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public string billtypeid_number { get; set; }
        /// <summary>
        /// 公司编码
        /// </summary>
        public string org_number { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime bizdate { get; set; }

        /// <summary>
        /// 汇率日期
        /// </summary>
        public DateTime exratedate
        {
            get
            {
                return bizdate;
            }
        }
        /// <summary>
        /// 应付金额4位小数
        /// </summary>
        public decimal pricetaxtotal4 { get; set; }
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal pricetaxtotal
        {
            get
            {
                return billEntryModels.Sum(p => p.pricetax * p.quantity) >= 0 ? Math.Abs(Math.Round(pricetaxtotal4, 2)) : -Math.Abs(Math.Round(pricetaxtotal4, 2));
            }
        }
        /// <summary>
        /// 往来类型
        /// </summary>
        public string asstacttype { get { return "bd_supplier"; } }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid asstact_number1 { get; set; }
        public string asstact_number { get { return asstact_number1.ToString().ToUpper(); } }

        public string purmode { get { return "CREDIT"; } }
        public string currency_number { get; set; }
        /// <summary>
        /// 汇率
        /// </summary>
        public decimal exchangerate { get; set; } = 1;
        public string exratetable_number { get { return "ERT-01"; } }
        /// <summary>
        /// 付款组织编码
        /// </summary>
        public string payorg_number { get; set; }
        /// <summary>
        /// 款项性质编码
        /// </summary>
        public string payproperty_number { get { return "2001"; } }
        /// <summary>
        /// 业务组织
        /// </summary>
        public string jfzx_business_number { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string jfzx_order_number { get; set; }
        public List<KingdeeDebtDetail> billEntryModels { get; set; }

        public List<AttachmentsModel> attachmentsModel { get; set; } = new List<AttachmentsModel>();
        public string jfzx_creator { get; set; }

        public string remark { get; set; }

        public bool jfzx_rebate { get; set; } = false;
        /// <summary>
        /// 是否暂存转采购
        /// </summary>
        public bool jfzx_staging_purchase { get; set; } = false;

        /// <summary>
        /// 应付不含税总额
        /// </summary>
        public decimal amount2 { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal amount
        {
            get
            {
                return Math.Round(amount2, 2);
            }
        }


        /// <summary>
        /// 购货修订明细
        /// </summary>
        public List<ApFinApPurchaseOrderAmendmentModel> purchaseOrderAmendmentModels { get; set; }
    }

    public class AttachmentsModel
    {
        public string fileName { get; set; }
        public string attachmentUrl { get; set; }
    }

    public class KingdeeDebtDetail
    {
        public string material_number1 { get; set; }
        public string material_number { get { return material_number1.ToUpper(); } }
        public string measureunit_number { get { return "pcs"; } }
        public decimal quantity { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal pricetax { get; set; }
        public string discountmode { get { return "NULL"; } }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxrate { get; set; }
        /// <summary>
        /// 金额（本位币）
        /// </summary>
        public decimal e_amountbaseMany { get; set; }

        public decimal e_amountbase
        {
            get
            {
                return Math.Round(e_amountbaseMany, 2);
            }
        }
        public string taxrateid_number
        {
            get
            {
                if (taxrate > 0)
                {

                    return $"V{taxrate}".Trim('0').Trim('.');
                }
                else
                {
                    return "V0";
                }
            }
        }

        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_project_number { get; set; }

        /// <summary>
        /// 修订类型 (stock:存货, soldout:已售)
        /// </summary>
        public string jfzx_revisiontype { get; set; }

        /// <summary>
        /// 关税
        /// </summary>
        public decimal jfzx_tariff { get; set; } = 0;

        /// <summary>
        /// 货品类型(设备:A,物资:B,服务:C)
        /// </summary>
        public string jfzx_goodstype { get; set; } = "";

        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal jfzx_standard_cost { get; set; } = 0;

        /// <summary>
        /// 应付不含税单价
        /// </summary>
        public decimal price2 { get; set; }


        /// <summary>
        /// 应付不含税单价
        /// </summary>
        public decimal price
        {
            get
            {
                return Math.Round(price2, 20);
            }
        }

        /// <summary>
        /// 返利类型
        /// </summary>
        public int? jfzx_rebateType { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? jfzx_rebatecustomerid { get; set; }

        /// <summary>
        /// 费用项目
        /// </summary>
        public string? expenseitem_number { get; set; }
    }


    public class KingdeePayApplyDto
    {
        /// <summary>
        /// 申请人，传单据上的操作人账号即可
        /// </summary>
        public string applyPeopleNumber { get; set; }
        /// <summary>
        /// 批量付款单号，预付不需要传
        /// </summary>
        public string payNumber { get; set; }
        /// <summary>
        /// A:批量付款   ------>201
        /// B:预付款       ------>202
        /// C:投标          ------>219
        /// D:履约          ------>220
        /// E:押金           ------>211
        /// F:进口关税   ------> 231G:进口增值税------>232
        /// </summary>
        public string payType { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// 预付批次
        /// </summary>
        public string? relateid { get; set; }

        /// <summary>
        /// OA请求ID，用于金蝶打印时获取审批流程信息
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// 供应商明细
        /// </summary>
        public List<KingdeePayApplyDetail> applyDetail { get; set; }
        /// <summary>
        /// 付款公司
        /// </summary>
        public string payOrgNumber { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string businessOrg { get; set; }

        #region 进口业务
        /// <summary>
        /// 发票号
        /// </summary>
        public string? jfzx_invoiceno { get; set; } = "";

        /// <summary>
        /// 合同号
        /// </summary>
        public string? jfzx_contractno { get; set; } = "";

        /// <summary>
        /// 境外/跨境支付0:否,1:是
        /// </summary>
        public string? jfzx_paymentabroad { get; set; } = "";

        /// <summary>
        /// 交易编码
        /// </summary>
        public string? jfzx_transactioncoding { get; set; } = "";

        /// <summary>
        /// 交易附言
        /// </summary>
        public string? jfzx_postscript { get; set; } = "";

        /// <summary>
        /// 本笔款项是否为保税货物下推0:否,1:是
        /// </summary>
        public string? jfzx_ynpush { get; set; } = "";

        /// <summary>
        /// 国内外费用承担方A:汇款人,B:收款人,C:共同人	
        /// </summary>
        public string? costBearingParty { get; set; } = "";

        /// <summary>
        /// 海关进口货物报关商品
        /// </summary>
        public string? importGoods { get; set; } = "";

        #endregion

        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentsModel>? attachmentsModel { get; set; } = new List<AttachmentsModel>();
    }

    public class KingdeePayApplyDetail
    {
        /// <summary>
        /// 供应商，
        /// </summary>
        public string supplierNumber { get { return AgentId.ToString().ToUpper(); } }
        /// <summary>
        /// 供应商Id
        /// </summary> 
        public Guid AgentId { get; set; }
        /// <summary>
        /// 供应商单据明细
        /// </summary>
        public List<ApplyDetailData> applyDetailData { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string bankAccount { get; set; }
        /// <summary>
        /// 账号名称
        /// </summary>
        public string bankName { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string moneyNumber { get; set; }
        /// <summary>
        /// 开户行名称
        /// </summary>
        public string bankBranchName { get; set; }
        /// <summary>
        /// 开户行编码
        /// </summary>
        public string bankBranchNumber { get; set; } = "";

        /// <summary>
        /// 转账附言
        /// </summary>
        public string? transferDiscourse { get; set; }
    }

    public class ApplyDetailData
    {
        /// <summary>
        /// 明细Id，应付付款计划的Id
        /// </summary>
        public string detailNumber { get; set; }
        /// <summary>
        /// 应付计划的本次付款金额
        /// </summary>
        public decimal gaterAmount { get; set; }

        /// <summary>
        /// 结算方式编码  
        /// JSFS02 现金支票
        /// JSFS03 转账支票
        /// JSFS04 电汇
        /// JSFS05 信汇
        /// JSFS06 商业承兑汇票
        /// JSFS07 银行承兑汇票
        /// JSFS08 信用证
        /// JSFS09 应收票据背书
        /// JSFS10 内部利息结算
        /// JSFS11 集中结算
        /// JSFS12 票据退票
        /// JSFS13 银企支付
        /// JSFS-YXFY-01	营销费用账户 
        /// </summary>
        public string? settlementModel { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string? projectNumber { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string? orderNumber { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? receivableNumber { get; set; }

        /// <summary>
        /// 到单号
        /// </summary>
        public string? arrivalNumber { get; set; }

        /// <summary>
        /// 现金折扣
        /// </summary>
        public decimal? cashDiscount { get; set; }
    }

    public class KingdeeIncomeIsConfirm
    {
        /// <summary>
        /// 数据集合
        /// </summary>
        public List<IncomeIsConfirmData> data { get; set; }
        /// <summary>
        /// 收入确认
        /// </summary>
        public bool? jfzx_iscofirm { get; set; }
    }
    public class IncomeIsConfirmData
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string billno { set; get; }

        /// <summary>
        /// 记账日期、确认日期
        /// </summary>
        public string date { get; set; }
    }
    public class RecognizeReceiveInput : BaseQuery
    {
        /// <summary>
        /// 客户ids
        /// </summary>
        public List<QueryCasRecbillCustomerModel>? casRecbillCustomer { get; set; } = new List<QueryCasRecbillCustomerModel>() { };
        /// <summary>
        /// 客户id (无需传给金蝶仅仅查询用)
        /// </summary>
        public List<string>? customers { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public string? customer { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public List<string>? company { get; set; } = new List<string>();

        public int? opt { get; set; }
        public List<string>? type { get; set; }
        public string? classify { get; set; }

        public string? staratDate
        {
            get
            {
                if (staratDateStamp.HasValue)
                {
                    return DateTimeHelper.GetDateTime(staratDateStamp.Value).AddHours(8).ToString("yyyy-MM-dd");
                }
                else
                {
                    return "";
                }
            }
        }

        public string? endDate
        {
            get
            {
                if (endDateStamp.HasValue)
                {
                    return DateTimeHelper.GetDateTime(endDateStamp.Value).AddHours(8).ToString("yyyy-MM-dd");
                }
                else
                {
                    return "";
                }
            }
        }

        public long? staratDateStamp { get; set; }

        public long? endDateStamp { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? code { get; set; }
        /// <summary>
        /// 是否显示负数应收
        /// </summary>
        public bool? displayNear
        {
            get
            {
                if (displayNearInt.HasValue)
                {
                    return displayNearInt == 1;
                }
                return null;
            }
        }

        /// <summary>
        /// 是否显示负数应收
        /// </summary>
        public int? displayNearInt { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string? projectNo { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? customerName { get; set; }

        /// <summary>
        /// 客商名称(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? payerType { get; set; }

        /// <summary>
        /// 收款人银行类型
        /// </summary>
        public string? payeeBankName { get; set; }

        /// <summary>
        /// 收款人银行账户
        /// </summary>
        public string? accountBank { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public string? serviceId { get; set; }

    }
    public class RecognizeReceiveOneInput
    {
        /// <summary>
        /// 客户ids
        /// </summary>
        public List<QueryCasRecbillCustomerModel>? casRecbillCustomer { get; set; } = new List<QueryCasRecbillCustomerModel>() { };
        /// <summary>
        /// 客户id
        /// </summary>
        public string? customer { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public string? company { get; set; }

        public int? opt { get; set; }
        public List<string>? type { get; set; }
    }

    public class modifyCollectionTypeInput
    {
        /// <summary>
        /// 运营备注
        /// </summary>
        public string jfzx_operationremake { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public string receivingType { get; set; }
        /// <summary>
        /// 单据类型,A:收款单 , B:认款单
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 业务组织
        /// </summary>
        public string orgno { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string projectno { get; set; }
    }
    public class QueryCasRecbillCustomerModel
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string? customer { get; set; }
    }
    public class RecognizeReceiveOutput
    {
        /// <summary>
        /// 单据编码
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 付款单位
        /// </summary>
        public string payerNumber { get; set; }
        /// <summary>
        /// 付款单位名称
        /// </summary>
        public string payerName { get; set; }
        /// <summary>
        /// 收款单位
        /// </summary>
        public string orgNumber { get; set; }
        /// <summary>
        /// 收款单位名称
        /// </summary>
        public string orgName { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal actrecamt { get; set; }
        /// <summary>
        /// 收款类型
        /// </summary>
        public string receivingtype { get; set; }
        /// <summary>
        /// 结算方式
        /// </summary>
        public string settletype { get; set; }
        /// <summary>
        /// 收款日期
        /// </summary>
        public string? payeedate { get; set; }
        /// <summary>
        /// 收款日期
        /// </summary>
        public string? payeedateStr
        {
            get
            {
                if (string.IsNullOrEmpty(payeedate))
                {
                    return string.Empty;
                }
                else
                {
                    return payeedate.Split(' ')[0];
                }
            }
        }
        /// <summary>
        /// 未认领金额
        /// </summary>
        public decimal namountclaimed { get; set; }

        public string? kdReceivingtype { get; set; }
        /// <summary>
        /// 运营备注
        /// </summary>
        public string? remake { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string? deptId { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string? code { get; set; }
        /// <summary>
        /// 结算号
        /// </summary>
        public string? settletnumber { get; set; }
        /// <summary>
        /// 到期日
        /// </summary>
        public string? draftbillexpiredate { get; set; }
        /// <summary>
        /// 到期日
        /// </summary>
        public string? draftbillexpiredateStr
        {
            get
            {
                if (string.IsNullOrEmpty(draftbillexpiredate))
                {
                    return string.Empty;
                }
                else
                {
                    return draftbillexpiredate.Split(' ')[0];
                }
            }
        }

        /// <summary>
        /// 项目号
        /// </summary>
        public string? itemNumber { get; set; }

        /// <summary>
        /// 银行账户
        /// </summary>
        public string? bankNum { get; set; }

        /// <summary>
        /// 银行类型(名称)
        /// </summary>
        public string? bankName { get; set; }
        /// <summary>
        /// 交易时间
        /// </summary>
        public string? bizTime { get; set; }

        /// <summary>
        /// 付款人类型(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? payerType { get; set; }

        /// <summary>
        /// 收款人银行类型
        /// </summary>
        public string? payeeBankName { get; set; }

        /// <summary>
        /// 收款人银行账户
        /// </summary>
        public string? accountBank { get; set; }

        /// <summary>
        /// 业务单元（金蝶存的名称）
        /// </summary>
        public string? serviceId { get; set; }
    }

    public class RecognizeReceiveBatchSaveInput
    {
        //认款单号--增加

        /// <summary>
        /// 收款单编号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public List<CasRecbillOrderNoBean>? orderno { get; set; } = new List<CasRecbillOrderNoBean>() { };
        /// <summary>
        /// 发票明细
        /// </summary>
        public List<CasRecbillInvoiceBean>? invoice { get; set; } = new List<CasRecbillInvoiceBean>() { };
    }
    /// <summary>
    /// 发票明细
    /// </summary>
    public class CasRecbillInvoiceBean
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string number { get; set; }
        /// <summary>
        /// 抄送金额
        /// </summary>
        public decimal copiedamount { get; set; }
    }
    /// <summary>
    /// 订单明细
    /// </summary>
    public class CasRecbillOrderNoBean
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderNum { get; set; }
        /// <summary>
        /// 抄送金额
        /// </summary>
        public decimal orderAmount { get; set; }
    }
    /// <summary>
    /// 推送运营制作开票单到金碟
    /// </summary>
    public class PushCustomizeInvoiceSaveInput
    {
        /// <summary>
        /// 单据类型
        /// </summary>
        public string billsourcetype { get; set; }

        /// <summary>
        /// 单据性质
        /// </summary>
        public string billproperties { get; set; }

        /// <summary>
        /// 申请方
        /// </summary>

        [JsonProperty("applicant", NullValueHandling = NullValueHandling.Ignore)]
        public string? applicant { get; set; }

        /// <summary>
        /// 红字信息表编号/红字确认单编号
        /// </summary>
        [JsonProperty("infocode", NullValueHandling = NullValueHandling.Ignore)]
        public List<string>? infocode { get; set; }

        /// <summary>
        /// 冲红原因
        /// </summary>

        [JsonProperty("redreason", NullValueHandling = NullValueHandling.Ignore)]
        public string? redreason { get; set; }

        /// <summary>
        /// 待冲蓝票号码
        /// </summary>
        [JsonProperty("originalinvoiceno", NullValueHandling = NullValueHandling.Ignore)]
        public string? originalinvoiceno { get; set; }

        /// <summary>
        /// 待冲蓝票代码
        /// </summary>
        [JsonProperty("originalinvoicecode", NullValueHandling = NullValueHandling.Ignore)]
        public string? originalinvoicecode { get; set; }
        /// <summary>
        /// 待冲蓝票开票日期
        /// </summary>
        public DateTime originalissuetime { get; set; }
        /// <summary>
        /// 征税方式
        /// </summary>
        public string taxationstyle { get; set; }

        /// <summary>
        /// 发票种类
        /// </summary>
        public string invoicetype { get; set; }
        /// <summary>
        /// 组织id
        /// </summary>
        public string orgid { get; set; }
        /// <summary>
        /// 交付手机
        /// </summary>
        public string buyerphone { get; set; }
        /// <summary>
        /// 购方开户行及账号
        /// </summary>
        public string buyerbank { get; set; }
        /// <summary>
        /// 发票备注
        /// </summary>
        public string? invoiceremark { get; set; }
        /// <summary>
        /// 审批备注
        /// </summary>
        public string? jfzx_auditsuggestion { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public string billdate { get; set; }
        /// <summary>
        /// 购方纳税识别号
        /// </summary>
        public string buyertaxno { get; set; }
        /// <summary>
        /// 交付邮箱
        /// </summary>
        public string buyeremail { get; set; }
        /// <summary>
        /// 业务单据编号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 特殊票种
        /// </summary>
        public string specialtype { get; set; }
        /// <summary>
        /// 购方名称
        /// </summary>
        public string buyername { get; set; }
        /// <summary>
        /// 购方地址及电话
        /// </summary>
        public string buyeraddr { get; set; }

        /// <summary>
        /// 商品详情
        /// </summary>
        public List<OriginalBillEntryItem> originalBillEntry { get; set; }
        /// <summary>
        /// 应收明细
        /// </summary>
        public List<originalBillArEntryItem> originalBillArEntry { get; set; }
        public string username { get; set; }
        public string jfzx_customerf { get; set; }
        /// <summary>
        /// 是否预开票
        /// </summary>
        public bool jfzx_preinvoicing { get; set; } = false;
        /// <summary>
        /// 预开票订单号
        /// </summary>
        public string jfzx_ordernumber { get; set; }

        /// <summary>
        /// 预开票项目号
        /// </summary>

        public string jfzx_itemnumber { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary> 
        public string jfzx_businessorg { get; set; }
    }

    public class originalBillArEntryItem
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string jfzx_arbillnum { get; set; }
        /// <summary>
        /// 使用金额
        /// </summary>
        public decimal jfzx_syamount { get; set; }
        /// <summary>
        /// 业务组织
        /// </summary>
        public string? jfzx_bizorg { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? jfzx_customer { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string? jfzx_project { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? jfzx_order { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string user { get; set; }

        /// <summary>
        /// 税率明细
        /// </summary>
        public List<taxRateDetail> taxRateDetails { get; set; } = new List<taxRateDetail>();
    }
    public class taxRateDetail
    {
        /// <summary>
        /// 使用金额
        /// </summary>
        public decimal usedAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxrate { get; set; }
    }
    /// <summary>
    /// 撤销发票
    /// </summary>
    public class ReturnCustomizeInvoiceSaveInput
    {
        public string billno { get; set; }
    }
    /// <summary>
    /// 推送运营制作开票单-商品详情
    /// </summary>
    public class OriginalBillEntryItem
    {
        /// <summary>
        /// 金额含税
        /// </summary>
        public decimal taxamount { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool gift { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxrate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal num { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        public string specification { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string goodsname { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 单价(不含税)
        /// </summary>
        public decimal unitprice { get; set; }
        /// <summary>
        /// 单价(含税)
        /// </summary>
        public decimal taxunitprice { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal tax { get; set; }
        /// <summary>
        /// 税收分类编码名
        /// </summary>
        public string taxratecodeid { get; set; }
        /// <summary>
        /// 计量单位
        /// </summary>
        public string unit { get; set; }
        /// <summary>
        /// 是否享受优惠
        /// </summary>
        public string policylogo { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string jfzx_receivableno { get; set; }
        /// <summary>
        /// 行性质 1:折扣行,2:商品行
        /// </summary>
        public string rowtype { get; set; }
    }


    public class HoldStorageInput
    {
        /// <summary>
        ///供应商
        /// </summary>
        public string jfzx_supplier { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<HoldStorageDetail> entryentity { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public DateTime jfzx_tallydate { get; set; }
        /// <summary>
        /// 库存组织
        /// </summary>
        public string org { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime jfzx_date { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string jfzx_customer { get; set; }
        /// <summary>
        /// 单据编码
        /// </summary>
        public string billno { get; set; }
        public decimal? fk_jfzx_totalsalescost { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string jfzx_remake { get; set; }

        public string jfzx_creator { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        public int StoreInType { get; set; }

        public string jfzx_stockremovaltype
        {
            get
            {
                //storeinType:2=寄售调出,7=集团寄售入库
                if (StoreInType == 2 || StoreInType == 7)
                {
                    return "B"; //销售换入
                }
                else if (StoreInType == 15)//销售换入
                {
                    return "C"; //销售换入
                }
                else if (StoreInType == 3)//进口换货入库
                {
                    return "D"; //进口换货入库
                }
                else if (StoreInType == 22)//赠品调回入库
                {
                    return "F";
                }
                else if (StoreInType == 1000)// 盘盈入库
                {
                    return "E";
                }
                return "A";//寄售入库:
            }
        }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string jfzx_businessorg { get; set; }
    }
    public class HoldStorageDetail
    {
        /// <summary>
        /// 数量
        /// </summary>
        public int jfzx_count { get; set; }
        /// <summary>
        /// 销售成本
        /// </summary>
        public decimal jfzx_sellingcost { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string jfzx_material { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        public string jfzx_model { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_projectnos { get; set; }
        /// <summary>
        /// 单价(单位成本)
        /// </summary>
        public decimal jfzx_unitprice { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public string jfzx_suppliers { get; set; }

        /// <summary>
        /// 货品类型
        /// </summary>
        public int? Mark { get; set; }

        public string jfzx_goodstypes
        {
            get
            {
                if (Mark == 0)
                {
                    return "JX";
                }
                else if (Mark == 1)
                {

                    return "JS";
                }
                else if (Mark == 2)
                {
                    return "JTJS"; //集团寄售 
                }
                else if (Mark == 3)
                {
                    return "JTJX"; //集团经销 
                }
                return "JX";
            }
        }
        /// <summary>
        /// 关税金额(明细汇总)
        /// </summary>
        public decimal? jfzx_tariff { get; set; }
        /// <summary>
        /// 进口增值税金额(明细汇总)
        /// </summary>
        public decimal? jfzx_vat { get; internal set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string? taxRateNumber { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? tax { get; set; }

        /// <summary>
        /// 预计销售单价
        /// </summary>
        public decimal? salePrice { get; set; }
    }

    public class HoldStockRemovalInput
    {
        /// <summary>
        /// 单据编码
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 销售成本总额
        /// </summary>
        public decimal? fk_jfzx_totalsalescost { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime jfzx_date { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public DateTime jfzx_tallydate { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string jfzx_customer
        {
            get; set;
        }
        /// <summary>
        ///供应商
        /// </summary>
        public string jfzx_supplier { get; set; }

        /// <summary>
        ///业务组织
        /// </summary>
        public string jfzx_businessorg { get; set; }
        /// <summary>
        /// 库存组织
        /// </summary>
        public string org { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string jfzx_remake { get; set; }

        public string jfzx_creator { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        public int StoreOutType { get; set; }

        public string jfzx_stockremovaltype
        {
            get
            {   //StoreOutType:4=暂存出库
                if (StoreOutType == 4)
                {
                    return "A";//暂存出库:
                }
                if (StoreOutType == 6)
                {
                    return "I";//退货出库:
                }
                if (StoreOutType == 12)
                {
                    return "J";//盘亏出库:
                }
                else if (StoreOutType == 15)
                {
                    return "C"; //销售换出
                }
                else if (StoreOutType == 16)
                {
                    return "D"; //赠品出库
                }
                else if (StoreOutType == 18)
                {
                    return "F"; //样品出库
                }
                else if (StoreOutType == 19)
                {
                    return "K"; //销毁出库
                }
                else if (StoreOutType == 20)
                {
                    return "H"; //经销调出
                }
                else if (StoreOutType == 22)
                {
                    return "G"; //换货退货
                }
                else if (StoreOutType == 17)
                {
                    return "L"; //租借出库
                }
                return "B";//寄售调出
            }
        }

        /// <summary>
        /// 明细
        /// </summary>
        public List<HoldStockRemovalDetail> holdStockRemovalEntrysModel { get; set; }

    }
    public class HoldStockRemovalDetail
    {
        /// <summary>
        /// 数量
        /// </summary>
        public int jfzx_count { get; set; }
        /// <summary>
        /// 销售成本
        /// </summary>
        public decimal jfzx_sellingcost { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string jfzx_material { get; set; }
        /// <summary>
        /// 规格型号
        /// </summary>
        public string jfzx_model { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_projectnos { get; set; }
        /// <summary>
        /// 单价(单位成本)
        /// </summary>
        public decimal jfzx_unitprice { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public string jfzx_suppliers { get; set; }

        /// <summary>
        /// 货品类型
        /// </summary>
        public int? Mark { get; set; }

        public string jfzx_goodstypes
        {
            get
            {
                if (Mark == 0)
                {
                    return "JX";
                }
                else if (Mark == 1)
                {

                    return "JS";
                }
                else if (Mark == 2)
                {
                    return "JTJS"; //集团寄售 
                }
                else if (Mark == 3)
                {
                    return "JTJX"; //集团经销 
                }
                return "JX";
            }
        }

        /// <summary>
        /// 税率编码
        /// </summary>
        public string? taxrateid_number { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? jfzx_tax { get; set; }


        /// <summary>
        /// 预计销售单价
        /// </summary>
        public decimal? jfzx_expectsellprice { get; set; }

        /// <summary>
        /// 预计销售金额
        /// </summary>
        public decimal? jfzx_expectsellamount { get; set; }

        /// <summary>
        /// 采购成本税额
        /// </summary>
        public decimal? jfzx_purtax { get; set; }
    }

    public class ReceiptNumberModelInput
    {
        public string paymentNum { get; set; }
    }

    public class ReceiptNumberModelOutput
    {
        public string address { get; set; }
        public string paymentNum { get; set; }
        public string previewAddress { get; set; }
    }

    /// <summary>
    /// 保存认款单
    /// </summary>
    public class BatchSaveAcceptanceInput
    {
        /// <summary>
        /// 本次认款金额
        /// </summary>
        public decimal jfzx_amountfield { get; set; }
        /// <summary>
        /// 收款日期
        /// </summary>
        public string jfzx_gatheringdate { get; set; }
        /// <summary>
        /// 收款单单位
        /// </summary>
        public string jfzx_gatheringorg { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal jfzx_gatheringamount { get; set; }
        /// <summary>
        /// 付款单位
        /// </summary>
        public string jfzx_payer { get; set; }
        /// <summary>
        /// 认款单号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string jfzx_gatheringnum { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string jfzx_bizorg { get; set; }

        /// <summary>
        /// 单据类型：ar_finarbill代表负数应收，cas_recbill代表收款单
        /// </summary>
        public string billtype { get; set; }

        /// <summary>
        /// 明细集合
        /// </summary>
        public List<AcceptanceEntrysItemInput> acceptanceEntrys { get; set; }

        /// <summary>
        /// 结算明细
        /// </summary>
        public List<AcceptanceSettleEntriesItemInput> settlementEntries { get; set; }

        /// <summary>
        /// 操作类型（保存-save，更新-update）
        /// </summary>
        public string? operation { get; set; } = "save";
    }

    /// <summary>
    /// 结算明细
    /// </summary>
    public class AcceptanceSettleEntriesItemInput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? receivableNumber { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? orderNumber { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string? projectsNumber { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? invoiceNo { get; set; }
        /// <summary>
        /// 母项目号
        /// </summary>
        public string? parentItemNumber { get; set; }
        /// <summary>
        /// 本次结算金额
        /// </summary>
        public decimal? settleAmount { get; set; }
        /// <summary>
        /// 应收是否结算
        /// </summary>
        public bool? revenueConfirm { get; set; }
    }

    /// <summary>
    /// 项目号编码
    /// </summary>
    public class SavePaymentAdjustmentInput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        public string? jfzx_paymentno { get; set; }
        /// <summary>
        /// 原订单号
        /// </summary>
        public string? jfzx_orderno { get; set; }
        /// <summary>
        /// 调整订单号
        /// </summary>
        public string? jfzx_adorderno { get; set; }
        /// <summary>
        /// 调整金额
        /// </summary>
        public decimal jfzx_adamount { get; set; }
        /// <summary>
        /// 项目号编码
        /// </summary>
        public string? jfzx_projectno { get; set; }
        /// <summary>
        /// 业务组织编码
        /// </summary>
        public string? jfzx_org { get; set; }
    }

    /// <summary>
    /// 保存认款单明细
    /// </summary>
    public class AcceptanceEntrysItemInput
    {
        /// <summary>
        /// 备注
        /// </summary>
        public string jfzx_remaker { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>
        public decimal jfzx_subscriptionamount { get; set; }
        /// <summary>
        /// 认款日期
        /// </summary>
        public string jfzx_subscriptiondate { get; set; }
        /// <summary>
        /// 认款类型,A:发票号,B:订单号,C:应收单号,D:暂收款
        /// </summary>
        public string jfzx_subscriptiontype { get; set; }

        /// <summary>
        /// 货款类型,1:租金,2:商品款,3:服务费
        /// </summary>
        public string jfzx_goodspaymenttype { get; set; }

        /// <summary>
        /// 实际付款人
        /// </summary>
        public string jfzx_customer { get; set; }

        /// <summary>
        /// 收款类型
        /// </summary>
        public string jfzx_collectiontype { get; set; }
        /// <summary>
        /// 认款编码
        /// </summary>
        public string jfzx_subscriptionnum { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool jfzx_skipornot { get; set; }
        /// <summary>
        /// 认款人
        /// </summary>
        public string jfzx_payee { get; set; }
        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_projectnos { get; set; }
    }

    /// <summary>
    /// 应付付款结算
    /// </summary>
    public class PaymentSettlementInput
    {
        /// <summary>
        /// 主方结算金额
        /// </summary>
        public decimal mianSettleAmt { get; set; }

        /// <summary>
        /// 主方单据编码(财务应付单)
        /// </summary>
        public string mianbillno { get; set; }

        /// <summary>
        /// 辅方单据编码(付款单)
        /// </summary>
        public string asstbillno { get; set; }

        /// <summary>
        /// 辅方结算金额
        /// </summary>
        public decimal asstSettleAmt { get; set; }

        /// <summary>
        /// 是否单次结算
        /// </summary>
        public bool? singleSettle { get; set; }
    }

    /// <summary>
    /// 校验发票号 入参
    /// </summary>
    public class InvoiceNumberVerificationInput
    {
        /// <summary>
        /// 收款单编号
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 发票号码
        /// </summary>
        public string jfzx_invoiceno { get; set; }
    }

    /// <summary>
    /// 校验发票号 出参
    /// </summary>
    public class InvoiceNumberVerificationOutput
    {
        /// <summary>
        /// 价税合计
        /// </summary>
        public decimal jfzx_totalamount { get; set; }

        /// <summary>
        /// 已抄金额
        /// </summary>
        public decimal jfzx_copiedamount { get; set; }

        /// <summary>
        /// 未抄金额
        /// </summary>
        public decimal jfzx_ncopiedamount { get; set; }
    }
    public class AuditBillingApplicationInput
    {
        public string billno { get; set; }
    }
    public class RollBackBillDto
    {
        public string billType { get; set; }

        public List<RollBackBillNo> billnos { get; set; }

    }
    public class RollBackBillNo
    {
        public string billno { get; set; }
    }


    /// <summary>
    /// 金蝶进项发票提交参数
    /// </summary>
    public class KingdeeInputBillSubmitDto
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceno { get; set; }

        /// <summary>
        /// 对应应付明细
        /// </summary>
        public List<KingdeeInputBillSubmitDetailDto> finentry { get; set; }

    }

    /// <summary>
    /// 进项票对应应付明细
    /// </summary>
    public class KingdeeInputBillSubmitDetailDto
    {
        /// <summary>
        /// 对应应付金额
        /// </summary>
        public decimal f_usedamt { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string finNum { get; set; }

        [JsonIgnore]
        public string relateCode { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string user { get; set; }

        /// <summary>
        /// 关联日期
        /// </summary>
        public string associatedDate { get; set; }

        /// <summary>
        /// 按税率拆分
        /// </summary>
        //public List<PayBillRate> payBillRateList { get; set; }

    }

    /// <summary>
    /// 按税率拆分
    /// </summary>
    public class PayBillRate
    {
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal? usedAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxRate { get; set; }
    }

    /// <summary>
    /// 按税率入库单号
    /// </summary>
    public class PayBillRateStoreInItemCode
    {
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal? usedAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxRate { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? storeInItemCode { get; set; }
    }

    public class ExchangeRateQueryInput
    {
        /// <summary>
        /// 原币.名称人民币，港币，日元，美元，欧元，英镑，韩元
        /// </summary>
        public string orgcur_name { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        public string effectdate { get; set; }

        /// <summary>
        /// 目标币.名称人民币，港币，日元，美元，欧元，英镑，韩元
        /// </summary>
        public string cur_name { get; set; }
    }
    public class ExchangeRateQueryOutput
    {
        /// <summary>
        /// 
        /// </summary>
        public string? filter { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? lastPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<RowsItem> rows { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int totalCount { get; set; }
    }
    public class RowsItem
    {
        /// <summary>
        /// 失效日期
        /// </summary>
        public string? expirydate { get; set; }
        /// <summary>
        /// 直接汇率值
        /// </summary>
        public decimal? excval { get; set; }
        /// <summary>
        /// 生效日期
        /// </summary>
        public string? effectdate { get; set; }
        /// <summary>
        /// 原币.名称
        /// </summary>
        public string? orgcur_name { get; set; }
        /// <summary>
        /// 间接汇率值
        /// </summary>
        public decimal? indirectexrate { get; set; }
        /// <summary>
        /// 目标币.名称
        /// </summary>
        public string? cur_name { get; set; }
    }


    public class saveCoreToBizapplyDetailInput
    {
        /// <summary>
        /// 商品原产地
        /// </summary>
        public string productRegion { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string detailNumber { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal price { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int count { get; set; }
        /// <summary>
        /// 合同金额
        /// </summary>
        public decimal contractMoney { get; set; }

        /// <summary>
        ///海关编码
        /// </summary>
        public string customsNumber { get; set; }
    }

    public class saveCoreToBizapplyInput
    {
        /// <summary>
        /// 币种
        /// </summary>
        public string moneyType { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string dept { get; set; }
        /// <summary>
        /// 采购合同号
        /// </summary>
        public string purchaseContractNumber { get; set; }
        /// <summary>
        /// 价格条款
        /// </summary>
        public string priceDescription { get; set; }
        /// <summary>
        /// 卸货港
        /// </summary>
        public string dischargePort { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal money { get; set; }
        /// <summary>
        /// 受益人描述
        /// </summary>
        public string beneficiary { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string projcetName { get; set; }
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string purchaseOrderNumber { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string supplier { get; set; }
        /// <summary>
        /// 装货港
        /// </summary>
        public string loadPort { get; set; }
        /// <summary>
        /// 法人公司
        /// </summary>
        public string company { get; set; }
        /// <summary>
        /// 业务明细信息
        /// </summary>
        public List<saveCoreToBizapplyDetailInput> detail { get; set; }
        /// <summary>
        /// 国别地区
        /// </summary>
        public string region { get; set; }
        /// <summary>
        /// 申请人信息描述
        /// </summary>
        public string applyInfo { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string? projectNumber { get; set; }
    }

    public class getLetterCreditInput
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public List<string> numbers { get; set; }
    }

    public class getLetterCreditOutput
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string? applyNumber { get; set; }
        /// <summary>
        /// 信用证号
        /// </summary>
        public string? creditNumber { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string? status { get; set; }
        /// <summary>
        /// 结算币种
        /// </summary>
        public string? currency { get; set; }

        /// <summary>
        /// 开证时间
        /// </summary>
        public string? startTime { get; set; }

        /// <summary>
        /// 开证金额
        /// </summary>
        public decimal? startMoney { get; set; }

        /// <summary>
        /// 到单单号
        /// </summary>
        public string? arrivalNumber { get; set; }

        /// <summary>
        /// 到单时间
        /// </summary>
        public string? arrivalTime { get; set; }

        /// <summary>
        /// 到单金额
        /// </summary>
        public decimal? arrivalMoney { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? payNumber { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public string? payTime { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal? payMoney { get; set; }

        /// <summary>
        /// 剩余金额
        /// </summary>
        public decimal? remainingAmount { get; set; }

        /// <summary>
        /// 确认日期
        /// </summary>
        public string? configTime { get; set; }

        /// <summary>
        /// 到单状态(到单已登记：arrival_register；到单已确认：arrival_confirm；到单已付款：arrival_pay)
        /// </summary>
        public string? arrivalStatus { get; set; }
    }

    public class QueryInvoiceAttachmentInput
    {
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? invoiceCode { get; set; }

        /// <summary>
        /// 发票类型(进项:JX,销项:XX)	
        /// </summary>
        public string? invoiceType { get; set; } = "XX";
    }

    public class QueryInvoiceAttachmentOutput
    {
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceNo { get; set; }

        /// <summary>
        /// 附件下载地址
        /// </summary>
        public string address { get; set; }

        /// <summary>
        /// 附件预览地址
        /// </summary>
        public string previewAddress { get; set; }
        /// <summary>
        /// XML文件URL 
        /// </summary>
        public string xmlFileUrl { get; set; }
        /// <summary>
        /// OFD文件URL
        /// </summary>
        public string ofdFileUrl { get; set; }
        /// <summary>
        /// PDF文件URL
        /// </summary>
        public string pdfFileUrl { get; set; }
    }

    public class QueryPaymentRefundInput
    {
        /// <summary>
        /// 公司
        /// </summary>
        public List<string>? paymentNum { get; set; } = new List<string>() { };

        /// <summary>
        /// 核算部门
        /// </summary>
        public List<string>? orgNum { get; set; } = new List<string>() { };

        /// <summary>
        /// 开始日期
        /// </summary>
        public string? startDate { get; set; } = "";

        /// <summary>
        /// 结束日期
        /// </summary>
        public string? endDate { get; set; } = "";

        /// <summary>
        /// 客户
        /// </summary>
        public List<string>? customerNum { get; set; } = new List<string>() { };

        public int page { get; set; }

        public int limit { get; set; }
        public int pageindex { get { return page; } }
        public int pagesize { get { return limit; } }
        public string? searchKey { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RefundStatusEnum? status { get; set; }

        /// <summary>
        /// 单据状态（B：已提交；C:审核通过；E：审核不通过）
        /// </summary>
        public string? billstatus { get; set; }

        /// <summary>
        /// 付款状态（A：未付款，C：已付款）
        /// </summary>
        public string? paidstatus { get; set; }
        public int? refundParty { get; set; }
    }

    public class QueryPaymentRefundOutput
    {
        public List<QueryPaymentRefundOutputData> list { get; set; }
        public int all { get; set; }
    }

    public class QueryPaymentRefundOutputData
    {
        /// <summary>
        /// id
        /// </summary>
        public Guid? id { get; set; }

        /// <summary>
        /// 申请单号
        /// </summary>
        public string? billno { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? e_payee { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string? e_payeeNum { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string? org { get; set; }
        public string? orgStr
        {
            get
            {
                var ret = string.Empty;
                if (!string.IsNullOrEmpty(org))
                {
                    ret = org.Replace(".", "/");
                }
                return ret;
            }
        }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string? billstatus { get; set; }

        /// <summary>
        /// 付款状态
        /// </summary>
        public string? paidstatus { get; set; }
        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? orgNum { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? payment { get; set; }

        /// <summary>
        /// 公司namecode
        /// </summary>
        public string? paymentNum { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        public string? e_settlementtype { get; set; }
        /// <summary>
        /// 付款方式Id
        /// </summary>
        public string? e_settlementtypeid { get; set; }
        /// <summary>
        /// 收款账号
        /// </summary>
        public string? e_payeeaccbanknum { get; set; }

        /// <summary>
        /// 退款类型
        /// </summary>
        public string? paymenttype { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal? payeeamount { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public string? applydate { get; set; }

        /// <summary>
        /// OA流程id
        /// </summary>
        public long? jfzx_requestid { get; set; }

        /// <summary>
        /// OA流程id
        /// </summary>
        public string? creator { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? createtime { get; set; }
        /// <summary>
        /// 明细数据
        /// </summary>
        public List<QueryPaymentRefundDetailData>? refundEntry { get; set; }
        /// <summary>
        /// 事由
        /// </summary>
        public string? applycause { get; set; }
        /// <summary>
        /// 收款单号集合
        /// </summary>
        public List<string?>? receivablesNumberList { get; set; }
        public string? projectCode { get; set; }
        public Guid? projectId { get; set; }
        public string? projectName { get; set; }
        public int? RefundParty { get; set; }
        public string? nameCode { get; set; }
        public string? companyId { get; set; }
        /// <summary>
        /// 收款人类型(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? e_payeetype { get; set; }
    }
    public class QueryPaymentRefundDetailData
    {
        /// <summary>
        /// 负数应收单号
        /// </summary>
        public string? jfzx_minusnumber { get; set; }
        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal e_payeeamount { get; set; }
        /// <summary>
        /// 金蝶返回的收款单号
        /// </summary>
        public string? receivablesNumber { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? receiveCode { get; set; }

        public decimal? receiveAmount { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? paymentCode { get; set; }

        public decimal? paymentAmount { get; set; }

    }
    public class SaveOrUpdateRefundInput
    {

        /// <summary>
        /// 申请单号
        /// </summary>
        public string? billno { get; set; }

        /// <summary>
        /// 方式新增:insert,更新：update
        /// </summary>
        public string model { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        public string payModel { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string client { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        public string? bankAccount { get; set; }

        /// <summary>
        /// 账号名称
        /// </summary>
        public string? bankName { get; set; }

        /// <summary>
        /// 联行号
        /// </summary>
        public string? bankNo { get; set; }

        /// <summary>
        /// 银行支行名称
        /// </summary>
        public string? bankBranchName { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        public string? bankBranchNumber { get; set; }

        /// <summary>
        /// 核算部门Id
        /// </summary>
        public string dept { get; set; }
        public string? businessDeptFullPath { get; set; }
        public string? businessDeptFullName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string? projectNumber { get; set; }

        public string? projectId { get; set; }

        public string? projectName { get; set; }

        /// <summary>
        /// 招标编号
        /// </summary>
        public string? tenderNumber { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string company { get; set; }

        public string? companyName { get; set; }

        /// <summary>
        /// 退款类型负数应收：203，其他：996,投标保证金:219,履约保证金:220,押金:221,中标服务费：230，退回收到的押金228 【240】退返利款 【204】退预收款 【229】 退回收到的保证金
        /// </summary>
        public string refundType { get; set; }

        /// <summary>
        /// 退款形式 客商退我方
        /// </summary>
        public int? RefundParty { get; set; }

        /// <summary>
        /// 退款类型推销售回：203，退预收款：204
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// OA账号
        /// </summary>
        public string OAUserName { get; set; }

        /// <summary>
        /// 退款总金额
        /// </summary>
        public decimal refundAllMoney { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string? moneyNumber { get; set; }

        /// <summary>
        /// 退款单申请详情
        /// </summary>
        public List<CasRefundDetailModelInput>? list { get; set; }

        /// <summary>
        /// 收款人类型(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? e_payeetype { get; set; } = "bd_customer";

        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentsModel>? attachmentsModel { get; set; } = new List<AttachmentsModel>();


        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        public string? refundItemId { get; set; }

        /// <summary>
        /// 转账附言（最大200个字）
        /// </summary>
        public string? transferPostscript { get; set; }

        /// <summary>
        /// 往来单位（供应商）
        /// </summary>
        public string? billPayerNumber { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string? bizOrgNumber { get; set; }

        /// <summary>
        /// 母项目号
        /// </summary>
        public string? parentProjectNumber { get; set; }

        /// <summary>
        /// 费用项目
        /// </summary>
        public string? expenseItemNumber { get; set; }

        /// <summary>
        /// 收款人
        /// </summary>
        public string? payerName { get; set; }

        /// <summary>
        /// 申请日期
        /// </summary>
        public DateTime? bizDate { get; set; }

        /// <summary>
        /// 预计冲销日期
        /// </summary>
        public DateTime? repaymentDate { get; set; }
    }
    public class CasRefundDetailModelInput
    {
        /// <summary>
        /// 负数应收单号
        /// </summary>
        public string? minusNumber { get; set; }
        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal refundMoney { get; set; }

        /// <summary>
        /// 收款人类型(客户:bd_customer,供应商:bd_supplier)
        /// </summary>
        public string? e_payeetype { get; set; } = "bd_customer";

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? receivablesNumber { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? receiveCode { get; set; }

        public decimal? receiveAmount { get; set; }


        /// <summary>
        /// 付款单号
        /// </summary>
        public string? paymentCode { get; set; }

        public decimal? paymentAmount { get; set; }
    }

    public class RefundDeleteInput
    {
        /// <summary>
        /// 创建人
        /// </summary>
        public string creator { get; set; }
        /// <summary>
        /// 申请单号
        /// </summary>
        public string billno { get; set; }

        public string? id { get; set; }
    }

    public class FindFileInput
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string billno { get; set; }
        /// <summary>
        /// 单据类型 付款单：payBill，收款单：recBill
        /// </summary>
        public string type { get; set; }
    }
    /// <summary>
    /// 行名行号信息
    /// </summary>
    public class SaveLineNameLineNumberInput
    {

        /// <summary>
        /// 编码(新增数据不填,修改数据时必填)
        /// </summary>
        public string? number { get; set; }

        /// <summary>
        /// 名称(必填)
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 国家地区(必填)
        /// </summary>
        public string country { get; set; }

        /// <summary>
        ///  swift_code
        /// </summary>
        public string? swift_code { get; set; }

        /// <summary>
        ///  地址
        /// </summary>
        public string? address { get; set; }

        /// <summary>
        ///  地址(英文)
        /// </summary>
        public string? address_eng { get; set; }

        /// <summary>
        ///  电话
        /// </summary>
        public string? telephone { get; set; }

        /// <summary>
        ///  传真
        /// </summary>
        public string? fax { get; set; }

        /// <summary>
        ///  备注
        /// </summary>
        public string? remark { get; set; }

        /// <summary>
        /// 城市(银企)
        /// </summary>
        public string? citytxt { get; set; }
    }

    public class DeleteLineNameLineNumberInput
    {
        /// <summary>
        /// 编码
        /// </summary>
        public string number { get; set; }

    }

    public class CompanyInfoItem
    {
        /// <summary>
        /// 税号
        /// </summary>
        public string taxNo { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
    }

    public class RecognitionCheckInput
    {
        /// <summary>
        /// 文件base64，base64或者fileDownUrl二选一
        /// </summary>
        public string? base64 { get; set; } = "";
        /// <summary>
        /// 文件下载地址，免鉴权的http或者https地址，或者苍穹附件路径 二选一
        /// </summary>
        public string? fileDownUrl { get; set; } = "";
        /// <summary>
        /// 文件类型1-pdf，2-图片（png/jpg等），4-ofd，9-xml(必填)
        /// </summary>
        public string? fileType { get; set; } = "1";
        /// <summary>
        /// 是否返回合规性校验结果, 1-是，0-否
        /// </summary>
        public string? verifyFlag { get; set; } = "";
        /// <summary>
        /// 只识别不查验，1-只识别文件不查验，默认查验
        /// </summary>
        public string? notCheck { get; set; } = "";
        /// <summary>
        /// 单据类型，verifyFlag为1时必填
        /// </summary>
        public string? billType { get; set; } = "";
        /// <summary>
        /// 星瀚组织编码
        /// </summary>
        public string? orgNumber { get; set; } = "";
        /// <summary>
        /// 税号
        /// </summary>
        public string? taxNo { get; set; } = "";
        /// <summary>
        /// 数据来源，系统默认值“第三方”
        /// </summary>
        public string? resource { get; set; } = "";
        /// <summary>
        /// 企业信息，用于合规性校验抬头
        /// </summary>
        public List<CompanyInfoItem>? companyInfo { get; set; } = new List<CompanyInfoItem>() { };
    }

    public class EolinkerInput<T>
    {
        /// <summary>
        /// 接口类型
        /// </summary>
        public string messageType { get; set; }
        /// <summary>
        /// 请求id
        /// </summary>
        public string messageId { get; set; }
        /// <summary>
        /// 请求参数
        /// </summary>
        public T data { get; set; }
    }

    public class InvoiceCheckInput
    {
        /// <summary>
        /// 发票代码，数电票不填
        /// </summary>
        public string? invoiceCode { get; set; } = "";

        /// <summary>
        /// 发票号码(必填)
        /// </summary>
        public string invoiceNo { get; set; } = "";

        /// <summary>
        /// 开票日期 (必填)
        /// </summary>
        public string invoiceDate { get; set; } = "";

        /// <summary>
        /// 校验码，增值税普通发票、通行费必填
        /// </summary>
        public string? checkCode { get; set; } = "";

        /// <summary>
        /// 发票金额，增值税专用发票不含税金额，数电票是价税合计
        /// </summary>
        public string? invoiceAmount { get; set; } = "";

        /// <summary>
        /// 数据来源，系统默认值“第三方”
        /// </summary>
        public string resource { get; } = "第三方";
    }

    public class SettlementtypeInput
    {
        /// <summary>
        /// 分页参数，分页数量
        /// </summary>
        public int pageSize { get; set; } = 100;

        /// <summary>
        /// 分页参数，分页数量
        /// </summary>
        public int pageNo { get; set; } = 1;

    }

    public class SettlementtypeOutput
    {
        public List<SettlementtypeData> rows { get; set; }
        public int totalCount { get; set; }
    }
    public class SettlementtypeData
    {
        /// <summary>
        /// 编码
        /// </summary>
        public string number { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 数据状态 A:暂存, B:已提交, C:已审核
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 使用状态 0:禁用, 1:可用
        /// </summary>
        public string enable { get; set; }
    }

    public class SaveOrUpdateRefundOutput
    {
        /// <summary>
        /// oa请求Id
        /// </summary>
        public string? requestid { get; set; }

        /// <summary>
        /// 单据编号
        /// </summary>
        public string? billno { get; set; }

        /// <summary>
        /// 退款单号集合
        /// </summary>
        public List<string?>? receivablesNumberList { get; set; }
    }

    /// <summary>
    /// 收款调整单保存
    /// </summary>
    public class SavePaymentModificationInput
    {
        /// <summary>
        /// 单据编码
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 原认款单号
        /// </summary>
        public string jfzx_sourceorder { get; set; }

        /// <summary>
        /// 认款单据类型 cas_recbill:收款处理, ar_finarbill:负数应收单
        /// </summary>
        public string jfzx_billtype { get; set; }

        /// <summary>
        /// 认款单号
        /// </summary>
        public string jfzx_accbillno { get; set; }

        /// <summary>
        /// 结算组织
        /// </summary>
        public string org { get; set; }

        /// <summary>
        /// 调整日期
        /// </summary>
        public string jfzx_adjustmentdate { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<PaymentModificationEntryModel> entryentity { get; set; }
        /// <summary>
        /// 结算明细
        /// </summary>
        public List<AcceptanceSettleEntriesItemInput> settlementEntries { get; set; }

        /// <summary>
        /// 操作类型（保存-save，更新-update）
        /// </summary>
        public string? operation { get; set; } = "save";
    }

    public class PaymentModificationEntryModel
    {
        /// <summary>
        /// 修改人
        /// </summary>
        public string jfzx_payee { get; set; }

        /// <summary>
        /// 调整类型 A:发票号, B:订单号, C:应收单号, D:暂收款
        /// </summary>
        public string jfzx_subscriptiontype { get; set; }

        /// <summary>
        /// 调整编码
        /// </summary>
        public string jfzx_modnumber { get; set; }

        /// <summary>
        /// 调整金额
        /// </summary>
        public decimal jfzx_adjustmentamounts { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string jfzx_remarks { get; set; }

        /// <summary>
        /// 业务组织编码
        /// </summary>
        public string jfzx_bizorg { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_project { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string jfzx_customer { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string jfzx_receivingtype_number { get; set; }
        /// <summary>
        /// 货款类型,1:租金,2:商品款,3:服务费
        /// </summary>
        public string jfzx_goodspaymenttype { get; set; }
    }

    #region 红字确认单编号查询相关Dto
    public class QueryRedConfirmationFormNumberInput()
    {
        /// <summary>
        /// 公司
        /// </summary>
        public string? orgCode { get; set; }

        /// <summary>
        /// 税号
        /// </summary>
        public string? taxpayerId { get; set; }

        public string? searchKey { get; set; }

        public int page { get; set; }

        public int limit { get; set; }
        public int pageindex { get { return page; } }
        public int pagesize { get { return limit; } }
    }

    /// <summary>
    /// 红字确认单编号查询入参
    /// </summary>
    public class SearchRedConfirmationFormNumberInput()
    {
        public string requestId { get; set; }

        public string interfaceCode { get; set; }

        public string businessSystemCode { get; set; }

        public string data { get; set; }
    }

    /// <summary>
    /// 红字确认单编号查询入参 - (查询主体参数)
    /// </summary>
    public class QueryRedConfirmationFormNumberDto
    {
        public string? taxpayerId { get; set; }

        public string? orgCode { get; set; }

        public string redInfoBillNo { get; set; }
    }

    /// <summary>
    /// 红字确认单编号查询返回
    /// </summary>
    public class QueryRedConfirmationFormNumberOutput()
    {
        public string errorCode { get; set; }

        public string success { get; set; }

        public string message { get; set; }

        public RedConfirmationFormNumberListReturnData data { get; set; }
    }

    public class RedConfirmationFormNumberListReturnData
    {
        public List<RedConfirmationFormNumberListOutput2> dataList { get; set; }


        public int pageNo { get; set; }

        public int pageSize { get; set; }

        public int totalElement { get; set; }
    }

    /// <summary>
    /// 2开接口返回
    /// </summary>
    public class RedConfirmationFormNumberListOutput2()
    {
        /// <summary>
        /// 红字确认单编号
        /// </summary>
        public string? number { get; set; }
        /// <summary>
        /// 确认单状态
        /// </summary>
        public string? confirmStatus { get; set; }
        /// <summary>
        /// 确认单状态说明
        /// </summary>
        public string redConfirmBillStatusName
        {
            get
            {
                if (confirmStatus == "01")
                {
                    return "无需确认";
                }
                else if (confirmStatus == "02")
                {
                    return "销方录入待购方确认";
                }
                else if (confirmStatus == "03")
                {
                    return "购方录入待销方确认";
                }
                else if (confirmStatus == "04")
                {
                    return "购销双方已确认";
                }
                else if (confirmStatus == "05")
                {
                    return "作废（销方录入购方否认）";
                }
                else if (confirmStatus == "06")
                {
                    return "作废（购方录入销方否认）";
                }
                else if (confirmStatus == "07")
                {
                    return "作废（超72小时未确认）";
                }
                else if (confirmStatus == "08")
                {
                    return "作废（发起方已撤销）";
                }
                else if (confirmStatus == "09")
                {
                    return "作废（确认后撤销）";
                }
                else if (confirmStatus == "10")
                {
                    return "作废（异常凭证）";
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 红字确认单录入日期
        /// </summary>
        public string? uploadDate { get; set; }
        /// <summary>
        /// 红冲原因
        /// </summary>
        public string? redReason { get; set; }
        /// <summary>
        /// 红冲原因说明
        /// </summary>
        public string redReasonName
        {
            get
            {
                if (redReason == "01")
                {
                    return "开票有误";
                }
                else if (redReason == "02")
                {
                    return "销货退回";
                }
                else if (redReason == "03")
                {
                    return "服务终止";
                }
                else if (redReason == "04")
                {
                    return "销售折让";
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 录入方身份
        /// </summary>
        public string? enterIdEntity { get; set; }
        /// <summary>
        /// 录入方身份说明
        /// </summary>
        public string enterIdentityName
        {
            get
            {
                if (enterIdEntity == "0")
                {
                    return "销方录入";
                }
                else if (enterIdEntity == "1")
                {
                    return "购方录入";
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 购方名称
        /// </summary>
        public string? buyerName { get; set; }
        /// <summary>
        /// 销方税号
        /// </summary>
        public string? saleTaxNo { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? invoiceNo { get; set; }
        /// <summary>
        /// 红字冲销不含税金额
        /// </summary>
        public decimal? invoiceAmount { get; set; }
        /// <summary>
        /// 红字冲销税额
        /// </summary>
        public decimal? totalTax { get; set; }
        /// <summary>
        /// 红票开票日期
        /// </summary>
        public string? issueTime { get; set; }
        /// <summary>
        /// 蓝字发票类型
        /// </summary>
        public string? originalInvoiceType { get; set; }
        /// <summary>
        /// 蓝字发票类型说明
        /// </summary>
        public string originalInvoiceTypeName
        {
            get
            {
                if (originalInvoiceType == "10xdp")
                {
                    return "数电票（普通发票）";
                }
                else if (originalInvoiceType == "08xdp")
                {
                    return "数电票（增值税专用发票）";
                }
                else if (originalInvoiceType == "007")
                {
                    return "纸质普通发票";
                }
                else if (originalInvoiceType == "004")
                {
                    return "纸质专用发票";
                }
                else if (originalInvoiceType == "lq01")
                {
                    return "增值税专用发票";
                }
                else if (originalInvoiceType == "lq02")
                {
                    return "普通发票";
                }
                else if (originalInvoiceType == "lq03")
                {
                    return "机动车统一销售发票";
                }
                else if (originalInvoiceType == "lq04")
                {
                    return "二手车统一销售发票";
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? originalInvoiceCode { get; set; }
        /// <summary>
        /// 蓝字发票号码
        /// </summary>
        public string? originalInvoiceNo { get; set; }
        /// <summary>
        /// 蓝字发票不含税金额
        /// </summary>
        public decimal? originalInvoiceAmount { get; set; }
        /// <summary>
        /// 蓝字发票税额
        /// </summary>
        public decimal? originalTotalTax { get; set; }
        /// <summary>
        /// 蓝字发票日期
        /// </summary>
        public string? originalIssueTime { get; set; }
    }


    public class RedConfirmationFormNumberListOutput()
    {
        public string? govRedConfirmBillUuid { get; set; }

        public RedConfirmBillStatusEnum? redConfirmBillStatus { get; set; }

        /// <summary>
        /// 确认单状态说明
        /// </summary>
        public string redConfirmBillStatusName
        {
            get
            {
                return redConfirmBillStatus.GetDescription();
            }
        }

        //public string? redConfirmBillStatus { get; set; }

        public string? redInfoBillNo { get; set; }

        public string? redConfirmEnterDate { get; set; }

        public RedReasonEnum redReason { get; set; }

        /// <summary>
        /// 红冲原因说明
        /// </summary>
        public string redReasonName
        {
            get
            {
                return redReason.GetDescription();
            }
        }

        //public string? redReason { get; set; }

        public EnterIdentityEnum enterIdentity { get; set; }

        /// <summary>
        /// 录入方身份说明
        /// </summary>
        public string enterIdentityName
        {
            get
            {
                return enterIdentity.GetDescription();
            }
        }

        //public string? enterIdentity { get; set; }

        public string? buyerName { get; set; }

        public string? buyerTaxpayerId { get; set; }

        public string? sellerName { get; set; }

        public string? invoiceNo { get; set; }

        public string? totalAmount { get; set; }

        public string? totalTaxAmount { get; set; }

        public string? invoiceDate { get; set; }


        public OriginalInvoiceTypeEnum originalInvoiceType { get; set; }

        /// <summary>
        /// 红冲原因说明
        /// </summary>
        public string originalInvoiceName
        {
            get
            {
                return originalInvoiceType.GetDescription();
            }
        }

        public string? originalInvoiceCode { get; set; }

        public string? originalInvoiceNumber { get; set; }

        public string? originalInvoiceAmount { get; set; }

        public string? originalTotalTaxAmount { get; set; }

        public string? originalIssueTime { get; set; }

        public string? sellerTaxpayerId { get; set; }

        public List<RedConfirmationFormDetails?>? invoiceDetail { get; set; }

    }

    /// <summary>
    /// 红字确认单编号明细
    /// </summary>
    public class RedConfirmationFormDetails
    {
        public string? blueInvoiceItemIndex { get; set; }

        public string? index { get; set; }

        public string? revenueCode { get; set; }

        public string? goodsName { get; set; }

        public string? specification { get; set; }

        public string? units { get; set; }

        public string? quantity { get; set; }

        public string? price { get; set; }

        public decimal? amount { get; set; }

        public decimal? taxAmount { get; set; }

        public decimal? taxRate { get; set; }
    }
    #endregion

    #region 红字确认单下载相关Dto
    public class DownloadRedConfirmationFormNumberInput
    {
        /// <summary>
        /// 公司
        /// </summary>
        public string? org { get; set; }
        /// <summary>
        /// 税号
        /// </summary>
        public string? taxNo { get; set; }
        /// <summary>
        /// 购销身份
        /// </summary>
        public string? identity { get; set; }
        public string? otherSideName { get; set; }
        public string? redConfirmBillStatus { get; set; }
        public string? invoiceOpenState { get; set; }
        public DateTime? startDate { get; set; }
        public DateTime? endDate { get; set; }
        public string? redInfoBillNo { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int page { get; set; } = 1;
        /// <summary>
        /// 页面大小
        /// </summary>
        public int limit { get; set; } = 10;

        public int? pageSize
        {
            get
            {
                return limit;
            }
        }
    }
    #endregion

    #region 红字确认单生成相关Dto
    public class GenerateRedConfirmationFormNumberInput
    {
        public string? orderNo { get; set; }

        public string? invoiceCode { get; set; }

        public string? invoiceNo { get; set; }

        public string? redReason { get; set; }

        public string? invoiceType { get; set; }

        public List<GenerateRedConfirmationFormNumberItemInput>? invoiceDetail { get; set; }
    }

    public class GenerateRedConfirmationFormNumberItemInput
    {
        /// <summary>
        /// 行性质，0：正常商品行；1：折扣行[折扣行金额需为负数，它的上一行必须是被折扣行]；2：被折扣行[此商品行下一行必须是折扣行]【长度：2】
        /// </summary>
        public string lineProperty { get; set; }

        /// <summary>
        /// 原蓝字发票明细序号（传了只按原蓝票明细匹配）
        /// </summary>
        public int? originalSeq { get; set; }

        /// <summary>
        /// 业务系统明细id，用于反写回原业务系统明细 【长度：32】
        /// </summary>
        public string? billSourceId { get; set; }

        /// <summary>
        /// 星瀚商品编码【长度：32】，可不传，传了会进行商品匹配
        /// </summary>
        public string? goodsCode { get; set; }

        /// <summary>
        /// 税收项目名称（含税分编码简称的长度）【长度：GBK编码不超过92字节】
        /// </summary>
        public string? goodsName { get; set; }

        /// <summary>
        /// 税收分类编码，必填【长度：19】
        /// </summary>
        public string revenueCode { get; set; }

        /// <summary>
        /// 规格型号【长度：40字节】
        /// </summary>
        public string? specification { get; set; }

        /// <summary>
        /// 计量单位【长度：22字节】
        /// </summary>
        public string? units { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public string? quantity { get; set; }

        /// <summary>
        /// 单价，不含税【长度：整数位加小数位最大16位，小数位最多13，小数点占1位】
        /// </summary>
        public string? price { get; set; }

        /// <summary>
        /// 单价，含税
        /// </summary>
        public string? priceTax { get; set; }

        /// <summary>
        /// 不含税金额【长度：(14,2)】，红票金额小于0，蓝票金额大于0，含税标识includeTaxFlag=1时该金额为含税
        /// </summary>
        public string? amount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string? taxRate { get; set; }

        /// <summary>
        /// 税额，若传入则校验误差[不含税金额*税率-税额≤±0.06]；【长度：(14,2)】
        /// </summary>
        public string? taxAmount { get; set; }
    }

    public class GenerateRedConfirmationFormNumberOutput
    {
        public string errorCode { get; set; }

        public bool status { get; set; }

        public string message { get; set; }

        public string confirmStatus { get; set; }

        /// <summary>
        /// base64字符串
        /// </summary>
        public string? data { get; set; }

    }

    public class GenerateRedConfirmationFormNumberDataOutput
    {
        public string? number { get; set; }

        public string? govRedConfirmBillUuid { get; set; }

        public List<RiskInfo?>? riskInfo { get; set; }

        public string? confirmStatus { get; set; }
    }

    public class RiskInfo
    {
        public string? riskNumber { get; set; }
        public string? riskMsg { get; set; }
    }

    public class ComputeAmountInput
    {
        /// <summary>
        /// 数量
        /// </summary>
        public string? quantity { get; set; }

        /// <summary>
        /// 单价，含税【长度：整数位加小数位最大16位，小数位最多13，小数点占1位】
        /// </summary>
        public string? priceTax { get; set; }

        /// <summary>
        /// 单价，不含税【长度：整数位加小数位最大16位，小数位最多13，小数点占1位】
        /// </summary>
        public string? taxRate { get; set; }
    }
    #endregion

    #region 推送初始化发票至金蝶相关Dto
    public class InitInvoicesInput
    {
        /// <summary>
        /// 开票时间 开始
        /// </summary>
        public DateTime? InvoiceTimeStart { get; set; }
        /// <summary>
        /// 开票时间 结束
        /// </summary>
        public DateTime? InvoiceTimeEnd { get; set; }
    }

    public class PushInitInvoicesInput
    {
        public List<InvoiceChildInitRequestVo> requestVoList { get; set; }
    }

    public class InvoiceChildInitRequestVo
    {
        /// <summary>
        /// 购方名称
        /// </summary>
        public string? buyerName { get; set; }
        /// <summary>
        /// 购方纳税人识别号
        /// </summary>
        public string? buyerTaxNo { get; set; }
        /// <summary>
        /// 销方名称
        /// </summary>
        public string? salerName { get; set; }
        /// <summary>
        /// 销方纳税人识别号
        /// </summary>
        public string? salerTaxNo { get; set; }
        /// <summary>
        /// 发票代码	
        /// </summary>
        public string? invoicecode { get; set; }
        /// <summary>
        /// 发票号码
        /// </summary>
        public string? invoiceno { get; set; }
        /// <summary>
        /// 收款人
        /// </summary>
        public string? payee { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string? drawer { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        public decimal? totalAmount { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public string? issuetime { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? jfzx_customerf { get; set; }
    }
    #endregion

    /// <summary>
    /// 发票云接口入参
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class SimInput
    {
        /// <summary>
        /// 接口类型
        /// </summary>
        public string requestId { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
        /// <summary>
        /// 请求id
        /// </summary>
        public string interfaceCode { get; set; } = "ALLE.INVOICE.QUERY";
        public string businessSystemCode { get; set; } = "HEPT_SYSTEM";
        /// <summary>
        /// 请求参数
        /// </summary>
        public string data { get; set; }
    }

    public class SimData
    {
        public string invoiceNum { get; set; }
        public string sellerTaxpayerId { get; set; }
    }

    public class SimDataOutput
    {
        /// <summary>
        /// PDF文件URL
        /// </summary>
        public string invoiceFileUrl { get; set; }
        /// <summary>
        /// XML文件URL
        /// </summary>
        public string xmlFileUrl { get; set; }
        /// <summary>
        /// OFD文件URL
        /// </summary>
        public string ofdFileUrl { get; set; }

        /// <summary>
        /// 发票快照URL 
        /// </summary>
        public string invoiceImageUrl { get; set; }
    }

    public class RebateProvisionSaveInput
    {
        /// <summary>
        /// 返利单编码
        /// </summary>
        public string? billno { get; set; }

        /// <summary>
        /// 计提类型,1:月末计提,2:月初冲回
        /// </summary>
        public int? jfzx_provisiontype { get; set; }

        /// <summary>
        /// 法人主体
        /// </summary>
        public string? org { get; set; }

        public List<RebateProvisionEntryModel> entrys { get; set; }
    }

    public class RebateProvisionEntryModel
    {
        /// <summary>
        /// 确认函日期
        /// </summary>
        public DateTime? jfzx_confirmationdate { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? jfzx_confirmtaxexamount { get; set; }

        /// <summary>
        /// 实际收到日期
        /// </summary>
        public DateTime? jfzx_actualarrivaldate { get; set; }

        /// <summary>
        /// 政策期限
        /// </summary>
        public string? jfzx_policydeadline { get; set; }

        /// <summary>
        /// 返利类型 A:平移返利, B:指标返利, C:补偿返利
        /// </summary>
        public string? jfzx_rebatetype { get; set; }

        /// <summary>
        /// 发票日期/优惠卷日期
        /// </summary>
        public DateTime? jfzx_coupondate { get; set; }

        /// <summary>
        /// 返利金额
        /// </summary>
        public decimal? jfzx_rebateamount { get; set; }


        /// <summary>
        /// 返利不含税金额
        /// </summary>
        public decimal? jfzx_rebatetaxamount { get; set; }

        /// <summary>
        /// 厂家红票发票号
        /// </summary>
        public string? jfzx_redinvoice { get; set; }

        /// <summary>
        /// 返利期间(摘要)
        /// </summary>
        public string? jfzx_periodsummary { get; set; }

        /// <summary>
        /// 下家对应金额
        /// </summary>
        public decimal? jfzx_nextamount { get; set; }

        /// <summary>
        /// 下家不含税金额
        /// </summary>
        public decimal? jfzx_nexttaxamount { get; set; }

        /// <summary>
        /// 下家返利方式 A:发票, B:优惠劵
        /// </summary>
        public string? jfzx_nextrebatemethod { get; set; }

        /// <summary>
        /// 下家返利发票号/优惠券(针对已结算的)
        /// </summary>
        public string? jfzx_nextinvoiceorcoupon { get; set; }

        /// <summary>
        /// 业务组织编码
        /// </summary>
        public string? jfzx_bizorg { get; set; }

        /// <summary>
        /// 项目号编码
        /// </summary>
        public string? jfzx_projectno { get; set; }

        /// <summary>
        /// 厂家/供应商编码
        /// </summary>
        public string? jfzx_supplier { get; set; }

        /// <summary>
        /// 下游客户编码
        /// </summary>
        public string? jfzx_customer { get; set; }

    }

    public class PurchaseCoreBillInput
    {
        /// <summary>
        /// 设备编码
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 资产组织/核算组织/经 办部门.编码
        /// </summary>
        public string orgNumber { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public long purchaseDate { get; set; }

        /// <summary>
        /// 建卡方式
        /// </summary>
        public string? buildway { get; set; }

        /// <summary>
        /// 经办人
        /// </summary>
        public string handlerNumber { get; set; }

        /// <summary>
        /// 业务组织.编码
        /// </summary>
        public string basedatafield { get; set; }

        /// <summary>
        /// 核心平台创建人
        /// </summary>
        public string creator { get; set; }

        /// <summary>
        /// 设备出库来源单号
        /// </summary>
        public string? deliveryNumber { get; set; }

        /// <summary>
        /// 含税总金额
        /// </summary>
        public decimal? includeTaxTotalAmoun { get; set; }

        /// <summary>
        /// 税额总额
        /// </summary>
        public decimal? totalTaxAmount { get; set; }
        /// <summary>
        /// 是否生成固定资产
        /// </summary>
        public string? fixedAssetVoucher { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public purchaseDetailInput purchaseDetail { get; set; }

    }
    public class purchaseDetailInput
    {
        /// <summary>
        /// 项目号
        /// </summary>
        public string projectNumber { get; set; }
        /// <summary>
        /// 业务组织
        /// </summary>
        public string detailbizorg { get; set; }
        /// <summary>
        /// 设备编码
        /// </summary>
        public string eid { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string? assetname { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string? model { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int assetqty { get; set; }

        /// <summary>
        /// 启用日期 时间粗
        /// </summary>
        public long? realaccountdate { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string customer { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public string? fsupplierid { get; set; }
        /// <summary>
        /// 单价（含税）
        /// </summary>
        public decimal? newunitprice { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? unitprice { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public string taxrate { get; set; }

        /// <summary>
        /// 无税金额
        /// </summary>
        public decimal? notaxamount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? taxamount { get; set; }

        /// <summary>
        /// 价税合计
        /// </summary>
        public decimal? totalamount { get; set; }
    }

    public class DeletePurchaseCoreBillInput
    {
        public List<string> billnos { get; set; }
    }

    /// <summary>
    /// 打包发送发票入参
    /// </summary>
    public class SendMailInvoicesInput
    {
        /// <summary>
        /// 开票日期始
        /// </summary>
        public DateTime? dateStart { get; set; }
        /// <summary>
        /// 开票日期止
        /// </summary>
        public DateTime? dateEnd { get; set; }
        /// <summary>
        /// 销方税号（公司税号）
        /// </summary>
        public string salertaxNo { get; set; }
        /// <summary>
        /// 购方客户编码
        /// </summary>
        public string? buyerName { get; set; }
        /// <summary>
        /// 发票集合
        /// </summary>
        public List<string?> invoiceNo { get; set; }
        /// <summary>
        /// 开票申请编码
        /// </summary>
        public string? billNo { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        public string email { get; set; }
    }

    public class BatchSaveBarterDisposeBillInput
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 库存组织
        /// </summary>
        public string jfzx_org { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public string jfzx_bizdate { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string jfzx_supplier { get; set; }

        /// <summary>
        /// 单据类型(01:经销报损,02:寄售报损,03:换货报损)
        /// </summary>
        public string jfzx_type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string jfzx_remark { get; set; }

        /// <summary>
        /// 物料明细
        /// </summary>
        public List<ApBarterDisposeEntryModel> jfzx_materialentity { get; set; }
    }

    public class ApBarterDisposeEntryModel
    {
        /// <summary>
        /// 项目号
        /// </summary>
        public string jfzx_project { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? customerId { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string jfzx_materialnumber { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal jfzx_qty { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string jfzx_bizorg { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? jfzx_invoicenumber { get; set; }

        /// <summary>
        /// 税率编码
        /// </summary>
        public string? taxrateid_number { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? jfzx_ordernumber { get; set; }

        /// <summary>
        /// 标准总成本
        /// </summary>
        public decimal? jfzx_standard_total_cost { get; set; }

        /// <summary>
        /// 实际总成本
        /// </summary>
        public decimal? jfzx_actual_total_cost { get; set; }

        /// <summary>
        /// 是否进口(0:否,1:是)
        /// </summary>
        public string? jfzx_isimported { get; set; }

        #region 库存调整新增字段
        /// <summary>
        /// 新项目号
        /// </summary>
        public string jfzx_project_new { get; set; }

        /// <summary>
        /// 新业务组织
        /// </summary>
        public string? jfzx_bizorg_new { get; set; }

        /// <summary>
        /// 新订单号
        /// </summary>
        public string? jfzx_ordernumber_new { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? jfzx_supplier { get; set; }

        /// <summary>
        /// 新供应商
        /// </summary>
        public string? jfzx_supplier_new { get; set; }
        #endregion

        /// <summary>
        /// 商品类型(A:库存,B:发出)
        /// </summary>
        public string? jfzx_productType { get; set; }

        /// <summary>
        /// 货品类型(A:寄售,B:经销)
        /// </summary>
        public string? jfzx_goodsType { get; set; }
    }

    public class RevcfmbillBatchSaveInput
    {
        /// <summary>
        /// 2单据编号
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 3单据类型.编码
        /// </summary>
        public string billtype_number { get; set; }

        /// <summary>
        /// 4确认日期
        /// </summary>
        public string bizdate { get; set; }

        /// <summary>
        /// 5往来类型
        /// </summary>
        public string asstacttype { get; set; }

        /// <summary>
        /// 6往来户.编码
        /// </summary>
        public string asstact_number { get; set; }

        /// <summary>
        /// 7结算组织.编码
        /// </summary>
        public string org_number { get; set; }

        /// <summary>
        /// 8汇率
        /// </summary>
        public decimal exchangerate { get; set; }

        /// <summary>
        /// 9确认方式
        /// </summary>
        public string confirmway { get; set; }

        /// <summary>
        /// 10汇率日期
        /// </summary>
        public DateTime? exratedate { get; set; }

        /// <summary>
        /// 收款组织.编码
        /// </summary>
        public string recorg_number { get; set; }

        /// <summary>
        /// 结算币别.货币代码
        /// </summary>
        public string currency_number { get; set; }

        /// <summary>
        /// 销售组织.编码
        /// </summary>
        public string? salesorg_number { get; set; }

        /// <summary>
        /// 款项性质.编码
        /// </summary>
        public string payproperty_number { get; set; }

        /// <summary>
        /// 汇率表.编码
        /// </summary>
        public string exratetable_number { get; set; }


        /// <summary>
        /// 19备注
        /// </summary>
        public string? remark { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string jfzx_textfield { get; set; }

        /// <summary>
        /// 业务组织.编码
        /// </summary>
        public string jfzx_bizorg_number { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal? total_amount { get; set; }
        /// <summary>
        /// 20明细
        /// </summary>
        public List<RevcfmbillBatchSaveDetail> entry { get; set; }
    }

    public class RevcfmbillBatchSaveDetail
    {
        /// <summary>
        /// 3明细.是否赠品
        /// </summary>
        public bool? e_ispresent { get; set; }

        /// <summary>
        /// 4明细.数量
        /// </summary>
        public decimal e_quantity { get; set; }

        /// <summary>
        /// 5明细.单价
        /// </summary>
        public decimal e_unitprice { get; set; }

        /// <summary>
        /// 6明细.金额
        /// </summary>
        public decimal e_amount { get; set; }

        /// <summary>
        /// 7明细.未核销金额
        /// </summary>
        public decimal? e_unverifyamt { get; set; }

        /// <summary>
        /// 8明细.折扣方式 PERCENT:折扣率(%), PERUNIT:单位折扣额, NULL:无, TOTAL:固定折扣额
        /// </summary>
        public string? e_discountmode { get; set; }

        /// <summary>
        /// 9明细.单位折扣(率)
        /// </summary>
        public decimal? e_discountrate { get; set; }

        /// <summary>
        /// 10明细.备注
        /// </summary>
        public string? e_remark { get; set; }

        /// <summary>
        /// 11物料编码.编码
        /// </summary>
        public string e_material_number { get; set; }

        /// <summary>
        ///12行类型.编码
        /// </summary>
        public string? linetype_number { get; set; }

        /// <summary>
        /// 13计量单位.编码
        /// </summary>
        public string e_measureunit_number { get; set; }

        /// <summary>
        /// 明细.总成本
        /// </summary>
        public decimal? jfzx_totalcost { get; set; }

        /// <summary>
        /// 明细.标准成本
        /// </summary>
        public decimal? jfzx_standardtotal { get; set; }

        /// <summary>
        /// 项目号.编码
        /// </summary>
        public string jfzx_projectno_number { get; set; }

        /// <summary>
        /// 母项目号.编码
        /// </summary>
        public string? jfzx_paproject_f { get; set; }

        public decimal? saleTaxRate { get; set; }

        /// <summary>
        ///	税率编码(销售)
        /// </summary>
        public string? taxrateid_number
        {
            get
            {
                if (saleTaxRate.HasValue)
                {
                    if (saleTaxRate > 0)
                    {
                        return $"V{saleTaxRate}".Trim('0').Trim('.');
                    }
                    else
                    {
                        return "V0";
                    }
                }
                else
                {
                    return "V0";
                }
            }
        }
    }


    public class AssociatedInvoicingInput
    {
        public List<AssociatedInvoicing> InvoiceAndReceivablesVos { get; set; }
    }
    public class AssociatedInvoicing
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string receivablesNumber { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNumber { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal amount { get; set; }
        [JsonProperty("operator")]
        public string? Operator { get; set; }
    }

    public class RevcfmbillBatchiscofirmInput
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 收入确认
        /// </summary>
        public bool jfzx_iscofirm { get; set; }

        /// <summary>
        /// 确认金额(不含税)
        /// </summary>
        public decimal confirmamt { get; set; }
        /// <summary>
        /// 不含税成本
        /// </summary>
        public decimal totalCost { get; set; }
    }

    public class InputBillUnassignInput
    {
        /// <summary>
        /// 发票号码
        /// </summary>
        public string invoiceno { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string user { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string associatedDate { get; set; }
    }

    /// <summary>
    /// 可认款金额返回
    /// </summary>
    public class RecognizeReceiveAmountDto
    {
        /// <summary>
        /// 本身Code
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 关联Code或id
        /// </summary>
        public List<string?> Ids { get; set; }
        /// <summary>
        /// 已认款金额
        /// </summary>
        public decimal? Amount { get; set; }
        /// <summary>
        /// 应收剩余可用金额
        /// </summary>
        public decimal? CreditSurplusTotalValue { get; set; }
        /// <summary>
        /// 应收code
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 应收id
        /// </summary>
        public Guid? CreditId { get; set; }
    }

    public class TaxClassCodeInput
    {
        /// <summary>
        /// 税收分类编码（精确） 
        /// </summary>
        public string? number { get; set; } = "";

        /// <summary>
        /// 税收分类编码（模糊）
        /// </summary>
        public string? numberLike { get; set; } = "";

        /// <summary>
        /// 大于更新时间
        /// </summary>
        public string? modifyTime { get; set; } = "";

        /// <summary>
        /// 分页页码
        /// </summary>
        public int page { get; set; }
        /// <summary>
        /// 分页记录数
        /// </summary>
        public int pageSize { get; set; }
    }

    public class TaxClassCodeOutput
    {
        public int total { get; set; }
        public List<TaxClassCodeData> queryTaxClassCode { get; set; }
    }

    public class TaxClassCodeData
    {
        /// <summary>
        /// 税收分类编码（精确） 
        /// </summary>
        public string? number { get; set; }

        /// <summary>
        /// 税收分类编码（模糊）
        /// </summary>
        public string? name { get; set; }

        /// <summary>
        /// 合并编码
        /// </summary>
        public string? mergeCode { get; set; }

        /// <summary>
        /// 简称
        /// </summary>
        public string? simpleName { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxRate { get; set; }

        /// <summary>
        /// 使用状态('0'=禁用,'1'=可用)
        /// </summary>
        public string? enable { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string? description { get; set; }

        /// <summary>
        /// 上级编码
        /// </summary>
        public string? parentNumber { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public long modifyTime { get; set; }
    }

    public class QueryBeBankInput
    {
        /// <summary>
        /// 国内/国外银行枚举（精准）(1:境内,2:境外)
        /// </summary>
        public string? country { get; set; }

        /// <summary>
        /// 联行号（精准）
        /// </summary>
        public string? unionNumber { get; set; }

        /// <summary>
        /// 银行名称（模糊）
        /// </summary>
        public string? nameLike { get; set; }

        /// <summary>
        /// 银行名称（精确）
        /// </summary>
        public string? name { get; set; }

        /// <summary>
        /// 大于更新时间
        /// </summary>
        public string? modifyTime { get; set; }

        /// <summary>
        /// 分页页码
        /// </summary>
        public int page { get; set; }
        /// <summary>
        /// 分页记录数
        /// </summary>
        public int pageSize { get; set; }

    }

    public class QueryBeBankOutput
    {
        public int total { get; set; }
        public List<QueryBeBankData> queryBeBank { get; set; }

    }

    public class QueryBeBankData
    {
        /// <summary>
        /// 银行名称 
        /// </summary>
        public string? name { get; set; }

        /// <summary>
        /// 国家地区 
        /// </summary>
        public string? countryName { get; set; }

        /// <summary>
        /// 省份 
        /// </summary>
        public string? provinceTxt { get; set; }

        /// <summary>
        /// 城市 
        /// </summary>
        public string? cityTxt { get; set; }

        /// <summary>
        /// 联行号 
        /// </summary>
        public string? unionNumber { get; set; }

        /// <summary>
        /// 疑似过期(Y：是，N：否) 
        /// </summary>
        public string? isOverdue { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public long modifyTime { get; set; }

        /// <summary>
        ///使用状态(0：禁用，1：启用)
        /// </summary>
        public string? enable { get; set; }
    }
    public class CheckDataForKingdeeInput
    {
        public CheckDataForKingdeeDataInput data { get; set; }
        public int pageNo { get; set; } = 1;
        public int pageSize { get; set; } = 10;
    }

    public class CheckDataForKingdeeDataInput
    {
        public string billno { get; set; }
        public string org_number { get; set; }
    }

    public class CheckForKingdeeInput
    {
        public string billCode { get; set; }
        public string org_number { get; set; }
    }
    /// <summary>
    /// 应付退款结算
    /// </summary>
    public class DebtRefundSettleInput
    {
        /// <summary>
        /// 主方结算金额
        /// </summary>
        public decimal mainSettleAmt { get; set; }

        /// <summary>
        /// 主方单据编码(财务应付单)
        /// </summary>
        public string mainBill { get; set; }

        /// <summary>
        /// 辅方单据编码(收款单)
        /// </summary>
        public string asstBill { get; set; }

        /// <summary>
        /// 辅方结算金额
        /// </summary>
        public decimal asstSettleAmt { get; set; }

        /// <summary>
        /// 单次冲销
        /// </summary>
        public bool? singleSettle { get; set; }
    }

    /// <summary>
    /// 库存调整入参
    /// </summary>
    public class ApBarterDisposeBillModel
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string? billno { get; set; }

        /// <summary>
        /// 库存组织
        /// </summary>
        public string? jfzx_org { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public DateTime? jfzx_bizdate { get; set; }

        /// <summary>
        /// 单据类型(01:项目更换,02:部门更换,03:订单更换)
        /// </summary>
        public string? jfzx_type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? jfzx_remark { get; set; }

        /// <summary>
        /// 单据编号
        /// </summary>
        public List<ApBarterDisposeEntryModel>? jfzx_materialentity { get; set; }

    }

    /// <summary>
    /// 保存退款至本地入参
    /// </summary>
    public class RefundSaveInput : RefundItemPo
    {
        /// <summary>
        /// 详情
        /// </summary>
        public List<RefundDetailPo>? List { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? userName { get; set; }
        /// <summary>
        /// 退款形式 客商退我方
        /// </summary>
        public int? RefundParty { get; set; }
    }

    /// <summary>
    /// 提交本地退款至金蝶
    /// </summary>
    public class RefundSubmitInput
    {
        /// <summary>
        /// id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string? userName { get; set; }

        public int? refundParty { get; set; }
    }

    /// <summary>
    /// 库存更换项目入参
    /// </summary>
    public class ChangeProjectInput
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 公司code
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public long? BillDate { get; set; }

        /// <summary>
        /// 单据类型(01:项目更换,02:部门更换,03:订单更换)
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<ChangeProjectDetailInput>? List { get; set; }
    }

    public class ChangeProjectDetailInput
    {
        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string ProjectCode { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string BusinessDepId { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 含税成本单价
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardCost { get; set; }

        /// <summary>
        /// 不含税成本单价
        /// </summary>
        public decimal? NoRateUnitCost { get; set; }

        /// <summary>
        /// 是否进口(0:否,1:是)
        /// </summary>
        public string? IsImported { get; set; }

        #region 库存调整新增字段
        /// <summary>
        /// 新项目号
        /// </summary>
        public string? NewProjectCode { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentId { get; set; }
        #endregion

        /// <summary>
        /// 商品类型(A:库存,B:发出)
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 货品类型(A:寄售,B:经销)
        /// </summary>
        public string? Mark { get; set; }
    }

    /// <summary>
    /// 库存更换部门入参
    /// </summary>
    public class ChangeDeptInput
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 公司code
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public long? BillDate { get; set; }

        /// <summary>
        /// 单据类型(01:项目更换,02:部门更换,03:订单更换)
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<ChangeDeptDetailInput>? List { get; set; }
    }

    public class ChangeDeptDetailInput
    {
        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 项目号
        /// </summary>
        public string ProjectCode { get; set; }

        /// <summary>
        /// 新项目号
        /// </summary>
        public string? NewProjectCode { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 业务组织
        /// </summary>
        public string BusinessDepId { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 含税成本单价
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// 标准成本单价
        /// </summary>
        public decimal? StandardCost { get; set; }
        /// <summary>
        /// 不含税成本单价
        /// </summary>
        public decimal? NoRateUnitCost { get; set; }

        /// <summary>
        /// 是否进口(0:否,1:是)
        /// </summary>
        public string? IsImported { get; set; }

        #region 库存调整新增字段
        /// <summary>
        /// 新部门
        /// </summary>
        public string? NewBusinessDepId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentId { get; set; }
        #endregion

        /// <summary>
        /// 商品类型(A:库存,B:发出)
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 货品类型(A:寄售,B:经销)
        /// </summary>
        public string? Mark { get; set; }
    }

    public class BatchSaveBarterDisposeBillByStagingInput
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 公司code
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public long? BillDate { get; set; }

        /// <summary>
        /// 单据类型(01:项目更换,02:部门更换,03:订单更换)
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<BatchSaveBarterDisposeBillByStagingDetails>? List { get; set; }
    }

    public class BatchSaveBarterDisposeBillByStagingDetails
    {
        /// <summary>
        /// 项目号
        /// </summary>
        public string ProjectCode { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>

        public string? CustomerId { get; set; }

        /// <summary>
        /// 新项目号
        /// </summary>
        public string? NewProjectCode { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 业务组织（部门）
        /// </summary>
        public string BusinessDepId { get; set; }

        /// <summary>
        /// 新业务组织（部门）
        /// </summary>
        public string? NewBusinessDepId { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 含税成本单价
        /// </summary>
        public decimal? UnitCost { get; set; }
        /// <summary>
        /// 标准成本单价
        /// </summary>
        public decimal? StandardCost { get; set; }
        /// <summary>
        /// 不含税成本单价
        /// </summary>
        public decimal? NoRateUnitCost { get; set; }

        /// <summary>
        /// 商品类型(A:库存,B:发出)
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 货品类型(A:寄售,B:经销)
        /// </summary>
        public string? Mark { get; set; }
    }

    // 定义退款请求体模型类
    public class RefundRequestBodyModel
    {
        ///// <summary>
        ///// 公司编号列表，允许传入多个公司编号
        ///// </summary>
        //public List<string> paymentNum { get; set; }
        ///// <summary>
        ///// 核算部门编号列表，允许传入多个核算部门编号
        ///// </summary>
        //public List<string> orgNum { get; set; }
        ///// <summary>
        ///// 开始日期
        ///// </summary>
        //public DateTime? startDate { get; set; }
        ///// <summary>
        ///// 结束日期
        ///// </summary>
        //public DateTime? endDate { get; set; }
        ///// <summary>
        ///// 客户编号列表，允许传入多个客户编号
        ///// </summary>
        //public List<string> customerNum { get; set; }
        /// <summary>
        /// 付款申请单号列表，允许传入多个单号
        /// </summary>
        public List<string> billNo { get; set; }
        ///// <summary>
        ///// 付款类型列表，允许传入多种付款类型
        ///// </summary>
        //public List<string> paymentType { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int pageindex { get; set; }
        /// <summary>
        /// 每页数量
        /// </summary>
        public int pagesize { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string billStatus { get; set; }
        ///// <summary>
        ///// 付款状态
        ///// </summary>
        //public string paidStatus { get; set; }
    }

    // 定义一个包含退款请求体模型的外层类
    public class QueryRefundDataInput
    {
        /// <summary>
        /// 包含查询退款数据所需的所有详细信息
        /// </summary>
        public RefundRequestBodyModel data { get; set; }
    }
    public class ReceiveAbtPaymentInput
    {
        public List<RecPaySettleRequestDto> data { get; set; }

    }
    public class RecPaySettleRequestDto
    {
        /// <summary>
        /// 主方单据编码（收款处理单）
        /// </summary>

        public string mainBill { get; set; }
        /// <summary>
        /// 主方结算金额
        /// </summary>

        public decimal mainSettleAmt { get; set; }
        /// <summary>
        /// 辅方单据编码（付款处理单）
        /// </summary>

        public string asstBill { get; set; }
        /// <summary>
        /// 辅方结算金额
        /// </summary>

        public decimal asstSettleAmt { get; set; }
    }

    public class ReverseSettlementInput
    {
        public List<ReverseSettlementRequestDto> data { get; set; }
    }

    public class ReverseSettlementRequestDto
    {
        /// <summary>
        /// 主方单号
        /// </summary>
        public string mainBillNo { get; set; }
        /// <summary>
        /// 反结算金额
        /// </summary>
        public decimal reverseAmount { get; set; }
        /// <summary>
        /// 主方单号
        /// </summary>
        public string asstBillNo { get; set; }
    }

    public class KingdeeAdjustDebt : KingdeeDebt
    {
        /// <summary>
        /// 关联单据号
        /// </summary>
        public string associatedNumber { get; set; }

        /// <summary>
        /// 新业务单号
        /// </summary>
        public string newBusinessNumber { get; set; }

        /// <summary>
        /// 调整单标识,A-源单,B-冲销单,C-新单
        /// </summary>
        public string adjustmentFlag { get; set; }

        public new decimal pricetaxtotal
        {
            get
            {
                return Math.Round(pricetaxtotal4, 2);
            }
        }
    }
    /// <summary>
    /// 保存损失确认负数应付单
    /// </summary>
    public class SaveLossRecognitionDebtInput
    {
        /// <summary>
        /// 单据id，唯一标识单据
        /// </summary>
        public long? id { get; set; }

        /// <summary>
        /// 单据编号，用于识别单据
        /// </summary>
        public string? billNo { get; set; }

        /// <summary>
        /// 发票号，关联发票的编号
        /// </summary>
        public string? invoiceNo { get; set; }

        /// <summary>
        /// 单据类型编码，指定单据的类型
        /// </summary>
        public string? billTypeIdNumber { get; set; }

        /// <summary>
        /// 结算组织编码，标识结算的组织
        /// </summary>
        public string? orgNumber { get; set; }

        /// <summary>
        /// 是否冲应收，指示单据是否用于冲抵应收款项
        /// </summary>
        public bool? hedgeReceivable { get; set; }

        /// <summary>
        /// 应付金额，包含税的应付总金额
        /// </summary>
        public decimal? priceTaxTotal { get; set; }

        /// <summary>
        /// 金额，不包含税的金额
        /// </summary>
        public decimal? amount { get; set; }

        /// <summary>
        /// 创建人名称，创建该单据的人员姓名
        /// </summary>
        public string? creator { get; set; }

        /// <summary>
        /// 单据日期，单据创建的日期
        /// </summary>
        public DateTime? bizdate { get; set; }

        /// <summary>
        /// 到期日，单据的到期日期
        /// </summary>
        public DateTime? duedate { get; set; }

        /// <summary>
        /// 记账日期，单据记账的日期
        /// </summary>
        public string? bookDate { get; set; }

        /// <summary>
        /// 往来类型，指定单据的往来业务类型
        /// </summary>
        public string? asstActType { get; set; }

        /// <summary>
        /// 往来户编码，标识往来的客户或供应商
        /// </summary>
        public string? asstActNumber { get; set; }

        /// <summary>
        /// 付款方式，指定付款的方式
        /// </summary>
        public string? purMode { get; set; }

        /// <summary>
        /// 结算币别 货币代码，指定结算使用的货币
        /// </summary>
        public string? currencyNumber { get; set; }

        /// <summary>
        /// 汇率，用于货币换算的汇率
        /// </summary>
        public decimal? exchangeRate { get; set; }

        /// <summary>
        /// 汇率表编码，关联的汇率表编号
        /// </summary>
        public string? exRateTableNumber { get; set; }

        /// <summary>
        /// 付款组织编码，标识付款的组织
        /// </summary>
        public string? payOrgNumber { get; set; }

        /// <summary>
        /// 款项性质编码，指定款项的性质
        /// </summary>
        public string? payPropertyNumber { get; set; }

        /// <summary>
        /// 业务组织，关联的业务组织编号
        /// </summary>
        public string? businessNumber { get; set; }

        /// <summary>
        /// 订单号，关联的订单编号
        /// </summary>
        public string? orderNumber { get; set; }

        /// <summary>
        /// 付款条件编码，指定付款的条件
        /// </summary>
        public string? payCondNumber { get; set; }

        /// <summary>
        /// 结算方式，指定结算的方式
        /// </summary>
        public string? settlementTypeNumber { get; set; }

        /// <summary>
        /// 是否期初，指示单据是否为期初数据
        /// </summary>
        public bool? isPeriod { get; set; }

        /// <summary>
        /// 录入含税价，指示录入的价格是否包含税
        /// </summary>
        public bool? isIncludeTax { get; set; }

        /// <summary>
        /// 是否返利，指示单据是否包含返利
        /// </summary>
        public bool? rebate { get; set; }

        /// <summary>
        /// 部门编码，关联的部门编号
        /// </summary>
        public string? departmentNumber { get; set; }

        /// <summary>
        /// 汇率日期，汇率生效的日期
        /// </summary>
        public DateTime? exratedate { get; set; }

        /// <summary>
        /// 是否录入总价，指示录入的是否为总价
        /// </summary>
        public bool? isPriceTotal { get; set; }

        /// <summary>
        /// 采购组织编码，标识采购的组织
        /// </summary>
        public string? purOrgNumber { get; set; }

        /// <summary>
        /// 采购组编码，标识采购的小组
        /// </summary>
        public string? purDeptNumber { get; set; }

        /// <summary>
        /// 采购员 业务员编码，标识负责采购的人员
        /// </summary>
        public string? purchaserOperatorNumber { get; set; }

        /// <summary>
        /// 质保金比例（%），质保金占总金额的比例
        /// </summary>
        public decimal? premiumRate { get; set; }

        /// <summary>
        /// 质保金金额，质保金的具体金额
        /// </summary>
        public decimal? premiumAmt { get; set; }

        /// <summary>
        /// 保证金到期日，质保金的到期日期
        /// </summary>
        public DateTime? premduedate { get; set; }

        /// <summary>
        /// 收款账号，收款的银行账号
        /// </summary>
        public string? payeeBankNum { get; set; }

        /// <summary>
        /// 收款银行编码，收款银行的编号
        /// </summary>
        public string? beBankNumber { get; set; }

        /// <summary>
        /// 备注，对单据的额外说明
        /// </summary>
        public string? remark { get; set; }

        /// <summary>
        /// 是否暂存转采购，指示单据是否从暂存状态转为采购状态
        /// </summary>
        public bool? stagingPurchase { get; set; }

        /// <summary>
        /// 关联单号，关联的其他单据编号
        /// </summary>
        public string? associatedNumber { get; set; }
    }
    /// <summary>
    ///  保存损失确认负数应收单
    /// </summary>
    public class SaveLossRecognitionCreditInput
    {
        /// <summary>
        /// id，唯一标识单据
        /// </summary>
        public long? id { get; set; }

        /// <summary>
        /// 单据编号，用于识别单据
        /// </summary>
        public string billno { get; set; }

        /// <summary>
        /// 单据类型编码，指定单据的类型
        /// </summary>
        public string billtype_number { get; set; }

        /// <summary>
        /// 结算组织编码，标识结算的组织
        /// </summary>
        public string org_number { get; set; }

        /// <summary>
        /// 单据日期，单据创建的日期
        /// </summary>
        public string bizdate { get; set; }

        /// <summary>
        /// 记账日期，单据记账的日期
        /// </summary>
        public string? bookDate { get; set; }

        /// <summary>
        /// 往来类型，指定单据的往来业务类型
        /// </summary>
        public string asstacttype { get; set; }

        /// <summary>
        /// 往来户编码，标识往来的客户或供应商
        /// </summary>
        public string asstact_number { get; set; }

        /// <summary>
        /// 收款组织编码，标识收款的组织
        /// </summary>
        public string recorg_number { get; set; }

        /// <summary>
        /// 付款方式，指定付款的方式
        /// </summary>
        public string paymode { get; set; }

        /// <summary>
        /// 款项性质编码，指定款项的性质
        /// </summary>
        public string payproperty_number { get; set; }

        /// <summary>
        /// 结算币别货币代码，指定结算使用的货币
        /// </summary>
        public string currency_number { get; set; }

        /// <summary>
        /// 汇率表编码，关联的汇率表编号
        /// </summary>
        public string exratetable_number { get; set; }

        /// <summary>
        /// 汇率，用于货币换算的汇率
        /// </summary>
        public decimal? exchangerate { get; set; }

        /// <summary>
        /// 到期日，单据的到期日期
        /// </summary>
        public DateTime? duedate { get; set; }

        /// <summary>
        /// 应收金额，应收取的金额
        /// </summary>
        public decimal? recamount { get; set; }

        /// <summary>
        /// 金额，不包含税等其他费用的金额
        /// </summary>
        public decimal? amount { get; set; }

        /// <summary>
        /// 总成本，业务的总成本
        /// </summary>
        public decimal? jfzx_alltotalcost { get; set; }

        /// <summary>
        /// 汇率日期，汇率生效的日期
        /// </summary>
        public DateTime? exratedate { get; set; }

        /// <summary>
        /// 收款条件编码，指定收款的条件
        /// </summary>
        public string paycond_number { get; set; }

        /// <summary>
        /// 结算方式编码，指定结算的方式
        /// </summary>
        public string settlementtype_number { get; set; }

        /// <summary>
        /// 部门编码，关联的部门编号
        /// </summary>
        public string department_number { get; set; }

        /// <summary>
        /// 销售组织编码，标识销售的组织
        /// </summary>
        public string salesorg_number { get; set; }

        /// <summary>
        /// 销售组编码，标识销售的小组
        /// </summary>
        public string salesgroup_number { get; set; }

        /// <summary>
        /// 销售员业务员编码，标识负责销售的人员
        /// </summary>
        public string salesman_operatornumber { get; set; }

        /// <summary>
        /// 是否期初，指示单据是否为期初数据
        /// </summary>
        public bool? isperiod { get; set; }

        /// <summary>
        /// 录入含税价，指示录入的价格是否包含税
        /// </summary>
        public bool? isincludetax { get; set; }

        /// <summary>
        /// 收入确认，指示收入是否已确认
        /// </summary>
        public bool? jfzx_iscofirm { get; set; }

        /// <summary>
        /// 录入总价，指示录入的是否为总价
        /// </summary>
        public bool? ispricetotal { get; set; }

        /// <summary>
        /// 备注，对单据的额外说明
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// 业务组织，关联的业务组织编号
        /// </summary>
        public string jfzx_businessnumber { get; set; }

        /// <summary>
        /// 创建人名称，创建该单据的人员姓名
        /// </summary>
        public string jfzx_creator { get; set; }

        /// <summary>
        /// 关联单号，关联的其他单据编号
        /// </summary>
        public string associatedNumber { get; set; }

        /// <summary>
        /// 订单号，关联的订单编号
        /// </summary>
        public string jfzx_ordernumber { get; set; }

        /// <summary>
        /// 业务类型：A 直销;B 分销
        /// </summary>
        public string jfzx_businesstype { get; set; }

        /// <summary>
        /// 是否返利，指示单据是否包含返利
        /// </summary>
        public bool? jfzx_rebate { get; set; }

        /// <summary>
        /// 业务单元，关联的业务单元编号
        /// </summary>
        public string jfzx_serviceid { get; set; }
    }

    /// <summary>
    /// 损失确认回滚应收入参
    /// </summary>
    public class RollBackLossRecognitionCreditInput
    {
        /// <summary>
        /// 单据编号（应收单号）
        /// </summary>
        public string? billNo { get; set; }
    }

    /// <summary>
    /// 损失确认回滚应付入参
    /// </summary>
    public class RollBackLossRecognitionDebtInput
    {
        /// <summary>
        /// 单据编号（应付单号）
        /// </summary>
        public string? billNo { get; set; }
    }


    /// <summary>
    /// 撤销认款（包含明细）
    /// </summary>
    public class QuashAcceptancesRequestVo
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        public string? billNo { get; set; }
        /// <summary>
        /// 认款类型 （A发票，B订单，C应收单，D暂收款）
        /// </summary>
        public string? subscriptionType { get; set; }
        /// <summary>
        /// 明细单号（货款传）
        /// </summary>
        public List<string>? subscriptionNumList { get; set; }
        /// <summary>
        /// 客户信息及金额（暂收款传）
        /// </summary>
        public List<AcceptancesCustomerModel>? customerModel { get; set; }
        
        /// <summary>
        /// 实际撤销日期
        /// </summary>
        public string? ActualDate{get; set;}
    }

    /// <summary>
    /// 客户信息及金额
    /// </summary>
    public class AcceptancesCustomerModel
    {
        /// <summary>
        /// 客户
        /// </summary>
        public string? customer { get; set; }
        /// <summary>
        /// 认款金额
        /// </summary>
        public decimal? subscriptionAmount { get; set; }
    }

    /// <summary>
    /// 查询在途结算数据入参
    /// </summary>
    public class NoPostBackSettleRequestVo
    {
        /// <summary>
        /// 组织简码
        /// </summary>
        public string? orgNumber { get; set; }
        /// <summary>
        /// 截止日期
        /// </summary>
        public string? endDate { get; set; }
    }

    /// <summary>
    /// 查询在途结算数据出参
    /// </summary>
    public class NoPostBackSettleResponseVo
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string? number { get; set; }
    }

    /// <summary>
    /// 查询合同台账入参
    /// </summary>
    public class QueryContractBillInput
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageSize { get; set; } = 10;
        /// <summary>
        /// 页码
        /// </summary>
        public int PageNo { get; set; } = 1;
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 公司简码
        /// </summary>
        public string? NameCode { get; set; }
    }

    /// <summary>
    /// 查询合同台账金蝶入参
    /// </summary>
    public class QueryContractBillByKingdeeInput
    {
        /// <summary>
        /// 主要查询参数
        /// </summary>
        public ContractQueryInput Data { get; set; }
        /// <summary>
        /// 分页参数，查询页码 1
        /// </summary>
        public int PageNo { get; set; }
        /// <summary>
        /// 分页参数，分页数量 10
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// 合同主要查询参数
    /// </summary>
    public class ContractQueryInput
    {
        /// <summary>
        /// 核算组织.编码
        /// </summary>
        public string? ContractingPartyNumber { get; set; }
        /// <summary>
        /// 签约方名称
        /// </summary>
        public string? CompanyNumber { get; set; }
    }

    /// <summary>
    /// 查询合同台账出参
    /// </summary>
    public class QueryContractBillByKingdeeOutput
    {
        /// <summary>
        /// 过滤条件
        /// </summary>
        public string? Filter { get; set; }
        /// <summary>
        /// 是否最后一页
        /// </summary>
        public bool? LastPage { get; set; }
        /// <summary>
        /// 分页参数，查询页码 1
        /// </summary>
        public int? PageNo { get; set; }
        /// <summary>
        /// 分页参数，分页数量 10
        /// </summary>
        public int? PageSize { get; set; }
        /// <summary>
        /// 合同列表
        /// </summary>
        public List<ContractQueryOutput?>? Rows { get; set; }
        /// <summary>
        /// 总数
        /// </summary>
        public int? TotalCount { get; set; }
    }

    /// <summary>
    /// 合同列表
    /// </summary>
    public class ContractQueryOutput
    {
        /// <summary>
        /// 合同号
        /// </summary>
        public string? ContractCode { get; set; }
        /// <summary>
        /// 合同类型.编码
        /// </summary>
        public string? ContractTypeNumber { get; set; }
        /// <summary>
        /// 合同类型.名称
        /// </summary>
        public string? ContractTypeName { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 单据状态（A:暂存, B:已提交, C:审核中, D:审核未通过, E:审核通过, F:执行中, J:冻结, G:已付款, H:废弃, I:关闭）
        /// </summary>
        public string? BillStatus { get; set; }
    }
    /// <summary>
    /// 购货修订明细
    /// </summary>
    public class ApFinApPurchaseOrderAmendmentModel
    {
        /// <summary>
        /// 设备编码
        /// </summary>
        public string equipmentCode { get; set; }

        /// <summary>
        ///修订后的设备总金额（不含税） 
        /// </summary>
        public decimal revisedTotalAmountExclTax { get; set; }
    }

    /// <summary>
    /// 中标服务费入参
    /// </summary>
    public class PrepayBillInput
    {
        /// <summary>
        /// 单据编码
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 申请日期
        /// </summary>
        public string? BizDate { get; set; }

        /// <summary>
        /// 核算组织编码
        /// </summary>
        public string CostCompanyNumber { get; set; }

        /// <summary>
        /// 往来单位编码
        /// </summary>
        public string BillPayerNumber { get; set; }

        /// <summary>
        /// 业务组织编码
        /// </summary>
        public string BizOrgNumber { get; set; }

        /// <summary>
        /// 预计冲销日期
        /// </summary>
        public string? RepaymentDate { get; set; }

        /// <summary>
        /// 事由
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        public string Applicant { get; set; }

        /// <summary>
        /// 预付信息
        /// </summary>
        public List<PrepaymentBillExpenseEntryVo> ExpenseEntryVoList { get; set; }

        /// <summary>
        /// 收款信息
        /// </summary>
        public List<PrepaymentBillAccountEntryVo> AccountEntryVoList { get; set; }

        /// <summary>
        /// 预付信息
        /// </summary>
        public class PrepaymentBillExpenseEntryVo
        {
            /// <summary>
            /// 预付信息申请金额
            /// </summary>
            public decimal ExpenseAmount { get; set; }

            /// <summary>
            /// 项目号编码
            /// </summary>
            public string ProjectNumber { get; set; }

            /// <summary>
            /// 母项目号编码
            /// </summary>
            public string ParentProjectNumber { get; set; }

            /// <summary>
            /// 费用项目编码
            /// </summary>
            public string ExpenseItemNumber { get; set; }

            /// <summary>
            /// 预付信息备注
            /// </summary>
            public string Remark { get; set; }
        }

        /// <summary>
        /// 收款信息
        /// </summary>
        public class PrepaymentBillAccountEntryVo
        {
            /// <summary>
            /// 收款信息.收款人类型 bd_supplier:供应商, bd_customer:客户, bos_org:内部公司, er_payeer:个人, other:其他
            /// </summary>
            public string PayerType { get; set; }

            /// <summary>
            /// 支付方式编码
            /// </summary>
            public string PayModeNumber { get; set; }

            /// <summary>
            /// 银行账号
            /// </summary>
            public string PayerAccount { get; set; }

            /// <summary>
            /// 账户名称
            /// </summary>
            public string PayerAccountName { get; set; }

            /// <summary>
            /// 开户银行编码
            /// </summary>
            public string PayerBankNumber { get; set; }

            /// <summary>
            /// 收款人
            /// </summary>
            public string PayerName { get; set; }
        }

        /// <summary>
        /// 保存负数开票申请
        /// </summary>
        public class saveRedBillingApplicationInput
        {
            /// <summary>
            /// 组织id
            /// </summary>
            public string orgid { get; set; }
            /// <summary>
            /// 单据编码
            /// </summary>
            public string BillNo { get; set; }
            /// <summary>
            /// 单据日期
            /// </summary>
            public string billdate { get; set; }
            /// <summary>
            /// 单据类型
            /// </summary>
            public string billsourcetype { get; set; }

            /// <summary>
            /// 发票种类
            /// </summary>
            public string invoicetype { get; set; }
            /// <summary>
            /// 征税方式
            /// </summary>
            public string taxationstyle { get; set; }
            /// <summary>
            /// 特殊票种
            /// </summary>
            public string specialtype { get; set; }
            /// <summary>
            /// 交付手机
            /// </summary>
            public string buyerphone { get; set; }

            /// <summary>
            /// 交付邮箱
            /// </summary>
            public string buyeremail { get; set; }
            /// <summary>
            /// 发票备注
            /// </summary>
            public string? invoiceremark { get; set; }
            /// <summary>
            /// 审批备注
            /// </summary>
            public string? auditsuggestion { get; set; }
            /// <summary>
            /// 购方名称
            /// </summary>
            public string buyername { get; set; }

            /// <summary>
            /// 购方纳税识别号
            /// </summary>
            public string buyertaxno { get; set; }
            /// <summary>
            /// 购方地址及电话
            /// </summary>
            public string buyeraddr { get; set; }
            /// <summary>
            /// 购方开户行及账号
            /// </summary>
            public string buyerbank { get; set; }
            /// <summary>
            /// 单据性质
            /// </summary>
            public string billproperties { get; set; }
            /// <summary>
            /// 是否销售折让: Y/N
            /// </summary>
            public string salesDiscount { get; set; }
            /// <summary>
            /// 申请方
            /// </summary>

            [JsonProperty("applicant", NullValueHandling = NullValueHandling.Ignore)]
            public string? applicant { get; set; }

            /// <summary>
            /// 红字信息表编号/红字确认单编号
            /// </summary>
            [JsonProperty("infocode", NullValueHandling = NullValueHandling.Ignore)]
            public List<string>? infocode { get; set; }

            /// <summary>
            /// 冲红原因
            /// </summary>

            [JsonProperty("redreason", NullValueHandling = NullValueHandling.Ignore)]
            public string? redreason { get; set; }

            /// <summary>
            /// 待冲蓝票开票日期
            /// </summary>
            public DateTime originalIssueTime { get; set; }

            /// <summary>
            /// 待冲蓝票号码
            /// </summary>
            [JsonProperty("originalinvoiceno", NullValueHandling = NullValueHandling.Ignore)]
            public string? originalInvoiceNo { get; set; }

            /// <summary>
            /// 待冲蓝票代码
            /// </summary>
            [JsonProperty("originalInvoiceCode", NullValueHandling = NullValueHandling.Ignore)]
            public string? originalInvoiceCode { get; set; }

            /// <summary>
            /// 用户名
            /// </summary>
            public string username { get; set; }

            /// <summary>
            /// 客户
            /// </summary>
            public string? customer { get; set; }

            /// <summary>
            /// 是否预开票
            /// </summary>
            public bool preInvoicing { get; set; } = false;

            /// <summary>
            /// 关联单号(订单号)
            /// </summary>
            public string orderNumber { get; set; }

            /// <summary>
            /// 预开票项目号
            /// </summary>
            public string? itemNumber { get; set; }

            /// <summary>
            /// 核算部门(业务组织)
            /// </summary>
            public string businessOrg { get; set; }
            /// <summary>
            /// 商品详情
            /// </summary>
            public List<RedOriginalBillEntryModel> originalBillEntry { get; set; }
            /// <summary>
            /// 应收单分录
            /// </summary>
            public List<RedOriginalBillArEntryModel> arEntryList { get; set; }
        }

        /// <summary>
        /// 推送运营制作开票单-商品详情
        /// </summary>
        public class RedOriginalBillEntryModel
        {
            /// <summary>
            /// 商品名称
            /// </summary>
            public string goodsName { get; set; }

            /// <summary>
            /// 税收分类编码名
            /// </summary>
            public string taxRateCodeId { get; set; }
            /// <summary>
            /// 规格型号
            /// </summary>
            public string specification { get; set; }

            /// <summary>
            /// 计量单位
            /// </summary>
            public string unit { get; set; }


            /// <summary>
            /// 数量
            /// </summary>
            public decimal num { get; set; }

            /// <summary>
            /// 金额含税
            /// </summary>
            public decimal taxAmount { get; set; }

            /// <summary>
            /// 税额
            /// </summary>
            public decimal tax { get; set; }
            /// <summary>
            /// 剩余可申请金额
            /// </summary>
            public decimal remainValidAmount { get; set; }

            /// <summary>
            /// 是否赠品
            /// </summary>
            public bool gift { get; set; }

            /// <summary>
            /// 是否享受优惠
            /// </summary>
            public string policylogo { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string remark { get; set; }

            /// <summary>
            /// 行性质 1:折扣行,2:商品行
            /// </summary>
            public string rowtype { get; set; }

            /// <summary>
            /// 税率
            /// </summary>
            public decimal taxrate { get; set; }

            /// <summary>
            /// 应收单号
            /// </summary>
            public string receivableno { get; set; }

            /// <summary>
            /// 发票序号
            /// </summary>
            public int invoiceSeq { get; set; }
        }
        public class RedOriginalBillArEntryModel
        {
            /// <summary>
            /// 应收单号
            /// </summary>
            public string arBillNum { get; set; }
            /// <summary>
            /// 操作人
            /// </summary>
            public string user { get; set; }
            /// <summary>
            /// 关联日期
            /// </summary>
            public string associatedDate { get; set; }

            /// <summary>
            /// 税率明细
            /// </summary>
            public List<taxRateDetail> taxRateDetails { get; set; } = new List<taxRateDetail>();
            /// <summary>
            /// 业务组织
            /// </summary>
            public string bizOrg { get; set; }
            /// <summary>
            /// 客户
            /// </summary>
            public string customer { get; set; }
            /// <summary>
            /// 项目号
            /// </summary>
            public string project { get; set; }
            /// <summary>
            /// 订单号
            /// </summary>
            public string order { get; set; }
        }
        public class BatchUpdateOrderNumberInput
        {
            /// <summary>
            /// 认款单号
            /// </summary>
            public string BillNo { get; set; }

            /// <summary>
            /// 订单号列表
            /// </summary>
            public List<OrderNumberUpdateEntry> AcceptanceEntryList { get; set; }
        }

        public class OrderNumberUpdateEntry
        {
            /// <summary>
            /// 订单号
            /// </summary>
            public string OrderNumber { get; set; }

            /// <summary>
            /// 原始订单号
            /// </summary>
            public string OriginalOrderNumber { get; set; }
        }
    }
}
