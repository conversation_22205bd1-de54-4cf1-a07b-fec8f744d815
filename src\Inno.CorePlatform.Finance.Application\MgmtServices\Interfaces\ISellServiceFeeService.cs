﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 订单修订
    /// </summary>
    public interface ISellReviseAppService: ISellAppService
    {
        Task<BaseResponseData<List<KingdeeCredit>>> GetSaleReviseKingdeeCreditParams(EventBusDTO dto);
    }
}
