using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class PurchaseOrderAppService : BaseAppService, IPurchaseOrderAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IPurchasePayPlanRepository _purchasePayPlanRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IBaseAllQueryService<AbatementPo> _abatementQueryService;
        private readonly IBaseAllQueryService<PaymentPo> _paymentQueryService;
        private readonly DaprClient _daprClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly IStoreInApplyApiClient _storeInApplyApiClient;
        private readonly ILogger<PurchaseOrderAppService> _logger;
        private IEasyCachingProvider _easyCaching;

        public PurchaseOrderAppService(
            IPaymentRepository paymentRepository,
            IPurchasePayPlanRepository purchasePayPlanRepository,
            ISubLogService subLogRepository,
            ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            IPurchaseApiClient purchaseApiClient,
            IUnitOfWork unitOfWork,
            DaprClient daprClient,
            IKingdeeApiClient kingdeeApiClient,
            IDomainEventDispatcher? deDispatcher,
            IBaseAllQueryService<AbatementPo> abatementQueryService,
            IBaseAllQueryService<PaymentPo> paymentQueryService,
            IDebtDetailRepository debtDetailRepository,
            IExchangeRateService exchangeRateService,
            IStoreInApplyApiClient storeInApplyApiClient,
            IBDSApiClient bDSApiClient,
            FinanceDbContext db,
            IAppServiceContextAccessor? contextAccessor,
            ILogger<PurchaseOrderAppService> logger,
            IEasyCachingProvider easyCaching) :
            base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            this._paymentQueryService = paymentQueryService;
            this._daprClient = daprClient;
            this._paymentRepository = paymentRepository;
            this._unitOfWork = unitOfWork;
            this._purchaseApiClient = purchaseApiClient;
            this._purchasePayPlanRepository = purchasePayPlanRepository;
            this._abatementQueryService = abatementQueryService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._debtDetailRepository = debtDetailRepository;
            this._exchangeRateService = exchangeRateService;
            this._storeInApplyApiClient = storeInApplyApiClient;
            this._bDSApiClient = bDSApiClient;
            this._db = db;
            this._logger = logger;
            this._easyCaching = easyCaching;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            List<string> advanceCodes = null;
            bool isSuccess = true;
            try
            {
                if (input.RelateId.HasValue)
                {
                    var paymentdb = await _paymentQueryService.FirstOrDefaultAsync(p => p.RelateId == input.RelateId);
                    if (paymentdb != null) return ret;
                    var debtPayment = await _db.DebtPaymentUseDetails.FirstOrDefaultAsync(p => p.RelateId == input.RelateId);
                    if (debtPayment != null) return ret;
                }
                var purchaseOrder = await _purchaseApiClient.GetByIdAsync(input.BusinessId.Value);

                if (purchaseOrder == null || purchaseOrder.PurchaseOrderDetails == null || !purchaseOrder.PurchaseOrderDetails.Any())
                {
                    throw new Exception($"操作失败：采购单：{input.BusinessId}，没有查到采购单");
                }
                if (purchaseOrder.PaymentPlans == null || !purchaseOrder.PaymentPlans.Any())
                {
                    throw new Exception($"操作失败：采购单：{purchaseOrder.Code}，没有付款计划");
                }

                string _cachePrefix = "AdvancePays-PaymentCodes-";
                advanceCodes = purchaseOrder?.AdvancePays?.FirstOrDefault(p => p.Id == input.RelateId)?.PaymentCodes?.Where(p => !string.IsNullOrEmpty(p.Code)).Select(o => _cachePrefix + o.Code)?.Distinct()?.ToList();
                if (advanceCodes != null && advanceCodes.Any())
                {
                    var cacheData = await _easyCaching.GetAllAsync<string>(advanceCodes);
                    if (cacheData != null && cacheData.Any(p => p.Value.HasValue))
                    {
                        isSuccess = false;
                        throw new Exception($"操作失败：采购单：{purchaseOrder.Code}，游离付款单正在处理中");
                    }

                    Dictionary<string, string> cacheDict = new Dictionary<string, string>();
                    foreach (var code in advanceCodes)
                    {
                        if (!string.IsNullOrEmpty(code))
                        {
                            cacheDict.Add(code, input.BusinessCode ?? "");
                        }
                    }
                    await _easyCaching.SetAllAsync(cacheDict, TimeSpan.FromMinutes(3));
                }

                string createdBy = (purchaseOrder?.CreatedBy) ?? "none";
                //var list = await _purchasePayPlanRepository.GetByPurchaseNo(purchaseOrder.Code);
                //删除再添加
                await _purchasePayPlanRepository.DeleteByPurchaseNo(purchaseOrder.Code);
                var purchasePayPlans = new List<PurchasePayPlan>();
                foreach (var plan in purchaseOrder.PaymentPlans)
                {
                    if (plan.APRevisionDetails == null || !plan.APRevisionDetails.Any())
                    {
                        throw new Exception($"操作失败：采购单：{purchaseOrder.Code}，没有应付修订明细");
                    }
                    foreach (var detail in plan.APRevisionDetails)
                    {
                        var temp = new PurchasePayPlan
                        {
                            Id = Guid.NewGuid(),
                            CompanyId = purchaseOrder.Consignee?.Id,
                            CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                            NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                            ServiceId = purchaseOrder.Service?.Id,
                            ServiceName = purchaseOrder.Service?.Name,
                            AgentId = purchaseOrder.Agent?.Id,
                            AgentName = purchaseOrder.Agent?.Name,
                            ProductNo = (detail.Product?.ProductNo) ?? string.Empty,
                            ProductId = detail.Product?.Id,
                            PurchaseId = input.BusinessId,
                            PurchaseCode = purchaseOrder.Code,
                            ForwardPurchaseCode = purchaseOrder.ForwardOrder?.Code,
                            AccountPeriodType = (AccountPeriodTypeEnum)plan.DPOType,
                            Ratio = plan.ProportionRevision == null ? 0 : plan.ProportionRevision.Value,
                            Quantity = detail.Quantity,
                            RatioPrice = detail.PurchaseCostRevision,
                            PurchaseDetailId = detail.PurchaseDetailId,
                            Price = detail.PurchaseCost,
                            ProbablyPayTime = plan.ProbablyPayTime.HasValue ? plan.ProbablyPayTime.Value.DateTime : plan.APTime,
                            CreatedTime = DateTime.Now,
                            UpdatedTime = DateTime.Now,
                            CreatedBy = createdBy,
                            UpdatedBy = createdBy,
                            AccountPeriodDays = plan.DPO
                        };
                        await _purchasePayPlanRepository.AddAsync(temp);
                        purchasePayPlans.Add(temp);
                    }
                }
                var purchasePayPlan = purchasePayPlans.FirstOrDefault(p => p.AccountPeriodType == AccountPeriodTypeEnum.ProbablyPay);
                var kdInpus = new List<SavePaymentAdjustmentInput>();
                if (purchasePayPlan != null)
                {
                    var advancePays = purchaseOrder.AdvancePays.FirstOrDefault(p => p.Id == input.RelateId);
                    if (advancePays != null)
                    {
                        if (advancePays.PaymentCodes != null && advancePays.PaymentCodes.Any()) //游离付款单支付
                        {
                            List<Payment> payments_db = new List<Payment>();

                            var paymentCodes = advancePays.PaymentCodes.Select(p => p.Code).Distinct().ToList();

                            var originPayments = await _paymentQueryService.GetAllListAsync(p => paymentCodes.Contains(p.Code));
                            if (originPayments != null && originPayments.Any())
                            {
                                var originPaymentCodes = originPayments.Select(p => p.OriginCode).Distinct().ToList();
                                payments_db = (await _paymentQueryService.GetAllListAsync(p => originPaymentCodes.Contains(p.OriginCode) && p.AbatedStatus == AbatedStatusEnum.NonAbate && string.IsNullOrEmpty(p.PurchaseCode))).OrderByDescending(p => p.Value).Adapt<List<Payment>>();
                            }
                            else
                            {
                                payments_db = (await _paymentQueryService.GetAllListAsync(p => paymentCodes.Contains(p.Code) && p.AbatedStatus == AbatedStatusEnum.NonAbate && string.IsNullOrEmpty(p.PurchaseCode))).OrderByDescending(p => p.Value).Adapt<List<Payment>>();
                            }

                            var abatementsOfPay = await _abatementQueryService.GetAllListAsync(p => paymentCodes.Contains(p.DebtBillCode) && p.DebtType == "payment");
                            var payAmount = payments_db.Sum(p => p.Value);
                            if (payments_db != null && payments_db.Any())
                            {
                                var remainValue = Math.Round(advancePays.ActualPayAmount, 2); ;//100
                                foreach (var payment in payments_db)
                                {
                                    string? purchaseCode = "";
                                    string? _code = payment.Code;
                                    if (originPayments != null && originPayments.Any())
                                    {
                                        _code = originPayments.FirstOrDefault(p => p.OriginCode == payment.OriginCode)?.Code;
                                    }

                                    var abatementAmount = abatementsOfPay.Where(p => p.DebtBillCode == payment.Code).Sum(p => p.Value);
                                    if (remainValue > 0)
                                    {
                                        payment.PurchaseCode = input.BusinessCode;
                                        payment.ProducerOrderNo = purchaseOrder.ProducerOrderNo;
                                        payment.ProjectId = Guid.Parse(purchaseOrder.Project.Id);
                                        payment.ProjectName = purchaseOrder.Project.Name;
                                        payment.ProjectCode = purchaseOrder.Project.Code;
                                    }
                                    var paymentCode = advancePays.PaymentCodes.Where(p => p.Code == payment.Code).FirstOrDefault();
                                    //如果是返利单直接使用paymentCoded的Value
                                    if (paymentCode != null && !string.IsNullOrEmpty(paymentCode.TypeName) && paymentCode.TypeName.Equals("返利单") && paymentCode.Value.HasValue)
                                    {
                                        remainValue = remainValue - paymentCode.Value.Value;
                                    }
                                    else
                                    {
                                        remainValue = remainValue - (payment.Value - abatementAmount); //重新计算剩下的
                                    }

                                    if (remainValue <= 0)
                                    {
                                        payment.Value = payment.Value - Math.Abs(remainValue);

                                        kdInpus = InitSavePaymentAdjustmentInput(payment, purchaseCode, input.BusinessCode);//金蝶关联付款单
                                        await InitRMBAmount(purchaseOrder, payment);
                                        await _paymentRepository.UpdateAsync(payment);
                                        var code_array = payment.Code.Split("-");

                                        if (code_array.Length == 1)
                                        {
                                            purchaseOrder.Code = $"{payment.Code}-001";
                                        }
                                        else
                                        {
                                            purchaseOrder.Code = $"{payment.Code}-{code_array[code_array.Length - 1]}";
                                        }
                                        var PaymentDate = payment.PaymentDate;

                                        await AddPayment(advancePays, purchaseOrder, Math.Abs(remainValue), createdBy, true, PaymentDate, payment.OriginCode);

                                        break;
                                    }
                                    else
                                    {
                                        kdInpus = InitSavePaymentAdjustmentInput(payment, purchaseCode, input.BusinessCode);//金蝶关联付款单
                                        await InitRMBAmount(purchaseOrder, payment);
                                        await _paymentRepository.UpdateAsync(payment);
                                    }
                                }
                            }

                            //计算剩下的金额
                            var lessAmount = Math.Round(advancePays.ActualPayAmount, 2);
                            if (payments_db != null && payments_db.Any())
                            {
                                lessAmount = lessAmount - payAmount;
                            }
                            if (abatementsOfPay != null && abatementsOfPay.Any())
                            {
                                lessAmount = lessAmount + abatementsOfPay.Sum(p => p.Value);
                            }
                            //付款单没有付完
                            if (lessAmount > 0)
                            {
                                //如果付款单中包含有负数应付数据
                                var debts = await _db.Debts.Where(p => paymentCodes.Contains(p.BillCode) && p.AbatedStatus == AbatedStatusEnum.NonAbate).OrderByDescending(p => p.Value).ToListAsync();
                                var debtPaymentUses = await _db.DebtPaymentUseDetails.Where(p => paymentCodes.Contains(p.DebtCode)).ToListAsync();
                                var abatements = await _db.Abatements.Where(p => p.DebtType == "debt" && paymentCodes.Contains(p.DebtBillCode)).ToListAsync();
                                if (debts != null && debts.Any())
                                {
                                    foreach (var debt in debts)
                                    {
                                        var useTotalAmount = debtPaymentUses.Where(p => p.DebtCode == debt.BillCode).Sum(p => p.UseAmount);
                                        var needUseAmount = Math.Abs(debt.Value) - useTotalAmount;

                                        var abatement = abatements.Where(p => p.DebtBillCode == debt.BillCode).ToList();
                                        if (abatement != null && abatement.Any())
                                        {
                                            needUseAmount = Math.Abs(needUseAmount) - abatement.Sum(p => p.Value);
                                        }
                                        if (needUseAmount > 0)
                                        {
                                            var useAmount = lessAmount > needUseAmount ? needUseAmount : lessAmount; //本次使用金额
                                            var paymentCode = advancePays.PaymentCodes.Where(p => p.Code == debt.BillCode).FirstOrDefault();
                                            var tempUseAmount = useAmount;
                                            //如果是返利单直接使用paymentCoded的Value
                                            if (paymentCode != null && !string.IsNullOrEmpty(paymentCode.TypeName) && paymentCode.TypeName.Equals("返利单") && paymentCode.Value.HasValue)
                                            {
                                                lessAmount = lessAmount - paymentCode.Value.Value;
                                                tempUseAmount = paymentCode.Value.Value;
                                            }
                                            else
                                            {
                                                lessAmount = lessAmount - useAmount;
                                            }

                                            _db.DebtPaymentUseDetails.Add(new DebtPaymentUseDetailPo
                                            {
                                                Id = Guid.NewGuid(),
                                                CreatedBy = "none",
                                                CreatedTime = DateTimeOffset.Now,
                                                DebtCode = debt.BillCode,
                                                DebtId = debt.Id,
                                                UseAmount = tempUseAmount,
                                                UseCode = input.BusinessCode,
                                                RelateId = input.RelateId
                                            });
                                            if (lessAmount <= 0)
                                            {
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            //负数应付没有付完
                            if (lessAmount > 0)
                            {
                                //如果付款单中包含有负数应付数据
                                var credits = await _db.Credits.Where(p => paymentCodes.Contains(p.BillCode) && p.AbatedStatus == AbatedStatusEnum.NonAbate).OrderByDescending(p => p.Value).ToListAsync();
                                var paymentUses = await _db.DebtPaymentUseDetails.Where(p => paymentCodes.Contains(p.DebtCode)).ToListAsync();
                                var abatements = await _db.Abatements.Where(p => p.DebtType == "credit" && paymentCodes.Contains(p.DebtBillCode)).ToListAsync();
                                if (credits != null && credits.Any())
                                {
                                    foreach (var credit in credits)
                                    {
                                        var useTotalAmount = paymentUses.Where(p => p.DebtCode == credit.BillCode).Sum(p => p.UseAmount);
                                        var needUseAmount = Math.Abs(credit.Value) - useTotalAmount;

                                        var abatement = abatements.Where(p => p.DebtBillCode == credit.BillCode).ToList();
                                        if (abatement != null && abatement.Any())
                                        {
                                            needUseAmount = Math.Abs(needUseAmount) - abatement.Sum(p => p.Value);
                                        }
                                        if (needUseAmount > 0)
                                        {
                                            var useAmount = lessAmount > needUseAmount ? needUseAmount : lessAmount; //本次使用金额
                                            var paymentCode = advancePays.PaymentCodes.Where(p => p.Code == credit.BillCode).FirstOrDefault();
                                            var tempUseAmount = useAmount;
                                            //如果是返利单直接使用paymentCoded的Value
                                            if (paymentCode != null && !string.IsNullOrEmpty(paymentCode.TypeName) && paymentCode.TypeName.Equals("返利单") && paymentCode.Value.HasValue)
                                            {
                                                lessAmount = lessAmount - paymentCode.Value.Value;
                                                tempUseAmount = paymentCode.Value.Value;
                                            }
                                            else
                                            {
                                                lessAmount = lessAmount - useAmount;
                                            }

                                            _db.DebtPaymentUseDetails.Add(new DebtPaymentUseDetailPo
                                            {
                                                Id = Guid.NewGuid(),
                                                CreatedBy = "none",
                                                CreatedTime = DateTimeOffset.Now,
                                                DebtCode = credit.BillCode,
                                                DebtId = credit.Id,
                                                UseAmount = tempUseAmount,
                                                UseCode = input.BusinessCode,
                                                RelateId = input.RelateId
                                            });
                                            if (lessAmount <= 0)
                                            {
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            //付款单和负数应付和服务费应收都没有付清，剩下的新增需要付款的付款单
                            if (lessAmount > 0)
                            {
                                _logger.LogInformation($"付款金额不足，新增金蝶付款单：{input.BusinessCode}**{input.RelateId}");
                                var payment = await AddPayment(advancePays, purchaseOrder, decimal.Parse(lessAmount.ToString("F2")), createdBy, false, DateTime.MinValue);
                                if (payment.Value > 0)
                                {
                                    //采购付款计划推送金蝶
                                    ret = await PushPurchasePayPlans(payment, advancePays);
                                    if (ret.Code == CodeStatusEnum.Failed)
                                    {
                                        throw new Exception(ret.Message);
                                    }
                                }
                            }
                            if (lessAmount <= 0)
                            {
                                _logger.LogInformation($"付款完毕，通知采购中心：{input.BusinessCode}**{input.RelateId}");
                                await _daprClient.PublishEventAsync<PurchaseOrder>("pubsub-default", "fam-purchase-payfinished", new PurchaseOrder(input.BusinessCode, 0, input.RelateId.Value));
                            }
                        }
                        else
                        {
                            if (Math.Round(advancePays.ActualPayAmount, 2) > 0)
                            {
                                var actualAmount = decimal.Parse(advancePays.ActualPayAmount.ToString("F2"));
                                if (AdvancePayModeEnum.NotUseQuota == advancePays.AdvancePayMode)
                                {
                                    Payment payment = await AddPayment(advancePays, purchaseOrder, actualAmount, createdBy, false, DateTime.MinValue);
                                    //采购付款计划推送金蝶
                                    ret = await PushPurchasePayPlans(payment, advancePays);
                                    if (ret.Code == CodeStatusEnum.Failed)
                                    {
                                        throw new Exception(ret.Message);
                                    }
                                }
                                else
                                {
                                    await _daprClient.PublishEventAsync<PurchaseOrder>("pubsub-default", "fam-purchase-payfinished", new PurchaseOrder(input.BusinessCode, 0, input.RelateId.Value));
                                }
                            }
                        }

                    }
                }
                if (kdInpus.Any())
                {
                    var kind = await _kingdeeApiClient.SavePaymentAdjustment(kdInpus);
                    if (kind != null && kind.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception(kind.Message);
                    }
                }
                var retCommit = await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (advanceCodes != null && advanceCodes.Any() && isSuccess)
                {
                    await _easyCaching.RemoveAllAsync(advanceCodes);
                }
            }
            return ret;
        }

        private async Task<Payment> AddPayment(AdvancePayOutput advance, PurchaseOutPut? purchaseOrder, decimal paymentValue, string createdBy, bool isNoMaster, DateTime? PaymentDate, string? originCode = null)
        {
            var payment = new Payment
            {
                Id = Guid.NewGuid(),
                BillDate = DateTime.Now,
                AbatedStatus = AbatedStatusEnum.NonAbate,
                AgentId = purchaseOrder.Agent?.Id,
                AgentName = purchaseOrder.Agent?.Name,
                CompanyId = purchaseOrder.Consignee?.Id,
                CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                CreatedBy = createdBy ?? "none",
                UpdatedBy = createdBy ?? "none",
                CreatedTime = DateTimeOffset.UtcNow,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                PurchaseCode = isNoMaster ? "" : purchaseOrder.Code,
                PaymentDate = isNoMaster ? PaymentDate : null,
                Code = isNoMaster ? purchaseOrder.Code : "",
                ProducerOrderNo = isNoMaster ? "" : purchaseOrder.ProducerOrderNo,
                Value = paymentValue,
                Type = PaymentTypeEnum.Prepay,
                RelateId = advance.Id,
                AdvancePayMode = advance.AdvancePayMode,
                CreditAmount = 0,
                BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                ProjectId = purchaseOrder?.Project == null ? Guid.Empty : Guid.Parse(purchaseOrder.Project.Id),
                ProjectCode = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Code,
                ProjectName = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Name,
                PurchaseContactNo = purchaseOrder?.Contract?.Code,
                CustomerId = purchaseOrder?.Hospital?.Id,
                CustomerName = purchaseOrder?.Hospital?.Name,
                OriginCode = originCode,
            };
            await InitRMBAmount(purchaseOrder, payment);
            if (payment.Value > 0)
            {
                await _paymentRepository.AddAsync(payment);
            }
            return payment;
        }

        private async Task InitRMBAmount(PurchaseOutPut? purchaseOrder, Payment payment)
        {
            if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
            {
                if (purchaseOrder.ExternalTradeInfo.CoinName != "人民币")
                {
                    var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                    {
                        Effectdate = DateTime.Now,
                        OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                    });
                    if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception("操作失败：未获取到汇率");
                    }
                    payment.RMBAmount = payment.Value * exchange.Data.Excval;
                }
                else
                {
                    payment.RMBAmount = payment.Value;
                }
            }
            else
            {
                payment.RMBAmount = payment.Value;
            }
        }

        /// <summary>
        /// 推送采购付款计划给金蝶
        /// </summary>
        /// <param name="payment"></param>
        private async Task<BaseResponseData<int>> PushPurchasePayPlans(Payment payment, AdvancePayOutput advancePay)
        {

            //    ret = await PushPurchasePayPlans(payment, advancePays.AgentBank, advancePays.SettlementModel, advancePays.FinanceRemark, advancePays.TransferDiscourse, purchaseOrder.Project);
            var ret = BaseResponseData<int>.Failed(500, "操作失败");
            if (payment == null) return ret;
            var inputKD = new KingdeePayApplyDto
            {
                applyPeopleNumber = payment.CreatedBy,
                payOrgNumber = payment.NameCode,
                payType = "202",
                remark = advancePay.FinanceRemark ?? "预付款",
                relateid = payment.RelateId.ToString(),
                applyDetail = new List<KingdeePayApplyDetail> {
                             new KingdeePayApplyDetail
                             {
                                  AgentId=payment.AgentId.Value,
                                  applyDetailData=new List<ApplyDetailData> {
                                       new ApplyDetailData {
                                            gaterAmount=payment.Value,
                                            detailNumber=payment.PurchaseCode??"",
                                            settlementModel=advancePay.SettlementModel??"JSFS04",
                                            orderNumber=payment.PurchaseCode,
                                            projectNumber=payment.ProjectCode,
                                            arrivalNumber=advancePay.CreditInfo?.ArrivalNumber
                                       }
                                  },
                                  bankAccount=advancePay.AgentBank?.bankNo??"",
                                  bankName=advancePay.AgentBank.account??"",
                                  bankBranchName=advancePay.AgentBank.bank??"",
                                  bankBranchNumber=advancePay.AgentBank.bankCode??"",
                                  transferDiscourse=advancePay.TransferDiscourse,
                                  moneyNumber=string.IsNullOrEmpty(payment.CoinCode)?"CNY":payment.CoinCode
                             }
                      },
                businessOrg = payment.BusinessDeptId.ToString(),
                payNumber = string.Empty
            };
            if (advancePay.AgentBank.type == 2)
            {
                inputKD.importGoods = advancePay.CustomsDeclarationGoods;
                inputKD.costBearingParty = advancePay.ChargesBorneBy;
                inputKD.jfzx_ynpush = advancePay.IsBonded.HasValue && advancePay.IsBonded.Value ? "1" : "0";
                inputKD.jfzx_postscript = "";
                inputKD.jfzx_transactioncoding = "121010";
                inputKD.jfzx_paymentabroad = "1";
                inputKD.jfzx_contractno = advancePay.ContractNumber;
                inputKD.jfzx_invoiceno = advancePay.InvoiceNumber;
            }
            ret = await _kingdeeApiClient.PushPaymentApplyToKingdee(inputKD);
            return ret;

        }

        /// <summary>
        /// 采购订单强制结束
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> PurchaseForceend(EventBusDTO input)
        {
            var purchaseOrder = await _purchaseApiClient.GetByIdAsync(input.BusinessId.Value);
            var ret = BaseResponseData<int>.Success("操作成功");
            if (purchaseOrder == null)
            {
                ret = BaseResponseData<int>.Success("操作失败：原因：未找到采购单！");
                return ret;
            }
            //1,根据采购订单号，找到未冲销的预付
            //2,把未冲销的预付汇总-已冲销付款金额 建立新付款单
            //3,把未冲销 状态改为已冲销，并更新新的付款值 
            var payments = (await _paymentQueryService.GetAllListAsync(p =>
            p.PurchaseCode == input.BusinessCode
            && p.Type == PaymentTypeEnum.Prepay
            //&& !string.IsNullOrEmpty(p.Code)
            && p.AbatedStatus == AbatedStatusEnum.NonAbate)).Adapt<List<Payment>>();
            //多个付款单
            List<PubPaymentOutput> pubPayments = new List<PubPaymentOutput>();
            if (payments != null && payments.Any())
            {
                var newPayments = new List<Payment>();
                int index = 1;
                var kdInpus = new List<SavePaymentAdjustmentInput>();
                foreach (var payment in payments)
                {
                    if (payment.AdvancePayMode == AdvancePayModeEnum.UseQuota)
                    {
                        payment.CreditAmount = payment.Value;
                        await InitRMBAmount(purchaseOrder, payment);
                        await _paymentRepository.UpdateAsync(payment);
                    }
                    else
                    {
                        var detailExcutes = await _debtDetailRepository.GetDebtDetailExcuteBy(payment.Code);//获取应付付款计划执行明细
                        var paymenttotal = detailExcutes.Select(p => p.Value).Sum(); //已经冲销金额
                        if (payment.Value - paymenttotal > 0)
                        {
                            PubPaymentOutput pubPayment = new PubPaymentOutput();
                            pubPayment.CoinCode = payment.CoinCode;
                            pubPayment.CoinName = payment.CoinName;
                            pubPayment.PurchaseCode = payment.PurchaseCode;
                            if (paymenttotal == 0)
                            {
                                payment.PurchaseCode = string.Empty;
                                payment.ProducerOrderNo = string.Empty;
                                payment.PurchaseContactNo = string.Empty;
                                payment.RelateId = null;
                                payment.AdvancePayMode = null;
                                payment.Code += $"-1000";
                                payment.BillDate = DateTime.Now;
                                await InitRMBAmount(purchaseOrder, payment);
                                await _paymentRepository.UpdateAsync(payment);
                                //原来的游离付款单
                                pubPayment.Code = payment.Code;
                                pubPayment.Amount = payment.Value;
                            }
                            else
                            {
                                var payment_temp = payment.Adapt<Payment>();
                                payment_temp.Value = payment.Value - paymenttotal;
                                payment_temp.PurchaseCode = string.Empty;
                                payment_temp.ProducerOrderNo = string.Empty;
                                payment_temp.PurchaseContactNo = string.Empty;
                                payment_temp.RelateId = Guid.Empty;
                                payment_temp.AdvancePayMode = null;
                                payment_temp.BillDate = DateTime.Now;
                                //payment_temp.ProjectId = Guid.Empty;
                                //payment_temp.ProjectName = string.Empty;
                                payment_temp.Id = Guid.NewGuid();
                                payment_temp.Code = $"{payment.Code}-1000";
                                newPayments.Add(payment_temp);
                                payment.AbatedStatus = AbatedStatusEnum.Abated;
                                payment.Value = paymenttotal;
                                payment.OriginCode = payment.OriginCode;
                                await InitRMBAmount(purchaseOrder, payment);
                                await _paymentRepository.UpdateAsync(payment);
                                //拆分出来的游离付款单
                                pubPayment.Code = payment_temp.Code;
                                pubPayment.Amount = payment_temp.Value;
                            }
                            pubPayments.Add(pubPayment);
                            kdInpus = InitSavePaymentAdjustmentInput(payment, input.BusinessCode, "");//金蝶关联付款单 
                        }
                    }
                }
                if (newPayments.Any())
                {
                    foreach (var payment in newPayments)
                    {
                        await InitRMBAmount(purchaseOrder, payment);
                    }
                    await _paymentRepository.AddManyAsync(newPayments);
                }
                if (kdInpus.Any())
                {
                    var kind = await _kingdeeApiClient.SavePaymentAdjustment(kdInpus);
                    if (kind != null && kind.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception(kind.Message);
                    }
                }
            }
            var useDebts = await _db.DebtPaymentUseDetails.Where(p =>
                             p.UseCode == input.BusinessCode)
                             .AsNoTracking().ToListAsync();
            if (useDebts != null && useDebts.Any())
            {
                var useAmount = useDebts.Where(p => p.UseAmount > 0).Sum(p => p.UseAmount);
                var abatement = useDebts.Where(p => p.UseAmount < 0).Sum(p => Math.Abs(p.UseAmount));

                var debtPaymentUses = new List<DebtPaymentUseDetailPo>();
                var posDebts = useDebts.Where(p => p.UseAmount > 0);
                var negDebts = useDebts.Where(p => p.UseAmount < 0);
                var leftAmount = useAmount - abatement;
                if (leftAmount > 0)
                {
                    foreach (var item in posDebts)
                    {
                        var _amounts = negDebts.Where(w => w.DebtCode == item.DebtCode)?.Sum(s => s.UseAmount) ?? 0;
                        if (item.UseAmount - _amounts > leftAmount && leftAmount > 0)
                        {
                            //记录一个负数使用明细
                            debtPaymentUses.Add(new DebtPaymentUseDetailPo
                            {
                                CreatedBy = "none",
                                Id = Guid.NewGuid(),
                                UseCode = item.UseCode,
                                UseAmount = -leftAmount,
                                DebtCode = item.DebtCode,
                                DebtId = item.DebtId,
                            });
                            leftAmount = 0;
                        }
                        else if (item.UseAmount - _amounts == leftAmount && leftAmount > 0)
                        {
                            //记录一个负数使用明细
                            debtPaymentUses.Add(new DebtPaymentUseDetailPo
                            {
                                CreatedBy = "none",
                                Id = Guid.NewGuid(),
                                UseCode = item.UseCode,
                                UseAmount = -leftAmount,
                                DebtCode = item.DebtCode,
                                DebtId = item.DebtId,
                            });
                            leftAmount = 0;
                        }
                        else if (item.UseAmount - _amounts < leftAmount && leftAmount > 0)
                        {
                            //记录一个负数使用明细
                            debtPaymentUses.Add(new DebtPaymentUseDetailPo
                            {
                                CreatedBy = "none",
                                Id = Guid.NewGuid(),
                                UseCode = item.UseCode,
                                UseAmount = -(item.UseAmount - _amounts),
                                DebtCode = item.DebtCode,
                                DebtId = item.DebtId,
                            });
                            leftAmount = leftAmount - (item.UseAmount - _amounts);
                        }
                    }

                    if (debtPaymentUses.Count > 0)
                    {
                        await _db.DebtPaymentUseDetails.AddRangeAsync(debtPaymentUses);
                    }
                }
            }
            if (pubPayments != null && pubPayments.Any())
            {
                //通知采购写到游离付款单表
                foreach (var pubPayment in pubPayments)
                {
                    _logger.LogInformation($"强制结束，写游离付款单表：{pubPayment.ToJson()}");
                    await _daprClient.PublishEventAsync<PubPaymentOutput>("pubsub-default", "fam-purchase-purchaseforceend", pubPayment);
                }
            }

            var temp = await _unitOfWork.CommitAsync();
            return ret;
        }

        private static List<SavePaymentAdjustmentInput> InitSavePaymentAdjustmentInput(Payment payment, string jfzx_orderno, string jfzx_adorderno = "")
        {
            if (string.IsNullOrWhiteSpace(payment.OriginCode))
            {
                throw new ApplicationException($"游离付款单的原始单号不能为空，游离付款单：{payment.Code}");
            }

            var kdInpus = new List<SavePaymentAdjustmentInput>();
            var temp = new SavePaymentAdjustmentInput
            {
                jfzx_adamount = payment.Value,
                jfzx_orderno = jfzx_orderno,
                jfzx_adorderno = jfzx_adorderno,
                jfzx_org = payment.BusinessDeptId,
                jfzx_paymentno = payment.OriginCode,
                jfzx_projectno = payment.ProjectCode
            };

            kdInpus.Add(temp);
            return kdInpus;
        }

        /// <summary>
        /// 回滚
        /// </summary>
        /// <param name="purchaseCode"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RollBack(string purchaseCode)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            try
            {

                await _purchasePayPlanRepository.DeleteByPurchaseNo(purchaseCode); //删除采购计划
                await _paymentRepository.ClearPurchaseCodeOfNonAbate(purchaseCode); //清空未冲销的付款单上面的采购单号，与厂家单号
            }
            catch (Exception ex)
            {
                ret = BaseResponseData<int>.Failed(500, ex.Message);
            }
            //删除金蝶 todo
            return ret;
        }

        /// <summary>
        /// 生成进口关税增值税缴费单应付【批量付款】
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> GeneralCustomsDebt(Guid id, string code)
        {
            try
            {
                var check = await base.IsCreatedDebtForBill(code);
                if (check)
                {
                    //return BaseResponseData<int>.Failed(500, "该单据已生成过应付");
                    throw new Exception("该单据已生成过应付");
                }
                var info = await _storeInApplyApiClient.GetCustomsInfoById(id);
                if (info == null || info.Details == null || !info.Details.Any())
                {
                    throw new Exception("未获取到缴款单数据或缴款单明细为空");
                }
                if (!info.PayeeId.HasValue || string.IsNullOrEmpty(info.PayeeName))
                {
                    throw new Exception("收款单位信息不正确");
                }
                var group = info.Details.GroupBy(p => new { p.ProjectId, p.PurcaseCode }).ToList();
                var debts = new List<Debt>();
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { info.CompanyId.ToString() }
                })).FirstOrDefault();

                var agents = info.PayeeId != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { info.PayeeId.Value }) : null;
                var index = 1;
                foreach (var item in group)
                {
                    var debt1 = new Debt()
                    {
                        AbatedStatus = AbatedStatusEnum.NonAbate,
                        AgentId = info.PayeeId,
                        AgentName = info.PayeeName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        BillCode = code + index.ToString().PadLeft(3, '0'),
                        BillDate = info.BillDate,
                        BusinessDeptFullName = info.BusinessDeptFullName,
                        BusinessDeptFullPath = info.BusinessDeptFullPath,
                        BusinessDeptId = info.BusinessDeptId,
                        CoinCode = "CNY",
                        CoinName = "人民币",
                        CompanyId = info.CompanyId,
                        CompanyName = companyInfo.companyName,
                        CreatedBy = info.CreatedBy,
                        Note = item.Key.PurcaseCode + "关税",
                        CreatedTime = DateTime.UtcNow,
                        Id = Guid.NewGuid(),
                        DebtType = DebtTypeEnum.expenses,
                        InvoiceStatus = InvoiceStatusEnum.noninvoice,
                        NameCode = companyInfo.nameCode,
                        ProjectCode = item.First().ProjectCode,
                        ProjectId = item.Key.ProjectId,
                        OrderNo = item.Key.PurcaseCode,
                        ProjectName = item.First().ProjectName,
                        PurchaseCode = item.Key.PurcaseCode,
                        RMBAmount = item.Sum(p => p.TariffAmount),
                        Value = item.Sum(p => p.TariffAmount),
                        RelateCode = code,
                        IsInternalTransactions = true,

                    };
                    index++;
                    var debtDetail1 = new DebtDetail()
                    {
                        Id = Guid.NewGuid(),
                        AccountPeriodType = 1,
                        Code = debt1.BillCode + "-001",
                        ProbablyPayTime = DateTime.Now.Date,
                        PurchaseCode = item.Key.PurcaseCode,
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = debt1.Value,
                    };
                    debt1.DebtDetails = new List<DebtDetail>() { debtDetail1 };
                    var debt2 = new Debt()
                    {
                        AbatedStatus = AbatedStatusEnum.NonAbate,
                        AgentId = info.PayeeId,
                        AgentName = info.PayeeName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        BillCode = code + index.ToString().PadLeft(3, '0'),
                        BillDate = info.BillDate,
                        BusinessDeptFullName = info.BusinessDeptFullName,
                        BusinessDeptFullPath = info.BusinessDeptFullPath,
                        BusinessDeptId = info.BusinessDeptId,
                        CoinCode = "CNY",
                        CoinName = "人民币",
                        CompanyId = info.CompanyId,
                        CompanyName = companyInfo.companyName,
                        CreatedBy = info.CreatedBy,
                        Note = item.Key.PurcaseCode + "进口增值税",
                        CreatedTime = DateTime.UtcNow,
                        Id = Guid.NewGuid(),
                        DebtType = DebtTypeEnum.expenses,
                        InvoiceStatus = InvoiceStatusEnum.noninvoice,
                        NameCode = companyInfo.nameCode,
                        ProjectCode = item.First().ProjectCode,
                        ProjectId = item.Key.ProjectId,
                        OrderNo = item.Key.PurcaseCode,
                        ProjectName = item.First().ProjectName,
                        PurchaseCode = item.Key.PurcaseCode,
                        RMBAmount = item.Sum(p => p.ImportAddAmount),
                        Value = item.Sum(p => p.ImportAddAmount),
                        RelateCode = code,
                        IsInternalTransactions = true,
                    };
                    var debtDetail2 = new DebtDetail()
                    {
                        Id = Guid.NewGuid(),
                        AccountPeriodType = 1,
                        Code = debt2.BillCode + "-001",
                        ProbablyPayTime = DateTime.Now.Date,
                        PurchaseCode = item.Key.PurcaseCode,
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = debt2.Value,
                    };
                    debt2.DebtDetails = new List<DebtDetail>() { debtDetail2 };
                    if (debt1.Value != 0)
                    {
                        debts.Add(debt1);
                    }
                    if (debt2.Value != 0)
                    {
                        debts.Add(debt2);
                    }
                    index++;
                }
                if (debts != null && debts.Any())
                {
                    #region 应付推到金蝶
                    var kingdeeDebts = new List<KingdeeDebt>();
                    foreach (var debt in debts)
                    {
                        var kingdeeDebt = new KingdeeDebt()
                        {
                            asstact_number1 = debt.AgentId.Value,
                            billno = debt.BillCode,
                            bizdate = debt.BillDate.Value,
                            org_number = debt.NameCode,
                            payorg_number = debt.NameCode,
                            billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                            jfzx_business_number = debt.BusinessDeptId,
                            jfzx_order_number = debt.PurchaseCode,
                            jfzx_creator = debt.CreatedBy ?? "none",
                            remark = debt.Note.Contains("关税") ? "关税" : "增值税",
                            currency_number = debt.CoinCode ?? "CNY",
                            pricetaxtotal4 = debt.Value,
                        };
                        var kingdeeDetail = new KingdeeDebtDetail()
                        {
                            jfzx_project_number = debt.ProjectCode,
                            pricetax = debt.Value,
                            quantity = 1,
                            material_number1 = "无",
                            taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? 13.00m : 0,
                            //taxrate = 13.00m
                        };
                        //应付不含税单价
                        kingdeeDetail.price2 = Math.Round((kingdeeDetail.pricetax / (1 + kingdeeDetail.taxrate / 100.00M)), 20);
                        var amount = kingdeeDetail.price2 * kingdeeDetail.quantity;
                        //应付不含税总额
                        kingdeeDebt.amount2 = Math.Round(amount, 2);

                        kingdeeDebt.billEntryModels = new List<KingdeeDebtDetail>() { kingdeeDetail };
                        kingdeeDebts.Add(kingdeeDebt);
                    }
                    var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, "关税、增值税", code);
                    #endregion
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await _debtRepository.InertManyAsync(debts);
                        await _unitOfWork.CommitAsync();
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }

                }
                else
                {
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 生成进口关税增值税缴费单应付【付款申请单】
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> GeneralCustomsPayment(Guid id, string code)
        {
            try
            {
                var paymentdb = await _paymentQueryService.FirstOrDefaultAsync(p => p.RelateId == id);
                if (paymentdb != null) return BaseResponseData<int>.Success("操作成功");

                var info = await _storeInApplyApiClient.GetCustomsInfoById(id);
                if (info == null || info.Details == null || !info.Details.Any())
                {
                    throw new Exception("未获取到缴款单数据或缴款单明细为空");
                }
                if (!info.PayeeId.HasValue || string.IsNullOrEmpty(info.PayeeName))
                {
                    throw new Exception("收款单位信息不正确");
                }
                var group = info.Details.GroupBy(p => new { p.ProjectId, p.PurcaseCode }).ToList();

                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { info.CompanyId.ToString() }
                })).FirstOrDefault();
                var index = 1;
                List<Payment> payments = new List<Payment>();
                foreach (var item in group)
                {
                    if (item.Sum(p => p.TariffAmount) > 0)
                    {
                        payments.Add(new Payment
                        {
                            Id = Guid.NewGuid(),
                            AbatedStatus = AbatedStatusEnum.NonAbate,
                            AdvancePayMode = AdvancePayModeEnum.NotUseQuota,
                            AgentId = info.PayeeId,
                            AgentName = info.PayeeName,
                            BillDate = info.BillDate.Value,
                            BusinessDeptFullName = info.BusinessDeptFullName,
                            BusinessDeptFullPath = info.BusinessDeptFullPath,
                            BusinessDeptId = info.BusinessDeptId,
                            ProjectName = item.First().ProjectName,
                            PurchaseCode = item.Key.PurcaseCode,
                            RMBAmount = item.Sum(p => p.TariffAmount),
                            Value = item.Sum(p => p.TariffAmount),
                            NameCode = companyInfo.nameCode,
                            CompanyId = info.CompanyId,
                            CompanyName = companyInfo.companyName,
                            ProjectCode = item.First().ProjectCode,
                            ProjectId = item.Key.ProjectId,
                            CoinCode = "CNY",
                            CoinName = "人民币",
                            RelateId = id,
                            Type = PaymentTypeEnum.Tariff,
                            CreatedBy = info.CreatedBy,
                            CreatedTime = DateTime.UtcNow,
                            ProducerOrderNo = code,
                        });
                    }
                    if (item.Sum(p => p.ImportAddAmount) > 0)
                    {
                        payments.Add(new Payment
                        {
                            Id = Guid.NewGuid(),
                            AbatedStatus = AbatedStatusEnum.NonAbate,
                            AdvancePayMode = AdvancePayModeEnum.NotUseQuota,
                            AgentId = info.PayeeId,
                            AgentName = info.PayeeName,
                            BillDate = info.BillDate.Value,
                            BusinessDeptFullName = info.BusinessDeptFullName,
                            BusinessDeptFullPath = info.BusinessDeptFullPath,
                            BusinessDeptId = info.BusinessDeptId,
                            ProjectName = item.First().ProjectName,
                            PurchaseCode = item.Key.PurcaseCode,
                            RMBAmount = item.Sum(p => p.ImportAddAmount),
                            Value = item.Sum(p => p.ImportAddAmount),
                            NameCode = companyInfo.nameCode,
                            CompanyId = info.CompanyId,
                            CompanyName = companyInfo.companyName,
                            ProjectCode = item.First().ProjectCode,
                            ProjectId = item.Key.ProjectId,
                            CoinCode = "CNY",
                            CoinName = "人民币",
                            RelateId = id,
                            Type = PaymentTypeEnum.ImportAddedTax,
                            CreatedBy = info.CreatedBy,
                            CreatedTime = DateTime.UtcNow,
                            ProducerOrderNo = code,
                        });
                    }
                }
                if (payments != null && payments.Any())
                {
                    #region 应付推到金蝶
                    var kingdeeDebts = new List<KingdeeDebt>();
                    foreach (var payment in payments)
                    {
                        var inputKD = new KingdeePayApplyDto
                        {
                            applyPeopleNumber = payment.CreatedBy,
                            payOrgNumber = payment.NameCode,
                            payType = payment.Type == PaymentTypeEnum.Tariff ? "233" : "234",
                            remark = payment.Type == PaymentTypeEnum.Tariff ? "关税" : "进口增值税",
                            relateid = payment.Id.ToString(),
                            applyDetail = new List<KingdeePayApplyDetail> {
                            new KingdeePayApplyDetail
                            {
                                  AgentId=payment.AgentId.Value,
                                  applyDetailData=new List<ApplyDetailData> {
                                       new ApplyDetailData {
                                            gaterAmount=payment.Value,
                                            detailNumber=payment.PurchaseCode??"",
                                            settlementModel=  info.PayInfo.SettlementModel??"JSFS04",
                                            orderNumber=payment.PurchaseCode,
                                            projectNumber=payment.ProjectCode
                                       }
                                  },
                                  bankAccount=info.PayInfo.AgentBank?.bankNo??"",
                                  bankName=info.PayInfo.AgentBank.account??"",
                                  bankBranchName=info.PayInfo.AgentBank.bank??"",
                                  bankBranchNumber=info.PayInfo.AgentBank.bankCode??"",
                                  transferDiscourse=info.PayInfo.TransferDiscourse,
                                  moneyNumber=string.IsNullOrEmpty(payment.CoinCode)?"CNY":payment.CoinCode
                             }
                            },
                            businessOrg = payment.BusinessDeptId.ToString(),
                            payNumber = string.Empty
                        };
                        //if (info.PayInfo.AgentBank.type == 2)
                        //{
                        //    inputKD.importGoods = info.PayInfo.CustomsDeclarationGoods;
                        //    inputKD.costBearingParty = info.PayInfo.ChargesBorneBy;
                        //    inputKD.jfzx_ynpush = info.PayInfo.IsBonded.HasValue && info.PayInfo.IsBonded.Value ? "1" : "0";
                        //    inputKD.jfzx_postscript = "";
                        //    inputKD.jfzx_transactioncoding = "121010";
                        //    inputKD.jfzx_paymentabroad = "1";
                        //    inputKD.jfzx_contractno = info.PayInfo.ContractNumber;
                        //    inputKD.jfzx_invoiceno = info.PayInfo.InvoiceNumber;
                        //}
                        var kdret = await _kingdeeApiClient.PushPaymentApplyToKingdee(inputKD);
                        if (kdret.Code != CodeStatusEnum.Success)
                        {
                            throw new ApplicationException("【金蝶】推送付款申请到金蝶错误");
                        }
                    }
                    #endregion
                    await _paymentRepository.AddManyAsync(payments);
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Success("操作成功");
                }
                else
                {
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        /// 服务采购订单创建生成应付
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ServicePurchaseCreated(Guid id, string code)
        {
            var ret = new BaseResponseData<int>();
            var check = await base.IsCreatedDebtForBill(code);
            if (check)
            {
                throw new Exception("该单据已生成过应付");
            }
            var servicePurchaseOrder = await _purchaseApiClient.ServicePurchaseGetById(id);
            if (servicePurchaseOrder == null || servicePurchaseOrder.Details == null || !servicePurchaseOrder.Details.Any())
            {
                throw new Exception("未获取到服务采购订单数据或服务采购订单明细为空");
            }
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { servicePurchaseOrder.Company.Id }
            })).FirstOrDefault();
            var group = servicePurchaseOrder.Details.GroupBy(p => new { p.TaxRate }).ToList();
            var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
            var agents = servicePurchaseOrder.Agent != null && servicePurchaseOrder.Agent.Id != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { servicePurchaseOrder.Agent.Id.Value }) : null;

            var debts = new List<Debt>();
            int index = 1;
            foreach (var item in group)
            {
                var debt = new Debt()
                {
                    AbatedStatus = AbatedStatusEnum.NonAbate,
                    AgentId = servicePurchaseOrder.Agent.Id,
                    AgentName = servicePurchaseOrder.Agent.Name,
                    IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                    BillCode = code + "-00" + index,
                    BillDate = billDate,//servicePurchaseOrder.BillDate,
                    BusinessDeptFullName = servicePurchaseOrder.BusinessDeptFullName,
                    BusinessDeptFullPath = servicePurchaseOrder.BusinessDeptFullPath,
                    BusinessDeptId = servicePurchaseOrder.BusinessDeptId,
                    CoinCode = "CNY",
                    CoinName = "人民币",
                    PurchaseContactNo = servicePurchaseOrder.Contract != null ? servicePurchaseOrder.Contract.Code : "",
                    CompanyId = Guid.Parse(servicePurchaseOrder.Company.Id),
                    CompanyName = servicePurchaseOrder.Company.Name,
                    CreatedBy = servicePurchaseOrder.CreatedBy,
                    Note = code + "服务采购",
                    CreatedTime = DateTime.UtcNow,
                    Id = Guid.NewGuid(),
                    DebtType = DebtTypeEnum.servicefee,
                    InvoiceStatus = InvoiceStatusEnum.noninvoice,
                    NameCode = companyInfo.nameCode,
                    ProjectCode = servicePurchaseOrder.Project.Code,
                    ProjectId = Guid.Parse(servicePurchaseOrder.Project.Id),
                    OrderNo = code,
                    ProjectName = servicePurchaseOrder.Project.Name,
                    PurchaseCode = code,
                    RMBAmount = item.ToList().Sum(p => p.Cost * p.Quantity),
                    Value = item.ToList().Sum(p => p.Cost.Value * (p.Quantity ?? 1)),
                    RelateCode = code,
                    TaxRate = item.Key.TaxRate,
                    IsInternalTransactions = true
                };
                if (debt.Value > 0)
                {
                    var debtDetail = new DebtDetail()
                    {
                        Id = Guid.NewGuid(),
                        AccountPeriodType = (int)AccountPeriodTypeEnum.ProbablyPay,
                        Code = debt.BillCode + "-001",
                        ProbablyPayTime = DateTime.Now.Date,
                        PurchaseCode = debt.PurchaseCode,
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = debt.Value,
                    };
                    debt.DebtDetails = new List<DebtDetail>() { debtDetail };
                }

                if (debt.Value != 0)
                {
                    debts.Add(debt);
                }
                index++;
            }
            if (debts != null && debts.Any())
            {
                var kingdeeDebts = new List<KingdeeDebt>();
                foreach (var debt in debts)
                {
                    #region 应付推到金蝶
                    var kingdeeDebt = new KingdeeDebt()
                    {
                        asstact_number1 = debt.AgentId.Value,
                        billno = debt.BillCode,
                        bizdate = debt.BillDate.Value,
                        org_number = debt.NameCode,
                        payorg_number = debt.NameCode,
                        billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                        jfzx_business_number = debt.BusinessDeptId,
                        jfzx_order_number = debt.PurchaseCode,
                        jfzx_creator = debt.CreatedBy ?? "none",
                        remark = "",
                        currency_number = debt.CoinCode ?? "CNY",
                        pricetaxtotal4 = debt.Value,
                        hedgeReceivable = servicePurchaseOrder.Hedgereceivable.HasValue ? servicePurchaseOrder.Hedgereceivable.Value : false
                    };
                    var kingdeeDetail = new KingdeeDebtDetail()
                    {
                        jfzx_project_number = debt.ProjectCode,
                        pricetax = debt.Value >= 0 ? debt.Value : Math.Abs(debt.Value),
                        quantity = debt.Value >= 0 ? 1 : -1,
                        jfzx_goodstype = "C",//货品类型(设备:A,物资:B,服务:C)
                        material_number1 = "",
                        taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? debt.TaxRate.Value : 0,
                        expenseitem_number = KingdeeHelper.TransferServiceItemTypeCode(servicePurchaseOrder.ServiceItemTypeCode),
                    };
                    //应付不含税单价
                    kingdeeDetail.price2 = Math.Round((kingdeeDetail.pricetax / (1 + kingdeeDetail.taxrate / 100.00M)), 20);
                    var amount = kingdeeDetail.price2 * kingdeeDetail.quantity;
                    //应付不含税总额
                    kingdeeDebt.amount2 = Math.Round(amount, 2);

                    kingdeeDebt.billEntryModels = new List<KingdeeDebtDetail>() { kingdeeDetail };

                    kingdeeDebts.Add(kingdeeDebt);
                }

                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, "服务费采购", code);
                #endregion
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    await _debtRepository.InertManyAsync(debts);
                    await _unitOfWork.CommitAsync();
                    return BaseResponseData<int>.Success("操作成功");
                }
                else
                {
                    throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            else
            {
                await _unitOfWork.CommitAsync();
                return BaseResponseData<int>.Success("操作成功");
            }
        }
    }
}