﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface ICheckDataWithKingdeeService
    {
        Task<BaseResponseData<CheckDataWithKingdeeRes>> GetTempStoreOutDataAsync(CheckDataWithKingdeeInputDto input);

        Task<BaseResponseData<List<CheckByBillOutput>>> CheckBillByBill(CheckByBillInput checkByBillInput);
    }
}
