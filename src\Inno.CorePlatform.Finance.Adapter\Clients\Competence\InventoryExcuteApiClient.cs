﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class InventoryExcuteApiClient : BaseDaprApiClient<InventoryExcuteApiClient>, IInventoryExcuteApiClient
    {
        public InventoryExcuteApiClient(DaprClient daprClient, ILogger<InventoryExcuteApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        public async Task<InventoryRespon<string>> UpdateInvoiceInfoRevoke(List<string> invoiceNumbers)
        {
            var p = new { invoiceNumber = invoiceNumbers };
            return await InvokeMethodWithQueryObjectAsync<object, InventoryRespon<string>>(p, AppCenter.Inventory_UpdateInvoiceInfoRevoke, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 更改库存入库入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<InventoryRespon<object>> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreInUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateDetailsForFinance, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 更改库存出库入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<InventoryRespon<object>> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreOutUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateStoreOutDetailsForFinance, RequestMethodEnum.POST);
        }

        protected override string GetAppId()
        {
            return AppCenter.Inventory_APPID;
        }

        /// <summary>
        /// 撤回 - 更改库存出库入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<InventoryRespon<object>> UpdateInvoiceInfoRevoke(List<InventoryStoreOutUpdateDetail> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<InventoryStoreOutUpdateDetail>, InventoryRespon<object>>(input, AppCenter.Inventory_UpdateInvoiceInfoRevokee, RequestMethodEnum.POST);
        }

        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }
    }
}
