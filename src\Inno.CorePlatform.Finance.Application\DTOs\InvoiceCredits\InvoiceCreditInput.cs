﻿using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits
{
    public class InvoiceCreditInput
    {
        /// <summary>
        /// 制作开票申请编码
        /// </summary>
        public string CustomizeInvoiceCode { get; set; }
        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string? Drawer { get; set; }
        /// <summary>
        /// 应收
        /// </summary> 
        public List<CreditInput>? Credits { get; set; }

        /// <summary>
        /// 发票
        /// </summary>
        public List<InvoiceInput> Invoices { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 蓝票号
        /// </summary>
        public string? BlueInvoiceNo { get; set;}

    }
    public class InvoiceInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary>
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string Type { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<InvoiceDetailInput> InvoicDetails { get; set; }
    }

    public class InvoiceDetailInput
    {
        /// <summary>
        /// 商品名称
        /// </summary> 
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary> 
        public string? ProductNo { get; set; }

        /// <summary>
        /// 单位
        /// </summary> 
        public string? Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal? UnitPriceOfNoTax { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal AmountOfNoTax { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTateCodeId { get; set; }
         
        /// <summary>
        /// 行号
        /// </summary>
        public int? RowNo { get; set; }
    }

    public class CreditInput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditNo { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal Amount { get; set; }
    }

    public class InvoiceRedCreditInput
    {
        /// <summary>
        /// 开票申请号
        /// </summary>
        public string CustomizeInvoiceCode { get; set; }
          
        /// <summary>
        /// 发票
        /// </summary>
        public List<InvoiceInput> Invoices { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 蓝票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }

    }
}
