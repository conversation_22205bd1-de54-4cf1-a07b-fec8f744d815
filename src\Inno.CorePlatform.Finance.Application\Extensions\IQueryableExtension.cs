﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using ThirdParty.BouncyCastle.Asn1;

namespace Inno.CorePlatform.Sell.Application.Extensions
{
    /// <summary>
    /// IQueryable 扩展
    /// </summary>
    public static class IQueryableExtension
    {

        /// <summary>
        /// 排序处理 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query">IQueryable<T></param>
        /// <param name="orderConditions">排序字段列表</param>
        /// <returns></returns>
        public static IQueryable<T> OrderConditions<T>(this IQueryable<T> query, IEnumerable<string> orderConditions)
        {
            var isFirst = true;
            IOrderedQueryable<T> orderedQueryable = null;
            foreach (var orderinfo in orderConditions)
            {
                //只按照第一个字段排序
                if(!isFirst)
                {
                    break;
                }
                var arrStr = orderinfo.Split(",");
                var t = typeof(T);
                var propertyInfo = t.GetProperty(arrStr[0], BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly);
                //从基类中获取字段
                if (propertyInfo == null && t.BaseType != null)
                {
                    propertyInfo = t.BaseType.GetProperty(arrStr[0], BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly);
                }
                if (propertyInfo == null)
                    continue;
                var parameter = Expression.Parameter(t);
                MemberExpression propertySelector = Expression.Property(parameter, propertyInfo);
                // 避免类型转换报错
                UnaryExpression convert = Expression.Convert(propertySelector, typeof(object));
                var orderby = Expression.Lambda<Func<T, object>>(convert, parameter);
                if (arrStr[1] == "desc")
                {
                    if (isFirst)
                    {
                        orderedQueryable = query.OrderByDescending(orderby);
                    }
                    else
                    {
                        orderedQueryable = orderedQueryable.ThenByDescending(orderby);
                    }
                }
                else
                {
                    if (isFirst)
                    {
                        orderedQueryable = query.OrderBy(orderby);
                    }
                    else
                    {
                        orderedQueryable = orderedQueryable.ThenBy(orderby);
                    }
                }
                isFirst = false;
            }
            query = orderedQueryable;
            return query;
        }
    }
}
