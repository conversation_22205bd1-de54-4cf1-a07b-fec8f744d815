﻿using Inno.CorePlatform.Common.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IFinanceRollbackAppService
    {
        /// <summary>
        /// 回滚出库单的凭证
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackStoreOut(string code);


        /// <summary>
        /// 回滚入库单的凭证
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackStoreIn(string code);

        /// <summary>
        /// 回滚应收
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackCredit(string code);


        /// <summary>
        /// 回滚应付
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackDebt(string code);
        
        /// <summary>
        /// 回滚应付
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackPurchase(string purchaseCode);

        Task<bool> RepaireDebtDiff(List<Guid> ids);
    }
}
