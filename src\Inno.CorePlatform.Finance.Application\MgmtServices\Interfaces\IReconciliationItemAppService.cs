﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IReconciliationItemAppService
    {
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="lstInput"></param>
        /// <returns></returns>
        /// 
        Task<BaseResponseData<string>> Add(ReconciliationItemInput input);
        Task<BaseResponseData<string>> AddDetails(ReconciliationDetailsInput input);
        Task<BaseResponseData<string>> Del(ReconciliationItemInput input);
    }
}
