﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.Extensions.Configuration;
using NPOI.OpenXmlFormats;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.ServiceClient;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using System.Globalization;
using Inno.CorePlatform.Finance.Application.DTOs.Refund;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 退款
    /// </summary>
    public class RefundAppService : IRefundAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IAbtmentService _abtmentService;
        private readonly IPaymentQueryService _paymentQueryService;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        
        /// <summary>
        /// 注入
        /// </summary>
        /// <param name="kingdeeApiClient"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        public RefundAppService(
            IKingdeeApiClient kingdeeApiClient,
            IUnitOfWork unitOfWork,
            ICodeGenClient codeGenClient,
            IBDSApiClient bDSApiClient,
            IAbtmentService abtmentService,
            IPaymentQueryService paymentQueryService,
            IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService
            )
        {
            _unitOfWork = unitOfWork;
            _appServiceContextAccessor = contextAccessor;
            _kingdeeApiClient = kingdeeApiClient;
            _codeGenClient = codeGenClient;
            _bDSApiClient = bDSApiClient;
            _abtmentService = abtmentService;
            _paymentQueryService = paymentQueryService;
            _db = db;
            _inventoryQueryService = inventoryQueryService;
        }
        /// <summary>
        /// 保存退款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> SaveRefund(RefundSaveInput input)
        {
            try
            {
                #region 拼装参数
                var refundItemId = Guid.NewGuid();
                var refundItem = input.Adapt<RefundItemPo>();
                refundItem.Id = refundItemId;
                //未提交到金蝶没有业务单号
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { input.CompanyId.ToString() }
                })).FirstOrDefault();
                var customer = await _bDSApiClient.GetCustomer(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    id = input.CustomerId.HasValue ? input.CustomerId.ToString() : Guid.Empty.ToString()
                });
                if (string.IsNullOrEmpty(input.BillCode)) 
                {
                    refundItem.BillCode = string.Empty;
                    var businessArea = "FXBD";
                    var dept = await _bDSApiClient.GetBusinessDeptById(input.BusinessDeptFullPath, "none", Guid.NewGuid().ToString());
                    if (dept != null && dept.Count > 0)
                    {
                        businessArea = dept[0].Value;
                    }
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = businessArea,
                        BillType = "RAI",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    if (outPut.Status)
                    {
                        refundItem.BillCode = outPut.Codes.First();
                       
                    }
                    else
                    {
                        throw new ApplicationException($"生成Code失败，{outPut.Msg}");
                    }
                    if (string.IsNullOrEmpty(outPut.Codes[0]))
                    {
                        throw new AppServiceException("单号生成异常，请重试！");
                    }
                }
                var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                DateTime.TryParse(sysMonth, out DateTime billDate);
                refundItem.BillDate = billDate;
                refundItem.CreatedBy = input.userName;
                var refundDetails = input.List.Adapt<List<RefundDetailPo>>();
                refundDetails.ForEach(x => x.RefundItemId = refundItemId);
                _db.RefundItem.Add(refundItem);
                _db.RefundDetails.AddRange(refundDetails);
                await _unitOfWork.CommitAsync();
                #endregion
                return BaseResponseData<int>.Success("保存成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "保存失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 提交退款到金蝶
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> SubmitKingdee([FromBody] RefundSubmitInput input)
        {
            try
            {


                if (!input.Id.HasValue)
                {
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "请选择一条数据");
                }
                var model = await _db.RefundItem.FirstOrDefaultAsync(x => x.Id == input.Id);
                if (model == null)
                {
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "数据不存在或已被删除");
                }
                if (input.refundParty == 0)
                {

                    var refundDetailPos = await _db.RefundDetails.Where(p => p.RefundItemId == input.Id).AsNoTracking().ToListAsync();
                    if (refundDetailPos == null || refundDetailPos.Count == 0)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：没有获取到退款单详情");
                    }
                    var paymentList = await _paymentQueryService.RefundApplyGetPaymentByCodes(refundDetailPos.Select(p => p.PaymentCode).ToList());
                    var RecPaySettleRequestDtoList = new List<RecPaySettleRequestDto>();
                    foreach (var item in refundDetailPos)
                    {
                        RecPaySettleRequestDtoList.Add(new RecPaySettleRequestDto()
                        {
                            mainBill = item.ReceiveCode,
                            mainSettleAmt = item.ReceiveAmount.Value > item.PaymentAmount.Value ? item.PaymentAmount.Value : item.ReceiveAmount.Value,
                            asstBill = string.IsNullOrEmpty(paymentList.Find(p => p.Code == item.PaymentCode).OriginCode) ? item.PaymentCode : paymentList.Find(p => p.Code == item.PaymentCode).OriginCode,
                            asstSettleAmt = item.ReceiveAmount.Value > item.PaymentAmount.Value ? item.PaymentAmount.Value : item.ReceiveAmount.Value,
                        });
                    }
                    await _kingdeeApiClient.ReceiveAbtPayment(new ReceiveAbtPaymentInput()
                    {
                        data = RecPaySettleRequestDtoList
                    });
                    model.Status = RefundStatusEnum.Complate;
                    //更新状态
                    model.UpdatedBy = input.userName;
                    model.UpdatedTime = DateTime.Now;
                    _db.RefundItem.Update(model);
                    await _unitOfWork.CommitAsync();
                    var count = await _abtmentService.ReceiveAbtPaymentSave(refundDetailPos, input.userName);
                    if (count == 0)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "提交成功，但是本地插入冲销信息失败");
                    }
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Success("提交成功");
                }
                else
                {
                    if (model.Status != Domain.RefundStatusEnum.waitSubmit)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "当前状态无法提交");
                    }

                    if (model.CompanyId.HasValue)
                    {
                        await CheckInventoryState(model.CompanyId.Value);
                    }
                    
                    var details = await _db.RefundDetails.Where(x => x.RefundItemId == model.Id).AsNoTracking().ToListAsync();
                    var ketInput = ConvertToSaveOrUpdateRefundInput(model);
                    ketInput.OAUserName = input.userName ??= ketInput.OAUserName;
                    if (details != null && details.Any())
                    {
                        var list = new List<CasRefundDetailModelInput>();
                        foreach (var item in details)
                        {
                            list.Add(new CasRefundDetailModelInput
                            {
                                minusNumber = item.MinusNumber,
                                refundMoney = item.RefundMoney.HasValue ? item.RefundMoney.Value : 0,
                                receivablesNumber = model.ReceivablesNumber
                            });
                        };
                        ketInput.list = list;
                    }
                    ketInput.transferPostscript = model.TransferPostscript;
                    var ret = await _kingdeeApiClient.SaveOrUpdateRefund(ketInput);
                    if (ret.Code == CodeStatusEnum.Success)
                    {
                        //更新状态和单号
                        if (ret.Data != null && !string.IsNullOrEmpty(ret.Data.billno))
                        {
                            model.BillCode = ret.Data.billno;
                        }
                        model.Status = Domain.RefundStatusEnum.waitAudit;
                        model.UpdatedBy = input.userName;
                        _db.RefundItem.Update(model);
                        await _unitOfWork.CommitAsync();
                    }
                    return ret;
                }
            }
            catch (Exception ex)
            {
                return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 转换
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        public static SaveOrUpdateRefundInput ConvertToSaveOrUpdateRefundInput(RefundItemPo po)
        {
            return new SaveOrUpdateRefundInput
            {
                model = "insert",
                OAUserName = po.CreatedBy,
                billno = "",
                payModel = po.PayModel,
                client = po.CustomerId.ToString().ToUpper() ?? string.Empty,
                bankAccount = po.BankAccount,
                bankName = po.BankName,
                bankNo = po.BankNo,
                bankBranchName = po.BankBranchName,
                bankBranchNumber = po.BankBranchNumber,
                dept = po.BusinessDeptId ?? string.Empty,
                company = po.NameCode,
                refundType = po.RefundType,
                remark = po.Remark ?? string.Empty,
                refundAllMoney = po.RefundAllMoney ?? 0,
                moneyNumber = po.MoneyNumber,
                e_payeetype = po.E_payeetype
            };
        }

        /// <summary>
        /// 更新退款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> UpdateRefund(RefundSaveInput input, RefundStatusEnum status)
        {
            try
            {
                #region 拼装参数
                var refundItem = input.Adapt<RefundItemPo>();
                refundItem.BillDate = DateTime.Now;
                refundItem.CreatedBy = input.userName;
                refundItem.UpdatedBy = input.userName;
                refundItem.Status = status;
                refundItem.Id = input.Id;
                var oldRefundDetails = await _db.RefundDetails.Where(p => p.RefundItemId == refundItem.Id).ToListAsync();
                _db.RefundDetails.RemoveRange(oldRefundDetails);
                var refundDetails = input.List.Adapt<List<RefundDetailPo>>();
                refundDetails.ForEach(x => x.RefundItemId = refundItem.Id);
                _db.RefundItem.Update(refundItem);
                _db.RefundDetails.UpdateRange(refundDetails);
                await _unitOfWork.CommitAsync();
                #endregion
                return BaseResponseData<int>.Success("更新成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "更新成功：" + ex.Message);
            }
        }

        /// <summary>
        /// 更新退款状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> UpdateRefundState(RefundStateSynchronizationInput input)
        {
            try
            {
                var refundItem = await _db.RefundItem.FirstOrDefaultAsync(x => x.BillCode == input.Code);
                if (refundItem == null)
                {
                    return BaseResponseData<int>.Failed(500, $"未找到{input.Code}的退款单");
                }
                refundItem.Status = input.Status;
                _db.RefundItem.Update(refundItem);
                await _unitOfWork.CommitAsync();
                return BaseResponseData<int>.Success("更新成功");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, "更新失败：" + ex.Message);
            }
        }

        public async Task<BaseResponseData<int>> DeleteRefund(Guid id)
        {
            var refundItem = _db.RefundItem.Where(p => p.Id == id).FirstOrDefault();
            if (refundItem != null)
            {
                _db.RefundItem.Remove(refundItem);
            }
            var refundDetails = _db.RefundDetails.Where(p => p.RefundItemId == id).ToList();
            if (refundDetails != null && refundDetails.Count > 0)
            {
                _db.RefundDetails.RemoveRange(refundDetails);
            }
            await _unitOfWork.CommitAsync();
            return BaseResponseData<int>.Success("删除成功");
        }

        #region  私有方法
        /// <summary>
        /// 盘点状态检查
        /// </summary>
        /// <param name="companyId"></param>
        private async Task CheckInventoryState(Guid companyId)
        {
            string sysMonth = DateTime.Now.ToString("yyyy-MM");
            var inventory = await _inventoryQueryService.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
            if (inventory != null)
            {
                if (inventory.Status == 2)
                {
                    throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                }
                if (inventory.Status == 99)
                {
                    DateTime.TryParse(sysMonth, out DateTime billDate);
                    if (billDate.Year == DateTime.Now.Year && billDate.Month == DateTime.Now.Month)
                    {
                        throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                    }
                }
            }
        }
        #endregion
    }
}
