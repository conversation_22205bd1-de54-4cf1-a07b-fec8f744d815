﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    public class InventoryStoreInUpdateDetail
    {
   
        public decimal invoiceQuantity { get; set; }

        public string storeInDetailId { get; set; }

        public decimal currentInvoiceQuantity { get; set; }

        public decimal invoiceAmount { get; set; }

        public string invoiceNumber { get; set; }

        public string InvoiceTypeStr { get; set; }

        /// <summary>
        /// utc时间戳
        /// </summary>
        public long InvoiceDate { get; set; }
    }
}
