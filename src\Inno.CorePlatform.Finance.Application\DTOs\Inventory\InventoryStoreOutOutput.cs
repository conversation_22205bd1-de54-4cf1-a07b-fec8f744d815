﻿using Inno.CorePlatform.Finance.Domain;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    public class InventoryStoreOutOutput : BusinessDepartDTO
    {

        /// <summary>
        /// 
        /// </summary>
        public string? productNameId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? productId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? projectId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? projectName { get; set; }
        public string? projectCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? companyId { get; set; }
        /// <summary>
        ///  
        /// </summary>
        public string? companyName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? businessUnitId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? businessUnitName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? customerId { get; set; }
        /// <summary>
        ///  
        /// </summary>
        public string? customerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeOutCode { get; set; }

        public string? signSysMonth { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? sourceBillCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? deliveryCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? deliveryStatus { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? pickerId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? pickerName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? createdBy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? updatedBy { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? remark { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? checker { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? checkerId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double? checkedTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? storeHouseId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int storeOutType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeHouseName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? contactName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? contactTel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? contactAddress { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? customerAddressId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? hospitalDept { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? hospitalDeptId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? customerDeptStaffId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? relateCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<Detail> Details { get; set; }
        public long? createdTime { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public long billDate { get; set; }

        /// <summary>
        /// 出库子类型
        /// 601=经销退货、 602=寄售退货、 603=经销报损、 604寄售报损、605=不合格品经销换货、606=不合格品寄售换货、1201=寄售盘亏、 1202=经销盘亏,108=集团销毁出库
        /// </summary>
        public int? relateCodeType { get; set; }

        /// <summary>
        /// 运输单号
        /// </summary>
        public string? shipmentCode { get; set; }
    }

    public class Detail
    {
        public Guid? producerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Guid id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? lotNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeOutCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double? validDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? barcode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int quantity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid productNameId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? specification { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? projectId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? productId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? projectName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? relateCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? unitCost { get; set; }
        public decimal? stockUnitCost { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? taxRate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? businessUnitId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? businessUnitName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? producerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeHouseId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeHouseName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? traceCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? mark { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double? produceDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? registrationId { get; set; }

        public string? registrationNo { get; set; }
        public Guid? saleDetailId { get; set; }
        /// <summary>
        /// 单位售价
        /// </summary>
        public decimal? price { get; set; }

        /// <summary>
        /// 原始售价
        /// </summary>
        public decimal? originalPrice { get; set; }

        /// <summary>
        /// 销售税率
        /// </summary>
        public decimal? priceTaxRate { get; set; }

        public decimal? standardUnitCost { get; set; }

        public string purchaseOrderCode { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary>
        public decimal? rmbAmount { get; set; }
        public string? coinCode { get; set; }
        /// <summary>
        /// 币种代码
        /// </summary>
        public string? coinAttribute { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string? coinName { get; set; }
        /// <summary>
        /// 原币单价
        /// </summary>
        public decimal? originCost { get; set; }

        public decimal? unitTariffAmount { get; set; }
        public decimal? unitImportAddAmount { get; set; }

        /// <summary>
        /// 进口=1，反之非进口
        /// </summary>
        public int? importBusinessFlag { get; set; }

        /// <summary>
        /// 结算成本价
        /// </summary>
        public decimal? settlementCost { get; set; }
        /// <summary>
        /// 0, "非集采",1, "集采"，2, "其他"
        /// </summary>
        public PriceSourceEnum? priceSource { get; set; }
    }

}
