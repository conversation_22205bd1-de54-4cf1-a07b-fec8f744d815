﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PaymentAutoDetailRepository : IPaymentAutoDetailRepository
    {
        private readonly FinanceDbContext _db;
        public PaymentAutoDetailRepository(FinanceDbContext dbContext)
        {
            _db = dbContext;
            UowJoined = true;
        }

        public bool UowJoined { get; set; } = false;

        public async Task<int> DeteteManyAsync(List<Guid> lstId, bool deleteBankInfo = false)
        {
            var lstPo = await _db.PaymentAutoDetails.Include(p => p.DebtDetail.Debt).Where(t => lstId.Contains(t.Id)).ToListAsync();
            if (lstPo.Count > 0 && lstPo.Any())
            {
                var paymentAutoItem = await _db.PaymentAutoItems.Where(p => p.Id.Equals(lstPo.First().PaymentAutoItemId)).FirstOrDefaultAsync();
                if (paymentAutoItem != null)
                {
                    if (paymentAutoItem.Status == Domain.PaymentAutoItemStatusEnum.WaitSubmit)
                    {
                        _db.PaymentAutoDetails.RemoveRange(lstPo);
                        if (deleteBankInfo)
                        {
                            //删除的供应商Id
                            var agentIds = lstPo.Select(p => p.DebtDetail.Debt.AgentId).Distinct().ToList();
                            var itemId = lstPo.FirstOrDefault()?.PaymentAutoItemId;
                            //删除后剩余的供应商
                            var details = await _db.PaymentAutoDetails.Include(p => p.DebtDetail.Debt).Where(p => p.PaymentAutoItemId == itemId&& !lstId.Contains(p.Id)).Select(p => p.DebtDetail.Debt.AgentId).ToListAsync();
                            
                            var detailAgents = details.Distinct().ToList();
                            var bankInfos = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == itemId && agentIds.Contains(p.AgentId) && !detailAgents.Contains(p.AgentId)).ToListAsync();
                            if (bankInfos.Any())
                            {
                                _db.PaymentAutoAgentBankInfos.RemoveRange(bankInfos);
                            }
                        }
                        await _db.SaveChangesAsync();
                    }
                }
            }
            return 1;
        }

        public async Task<int> UpdateManyAsync(List<PaymentAutoDetail> lstDetail)
        {
            var lstPo = lstDetail.Adapt<List<PaymentAutoDetailPo>>();

            _db.PaymentAutoDetails.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }
    }
}
