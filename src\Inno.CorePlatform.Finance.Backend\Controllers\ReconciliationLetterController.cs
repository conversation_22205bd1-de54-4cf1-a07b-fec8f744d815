﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 对账函操作
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationLetterController : BaseController
    {
        private readonly IReconciliationLetterAppService _reconciliationLetterAppService;
        public ReconciliationLetterController(
            IReconciliationLetterAppService reconciliationLetterAppService, ISubLogService subLog) : base(subLog)
        {
            this._reconciliationLetterAppService = reconciliationLetterAppService;
        }
        /// <summary>
        ///提交对账函
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut("submit")]
        public async Task<BaseResponseData<string>> Submit(Guid? id)
        {
            return await _reconciliationLetterAppService.SubmitItemAsync(id, CurrentUser.UserName);
        }
        /// <summary>
        /// 删除对账函
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpDelete("del")]
        public async Task<BaseResponseData<bool>> Del(List<Guid> ids)
        {
            var res = 0;
            foreach (var id in ids)
            {
                res += await _reconciliationLetterAppService.DeleteItemAsync(id);
            }
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }
        /// <summary>
        /// 删除对账函明细
        /// </summary>
        /// <param name="id"></param>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpGet("deleteDetail")]
        public async Task<BaseResponseData<bool>> DeleteDetail(Guid id, Guid itemId)
        {
            var res = await _reconciliationLetterAppService.DeleteDetailItemAsync(id, itemId);

            if (res > 0)
            {
                return BaseResponseData<bool>.Success("删除成功！");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "删除失败！");
            }
        }

        /// <summary>
        /// 上传回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AttachFileIds_letter")]
        public async Task<BaseResponseData<int>> AttachFileIds_letter(ReconciliationLetterAttachFileInput input)
        {
            return await _reconciliationLetterAppService.AttachFileIds_letter(input);
        }
        /// <summary>
        /// 查看回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile_letter")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile_letter(ReconciliationLetterAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            ret.Data = await _reconciliationLetterAppService.GetAttachFile_letter(input);
            return ret;
        }
        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(ReconciliationLetterAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            ret.Data = await _reconciliationLetterAppService.GetAttachFile(input);
            return ret;
        }
        /// <summary>
        /// 删除回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds_letter")]
        public async Task<BaseResponseData<string>> DeleteAttachFileIds_letter(ReconciliationLetterAttachFileInput input)
        {
            return await _reconciliationLetterAppService.DeleteAttachFileIds_letter(input);
        }

        /// <summary>
        /// 创建对账函
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Create")]
        public async Task<BaseResponseData<string>> Create(CreateReconciliationLetterInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            if (input.OperateType == "insert")
            {
                return await _reconciliationLetterAppService.CreateReconciliationLetter(input);//新增
            }
            else
            {
                return await _reconciliationLetterAppService.UpdateReconciliationLetter(input);//编辑
            }

        }
        /// <summary>
        /// 重新同步对账函明细数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut("reset")]
        public async Task<BaseResponseData<string>> Reset(Guid? id)
        {
            return await _reconciliationLetterAppService.ResetItemAsync(id, CurrentUser.UserName);
        }
        /// <summary>
        /// 对账函获取打印模板
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("GetReconciliationLetterPDF")]
        public async Task<IActionResult> GetReconciliationLetterPDF(Guid id)
        {
            var pdfResult = await _reconciliationLetterAppService.GetReconciliationLetterPDF(id);
            var stream = await pdfResult.ReadAsStreamAsync();
            return File(stream, pdfResult.Headers.ContentType.ToString());
        }

    }

}
