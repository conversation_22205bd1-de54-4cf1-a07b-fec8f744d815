﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.SPD
{
    /// <summary>
    /// 开票数据接口
    /// </summary>
    public class InoviceSpdInput
    {
        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal? amount { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? invoiceNum { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? billingDate { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public string? remark { get; set; } = "";
        public int? is_refund { get; set; } = 0;
        /// <summary>
        /// 应收明细
        /// </summary>
        public List<CreditDetail> creditDetails { get; set; }=new List<CreditDetail>();
    }

    public class CreditDetail
    {
        /// <summary>
        /// SPD应收单号
        /// </summary>
        public string creditCode { get; set; }
        /// <summary>
        /// 应收单发票金额
        /// </summary>
        public decimal creditAmount { get; set; }
    }
}
