﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Data;

namespace Inno.CorePlatform.Finance.Adapter
{
    public class EfUnitOfWork : IUnitOfWork
    {
        private readonly FinanceDbContext dbContext;
        private readonly IRepositorySupportUow[] repositories;

        public EfUnitOfWork(FinanceDbContext dbContext, IRepositorySupportUow[] repositories)
        {
            this.dbContext = dbContext;
            this.repositories = repositories;

            //set UowJoined of repositories
            foreach (var repository in repositories)
            {
                repository.UowJoined = true;
            }
        }

        public async Task<int> CommitAsync(CancellationToken cancellationToken = default)
        {
            return await dbContext.SaveChangesAsync();
        }


        public void Dispose()
        {
            //restore UowJoined of repositories
            foreach (var repository in repositories)
            {
                repository.UowJoined = false;
            }
        }
    }
    public class EfUnitOfWorkFactory : IUnitOfWorkFactory
    {
        private readonly FinanceDbContext dbContext;

        public EfUnitOfWorkFactory(FinanceDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public IUnitOfWork Create(params IRepositorySupportUow[] repositories)
        {
            if (repositories is null)
            {
                throw new ArgumentNullException(nameof(repositories));
            }

            return new EfUnitOfWork(dbContext, repositories);
        }
    }
}