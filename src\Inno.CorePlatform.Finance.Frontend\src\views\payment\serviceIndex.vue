<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-paymentQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>付款单清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal" style="padding: 0">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="activeName" @tab-click="search">
                <el-tab-pane :label="`全部(${tabCount.allCount})`" name="all" />
                <el-tab-pane :label="`已付款单(${tabCount.paidCount})`" name="paid" />
                <el-tab-pane :label="`未付款单(${tabCount.nonPaidCount})`" name="unpaid" />
              </el-tabs>
            </template>
            <template #default>
              <!-- v-auth="functionUris.export" -->
              <el-button
                v-if="hasPermission(functionUris.export)"
                icon="Printer"
                type="primary"
                style="margin-left: 15px"
                :loading="downLoading"
                @click="
                  downloadAsync(
                    'api/PaymentQuery/payment/export',
                    '付款单清单',
                    crud.query
                  )
                "
              >导出付款单</el-button>
              <el-button
                v-if="hasPermission(functionUris.export)"
                icon="Printer"
                type="primary"
                style="margin-left: 15px"
                :loading="downLoading"
                @click="exportPaymentInfo"
              >导出付款信息</el-button>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon class="icon" :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableItem"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            :data="crud.data"
            stripe
            border
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="crud.singleSelection"
          >
            <el-table-column type="selection" fixed="left" width="55"></el-table-column>
            <el-table-column label="付款单号" property="code" fixed="left" min-width="180" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.code" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" min-width="200" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.companyId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="供应商" property="agentName" min-width="200" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.agentId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="业务单元" property="serviceName" min-width="100" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.serviceId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="类型" min-width="90" property="typeDisplay" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.type" :crud="crud" :column="column" />
              </template>
            </el-table-column>
            <el-table-column label="单据日期" min-width="120" property="billDate" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billDateS" :crud="crud" :column="column" />
              </template>
              <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
            </el-table-column>
            <el-table-column label="实际付款日期" min-width="120" property="paymentDate" show-overflow-tooltip sortable>
              <template #default="scope">
                {{
                dateFormat(scope.row.paymentDate, 'YYYY-MM-DD') ===
                '1901-01-01'
                ? ''
                : dateFormat(scope.row.paymentDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="币种" min-width="80" property="CoinName" show-overflow-tooltip sortable>
              <template #default="scope">{{ scope.row.coinName }}</template>
            </el-table-column>
            <el-table-column label="币种金额" min-width="100" property="value" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="人民币金额" min-width="120" property="rmbAmount" class-name="isSum" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.rmbAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="已冲销" min-width="120" property="abatedValue" class-name="isSum" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.abatedValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="余额" min-width="120" property="remainValue" class-name="isSum" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.remainValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="采购单号" min-width="200" property="purchaseCode" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.purchaseCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="采购合同单号" min-width="150" property="purchaseContactNo" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.purchaseContactNo" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseContactNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="厂家单号" min-width="150" property="producerOrderNo" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.producerOrderNo" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="批量付款单号" property="paymentAutoItemCode" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.paymentAutoItemCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="现金折扣金额" min-width="100" property="LimitedDiscount" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.limitedDiscount }}</template>
            </el-table-column>
            <el-table-column label="现金折扣附件" property="attachFileIds" width="100" show-overflow-tooltip>
              <template #default="scope">
                <el-button
                  type="primary"
                  style="font-size: 12px"
                  v-if="scope.row.attachFileIds"
                  @click.stop="
                        showAttachFile(
                          scope.row.attachFileIds,
                          scope.row.id
                        )
                      "
                >查看附件</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作人" min-width="80" property="createdByName" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link v-if="scope.row.code" style="font-size: 12px" type="primary" @click="downloadFile(scope.row.code)">回执单</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <span style="margin-left: 20px">
              金额：
              <inno-numeral :value="selectionValue" format="0,0.00" />
            </span>
            <div class="flex-1" />

            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
            <template #opts-left>
              <el-tabs v-model="setDetailTab" @tab-change="tabDetailActiveClick">
                <el-tab-pane :label="`冲销信息`" name="abatementDetail"></el-tab-pane>
                <el-tab-pane :label="`付款信息`" name="paymentPlan"></el-tab-pane>
              </el-tabs>
            </template>

            <template #right>
              <el-button
                v-if="setDetailTab === 'paymentPlan'"
                type="primary"
                :loading="downLoading"
                @click="
                downloadAsync(
                  'api/paymentQuery/exportPaymentPlan',
                  '付款信息',
                  crud3.query
                )
              "
              >导出数据</el-button>
              <el-button type="primary" @click="onFull">
                <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
              </el-button>
            </template>
          </inno-crud-operation>
          <!-- 冲销信息 -->
          <el-table
            ref="tableDetail"
            v-inno-loading="crudDetail.loading"
            class="auto-layout-table"
            highlight-current-row
            v-if="setDetailTab === 'abatementDetail'"
            :data="crudDetail.data"
            stripe
            border
            :row-class-name="crudDetail.tableRowClassName"
            @sort-change="crudDetail.sortChange"
            @selection-change="crudDetail.selectionChangeHandler"
          >
            <el-table-column property="creditBillCode" label="单据号码" width="300" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column property="companyName" label="公司" width="300" show-overflow-tooltip>
              <template #default>
                <inno-button-copy :link="false">{{ crud.rowData.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column property="agentName" label="供应商" width="300" show-overflow-tooltip>
              <template #default>
                <inno-button-copy :link="false">{{ crud.rowData.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="冲销日期" min-width="200" property="abtdate" show-overflow-tooltip>
              <template #default="scope">{{ dateFormat(scope.row.abtdate, 'YYYY-MM-DD') }}</template>
            </el-table-column>
            <el-table-column property="value" label="冲销金额" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" min-width="200" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.customerId" :crud="crudDetail" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
          </el-table>
          <!-- 付款信息 -->
          <el-table
            ref="tableDetail"
            v-inno-loading="crud3.loading"
            class="auto-layout-table"
            highlight-current-row
            v-if="setDetailTab === 'paymentPlan'"
            border
            :data="crud3.data"
            stripe
            fit
            :row-class-name="crud3.tableRowClassName"
            @sort-change="crud3.sortChange"
            @selection-change="crud3.selectionChangeHandler"
          >
            <el-table-column type="selection" fixed="left" width="40" />
            <el-table-column label="应付单号" width="200" property="debtBillCode" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObjectDetail.debtBillCode" :crud="crud3" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.debtBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="采购合同单号" property="purchaseContactNo" width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseContactNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="币种" property="coinName" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.coinName }}</template>
            </el-table-column>
            <el-table-column label="厂家单号" property="producerOrderNo" show-overflow-tooltip width="130">
              <template #default="scope">{{ scope.row.producerOrderNo }}</template>
            </el-table-column>
            <el-table-column label="应付金额" property="debtValue" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.debtValue, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="应付冲销金额" width="100" property="debtAbatedValue" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.debtAbatedValue, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="应付余额" property="debtBalance" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.debtBalance, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="本次付款金额" width="100" property="debtDetailValue" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.debtDetailValue, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="现金折扣金额" width="100" property="limitedDiscount" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.limitedDiscount, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="折扣" property="discount" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.discount, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="折前金额" property="originValue" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.originValue, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="账期类型" property="accountPeriodDescription" show-overflow-tooltip width="130"></el-table-column>
            <el-table-column label="采购单号" property="purchaseCode" min-width="200" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detailPurchaseCode(scope.row.purchaseCode)">{{ scope.row.purchaseCode }}</el-link>
                </inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="业务单元" property="serviceName" show-overflow-tooltip width="150"></el-table-column>
            <el-table-column label="供应商" property="agentName" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="进项票金额" property="noTaxAmount" show-overflow-tooltip>
              <template #default="scope">{{ asyncNumeral(scope.row.noTaxAmount, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="应收单号" width="200" property="creditBillCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="收款单号" width="200" property="receiveCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" width="200" property="customerName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="终端医院" width="200" property="hospitalName" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.hospitalName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" width="200" property="orderNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票号" width="200" property="invoiceNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="发票日期" property="invoiceTime" width="150" show-overflow-tooltip></el-table-column>
          </el-table>
        </template>
      </inno-split-pane>
    </div>

    <!-- 查看附件 -->
    <el-dialog v-model="comfile_show" title="查看现金折扣附件" :close-on-click-modal="false" :destroy-on-close="true" modal-class="position-fixed" draggable>
      <el-table :data="showfiles" stripe style="width: 100%">
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="length" label="大小">
          <template #default="scope">
            <inno-button-copy :link="false">{{ format(scope.row.length) }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column prop="uploadedBy" label="上传人" />
        <el-table-column prop="uploadedTime" label="上传时间" />
        <el-table-column label="操作 ">
          <template #default="scope">
            <span style="cursor: pointer" @click="showFileInfo(scope.row.id)">查看</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import request from '@/utils/request';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import { useRouter,useRoute }from 'vue-router';
import {
  ElTable,
  ElForm,
  ElMessageBox,
  ElMessage,
  ElLoading
} from 'element-plus';
import { AbatedStatus, PaymentTypeEnum } from '@/api/metaInfo';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
const functionUris = {
  export: 'metadata://fam/finance-ServicePayment/functions/excute-export'
};
const tableItem = ref<InstanceType<typeof ElTable>>();
const tableDetail = ref<InstanceType<typeof ElTable>>();
  
const setDetailTab = ref('abatementDetail');
//切换
const tabDetailActiveClick = async (tab: any) => {
  if (crud.data && crud.data.length > 0) {
    if (tab === 'abatementDetail') {
      crudDetail.query = { debtBillCode: crud.rowData.code, limit: 2000 };
      crudDetail.toQuery();
    } else if (tab === 'paymentPlan') {
      crud3.query = { paymentCode: crud.rowData.code, limit: 2000 };
      crud3.toQuery();
    }
  }
};

let tabCount = ref({
  nonPaidCount: 0,
  paidCount: 0,
  allCount: 0
});
const loadTableData = () => {
  request({
    url: '/api/PaymentQuery/GetTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
const crud = CRUD(
  {
    title: '应用',
    url: '/api/PaymentQuery/GetList',
    idField: 'Id',
    method: 'post',
    crudMethod: {},
    tablekey: 'tablekeyItemhty', // 同一个页面使用多个 高级查询时，为了区分存储用户信息
    query: {},
    userNames: ['createdBy'],
    optShow: {
      add: false,
      edit: false,
      del: false,
      download: false,
      reset: true //重置按钮
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: (_crud) => {
        loadTableData();
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        } else {
          crudDetail.data = [];
        }
      }
    },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableItem
  }
);
const selectionValue = computed(() => {
  let value = 0;
  crud.selections.forEach((item) => {
    if (item.value) {
      value += item.value;
    }
  });
  return value;
});
const activeName = ref('all');

const search = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.name == 'paid') {
    crud.query.paied = 1;
  } else if (tab.props.name == 'unpaid') {
    crud.query.paied = 0;
  } else {
    crud.query.paied = -1;
  }
  crud.toQuery();
  loadTableData();
};
const crudDetail = CRUD(
  {
    title: '应用',
    url: '/api/AbatementQuery/GetList',
    method: 'post',
    tablekey: 'tablekeyDetailhty',
    query: { debtType: 'payment' },
    resultKey: {
      list: 'list',
      total: 'total'
    }
  },
  {
    table: tableDetail
  }
);
const crud3 = CRUD(
  {
    title: '付款明细',
    url: '/api/PaymentQuery/GetPaymentPlan',
    method: 'post',
    idField: 'id',
    tablekey: 'tablekeyDetail',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    optShow: {
      exportCurrentPage: true // 为false则不会显示按钮
    }
  },
  {
    table: tableDetail
  }
);
const queryListDetail = [
  {
    key: 'debtBillCode',
    label: '应付单号',
    show: true
  },
  {
    key: 'purchaseContactNo',
    label: '采购合同单号',
    show: true
  },
  {
    key: 'producerOrderNo',
    label: '厂家单号',
    show: true
  },
  {
    key: 'discount',
    label: '折扣',
    show: true
  },
  {
    key: 'purchaseCode',
    label: '采购单号',
    show: true
  },
  {
    key: 'serviceId',
    label: '业务单元',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/businessUnits/meta`,
    placeholder: '业务单元名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'nameLike', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
    show: false
  },
  {
    key: 'agentId',
    label: '供应商',
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    placeholder: '供应商名称搜索',
    valueK: 'id',
    labelK: 'name',
    props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
    show: false
  },
  {
    key: 'creditBillCode',
    label: '应收单号',
    show: true
  },
  {
    key: 'receiveCode',
    label: '收款单号',
    show: true
  },
  {
    key: 'customerIds',
    label: '客户',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    multiple: true,
    props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
    show: true
  },
  {
    key: 'hospitalName',
    label: '终端医院',
    show: true
  },
  {
    key: '订单号',
    label: 'orderNo',
    show: true
  },
  {
    key: 'invoiceNo',
    label: '发票号',
    show: true
  },
];
const queryObjectDetail = computed(() =>
  Object.fromEntries(queryListDetail.map((item) => [item.key, item]))
);
onBeforeMount(() => {
  // crud.toQuery();
});
onMounted(() => {
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  crud.query.billDateS = firstDay.getTime();
  crud.query.billDateE = lastDay.getTime();
  crud.toQuery();
  // loadTableData();
  // 表头拖拽必须在这里执行
  tableDrag(tableItem, crud.tablekey);
  tableDrag(tableDetail, crudDetail.tablekey);
});
const props = defineProps({
  __refresh: Boolean
});
onActivated(() => {
  if (props.__refresh) {
    // crud.toQuery();
    // loadTableData();
  }
});

// onMounted(() => {
//   crud.toQuery();
// });
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
const route = useRoute();
const isPuc = ref(true);
watch(
  () => crud.rowData.id,
  (n, o) => {
    crudDetail.query.debtBillCode = crud.rowData.code;
    crudDetail.toQuery();
    // if (!window.location.href.includes("puc")) {
      // isPuc.value = false;
      crud3.query.paymentCode = crud.rowData.code;
      crud3.toQuery();
    // }
    console.log('当前路由为：'+ window.location.href)
  },
  { deep: true }
);
//高级检索
const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId',
      props: { queryData: { functionUri: 'metadata://fam' } }
    }
  );
  return [
    {
      key: 'billDateS',
      endDate: 'billDateE',
      label: '单据日期',
      type: 'daterange',
      show: true
    },
    {
      key: 'paymentDateS',
      endDate: 'paymentDateE',
      label: '实际付款日期',
      type: 'daterange',
      show: true
    },
    ...items,
    {
      key: 'abatedStatus',
      label: '冲销状态',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: AbatedStatus,
      show: true
    },
    {
      key: 'agentId',
      label: '供应商',
      type: 'remoteSelect',
      method: 'post',
      url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
      labelK: 'name',
      valueK: 'id',
      props: { KeyWord: 'name', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
      show: true
    },

    {
      key: 'type',
      label: '付款单类型',
      type: 'select',
      labelK: 'name',
      valueK: 'id',
      dataList: PaymentTypeEnum,
      show: true
    },
    {
      key: 'serviceId',
      label: '业务单元',
      method: 'post',
      type: 'remoteSelect',
      url: `${window.gatewayUrl}v1.0/bdsapi/api/businessUnits/meta`,
      placeholder: '业务单元名称搜索',
      valueK: 'id',
      labelK: 'name',
      props: { KeyWord: 'nameLike', resultKey: 'data.data', queryData: { functionUri: 'metadata://fam' } },
      show: false
    },
    {
      key: 'code',
      label: '付款单号',
      show: true
    },
    {
      key: 'purchaseCode',
      label: '采购单号',
      show: true
    },
    {
      key: 'purchaseContactNo',
      label: '采购合同单号',
      show: true
    },
    {
      key: 'producerOrderNo',
      label: '厂家单号',
      show: true
    },
    {
      key: 'projectName',
      label: '项目名称',
      show: true
    },
    {
      key: 'paymentAutoItemCode',
      label: '批量付款单号',
      show: true
    },
  ]
});
const downloadFile = (code) => {
  request({
    url: '/api/PaymentQuery/GetKDFilePath?code=' + code,
    method: 'get'
  })
    .then((res) => {
      if (res.data.code == 200) {
        if (res.data.data != null && res.data.data.length > 0) {
          window.open(res.data.data[0].previewAddress);
        } else {
          ElMessage({
            showClose: true,
            message: '未找到金蝶回执单，请稍后再试！',
            type: 'error',
            duration: 3 * 1000
          });
        }
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((t) => {});
};

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
const ExportPayment = async () => {
  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportConvertPaymentList(crud.query, '付款单');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
const ExportConvertPaymentList = async (data, filename) => {
  await request({
    url: '/api/PaymentQuery/GetExportList',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
};
const searchCrud = () => {
  activeName.value = 'all';
  crud.query.paied = -1;
  // activeName.value = 'paid';
  crud.toQuery();
};
//协调服务导出
let downLoading = ref(false);
const downloadAsync = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};

const showfiles = ref([]);
const comfile_show = ref(false);
const showAttachFile = (showAttachFileids, id) => {
  if (showAttachFileids == '' || showAttachFileids.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：该数据没有附件！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    request({
      url: `/api/PaymentQuery/GetAttachFile`,
      method: 'POST',
      data: {
        Id: id
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          comfile_show.value = true;
          showfiles.value = res.data.data;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      });
  }
};

const showFileInfo = (fileid) => {
  FileViewer.show(
    [fileid], // 可以为数组和逗号隔开的字符串
    0, // 默认打开的下标
    {} // FileViewer props
  );
};

//文件大小格式化
const format = (size) => {
  if (size > 0) {
    if (size < 1000) {
      return size + 'B';
    } else if (size < 1000000) {
      return (size / 1000).toFixed(1) + 'KB';
    } else if (size < 1000000000) {
      return (size / 1000000).toFixed(1) + 'MB';
    } else {
      return (size / 1000000000).toFixed(1) + 'GB';
    }
  } else {
    return 0;
  }
}

const ExportConvertPaymentInfoList = async (data, filename) => {
  await request({
    url: '/api/paymentQuery/exportPaymentInfo',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' },
  })
    .then((res) => {
      debugger;
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
    })
    .catch((err) => {
      throw '请求错误';
    });
};

const exportPaymentInfo = async () => {
  const { billDateS, billDateE, paymentDateS, paymentDateE } = crud.query;

  const hasBillDate = billDateS && billDateE;
  const hasPaymentDate = paymentDateS && paymentDateE;

  if (!hasBillDate && !hasPaymentDate) {
    ElMessage({
      showClose: true,
      message: '请选择单据日期或实际付款日期',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }

  const dateRanges = [];
  if (hasBillDate) dateRanges.push({ start: billDateS, end: billDateE });
  if (hasPaymentDate) dateRanges.push({ start: paymentDateS, end: paymentDateE });

  for (const { start, end } of dateRanges) {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const diffMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth());

    if (diffMonths > 3) {
      ElMessage({
        showClose: true,
        message: '时间跨度不能超过三个月',
        type: 'error',
        duration: 3 * 1000
      });
      return;
    }
  }

  ElMessageBox.confirm(
    '确认导出所筛选的全部数据吗，若数据量大，请耐心等待哦~',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '数据处理中',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    try {
      await ExportConvertPaymentInfoList(crud.query, '付款信息');
    } catch (error) {
      loading.close();
    }
    loading.close();
  });
};
</script>
