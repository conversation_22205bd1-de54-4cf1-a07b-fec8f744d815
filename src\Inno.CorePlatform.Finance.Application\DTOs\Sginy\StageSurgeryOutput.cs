﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sginy
{
    /// <summary>
    /// 跟台-根据id查询跟台手术数据信息
    /// </summary>
    public class StageSurgeryOutput
    {
        /// <summary>
        /// 跟台手术单id
        /// </summary>
        public string? stageSurgeryId { get; set; }
        /// <summary>
        /// 跟台手术单号
        /// </summary>
        public string? stageSurgeryNo { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public long? billDate { get; set; }
        /// <summary>
        /// 手术类型
        /// </summary>
        public string? surgeryTypeId { get; set; }
        /// <summary>
        /// 跟台单id
        /// </summary>
        public Guid? stageInventoryId { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? companyId { get; set; }
        /// <summary>   
        /// 公司名称
        /// </summary>
        public string? companyName { get; set; }
        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? businessDeptId { get; set; }
        /// <summary>
        /// 医院id(客户id)
        /// </summary>
        public Guid? hospitalId { get; set; }
        /// <summary>
        /// 医院 （301医院）
        /// </summary>
        public string? hospital { get; set; }
        /// <summary>
        /// 医生id(客户部门负责人id)
        /// </summary>
        public string? doctorId { get; set; }
        /// <summary>
        /// 医生(石顺谨)
        /// </summary>
        public string? doctor { get; set; }
        /// <summary>
        /// 跟台员id
        /// </summary>
        public string? salesmanId { get; set; }
        /// <summary>
        /// 跟台员(maoyangsheng)
        /// </summary>
        public string? salesmanName { get; set; }
        /// <summary>
        /// 状态(1-手术中 99-已完成)
        /// </summary>
        public int status { get; set; }
        /// <summary>
        ///状态名(已完成)
        /// </summary>
        public string? statusName { get; set; }
        /// <summary>
        /// 类型(1-跟台转销售 10-跟台转暂存未用 20-跟台转暂存已用 30-跟台转退货)
        /// </summary>
        public int type { get; set; }
        /// <summary>
        /// 类型名（跟台转暂存未用）
        /// </summary>
        public string? typeName { get; set; }
        /// <summary>
        /// 手术单日期
        /// </summary>
        public long? surgeryTime { get; set; }
        /// <summary>
        /// 完成日期
        /// </summary>
        public long? completeTime { get; set; }
        /// <summary>
        /// 操作人（maoyangsheng）
        /// </summary>
        public string? @operator { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        public string? remark { get; set; }
        /// <summary>
        /// 销售子系统id
        /// </summary>
        public string? saleSystemId { get; set; }
        /// <summary>
        /// 创建人（maoyangsheng）
        /// </summary>
        public string? createdBy { get; set; }
        /// <summary>
        /// 创建人（毛阳圣）
        /// </summary>
        public string? createdByName { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public long? createdTime { get; set; }
        /// <summary>
        /// 记账日期
        /// </summary>
        public long accountingDateTime { get; set; }
        /// <summary>
        /// 更新人(maoyangsheng)
        /// </summary>
        public string? updatedBy { get; set; }
        /// <summary>
        /// 更新人(毛阳圣)
        /// </summary>
        public string? updatedByName { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public long? updatedTime { get; set; }
        /// <summary>
        /// 手术明细数据
        /// </summary>
        public List<DetailResItem>? detailRes { get; set; }
    }
    /// <summary>
    /// 手术明细数据
    /// </summary>
    public class DetailResItem
    {
        /// <summary>
        /// 手术单明细id
        /// </summary>
        public string? stageSurgeryDetailsId { get; set; }
        /// <summary>
        /// 跟台手术单id
        /// </summary>
        public string? stageSurgeryId { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid? projectId { get; set; }
        /// <summary>
        /// 项目名称(谢剑虹测试-寄售0720-001)
        /// </summary>
        public string? projectName { get; set; }
        /// <summary>
        /// 货号id
        /// </summary>
        public Guid? productId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 产品id
        /// </summary>
        public Guid? productNameId { get; set; }
        /// <summary>
        /// 产品名（硬脑（脊）膜修补片)
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 含税实际成本
        /// </summary>
        public decimal? unitCost { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? standardUnitCost { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxRate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int confirmQuantity { get; set; }
        /// <summary>
        /// 规格(20片一包)
        /// </summary>
        public string? specification { get; set; }
        /// <summary>
        /// 型号
        /// </summary>
        public string? model { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int mark { get; set; }
        /// <summary>
        ///类型描述（寄售）
        /// </summary>
        public string? markDesc { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 痕迹代码
        /// </summary>
        public string? stageTraceCode { get; set; }
        /// <summary>
        /// 跟台箱id
        /// </summary>
        public string? sginyContainerId { get; set; }
    }
}
