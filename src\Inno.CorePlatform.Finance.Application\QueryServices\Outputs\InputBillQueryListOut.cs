﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class InputBillQueryListOut
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanName { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime BillTime { get; set; }


        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 状态 1=临时发票，2=已提交，3=正在匹配，9=忽略
        /// </summary>
        public InputBillStatusEnum? Status { get; set; }

        /// <summary>
        /// 状态名称
        /// </summary>

        public string? StatusName
        {
            get
            {
                return Status.HasValue ? Status.GetDescription() : "";
            }
        }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }


        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }


        /// <summary>
        /// 金额
        /// </summary>
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额=金额*税率
        /// </summary>
        public decimal TaxAmount { get; set; }


        /// <summary>
        /// 总金额=金额+税额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }


        /// <summary>
        /// 导航属性-金蝶发票详情
        /// </summary>
        public List<InputBillDetailPo>? InputBillDetail { get; set; }

        /// <summary>
        /// 导航属性-提交发票详情
        /// </summary>
        public List<InputBillSubmitDetailPo>? InputBillSubmitDetail { get; set; }
        public string? Remark { get; set; }

        /// <summary>
        /// 取消勾稽时间
        /// </summary>
        public DateTime? CancelReconciliationTime { get; set; }

        /// <summary>
        /// 是否已取消勾稽
        /// </summary>
        public bool? IsCancelledReconciliation { get; set; }

        /// <summary>
        /// 取消勾稽状态名称
        /// </summary>
        public string? CancelReconciliationStatusName
        {
            get
            {
                return IsCancelledReconciliation.HasValue ? (IsCancelledReconciliation.Value ? "是" : "否") : "";
            }
        }
    }

    /// <summary>
    /// 根据应付单号获取进项发票号
    /// </summary>
    public class InputBillQueryByDebtCodesOutput
    {
        /// <summary>
        /// 进项发票号
        /// </summary>
        public string? InvoiceNumber { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 进项票金额
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }

        /// <summary>
        /// ID
        /// </summary>
        public Guid? Id { get; set; }
    }
}
