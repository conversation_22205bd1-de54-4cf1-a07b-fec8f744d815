﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.Extensions;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Models.File;
using Inno.CorePlatform.ServiceClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using NetTopologySuite.Index.HPRtree;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class ReconciliationLetterAppService : IReconciliationLetterAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly ICreditQueryService _creditQueryService;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IConfiguration _configuration;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly ILogisticsApiClient _logisticsApiClient;
        private readonly ISellApiClient _sellApiClient;
        private readonly IInventoryApiClient _inventoryApiClient;
        public ReconciliationLetterAppService(
            FinanceDbContext db,
            IBDSApiClient bDSApiClient,
            IUnitOfWork unitOfWork,
            IFileGatewayClient fileGatewayClient,
            ICreditQueryService creditQueryService,
            IConfiguration configuration,
            IWeaverApiClient weaverApiClient,
            ICodeGenClient codeGenClient,
            ILogisticsApiClient logisticsApiClient,
            ISellApiClient sellApiClient,
            IInventoryApiClient inventoryApiClient,
            IApplyBFFService applyBFFService)
        {
            this._db = db;
            this._configuration = configuration;
            this._unitOfWork = unitOfWork;
            this._bDSApiClient = bDSApiClient;
            this._fileGatewayClient = fileGatewayClient;
            this._creditQueryService = creditQueryService;
            this._codeGenClient = codeGenClient;
            this._weaverApiClient = weaverApiClient;
            this._logisticsApiClient = logisticsApiClient;
            this._sellApiClient = sellApiClient;
            this._inventoryApiClient = inventoryApiClient;
        } /// <summary>
          /// 删除
          /// </summary>
          /// <param name="id"></param>
          /// <returns></returns>
        public async Task<int> DeleteItemAsync(Guid? id)
        {
            var res = 0;
            var letterItem = await _db.ReconciliationLetterItem.Where(p => p.Id == id).FirstOrDefaultAsync();
            if (letterItem != null)
            {
                _db.ReconciliationLetterItem.Remove(letterItem);
                var letterDetails = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == id).ToListAsync();
                if (letterDetails != null && letterDetails.Count != 0)
                {
                    _db.ReconciliationLetterDetails.RemoveRange(letterDetails);
                }
                var reconciliationLetterProductDetails = await _db.ReconciliationLetterProductDetails.Where(p => p.ReconciliationLetterItemId == id).ToListAsync();
                if (reconciliationLetterProductDetails != null && reconciliationLetterProductDetails.Count != 0)
                {
                    _db.ReconciliationLetterProductDetails.RemoveRange(reconciliationLetterProductDetails);
                }
                if (!string.IsNullOrEmpty(letterItem.OARequestId))
                {
                    //删除oa流程
                    await _weaverApiClient.DelWorkFlow(letterItem.CreatedBy, Convert.ToInt32(letterItem.OARequestId));
                }
                res = await _unitOfWork.CommitAsync();

            }
            return res;
        }
        /// <summary>
        /// 删除详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="itemId"></param>
        /// <returns></returns>
        public async Task<int> DeleteDetailItemAsync(Guid id, Guid itemId)
        {
            var res = 0;
            var letter = await _db.ReconciliationLetterItem.Where(p => p.Id == itemId).FirstOrDefaultAsync();
            if (letter != null)
            {
                if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product)
                {
                    var reconciliationLetterProductDetail = await _db.ReconciliationLetterProductDetails.Where(p => p.Id == id).FirstOrDefaultAsync();
                    if (reconciliationLetterProductDetail != null)
                    {
                        letter.TotalAmount = letter.TotalAmount - reconciliationLetterProductDetail.Amount;
                        letter.ArrearsAmount = letter.ArrearsAmount - reconciliationLetterProductDetail.Amount;
                        _db.ReconciliationLetterProductDetails.Remove(reconciliationLetterProductDetail);
                    }
                }
                else
                {
                    var letterDetail = await _db.ReconciliationLetterDetails.Where(p => p.Id == id).FirstOrDefaultAsync();
                    if (letterDetail != null)
                    {
                        letter.ArrearsAmount = letter.ArrearsAmount - letterDetail.NonReceivedValue;
                        _db.ReconciliationLetterDetails.Remove(letterDetail);
                    }
                }
                res = await _unitOfWork.CommitAsync();
            }
            return res;
        }
        /// <summary>
        /// 提交对账函
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SubmitItemAsync(Guid? id, string user)
        {
            var res = BaseResponseData<string>.Success("操作成功");
            try
            {

                var letter = await _db.ReconciliationLetterItem.Where(p => p.Id == id).FirstOrDefaultAsync();
                if (letter != null)
                {
                    if (letter.Status != StatusEnum.waitSubmit)
                    {
                        res = BaseResponseData<string>.Failed(500, "只有待提交状态才能提交");
                        return res;
                    }
                    var outputFile = await CreateReconciliationLetterExcel(letter, user);
                    if (outputFile != null)
                    {
                        var guids = new List<Guid>();
                        guids.Add(outputFile.Id);
                        if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product)
                        {
                            letter.Status = StatusEnum.Complate;
                        }
                        else
                        {
                            var fileDirectUrls = await _fileGatewayClient.GetFileDirectUrlsAsync(new BizFileDirectUrlInput() { Ids = guids.ToArray() });
                            #region 提交oa审核
                            var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                            {
                                BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                                {
                                    Operator = letter.CreatedBy,
                                    RequestName = $"【财务-对账函】[{letter.BillCode}]-{letter.CompanyName}-{letter.CustomerName}",
                                    Remark = "",
                                    RequestId = 0,
                                    RequestLevel = 1,

                                },
                                MainData = new Gateway.Common.WeaverOA.MainData
                                {
                                    FCreatorID = letter.CreatedBy,
                                    Iframe_link = $"{_configuration["BaseUri"]}/fam/financeManagement/reconciliationLetterOA?id={letter.Id}", //PC的Iframe地址,
                                    Height_m = 480,
                                    Iframe_link_m = "",//手机地址
                                    CpDepartment = "",
                                    CPcompanyCode = letter.NameCode,
                                    Condition = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                                    Business_id = letter.Id.ToString(),
                                    IsTreasurerAudit = 1,

                                },
                                DetailData = new Gateway.Common.WeaverOA.DetailData
                                {

                                    DT1 = new List<dynamic>
                            {
                                new
                                {
                                    htxywj=new List<dynamic>
                                    {   new{
                                            fileName= outputFile.Name,
                                            filePath = fileDirectUrls.Urls.FirstOrDefault()
                                       }
                                    }

                                }
                            }
                                },
                                OtherParams = new Gateway.Common.WeaverOA.OtherParams
                                {
                                    IsNextFlow = 1,
                                }
                            };
                            if (string.IsNullOrEmpty(letter.OARequestId))
                            {
                                var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.ReconciliationLetterForm);
                                if (!oaRet.Status)
                                {
                                    res = BaseResponseData<string>.Failed(500, oaRet.Msg);
                                    return res;
                                }
                                letter.OARequestId = oaRet.Data.Requestid.ToString();
                            }
                            else
                            {
                                oaInput.BaseInfo.RequestId = int.Parse(letter.OARequestId);
                                var oaRet = await _weaverApiClient.SubmitWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.ReconciliationLetterForm);
                                if (!oaRet.Status)
                                {
                                    res = BaseResponseData<string>.Failed(500, oaRet.Msg);
                                    return res;
                                }
                            }
                            #endregion
                            letter.Status = StatusEnum.waitAudit;
                        }
                        letter.AttachFileIds = outputFile.Id.ToString();
                        letter.UpdatedTime = DateTime.Now;
                        letter.UpdatedBy = user;
                        _db.Update(letter);
                        await _unitOfWork.CommitAsync();
                    }
                    else
                    {
                        res = BaseResponseData<string>.Failed(500, "生成对账函失败");
                        return res;
                    }
                }
                else
                {
                    res = BaseResponseData<string>.Failed(500, "没有找到对账函或者此对账函已删除");
                }
            }
            catch (Exception ex)
            {

                res = BaseResponseData<string>.Failed(500, ex.Message);
            }
            return res;
        }
        /// <summary>
        /// 上传回函件
        /// </summary>
        public async Task<BaseResponseData<int>> AttachFileIds_letter(ReconciliationLetterAttachFileInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");
            try
            {
                var letter = await _db.ReconciliationLetterItem.Where(c => c.Id == input.ReconciliationLetterItemId).AsNoTracking().FirstOrDefaultAsync();
                if (letter != null)
                {
                    letter.ConfirmAttachFileIds = letter.ConfirmAttachFileIds + "," + input.FileIds;
                    _db.ReconciliationLetterItem.Update(letter);
                    await _unitOfWork.CommitAsync();
                }
            }
            catch (Exception ex)
            {
                BaseResponseData<int>.Failed(500, "操作失败！" + ex.Message);
            }
            return ret;
        }
        /// <summary>
        /// 查看回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BizFileUploadOutput>> GetAttachFile_letter(ReconciliationLetterAttachFileInput input)
        {
            var letter = await _db.ReconciliationLetterItem.Where(c => c.Id == input.ReconciliationLetterItemId).AsNoTracking().FirstOrDefaultAsync();
            var bizFiles = new List<BizFileUploadOutput>();
            if (letter != null && !string.IsNullOrEmpty(letter.ConfirmAttachFileIds))
            {
                var fileIds = letter.ConfirmAttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                return bizFiles;
            }
            return bizFiles;
        }
        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BizFileUploadOutput>> GetAttachFile(ReconciliationLetterAttachFileInput input)
        {
            var letter = await _db.ReconciliationLetterItem.Where(c => c.Id == input.ReconciliationLetterItemId).AsNoTracking().FirstOrDefaultAsync();
            var bizFiles = new List<BizFileUploadOutput>();
            if (letter != null && !string.IsNullOrEmpty(letter.AttachFileIds))
            {
                var fileIds = letter.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                return bizFiles;
            }
            return bizFiles;
        }
        /// <summary>
        /// 删除回函件
        /// </summary>
        public async Task<BaseResponseData<string>> DeleteAttachFileIds_letter(ReconciliationLetterAttachFileInput input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var letter = await _db.ReconciliationLetterItem.Where(c => c.Id == input.ReconciliationLetterItemId).AsNoTracking().FirstOrDefaultAsync();
            var newAttachFileIds = "";
            if (!string.IsNullOrEmpty(letter.ConfirmAttachFileIds))
            {
                foreach (var fildId in letter.ConfirmAttachFileIds.Split(","))
                {
                    if (!string.IsNullOrEmpty(fildId))
                    {
                        if (fildId.ToLower() != input.FileIds.ToLower())
                        {
                            newAttachFileIds += fildId + ",";
                        }
                    }
                }
            }
            newAttachFileIds = newAttachFileIds.TrimEnd(',');
            letter.ConfirmAttachFileIds = newAttachFileIds;
            _db.ReconciliationLetterItem.Update(letter);
            await _unitOfWork.CommitAsync();
            ret.Data = newAttachFileIds;
            return ret;
        }
        /// <summary>
        /// 创建对账函模板
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> CreateReconciliationLetter(CreateReconciliationLetterInput input)
        {
            try
            {
                var res = BaseResponseData<string>.Success("保存成功");
                var isExist = await _db.ReconciliationLetterItem.Where(p => p.CustomerId == input.CustomerId && 
                                                                            p.CompanyId == input.CompanyId && p.Deadline == input.Deadline && 
                                                                            p.ReconciliationLetterTemplate == input.ReconciliationLetterTemplate &&
                                                                            p.Status!=StatusEnum.Refuse).AsNoTracking().ToListAsync();
                if (isExist.Count() > 0)
                {
                    return BaseResponseData<string>.Failed(500, "操作失败,已经存在当前对账函,请勿重复创建");
                }
                if (input.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product && !input.DeadlineRange.Any())
                {
                    return BaseResponseData<string>.Failed(500, "操作失败,请选择日期范围");
                }

                var entity = new ReconciliationLetterItemPo
                {
                    CompanyId = input.CompanyId,
                    CustomerId = input.CustomerId,
                    CustomerName = input.CustomerName,
                    BillDate = DateTime.Now,
                    CreatedBy = input.CurrentUser ?? "none",
                    CreatedTime = DateTimeOffset.Now,
                    CompanyName = input.CompanyName,
                    ReconciliationLetterTemplate = input.ReconciliationLetterTemplate,
                    Deadline = input.Deadline,
                    BillCode = "",
                    Status = StatusEnum.waitSubmit,
                    NameCode = input.NameCode,
                    Remark = input.Remark,
                    Id = Guid.NewGuid()
                };
                if (input.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product && input.DeadlineRange.Any())
                {
                    entity.StartDate = input.DeadlineRange.First();
                }
                #region
                //生成单号
                var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { entity.CompanyId.ToString() } });
                var companyInfo = companyInfoOutput?.FirstOrDefault();
                if (companyInfo == null)
                {
                    entity.BillCode = Guid.NewGuid().ToString();
                    //throw new AppServiceException("公司信息不存在");
                }
                else
                {
                    if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
                    {
                        companyInfo.sysMonth = DateTime.Now.ToString("yyyy-MM");
                    }
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = input.BusinessArea ?? "FXBD",
                        BillType = "RTL",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    if (outPut.Status)
                    {
                        entity.BillCode = outPut.Codes.First();
                        var sysMonth = await _bDSApiClient.GetSystemMonth(companyInfo.companyId);
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        entity.BillDate = billDate;
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, $"生成Code失败，{outPut.Msg}");
                    }
                    if (string.IsNullOrEmpty(outPut.Codes[0]))
                    {
                        return BaseResponseData<string>.Failed(500, "单号生成异常，请重试！");
                    }
                }
                #endregion

                var reconciliationLetterProductDetails = new List<ReconciliationLetterProductDetailPo>();
                var detailList = new List<ReconciliationLetterDetailPo>();
                await InitDetailInfo(entity, reconciliationLetterProductDetails, detailList);
                await _db.ReconciliationLetterProductDetails.AddRangeAsync(reconciliationLetterProductDetails);
                await _db.ReconciliationLetterDetails.AddRangeAsync(detailList);
                await _db.ReconciliationLetterItem.AddAsync(entity);
                int count = await _unitOfWork.CommitAsync();
                if (count <= 0)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败");
                }
                res.Data = entity.Id.ToString();
                return res;
            }
            catch (Exception ex)
            {

                return BaseResponseData<string>.Failed(500, "保存失败!" + ex.Message);
            }
        }

        private async Task InitDetailInfo(
            ReconciliationLetterItemPo entity,
            List<ReconciliationLetterProductDetailPo> reconciliationLetterProductDetails,
            List<ReconciliationLetterDetailPo> detailList)
        {
            if (entity.ReconciliationLetterTemplate == ReconciliationLetterEnum.Invoce) //带发票明细
            {
                var invoices = await _creditQueryService.GetCreditInvoiceBalanceByCustomerId(entity.CompanyId.Value, entity.CustomerId.Value, entity.Deadline);
                var invoicesAmount = 0M;
                if (invoices != null && invoices.Count > 0)
                {
                    foreach (var item in invoices)
                    {
                        detailList.Add(new ReconciliationLetterDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            CreatedTime = DateTimeOffset.Now,
                            Classify = entity.ReconciliationLetterTemplate,
                            CreatedBy = entity.CreatedBy ?? "none",
                            ReconciliationLetterItemId = entity.Id,
                            NonReceivedValue = item.UnReceiveAmount,
                            ReceivedValue = item.ReceiveAmount,
                            Value = item.Amount,
                            BillCode = item.InvoiceNo,
                            BillDate = item.InvoiceDate,
                        });
                        invoicesAmount += item.UnReceiveAmount;
                    }
                }
                string customerId = entity.CustomerId.ToString();
                string companyId = entity.CompanyId.ToString();
                //应收总值
                var creditsTotalValue = await _db.Credits.Where(p => p.CustomerId == entity.CustomerId.Value &&
                                                                     p.CompanyId == entity.CompanyId &&
                                                                     p.BillDate <= entity.Deadline).SumAsync(p => p.Value);

                var status = new List<RecognizeReceiveItemStatusEnum> {
                    RecognizeReceiveItemStatusEnum.Completed,
                    RecognizeReceiveItemStatusEnum.PartCanceled
                };
                //认款总值
                var receiveTotalValue = await _db.RecognizeReceiveDetails.Include(p => p.RecognizeReceiveItem)
                                                                     .Where(p => p.RecognizeReceiveItem.BillDate <= entity.Deadline &&
                                                                                 p.CustomerId == customerId &&
                                                                                 p.RecognizeReceiveItem.CompanyId == companyId &&
                                                                                 p.RecognizeReceiveItem.ReceiveValue > 0 &&
                                                                                 p.Status != RecognizeReceiveDetailEnum.Cancel&&
                                                                                 status.Contains(p.RecognizeReceiveItem.Status)).SumAsync(p => p.Value);
                detailList.Add(new ReconciliationLetterDetailPo()
                {
                    Id = Guid.NewGuid(),
                    CreatedTime = DateTimeOffset.Now,
                    Classify = entity.ReconciliationLetterTemplate,
                    CreatedBy = entity.CreatedBy ?? "none",
                    ReconciliationLetterItemId = entity.Id,
                    NonReceivedValue = creditsTotalValue - receiveTotalValue - invoicesAmount,
                    ReceivedValue = 0,
                    Value = 0,
                    BillCode = "其它应收金额",
                    BillDate = DateTime.MinValue,
                });
                // 待收款金额
                entity.ArrearsAmount = detailList.Sum(p => p.NonReceivedValue);
            }
            else if (entity.ReconciliationLetterTemplate == ReconciliationLetterEnum.Credit) //应收
            {
                var credits = await _creditQueryService.GetCreditBalanceByCustomerId(entity.CompanyId.Value, entity.CustomerId.Value, entity.Deadline);
                if (credits != null && credits.Count > 0)
                {
                    foreach (var item in credits)
                    {
                        detailList.Add(new ReconciliationLetterDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            CreatedTime = DateTimeOffset.Now,
                            Classify = entity.ReconciliationLetterTemplate,
                            CreatedBy = entity.CreatedBy ?? "none",
                            ReconciliationLetterItemId = entity.Id,
                            NonReceivedValue = item.UnAbatmentAmount,
                            ReceivedValue = item.AbatmentAmount,
                            Value = item.Amount,
                            BillCode = item.CreditNo,
                            BillDate = item.CreditDate,
                        });
                    }
                }
                entity.ArrearsAmount = detailList.Sum(p => p.NonReceivedValue);
            }
            else if (entity.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product) //货号
            {
                DateTimeOffset Deadline1 = new DateTimeOffset(entity.StartDate.Value, TimeSpan.FromHours(8));
                DateTimeOffset Deadline2 = new DateTimeOffset(entity.Deadline, TimeSpan.FromHours(8)).AddDays(1).AddMinutes(-1);
                var storeDetails = await _inventoryApiClient.QuerySaleRecallDetailRec(new DTOs.Inventory.QuerySaleRecallDetailRecInput
                {
                    companyId = entity.CompanyId.ToString(),
                    shipperId = entity.CustomerId.ToString(),
                    recStartTime = Deadline1.ToUnixTimeMilliseconds(), // 将DateTime类型转换为时间戳(毫秒值)
                    recEndTime = Deadline2.ToUnixTimeMilliseconds(), // 将DateTime类型转换为时间戳(毫秒值)
                });
                var saleDetails = await _sellApiClient.GetSaleDetailsForFinanceAsync(new GetSaleDetailsForFinanceInput
                {
                    CompanyId = entity.CompanyId.Value,
                    CustomerId = entity.CustomerId.Value,
                    EndTime = Deadline2,
                    StartTime = Deadline1,
                });
                var receivedAmount = 0m;
                var credits = new List<CreditPo>();
                if (storeDetails.Any() && storeDetails.Count > 0)
                {
                    var storeInCodes = storeDetails.Select(p => p.storeInCode).ToList();
                    var creditstemp = await _db.Credits.Where(p => !string.IsNullOrEmpty(p.RelateCode) && storeInCodes.Contains(p.RelateCode)).AsNoTracking().ToListAsync();
                    if (creditstemp.Any())
                    {
                        credits.AddRange(creditstemp);
                    }
                    var invoiceCredits = new List<InvoiceCreditPo>();
                    if (credits.Any())
                    {
                        var creditIds = credits.Select(p => p.Id).ToHashSet();
                        invoiceCredits = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value) && p.IsCancel != true).ToListAsync();
                    }
                    foreach (var item in storeDetails)
                    {
                        var creditsTemp2 = creditstemp.Where(p => p.RelateCode == item.storeInCode).ToList();
                        var credit = creditstemp.FirstOrDefault();
                        var creditCodes = creditsTemp2.Select(p => p.BillCode).Distinct().ToList();
                        var invoiceCreditsTemp = new List<InvoiceCreditPo>();
                        if (credit != null)
                        {
                            invoiceCreditsTemp = invoiceCredits.Where(p => p.CreditId == credit.Id).ToList();
                        }
                        reconciliationLetterProductDetails.Add(new ReconciliationLetterProductDetailPo
                        {
                            Amount = item.amount,
                            BatchNo = item.lotNo,
                            BillCode = item.storeInCode,
                            BillDate = item.storeInApplyCreatedTime.ToDateTimeOffSet().ToBeiJingDateTime(),
                            BusinessType = "销售调回",
                            CreatedTime = item.storeInApplyCreatedTime.ToDateTimeOffSet(),
                            CreatedBy = item.storeInCreatedBy,
                            Id = new Guid(),
                            Price = item.price,
                            ProductNo = item.productNo,
                            Quantity = item.quantity,
                            ReconciliationLetterItemId = entity.Id,
                            Specification = item.specification,
                            Salesman = item.storeInCreatedBy,
                            Remark = item.remark,
                            ReceivableCode = string.Join(",", creditCodes),
                            InvoiceTime = invoiceCreditsTemp.Count() > 0 ? invoiceCreditsTemp.First().InvoiceTime : null,
                        });
                    }
                }
                if (saleDetails.Any() && saleDetails.Count > 0)
                {
                    var orderNos = saleDetails.Select(p => p.BillCode).ToList();
                    var creditstemp = await _db.Credits.Where(p => !string.IsNullOrEmpty(p.OrderNo) && orderNos.Contains(p.OrderNo)).AsNoTracking().ToListAsync();
                    if (creditstemp.Any())
                    {
                        credits.AddRange(creditstemp);
                    }

                    var invoiceCredits = new List<InvoiceCreditPo>();
                    if (credits.Any())
                    {
                        var creditIds = credits.Select(p => p.Id).ToHashSet();
                        invoiceCredits = await _db.InvoiceCredits.Where(p => creditIds.Contains(p.CreditId.Value) && p.IsCancel != true).ToListAsync();
                    }
                    foreach (var item in saleDetails)
                    {
                        var creditsTemp2 = creditstemp.Where(p => p.OrderNo == item.BillCode).ToList();
                        var credit = creditstemp.FirstOrDefault();
                        var creditCodes = creditsTemp2.Select(p => p.BillCode).Distinct().ToList();
                        var invoiceCreditsTemp = new List<InvoiceCreditPo>();
                        if (credit != null)
                        {
                            invoiceCreditsTemp = invoiceCredits.Where(p => p.CreditId == credit.Id).ToList();
                        }
                        reconciliationLetterProductDetails.Add(new ReconciliationLetterProductDetailPo
                        {
                            Amount = item.Amount,
                            BatchNo = item.LotNo,
                            BillCode = item.BillCode,
                            BillDate = item.BillDate,
                            BusinessType = item.SaleTypeName,
                            CreatedTime = item.CreatedTime,
                            CreatedBy = item.CreatedBy,
                            Salesman = item.CreatedBy,
                            Id = new Guid(),
                            Price = item.Price,
                            ProductNo = item.ProductNo,
                            Quantity = item.Quantity,
                            ReconciliationLetterItemId = entity.Id,
                            Remark = item.Remark,
                            Specification = item.Specification,
                            ReceivableCode = string.Join(",", creditCodes),
                            InvoiceTime = invoiceCreditsTemp.Count() > 0 ? invoiceCreditsTemp.First().InvoiceTime : null,
                            ShipmentCode = item.ShipmentCode,
                        });
                    }

                }
                if (credits.Count() > 0)
                {
                    credits = credits.DistinctBy(p => p.BillCode).ToList();
                    var creditCodes = credits.Select(p => p.BillCode).ToList();
                    var abatement = await _db.Abatements.Where(x => creditCodes.Contains(x.CreditBillCode)).AsNoTracking().Distinct().ToListAsync();
                    var abatement2 = await _db.Abatements.Where(x => creditCodes.Contains(x.DebtBillCode)).AsNoTracking().Distinct().ToListAsync();
                    abatement.AddRange(abatement2);
                    abatement = abatement.Distinct().ToList();
                    foreach (var credit in credits)
                    {
                        var abatementsTemp = abatement.Where(x => x.DebtBillCode == credit.BillCode || x.CreditBillCode == credit.BillCode).ToList();
                        if (abatementsTemp != null && abatementsTemp.Count() > 0)
                        {
                            if (credit.Value < 0)
                            {
                                receivedAmount = receivedAmount - abatementsTemp.Sum(x => x.Value);
                            }
                            else
                            {
                                receivedAmount = receivedAmount + abatementsTemp.Sum(x => x.Value);
                            }
                        }
                    }
                }
                entity.TotalAmount = reconciliationLetterProductDetails.Sum(p => p.Amount);
                entity.ReceivedAmount = receivedAmount;
                entity.ArrearsAmount = entity.TotalAmount.Value - receivedAmount;
            }
        }

        /// <summary>
        /// 更新对账函
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> UpdateReconciliationLetter(CreateReconciliationLetterInput input)
        {
            try
            {
                var res = BaseResponseData<string>.Success("编辑成功");
                var letterItem = await _db.ReconciliationLetterItem.Where(p => p.BillCode == input.BillCode).AsNoTracking().FirstOrDefaultAsync();
                if (letterItem == null)
                {
                    return BaseResponseData<string>.Failed(500, "编辑失败,没有找到当前对账函");
                }

                if (!letterItem.CustomerId.Equals(input.CustomerId) ||
                    letterItem.ReconciliationLetterTemplate != input.ReconciliationLetterTemplate ||
                    letterItem.Deadline != input.Deadline)
                {
                    //如果修改了业务字段
                    letterItem.CustomerId = input.CustomerId;
                    letterItem.CustomerName = input.CustomerName;
                    letterItem.Deadline = input.Deadline;
                    if (letterItem.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product)
                    {
                        letterItem.StartDate = input.DeadlineRange != null && input.DeadlineRange.Count() > 0 ? input.DeadlineRange.First() : letterItem.StartDate;
                    }
                    letterItem.ReconciliationLetterTemplate = input.ReconciliationLetterTemplate;
                    var detailList = new List<ReconciliationLetterDetailPo>();
                    var reconciliationLetterProductDetails = new List<ReconciliationLetterProductDetailPo>();
                    await InitDetailInfo(letterItem, reconciliationLetterProductDetails, detailList);
                    if (detailList.Count() > 0)
                    {
                        var delDetail = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == letterItem.Id).ToListAsync();
                        _db.ReconciliationLetterDetails.RemoveRange(delDetail);//删除原来的明细

                        await _db.ReconciliationLetterDetails.AddRangeAsync(detailList);//添加新的明细
                    }
                    if (reconciliationLetterProductDetails.Count() > 0)
                    {
                        //删除原来的明细
                        var delDetail = await _db.ReconciliationLetterProductDetails.Where(p => p.ReconciliationLetterItemId == letterItem.Id).ToListAsync();
                        _db.ReconciliationLetterProductDetails.RemoveRange(delDetail);
                        await _db.ReconciliationLetterProductDetails.AddRangeAsync(reconciliationLetterProductDetails);
                    }
                }

                letterItem.Remark = input.Remark;
                letterItem.UpdatedBy = input.CurrentUser;
                letterItem.UpdatedTime = DateTimeOffset.Now;

                _db.ReconciliationLetterItem.Update(letterItem);
                int count = await _unitOfWork.CommitAsync();

                if (count <= 0)
                {
                    return BaseResponseData<string>.Failed(500, "编辑失败");
                }
                res.Data = letterItem.Id.ToString();
                return res;
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "编辑失败!" + ex.Message);
            }
        }

        private async Task<BizFileUploadOutput> CreateReconciliationLetterExcel(ReconciliationLetterItemPo letter, string user)
        {
            var outputFile = new BizFileUploadOutput();
            try
            {
                if (letter == null)
                    return outputFile;
                if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Credit)
                {

                    var mstream = new MemoryStream();
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    var excelPackage = new ExcelPackage(mstream);
                    var worksheet = excelPackage.Workbook.Worksheets.Add("对账函");
                    int columnCount = 6;
                    // 设置所有列的宽度
                    for (int col = 1; col <= columnCount; col++)
                    {
                        worksheet.Column(col).Width = 15;
                    }
                    worksheet.Cells[1, 1, 1, 6].Merge = true;
                    worksheet.Cells[1, 1].Value = "财务对账函";
                    worksheet.Row(1).Height = 30;
                    worksheet.Cells["A1:F19"].Style.Font.Name = "宋体";
                    worksheet.Cells["A1"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells["A1"].Style.Font.Bold = true;
                    worksheet.Cells["A1"].Style.Font.Size = 20;
                    worksheet.Cells["A1"].Style.Font.Name = "华文楷体";
                    worksheet.Cells["A2"].Value = "尊敬的：";
                    worksheet.Cells["B2"].Value = letter.CustomerName;
                    worksheet.Cells[3, 1, 3, 6].Merge = true;
                    var text = "    为加强企业资金管理，降低企业内控风险，本公司现对往来账款进行询证。下列信息出自本公司账簿记录，如与贵院记录相符，请在本函下端“信息证明无误”处签章证明；如有不符，请在“信息不符”处列出这些项目的金额及详细资料。\r\n    截止Date，贵院欠我司账款为  人民币:ChineseNumber(¥Money元)";
                    text = text.Replace("Date", letter.Deadline.Year.ToString() + "年" + letter.Deadline.Month.ToString() + "月" + letter.Deadline.Day + "日");
                    text = text.Replace("ChineseNumber", ChineseNumberConverter.GetChinaMoney(letter.ArrearsAmount));
                    text = text.Replace("Money", letter.ArrearsAmount.ToString());
                    worksheet.Cells[3, 1].Value = text;
                    worksheet.Cells["A3"].Style.WrapText = true; //单元格自动换行
                    worksheet.Row(3).Height = 100;
                    worksheet.Cells["A3"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
                    worksheet.Cells["A3"].Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;//上下居中
                    worksheet.Cells[6, 1].Value = "本函仅为符合账目之用，并非催款结算。";
                    worksheet.Cells[7, 1].Value = "若款项在上述日期之前已经付清，仍请及时函复为盼。";
                    worksheet.Cells[9, 4, 9, 6].Merge = true;
                    worksheet.Cells[9, 4].Value = letter.CompanyName;
                    worksheet.Cells["D9"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells[10, 5, 10, 6].Merge = true;
                    worksheet.Cells[10, 5].Value = letter.Deadline.Year.ToString() + "年" + letter.Deadline.Month.ToString() + "月" + letter.Deadline.Day + "日";
                    worksheet.Cells["E10"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells[11, 1].Value = "结论：";
                    worksheet.Cells["A11"].Style.Font.Bold = true;
                    worksheet.Cells[12, 1].Value = "1.信息证明无误。";
                    worksheet.Cells[12, 3].Value = "2.信息不符，请列明不符项目。";
                    worksheet.Cells["A12:F19"].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    worksheet.Cells["A12:B19"].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    worksheet.Cells[17, 2].Value = "（公司盖章）";
                    worksheet.Cells["B17"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[17, 6].Value = "（公司盖章）";
                    worksheet.Cells["F17"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;

                    worksheet.Cells[18, 2].Value = "年   月   日";
                    worksheet.Cells["B18"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[18, 6].Value = "年   月   日";
                    worksheet.Cells["F18"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[19, 1].Value = "经办人：";
                    worksheet.Cells[19, 3].Value = "经办人：";

                    excelPackage.Save();
                    mstream.Position = 0;
                    outputFile = await _fileGatewayClient.UploadBizFileContentAsync(new BizFileUploadInput
                    {
                        AppId = "fam",
                        BizType = "finance",
                        Folders = new List<string>() { "ReconciliationLetter" },
                        ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        Tags = new List<BizFileUploadTag>() { },
                    }, mstream, "财务对账函（不带发票明细）" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx", user);
                }
                else if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Invoce)
                {
                    var letterDetails = _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == letter.Id && p.Classify == ReconciliationLetterEnum.Invoce).OrderByDescending(p => p.BillDate).AsNoTracking().ToList();
                    var mstream = new MemoryStream();
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    var excelPackage = new ExcelPackage(mstream);
                    var worksheet = excelPackage.Workbook.Worksheets.Add("对账函");
                    int columnCount = 6;
                    // 设置所有列的宽度
                    for (int col = 1; col <= columnCount; col++)
                    {
                        worksheet.Column(col).Width = 15;
                    }
                    worksheet.Cells[1, 1, 1, 6].Merge = true;
                    worksheet.Cells[1, 1].Value = "财务对账函";
                    worksheet.Row(1).Height = 30;
                    worksheet.Cells["A1:F22"].Style.Font.Name = "宋体";
                    worksheet.Cells["A1"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells["A1"].Style.Font.Bold = true;
                    worksheet.Cells["A1"].Style.Font.Size = 20;
                    worksheet.Cells["A1"].Style.Font.Name = "华文楷体";
                    worksheet.Cells["A2"].Value = "尊敬的：";
                    worksheet.Cells["B2"].Value = letter.CustomerName;
                    worksheet.Cells[3, 1, 3, 6].Merge = true;
                    var text = "    为加强企业资金管理，降低企业内控风险，本公司现对往来账款进行询证。下列信息出自本公司账簿记录，如与贵院记录相符，请在本函下端“信息证明无误”处签章证明；如有不符，请在“信息不符”处列出这些项目的金额及详细资料。\r\n    截止Date，贵院欠我司账款为  人民币:ChineseNumber(¥Money元)";
                    text = text.Replace("Date", letter.Deadline.Year.ToString() + "年" + letter.Deadline.Month.ToString() + "月" + letter.Deadline.Day + "日");
                    text = text.Replace("ChineseNumber", ChineseNumberConverter.GetChinaMoney(letter.ArrearsAmount));
                    text = text.Replace("Money", letter.ArrearsAmount.ToString());
                    worksheet.Cells[3, 1].Value = text;
                    worksheet.Cells["A3"].Style.WrapText = true; //单元格自动换行
                    worksheet.Row(3).Height = 100;
                    worksheet.Cells["A3"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Left;
                    worksheet.Cells["A3"].Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;//上下居中
                    worksheet.Cells[4, 1].Value = "开票日期";
                    worksheet.Cells[4, 2].Value = "发票号码";
                    worksheet.Cells[4, 3].Value = "发票金额";
                    worksheet.Cells[4, 4].Value = "已收款";
                    worksheet.Cells[4, 5].Value = "未收款";
                    worksheet.Cells[4, 6].Value = "备注";
                    worksheet.Cells[9, 1].Value = "本函仅为符合账目之用，并非催款结算。";
                    worksheet.Cells[10, 1].Value = "若款项在上述日期之前已经付清，仍请及时函复为盼。";
                    worksheet.Cells[12, 4, 12, 6].Merge = true;
                    worksheet.Cells[12, 4].Value = letter.CompanyName;
                    worksheet.Cells["D12"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells[13, 5, 13, 6].Merge = true;
                    worksheet.Cells[13, 5].Value = letter.Deadline.Year.ToString() + "年" + letter.Deadline.Month.ToString() + "月" + letter.Deadline.Day + "日";
                    worksheet.Cells["E13"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                    worksheet.Cells[14, 1].Value = "结论：";
                    worksheet.Cells["A14"].Style.Font.Bold = true;
                    worksheet.Cells[15, 1].Value = "1.信息证明无误。";
                    worksheet.Cells[15, 3].Value = "2.信息不符，请列明不符项目。";
                    worksheet.Cells["A15:F22"].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    worksheet.Cells["A15:B22"].Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                    worksheet.Cells[20, 2].Value = "（公司盖章）";
                    worksheet.Cells["B20"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[20, 6].Value = "（公司盖章）";
                    worksheet.Cells["F20"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;

                    worksheet.Cells[21, 2].Value = "年   月   日";
                    worksheet.Cells["B21"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[21, 6].Value = "年   月   日";
                    worksheet.Cells["F21"].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    worksheet.Cells[22, 1].Value = "经办人：";
                    worksheet.Cells[22, 3].Value = "经办人：";
                    int startRow = 5;
                    for (int i = 0; i < letterDetails.Count(); i++)
                    {
                        worksheet.InsertRow(startRow, 1, 4);
                        worksheet.Cells[startRow, 1].Value = letterDetails[i].BillDate.ToString("yyyy-MM-dd");
                        worksheet.Cells[startRow, 2].Value = letterDetails[i].BillCode;
                        worksheet.Cells[startRow, 3].Value = letterDetails[i].Value;
                        worksheet.Cells[startRow, 4].Value = letterDetails[i].ReceivedValue;
                        worksheet.Cells[startRow, 5].Value = letterDetails[i].NonReceivedValue;
                        worksheet.Cells[startRow, 6].Value = letter.Remark;
                        startRow++;
                        if (i == letterDetails.Count() - 1)
                        {
                            worksheet.Cells[startRow, 1].Value = "总计";
                            worksheet.Cells[startRow, 3].Value = letterDetails.Sum(p => p.Value);
                            worksheet.Cells[startRow, 4].Value = letterDetails.Sum(p => p.ReceivedValue);
                            worksheet.Cells[startRow, 5].Value = letterDetails.Sum(p => p.NonReceivedValue);
                            worksheet.Cells["A" + startRow.ToString() + ":F" + startRow.ToString()].Style.Font.Bold = true;
                        }
                    }
                    worksheet.Cells["A4:F4"].Style.Font.Bold = true;
                    excelPackage.Save();
                    mstream.Position = 0;
                    outputFile = await _fileGatewayClient.UploadBizFileContentAsync(new BizFileUploadInput
                    {
                        AppId = "fam",
                        BizType = "finance",
                        Folders = new List<string>() { "ReconciliationLetter" },
                        ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        Tags = new List<BizFileUploadTag>() { },
                    }, mstream, "财务对账函" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx", user);
                }
                else if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Product)
                {
                    var users = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
                    {
                        Names = new List<string> { letter.CreatedBy }
                    });
                    var list = await _db.ReconciliationLetterProductDetails.Where(p => p.ReconciliationLetterItemId == letter.Id).ToListAsync();
                    var receivableCodes = list.Select(p => p.ReceivableCode).ToList();
                    var creditCodeStr = string.Join(",", receivableCodes);
                    var creditCodes = new HashSet<string>();
                    if (!string.IsNullOrEmpty(creditCodeStr))
                    {
                        creditCodes = creditCodeStr.Split(',').ToHashSet();
                    }
                    var credits = await _db.Credits.Where(p => creditCodes.Contains(p.BillCode)).ToListAsync();
                    var mstream = new MemoryStream();
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    using (var package = new ExcelPackage(mstream))
                    {
                        var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                        worksheet.Cells[1, 1, 1, 14].Merge = true;
                        worksheet.Cells[1, 1].Value = letter.CompanyName + "对账单";
                        worksheet.Row(1).Height = 30;
                        worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // 设置水平居中
                        worksheet.Cells[1, 1].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                        worksheet.Cells[2, 1].Value = "客户：";
                        worksheet.Cells[2, 2].Value = letter.CustomerName;
                        worksheet.Cells[2, 5].Value = "期间";
                        worksheet.Cells[2, 6, 2, 8].Merge = true;
                        worksheet.Cells[2, 6].Value = letter.StartDate.Value.ToString("yyyy-MM-dd") + " 至 " + letter.Deadline.ToString("yyyy-MM-dd");
                        worksheet.Cells[2, 11].Value = "业务员";
                        worksheet.Cells[2, 12].Value = users.Data.List.First().DisplayName;
                        worksheet.Cells[4, 1].Value = "日期";
                        worksheet.Cells[4, 2].Value = "业务类型";
                        worksheet.Cells[4, 3].Value = "随货单号";
                        worksheet.Cells[4, 4].Value = "单据号";
                        worksheet.Cells[4, 5].Value = "业务员";
                        worksheet.Cells[4, 6].Value = "货号";
                        worksheet.Cells[4, 7].Value = "规格";
                        worksheet.Cells[4, 8].Value = "批号";
                        worksheet.Cells[4, 9].Value = "单价";
                        worksheet.Cells[4, 10].Value = "数量";
                        worksheet.Cells[4, 11].Value = "金额";
                        worksheet.Cells[4, 12].Value = "开票时间";
                        worksheet.Cells[4, 13].Value = "应收单号";
                        worksheet.Cells[4, 14].Value = "项目名称";
                        worksheet.Cells[4, 15].Value = "厂家";
                        worksheet.Cells[4, 16].Value = "备注";
                        int row = 5;
                        foreach (var detail in list)
                        {
                            var credit = credits.Where(p => detail.ReceivableCode.Contains(p.BillCode)).FirstOrDefault();
                            worksheet.Cells[row, 1].Value = detail.BillDate.ToString("yyyy-MM-dd");
                            worksheet.Cells[row, 2].Value = detail.BusinessType;
                            worksheet.Cells[row, 3].Value = detail.ShipmentCode;
                            worksheet.Cells[row, 4].Value = detail.BillCode;
                            worksheet.Cells[row, 5].Value = detail.Salesman;
                            worksheet.Cells[row, 6].Value = detail.ProductNo;
                            worksheet.Cells[row, 7].Value = detail.Specification;
                            worksheet.Cells[row, 8].Value = detail.BatchNo;
                            worksheet.Cells[row, 9].Value = detail.Price;
                            worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 10].Value = detail.Quantity;
                            worksheet.Cells[row, 11].Value = detail.Amount;
                            worksheet.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 12].Value = detail.InvoiceTime.HasValue ? detail.InvoiceTime.Value.ToString("yyyy-MM-dd") : "";
                            worksheet.Cells[row, 13].Value = detail.ReceivableCode;
                            worksheet.Cells[row, 14].Value = credit == null ? "" : credit.ProjectName;
                            worksheet.Cells[row, 15].Value = credit == null ? "" : credit.ProducerName;
                            worksheet.Cells[row, 16].Value = detail.Remark;
                            row++;
                        }
                        worksheet.Cells[row + 1, 10].Value = "合计总金额:";
                        worksheet.Cells[row + 1, 11].Value = letter.TotalAmount;
                        worksheet.Cells[row + 1, 11].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row + 2, 10].Value = "已回款总金额:";
                        worksheet.Cells[row + 2, 11].Value = letter.ReceivedAmount;
                        worksheet.Cells[row + 2, 11].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row + 3, 10].Value = "待回款总金额:";
                        worksheet.Cells[row + 3, 11].Value = letter.ArrearsAmount;
                        worksheet.Cells[row + 3, 11].Style.Numberformat.Format = "#,##0.00";
                        var headerRow = worksheet.Row(1);
                        headerRow.Style.Font.Bold = true;
                        package.Save();
                        mstream.Position = 0;
                        outputFile = await _fileGatewayClient.UploadBizFileContentAsync(new BizFileUploadInput
                        {
                            AppId = "fam",
                            BizType = "finance",
                            Folders = new List<string>() { "ReconciliationLetter" },
                            ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            Tags = new List<BizFileUploadTag>() { },
                        }, mstream, "财务对账函" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx", user);
                    }
                }

            }
            catch (Exception ex)
            {
                return null;
            }
            return outputFile;
        }
        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<string>> UpdateStatus(Guid id, StatusEnum statusEnum)
        {

            var ret = BaseResponseData<string>.Success("操作成功！");
            var letter = await _db.ReconciliationLetterItem.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            if (letter != null)
            {
                letter.Status = statusEnum;
                _db.ReconciliationLetterItem.Update(letter);
            }
            else
            {
                return BaseResponseData<string>.Failed(500, "获取对账函失败!对账函Id:" + id);
            }
            await _unitOfWork.CommitAsync();
            return ret;
        }
        /// <summary>
        /// 根据请求id 更新询证函回函件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="attachFileDtos"></param>
        /// <returns></returns>
        public async Task UpdateConfirmAttachFileldsByRequestId(string id, List<AttachFileDto> attachFileDtos)
        {
            var letter = await _db.ReconciliationLetterItem.Where(p => p.OARequestId == id).AsNoTracking().FirstOrDefaultAsync();
            if (letter != null)
            {
                foreach (var item in attachFileDtos)
                {
                    letter.ConfirmAttachFileIds = letter.ConfirmAttachFileIds + "," + item.AttachFileId;
                }

                _db.ReconciliationLetterItem.Update(letter);
                await _unitOfWork.CommitAsync();
            }
        }
        /// <summary>
        /// 重新获取同步对账函明细数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> ResetItemAsync(Guid? id, string user)
        {
            try
            {
                var res = BaseResponseData<string>.Success("同步成功");
                var isExist = await _db.ReconciliationLetterItem.Where(p => p.Id == id).AsNoTracking().ToListAsync();
                if (isExist.Count() == 0)
                {
                    return BaseResponseData<string>.Failed(500, "同步失败,没有找到当前对账函");
                }
                var letterItem = isExist.FirstOrDefault();
                var detailList = new List<ReconciliationLetterDetailPo>();
                var reconciliationLetterProductDetails = new List<ReconciliationLetterProductDetailPo>();
                await InitDetailInfo(letterItem, reconciliationLetterProductDetails, detailList);
                if (detailList.Count() > 0)
                {
                    var delDetail = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == letterItem.Id).ToListAsync();
                    _db.ReconciliationLetterDetails.RemoveRange(delDetail);//删除原来的明细

                    await _db.ReconciliationLetterDetails.AddRangeAsync(detailList);//添加新的明细
                }
                if (reconciliationLetterProductDetails.Count() > 0)
                {
                    //删除原来的明细
                    var delDetail = await _db.ReconciliationLetterProductDetails.Where(p => p.ReconciliationLetterItemId == letterItem.Id).ToListAsync();
                    _db.ReconciliationLetterProductDetails.RemoveRange(delDetail);
                    await _db.ReconciliationLetterProductDetails.AddRangeAsync(reconciliationLetterProductDetails);
                }

                letterItem.UpdatedBy = user;
                letterItem.UpdatedTime = DateTimeOffset.Now;
                _db.ReconciliationLetterItem.Update(letterItem);
                int count = await _unitOfWork.CommitAsync();

                if (count <= 0)
                {
                    return BaseResponseData<string>.Failed(500, "同步失败");
                }
                res.Data = letterItem.Id.ToString();
                return res;
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "同步失败!" + ex.Message);
            }
        }
        /// <summary>
        /// 对账函物流打印模板
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<HttpContent> GetReconciliationLetterPDF(Guid id)
        {
            var letter = await _db.ReconciliationLetterItem.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            if (letter == null)
            {
                throw new ApplicationException("没有找到对账函");
            }
            var input = new ReconciliationLetterPrintInput();
            var companyList = await _bDSApiClient.GetCompanyMetaAsync(new CompanyMetaInput() { id = letter.CompanyId.Value.ToString() });
            if (companyList.Count == 0)
            {
                throw new ApplicationException("没有获取到法人公司");
            }
            var dictionaryOutputs = await _bDSApiClient.GetDataDictionaryListByType("PrintTemplate");
            if (dictionaryOutputs.Count == 0 || dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函").ToList().Count == 0 || dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函（不带发票明细）").ToList().Count == 0)
            {
                throw new ApplicationException("请先配置对账函模板数据字典");
            }
            if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Invoce)
            {
                input = new ReconciliationLetterPrintInput()
                {
                    templateType = dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函模板").FirstOrDefault().DictionaryCode,
                    templateName = dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函").FirstOrDefault().DictionaryCode,
                    companyId = letter.CompanyId.Value.ToString(),
                    fileType = "pdf",
                    classification = 1
                };
                var letterDetails = await _db.ReconciliationLetterDetails.Where(p => p.ReconciliationLetterItemId == id && p.Classify == ReconciliationLetterEnum.Invoce).AsNoTracking().OrderByDescending(p => p.BillDate).ToListAsync();
                input.list = new List<LetterInvoinceList>();
                foreach (var item in letterDetails)
                {
                    input.list.Add(new LetterInvoinceList()
                    {
                        billcode = item.BillCode,
                        billdate = item.BillDate.ToString("yyyy-MM-dd"),
                        nonreceivedvalue = item.NonReceivedValue.ToString(),
                        receivedvalue = item.ReceivedValue.ToString(),
                        remark = letter.Remark,
                        value = item.Value.ToString(),

                    });
                }
                input.@params = new LetterAndInvoiceParams()
                {
                    chinesemoney = ChineseNumberConverter.GetChinaMoney(letter.ArrearsAmount),
                    companyname = letter.CompanyName,
                    customername = letter.CustomerName,
                    deadlinedate = letter.Deadline.ToString("yyyy-MM-dd"),
                    money = letter.ArrearsAmount.ToString(),
                    nonreceivedvaluesum = letterDetails.Sum(p => p.NonReceivedValue).ToString(),
                    receivedvaluesum = letterDetails.Sum(p => p.ReceivedValue).ToString(),
                    valuesum = letterDetails.Sum(p => p.Value).ToString(),

                };
            }
            else if (letter.ReconciliationLetterTemplate == ReconciliationLetterEnum.Credit)
            {
                input = new ReconciliationLetterPrintInput()
                {
                    templateType = dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函模板").FirstOrDefault().DictionaryCode,
                    templateName = dictionaryOutputs.Where(p => p.DictionaryName == "财务对账函（不带发票明细）").FirstOrDefault().DictionaryCode,
                    companyId = letter.CompanyId.Value.ToString(),
                    fileType = "pdf",
                    classification = 1
                };
                input.@params = new LetterAndInvoiceParams()
                {
                    chinesemoney = ChineseNumberConverter.GetChinaMoney(letter.ArrearsAmount),
                    companyname = letter.CompanyName,
                    customername = letter.CustomerName,
                    deadlinedate = letter.Deadline.ToString("yyyy-MM-dd"),
                    money = letter.ArrearsAmount.ToString(),
                };
            }
            return await _logisticsApiClient.GetReconciliationLetterPDFStream(input);
        }
    }
}
