﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs
{
    public class BusinessDeptMetaInput: BDSBaseInput
    {
        /// <summary>
        /// 数据策略权限地址
        /// </summary>
        public string? functionUri { get; set; }

        /// <summary>
        /// 核算部门名称
        /// </summary>
        public List<string?>? names { get; set; }
    }

    public class GetFlatCheckedDeptsInput  
    {
        /// <summary>
        /// 数据策略权限地址
        /// </summary>
        public string? functionUri { get; set; }
         
    }
}
