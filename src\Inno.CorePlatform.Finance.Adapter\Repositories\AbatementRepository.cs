﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class AbatementRepository : EfBaseRepository<Guid, Abatement, AbatementPo>, IAbatementRepository
    {
        private readonly FinanceDbContext _db;
        public AbatementRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> AddManyAsync(List<Abatement> lstAbt)
        {
            if (lstAbt.Count <= 0)
            {
                throw new Exception("冲销明细不能为空！");
            }
            var lstAbtPo = lstAbt.Adapt<List<AbatementPo>>();
            await _db.Abatements.AddRangeAsync(lstAbtPo);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        public override async Task<int> AddAsync(Abatement root)
        {
            var abtPo = root.Adapt<AbatementPo>();
            await _db.Abatements.AddAsync(abtPo);
            return 0;
        }

        public override async Task<int> UpdateAsync(Abatement root)
        {
            var isExist = await _db.Abatements.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("冲销明细不存在！");
            }

            var po = root.Adapt<AbatementPo>();

            _db.Abatements.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        protected override AbatementPo CreateDeletingPo(Guid id)
        {
            return new AbatementPo { Id = id };
        }

        protected override async Task<AbatementPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.Abatements.FirstOrDefaultAsync(x => x.Id == id);
        }
    }
}
