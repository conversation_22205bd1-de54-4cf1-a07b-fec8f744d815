﻿using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IBulkPaymentQueryService
    {
        /// <summary>
        /// 获取批量付款清单
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<PaymnetItemOutput>> GetPaymentItems(BulkPaymentQueryInput input);
        /// <summary>
        /// 获取批量付款详情
        /// </summary>
        /// <returns></returns>
        Task<List<PaymentDetailOutput>> GetPaymentDetails(QueryById id);
        /// <summary>
        /// 获取批量付款详情——按供应商聚合
        /// </summary>
        /// <returns></returns>
        Task<List<PaymentAggratByAgent>> GetPaymentDetailsAggregation(Guid? id);
        /// <summary>
        /// 获取数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BulkPaymentTabOutput> GetTabCountAsync(BulkPaymentQueryInput input);
        /// <summary>
        /// 根据公司id获取收款单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<SelectAssemblyOutput>> GetReceiveCodesByCompanyId(BulkPaymentQueryInput input);
        Task<List<PaymentAutoDetailOfPurchaseOutput>> GetPaymentDetailsByCodes(PaymentAutoDetailOfPurchaseInput input);
    }
}
