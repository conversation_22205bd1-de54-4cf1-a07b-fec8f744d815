﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class ExchangeRateInput
    {
        /// <summary>
        /// 失效日期
        /// </summary>

        public DateTime Expirydate { get; set; }
        /// <summary>
        /// 直接汇率值
        /// </summary> 
        public decimal Excval { get; set; }
        /// <summary>
        /// 间接汇率值
        /// </summary> 
        public decimal Indirectexrate { get; set; }
        /// <summary>
        /// 生效日期
        /// </summary> 
        public DateTime Effectdate { get; set; }
        /// <summary>
        /// 原币.名称
        /// </summary> 
        public string OrgcurName { get; set; }
        /// <summary>
        /// 目标币.名称
        /// </summary>

        [MaxLength(100)]
        public string CurName { get; set; }
    }

    public class GetExchangeRateInput
    {
        /// <summary>
        /// 目标币.名称 默认为人民币
        /// </summary>
        public string? CurName { get; set; } = "人民币";
        /// <summary>
        /// 原币.名称人民币，港币，日元，美元，欧元，英镑，韩元
        /// </summary>
        public string OrgcurName { get; set; }
        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime Effectdate { get; set; }
    }

    public class GetExchangeRateOutput : ExchangeRateInput { }
}
