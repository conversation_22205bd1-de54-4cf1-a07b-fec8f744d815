﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class PaymnetItemOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? SumValue { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 附言
        /// </summary>
        public string? TransferDiscourse { get; set; } 
        /// <summary>
        /// 状态
        /// </summary>
        public PaymentAutoItemStatusEnum? Status { get; set; }
        /// <summary>
        /// 状态描述
        /// </summary>
        public string? StatusDescription { get { return this.Status != null ? this.Status.GetDescription() : ""; }}

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get;set; }
        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 预计付款日期
        /// </summary>
        public string ProbablyPayTime { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        public string? AttachFileIds { get; set; }
    }
}
