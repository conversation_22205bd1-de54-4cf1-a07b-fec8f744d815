﻿using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Models.File;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 发票入账单列表查询出参
    /// </summary>
    public class InvoiceReceiptItemQueryOutput
    {
        /// <summary>
        /// 主键id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 单据号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTimeOffset? BillDate { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public string? BillDateStr
        {
            get
            {
                return BillDate.HasValue ? BillDate.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 合约回款天数
        /// </summary>
        public int? BackAmountDays { get; set; }

        /// <summary>
        /// 销售账期天数
        /// </summary>
        public int? SaleAccountPeriodDays { get; set; }

        /// <summary>
        /// 实际回款天数
        /// </summary>
        public int? ActualBackAmountDays { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string? StatusStr
        {
            get
            {
                return Status != null ? Status.GetDescription() : string.Empty;
            }
        }

        /// <summary>
        /// 部门路径名称
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 部门路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 部门id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 客户名
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 附件ids
        /// </summary>
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// OARequestId
        /// </summary>
        public string? OARequestId { get; set; }

        /// <summary>
        /// 审批时间（最后修改时间）
        /// </summary>
        public DateTimeOffset? UpdatedTime { get; set; }

        /// <summary>
        /// 审批日期字段
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 审批日期字段
        /// </summary>
        public string? ApprovalTimeStr
        {
            get
            {
                return ApprovalTime.HasValue ? ApprovalTime.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }
    }

    /// <summary>
    /// 导出数据
    /// </summary>
    public class InvoiceReceiptDownLoadOutput : InvoiceReceiptItemQueryOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }
        /// <summary>
        /// 发票验证码
        /// </summary>
        public string? InvoiceCheckCode { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public string? InvoiceTimeStr
        {
            get
            {
                return InvoiceTime.HasValue ? InvoiceTime.Value.ToString("yyyy-MM-dd") : string.Empty;
            }
        }
        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 开票状态
        /// </summary>
        public bool? IsCancel { get; set; }
        /// <summary>
        /// 开票状态
        /// </summary>
        public string? IsCancelStr
        {
            get
            {
                return string.IsNullOrEmpty(InvoiceNo) ? string.Empty: (IsCancel.HasValue && IsCancel.Value? "已取消" : "已开票");
            }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 发票入账单主体查询出参
    /// </summary>
    public class InvoiceReceiptItemEditOutput : InvoiceReceiptItemQueryOutput
    {
        public List<InvoiceReceiptDetailQueryOutput> InvoiceDetail { get; set; }

        public List<BizFileUploadOutput> Attachments { get; set; }
    }

    /// <summary>
    /// 发票入账列表页签数据
    /// </summary>
    public class InvoiceReceiptListTabOutput
    {
        public int WaitSubmitCount { get; set; }
        public int WaitAuditCount { get; set; }
        public int ComplateCount { get; set; }
        public int AllCount { get; set; }
        public int MyCount { get; set; }
    }

    /// <summary>
    /// 选择组件返回Dto
    /// </summary>
    public class InvoiceReceiptsSelectAssemblyOutput
    {
        public string? id { get; set; }
        public string? name { get; set; }
    }

    /// <summary>
    /// 回款天数等数据
    /// </summary>
    public class InvoiceReceiptsDayOutput
    {
        /// <summary>
        /// 回款天数
        /// </summary>
        public int? BackAmountDays { get; set; }
        /// <summary>
        /// 销售账期天数
        /// </summary>
        public int? SaleAccountPeriodDays { get; set; }
    }
}
