﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class StoreInDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 入库单发票到齐状态 0 未到齐 1到齐 2全部  SupposedType 默认 2 全部（之前是默认0）
        /// </summary>
        public int? supposedType { get; set; } = 2;
        public int? types { get; set; } = 0;
        /// <summary>
        /// 入库类型   下拉框的
        /// </summary>
        public List<int>? typeList { get; set; } = new List<int>() { 1 };
        /// <summary>
        /// 出库类型
        /// </summary>
        public List<int>? storeOutTypeList { get; set; } = new List<int>();
        //public List<int>? typeList { get {

        //        if (types == 0)
        //        {
        //            //return new List<int>() { 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 999, 10000 };
        //            return new List<int>() { 1 };
        //        }
        //        else {
        //            var list = new List<int>();
        //            list.Add(Convert.ToInt32(types));
        //            return list;
        //        }

        //    } }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? companyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? companyName { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? storeInCode { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public List<string>? storeInCodes { get; set; }
        /// <summary>
        /// 经销调出单号
        /// </summary>
        public string? storeOutCode { get; set; }
        /// <summary>
        /// 经销调出单号
        /// </summary>
        public List<string>? storeOutCodes { get; set; }
        /// <summary>
        /// 入库起始日期
        /// </summary>
        public string? storeInDateStart { get; set; }
        /// <summary>
        /// 入库截至时间
        /// </summary>
        public string? storeInDateEnd { get; set; }

        /// <summary>
        /// 入库起始日期
        /// </summary>
        public string? storeOutDateStart
        {
            get
            {
                return storeInDateStart;
            }
        }
        /// <summary>
        /// 入库截至时间
        /// </summary>
        public string? storeOutDateEnd {
            get
            {
                return storeInDateEnd;
            }
        }
        /// <summary>
        /// 品名
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 上游订单号 (暂定)
        /// </summary>
        public string? UpstreamCode { get; set; }

        public Guid InputBillId { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();
        /// <summary>
        /// 标记  true：入库；false：经销调出
        /// </summary>
        public bool? IsStoreIn = true;

        /// <summary>
        /// 1=经销购货入库单,2=经销调出,3=寄售转购货
        /// </summary>
        public int Classify { get; set; }

        public Guid? UserId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 产品id
        /// </summary>
        public string? ProductId { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 产品名称id
        /// </summary>
        public string? ProductNameId { get; set; }

        /// <summary>
        /// 换货转退货code
        /// </summary>
        public string? billCode { get; set; }
        /// <summary>
        /// 换货转退货code集合
        /// </summary>
        public List<string>? billCodes { get; set; }
    }

    /// <summary>
    /// 查询ShowUseQuantity入参
    /// </summary>
    public class ShowUseQuantityQueryInput
    {
        public Guid InputBillId { get; set; }
        public List<StoreInDetailQueryOutput> List { get; set; }
        public int Classify { get; set; }
    }
}
