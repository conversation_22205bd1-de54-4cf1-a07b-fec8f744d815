﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.LossRecognition;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Models.File;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface ILossRecognitionAppService
    {
        /// <summary>
        /// 提交损失确认单
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SubmitItemAsync(Guid? id,string user);
        /// <summary>
        /// 删除损失确认单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteItemAsync(Guid? id);
        /// <summary>
        /// 删除详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteDetailItemAsync(Guid? id);
                /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BizFileUploadOutput>> GetAttachFile(LossRecognitionAttachFileInput input);
          /// <summary>
        /// 创建财务损失确认单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> CreateLossRecognition(CreateLossRecognitionInput input);
        /// <summary>
        /// 更新损失确认单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> UpdateLossRecognition(CreateLossRecognitionInput input);
        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<string>> UpdateStatus(Guid id, StatusEnum statusEnum);

        /// <summary>
        ///审核通过
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> AuditApproved(Guid id);

        /// <summary>
        /// 完成盘点后恢复损失确认单据处理
        /// 参考采购完成盘点逻辑，将损失确认（临时草稿、待审核）状态的单据日期修改到当前日期，单号也需要修改到下一个月，然后同时调用oa接口，将原来的标题修改成修改后单据对应的内容
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="targetSysMonth">目标系统月度</param>
        /// <returns></returns>
        Task<BaseResponseData<string>> ResumeLossRecognitionAfterInventory(Guid companyId, string targetSysMonth);
    }
}
