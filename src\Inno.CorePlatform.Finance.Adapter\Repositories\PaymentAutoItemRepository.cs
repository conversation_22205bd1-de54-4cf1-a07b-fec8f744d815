﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PaymentAutoItemRepository : EfBaseRepository<Guid, PaymentAutoItem, PaymentAutoItemPo>, IPaymentAutoItemRepository
    {

        private readonly FinanceDbContext _db;
        public PaymentAutoItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override async Task<int> UpdateAsync(PaymentAutoItem root)
        {
            var isExist = await _db.PaymentAutoItems.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new AppServiceException("批量付款单不存在！");
            }

            var po = root.Adapt<PaymentAutoItemPo>();

            _db.PaymentAutoItems.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();

        }

        public override async Task<int> DeleteAsync(Guid id)
        {
            var isExist = await _db.PaymentAutoItems.AnyAsync(x => x.Id == id);
            if (isExist == false)
            {
                throw new AppServiceException("批量付款单不存在！");
            }
            var po = await GetPoWithIncludeAsync(id);

            if (po.Status != Domain.PaymentAutoItemStatusEnum.WaitSubmit)
            {
                throw new AppServiceException("批量付款单不是临时草稿状态，无法删除");
            }

            if (po.PaymentAutoDetails.Count > 0)
            {
                _db.PaymentAutoDetails.RemoveRange(po.PaymentAutoDetails);
            }

            _db.PaymentAutoItems.Remove(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        protected override PaymentAutoItemPo CreateDeletingPo(Guid id)
        {
            return new PaymentAutoItemPo { Id = id };
        }

        protected override async Task<PaymentAutoItemPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.PaymentAutoItems.Include(x => x.PaymentAutoDetails).FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<PaymentAutoItem> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.PaymentAutoItems.Include(x => x.PaymentAutoDetails).AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<PaymentAutoItem>();
        }
        public async Task<BaseResponseData<int>> UpdateRemark(Guid Id, string remark)
        {
            var data = await _db.PaymentAutoItems.Where(p => p.Id == Id).FirstOrDefaultAsync();
            if (data.Status== PaymentAutoItemStatusEnum.WaitSubmit)
            { 
                data.Remark = remark;
                await _db.SaveChangesAsync();
                return BaseResponseData<int>.Success("操作成功");
            }
            else
            {
                return BaseResponseData<int>.Failed(500,"操作失败，只有草稿状态才能修改备注！");

            }
        }
        public async Task<BaseResponseData<int>> UpdateTransferDiscourse(Guid Id, string transferDiscourse)
        {
            var data = await _db.PaymentAutoItems.Where(p => p.Id == Id).FirstOrDefaultAsync();
            if (data.Status == PaymentAutoItemStatusEnum.WaitSubmit)
            {
                data.TransferDiscourse = transferDiscourse;
                await _db.SaveChangesAsync();
                return BaseResponseData<int>.Success("操作成功");
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "操作失败，只有草稿状态才能修改备注！");

            }
        }
        public async Task<int> UpdateManyAsync(List<PaymentAutoItem> lstItem)
        {
            var lstPo = lstItem.Adapt<List<PaymentAutoItemPo>>();

            _db.PaymentAutoItems.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }
    }
}
