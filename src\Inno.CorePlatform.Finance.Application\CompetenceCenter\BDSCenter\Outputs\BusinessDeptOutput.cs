﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{
    
    public class BusinessDeptOutput
    {
        public string id { get; set; }
        public string name { get; set; }
        public int status { get; set; }
        public BusinessDeptExtraInfo extraInfo { get; set; }

    }
    public class BusinessDeptExtraInfo
    {
        public List<BusinessDeptOutput> children { get; set; }
        public string deptShortName { get; set; }
        public string parentId { get; set; }
    }

    public class BusinessDeptQueryOutput
    {
        public int? businessUnit { get; set; }
        public string? id { get; set; }
        public string? path { get; set; }
        public string? deptFullName { get; set; }
        public string? deptName { get; set; }

    }

    public class GetFlatCheckedDeptsOutput
    { 
        public string? id { get; set; }
        public string? path { get; set; }
        public string? deptFullName { get; set; }
        public string? deptName { get; set; }

    }
}
