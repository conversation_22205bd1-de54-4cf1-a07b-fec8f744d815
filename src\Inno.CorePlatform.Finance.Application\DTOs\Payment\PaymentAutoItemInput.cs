﻿using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class PaymentAutoItemInput
    {
        public Guid? Id { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 事业部编码
        /// </summary>
        public string? BusinessArea { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public PaymentAutoItemStatusEnum? Status { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; } = "none";

        /// <summary>
        /// 修改人
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 操作者真实姓名
        /// </summary>
        public string? OperatorName { get; set; }

        /// <summary>
        /// 操作者Id
        /// </summary>
        public Guid? OperatorId { get; set; }

        /// <summary>
        /// 批量付款明细
        /// </summary>
        public List<PaymentAutoDetailInput> DetailInput { get; set; } = new List<PaymentAutoDetailInput>();
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }

    }

    public class PaymentSettlementtypeOutput
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }
}
