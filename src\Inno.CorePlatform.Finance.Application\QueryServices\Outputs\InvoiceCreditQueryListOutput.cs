﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 销项发票返回
    /// </summary>
    public class InvoiceCreditQueryListOutput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>

        public string? Type { get; set; }
          
        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; } 

        public string? HospitalId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单号   应收单
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }


        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        ///  货号
        /// </summary>
        public string? ProductNo { get; set; }


        /// <summary>
        ///  规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        ///  数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        ///  计量单位
        /// </summary>
        public string? PackUnit { get; set; }


        public CustomizeInvoiceStatusEnum? Status { get; set; }

        public string? StatusStr
        {
            get
            {
                return Status?.GetDescription();
            }
        }
        /// <summary>
        ///  编号  开票单
        /// </summary>

        public string? Code { get; set; }

        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount
        {
            get; set;
        }


        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }


        /// <summary>
        ///  单价 （含税）
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        ///  金额  (含税)
        /// </summary>

        public decimal? Value { get; set; }

        /// <summary>
        ///  不含税单价
        /// </summary>
        public decimal? NoTaxAmountPrice { get; set; }

        /// <summary>
        ///  不含税总金额
        /// </summary>
        public decimal? TotalNoTaxAmountPrice { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTateCodeId { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        public int? RowNo { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
    }

    /// <summary>
    /// 销项发票导出返回
    /// </summary>
    public class InvoiceCreditExportListOutput
    {
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>

        public string? Type { get; set; }



        /// <summary>
        /// 开票时间
        /// </summary>
        public string? InvoiceTime { get; set; }


        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单号   应收单
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }


        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        ///  货号
        /// </summary>
        public string? ProductNo { get; set; }


        /// <summary>
        ///  规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        ///  数量
        /// </summary>
        public string? Quantity { get; set; }

        /// <summary>
        ///  计量单位
        /// </summary>
        public string? PackUnit { get; set; }



        /// <summary>
        ///  编号  开票单
        /// </summary>

        public string? Code { get; set; }



        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }



        /// <summary>
        /// 状态
        /// </summary>
        public string? StatusStr { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string? TaxAmount { get; set; }


        /// <summary>
        /// 税率
        /// </summary>
        public string? TaxRate { get; set; }


        /// <summary>
        ///  单价 （含税）
        /// </summary>
        public string? Price { get; set; }

        /// <summary>
        ///  金额  (含税)
        /// </summary>

        public string? Value { get; set; }

        /// <summary>
        ///  不含税单价
        /// </summary>
        public string? NoTaxAmountPrice { get; set; }

        /// <summary>
        ///  不含税总金额
        /// </summary>
        public string? TotalNoTaxAmountPrice { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>

        public string? ProjectName { get; set; }
    }

    /// <summary>
    /// 根据发票号查询订单信息出参
    /// </summary>
    public class OrderInfoForInvoiceNoOutput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 类型 1=暂存核销 ，2=销售出库
        /// </summary>
        public int? Classify { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
    }

    /// <summary>
    /// 分批确认收入查询出参
    /// </summary>
    public class CreditSureIncomeQueryOutput
    {
        /// <summary>
        /// 应收id
        /// </summary>
        public Guid CreditId { get; set; }

        /// <summary>
        /// 收入确认金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 收入确认不含税金额
        /// </summary>
        public decimal NoTaxValue { get; set; }

        /// <summary>
        /// 收入确认日期
        /// </summary>
        public DateTimeOffset SureIncomeDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }
        /// <summary>
        /// 含税成本
        /// </summary>
        public decimal? Cost { get; set; }
        /// <summary>
        /// 不含税成本
        /// </summary>
        public decimal? NoTaxCost { get; set; }
    }
}
