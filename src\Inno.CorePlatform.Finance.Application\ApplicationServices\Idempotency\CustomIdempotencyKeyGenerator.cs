﻿using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System.Collections;
using System.Security.Cryptography;
using System.Text;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency
{
    /// <summary>
    /// 自定义幂等性键生成器，根据入参、控制器、操作和用户信息生成唯一的幂等性键。
    /// </summary>
    public class CustomIdempotencyKeyGenerator : IIdempotencyKeyGenerator
    {
        public string GenerateKey(ActionExecutingContext context)
        {
            var controller = context.Controller.GetType().Name;
            var action = context.ActionDescriptor.DisplayName;
            var userId = context.HttpContext.User?.Identity?.Name ?? "anonymous";

            // 序列化并哈希请求参数
            string parametersHash = GetParametersHash(context.ActionArguments);

            return $"{controller}.{action}.{userId}.{parametersHash}";
        }

        private static string GetParametersHash(IDictionary<string, object> parameters)
        {
            if (parameters == null || !parameters.Any())
                return "nullparams";

            var sb = new StringBuilder();
            foreach (var (key, value) in parameters.OrderBy(p => p.Key))
            {
                sb.Append(key);
                sb.Append('=');
                AppendValue(sb, value); // 使用新的智能值处理方法
                sb.Append(';');
            }

            using var sha = SHA256.Create();
            byte[] hashBytes = sha.ComputeHash(Encoding.UTF8.GetBytes(sb.ToString()));
            return BitConverter.ToString(hashBytes).Replace("-", "").Substring(0, 12);
        }

        // 递归处理嵌套对象和集合
        private static void AppendValue(StringBuilder sb, object value)
        {
            if (value == null)
            {
                sb.Append("null");
                return;
            }

            switch (value)
            {
                case IFormFile formFile:
                    sb.Append($"File[{formFile.FileName}:{formFile.Length}bytes]");
                    break;

                case IEnumerable enumerable when !(value is string):
                    var elements = new List<string>();
                    foreach (var item in enumerable)
                    {
                        var elementBuilder = new StringBuilder();
                        AppendValue(elementBuilder, item); // 递归处理元素
                        elements.Add(elementBuilder.ToString());
                    }
                    sb.Append($"[{string.Join(",", elements.OrderBy(x => x))}]"); // 排序确保顺序无关
                    break;

                default:
                    // 使用稳定且包含所有字段的JSON序列化
                    var json = JsonConvert.SerializeObject(value, new JsonSerializerSettings
                    {
                        ContractResolver = new DefaultContractResolver() // 包含所有字段
                        {
                            NamingStrategy = new CamelCaseNamingStrategy()
                        },
                        Formatting = Formatting.None,
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                    });
                    sb.Append(json);
                    break;
            }
        }
        

    }
}
