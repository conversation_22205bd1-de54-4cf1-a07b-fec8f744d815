﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService;
using Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment;
using Inno.CorePlatform.Finance.Application.LogServices;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class AdvancePaymentAppService : ApplicationServices.BaseAppService<AdvancePaymentAppService>, IAdvancePaymentAppService
    {
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly IPCApiClient _pcApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly ISubLogService _subLogService;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        public AdvancePaymentAppService(
            FinanceDbContext db,
            DaprClient daprClient,
            IBDSApiClient bDSApiClient,
            IAppServiceContextAccessor appServiceContextAccessor,
            IWeaverApiClient weaverApiClient,
            IPCApiClient pcApiClient,
            IPurchaseApiClient purchaseApiClient,
            ILogger<AdvancePaymentAppService> logger,
            IHttpContextAccessor httpContextAccessor,
            IApplyBFFService applyBFFService,
            ISubLogService subLogService,
            ICodeGenClient codeClient,
            IInventoryMgmAppService inventoryMgmAppService
            ) : base(db, daprClient, codeClient, logger, httpContextAccessor, applyBFFService)
        {
            _db = db;
            _bDSApiClient = bDSApiClient;
            _appServiceContextAccessor = appServiceContextAccessor;
            _pcApiClient = pcApiClient;
            _weaverApiClient = weaverApiClient;
            _purchaseApiClient = purchaseApiClient;
            _subLogService = subLogService;
            _inventoryMgmAppService = inventoryMgmAppService;
        }

        /// <summary>
        /// OA审批完成
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> AuditApproved(Guid id)
        {
            try
            {
                //调用采购购货修订生成负数应付
                var purInput = new List<PurchaseDetailsInput>();
                //成功后对应应付的回款账期改为销售账期，并写上预计付款日期
                //并根据应付的每条付款计划毛利率得出最后利润生成一条负数应付
                var ret = BaseResponseData<string>.Success("操作成功！");
                var advancePaymentItem = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(p => p.Id == id);
                if (advancePaymentItem == null)
                {
                    return BaseResponseData<string>.Failed(500, "操作失败，原因：数据不存在");
                }

                // 检查是否正在盘点，如果正在盘点则抛出异常
                await CheckInventoryStatusBeforeApproval(advancePaymentItem);

                // 如果已经是完成状态，直接返回成功
                if (advancePaymentItem.Status == AdvancePaymentStatusEnum.Complate)
                {
                    return BaseResponseData<string>.Success("提前付款垫资申请已完成！");
                }
                if (advancePaymentItem.AdvancePaymentDebtDetails != null && advancePaymentItem.AdvancePaymentDebtDetails.Any())
                {
                    var debtBillNos = advancePaymentItem.AdvancePaymentDebtDetails.Select(x => x.DebtBillNo).ToHashSet();
                    var debts = await _db.Debts.Where(x => !string.IsNullOrEmpty(x.BillCode) && debtBillNos.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                    var debtIds = debts.Select(x => x.Id).ToHashSet();
                    var debtDetails = await _db.DebtDetails.Where(x => x.DebtId.HasValue && debtIds.Contains(x.DebtId.Value)).AsNoTracking().ToListAsync();
                    //add付款计划集合
                    var addDebtDetails = new List<DebtDetailPo>();
                    //upd付款计划集合
                    var updDebtDetails = new List<DebtDetailPo>();
                    foreach (var apdd in advancePaymentItem.AdvancePaymentDebtDetails)
                    {
                        //应付只会对应一个应收,多个回款账期也只对应一个
                        var firstDebtDetail = debtDetails.FirstOrDefault(x => x.AccountPeriodType == 0 && x.PurchaseCode == apdd.PurchaseCode);
                        if (firstDebtDetail == null)
                        {
                            continue;
                        }
                        //合并所有回款账期金额
                        firstDebtDetail.Value = debtDetails.Where(x => x.PurchaseCode == apdd.PurchaseCode && x.AccountPeriodType ==
                     0).Sum(x => x.Value);
                        //差额
                        var differenceValue = firstDebtDetail.Value - (apdd.PaymentAmount.HasValue ? apdd.PaymentAmount.Value : 0);
                        //销售账期的金额
                        var saleDebtDetailValue = apdd.PaymentAmount; //- apdd.AdvanceTaxAmount;
                        var productDetails = advancePaymentItem.AdvancePaymentProductDetails.Where(x => x.PurchaseCode == apdd.PurchaseCode).ToList();
                        foreach (var productDetail in productDetails)
                        {
                            //封装采购购货修订接口入参
                            purInput.Add(new PurchaseDetailsInput
                            {
                                PurchaseOrderDetailId = productDetail.PurchaseDetailId,
                                PurchaseOrderId = productDetail.PurchaseOrderId,
                                PurchaseOrderCode = productDetail.PurchaseCode,
                                AdvancePaymentCode = advancePaymentItem.BillCode,
                                Quantity = productDetail.Quantity,
                                BeforeCost = productDetail.OriginalCost / productDetail.Quantity,
                                AfterCost = (productDetail.OriginalCost / productDetail.Quantity) - productDetail.Profit
                            });
                        }
                        //深拷贝对象
                        if (differenceValue > 0)
                        {
                            var deepWorkOriginDebtDetail = JsonConvert.DeserializeObject<DebtDetailPo>(JsonConvert.SerializeObject(firstDebtDetail));
                            if (deepWorkOriginDebtDetail == null)
                            {
                                continue;
                            }
                            //生成单号
                            var codeResult = await CreateBillCode(advancePaymentItem.CompanyId.Value.ToString(), deptShortName: apdd.DebtBillNo.Split('-')[0] ?? "ZXBD", billType: "DPP");
                            deepWorkOriginDebtDetail.Code = codeResult.Item1;
                            deepWorkOriginDebtDetail.Id = Guid.NewGuid();
                            deepWorkOriginDebtDetail.Value = differenceValue;
                            deepWorkOriginDebtDetail.UpdatedTime = DateTime.Now;
                            addDebtDetails.Add(deepWorkOriginDebtDetail);
                        }
                        //销售账期
                        if (saleDebtDetailValue.HasValue && saleDebtDetailValue.Value > 0)
                        {
                            var deepWorkSaleDebtDetail = JsonConvert.DeserializeObject<DebtDetailPo>(JsonConvert.SerializeObject(firstDebtDetail));
                            if (deepWorkSaleDebtDetail == null)
                            {
                                continue;
                            }
                            deepWorkSaleDebtDetail.Id = Guid.NewGuid();
                            var codeResult = await CreateBillCode(advancePaymentItem.CompanyId.Value.ToString(), deptShortName: apdd.DebtBillNo.Split('-')[0] ?? "ZXBD", billType: "DPP");
                            deepWorkSaleDebtDetail.Code = codeResult.Item1;
                            deepWorkSaleDebtDetail.Value = saleDebtDetailValue.HasValue ? saleDebtDetailValue.Value : 0;
                            deepWorkSaleDebtDetail.AccountPeriodType = 2;
                            deepWorkSaleDebtDetail.ProbablyPayTime = apdd.ActualPaymentDate;
                            deepWorkSaleDebtDetail.CreatedTime = DateTime.Now;
                            deepWorkSaleDebtDetail.CreatedBy = "AutoAbatementByAdvance";
                            deepWorkSaleDebtDetail.UpdatedTime = DateTime.Now;
                            addDebtDetails.Add(deepWorkSaleDebtDetail);
                        }
                        //完成的回款账期数据
                        //深拷贝对象
                        //var deepWorkBackDebtDetail = JsonConvert.DeserializeObject<DebtDetailPo>(JsonConvert.SerializeObject(firstDebtDetail));
                        //if (deepWorkBackDebtDetail == null)
                        //{
                        //    continue;
                        //}
                        //deepWorkBackDebtDetail.Id = Guid.NewGuid();
                        //deepWorkBackDebtDetail.Value = apdd.AdvanceTaxAmount.HasValue ? apdd.AdvanceTaxAmount.Value : 0;
                        //deepWorkBackDebtDetail.Status = DebtDetailStatusEnum.Completed;
                        //deepWorkBackDebtDetail.UpdatedTime = DateTime.Now;
                        //addDebtDetails.Add(deepWorkBackDebtDetail);
                        //删除回款账期数据，重新生成
                        var delDebtDetails = debtDetails.Where(x => x.PurchaseCode == apdd.PurchaseCode && x.AccountPeriodType ==
                         0).ToList();
                        if (debtDetails.Any())
                        {
                            _db.DebtDetails.RemoveRange(delDebtDetails);
                        }
                    }
                    if (addDebtDetails.Any())
                    {
                        _db.DebtDetails.AddRange(addDebtDetails);
                    }
                }
                advancePaymentItem.Status = AdvancePaymentStatusEnum.Complate;
                if (purInput.Any())
                {
                    var jsonStr = JsonConvert.SerializeObject(purInput);
                    await _subLogService.LogAsync("提前垫资付款生成采购购货修订",jsonStr);
                    var purRet = await _purchaseApiClient.GenerateAdvancePaymentRevise(purInput);  //接口
                    if (purRet.Code == CodeStatusEnum.Success)
                    {
                        await _db.SaveChangesAsync();
                        return BaseResponseData<string>.Success($"操作成功！{purRet.Data}行受影响");
                    }
                    else
                    {
                        ret = BaseResponseData<string>.Success("操作成功！");
                        ret.Message = string.Concat("【采购】", purRet.Message);
                        return ret;
                    }
                }
                return BaseResponseData<string>.Failed(500, "操作失败，原因：修改数据失败！");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "操作失败，原因：" + ex.Message);
            }
        }

        public async Task<BaseResponseData<string>> UpdateStatus(Guid id, AdvancePaymentStatusEnum refuse)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var advancePaymentItem = await _db.AdvancePaymentItem.FirstOrDefaultAsync(p => p.Id == id);
            if (advancePaymentItem == null)
            {
                ret = BaseResponseData<string>.Failed(500, "操作失败，原因：数据不存在");
            }
            else
            {
                advancePaymentItem.Status = refuse;
                if (await _db.SaveChangesAsync() > 0)
                {
                    ret = BaseResponseData<string>.Success("操作成功！");
                }
                else
                {
                    ret = BaseResponseData<string>.Failed(500, "操作失败，原因：修改数据失败！");
                }
            }
            return ret;
        }

        /// <summary>
        /// 检查盘点状态，如果正在盘点则不允许归档
        /// </summary>
        /// <param name="advancePaymentItem">提前付款垫资申请单</param>
        /// <returns></returns>
        private async Task CheckInventoryStatusBeforeApproval(AdvancePaymentItemPo advancePaymentItem)
        {
            try
            {
                _logger.LogInformation("提前付款垫资申请单 {BillCode} 开始检查盘点状态", advancePaymentItem.BillCode);

                // 检查是否正在盘点（状态为2）
                var inventoryCheckResult = await _inventoryMgmAppService.InventoryCheck(advancePaymentItem.CompanyId.Value);

                if (inventoryCheckResult.Code != CodeStatusEnum.Success)
                {
                    var errorMessage = $"提前付款垫资申请单 {advancePaymentItem.BillCode} 归档失败：公司 {advancePaymentItem.CompanyName} 正在盘点中，盘点期间不允许提前付款垫资申请单归档";
                    _logger.LogError("提前付款垫资归档检查失败 - {ErrorMessage}", errorMessage);
                    throw new ApplicationException(errorMessage);
                }

                _logger.LogInformation("提前付款垫资申请单 {BillCode} 盘点状态检查通过", advancePaymentItem.BillCode);
            }
            catch (ApplicationException)
            {
                // 重新抛出业务异常
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查提前付款垫资申请单 {advancePaymentItem.BillCode} 盘点状态时发生异常：{ex.Message}";
                _logger.LogError(ex, "提前付款垫资归档盘点状态检查异常 - 申请单: {BillCode}, 公司: {CompanyName}, 错误: {ErrorMessage}",
                    advancePaymentItem.BillCode, advancePaymentItem.CompanyName, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }
    }
}
