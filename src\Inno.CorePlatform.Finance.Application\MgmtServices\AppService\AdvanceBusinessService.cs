﻿using Google.Api;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 寄售垫资服务
    /// </summary>
    public class AdvanceBusinessService : IAdvanceBusinessService
    {
        private IBaseAllQueryService<CreditPo> _baseCreditQueryService;
        private IBaseAllQueryService<AdvanceBusinessApplyPO> _baseAdvanceBusinessApplyQueryService;
        private IBaseAllQueryService<AdvanceBusinessDetailPO> _baseAdvanceBusinessDetailQueryService;
        private IBaseAllQueryService<DebtDetailPo> _baseDebtdetailQueryService;
        private IBaseAllQueryService<InvoiceCreditPo> _baseInvoiceCreditQueryService;
        private IBaseAllQueryService<AbatementPo> _baseAbatmentQueryService;
        private IAdvanceBusinessRepository _baseAdvanceBusinessRepository;
        private IUnitOfWork _unitOfWork;
        public AdvanceBusinessService(IBaseAllQueryService<CreditPo> baseCreditQueryService, IBaseAllQueryService<AdvanceBusinessApplyPO> baseAdvanceBusinessApplyQueryService,
                                      IBaseAllQueryService<InvoiceCreditPo> baseInvoiceCreditQueryService,
                                      IBaseAllQueryService<DebtDetailPo> baseDebtdetailQueryService,
                                      IBaseAllQueryService<AbatementPo> baseAbatmentQueryService,
                                      IAdvanceBusinessRepository baseAdvanceBusinessRepository,
                                      IBaseAllQueryService<AdvanceBusinessDetailPO> baseAdvanceBusinessDetailQueryService,
                                      IUnitOfWork unitOfWork)
        {
            _baseCreditQueryService = baseCreditQueryService;
            _baseAdvanceBusinessApplyQueryService = baseAdvanceBusinessApplyQueryService;
            _baseInvoiceCreditQueryService = baseInvoiceCreditQueryService;
            _baseAbatmentQueryService = baseAbatmentQueryService;
            _baseAdvanceBusinessRepository = baseAdvanceBusinessRepository;
            _baseAdvanceBusinessDetailQueryService = baseAdvanceBusinessDetailQueryService;
            _baseDebtdetailQueryService = baseDebtdetailQueryService;
            _unitOfWork = unitOfWork;
        }

        public async Task CreateAdvanceAsync(List<Guid> creditIds)
        {
            //var types = new List<int>() { 1, 3, 4, };
            var credits = await _baseCreditQueryService.GetAllListAsync(p => creditIds.Contains(p.Id));
            if (credits.Count == 0)
            {
                return;
            }
            var debtdetails = await _baseDebtdetailQueryService.GetAllListAsync(p => p.AccountPeriodType != 0 && creditIds.Contains(p.CreditId.Value), new List<string>() { "Debt" });
            if (debtdetails.Count == 0)
            {
                return;
            }
            var invoices = await _baseInvoiceCreditQueryService.GetAllListAsync(p => creditIds.Contains(p.CreditId.Value));
            var companyIds = credits.Select(p => p.CompanyId).Distinct().ToList();
            var customerIds = credits.Select(p => p.CustomerId).Distinct().ToList();
            var serviceIds = credits.Select(p => p.ServiceId).Distinct().ToList();
            var advanceBusinessItems = await _baseAdvanceBusinessApplyQueryService.GetAllListAsync(p => p.Status == 2 && companyIds.Contains(p.CompanyId) && customerIds.Contains(p.HospitalId) && serviceIds.Contains(p.ServiceId));
            var newdetails = new List<AdvanceBusinessDetail>();
            foreach (var d in debtdetails)
            {
                var thisCredit = credits.FirstOrDefault(t => t.Id == d.CreditId);
                if (thisCredit == null)
                {
                    continue;
                }

                List<Guid> hospitalIds = string.IsNullOrEmpty(thisCredit.HospitalId) ? new List<Guid>() : thisCredit.HospitalId.Split(",").Select(p => Guid.Parse(p)).ToList();
                var thisItem = advanceBusinessItems.FirstOrDefault(t => t.HospitalId == thisCredit.CustomerId && hospitalIds.Contains(t.EndHospitalId) && t.CompanyId == thisCredit.CompanyId && t.ServiceId == thisCredit.ServiceId);
                if (thisItem == null)
                {
                    continue;
                }
                var newDetail = new AdvanceBusinessDetail()
                {
                    AccountPeriod = thisItem.ProvidePayDays ?? 0,
                    AdvanceBusinessApplyId = thisItem.Id,
                    CreditCode = thisCredit.BillCode,
                    Id = Guid.NewGuid(),
                    CreditDate = thisCredit.BillDate.Value,
                    CreditId = thisCredit.Id,
                    CreditValue = thisCredit.Value,
                    DebtCode = d.Debt.BillCode,
                    DebtDate = d.Debt.BillDate.Value,
                    DebtDetailId = d.Id,
                    DebtValue = d.Debt.Value,
                    ExpectPaymentDate = d.ProbablyPayTime,
                    SalesTaxRate = 13.00m,//后续要从订单里取
                    SCFDiscount = d.FinanceDiscount ?? 0,
                    Discount = d.CostDiscount ?? 0,
                    //ADFDiscount = thisItem.Ratio,
                    BaseDiscount = d.DistributionDiscount ?? 0,
                    SPDDiscount = d.SpdDiscount ?? 0,
                    ReceivePeriod = thisItem.ReturnMoneyDays ?? 0,
                    PaymentCode = "",
                    ReceiveCode = ""
                };
                newDetail.ADFDiscount = thisItem.Ratio; //newDetail.SCFDiscount + newDetail.BaseDiscount + newDetail.SPDDiscount + d.TaxDiscount;
                var invoice = invoices.OrderByDescending(t => t.InvoiceTime).FirstOrDefault(t => t.CreditId == thisCredit.Id);
                //发票信息
                if (invoice != null)
                {
                    newDetail.InvoiceDate = invoice.InvoiceTime;
                    newDetail.ExpectReceiveDate = invoice.InvoiceTime.Value.AddDays(thisItem.ReturnMoneyDays ?? 0); //计划回款日期:开票日期+医院回款期
                    newDetail.ExpectPaymentDate = invoice.InvoiceTime.Value.AddDays(thisItem.ProvidePayDays ?? 0); //预计付款日期:开票日期+销售账期天数
                }
                #region 收款信息
                if (thisCredit.Value > 0)//正数应收
                {
                    var abatement = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditType == "receive" && t.DebtType == "credit" && t.DebtBillCode == thisCredit.BillCode);
                    if (abatement != null)
                    {
                        newDetail.ReceiveCode = abatement.CreditBillCode;
                        newDetail.ReceiveDate = abatement.Abtdate;
                    }
                    else
                    {
                        abatement = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditType == "credit" && t.DebtType == "credit" && t.DebtBillCode == thisCredit.BillCode);
                        if (abatement != null)
                        {
                            newDetail.ReceiveCode = abatement.CreditBillCode;
                            newDetail.ReceiveDate = abatement.Abtdate;
                        }
                    }
                }
                else//负数应收
                {
                    var abatement = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditBillCode == thisCredit.BillCode || t.DebtBillCode == thisCredit.BillCode);
                    if (abatement != null)
                    {
                        newDetail.ReceiveCode = abatement.DebtBillCode == thisCredit.BillCode ? abatement.CreditBillCode : abatement.DebtBillCode;
                        newDetail.ReceiveDate = abatement.Abtdate;
                    }
                }
                #endregion

                #region 付款信息
                if (d.Status == Domain.DebtDetailStatusEnum.Completed)//已付款
                {
                    if (d.Value > 0)
                    {
                        var abatment = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditType == "debt" && t.DebtType == "payment" && t.CreditBillCode == d.Debt.BillCode && t.Value == d.Value);
                        if (abatment == null)
                        {
                            abatment = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditType == "debt" && t.DebtType == "debt" && t.CreditBillCode == d.Debt.BillCode && t.Value == d.Value);
                        }
                        if (abatment != null)
                        {
                            newDetail.PaymentCode = abatment.DebtBillCode;
                            newDetail.PaymentDate = abatment.Abtdate;
                        }
                    }
                    else
                    {
                        var abatment = await _baseAbatmentQueryService.FirstOrDefaultAsync(t => t.CreditType == "debt" && t.DebtType == "debt" && t.DebtBillCode == d.Debt.BillCode && t.Value == d.Value);
                        if (abatment != null)
                        {
                            newDetail.PaymentCode = abatment.CreditBillCode;
                            newDetail.PaymentDate = abatment.Abtdate;
                        }
                    }
                }
                #endregion

                newdetails.Add(newDetail);
            }
            if (newdetails.Count != 0)
            {
                _baseAdvanceBusinessRepository.UowJoined = true;
                var exists = await _baseAdvanceBusinessDetailQueryService.GetAllListAsync(p => creditIds.Contains(p.CreditId));
                if (exists.Count != 0)
                {
                    var ids = exists.Select(p => p.Id).ToList();
                    await _baseAdvanceBusinessRepository.DeleteAdvanceDetails(ids);
                }
                await _baseAdvanceBusinessRepository.AddAdvanceDetails(newdetails);
                await _unitOfWork.CommitAsync();
            }
        }
        /// <summary>
        /// 订阅垫资单消息
        /// </summary>
        /// <param name="advanceBusinessApplies"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> AdvanceSubAsync(List<AdvanceBusinessApply> advanceBusinessApplies)
        {
            try
            {
                var codes = advanceBusinessApplies.Select(p => p.Code).ToList();
                //var exists = await _baseAdvanceBusinessApplyQueryService.GetAllListAsync(t => codes.Contains(t.Code));
                var exists = await _baseAdvanceBusinessRepository.GetByCodes(codes);
                var exsits_codes = exists.Select(p => p.Code).ToList();
                //新增垫资单
                var addAdvances = advanceBusinessApplies.Where(t => !exsits_codes.Contains(t.Code)).ToList();
                await _baseAdvanceBusinessRepository.AddManyAsync(addAdvances);

                //更新已有垫资单
                var updateAdvances = advanceBusinessApplies.Where(t => exsits_codes.Contains(t.Code)).ToList();
                //按Number聚合，避免重复数据
                var numbers = updateAdvances.GroupBy(t => t.Code).Select(g => g.Key).ToList();
                var entitys = new List<AdvanceBusinessApply>();
                foreach (var number in numbers)
                {
                    var exist = exists.OrderByDescending(t => t.CreatedTime).FirstOrDefault(t => t.Code == number);
                    var item = updateAdvances.OrderByDescending(t => t.CreatedTime).FirstOrDefault(t => t.Code == number);
                    if (exist != null)
                    {
                        var config = new TypeAdapterConfig();
                        _ = config.ForType<AdvanceBusinessApply, AdvanceBusinessApply>()
                         .Ignore(dest => dest.CreatedTime)
                         .Ignore(dest => dest.CreatedBy)
                         .Ignore(dest => dest.Id);
                        exist = item.Adapt(exist, config);
                        entitys.Add(exist);
                    }
                }
                await _baseAdvanceBusinessRepository.UpdateManyAsync(entitys);
                await _unitOfWork.CommitAsync();

                return BaseResponseData<int>.Success(advanceBusinessApplies.Count, "操作成功");
            }
            catch (Exception ex)
            {

                return BaseResponseData<int>.Failed((int)CodeStatusEnum.Failed, "操作失败" + ex.Message);
            }
        }
    }
}
