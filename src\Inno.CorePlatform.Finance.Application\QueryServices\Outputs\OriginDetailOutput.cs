﻿using Inno.CorePlatform.Finance.Domain;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 获取原始开票明细查询  出参
    /// </summary>
    public class OriginDetailOutput
    {
        /// <summary>
        /// 明细Id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 原明细Id
        /// </summary>
        public string? OriginDetailId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 包装规格
        /// </summary>
        public string? OriginalPackSpec { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }
        /// <summary>
        /// 原始品名（不变）
        /// </summary>
        public string? OriginProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 原始单位
        /// </summary>
        public string? OriginPackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }
        /// <summary>
        /// 原始单价
        /// </summary>
        public decimal? OriginalPrice { get; set; }
        /// <summary>
        /// 原始成本
        /// </summary>
        public decimal? OriginalCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount
        {
            get
            {
                if (TaxRate.HasValue && NoInvoiceAmount.HasValue)
                {

                    var ret = decimal.Parse((Math.Abs(NoInvoiceAmount.Value) - Math.Abs(NoInvoiceAmount.Value) / (1 + (TaxRate / 100))).Value.ToString("F2"));
                    if (Value < 0)
                    {
                        ret = -ret;
                    }
                    return ret;
                }
                else
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 客户Id 
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>  
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>  
        public string? NameCode { get; set; }
        public Guid? OriginalId { get; set; }
        /// <summary>
        /// 原始规格型号
        /// </summary>
        public string? OriginSpecification { get; set; }
        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高价值(0:否,1:是)
        /// </summary>
        public int? IFHighValue { get; set; }
        public decimal? NoInvoiceAmount { get; set; }
        public decimal? NoInvoiceAmountShow { get; set; }
        public Guid CreditDetailId { get; set; }
        public Guid? BatchId { get; set; }
        public Guid? SaleDetailId { get; set; }
        /// <summary>
        /// 业务单元Id
        /// </summary>  
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        public Guid? ProjectId { get; set; }

        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        ///价格来源描述
        /// </summary>   
        public string PriceSourceStr
        {
            get
            {
                if (PriceSource.HasValue)
                {
                    return PriceSource.GetDescription();
                }
                else
                {
                    return "";
                }
            }
        }
    }

    public class CheckDetailOutput 
    {
        public List<OriginDetailOutput> Details { get; set; }
        public int Source { get; set; }
    }
    public class CreditDetailOutput : OriginDetailOutput
    {
        /// <summary>
        /// 采购成本
        /// </summary>
        public decimal? PurchaseCost { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string? DeptName { get; set; }
        /// <summary>
        /// 业务名称
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 订货人
        /// </summary>
        public string? CustomerPersonName { get; set; }
        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum? CreditType { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditTypeStr
        {
            get
            {
                string ret = string.Empty;
                if (CreditType != null)
                {
                    return CreditType.GetDescription();
                }
                return ret;
            }
        }
        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditSaleSubTypeStr
        {
            get
            {
                string ret = string.Empty;
                if (CreditType != null)
                {
                    return CreditSaleSubType.GetDescription();
                }
                return ret;
            }
        }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; } = "none";
        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 三方开票申请单号
        /// </summary>
        public string? ShipmentCode { get; set; }
        /// <summary>
        /// 红字消耗单号
        /// </summary>
        public string? RedReversalConsumNo { get; set; }

    }
}
