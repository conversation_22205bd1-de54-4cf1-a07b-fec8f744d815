﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.StoreInApply;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class StoreApiClient : BaseDaprApiClient<StoreInApplyApiClient>, IStoreApiClient
    {
        public StoreApiClient(DaprClient daprClient, ILogger<StoreInApplyApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {

        }

        /// <summary>
        /// 获取存货对账信息数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<QueryReconForFmOutput> QueryReconForFm(ReconciliationInput input)
        {
            try
            {
                //库存-获取存货对账信息数据
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, QueryReconForFmOutput>(input, AppCenter.QueryReconForFm, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用库存-获取存货对账信息数据失败");
            }
        }
        /// <summary>
        /// 查询收入成本对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<QueryReconIncomeOutput> QueryIncomeCostRec(ReconciliationInput input)
        {
            try
            {
                //库存-获取存货对账信息数据
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, QueryReconIncomeOutput>(input, AppCenter.QueryIncomeCostRec, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用库存-查询收入成本对账失败");
            }
        }


        protected override string GetAppId()
        {
            return AppCenter.Inventory_APPID;
        }


    }
}
