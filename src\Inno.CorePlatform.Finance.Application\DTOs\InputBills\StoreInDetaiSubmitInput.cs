﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.InputBills
{
    public class StoreInDetaiSubmitInput
    {
        /// <summary>
        /// 是否新增
        /// </summary>
        public bool? IsAdd { get; set; } = false;
        public Guid? InputBillId { get; set; }

        public Guid? Id { get; set; }

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInItemCode { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        public double? StoreInDate { get; set; }
        /// <summary>
        /// 聚合入库数量
        /// </summary>
        public decimal? PolyQuantity { get; set; }
        /// <summary>
        ///聚合已入票数量
        /// </summary>
        public decimal? PolyInvoiceQuantity { get; set; }
        /// <summary>
        /// 本次入票数量
        /// </summary>
        public decimal ThisQuantity { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal? TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal? NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        //public List<StoreInDetail> StoreInDetail { get; set; } = new List<StoreInDetail>();

        /// <summary>
        /// 详情
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();

        /// <summary>
        /// 是否已经自动匹配
        /// </summary>
        public bool IsMatch { get; set; } = false;
        public int BusinessType { get; set; }
    }
    public class StoreInDetail
    {
        /// <summary>
        /// 详情Id
        /// </summary>
        public string? StoreInDetailId { get; set; }

        /// <summary>
        /// 追溯id
        /// </summary>
        public string? SysBakId { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 已入票数量数量
        /// </summary>
        public int? InvoiceQuantity { get; set; }
    }
}
