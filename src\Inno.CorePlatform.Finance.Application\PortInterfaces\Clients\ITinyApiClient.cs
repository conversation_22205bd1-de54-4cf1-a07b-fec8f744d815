﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface ITinyApiClient
    {
        Task<List<ReconciliationOutput>> FinanceCostStatistics(ReconciliationInput input);
    }
    public interface ITinyApiClientOfResponseData
    {
        Task<TinyInventoryCreateOutputDto> CreateTinyInventory(TinyInventory<PERSON>reateRequestDto request);
    }
}
