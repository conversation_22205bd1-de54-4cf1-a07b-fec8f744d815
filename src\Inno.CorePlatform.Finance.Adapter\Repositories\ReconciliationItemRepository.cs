﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class ReconciliationItemRepository : EfBaseRepository<Guid, ReconciliationItem, ReconciliationItemPo>, IReconciliationItemRepository {
        private FinanceDbContext _db;
        public ReconciliationItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override Task<int> UpdateAsync(ReconciliationItem root)
        {
            throw new NotImplementedException();
        }

        protected override ReconciliationItemPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> IsExist(ReconciliationItem model)
        {
            var res = _db.ReconciliationItem.Where(t => t.SysMonth== model.SysMonth && t.CompanyId==model.CompanyId);
            return await res.AnyAsync();
        }

        protected override Task<ReconciliationItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
