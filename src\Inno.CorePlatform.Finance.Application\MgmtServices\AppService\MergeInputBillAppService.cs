﻿using Dapr.Client;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.SendNotification;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Application.Helpers;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation;
using Inno.CorePlatform.Finance.Application.Services.MatchCache;
using Inno.CorePlatform.Finance.Application.Services.MatchLogic;
using Inno.CorePlatform.Finance.Application.Services.SubmitHandling;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System.Linq;
namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 合并进项发票应用服务实现
    /// </summary>
    public class MergeInputBillAppService : BaseAppService<MergeInputBillAppService>, IMergeInputBillAppService
    {
        private readonly MatchLogicService _matchLogicService;
        private readonly IMatchCacheManager _cacheManager;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IManyInventoryApiClient _manyInventoryApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly ISendNotificationClient _sendNotificationClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ISubmitHandlingService _submitHandlingService;
        private readonly IInterfaceInvocationService _interfaceInvocationService;
        private readonly IPCApiClient _pcApiClient;
        private readonly MergeInputBillHelper _mergeInputBillHelper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="daprClient">边车服务客户端</param>
        /// <param name="codeGenClient">单号生成客户端</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="httpContextAccessor">HTTP上下文访问器</param>
        /// <param name="applyBFFService">外部能力中心访问服务</param>
        /// <param name="matchLogicService">匹配逻辑服务</param>
        /// <param name="cacheManager">缓存管理器</param>
        /// <param name="bDSApiClient">BDS API客户端</param>
        /// <param name="manyInventoryApiClient">多对一勾稽库存能力中心客户端</param>
        /// <param name="purchaseApiClient">采购能力中心客户端</param>
        /// <param name="sendNotificationClient">消息通知客户端</param>
        /// <param name="kingdeeApiClient">金蝶API客户端</param>
        /// <param name="submitHandlingService">提交处理服务</param>
        /// <param name="interfaceInvocationService">接口调用服务</param>
        /// <param name="pcApiClient">权限中心API客户端</param>
        /// <param name="mergeInputBillHelper">合并进项票辅助类</param>
        public MergeInputBillAppService(
            FinanceDbContext db,
            DaprClient daprClient,
            ICodeGenClient codeGenClient,
            ILogger<MergeInputBillAppService> logger,
            IHttpContextAccessor httpContextAccessor,
            IApplyBFFService applyBFFService,
            MatchLogicService matchLogicService,
            IMatchCacheManager cacheManager,
            IBDSApiClient bDSApiClient,
            IManyInventoryApiClient manyInventoryApiClient,
            IPurchaseApiClient purchaseApiClient,
            ISendNotificationClient sendNotificationClient,
            IKingdeeApiClient kingdeeApiClient,
            ISubmitHandlingService submitHandlingService,
            IInterfaceInvocationService interfaceInvocationService,
            IPCApiClient pcApiClient,
            MergeInputBillHelper mergeInputBillHelper)
            : base(db, daprClient, codeGenClient, logger, httpContextAccessor, applyBFFService)
        {
            _matchLogicService = matchLogicService;
            _cacheManager = cacheManager;
            _bDSApiClient = bDSApiClient;
            _manyInventoryApiClient = manyInventoryApiClient;
            _purchaseApiClient = purchaseApiClient;
            _sendNotificationClient = sendNotificationClient;
            _kingdeeApiClient = kingdeeApiClient;
            _submitHandlingService = submitHandlingService;
            _interfaceInvocationService = interfaceInvocationService;
            _pcApiClient = pcApiClient;
            _mergeInputBillHelper = mergeInputBillHelper;
        }

        /// <summary>
        /// 创建合并进项发票
        /// </summary>
        /// <param name="request">创建合并进项发票请求</param>
        /// <returns>创建合并进项发票响应</returns>
        public async Task<CreateMergeInputBillResponse> CreateMergeInputBill(CreateMergeInputBillRequest request)
        {
            // 验证请求
            if (request == null || request.InputBillIds == null || !request.InputBillIds.Any())
            {
                throw new ArgumentException("请选择要合并的进项发票");
            }

            // 验证合并进项票数量不能超过10张
            if (request.InputBillIds.Count > 10)
            {
                throw new ArgumentException("合并进项票数量不能超过10张");
            }

            // 查询原始进项发票
            var inputBills = await _db.InputBills
                .Include(x => x.InputBillDetail)
                .Include(x => x.InputBillSubmitDetail)
                .Where(x => request.InputBillIds.Contains(x.Id))
                .ToListAsync();

            if (inputBills.Count != request.InputBillIds.Count)
            {
                throw new ArgumentException("部分进项发票不存在");
            }

            // 验证进项发票状态
            if (inputBills.Any(x => x.Status != 1)) // 1=临时发票
            {
                throw new ArgumentException("只能合并临时状态的进项发票");
            }

            // 验证原进项票对应的提交明细不能有记录
            var inputBillsWithSubmitDetails = inputBills
                .Where(x => x.InputBillSubmitDetail != null && x.InputBillSubmitDetail.Any())
                .ToList();

            if (inputBillsWithSubmitDetails.Any())
            {
                var invalidBillNumbers = string.Join(", ", inputBillsWithSubmitDetails.Select(x => x.InvoiceNumber));
                throw new ArgumentException($"进项票{invalidBillNumbers}有勾稽明细，不能进行合并，请先删除发票明细，再进行合并勾稽");
            }

            // 验证进项发票公司和供应商是否一致
            var firstBill = inputBills.First();
            if (inputBills.Any(x => x.CompanyId != firstBill.CompanyId))
            {
                throw new ArgumentException("请选择相同公司的进项发票进行合并");
            }

            if (inputBills.Any(x => x.AgentId != firstBill.AgentId))
            {
                throw new ArgumentException("请选择相同供应商的进项发票进行合并");
            }

            // 验证进项发票的票据类型是否一致（普票/专票）
            if (inputBills.Any(x => x.Type != firstBill.Type))
            {
                throw new ArgumentException("不能合并勾稽不同票据类型的进项票，请重新选择进项票");
            }

            // 生成合并发票号
            var now = DateTime.Now;
            string mergeInvoiceNumber;

            // 获取公司信息
            var companyInfoOutput = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { firstBill.CompanyId.ToString() } });
            var companyInfo = companyInfoOutput?.FirstOrDefault();

            if (companyInfo == null)
            {
                throw new ArgumentException("公司信息不存在");
            }

            if (string.IsNullOrWhiteSpace(companyInfo.sysMonth))
            {
                companyInfo.sysMonth = DateTime.Now.ToString("yyyy-MM");
            }

            // 获取当前用户的核算部门信息
            string businessArea = "ZXBD"; // 默认核算部门
            if (CurrentUser.Id.HasValue)
            {
                try
                {
                    var userInfo = await _applyBFFService.GetUserInfoByIdAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                    {
                        userId = CurrentUser.Id.ToString()
                    });

                    if (!string.IsNullOrWhiteSpace(userInfo.DeptShortName))
                    {
                        businessArea = userInfo.DeptShortName;
                        _logger.LogInformation("CreateMergeInputBill - 使用当前用户核算部门简称: {DeptShortName}", businessArea);
                    }
                    else
                    {
                        _logger.LogWarning("CreateMergeInputBill - 当前用户核算部门简称为空，使用默认值: {DefaultDeptShortName}", businessArea);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "CreateMergeInputBill - 获取当前用户核算部门信息失败，使用默认值: {DefaultDeptShortName}", businessArea);
                }
            }
            else
            {
                _logger.LogWarning("CreateMergeInputBill - 当前用户ID为空，使用默认核算部门简称: {DefaultDeptShortName}", businessArea);
            }

            // 使用统一的单号生成方法
            var outPut = await _codeClient.ApplyCode(new ApplyCodeInput
            {
                BusinessArea = businessArea, // 使用当前用户的核算部门简称
                BillType = "IN", // 进项票类型
                SysMonth = companyInfo.sysMonth,
                DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                Num = 1,
                CompanyCode = companyInfo.nameCode // 公司简码
            });

            if (outPut.Status)
            {
                mergeInvoiceNumber = outPut.Codes.First(); // 格式为：ZXBD-SH-IN-2504-000001
            }
            else
            {
                throw new ApplicationException($"生成合并发票号失败：{outPut.Msg}");
            }

            // 创建合并进项发票
            var mergeInputBill = new MergeInputBillPo
            {
                Id = Guid.NewGuid(),
                MergeInvoiceNumber = mergeInvoiceNumber,
                CompanyId = firstBill.CompanyId,
                CompanName = firstBill.CompanName,
                AgentId = firstBill.AgentId,
                AgentName = firstBill.AgentName,
                MergeTime = now,
                Type = (InputBillTypeEnum)firstBill.Type,
                PurchaseDutyNumber = firstBill.PurchaseDutyNumber,
                SaleDutyNumber = firstBill.SaleDutyNumber,
                NotaxAmount = 0, // 初始化为0，后面会根据明细重新计算
                TaxAmount = 0, // 初始化为0，后面会根据明细重新计算
                Amount = 0, // 初始化为0，后面会根据明细重新计算
                Remark = request.Remark,
                Status = (int)MergeInputBillStatusEnum.Temporary, // 临时
                CreatedBy = CurrentUser.UserName ?? "System",
                CreatedTime = DateTimeOffset.Now
            };

            await _db.MergeInputBills.AddAsync(mergeInputBill);

            // 处理明细分组 - 重构后的代码，使用统一的方法处理三种不同的数量情况
            // 原来的代码分别处理了quantity>0、quantity<0和quantity=0三种情况
            // 我们保持三种情况分开处理，但使用更统一的代码结构

            // 定义一个通用的处理函数，根据不同的数量条件处理明细
            Func<decimal, bool> positiveCondition = q => q > 0;
            Func<decimal, bool> negativeCondition = q => q < 0;
            Func<decimal, bool> zeroCondition = q => q == 0;

            // 处理正数数量的明细
            var positiveDetails = ProcessInputBillDetails(
                inputBills,
                mergeInputBill.Id,
                positiveCondition,
                (group) =>
                {
                    // 正数情况下的金额计算逻辑
                    var taxAmount = group.Sum(x => x.TaxAmount); // 税额
                    var noTaxAmount = group.Sum(x => x.NoTaxAmount); // 不含税金额
                    var totalAmount = group.Sum(x => x.TaxAmount + x.NoTaxAmount); // 含税金额
                    return (taxAmount, noTaxAmount, totalAmount);
                });

            // 处理负数数量的明细
            var negativeDetails = ProcessInputBillDetails(
                inputBills,
                mergeInputBill.Id,
                negativeCondition,
                (group) =>
                {
                    // 负数情况下的金额计算逻辑
                    var taxAmount = group.Sum(x => x.TaxAmount); // 不含税金额
                    var noTaxAmount = group.Sum(x => x.NoTaxAmount); // 金额
                    var totalAmount = group.Sum(x => x.TaxAmount + x.NoTaxAmount); // 对于负数，使用noTaxAmount作为totalAmount
                    return (taxAmount, noTaxAmount, totalAmount);
                });

            // 处理零数量的明细
            var zeroDetails = ProcessInputBillDetails(
                inputBills,
                mergeInputBill.Id,
                zeroCondition,
                (group) =>
                {
                    // 零数量情况下的金额计算逻辑
                    var taxAmount = group.Sum(x => x.TaxAmount); // 税额
                    var noTaxAmount = group.Sum(x => x.NoTaxAmount); // 金额
                    var totalAmount = group.Sum(x => x.TaxAmount + x.NoTaxAmount); // 含税金额
                    return (taxAmount, noTaxAmount, totalAmount);
                });

            // 合并所有明细（正数、负数和零）
            var mergeInputBillDetails = positiveDetails.Concat(negativeDetails).Concat(zeroDetails).ToList();
            if (mergeInputBillDetails.Count == 0)
            {
                throw new ArgumentException("没有符合条件的金蝶明细，无法创建合并进项发票");
            }
            // 定义本地函数，用于处理不同数量条件的明细
            List<MergeInputBillDetailPo> ProcessInputBillDetails(
                List<InputBillPo> bills,
                Guid mergeId,
                Func<decimal, bool> quantityCondition,
                Func<IEnumerable<InputBillDetailPo>, (decimal taxAmount, decimal noTaxAmount, decimal totalAmount)> calculateAmounts)
            {
                return bills
                    .Where(x => x.InputBillDetail != null)
                    .SelectMany(x => x.InputBillDetail!)
                    .Where(x => quantityCondition(x.Quantity))
                    .GroupBy(x => new
                    {
                        ProductName = x.ProductName?.Trim() ?? string.Empty,
                        ProductNo = x.ProductNo?.Trim() ?? string.Empty,
                        x.TaxRate,
                        x.TaxCost
                    })
                    .Select(g =>
                    {
                        // 计算总数量
                        var quantity = g.Sum(x => x.Quantity);

                        // 获取第一个明细项，用于获取不含税单价
                        var firstDetail = g.FirstOrDefault();
                        var noTaxCost = firstDetail?.NoTaxCost ?? 0;

                        // 根据传入的计算函数计算金额
                        var (taxAmount, noTaxAmount, totalAmount) = calculateAmounts(g);

                        // 创建合并进项票明细
                        return new MergeInputBillDetailPo
                        {
                            Id = Guid.NewGuid(),
                            MergeInputBillId = mergeId,
                            ProductName = g.Key.ProductName,
                            ProductNo = g.Key.ProductNo,
                            Quantity = quantity,
                            TaxCost = g.Key.TaxCost, // 使用分组的含税单价
                            NoTaxCost = noTaxCost,
                            NoTaxAmount = noTaxAmount,
                            TotalAmount = totalAmount,
                            TaxRate = g.Key.TaxRate, // 使用分组的税率
                            TaxAmount = taxAmount,
                            CreatedBy = CurrentUser.UserName ?? "System",
                            CreatedTime = DateTimeOffset.Now
                        };
                    })
                    .ToList();
            }
            await _db.MergeInputBillDetails.AddRangeAsync(mergeInputBillDetails);

            // 更新合并进项发票的金额
            // 为了与原进项票保持一致，NotaxAmount保存含税金额，TaxAmount保存税额
            // 直接使用原进项票单头的税额和、金额和和总金额和，而不是通过明细计算
            mergeInputBill.NotaxAmount = inputBills.Sum(x => x.NotaxAmount); // 金额
            mergeInputBill.TaxAmount = inputBills.Sum(x => x.TaxAmount); // 税额
            mergeInputBill.Amount = inputBills.Sum(x => x.Amount); // 总金额

            _logger.LogInformation("CreateMergeInputBill - 使用原进项票单头税额和: {TaxAmount}, 金额和: {NotaxAmount}, 总金额和: {Amount}",
                mergeInputBill.TaxAmount, mergeInputBill.NotaxAmount, mergeInputBill.Amount);

            // 创建合并进项发票与原始进项发票的关联关系
            var mergeInputBillRelations = inputBills.Select(x => new MergeInputBillRelationPo
            {
                Id = Guid.NewGuid(),
                MergeInputBillId = mergeInputBill.Id,
                InputBillId = x.Id,
                CreatedBy = CurrentUser.UserName ?? "System",
                CreatedTime = DateTimeOffset.Now
            }).ToList();

            await _db.MergeInputBillRelations.AddRangeAsync(mergeInputBillRelations);

            // 更新原始进项发票状态为"正在合并勾稽"，并清除取消勾稽信息
            foreach (var inputBill in inputBills)
            {
                inputBill.Status = (int)InputBillStatusEnum.InMergeReconciliation; // 正在合并勾稽
                // 创建合并进项票时清除取消勾稽的时间和状态
                inputBill.CancelReconciliationTime = null;
                inputBill.IsCancelledReconciliation = null;
            }

            _db.InputBills.UpdateRange(inputBills);

            // 保存所有更改
            await _db.SaveChangesAsync();

            // 创建成功后自动调用 StartAsyncMatch 方法，异步处理匹配逻辑
            _logger.LogInformation("CreateMergeInputBill - 创建合并进项发票成功，开始异步处理匹配逻辑, MergeInputBillId: {MergeInputBillId}", mergeInputBill.Id);

            try
            {
                // 验证时间段
                if (request.StartDate == null || request.EndDate == null)
                {
                    _logger.LogWarning("CreateMergeInputBill - 未提供开始时间或结束时间，跳过自动匹配, MergeInputBillId: {MergeInputBillId}", mergeInputBill.Id);
                    return new CreateMergeInputBillResponse
                    {
                        MergeInputBillId = mergeInputBill.Id,
                        MergeInvoiceNumber = mergeInputBill.MergeInvoiceNumber
                    };
                }

                // 创建异步匹配请求
                var matchRequest = new StartAsyncMatchRequest
                {
                    MergeInputBillId = mergeInputBill.Id,
                    // 使用请求中的开始时间和结束时间
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    // 首次创建时不需要重新匹配
                    IsReload = false
                };

                // 调用异步匹配方法
                var matchResult = await StartAsyncMatch(matchRequest);

                if (matchResult.Code == CodeStatusEnum.Success)
                {
                    _logger.LogInformation("CreateMergeInputBill - 已启动异步匹配处理, MergeInputBillId: {MergeInputBillId}", mergeInputBill.Id);
                }
                else
                {
                    _logger.LogWarning("CreateMergeInputBill - 启动异步匹配处理返回失败状态, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        mergeInputBill.Id, matchResult.Message);
                }
            }
            catch (Exception ex)
            {
                // 仅记录异常，不影响创建结果的返回
                _logger.LogError(ex, "CreateMergeInputBill - 启动异步匹配处理失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    mergeInputBill.Id, ex.Message);
            }

            return new CreateMergeInputBillResponse
            {
                MergeInputBillId = mergeInputBill.Id,
                MergeInvoiceNumber = mergeInputBill.MergeInvoiceNumber
            };
        }

        /// <summary>
        /// 查询合并进项发票列表
        /// </summary>
        /// <param name="request">查询合并进项发票列表请求</param>
        /// <returns>查询合并进项发票列表响应</returns>
        public async Task<QueryMergeInputBillListResponse> QueryMergeInputBillList(QueryMergeInputBillListRequest request)
        {
            _logger.LogInformation("QueryMergeInputBillList - 开始查询合并进项发票列表");

            try
            {
                // 构建查询
                var query = _db.MergeInputBills.AsQueryable();

                // 应用数据策略权限过滤
                if (CurrentUser.Id.HasValue)
                {
                    var strategyInput = new StrategyQueryInput
                    {
                        userId = CurrentUser.Id,
                        functionUri = "metadata://fam/inputbill-deatil-Query/routes/inputBuillMultipleIndex-view"
                    };

                    // 获取用户数据策略权限
                    var strategys = await _pcApiClient.GetStrategyAsync(strategyInput);
                    if (strategys != null && strategys.RowStrategies.Count > 0)
                    {
                        // 公司权限过滤
                        if (strategys.RowStrategies.TryGetValue("company", out var companyIds) && companyIds.Any())
                        {
                            if (!companyIds.Contains("@all"))
                            {
                                var guidCompanyIds = companyIds
                                    .Where(id => Guid.TryParse(id, out _))
                                    .Select(id => Guid.Parse(id))
                                    .ToList();

                                if (guidCompanyIds.Any())
                                {
                                    query = query.Where(x => guidCompanyIds.Contains(x.CompanyId));
                                }
                                else
                                {
                                    // 如果没有有效的公司ID，则返回空结果
                                    query = query.Where(x => false);
                                }
                            }
                        }
                        else
                        {
                            // 如果没有公司权限，则返回空结果
                            query = query.Where(x => false);
                        }
                    }
                    else
                    {
                        // 如果没有任何权限策略，则返回空结果
                        query = query.Where(x => false);
                    }
                }

                // 根据条件过滤
                if (request.CompanyId.HasValue)
                {
                    query = query.Where(x => x.CompanyId == request.CompanyId.Value);
                }

                // 处理供应商ID筛选
                if (request.AgentId.HasValue)
                {
                    query = query.Where(x => x.AgentId == request.AgentId.Value);
                }
                else if (request.AgentIds != null && request.AgentIds.Any())
                {
                    // 如果提供了AgentIds列表，使用它进行筛选
                    query = query.Where(x => x.AgentId.HasValue && request.AgentIds.Contains(x.AgentId.Value));
                }

                if (request.Status.HasValue)
                {
                    query = query.Where(x => x.Status == (int)request.Status.Value);
                }

                // 根据合并进项票单号过滤
                if (!string.IsNullOrWhiteSpace(request.MergeInvoiceNumber))
                {
                    query = query.Where(x => x.MergeInvoiceNumber.Contains(request.MergeInvoiceNumber));
                }

                // 根据票据类型过滤
                if (request.Type.HasValue && request.Type.Value != InputBillTypeEnum.All)
                {
                    query = query.Where(x => x.Type == request.Type.Value);
                }

                // 开票时间
                if (request.BeginCreatedTime.HasValue && request.EndCreatedTime.HasValue)
                {
                    // 构建时间范围查询
                    var inputBillsQuery = _db.InputBills.Where(x => x.BillTime >= request.BeginCreatedTime.Value && x.BillTime <= request.EndCreatedTime.Value).AsQueryable();
                    // 查询符合条件的原始进项票ID
                    var timeFilteredInputBillIds = inputBillsQuery.Select(x => x.Id).ToList();

                    if (timeFilteredInputBillIds.Any())
                    {
                        // 查询包含这些原始进项票的合并进项票ID
                        var timeFilteredMergeInputBillIds = _db.MergeInputBillRelations
                            .Where(r => timeFilteredInputBillIds.Contains(r.InputBillId))
                            .Select(r => r.MergeInputBillId)
                            .Distinct()
                            .ToList();

                        if (timeFilteredMergeInputBillIds.Any())
                        {
                            // 过滤合并进项票
                            query = query.Where(x => timeFilteredMergeInputBillIds.Contains(x.Id));
                        }
                        else
                        {
                            // 如果没有找到匹配的合并进项票，返回空结果
                            query = query.Where(x => false);
                        }
                    }
                    else
                    {
                        // 如果没有找到匹配的原始进项票，返回空结果
                        query = query.Where(x => false);
                    }
                }

                // 根据发票号过滤（查询原始进项票）
                if (!string.IsNullOrWhiteSpace(request.InvoiceNumber))
                {
                    // 查询符合条件的原始进项票ID
                    var invoiceNumberFilteredInputBillIds = _db.InputBills
                        .Where(x => x.InvoiceNumber.Contains(request.InvoiceNumber))
                        .Select(x => x.Id)
                        .ToList();

                    if (invoiceNumberFilteredInputBillIds.Any())
                    {
                        // 查询包含这些原始进项票的合并进项票ID
                        var invoiceNumberFilteredMergeInputBillIds = _db.MergeInputBillRelations
                            .Where(r => invoiceNumberFilteredInputBillIds.Contains(r.InputBillId))
                            .Select(r => r.MergeInputBillId)
                            .Distinct()
                            .ToList();

                        if (invoiceNumberFilteredMergeInputBillIds.Any())
                        {
                            // 过滤合并进项票
                            query = query.Where(x => invoiceNumberFilteredMergeInputBillIds.Contains(x.Id));
                        }
                        else
                        {
                            // 如果没有找到匹配的合并进项票，返回空结果
                            query = query.Where(x => false);
                        }
                    }
                    else
                    {
                        // 如果没有找到匹配的原始进项票，返回空结果
                        query = query.Where(x => false);
                    }
                }

                // 根据业务单号过滤（查询合并进项票提交明细）
                string businessItemCodeToUse = null;

                // 优先使用BusinessItemCode，如果为空则使用StoreInItemCode
                if (!string.IsNullOrWhiteSpace(request.BusinessItemCode))
                {
                    businessItemCodeToUse = request.BusinessItemCode;
                }
                else if (!string.IsNullOrWhiteSpace(request.StoreInItemCode))
                {
                    businessItemCodeToUse = request.StoreInItemCode;
                }

                if (!string.IsNullOrWhiteSpace(businessItemCodeToUse))
                {
                    var businessItemCodeFilteredMergeInputBillIds = _db.MergeInputBillSubmitDetails
                        .Where(x => x.BussinessItemCode != null && x.BussinessItemCode.Contains(businessItemCodeToUse))
                        .Select(x => x.MergeInputBillId)
                        .Distinct()
                        .ToList();

                    if (businessItemCodeFilteredMergeInputBillIds.Any())
                    {
                        query = query.Where(x => businessItemCodeFilteredMergeInputBillIds.Contains(x.Id));
                    }
                    else
                    {
                        query = query.Where(x => false);
                    }
                }

                // 根据货号过滤（只查询合并进项票提交明细）
                List<Guid> productNoFilteredMergeInputBillIds = new List<Guid>();
                if (!string.IsNullOrWhiteSpace(request.ProductNo))
                {
                    // 查询合并进项票提交明细
                    productNoFilteredMergeInputBillIds = _db.MergeInputBillSubmitDetails
                        .Where(x => x.ProductNo != null && x.ProductNo.Contains(request.ProductNo))
                        .Select(x => x.MergeInputBillId)
                        .Distinct()
                        .ToList();

                    if (productNoFilteredMergeInputBillIds.Any())
                    {
                        query = query.Where(x => productNoFilteredMergeInputBillIds.Contains(x.Id));
                    }
                    else
                    {
                        // 如果没有找到匹配的合并进项票，返回空结果
                        query = query.Where(x => false);
                    }
                }

                // 根据型号过滤（只查询合并进项票提交明细）
                if (!string.IsNullOrWhiteSpace(request.Model))
                {
                    // 查询合并进项票提交明细
                    var modelFilteredMergeInputBillIds = _db.MergeInputBillSubmitDetails
                        .Where(x => x.Model != null && x.Model.Contains(request.Model))
                        .Select(x => x.MergeInputBillId)
                        .Distinct()
                        .ToList();

                    if (modelFilteredMergeInputBillIds.Any())
                    {
                        query = query.Where(x => modelFilteredMergeInputBillIds.Contains(x.Id));
                    }
                    else
                    {
                        // 如果没有找到匹配的合并进项票，返回空结果
                        query = query.Where(x => false);
                    }
                }

                // 根据是否已有发票明细过滤
                if (request.HasDetail.HasValue)
                {
                    if (request.HasDetail.Value == 1) // 有明细
                    {
                        // 查询有明细的合并进项票ID
                        var hasDetailFilteredMergeInputBillIds = _db.MergeInputBillSubmitDetails
                            .Select(x => x.MergeInputBillId)
                            .Distinct()
                            .ToList();

                        query = query.Where(x => hasDetailFilteredMergeInputBillIds.Contains(x.Id));
                    }
                    else if (request.HasDetail.Value == 2) // 无明细
                    {
                        // 查询有明细的合并进项票ID
                        var mergeInputBillIdsWithDetails = _db.MergeInputBillSubmitDetails
                            .Select(x => x.MergeInputBillId)
                            .Distinct()
                            .ToList();

                        // 过滤掉有明细的合并进项票
                        query = query.Where(x => !mergeInputBillIdsWithDetails.Contains(x.Id));
                    }
                    // 如果是0（全部），则不需要过滤
                }

                // 处理提交时间筛选
                if (request.SubmitTimeStart.HasValue)
                {
                    query = query.Where(x => x.SubmitTime.HasValue && x.SubmitTime.Value >= request.SubmitTimeStart.Value);
                }

                if (request.SubmitTimeEnd.HasValue)
                {
                    query = query.Where(x => x.SubmitTime.HasValue && x.SubmitTime.Value <= request.SubmitTimeEnd.Value.AddDays(1).AddMinutes(-1));
                }

                // 处理创建人筛选
                if (!string.IsNullOrWhiteSpace(request.CreatedBy))
                {
                    query = query.Where(x => x.CreatedBy.Contains(request.CreatedBy));
                }
                else if (request.CreatedByList != null && request.CreatedByList.Any())
                {
                    // 如果提供了CreatedByList列表，使用它进行筛选
                    query = query.Where(x => request.CreatedByList.Contains(x.CreatedBy));
                }

                // 取消勾稽状态过滤
                if (request.cancelReconciliationStatusName.HasValue)
                {
                    if (request.cancelReconciliationStatusName.Value)
                    {
                        query = query.Where(z => z.IsCancelledReconciliation == request.cancelReconciliationStatusName.Value);
                    }
                    else
                    {
                        query = query.Where(z => z.IsCancelledReconciliation != true);
                    }
                }

                // 取消勾稽时间范围过滤
                if (request.beginCancelReconciliationTime.HasValue && request.endCancelReconciliationTime.HasValue)
                {
                    var startTime = request.beginCancelReconciliationTime.Value;
                    var endTime = request.endCancelReconciliationTime.Value.AddDays(1).AddMinutes(-1);
                    query = query.Where(x => x.CancelReconciliationTime.HasValue &&
                                           x.CancelReconciliationTime.Value >= startTime &&
                                           x.CancelReconciliationTime.Value <= endTime);
                }

                // 获取总记录数
                var total = await query.CountAsync();

                // 获取分页后的合并进项票ID列表
                var mergeInputBillIds = await query
                    .OrderByDescending(x => x.CreatedTime)
                    .Skip((request.Page - 1) * request.Limit)
                    .Take(request.Limit)
                    .Select(x => x.Id)
                    .ToListAsync();

                // 一次性查询所有关联的原始进项票数量
                var relationCounts = await _db.MergeInputBillRelations
                    .Where(r => mergeInputBillIds.Contains(r.MergeInputBillId))
                    .GroupBy(r => r.MergeInputBillId)
                    .Select(g => new { MergeInputBillId = g.Key, Count = g.Count() })
                    .ToDictionaryAsync(x => x.MergeInputBillId, x => x.Count);

                // 查询合并进项票详情
                var mergeInputBills = await query
                    .Where(x => mergeInputBillIds.Contains(x.Id))
                    .OrderByDescending(x => x.CreatedTime)
                    .ToListAsync();

                // 查询原始进项票关联
                var mergeInputBillRelations = await _db.MergeInputBillRelations
                    .Where(r => mergeInputBillIds.Contains(r.MergeInputBillId))
                    .ToListAsync();

                // 获取所有原始进项票ID
                var originalInputBillIds = mergeInputBillRelations.Select(r => r.InputBillId).Distinct().ToList();

                // 查询原始进项票信息（包含取消对账相关字段）
                var originalInputBills = await _db.InputBills
                    .Where(x => originalInputBillIds.Contains(x.Id))
                    .Select(x => new
                    {
                        Id = x.Id,
                        InvoiceNumber = x.InvoiceNumber,
                        InvoiceCode = x.InvoiceCode,
                        BillTime = x.BillTime,
                        NotaxAmount = x.NotaxAmount,
                        TaxAmount = x.TaxAmount,
                        Amount = x.Amount,
                        Status = x.Status,
                        Type = x.Type,
                        CreatedTime = x.CreatedTime,
                        CompanyName = x.CompanName,
                        AgentName = x.AgentName,
                        PurchaseDutyNumber = x.PurchaseDutyNumber,
                        SaleDutyNumber = x.SaleDutyNumber,
                        CreatedBy = x.CreatedBy,
                        CancelReconciliationTime = x.CancelReconciliationTime,
                        IsCancelledReconciliation = x.IsCancelledReconciliation,
                    })
                    .ToListAsync();

                // 按合并进项票ID分组原始进项票
                var originalInputBillsByMergeId = mergeInputBillRelations
                    .GroupBy(r => r.MergeInputBillId)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(r => r.InputBillId).ToList()
                    );

                // 构建返回结果
                var items = mergeInputBills.Select(x =>
                {
                    var item = new MergeInputBillListItem
                    {
                        Id = x.Id,
                        MergeInvoiceNumber = x.MergeInvoiceNumber,
                        CompanyId = x.CompanyId,
                        CompanyName = x.CompanName,
                        AgentId = x.AgentId,
                        AgentName = x.AgentName,
                        MergeTime = x.MergeTime,
                        Type = (int)x.Type,
                        TypeDesc = x.Type.GetDescription(),
                        PurchaseDutyNumber = x.PurchaseDutyNumber,
                        SaleDutyNumber = x.SaleDutyNumber,
                        NotaxAmount = x.NotaxAmount,
                        TaxAmount = x.TaxAmount,
                        Amount = x.Amount,
                        Remark = x.Remark,
                        Status = (MergeInputBillStatusEnum)x.Status,
                        StatusDesc = ((MergeInputBillStatusEnum)x.Status).GetDescription(),
                        OriginalInputBillCount = relationCounts.TryGetValue(x.Id, out int count) ? count : 0,
                        CreatedTime = ConvertToBeijingTime(x.CreatedTime.UtcDateTime),
                        CancelReconciliationTime = x.CancelReconciliationTime,
                        IsCancelledReconciliation = x.IsCancelledReconciliation,
                        CreatedBy = x.CreatedBy ?? string.Empty,
                        SubmitTime = x.SubmitTime.HasValue ? ConvertToBeijingTime(x.SubmitTime.Value) : (DateTime?)null
                    };

                    // 添加原始进项票列表
                    if (originalInputBillsByMergeId.TryGetValue(x.Id, out var inputBillIds))
                    {
                        item.OriginalInputBills = inputBillIds
                            .Select(id =>
                            {
                                var originalBill = originalInputBills.FirstOrDefault(o => o.Id == id);
                                if (originalBill != null)
                                {
                                    return new MergeInputBillListOriginalItem
                                    {
                                        Id = originalBill.Id,
                                        CompanyName = originalBill.CompanyName,
                                        AgentName = originalBill.AgentName,
                                        InvoiceNumber = originalBill.InvoiceNumber,
                                        InvoiceCode = originalBill.InvoiceCode,
                                        BillTime = originalBill.BillTime,
                                        NotaxAmount = originalBill.NotaxAmount,
                                        TaxAmount = originalBill.TaxAmount,
                                        Amount = originalBill.Amount,
                                        Status = (InputBillStatusEnum)originalBill.Status,
                                        Type = (InputBillTypeEnum)originalBill.Type,
                                        CreatedTime = ConvertToBeijingTime(originalBill.CreatedTime.UtcDateTime),
                                        PurchaseDutyNumber = originalBill.PurchaseDutyNumber,
                                        SaleDutyNumber = originalBill.SaleDutyNumber,
                                        CreatedBy = originalBill.CreatedBy ?? string.Empty,
                                        CancelReconciliationTime = originalBill.CancelReconciliationTime,
                                        IsCancelledReconciliation = originalBill.IsCancelledReconciliation,
                                    };
                                }
                                return null;
                            })
                            .Where(o => o != null)
                            .Select(o => o!) // 非空断言
                            .ToList();
                    }

                    return item;
                }).ToList();

                _logger.LogInformation("QueryMergeInputBillList - 查询合并进项发票列表成功, 总数量: {Total}, 当前页: {Page}, 每页数量: {Limit}",
                    total, request.Page, request.Limit);

                return new QueryMergeInputBillListResponse
                {
                    Items = items,
                    Total = total,
                    Page = request.Page,
                    Limit = request.Limit
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "QueryMergeInputBillList - 查询合并进项发票列表失败");
                throw;
            }
        }

        /// <summary>
        /// 还原合并进项发票
        /// </summary>
        /// <param name="request">还原合并进项发票请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> RestoreMergeInputBill(RestoreMergeInputBillRequest request)
        {
            _logger.LogInformation("RestoreMergeInputBill - 开始还原合并进项发票, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("RestoreMergeInputBill - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 校验当前用户是否为创建者
                string currentUserName = CurrentUser.UserName ?? string.Empty;
                if (string.IsNullOrEmpty(currentUserName) || mergeInputBill.CreatedBy != currentUserName)
                {
                    _logger.LogWarning("RestoreMergeInputBill - 当前用户不是创建者，无法还原合并单据, 当前用户: {CurrentUser}, 创建者: {CreatedBy}",
                        currentUserName, mergeInputBill.CreatedBy);
                    throw new UnauthorizedAccessException("只有创建者才能还原合并单据");
                }
                // 检查状态，只有匹配完成状态才能还原匹配，已提交状态不允许还原匹配
                if (mergeInputBill.Status != (int)MergeInputBillStatusEnum.CompletedReconciliation)
                {
                    _logger.LogWarning("RestoreMergeInputBill - 当前状态不能还原匹配, 状态: {Status}", ((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription());
                    throw new ApplicationException($"只有匹配完成状态才能还原匹配，当前状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}");
                }

                // 查询原始进项发票ID
                var originalInputBillIds = await _db.MergeInputBillRelations
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .Select(x => x.InputBillId)
                    .ToListAsync();

                // 查询原始进项发票
                var originalInputBills = await _db.InputBills
                    .Where(x => originalInputBillIds.Contains(x.Id))
                    .ToListAsync();

                // 更新原始进项发票状态为"临时草稿"
                foreach (var inputBill in originalInputBills)
                {
                    inputBill.Status = 1; // 1=临时发票
                }

                _db.InputBills.UpdateRange(originalInputBills);

                // 删除合并进项发票相关数据
                var mergeInputBillRelations = await _db.MergeInputBillRelations
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                var mergeInputBillDetails = await _db.MergeInputBillDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                var mergeInputBillSubmitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                var mergeInputBillDebts = await _db.MergeInputBillDebts
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                // 删除数据
                _db.MergeInputBillDebts.RemoveRange(mergeInputBillDebts);
                _db.MergeInputBillSubmitDetails.RemoveRange(mergeInputBillSubmitDetails);
                _db.MergeInputBillDetails.RemoveRange(mergeInputBillDetails);
                _db.MergeInputBillRelations.RemoveRange(mergeInputBillRelations);
                _db.MergeInputBills.Remove(mergeInputBill);

                // 保存所有更改
                await _db.SaveChangesAsync();

                _logger.LogInformation("RestoreMergeInputBill - 还原合并进项发票成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RestoreMergeInputBill - 还原合并进项发票失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 开始异步匹配
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>操作结果</returns>
        public async Task<BaseResponseData<bool>> StartAsyncMatch(StartAsyncMatchRequest request)
        {
            try
            {
                // 验证请求
                if (request == null || request.MergeInputBillId == Guid.Empty)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请求参数无效",
                        Data = false
                    };
                }

                // 验证时间段
                if (request.StartDate == null || request.EndDate == null)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请选择开始时间和结束时间",
                        Data = false
                    };
                }

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "合并进项发票不存在",
                        Data = false
                    };
                }

                // 检查是否为创建人本人
                if (request.IsReload && !string.IsNullOrEmpty(mergeInputBill.CreatedBy) &&
                    !string.IsNullOrEmpty(CurrentUser.UserName) &&
                    mergeInputBill.CreatedBy != CurrentUser.UserName)
                {
                    _logger.LogWarning("StartAsyncMatch - 非创建人不能重新匹配, 创建人: {CreatedBy}, 当前用户: {CurrentUser}",
                        mergeInputBill.CreatedBy, CurrentUser.UserName);
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "只有创建人本人才能重新匹配",
                        Data = false
                    };
                }

                // 检查状态是否为匹配完成（仅当重新匹配时）
                if (request.IsReload && mergeInputBill.Status != (int)MergeInputBillStatusEnum.CompletedReconciliation)
                {
                    _logger.LogWarning("StartAsyncMatch - 当前状态不能重新匹配, 预期状态: {ExpectedStatus}, 当前状态: {CurrentStatus}",
                        MergeInputBillStatusEnum.CompletedReconciliation.GetDescription(), ((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription());
                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"当前状态不能重新匹配，预期状态: {MergeInputBillStatusEnum.CompletedReconciliation.GetDescription()}，当前状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}",
                        Data = false
                    };
                }

                // 更新合并进项发票状态为"正在匹配"，并清除取消勾稽信息
                mergeInputBill.Status = (int)MergeInputBillStatusEnum.InMergeReconciliation;
                // 重新开始匹配时清除取消勾稽的时间和状态
                mergeInputBill.CancelReconciliationTime = null;
                mergeInputBill.IsCancelledReconciliation = null;

                // 查询关联的原始进项票
                var originalInputBillIds = await _db.MergeInputBillRelations
                    .Where(r => r.MergeInputBillId == request.MergeInputBillId)
                    .Select(r => r.InputBillId)
                    .ToListAsync();

                if (originalInputBillIds.Count > 0)
                {
                    // 查询原始进项票
                    var originalInputBills = await _db.InputBills
                        .Where(x => originalInputBillIds.Contains(x.Id))
                        .ToListAsync();

                    // 更新原始进项票状态为"正在匹配"，并清除取消勾稽信息
                    foreach (var inputBill in originalInputBills)
                    {
                        inputBill.Status = (int)InputBillStatusEnum.InMergeReconciliation;
                        // 重新开始匹配时清除取消勾稽的时间和状态
                        inputBill.CancelReconciliationTime = null;
                        inputBill.IsCancelledReconciliation = null;
                    }

                    _db.InputBills.UpdateRange(originalInputBills);
                    _logger.LogInformation("StartAsyncMatch - 更新原始进项票状态为正在匹配并清除取消勾稽信息, 数量: {Count}", originalInputBills.Count);
                }

                await _db.SaveChangesAsync();

                // 异步发布匹配任务消息
                await _daprClient.PublishEventAsync("pubsub-default", "finance-match-task", request);

                string message = request.IsReload
                    ? "开始重新匹配成功，原有匹配记录将被删除，请等待匹配结果"
                    : "开始异步匹配成功，请等待匹配结果";

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Message = message,
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始异步匹配失败: {ErrorMessage}", ex.Message);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"开始异步匹配失败: {ex.Message}",
                    Data = false
                };
            }
        }

        /// <summary>
        /// 处理匹配任务
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>处理结果</returns>
        public async Task ProcessMatchTask(StartAsyncMatchRequest request)
        {
            try
            {
                _logger.LogInformation("开始处理匹配任务, MergeInputBillId: {MergeInputBillId}, IsReload: {IsReload}",
                    request.MergeInputBillId, request.IsReload);

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogError("处理匹配任务失败: 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return;
                }

                // 如果是重新匹配，则删除原有匹配记录
                if (request.IsReload)
                {
                    _logger.LogInformation("处理匹配任务 - 重新匹配，删除原有匹配记录, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                    // 查询并删除原有的提交明细
                    var existingSubmitDetails = await _db.MergeInputBillSubmitDetails
                        .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                        .ToListAsync();

                    if (existingSubmitDetails.Count > 0)
                    {
                        _logger.LogInformation("处理匹配任务 - 删除原有提交明细, 数量: {Count}", existingSubmitDetails.Count);
                        _db.MergeInputBillSubmitDetails.RemoveRange(existingSubmitDetails);
                        await _db.SaveChangesAsync();
                    }

                    // 清除缓存
                    _cacheManager.ClearAllCache(request.MergeInputBillId);
                }

                // 执行匹配逻辑 - StartDate和EndDate对应发票的开票时间
                var matchResult = await _matchLogicService.ExecuteMatchLogic(new GetMatchableDocumentsRequest
                {
                    MergeInputBillId = request.MergeInputBillId,
                    CompanyId = mergeInputBill.CompanyId,
                    AgentId = mergeInputBill.AgentId,
                    StartDate = request.StartDate.Value, // 转换为北京时间的0点
                    EndDate = request.EndDate.Value.AddDays(1).AddSeconds(-1), // 转换为北京时间的23:59:59
                    MatchPrecision = MatchPrecisionEnum.Auto // 默认使用自动匹配
                });

                // 注意：MatchDetails 方法已经将匹配结果写入到 SubmitDetail 表中，这里不需要重复写入
                _logger.LogInformation("ProcessMatchTask - 匹配完成, 匹配项数量: {Count}", matchResult.Items?.Count ?? 0);

                // 构建通知消息
                var notificationMessage = new MatchNotificationMessage
                {
                    MergeInputBillId = request.MergeInputBillId,
                    MergeInvoiceNumber = mergeInputBill.MergeInvoiceNumber,
                    IsSuccess = true,
                    Message = "匹配完成",
                    MatchedItems = matchResult.Items ?? new List<MatchableDocumentItem>(),
                    MatchPrecision = (int)MatchPrecisionEnum.Auto
                };

                // 格式化当前时间（北京时间，UTC+8）
                string currentTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")).ToString("yyyy-MM-dd HH:mm:ss");

                // 构建自定义消息内容
                string customMessage = $"您在【{currentTime}】【合并进项票】自动匹配已完成，合并进项票单号【{mergeInputBill.MergeInvoiceNumber}】";

                // 发送通知给前端
                var messageNotification = new MessageNotificationInput
                {
                    SourceModule = "合并进项发票匹配",
                    MsgContent = customMessage,
                    lstToUser = new List<UserInput>
                    {
                        new UserInput
                        {
                            UserName = mergeInputBill.CreatedBy,
                            TrueName=mergeInputBill.CreatedBy
                        }
                    }
                };

                await _sendNotificationClient.SendNotification(messageNotification);
                _logger.LogInformation("匹配任务处理完成并发送通知, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                // 发送通知后，更新合并进项发票状态为"匹配完成"
                mergeInputBill.Status = (int)MergeInputBillStatusEnum.CompletedReconciliation;

                // 查询关联的原始进项票
                var originalInputBillIds = await _db.MergeInputBillRelations
                    .Where(r => r.MergeInputBillId == request.MergeInputBillId)
                    .Select(r => r.InputBillId)
                    .ToListAsync();

                if (originalInputBillIds.Any())
                {
                    // 查询原始进项票
                    var originalInputBills = await _db.InputBills
                        .Where(x => originalInputBillIds.Contains(x.Id))
                        .ToListAsync();

                    // 更新原始进项票状态为"匹配完成"
                    foreach (var inputBill in originalInputBills)
                    {
                        inputBill.Status = (int)InputBillStatusEnum.CompletedReconciliation;
                    }

                    _db.InputBills.UpdateRange(originalInputBills);
                    _logger.LogInformation("更新原始进项票状态为匹配完成, 数量: {Count}", originalInputBills.Count);
                }

                await _db.SaveChangesAsync();
                _logger.LogInformation("匹配任务处理完成后更新状态为匹配完成, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理匹配任务失败: {ErrorMessage}, MergeInputBillId: {MergeInputBillId}",
                    ex.Message, request.MergeInputBillId);

                try
                {
                    // 查询合并进项发票
                    var mergeInputBill = await _db.MergeInputBills
                        .Where(x => x.Id == request.MergeInputBillId)
                        .FirstOrDefaultAsync();

                    if (mergeInputBill != null)
                    {
                        // 构建通知消息（仍然保留原始通知消息对象，以便将来可能需要的其他处理）
                        var notificationMessage = new MatchNotificationMessage
                        {
                            MergeInputBillId = request.MergeInputBillId,
                            MergeInvoiceNumber = mergeInputBill.MergeInvoiceNumber,
                            IsSuccess = false,
                            Message = $"匹配失败: {ex.Message}",
                            MatchedItems = new List<MatchableDocumentItem>(),
                            MatchPrecision = (int)MatchPrecisionEnum.ProductName // 默认使用品名匹配
                        };

                        // 格式化当前时间（北京时间，UTC+8）
                        string currentTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")).ToString("yyyy-MM-dd HH:mm:ss");

                        // 构建自定义消息内容
                        string customMessage = $"您在【{currentTime}】【合并进项票】自动匹配失败，合并进项票单号【{mergeInputBill.MergeInvoiceNumber}】，失败原因：{ex.Message}";

                        // 发送通知给前端
                        var messageNotification = new MessageNotificationInput
                        {
                            SourceModule = "合并进项发票匹配",
                            MsgContent = customMessage,
                            lstToUser = new List<UserInput>
                            {
                                new UserInput
                                {
                                    UserName = mergeInputBill.CreatedBy ?? "admin",
                                    TrueName = mergeInputBill.CreatedBy ?? "系统管理员"
                                }
                            }
                        };

                        await _sendNotificationClient.SendNotification(messageNotification);

                        // 注意：匹配失败时，保持"正在匹配"状态，不改变状态
                        _logger.LogInformation("匹配任务处理失败，保持状态为正在匹配, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    }
                }
                catch (Exception innerEx)
                {
                    _logger.LogError(innerEx, "发送匹配失败通知失败: {ErrorMessage}", innerEx.Message);
                }
            }
        }

        /// <summary>
        /// 提交勾稽结果
        /// </summary>
        /// <param name="request">提交勾稽结果请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SubmitMatch(DTOs.MergeInputBills.SubmitMatchRequest request)
        {
            _logger.LogInformation("SubmitMatch - 开始提交勾稽结果, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogError("SubmitMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new Exception("合并进项发票不存在");
                }

                // 检查是否为创建人本人
                if (!string.IsNullOrEmpty(mergeInputBill.CreatedBy) &&
                    !string.IsNullOrEmpty(CurrentUser.UserName) &&
                    mergeInputBill.CreatedBy != CurrentUser.UserName)
                {
                    _logger.LogWarning("SubmitMatch - 非创建人不能提交, 创建人: {CreatedBy}, 当前用户: {CurrentUser}",
                        mergeInputBill.CreatedBy, CurrentUser.UserName);
                    throw new Exception("只有创建人本人才能提交");
                }

                // 创建提交匹配请求
                var submitRequest = new Services.SubmitHandling.SubmitMatchRequest
                {
                    MergeInputBillId = request.MergeInputBillId
                };

                // 调用提交处理服务
                var result = await _submitHandlingService.SubmitMatch(submitRequest, CurrentUser.UserName ?? "System");

                if (result.Code != CodeStatusEnum.Success)
                {
                    _logger.LogError("SubmitMatch - 提交勾稽结果失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, result.Message);
                    throw new Exception(result.Message);
                }

                // 更新合并进项发票的提交时间
                mergeInputBill.SubmitTime = DateTimeHelper.GetCurrentDate();
                mergeInputBill.UpdatedTime = DateTimeOffset.Now;
                mergeInputBill.UpdatedBy = CurrentUser.UserName ?? "System";
                await _db.SaveChangesAsync();
                _logger.LogInformation("SubmitMatch - 更新合并进项发票提交时间成功, MergeInputBillId: {MergeInputBillId}, SubmitTime: {SubmitTime}",
                    request.MergeInputBillId, mergeInputBill.SubmitTime);

                _logger.LogInformation("SubmitMatch - 提交勾稽结果成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SubmitMatch - 提交勾稽结果失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 检查合并进项票是否可以编辑
        /// </summary>
        /// <param name="mergeInputBillId">合并进项票ID</param>
        /// <returns>是否可以编辑</returns>
        private async Task CheckEditableAsync(Guid mergeInputBillId)
        {
            // 查询合并进项发票
            var mergeInputBill = await _db.MergeInputBills
                .Where(x => x.Id == mergeInputBillId)
                .FirstOrDefaultAsync();

            if (mergeInputBill == null)
            {
                _logger.LogWarning("CheckEditable - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                throw new ArgumentException("合并进项发票不存在");
            }

            // 检查状态，只有完成合并勾稽状态才能编辑
            if (mergeInputBill.Status != (int)MergeInputBillStatusEnum.CompletedReconciliation)
            {
                _logger.LogWarning("CheckEditable - 当前状态不能编辑, 状态: {Status}", mergeInputBill.Status);
                throw new ArgumentException($"只有匹配完成状态才能编辑，当前状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}");
            }
        }

        /// <summary>
        /// 检查合并进项票状态是否与预期一致
        /// </summary>
        /// <param name="mergeInputBillId">合并进项票ID</param>
        /// <param name="expectedStatus">预期状态</param>
        /// <param name="operationName">操作名称，用于日志和错误消息</param>
        /// <returns>合并进项票对象</returns>
        private async Task<MergeInputBillPo> CheckStatusAsync(Guid mergeInputBillId, MergeInputBillStatusEnum expectedStatus, string operationName)
        {
            // 查询合并进项发票
            var mergeInputBill = await _db.MergeInputBills
                .Where(x => x.Id == mergeInputBillId)
                .FirstOrDefaultAsync();

            if (mergeInputBill == null)
            {
                _logger.LogWarning("{Operation} - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", operationName, mergeInputBillId);
                throw new ArgumentException("合并进项发票不存在");
            }

            // 检查状态是否与预期一致
            if (mergeInputBill.Status != (int)expectedStatus)
            {
                _logger.LogWarning("{Operation} - 当前状态不能{Operation}, 预期状态: {ExpectedStatus}, 当前状态: {CurrentStatus}",
                    operationName, operationName, expectedStatus.GetDescription(), ((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription());
                throw new ArgumentException($"当前状态不能{operationName}，预期状态: {expectedStatus.GetDescription()}，当前状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}");
            }

            return mergeInputBill;
        }

        /// <summary>
        /// 保存勾稽结果
        /// </summary>
        /// <param name="request">保存勾稽结果请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> SaveMatch(SaveMatchRequest request)
        {
            _logger.LogInformation("SaveMatch - 开始保存勾稽结果, MergeInputBillId: {MergeInputBillId}, 明细数量: {Count}",
                request.MergeInputBillId, request.MatchDetails?.Count ?? 0);

            try
            {
                // 检查是否可以编辑
                await CheckEditableAsync(request.MergeInputBillId);

                // 检查单据状态是否为匹配完成
                var mergeInputBill = await CheckStatusAsync(request.MergeInputBillId, MergeInputBillStatusEnum.CompletedReconciliation, "保存明细");

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("SaveMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 检查是否为创建人本人
                if (!string.IsNullOrEmpty(mergeInputBill.CreatedBy) &&
                    !string.IsNullOrEmpty(CurrentUser.UserName) &&
                    mergeInputBill.CreatedBy != CurrentUser.UserName)
                {
                    _logger.LogWarning("SaveMatch - 非创建人不能编辑, 创建人: {CreatedBy}, 当前用户: {CurrentUser}",
                        mergeInputBill.CreatedBy, CurrentUser.UserName);
                    throw new ArgumentException("只有创建人本人才能编辑开票明细");
                }

                _logger.LogInformation("SaveMatch - 找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInputBill.MergeInvoiceNumber);

                // 如果没有新的匹配明细，直接返回
                if (request.MatchDetails == null || request.MatchDetails.Count == 0)
                {
                    _logger.LogInformation("SaveMatch - 没有新的匹配明细需要保存");
                    return true;
                }

                // 查询合并进项发票明细，用于校验含税金额
                var mergeInputBillDetails = await _db.MergeInputBillDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                _logger.LogInformation("SaveMatch - 查询到合并进项发票明细, 数量: {Count}", mergeInputBillDetails.Count);

                // 查询现有的提交明细，用于判断是更新还是新增
                var existingSubmitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                _logger.LogInformation("SaveMatch - 查询到现有提交明细, 数量: {Count}", existingSubmitDetails.Count);

                // 用于存储需要新增的提交明细
                var newSubmitDetails = new List<MergeInputBillSubmitDetailPo>();
                // 用于存储需要更新的提交明细
                var updateSubmitDetails = new List<MergeInputBillSubmitDetailPo>();

                foreach (var detail in request.MatchDetails)
                {
                    // 生成匹配键，使用GUID
                    string matchKey = detail.MatchKey;
                    // 查找是否已存在相同MatchKey的记录
                    var existingDetail = existingSubmitDetails.FirstOrDefault(x => x.MatchKey == matchKey && x.MergeInputBillDetailId == detail.MergeInputBillDetailId);

                    // 查找对应的合并进项发票明细
                    var mergeInputBillDetail = mergeInputBillDetails.FirstOrDefault(x => x.Id == detail.MergeInputBillDetailId);
                    if (mergeInputBillDetail == null)
                    {
                        _logger.LogWarning("SaveMatch - 找不到对应的合并进项发票明细, MergeInputBillDetailId: {MergeInputBillDetailId}", detail.MergeInputBillDetailId);
                        throw new ArgumentException($"找不到对应的合并进项发票明细，ID: {detail.MergeInputBillDetailId}");
                    }

                    // 获取业务类型用于税率校验
                    var businessTypeForTaxValidation = Enum.Parse<BusinessType>(detail.BusinessType.ToString());

                    // 校验税率是否一致（损失确认业务类型跳过税率校验）
                    if (businessTypeForTaxValidation != BusinessType.LossRecognition && mergeInputBillDetail.TaxRate != detail.TaxRate)
                    {
                        _logger.LogWarning("SaveMatch - 税率不一致, 金蝶发票明细税率: {MergeDetailTaxRate}, 添加的明细税率: {MatchDetailTaxRate}",
                            mergeInputBillDetail.TaxRate, detail.TaxRate);
                        throw new ArgumentException($"税率不一致，金蝶发票明细税率: {(int)mergeInputBillDetail.TaxRate}，添加的明细税率: {string.Join(',', request.MatchDetails.Select(t => t.TaxRate).Distinct().ToArray())}");
                    }

                    // 记录损失确认跳过税率校验的日志
                    if (businessTypeForTaxValidation == BusinessType.LossRecognition)
                    {
                        _logger.LogInformation("SaveMatch - 损失确认业务类型跳过税率校验, 业务类型: {BusinessType}, MatchKey: {MatchKey}",
                            businessTypeForTaxValidation, matchKey);
                    }

                    if (existingDetail != null)
                    {
                        // 获取业务类型
                        var businessTypeEnum = Enum.Parse<BusinessType>(detail.BusinessType.ToString());

                        // 处理匹配数量和单价
                        decimal matchQuantity = detail.CurrentMatchQuantity;
                        decimal? taxCost = detail.TaxCost;
                        decimal? noTaxCost = detail.NoTaxCost;

                        // 对于购货修订、服务费和损失确认，匹配数量固定为1，将匹配金额写入taxcost
                        if (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition)
                        {
                            // 购货修订和服务费匹配的是金额，数量固定为1
                            matchQuantity = 1;
                            decimal matchAmount = detail.CurrentMatchAmount;

                            taxCost = existingDetail.TaxCost + matchAmount;

                            // 计算不含税单价 = 含税单价 / (1 + 税率/100)，使用10位小数精度
                            noTaxCost = existingDetail.NoTaxCost + Math.Round(matchAmount / (1 + detail.TaxRate / 100), 10);
                            _logger.LogInformation("SaveMatch - 特殊业务类型处理, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 匹配金额: {MatchAmount}, 匹配数量固定为1, 含税单价: {TaxCost}, 不含税单价: {NoTaxCost}",
                                businessTypeEnum, matchKey, matchAmount, taxCost, noTaxCost);
                        }
                        // 对于经销调出和换货转退货业务类型，确保MatchQuantity为负数（只在保存到数据库时转换）
                        else if (businessTypeEnum == BusinessType.DistributionTransfer || businessTypeEnum == BusinessType.ExchangeToReturn)
                        {
                            // 如果是红票业务类型，转换为负数
                            matchQuantity = -Math.Abs(matchQuantity);
                            _logger.LogInformation("SaveMatch - 红票业务类型特殊处理, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 保存到数据库时转换为负数: {MatchQuantity}",
                                businessTypeEnum, matchKey, matchQuantity);
                        }
                        // 其他业务类型保留原有符号
                        else
                        {
                            _logger.LogInformation("SaveMatch - 普通业务类型, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 保留原有符号: {MatchQuantity}",
                                businessTypeEnum, matchKey, matchQuantity);
                        }

                        // 如果已存在，则更新匹配数量和其他属性
                        _logger.LogInformation("SaveMatch - 更新现有提交明细, MatchKey: {MatchKey}, 原匹配数量: {OldMatchQuantity}, 新匹配数量: {NewMatchQuantity}",
                            matchKey, existingDetail.MatchQuantity, matchQuantity);

                        // 累加本次匹配数量到已有的匹配数量上
                        if (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition)
                        {
                            // 购货修订、服务费和损失确认：匹配数量固定为1，更新taxcost为新的匹配金额
                            existingDetail.MatchQuantity = 1;
                            existingDetail.TaxCost = taxCost;
                            existingDetail.NoTaxCost = noTaxCost; // 更新不含税单价
                        }
                        else
                        {
                            // 其他业务类型：累加本次匹配数量到已有的匹配数量上
                            existingDetail.MatchQuantity += matchQuantity;
                        }

                        // 税额计算
                        if (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition)
                        {
                            // 购货修订、服务费和损失确认：税额 = 含税金额 - 不含税金额，不含税金额 = 含税金额 / (1 + 税率/100)
                            existingDetail.TaxAmount = Math.Round(taxCost.Value - taxCost.Value / (1 + detail.TaxRate / 100), 4);
                            // 不含税金额 = 含税金额 / (1 + 税率/100)（不做四舍五入处理）
                            existingDetail.NoTaxAmount = taxCost.Value / (1 + detail.TaxRate / 100);
                            // 含税金额就是用户填写的金额
                            existingDetail.TotalAmount = taxCost.Value;
                            // 匹配金额就是用户填写的金额
                            existingDetail.MatchedAmount = taxCost.Value;
                        }
                        else
                        {
                            // 其他业务类型：税额 = 含税金额 - 不含税金额
                            existingDetail.TaxAmount = Math.Round(detail.TaxCost * existingDetail.MatchQuantity - detail.NoTaxCost * existingDetail.MatchQuantity, 4);
                            // 不含税金额 = 不含税单价 * 匹配数量（不做四舍五入处理）
                            existingDetail.NoTaxAmount = detail.NoTaxCost * existingDetail.MatchQuantity;
                            // 含税金额 = 含税单价 * 匹配数量（不做四舍五入处理）
                            existingDetail.TotalAmount = detail.TaxCost * existingDetail.MatchQuantity;
                            // 匹配金额 = 含税单价 * 匹配数量（不做四舍五入处理）
                            existingDetail.MatchedAmount = detail.TaxCost * existingDetail.MatchQuantity;
                        }

                        existingDetail.UpdatedBy = CurrentUser.UserName ?? "System";
                        existingDetail.UpdatedTime = DateTimeOffset.Now;

                        // 更新其他属性
                        existingDetail.ProductId = detail.ProductId;
                        existingDetail.ProductNameId = detail.ProductNameId;
                        existingDetail.ProducerId = detail.ProducerId;
                        existingDetail.ProducerName = detail.ProducerName;

                        // 只有经销入库和经销调出业务类型才需要给采购单号赋值，其它接口采购单号默认为空
                        existingDetail.PurchaseOrderCode = (businessTypeEnum == BusinessType.DistributionPurchase || businessTypeEnum == BusinessType.DistributionTransfer)
                            ? detail.PurchaseOrderCode
                            : null;

                        updateSubmitDetails.Add(existingDetail);
                    }
                    else
                    {
                        // 如果不存在，则创建新记录
                        _logger.LogInformation("SaveMatch - 创建新的提交明细, MatchKey: {MatchKey}, 匹配数量: {MatchQuantity}",
                            matchKey, detail.MatchQuantity);

                        // 获取业务类型
                        var businessTypeEnum = Enum.Parse<BusinessType>(detail.BusinessType.ToString());

                        // 特殊处理不同业务类型
                        decimal matchQuantity = detail.CurrentMatchQuantity;
                        decimal? taxCost = detail.TaxCost;
                        decimal? noTaxCost = detail.NoTaxCost;

                        // 对于购货修订、服务费和损失确认，匹配数量固定为1，将匹配金额写入taxcost
                        if (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition)
                        {
                            // 购货修订、服务费和损失确认匹配的是金额，数量固定为1
                            matchQuantity = 1;
                            // 将匹配金额写入taxcost（含税单价）
                            taxCost = detail.MatchedAmount;
                            // 计算不含税单价 = 含税单价 / (1 + 税率/100)，使用10位小数精度
                            noTaxCost = Math.Round(taxCost.Value / (1 + detail.TaxRate / 100), 10);
                            _logger.LogInformation("SaveMatch - 特殊业务类型处理, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 匹配金额: {MatchAmount}, 匹配数量固定为1, 含税单价: {TaxCost}, 不含税单价: {NoTaxCost}",
                                businessTypeEnum, matchKey, detail.MatchedAmount, taxCost, noTaxCost);
                        }
                        // 对于经销调出、换货转退货和损失确认业务类型，确保MatchQuantity为负数（只在保存到数据库时转换）
                        else if (businessTypeEnum == BusinessType.DistributionTransfer || businessTypeEnum == BusinessType.ExchangeToReturn)
                        {
                            // 如果是红票业务类型，转换为负数
                            matchQuantity = -Math.Abs(matchQuantity);
                            _logger.LogInformation("SaveMatch - 红票业务类型特殊处理, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 保存到数据库时转换为负数: {MatchQuantity}",
                                businessTypeEnum, matchKey, matchQuantity);
                        }
                        // 其他业务类型保留原有符号
                        else
                        {
                            _logger.LogInformation("SaveMatch - 普通业务类型, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 保留原有符号: {MatchQuantity}",
                                businessTypeEnum, matchKey, matchQuantity);
                        }

                        var newDetail = new MergeInputBillSubmitDetailPo
                        {
                            Id = Guid.NewGuid(),
                            MergeInputBillId = request.MergeInputBillId,
                            MergeInputBillDetailId = detail.MergeInputBillDetailId, // 合并进项发票明细ID
                            ProductName = detail.ProductName,
                            ProductId = detail.ProductId, // 产品名称ID可为空
                            ProductNo = detail.ProductNo,
                            ProductNameId = detail.ProductNameId, // 货号ID可为空
                            BussinessItemCode = detail.BusinessItemCode ?? detail.BusinessCode, // 优先使用BusinessItemCode
                            BussinessDate = detail.BusinessDate,
                            Quantity = detail.AvailableQuantity,
                            ReceivedNumber = detail.InvoicedQuantity, // 使用接口返回的已匹配数量
                            MatchQuantity = matchQuantity, // 使用处理后的匹配数量
                            MatchPrecision = detail.MatchPrecision, // 匹配维度
                            TaxCost = taxCost, // 使用处理后的含税单价
                            NoTaxCost = noTaxCost, // 使用处理后的不含税单价
                            // 1.2 taxcost 对应的是含税单价，taxamount对应的是税额=taxcost*matchquantity-notaxcost*matchquantity
                            // 对于新记录，税额计算使用本次匹配数量（CurrentMatchQuantity），因为新记录的MatchQuantity就是CurrentMatchQuantity
                            TaxAmount = (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition) ?
                                // 购货修订、服务费和损失确认：税额 = 含税金额 - 不含税金额，不含税金额 = 含税金额 / (1 + 税率/100)
                                Math.Round(taxCost.Value - taxCost.Value / (1 + detail.TaxRate / 100), 4) :
                                // 其他业务类型：税额 = 含税金额 - 不含税金额
                                Math.Round(detail.TaxCost * matchQuantity - detail.NoTaxCost * matchQuantity, 4),
                            // 注意：notaxcost 对应的是不含税单价，notaxamount对应的是含税金额=taxcost*matchquantity，在前端计算
                            TaxRate = detail.TaxRate, // 使用匹配明细中的税率
                            BusinessType = (int)businessTypeEnum,
                            ProducerOrderNo = detail.ProducerOrderNo, // 厂家订单号
                            // 只有经销入库和经销调出业务类型才需要给采购单号赋值，其它接口采购单号默认为空
                            PurchaseOrderCode = (businessTypeEnum == BusinessType.DistributionPurchase || businessTypeEnum == BusinessType.DistributionTransfer)
                                ? detail.PurchaseOrderCode
                                : null, // 采购订单号
                            ContractNo = null, // 合同编号可为空
                            Specification = detail.Specification,
                            Model = detail.Model,
                            ProducerId = detail.ProducerId, // 只使用厂家ID，不使用品名ID作为替代
                            ProducerName = detail.ProducerName, // 只使用厂家名称，不使用品名作为替代
                            MatchKey = matchKey, // 设置匹配键
                            CreatedBy = CurrentUser.UserName ?? "System",
                            CreatedTime = DateTimeOffset.Now,
                            // 计算不含税金额和含税金额
                            NoTaxAmount = (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition) ?
                                // 购货修订、服务费和损失确认：不含税金额 = 含税金额 / (1 + 税率/100)（不做四舍五入处理）
                                taxCost.Value / (1 + detail.TaxRate / 100) :
                                // 其他业务类型：不含税金额 = 不含税单价 * 匹配数量（不做四舍五入处理）
                                detail.NoTaxCost * matchQuantity,
                            TotalAmount = (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition) ?
                                // 购货修订、服务费和损失确认：含税金额就是用户填写的金额
                                taxCost.Value :
                                // 其他业务类型：含税金额 = 含税单价 * 匹配数量（不做四舍五入处理）
                                detail.TaxCost * matchQuantity,
                            // 匹配金额
                            MatchedAmount = (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition) ?
                                // 购货修订、服务费和损失确认：匹配金额就是用户填写的金额
                                taxCost.Value :
                                // 其他业务类型：匹配金额 = 含税单价 * 匹配数量（不做四舍五入处理）
                                detail.TaxCost * matchQuantity
                        };

                        newSubmitDetails.Add(newDetail);
                    }
                }

                // 添加新的提交明细
                if (newSubmitDetails.Count > 0)
                {
                    _logger.LogInformation("SaveMatch - 添加新的提交明细, 数量: {Count}", newSubmitDetails.Count);
                    await _db.MergeInputBillSubmitDetails.AddRangeAsync(newSubmitDetails);
                }

                // 更新现有的提交明细
                if (updateSubmitDetails.Count > 0)
                {
                    _logger.LogInformation("SaveMatch - 更新现有提交明细, 数量: {Count}", updateSubmitDetails.Count);
                    _db.MergeInputBillSubmitDetails.UpdateRange(updateSubmitDetails);
                }

                // 按业务类型分组处理缓存更新，并按业务类型排序（服务费排到最后）
                var businessTypeGroups = request.MatchDetails.GroupBy(x => x.BusinessType)
                    .OrderBy(g => g.Key == BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key);
                foreach (var group in businessTypeGroups)
                {
                    var businessType = group.Key;
                    var matchDetails = group.ToList();

                    // 获取当前业务类型的缓存数据
                    var cacheData = _cacheManager.GetBusinessTypeCache(request.MergeInputBillId, businessType);

                    // 处理每个匹配明细
                    foreach (var matchDetail in matchDetails)
                    {


                        // 只使用MatchKey查找匹配项，如果没有MatchKey则提示错误
                        if (string.IsNullOrEmpty(matchDetail.MatchKey))
                        {
                            _logger.LogWarning("SaveMatch - 匹配明细没有MatchKey, MergeInputBillDetailId: {MergeInputBillDetailId}", matchDetail.MergeInputBillDetailId);
                            throw new ArgumentException("匹配明细没有MatchKey，无法进行操作");
                        }

                        // 使用MatchKey查找缓存项
                        var cacheItem = cacheData.FirstOrDefault(x => !string.IsNullOrEmpty(x.MatchKey) && x.MatchKey == matchDetail.MatchKey);

                        // 更新缓存中的匹配数量
                        if (cacheItem != null)
                        {
                            // 获取业务类型
                            var businessTypeEnum = Enum.Parse<BusinessType>(matchDetail.BusinessType.ToString());

                            // 1. 购货修订和服务费：更新缓存中的taxcost值
                            if (businessTypeEnum == BusinessType.PurchaseRevision || businessTypeEnum == BusinessType.ServiceFeeProcurement || businessTypeEnum == BusinessType.LossRecognition)
                            {
                                // 获取匹配金额
                                decimal matchAmount = matchDetail.MatchedAmount;

                                // 获取当前缓存中的taxcost和noTaxCost值
                                var taxCostProperty = typeof(MatchableDocumentItem).GetProperty("TaxCost");
                                var noTaxCostProperty = typeof(MatchableDocumentItem).GetProperty("NoTaxCost");

                                // 更新含税单价
                                if (taxCostProperty != null)
                                {
                                    var currentTaxCost = (decimal?)taxCostProperty.GetValue(cacheItem);

                                    // 检查匹配金额绝对值是否大于缓存中taxcost绝对值
                                    if (Math.Abs(matchAmount) > Math.Abs(currentTaxCost ?? 0))
                                    {
                                        _logger.LogError("匹配金额绝对值大于可用金额绝对值: MatchKey={MatchKey}, 当前可用金额={CurrentTaxCost}, 匹配金额={MatchAmount}",
                                            matchDetail.MatchKey, currentTaxCost, matchAmount);
                                        throw new InvalidOperationException($"匹配金额不能大于可用金额, 当前可用金额: {currentTaxCost}, 匹配金额: {matchAmount}");
                                    }

                                    // 减去匹配金额，因为已经匹配了一部分
                                    decimal newTaxCost = (currentTaxCost ?? 0) - matchAmount;


                                    // 检查匹配金额是否会导致符号改变或超过可用金额
                                    if ((currentTaxCost > 0 && matchAmount < 0) ||
                                        (currentTaxCost < 0 && matchAmount > 0))
                                    {
                                        _logger.LogError("匹配金额超过可用金额或导致金额符号改变: MatchKey={MatchKey}, 当前可用金额={CurrentTaxCost}, 匹配金额={MatchAmount}, 计算结果={NewTaxCost}",
                                                                               matchDetail.MatchKey, currentTaxCost, matchAmount, newTaxCost);
                                        throw new InvalidOperationException($"匹配金额超过可用金额MatchKey: {matchDetail.MatchKey}, 当前可用金额: {currentTaxCost}, 匹配金额: {matchAmount}");
                                    }
                                    taxCostProperty.SetValue(cacheItem, newTaxCost);
                                    _logger.LogInformation("SaveMatch - 更新缓存中的taxcost值, MatchKey: {MatchKey}, 当前值: {CurrentTaxCost}, 减去: {MatchAmount}, 新值: {NewTaxCost}",
                                        matchDetail.MatchKey, currentTaxCost, matchAmount, newTaxCost);
                                }

                                // 更新不含税单价
                                if (noTaxCostProperty != null)
                                {
                                    var currentNoTaxCost = (decimal?)noTaxCostProperty.GetValue(cacheItem);
                                    decimal noTaxCost = Math.Round(matchAmount / (1 + matchDetail.TaxRate / 100), 10);
                                    decimal newNoTaxCost = (currentNoTaxCost ?? 0) - noTaxCost;
                                    noTaxCostProperty.SetValue(cacheItem, newNoTaxCost);
                                    _logger.LogInformation("SaveMatch - 更新缓存中的noTaxCost值, MatchKey: {MatchKey}, 当前值: {CurrentNoTaxCost}, 减去: {NoTaxCost}, 新值: {NewNoTaxCost}",
                                        matchDetail.MatchKey, currentNoTaxCost, noTaxCost, newNoTaxCost);
                                }

                                // 购货修订和服务费匹配数量固定为1
                                cacheItem.UpdateMatchQuantity(1);
                            }
                            // 3. 其他所有业务类型（包括经销调出和换货转退货）：更新匹配数量
                            else
                            {
                                // 获取当前匹配数量，对于经销调出和换货转退货取绝对值
                                decimal currentMatchQuantity = (businessTypeEnum == BusinessType.DistributionTransfer ||
                                                              businessTypeEnum == BusinessType.ExchangeToReturn)
                                                              ? Math.Abs(matchDetail.CurrentMatchQuantity)
                                                              : matchDetail.CurrentMatchQuantity;
                                if (currentMatchQuantity > cacheItem.RemainingQuantity)
                                {
                                    throw new InvalidOperationException($"匹配数量超过可用数量MatchKey: {matchDetail.MatchKey}, 当前可用数量: {cacheItem.RemainingQuantity}, 勾稽数量: {currentMatchQuantity}");
                                }
                                // 累加本次匹配数量到已有的匹配数量上
                                decimal newMatchQuantity = cacheItem.MatchQuantity + currentMatchQuantity;
                                cacheItem.UpdateMatchQuantity(newMatchQuantity);

                                _logger.LogInformation("SaveMatch - 更新缓存中的匹配数量, 业务类型: {BusinessType}, MatchKey: {MatchKey}, 原匹配数量: {OldMatchQuantity}, 新增: {CurrentMatchQuantity}, 新匹配数量: {NewMatchQuantity}",
                                    businessTypeEnum, matchDetail.MatchKey, cacheItem.MatchQuantity - currentMatchQuantity, currentMatchQuantity, newMatchQuantity);
                            }

                            // 保存完成后，重置本次匹配数量为0，为下次匹配做准备
                            cacheItem.UpdateCurrentMatchQuantity(0);
                        }
                        else
                        {
                            throw new ApplicationException("接口拉取数据有误，请重新匹配");
                        }
                    }

                    // 批量更新缓存
                    _cacheManager.SetBusinessTypeCache(request.MergeInputBillId, businessType, cacheData);
                }

                // 保存所有更改
                await _db.SaveChangesAsync();
                _logger.LogInformation("SaveMatch - 保存勾稽结果成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SaveMatch - 保存勾稽结果失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 删除匹配明细
        /// </summary>
        /// <param name="request">删除匹配明细请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> DeleteMatch(DeleteMatchRequest request)
        {
            _logger.LogInformation("DeleteMatch - 开始删除匹配明细, MergeInputBillId: {MergeInputBillId}, MatchDetailIds数量: {Count}",
                request.MergeInputBillId, request.MatchDetailIds?.Count ?? 0);

            try
            {
                // 检查是否可以编辑
                await CheckEditableAsync(request.MergeInputBillId);

                // 检查单据状态是否为匹配完成
                var mergeInputBill = await CheckStatusAsync(request.MergeInputBillId, MergeInputBillStatusEnum.CompletedReconciliation, "删除明细");

                // 检查是否为创建人本人
                if (mergeInputBill != null && !string.IsNullOrEmpty(mergeInputBill.CreatedBy) &&
                    !string.IsNullOrEmpty(CurrentUser.UserName) &&
                    mergeInputBill.CreatedBy != CurrentUser.UserName)
                {
                    _logger.LogWarning("DeleteMatch - 非创建人不能编辑, 创建人: {CreatedBy}, 当前用户: {CurrentUser}",
                        mergeInputBill.CreatedBy, CurrentUser.UserName);
                    throw new ArgumentException("只有创建人本人才能编辑开票明细");
                }

                // 如果没有指定要删除的明细ID，则返回错误
                if (request.MatchDetailIds == null || request.MatchDetailIds.Count == 0)
                {
                    _logger.LogWarning("DeleteMatch - 未指定要删除的匹配明细ID");
                    throw new ArgumentException("请指定要删除的匹配明细ID");
                }

                // 查询匹配明细，包括所有需要的字段
                var matchDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => request.MatchDetailIds.Contains(x.Id) && x.MergeInputBillId == request.MergeInputBillId)
                    .Select(x => new
                    {
                        x.Id,
                        x.MergeInputBillId,
                        x.MergeInputBillDetailId,
                        x.MatchKey,
                        x.MatchQuantity,
                        x.BusinessType,
                        x.TaxCost,
                        x.NoTaxCost,
                        x.TaxRate,
                        // 其他需要的字段
                    })
                    .ToListAsync();

                // 转换为MergeInputBillSubmitDetailPo列表
                var matchDetailsList = matchDetails.Select(x => new MergeInputBillSubmitDetailPo
                {
                    Id = x.Id,
                    MergeInputBillId = x.MergeInputBillId,
                    MergeInputBillDetailId = x.MergeInputBillDetailId,
                    MatchKey = x.MatchKey,
                    MatchQuantity = x.MatchQuantity,
                    BusinessType = x.BusinessType,
                    TaxCost = x.TaxCost,
                    NoTaxCost = x.NoTaxCost,
                    TaxRate = x.TaxRate,
                    // 其他需要的字段
                }).ToList();

                if (matchDetailsList == null || matchDetailsList.Count == 0)
                {
                    _logger.LogWarning("DeleteMatch - 匹配明细不存在, MatchDetailIds: {MatchDetailIds}", string.Join(", ", request.MatchDetailIds));
                    throw new ArgumentException("匹配明细不存在");
                }

                _logger.LogInformation("DeleteMatch - 找到匹配明细, 数量: {Count}", matchDetailsList.Count);

                // 收集所有需要更新的MatchKey和对应的业务类型
                var matchKeyBusinessTypes = matchDetailsList
                    .Where(x => !string.IsNullOrEmpty(x.MatchKey))
                    .Select(x => new { MatchKey = x.MatchKey, BusinessType = (BusinessType)(x.BusinessType ?? 0) })
                    .Distinct()
                    .ToList();

                // 删除匹配明细
                _db.MergeInputBillSubmitDetails.RemoveRange(matchDetailsList);
                await _db.SaveChangesAsync();

                // 按业务类型分组处理缓存更新，并按业务类型排序（服务费排到最后）
                var businessTypeGroups = matchKeyBusinessTypes.GroupBy(x => x.BusinessType)
                    .OrderBy(g => g.Key == BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key);

                // 处理每个业务类型的缓存
                foreach (var group in businessTypeGroups)
                {
                    var businessType = group.Key;
                    var matchKeysInGroup = group.Select(x => x.MatchKey).ToList();

                    // 从缓存中获取对应的业务类型数据
                    var cacheData = _cacheManager.GetBusinessTypeCache(request.MergeInputBillId, businessType);

                    // 找到所有匹配的缓存项
                    var matchedCacheItems = cacheData
                        .Where(x => !string.IsNullOrEmpty(x.MatchKey) && matchKeysInGroup.Contains(x.MatchKey))
                        .ToList();

                    // 更新缓存项的MatchQuantity
                    foreach (var cacheItem in matchedCacheItems)
                    {
                        // 计算所有相同MatchKey和BusinessType的匹配明细的总数量
                        var sumMatchQuantity = matchDetailsList
                            .Where(x => x.MatchKey == cacheItem.MatchKey && x.BusinessType == (int)businessType)
                            .Sum(x => x.MatchQuantity);

                        if (sumMatchQuantity != 0)
                        {
                            // 1. 购货修订和服务费：更新缓存中的taxcost值
                            if (businessType == BusinessType.PurchaseRevision || businessType == BusinessType.ServiceFeeProcurement || businessType == BusinessType.LossRecognition)
                            {
                                // 计算所有明细的taxcost和noTaxCost总和
                                var totalOriginalTaxCost = matchDetailsList
                                    .Where(x => x.MatchKey == cacheItem.MatchKey && x.BusinessType == (int)businessType)
                                    .Sum(x => x.TaxCost ?? 0);
                                var totalOriginalNoTaxCost = matchDetailsList
                                    .Where(x => x.MatchKey == cacheItem.MatchKey && x.BusinessType == (int)businessType)
                                    .Sum(x => x.NoTaxCost ?? 0);

                                // 获取当前缓存中的taxcost和noTaxCost值
                                var taxCostProperty = typeof(MatchableDocumentItem).GetProperty("TaxCost");
                                var noTaxCostProperty = typeof(MatchableDocumentItem).GetProperty("NoTaxCost");

                                // 更新含税单价
                                if (taxCostProperty != null)
                                {
                                    var currentTaxCost = (decimal?)taxCostProperty.GetValue(cacheItem);
                                    // 加回匹配金额，因为是删除操作
                                    decimal newTaxCost = (currentTaxCost ?? 0) + totalOriginalTaxCost;
                                    taxCostProperty.SetValue(cacheItem, newTaxCost);
                                    _logger.LogInformation("DeleteMatch - 更新缓存中的taxcost值, MatchKey: {MatchKey}, 当前值: {CurrentTaxCost}, 加回总金额: {TotalOriginalTaxCost}, 新值: {NewTaxCost}",
                                        cacheItem.MatchKey, currentTaxCost, totalOriginalTaxCost, newTaxCost);
                                }

                                // 更新不含税单价
                                if (noTaxCostProperty != null)
                                {
                                    var currentNoTaxCost = (decimal?)noTaxCostProperty.GetValue(cacheItem);
                                    decimal newNoTaxCost = (currentNoTaxCost ?? 0) + totalOriginalNoTaxCost;
                                    noTaxCostProperty.SetValue(cacheItem, newNoTaxCost);
                                    _logger.LogInformation("DeleteMatch - 更新缓存中的noTaxCost值, MatchKey: {MatchKey}, 当前值: {CurrentNoTaxCost}, 加回总金额: {TotalOriginalNoTaxCost}, 新值: {NewNoTaxCost}",
                                        cacheItem.MatchKey, currentNoTaxCost, totalOriginalNoTaxCost, newNoTaxCost);
                                }

                                // 购货修订和服务费匹配数量固定为1
                                cacheItem.UpdateMatchQuantity(1);
                            }
                            // 3. 其他所有业务类型（包括经销调出和换货转退货）：更新匹配数量
                            else
                            {
                                // 获取要恢复的匹配数量，对于经销调出和换货转退货取绝对值
                                decimal restoreQuantity = (businessType == BusinessType.DistributionTransfer ||
                                                         businessType == BusinessType.ExchangeToReturn)
                                                         ? Math.Abs(sumMatchQuantity)
                                                         : sumMatchQuantity;

                                decimal newMatchQuantity = cacheItem.MatchQuantity - restoreQuantity;
                                // 检查匹配数量是否小于0，如果是则抛出异常
                                if (newMatchQuantity < 0)
                                {
                                    _logger.LogError("删除匹配明细错误，匹配数量小于0: MatchKey={MatchKey}, 原匹配数量={OldMatchQuantity}, 减去总数量={RestoreQuantity}, 计算结果={NewMatchQuantity}",
                                        cacheItem.MatchKey, cacheItem.MatchQuantity, restoreQuantity, newMatchQuantity);
                                    throw new InvalidOperationException($"删除匹配明细错误，匹配数量小于0。MatchKey: {cacheItem.MatchKey}, 原匹配数量: {cacheItem.MatchQuantity}, 减去总数量: {restoreQuantity}");
                                }
                                cacheItem.UpdateMatchQuantity(newMatchQuantity);
                                _logger.LogInformation("DeleteMatch - 普通业务类型, 更新缓存中的匹配数量, MatchKey: {MatchKey}, 原匹配数量: {OldMatchQuantity}, 减去总数量: {RestoreQuantity}, 新匹配数量: {NewMatchQuantity}",
                                    cacheItem.MatchKey, cacheItem.MatchQuantity, restoreQuantity, newMatchQuantity);
                            }
                        }

                        // 重置本次匹配数量
                        cacheItem.UpdateCurrentMatchQuantity(0);
                    }

                    // 更新缓存
                    if (matchedCacheItems.Any())
                    {
                        _cacheManager.SetBusinessTypeCache(request.MergeInputBillId, businessType, cacheData);
                    }
                }

                _logger.LogInformation("DeleteMatch - 删除匹配明细成功, 删除数量: {Count}", matchDetailsList.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DeleteMatch - 删除匹配明细失败, 错误: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取缓存中的明细数据
        /// </summary>
        /// <param name="request">获取缓存中的明细数据请求</param>
        /// <returns>缓存中的明细数据</returns>
        public async Task<GetCacheDetailsResponse> GetCacheDetails(GetCacheDetailsRequest request)
        {
            _logger.LogInformation("GetCacheDetails - 开始获取缓存中的明细数据, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
            var allBusinessTypes = Enum.GetValues(typeof(BusinessType)).Cast<BusinessType>();
            try
            {
                // 使用新的QueryBusinessTypeCache方法获取符合条件的缓存数据
                List<MatchableDocumentItem> filteredItems = _cacheManager.QueryBusinessTypeCache(request.ToQueryCacheParams());

                // 如果缓存中没有数据，并且指定了业务类型，则尝试从外部接口获取数据
                if (filteredItems.Count == 0 && request.BusinessType.HasValue)
                {
                    // 从缓存中获取查询条件
                    var queryCondition = _cacheManager.GetQueryConditionFromCache(request.MergeInputBillId);

                    // 检查是否已经请求过该业务类型的接口数据
                    bool hasRequestedInterface = false;

                    if (queryCondition != null &&
                        queryCondition.InterfaceUpdateStatus != null &&
                        queryCondition.InterfaceUpdateStatus.TryGetValue(request.BusinessType.Value, out bool status))
                    {
                        hasRequestedInterface = status;
                    }

                    // 如果已经请求过接口数据，但仍然没有数据，则返回包含所有业务类型的空集合
                    if (hasRequestedInterface)
                    {
                        _logger.LogInformation("GetCacheDetails - 缓存中没有数据，但已经请求过接口数据，不再重复请求, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                            request.MergeInputBillId, request.BusinessType.Value);

                        // 获取匹配状态缓存
                        var cachedMatchStatus = _cacheManager.GetMatchStatusCache(request.MergeInputBillId);

                        // 创建包含所有业务类型的空集合
                        var emptyDetails = new Dictionary<BusinessType, List<MatchableDocumentItem>>();

                        foreach (var businessType in allBusinessTypes)
                        {
                            emptyDetails[businessType] = new List<MatchableDocumentItem>();
                        }

                        // 返回包含所有业务类型的空集合
                        return new GetCacheDetailsResponse
                        {
                            CacheDetails = emptyDetails,
                            MatchStatus = cachedMatchStatus,
                            Total = 0,
                            Page = request.Page,
                            Limit = request.Limit
                        };
                    }

                    // 如果没有请求过接口数据，才尝试从外部接口获取数据
                    _logger.LogInformation("GetCacheDetails - 缓存中没有数据，且未请求过接口数据，尝试从外部接口获取数据, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                        request.MergeInputBillId, request.BusinessType.Value);

                    // 如果缓存中有查询条件，记录日志
                    if (queryCondition != null)
                    {
                        _logger.LogInformation("GetCacheDetails - 从缓存中获取到查询条件, MergeInputBillId: {MergeInputBillId}, CompanyId: {CompanyId}, AgentId: {AgentId}",
                            request.MergeInputBillId, queryCondition.CompanyId, queryCondition.AgentId);
                    }
                    // 如果缓存中没有查询条件，则提示用户查询条件有误，请重新匹配
                    else
                    {
                        _logger.LogWarning("GetCacheDetails - 缓存中没有查询条件, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                        throw new InvalidOperationException("查询条件有误，请重新匹配");
                    }

                    // 使用查询条件调用外部接口获取数据
                    try
                    {
                        // 确保使用正确的公司和供应商条件
                        if (queryCondition != null)
                        {
                            _logger.LogInformation("GetCacheDetails - 使用缓存中的查询条件, MergeInputBillId: {MergeInputBillId}, CompanyId: {CompanyId}, AgentId: {AgentId}",
                                request.MergeInputBillId, queryCondition.CompanyId, queryCondition.AgentId);

                            // 调用MatchLogicService的FetchBusinessTypeData方法获取数据
                            var businessTypeData = await _matchLogicService.FetchBusinessTypeDataAsync(queryCondition, request.BusinessType.Value);

                            // 将获取的数据保存到缓存中
                            if (businessTypeData.Count > 0)
                            {
                                _logger.LogInformation("GetCacheDetails - 成功从外部接口获取数据, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}, 数据数量: {Count}",
                                    request.MergeInputBillId, request.BusinessType.Value, businessTypeData.Count);

                                // 保存到缓存
                                _cacheManager.SetBusinessTypeCache(request.MergeInputBillId, request.BusinessType.Value, businessTypeData);

                                // 更新接口更新状态
                                if (queryCondition.InterfaceUpdateStatus == null)
                                {
                                    queryCondition.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                                }
                                queryCondition.InterfaceUpdateStatus[request.BusinessType.Value] = true;

                                // 保存更新后的查询条件到缓存
                                _cacheManager.SaveQueryConditionToCache(request.MergeInputBillId, queryCondition);

                                // 设置匹配状态缓存
                                _cacheManager.SetMatchStatusCache(request.MergeInputBillId, request.BusinessType.Value, true);

                                // 更新请求对象中的接口更新状态
                                if (request.InterfaceUpdateStatus == null)
                                {
                                    request.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                                }
                                request.InterfaceUpdateStatus[request.BusinessType.Value] = true;

                                _logger.LogInformation("GetCacheDetails - 已更新接口更新状态, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                                    request.MergeInputBillId, request.BusinessType.Value);

                                // 重新获取缓存数据
                                filteredItems = _cacheManager.QueryBusinessTypeCache(request.ToQueryCacheParams());
                            }
                            else
                            {
                                _logger.LogInformation("GetCacheDetails - 外部接口未返回数据, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}",
                                    request.MergeInputBillId, request.BusinessType.Value);

                                // 即使没有返回数据，也标记为已请求过接口
                                if (queryCondition.InterfaceUpdateStatus == null)
                                {
                                    queryCondition.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                                }
                                queryCondition.InterfaceUpdateStatus[request.BusinessType.Value] = true;

                                // 保存更新后的查询条件到缓存
                                _cacheManager.SaveQueryConditionToCache(request.MergeInputBillId, queryCondition);

                                // 更新请求对象中的接口更新状态
                                if (request.InterfaceUpdateStatus == null)
                                {
                                    request.InterfaceUpdateStatus = new Dictionary<BusinessType, bool>();
                                }
                                request.InterfaceUpdateStatus[request.BusinessType.Value] = true;
                            }
                        }
                        else
                        {
                            _logger.LogWarning("GetCacheDetails - 无法获取查询条件, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                            throw new InvalidOperationException("查询条件异常，请重新匹配");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "GetCacheDetails - 从外部接口获取数据失败, MergeInputBillId: {MergeInputBillId}, BusinessType: {BusinessType}, 错误: {ErrorMessage}",
                            request.MergeInputBillId, request.BusinessType.Value, ex.Message);
                        // 继续处理，使用空列表
                    }
                }

                // 计算总数量
                int totalCount = filteredItems.Count;

                // 应用分页
                var pagedItems = filteredItems
                    .Skip((request.Page - 1) * request.Limit)
                    .Take(request.Limit)
                    .ToList();

                // 将分页后的数据按业务类型分组，并按业务类型排序（服务费排到最后）
                var pagedCacheDetails = new Dictionary<BusinessType, List<MatchableDocumentItem>>();
                foreach (var group in pagedItems.GroupBy(x => x.BusinessType)
                    .OrderBy(g => g.Key == BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key))
                {
                    // 初始化每个项目的 CurrentMatchQuantity 为 0
                    var itemsWithInitializedCurrentMatchQuantity = group.Select(x =>
                    {
                        // 初始化 CurrentMatchQuantity 为 0，确保每次返回给前端时都是初始状态
                        x.CurrentMatchQuantity = 0;
                        return x;
                    }).ToList();

                    pagedCacheDetails.Add(group.Key, itemsWithInitializedCurrentMatchQuantity);
                }

                // 获取匹配状态缓存
                var matchStatus = _cacheManager.GetMatchStatusCache(request.MergeInputBillId);

                // 确保所有业务类型都有对应的空数组

                foreach (var businessType in allBusinessTypes)
                {
                    if (!pagedCacheDetails.ContainsKey(businessType))
                    {
                        pagedCacheDetails[businessType] = new List<MatchableDocumentItem>();
                    }
                }

                var queryInput = request.ToQueryCacheParams();
                _logger.LogInformation("GetCacheDetails - 成功获取缓存中的明细数据, MergeInputBillId: {MergeInputBillId}, 总数量: {TotalCount}, 当前页: {Page}, 每页数量: {Limit}, 查询参数: {@QueryInput}",
                    request.MergeInputBillId, totalCount, request.Page, request.Limit, queryInput);

                return new GetCacheDetailsResponse
                {
                    CacheDetails = pagedCacheDetails,
                    MatchStatus = matchStatus,
                    Total = totalCount,
                    Page = request.Page,
                    Limit = request.Limit
                };
            }

            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCacheDetails - 获取缓存中的明细数据失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取待提交的明细
        /// </summary>
        /// <param name="request">获取待提交的明细请求</param>
        /// <returns>待提交的明细分页数据</returns>
        public async Task<PagedDataWithSummary<SubmitDetailItem>> GetSubmitDetails(GetSubmitDetailsRequest request)
        {
            _logger.LogInformation("GetSubmitDetails - 开始获取待提交的明细, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("GetSubmitDetails - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 查询待提交的明细
                var query = _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId);

                // 如果指定了合并进项发票明细ID，则根据合并进项发票明细ID进行过滤
                if (request.MergeInputBillDetailId.HasValue)
                {
                    query = query.Where(x => x.MergeInputBillDetailId == request.MergeInputBillDetailId);
                    _logger.LogInformation("GetSubmitDetails - 根据合并进项发票明细ID进行过滤, MergeInputBillDetailId: {MergeInputBillDetailId}", request.MergeInputBillDetailId);
                }

                // 如果指定了关键字，则进行模糊查询
                if (!string.IsNullOrWhiteSpace(request.Keyword))
                {
                    string keyword = request.Keyword.Trim();
                    query = query.Where(x =>
                        (x.ProductName != null && EF.Functions.Like(x.ProductName, $"%{keyword}%")) ||
                        (x.ProductNo != null && EF.Functions.Like(x.ProductNo, $"%{keyword}%")) ||
                        (x.Specification != null && EF.Functions.Like(x.Specification, $"%{keyword}%")) ||
                        (x.Model != null && EF.Functions.Like(x.Model, $"%{keyword}%")) ||
                        (x.BussinessItemCode != null && EF.Functions.Like(x.BussinessItemCode, $"%{keyword}%")) ||
                        (x.ProducerName != null && EF.Functions.Like(x.ProducerName, $"%{keyword}%")) ||
                        (x.PurchaseOrderCode != null && EF.Functions.Like(x.PurchaseOrderCode, $"%{keyword}%"))
                    );
                    _logger.LogInformation("GetSubmitDetails - 根据关键字进行模糊查询, Keyword: {Keyword}", keyword);
                }

                // 获取总记录数
                var total = await query.CountAsync();

                // 应用分页，按业务单号从小到大排序
                var submitDetails = await query
                    .OrderBy(x => x.BussinessItemCode)
                    .Skip((request.Page - 1) * request.Limit)
                    .Take(request.Limit)
                    .Select(x => new SubmitDetailItem
                    {
                        Id = x.Id,
                        MergeInputBillDetailId = x.MergeInputBillDetailId,
                        ProductName = x.ProductName,
                        ProductNo = x.ProductNo,
                        BusinessItemCode = x.BussinessItemCode,
                        BusinessCode = x.BussinessItemCode, // 为了兼容性，设置为相同的值
                        BusinessDate = FormatDateToYYYYMMDD(x.BussinessDate),
                        MatchQuantity = x.MatchQuantity,
                        CurrentMatchQuantity = 0, // 初始化本次匹配数量为0
                        MatchPrecision = x.MatchPrecision,
                        TaxCost = x.TaxCost,
                        NoTaxCost = x.NoTaxCost,
                        TaxAmount = x.TaxAmount,
                        Specification = x.Specification,
                        Model = x.Model,
                        TaxRate = x.TaxRate,
                        BusinessType = x.BusinessType,
                        CreatedTime = ConvertToBeijingTime(x.CreatedTime.UtcDateTime),
                        ProducerName = x.ProducerName,
                        ProducerId = x.ProducerId,
                        MatchKey = x.MatchKey,
                        ProducerOrderNo = x.ProducerOrderNo,
                        PurchaseOrderCode = x.PurchaseOrderCode,
                        NoTaxAmount = x.NoTaxAmount,
                        TotalAmount = x.TotalAmount,
                        MatchedAmount = x.MatchedAmount
                    })
                    .ToListAsync();

                // 计算所有数据的合计（不仅仅是当前页的数据）
                var allSubmitDetails = await query
                    .Select(x => new
                    {
                        x.Quantity,
                        x.MatchQuantity,
                        x.NoTaxCost,
                        x.TaxAmount,
                        x.TaxCost,
                        NoTaxAmount = x.NoTaxAmount,
                        TotalAmount = x.TotalAmount,
                        MatchedAmount = x.MatchedAmount
                    })
                    .ToListAsync();

                // 计算合计数据
                var summary = new SummaryData
                {
                    TotalQuantity = allSubmitDetails.Sum(x => x.Quantity ?? 0),
                    TotalMatchQuantity = allSubmitDetails.Sum(x => x.MatchQuantity),
                    // 直接使用计算好的字段，保留4位小数
                    TotalNoTaxAmount = Math.Round(allSubmitDetails.Sum(x => x.NoTaxAmount ?? 0), 4),
                    TotalAmount = Math.Round(allSubmitDetails.Sum(x => x.TaxCost * x.MatchQuantity ?? 0), 4),
                    TotalTaxAmount = Math.Round(allSubmitDetails.Sum(x => x.TaxAmount ?? 0), 4),
                    TotalMatchedAmount = Math.Round(allSubmitDetails.Sum(x => x.MatchedAmount ?? 0), 4)
                };

                _logger.LogInformation("GetSubmitDetails - 成功获取待提交的明细, MergeInputBillId: {MergeInputBillId}, 总数量: {Total}, 当前页: {Page}, 每页数量: {Limit}, " +
                    "合计: 数量: {TotalQuantity}, 匹配数量: {TotalMatchQuantity}, 不含税金额: {TotalNoTaxAmount}, 含税金额: {TotalAmount}, 税额: {TotalTaxAmount}, 匹配金额: {TotalMatchedAmount}",
                    request.MergeInputBillId, total, request.Page, request.Limit,
                    summary.TotalQuantity, summary.TotalMatchQuantity, summary.TotalNoTaxAmount, summary.TotalAmount, summary.TotalTaxAmount, summary.TotalMatchedAmount);

                return new PagedDataWithSummary<SubmitDetailItem>
                {
                    List = submitDetails,
                    Total = total,
                    PageNum = request.Page, // 页码从1开始
                    PageSize = request.Limit,
                    TotalPage = (total + request.Limit - 1) / request.Limit, // 计算总页数
                    Summary = summary
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetSubmitDetails - 获取待提交的明细失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取合并进项发票明细
        /// </summary>
        /// <param name="request">获取合并进项发票明细请求</param>
        /// <returns>合并进项发票明细分页数据</returns>
        public async Task<PagedDataWithSummary<MergeInputBillDetailItem>> GetMergeInputBillDetail(GetMatchedDetailsRequest request)
        {
            _logger.LogInformation("GetMergeInputBillDetail - 开始获取合并进项发票明细, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("GetMergeInputBillDetail - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 查询合并进项发票明细
                var query = _db.MergeInputBillDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId);

                // 获取总记录数
                var total = await query.CountAsync();

                // 应用分页
                var mergeInputBillDetails = await query
                    .OrderBy(x => x.CreatedTime)
                    .Skip((request.Page - 1) * request.Limit)
                    .Take(request.Limit)
                    .Select(x => new MergeInputBillDetailItem
                    {
                        Id = x.Id,
                        ProductName = x.ProductName ?? string.Empty,
                        ProductNo = x.ProductNo ?? string.Empty,
                        Specification = x.Specification ?? string.Empty,
                        Model = x.Model ?? string.Empty,
                        Quantity = x.Quantity,
                        TaxCost = x.TaxCost,
                        NoTaxCost = x.NoTaxCost,
                        NoTaxAmount = x.NoTaxAmount,
                        TaxRate = x.TaxRate,
                        TaxAmount = x.TaxAmount,
                        TotalAmount = x.TotalAmount,
                        MergeInputBillId = x.MergeInputBillId,
                        PurchaseDutyNumber = mergeInputBill.PurchaseDutyNumber,
                        SaleDutyNumber = mergeInputBill.SaleDutyNumber
                    })
                    .ToListAsync();

                // 查询提交明细，用于计算匹配成功金额
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                // 创建产品名称和货号的组合键
                var productDetailKeys = mergeInputBillDetails
                    .Select(x => new
                    {
                        ProductName = x.ProductName,
                        ProductNo = x.ProductNo ?? string.Empty
                    })
                    .Distinct()
                    .ToList();

                // 查询合并进项发票与原始进项发票的关系
                var relations = await _db.MergeInputBillRelations
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .Select(x => x.InputBillId)
                    .ToListAsync();

                // 获取所有产品名称（用于查询条件）
                var productNames = productDetailKeys.Select(x => x.ProductName).Distinct().ToList();

                // 一次性查询所有相关的原始发票明细
                var inputBillDetails = await _db.InputBillDetails
                    .Where(x => relations.Contains(x.InputBillId) && productNames.Contains(x.ProductName))
                    .Select(x => new
                    {
                        x.InputBillId,
                        x.ProductName,
                        x.ProductNo
                    })
                    .ToListAsync();

                // 一次性查询所有相关的原始发票
                var inputBills = await _db.InputBills
                    .Where(x => relations.Contains(x.Id))
                    .Select(x => new { x.Id, x.InvoiceNumber })
                    .ToListAsync();

                // 创建产品名称+货号到发票号的映射
                var productDetailToInvoiceNumbers = new Dictionary<string, List<string>>();
                foreach (var key in productDetailKeys)
                {
                    // 创建组合键
                    string combinedKey = $"{key.ProductName}|{key.ProductNo}";

                    // 找到包含该产品名称和货号的原始发票ID
                    var inputBillIds = inputBillDetails
                        .Where(x => x.ProductName == key.ProductName && (x.ProductNo ?? string.Empty) == key.ProductNo)
                        .Select(x => x.InputBillId)
                        .Distinct()
                        .ToList();

                    // 找到对应的发票号
                    var invoiceNumbers = inputBills
                        .Where(x => inputBillIds.Contains(x.Id))
                        .Select(x => x.InvoiceNumber)
                        .Where(x => !string.IsNullOrEmpty(x))
                        .Distinct()
                        .OrderBy(x => x)
                        .ToList();

                    productDetailToInvoiceNumbers[combinedKey] = invoiceNumbers;
                }

                // 为每个明细项填充发票号列表和匹配成功金额
                foreach (var detail in mergeInputBillDetails)
                {
                    // 创建组合键
                    string combinedKey = $"{detail.ProductName}|{detail.ProductNo ?? string.Empty}";

                    // 设置对应发票号列表
                    detail.InvoiceNumbers = productDetailToInvoiceNumbers.TryGetValue(combinedKey, out var invoiceNumbers)
                        ? invoiceNumbers
                        : new List<string>();

                    // 设置匹配金额
                    detail.MatchedAmount = submitDetails
                        .Where(x => x.MergeInputBillDetailId == detail.Id)
                        .Sum(x => x.MatchedAmount) ?? 0;
                }

                // 计算所有数据的合计（不仅仅是当前页的数据）
                var allDetails = await query
                    .Select(x => new
                    {
                        x.Quantity,
                        x.NoTaxCost,
                        x.TaxCost,
                        x.TaxAmount,
                        x.NoTaxAmount,
                        x.TotalAmount
                    })
                    .ToListAsync();

                // 计算合计数据
                var summary = new SummaryData
                {
                    TotalQuantity = allDetails.Sum(x => x.Quantity),
                    TotalNoTaxAmount = Math.Round(allDetails.Sum(x => x.NoTaxAmount), 4),
                    TotalAmount = Math.Round(allDetails.Sum(x => x.TotalAmount ?? 0), 4),
                    TotalTaxAmount = Math.Round(allDetails.Sum(x => x.TaxAmount), 4)
                };

                _logger.LogInformation("GetMergeInputBillDetail - 成功获取合并进项发票明细, MergeInputBillId: {MergeInputBillId}, 总数量: {Total}, 当前页: {Page}, 每页数量: {Limit}, " +
                    "合计: 数量: {TotalQuantity}, 不含税金额: {TotalNoTaxAmount}, 含税金额: {TotalAmount}, 税额: {TotalTaxAmount}",
                    request.MergeInputBillId, total, request.Page, request.Limit,
                    summary.TotalQuantity, summary.TotalNoTaxAmount, summary.TotalAmount, summary.TotalTaxAmount);

                return new PagedDataWithSummary<MergeInputBillDetailItem>
                {
                    List = mergeInputBillDetails,
                    Total = total,
                    PageNum = request.Page, // 页码从1开始
                    PageSize = request.Limit,
                    TotalPage = (total + request.Limit - 1) / request.Limit, // 计算总页数
                    Summary = summary
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMergeInputBillDetail - 获取合并进项发票明细失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 取消勾稽结果
        /// </summary>
        /// <param name="request">取消勾稽结果请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> RevokeMatch(DTOs.MergeInputBills.RevokeMatchRequest request)
        {
            _logger.LogInformation("RevokeMatch - 开始取消勾稽结果, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                request.MergeInputBillId, request.MergeInvoiceNumber);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("RevokeMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 校验当前用户是否为创建者
                string currentUserName = CurrentUser.UserName ?? string.Empty;
                if (string.IsNullOrEmpty(currentUserName) || mergeInputBill.CreatedBy != currentUserName)
                {
                    _logger.LogWarning("RevokeMatch - 当前用户不是创建者，无法取消勾稽, 当前用户: {CurrentUser}, 创建者: {CreatedBy}",
                        currentUserName, mergeInputBill.CreatedBy);
                    throw new UnauthorizedAccessException("只有创建者才能取消勾稽");
                }

                // 创建取消匹配请求
                var revokeRequest = new Services.SubmitHandling.RevokeMatchRequest
                {
                    MergeInputBillId = request.MergeInputBillId
                };

                // 调用提交处理服务
                var result = await _submitHandlingService.RevokeMatch(revokeRequest);

                if (result.Code != CodeStatusEnum.Success)
                {
                    _logger.LogError("RevokeMatch - 取消勾稽结果失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, result.Message);
                    throw new Exception(result.Message);
                }

                _logger.LogInformation("RevokeMatch - 取消勾稽结果成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RevokeMatch - 取消勾稽结果失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 取消已提交勾稽
        /// </summary>
        /// <param name="request">取消已提交勾稽请求</param>
        /// <returns>操作结果</returns>
        public async Task<bool> CancelSubmittedMatch(DTOs.MergeInputBills.CancelSubmittedMatchRequest request)
        {
            _logger.LogInformation("CancelSubmittedMatch - 开始取消已提交勾稽, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                request.MergeInputBillId, request.MergeInvoiceNumber);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("CancelSubmittedMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 校验当前用户是否为创建者
                string currentUserName = CurrentUser.UserName ?? string.Empty;
                if (string.IsNullOrEmpty(currentUserName) || mergeInputBill.CreatedBy != currentUserName)
                {
                    _logger.LogWarning("CancelSubmittedMatch - 当前用户不是创建者，无法取消勾稽, 当前用户: {CurrentUser}, 创建者: {CreatedBy}",
                        currentUserName, mergeInputBill.CreatedBy);
                    throw new UnauthorizedAccessException("只有创建者才能取消勾稽");
                }

                // 创建取消已提交勾稽请求
                var cancelRequest = new Services.SubmitHandling.CancelSubmittedMatchRequest
                {
                    MergeInputBillId = request.MergeInputBillId,
                    MergeInvoiceNumber = request.MergeInvoiceNumber
                };

                // 调用提交处理服务
                var result = await _submitHandlingService.CancelSubmittedMatch(cancelRequest);

                if (result.Code != CodeStatusEnum.Success)
                {
                    _logger.LogError("CancelSubmittedMatch - 取消已提交勾稽失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, result.Message);
                    throw new Exception(result.Message);
                }

                _logger.LogInformation("CancelSubmittedMatch - 取消已提交勾稽成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CancelSubmittedMatch - 取消已提交勾稽失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取发票金额统计
        /// </summary>
        /// <param name="request">获取发票金额统计请求</param>
        /// <returns>发票金额统计响应</returns>
        public async Task<GetInvoiceAmountStatisticsResponse> GetInvoiceAmountStatistics(GetInvoiceAmountStatisticsRequest request)
        {
            _logger.LogInformation("GetInvoiceAmountStatistics - 开始获取发票金额统计, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("GetInvoiceAmountStatistics - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    throw new ArgumentException("合并进项发票不存在");
                }

                // 直接使用单头的amount作为发票总金额
                decimal totalInvoiceAmount = mergeInputBill.Amount;

                // 查询待提交发票金额
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                // 计算待提交的含税总金额 (TaxCost * MatchQuantity)，保留两位小数
                decimal totalSubmitAmount = 0;
                foreach (var detail in submitDetails)
                {
                    if (detail.TaxCost.HasValue)
                    {
                        // 注意：这里使用 MatchQuantity（累计匹配数量），而不是 CurrentMatchQuantity（本次匹配数量）
                        totalSubmitAmount += detail.TaxCost.Value * detail.MatchQuantity;
                    }
                }
                totalSubmitAmount = Math.Round(totalSubmitAmount, 4);
                // 计算差值
                var differenceAmount = totalInvoiceAmount - totalSubmitAmount;

                _logger.LogInformation("GetInvoiceAmountStatistics - 获取发票金额统计成功, 发票总金额(单头Amount): {TotalInvoiceAmount}, 待提交含税总金额(TaxCost*MatchQuantity): {TotalSubmitAmount}, 差值: {DifferenceAmount}",
                    totalInvoiceAmount, totalSubmitAmount, differenceAmount);

                return new GetInvoiceAmountStatisticsResponse
                {
                    TotalInvoiceAmount = totalInvoiceAmount,
                    TotalSubmitAmount = totalSubmitAmount,
                    DifferenceAmount = differenceAmount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetInvoiceAmountStatistics - 获取发票金额统计失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 将UTC时间转换为北京时间（China Standard Time, UTC+8）
        /// </summary>
        /// <param name="utcTime">UTC时间</param>
        /// <returns>北京时间</returns>
        private static DateTime ConvertToBeijingTime(DateTime utcTime)
        {
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"));
        }

        /// <summary>
        /// 格式化日期为YYYY-MM-DD格式
        /// </summary>
        /// <param name="date">日期</param>
        /// <returns>格式化后的日期字符串</returns>
        private static string FormatDateToYYYYMMDD(DateTime? date)
        {
            return date?.ToString("yyyy-MM-dd") ?? string.Empty;
        }

        /// <summary>
        /// 获取匹配查询条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>匹配查询条件</returns>
        public async Task<GetMatchableDocumentsRequest> GetMatchQueryCondition(Guid mergeInputBillId)
        {
            _logger.LogInformation("GetMatchQueryCondition - 开始获取匹配查询条件, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);

            try
            {
                // 从缓存中获取查询条件
                var queryCondition = _cacheManager.GetQueryConditionFromCache(mergeInputBillId);

                // 如果缓存中没有查询条件，则创建一个默认的查询条件
                if (queryCondition == null)
                {
                    _logger.LogInformation("GetMatchQueryCondition - 缓存中没有查询条件，创建默认查询条件, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);

                    // 查询合并进项发票
                    var mergeInputBill = await _db.MergeInputBills
                        .Include(x => x.MergeInputBillDetail)
                        .Where(x => x.Id == mergeInputBillId)
                        .FirstOrDefaultAsync();

                    if (mergeInputBill == null)
                    {
                        _logger.LogWarning("GetMatchQueryCondition - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                        throw new ArgumentException("合并进项发票不存在");
                    }

                    // 创建默认查询条件
                    queryCondition = new GetMatchableDocumentsRequest
                    {
                        MergeInputBillId = mergeInputBillId,
                        CompanyId = mergeInputBill.CompanyId,
                        AgentId = mergeInputBill.AgentId,
                        StartDate = DateTime.Now.AddMonths(-3),
                        EndDate = DateTime.Now,
                        MatchPrecision = MatchPrecisionEnum.Auto
                    };

                    // 如果有明细，则添加到查询条件中
                    if (mergeInputBill.MergeInputBillDetail != null && mergeInputBill.MergeInputBillDetail.Any())
                    {
                        queryCondition.InvoiceDetails = mergeInputBill.MergeInputBillDetail.Select(x => new InvoiceDetailItem
                        {
                            Id = x.Id,
                            InvoiceName = x.ProductName,
                            ProductName = x.ProductName,
                            ProductNo = x.ProductNo,
                            Specification = x.ProductNo,
                            Quantity = x.Quantity,
                            TaxCost = x.TaxCost,
                            TaxRate = x.TaxRate
                        }).ToList();
                    }
                }
                else
                {
                    _logger.LogInformation("GetMatchQueryCondition - 从缓存中获取到查询条件, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                }

                return queryCondition;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetMatchQueryCondition - 获取匹配查询条件失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    mergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取缓存中的匹配条件
        /// </summary>
        /// <param name="request">获取缓存中的匹配条件请求</param>
        /// <returns>缓存中的匹配条件</returns>
        public async Task<GetCachedMatchConditionResponse> GetCachedMatchCondition(GetCachedMatchConditionRequest request)
        {
            _logger.LogInformation("GetCachedMatchCondition - 开始获取缓存中的匹配条件, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            try
            {
                // 从缓存中获取查询条件
                var queryCondition = _cacheManager.GetQueryConditionFromCache(request.MergeInputBillId);

                // 从缓存中获取匹配状态
                var matchStatus = _cacheManager.GetMatchStatusCache(request.MergeInputBillId);

                // 构建响应
                var response = new GetCachedMatchConditionResponse
                {
                    MatchCondition = queryCondition ?? new GetMatchableDocumentsRequest(),
                    MatchStatus = matchStatus ?? new Dictionary<BusinessType, bool>()
                };

                if (queryCondition == null)
                {
                    _logger.LogInformation("GetCachedMatchCondition - 缓存中没有匹配条件, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                    // 查询合并进项发票
                    var mergeInputBill = await _db.MergeInputBills
                        .Where(x => x.Id == request.MergeInputBillId)
                        .FirstOrDefaultAsync();

                    if (mergeInputBill == null)
                    {
                        _logger.LogWarning("GetCachedMatchCondition - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                        throw new ArgumentException("合并进项发票不存在");
                    }
                }
                else
                {
                    _logger.LogInformation("GetCachedMatchCondition - 从缓存中获取到匹配条件, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                }

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetCachedMatchCondition - 获取缓存中的匹配条件失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                throw;
            }
        }
    }
}
