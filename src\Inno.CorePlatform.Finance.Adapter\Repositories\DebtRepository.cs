﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using NPOI.OpenXmlFormats;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class DebtRepository : EfBaseRepository<Guid, Debt, DebtPo>, IDebtRepository
    {
        private FinanceDbContext db;

        public DebtRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            db = dbContext;
        }

        public override async Task<int> UpdateAsync(Debt root)
        {
            var isExist = await db.Debts.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("应付不存在！");
            }

            var po = root.Adapt<DebtPo>();

            db.Debts.Update(po);

            if (UowJoined) return 0;

            return await db.SaveChangesAsync();
        }

        protected override DebtPo CreateDeletingPo(Guid id)
        {
            return new DebtPo { Id = id };
        }

        protected override Task<DebtPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }


        public async Task<int> UpdateManyAsync(List<Debt> lstDebt)
        {
            var lstPo = lstDebt.Adapt<List<DebtPo>>();

            db.Debts.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await db.SaveChangesAsync();
        }

        public async Task<Debt> GetWithNoTrackAsync(Guid id)
        {
            var po = await db.Debts.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<Debt>();
        }

        public async Task<bool> IsExistForBill(string relateCode)
        {
            return await db.Debts.AnyAsync(p => p.RelateCode == relateCode);
        }

        public async Task<int> InertManyAsync(List<Debt> debts)
        {
            var insertList = debts.Adapt<List<DebtPo>>();
            await db.Debts.AddRangeAsync(insertList);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }

        public async Task<bool> DeleteByIds(List<Guid> ids)
        {
            var debts = await db.Debts.Where(p => ids.Contains(p.Id)).ToListAsync();
            var details = await db.DebtDetails.Where(p => ids.Contains(p.DebtId.Value)).ToListAsync();
            var detailIds = details.Select(p => p.Id);
            var excuteDetails = await db.DebtDetailExcutes.Where(p => detailIds.Contains(p.DebtDetailId)).ToListAsync();
            if (excuteDetails.Any())
            {
                db.DebtDetailExcutes.RemoveRange(excuteDetails);
            }

            if (details.Any())
            {
                db.DebtDetails.RemoveRange(details);
            }

            if (debts.Any())
            {
                db.Debts.RemoveRange(debts);
            }

            await db.SaveChangesAsync();
            return true;
        }

        //public async Task<bool> RepaireDebtDiff(List<Guid> ids)
        //{
        //    var debtDetails = await db.DebtDetails.Include(p => p.Debt).Where(p => ids.Contains(p.DebtId.Value)).AsNoTracking().ToListAsync();
        //    var group = debtDetails.GroupBy(p => p.DebtId).ToList();
        //    var updateList = new List<DebtDetailPo>();
        //    foreach (var g in group)
        //    {
        //        if (!g.Any())
        //        {
        //            continue;
        //        }
        //        var detailAmount = g.Where(p => p.Value > 0).Sum(p => p.Value);
        //        var itemAmount = g.FirstOrDefault().Debt.Value;
        //        if (detailAmount != itemAmount)//如果明细金额加起来不等于应付金额
        //        {
        //            var debtDetailsTemps = g.Where(p => p.Status == Domain.DebtDetailStatusEnum.WaitExecute &&
        //                                         (!p.ProbablyPayTime.HasValue || string.IsNullOrEmpty(p.OrderNo)))
        //                                         .OrderBy(p => p.AccountPeriodType).ThenByDescending(p => p.Value)
        //                                         .ToList();
        //            if (debtDetailsTemps != null && debtDetailsTemps.Any())
        //            {
        //                var diff = detailAmount - itemAmount;
        //                var flag = false;
        //                foreach (var item in debtDetailsTemps)
        //                {
        //                    if (flag)
        //                    {
        //                        break;
        //                    }
        //                    if (diff < 0)
        //                    {
        //                        item.Value += Math.Abs(diff);
        //                        updateList.Add(item);
        //                        break;
        //                    }
        //                    if (diff > 0)
        //                    {
        //                        if (diff >= item.Value)
        //                        {
        //                            item.Value = 0;
        //                            diff = diff - item.Value;
        //                        }
        //                        else
        //                        {
        //                            item.Value = item.Value - diff;
        //                            flag = true;
        //                        }
        //                        updateList.Add(item);
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    if (updateList.Count() > 0)
        //    {
        //        var lessZore = updateList.Where(p => p.Value <= 0).ToList();
        //        var greatZore = updateList.Where(p => p.Value > 0).ToList();
        //        if (lessZore.Any())
        //        {
        //            db.DebtDetails.RemoveRange(lessZore);
        //        }
        //        if (greatZore.Any())
        //        {
        //            db.DebtDetails.UpdateRange(greatZore);
        //        }
        //        await db.SaveChangesAsync();
        //    }
        //    return true;
        //}

        public async Task<bool> RepaireDebtDiff(List<Guid> ids)
        {
            var debtDetails = await db.DebtDetails.Include(p => p.Debt).Where(p => ids.Contains(p.DebtId.Value))
                .ToListAsync();
            var group = debtDetails.GroupBy(p => p.DebtId).ToList();
            var updateList = new List<DebtDetailPo>();
            foreach (var g in group)
            {
                if (!g.Any())
                {
                    continue;
                }

                var detailAmount = g.Sum(p => p.Value);
                var itemAmount = g.FirstOrDefault().Debt.Value;
                if (detailAmount != itemAmount) //如果明细金额加起来不等于应付金额
                {
                    var updatePo = g.Where(p => p.Status == Domain.DebtDetailStatusEnum.WaitExecute)
                        .OrderBy(p => p.AccountPeriodType).FirstOrDefault();
                    if (updatePo != null)
                    {
                        var diff = detailAmount - itemAmount;
                        if (diff > 0)
                        {
                            updatePo.Value -= diff;
                        }
                        else
                        {
                            updatePo.Value += Math.Abs(diff);
                        }

                        if (updatePo.OriginValue.HasValue && updatePo.CostDiscount.HasValue &&
                            updatePo.OriginValue != 0 && updatePo.CostDiscount != 0)
                        {
                            updatePo.OriginValue = updatePo.Value / updatePo.CostDiscount*100;
                        }

                        if (updatePo.Value != 0)
                        {
                            updateList.Add(updatePo);
                        }
                    }
                }
            }

            db.DebtDetails.UpdateRange(updateList);
            await db.SaveChangesAsync();
            return true;
        }
    }
}