﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npoi.Mapper;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 服务费订单修订
    /// </summary>
    public class SellServiceFeeAppService : BaseAppService, ISellServiceFeeService
    {
        public IBDSApiClient _bDSApiClient;
        public ISellApiClient _sellApiClient;
        private IInventoryApiClient _inventoryApiClient;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IBaseAllQueryService<DebtDetailExcutePo> _debtDetailExcuteQueryService;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private IProjectMgntApiClient _projectMgntApiClient;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly FinanceDbContext _db;
        protected readonly DaprClient _daprClient;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        private readonly ICompanyDateService _companyDateService;

        public SellServiceFeeAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
            IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
            IBaseAllQueryService<DebtDetailExcutePo> debtDetailExcuteQueryService,
            IDebtDetailRepository debtDetailRepository,
            IKingdeeApiClient kingdeeApiClient,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            ISellApiClient sellApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            FinanceDbContext db,
            DaprClient daprClient,
            IAppServiceContextAccessor? contextAccessor,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
            ICompanyDateService companyDateService) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._sellApiClient = sellApiClient;
            this._bDSApiClient = bDSApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _debtDetailQueryService = debtDetailQueryService;
            _purchasePayPlanQueryService = purchasePayPlanQueryService;
            _debtDetailExcuteQueryService = debtDetailExcuteQueryService;
            _projectMgntApiClient = projectMgntApiClient;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _db = db;
            _daprClient = daprClient;
            _inventoryQueryService = inventoryQueryService;
            _companyDateService= companyDateService;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.BusinessCode);
                if (saleOut == null || !saleOut.SaleDetails.Any())
                {
                    throw new Exception("订阅订单修订事件出错，原因：查询上游单据时未获取到相关数据");
                }

                var requestBody = JsonConvert.SerializeObject(input);
                var ret = await CreateCreditForSellServiceFee(saleOut, input.BusinessSubType, requestBody);
                return ret;
            }
            catch (Exception ex)
            {
                throw; // new Exception("订阅暂存核销事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }

        private async Task<BaseResponseData<int>> CreateCreditForSellServiceFee(SaleOutput input, string classify,
            string preRequestBody)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            if (input != null)
            {
                var check = await base.IsCreatedCreditForBill(input.BillCode);
                if (check)
                {
                    throw new Exception("该单据已生成过应收");
                }

                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new BDSBaseInput
                {
                    ids = new List<string> { input.CompanyId.ToString() }
                })).FirstOrDefault();
                if (companyInfo != null)
                {
                    var serviceIds = input.SaleDetails.Where(p => p.ServiceId.HasValue).Select(p => p.ServiceId.Value)
                        .Distinct().ToList();
                    var services = new List<ServiceMetaOutput>();
                    if (serviceIds.Any())
                    {
                        services = await _bDSApiClient.GetServiceMetaAsync(
                            new ServiceMetaInput
                            {
                                ids = serviceIds.Select(p => p.ToString()).ToList()
                            });
                    }

                    var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                    if (input.SaleDetails.Count(p => !p.ProjectId.HasValue) > 0)
                    {
                        throw new Exception("明细中项目信息为空，请销售检查");
                    }

                    var jsonStr = JsonConvert.SerializeObject(input.SaleDetails);
                    // 先根据 Mark、ServiceId、ProjectId 分组
                    var initialGroup = input.SaleDetails.GroupBy(p => new { p.BatchId, p.OriginalId, p.Mark })
                        .Select(x => new SaleDetail
                        {
                            BatchId = x.Key.BatchId,
                            OriginalId = x.Key.OriginalId,
                            Amount = x.Sum(p => p.Amount),
                            // 存储原始数据引用
                            OriginalData = x.ToList(),
                            // 标记分组的数据 Amount 之和是否大于 0
                            IsThenZero = x.Sum(p => p.Amount) > 0,
                            Mark = x.Key.Mark,
                        }).ToList();
                    // 重组数据
                    var result = initialGroup.GroupBy(p => new { p.IsThenZero, p.Mark }).Select(x => new SaleDetail
                    {
                        IsThenZero = true,
                        Amount = x.Sum(p => p.Amount),
                        // 合并原始数据引用
                        OriginalData = x.SelectMany(p => p.OriginalData).ToList(),
                        Mark = x.Key.Mark,
                    }).ToList();
                    var projectIds = input.SaleDetails.Select(p => p.ProjectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    var index = 1;
                    var kingdeeCredits = new List<KingdeeCredit>();
                    //应收集合
                    var credits = new List<CreditDto>();
                    var Ids = new List<Guid>();
                    var customer = await _bDSApiClient.GetCustomer(new BDSBaseInput
                    {
                        id = input.CustomerId.ToString(),
                    });

                    foreach (var g in result)
                    {
                        var nlst = g.OriginalData.ToList();
                        var credit = new CreditDto
                        {
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            NameCode = companyInfo.nameCode,
                            AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                            Value = input.SaleDetails.Sum(p => p.Quantity * p.Price),
                            BillCode = $"{input.BillCode}-{index.ToString().PadLeft(3, '0')}",
                            BillDate = billDate, //input.BillDate,
                            CreatedBy = input.CreatedBy ?? "none",
                            CreatedTime = DateTime.Now,
                            CreditType = CreditTypeEnum.servicefee.ToString(),
                            CustomerId = input.CustomerId,
                            CustomerName = input.CustomerName,
                            Id = Guid.NewGuid(),
                            InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                            ServiceId = input.ServiceId,
                            RelateCode = input.BillCode,
                            Mark = g.Mark.HasValue ? (int)g.Mark : null,
                            IsSureIncome = 1,
                            //IsSureIncomeDate = DateTime.Now,
                            OrderNo = input.Source == SaleSourceEnum.Spd ? input.RelateCode : input.BillCode,
                            BusinessDeptFullName = input.businessDeptFullName,
                            BusinessDeptFullPath = input.businessDeptFullPath,
                            BusinessDeptId = input.businessDeptId.ToString(),
                            SaleSystemId = input.SaleSystemId,
                            SaleSystemName = input.SaleSystemName,
                            SaleSource = input.Source,
                            HospitalId = input.HospitalId,
                            HospitalName = input.HospitalName,
                            Note = input.Description,
                            LatestUniCode = customer.latestUniCode,
                            CustomerOrderCode = input.CustomerOrderCode,
                            CustomerPersonName = input.CustomerPersonName,
                            SunPurchaseRelatecode = input.SunPurchaseRelatecode,
                            ServiceConfirmRevenuePlanModeEnum = input.ServiceConfirmRevenuePlanMode,
                            AgentName =
                                string.Join(",", input.SaleDetails.Select(p => p.AgentName).Distinct().ToList()),
                            ProducerName = string.Join(",",
                                input.SaleDetails.Select(p => p.ProducerName).Distinct().ToList()),
                            PriceSource = input.PriceSourceType == PriceSourceEnum.OPERATION_APPLY
                                ? null
                                : input.PriceSourceType
                        };

                        credit.IsSureIncomeDate = credit.BillDate;
                        if (input.ServiceConfirmRevenuePlanMode.HasValue && input.ServiceConfirmRevenuePlanMode.Value ==
                            ServiceConfirmRevenuePlanModeEnum.InstallmentGeneration)
                        {
                            credit.IsSureIncome = null;
                            credit.IsSureIncomeDate = null;
                        }

                        credit.DeptName = input.DeptName;
                        if (input.TempInventoryDetails != null && input.TempInventoryDetails.Any())
                        {
                            credit.DeptName = string.Join(",",
                                input.TempInventoryDetails.Select(p => p.deptName).Distinct());
                        }

                        credit.OriginOrderNo = input.RelateCode;

                        if (input.SaleDetails != null && input.SaleDetails.Any())
                        {
                            credit.ProjectId = projectInfo.First().Id;
                            credit.ProjectCode = projectInfo.First().Code;
                            credit.ProjectName = projectInfo.First().Name;
                        }

                        Ids.Add(credit.Id);
                        credits.Add(credit);
                        if (input.ServiceId.HasValue)
                        {
                            credit.ServiceName = services
                                .FirstOrDefault(t => t.id.ToLower() == input.ServiceId.ToString().ToLower())?.name;
                        }

                        var amount = 0m; //不含税总额
                        var jfzx_alltotalcost = 0m;

                        #region 包装金蝶应收参数

                        var kingdeeCredit = new KingdeeCredit()
                        {
                            confirmManner =
                                credit.ServiceConfirmRevenuePlanModeEnum ==
                                ServiceConfirmRevenuePlanModeEnum.InstallmentGeneration
                                    ? "B"
                                    : "A",
                            asstact_number1 = input.CustomerId,
                            billno = credit.BillCode,
                            billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                            bizdate = credit.BillDate.Value,
                            org_number = credit.NameCode,
                            jfzx_businessnumber = input.businessDeptId.ToString(),
                            jfzx_ordernumber = input.BillCode,
                            jfzx_iscofirm = true,
                            jfzx_creator = credit.CreatedBy ?? "none",
                            jfzx_serviceid = credit.ServiceName,
                        };
                        if (input.ServiceConfirmRevenuePlanMode.HasValue && input.ServiceConfirmRevenuePlanMode.Value ==
                            ServiceConfirmRevenuePlanModeEnum.InstallmentGeneration)
                        {
                            kingdeeCredit.jfzx_iscofirm = false;
                        }

                        kingdeeCredit.jfzx_rebate = input.RebateType.HasValue;
                        var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                        nlst.ForEach(b =>
                        {
                            var d = new KingdeeCreditDetail();
                            d.e_expenseitem_number = KingdeeHelper.TransferSaleSubType(input.SaleSubType); //费用项目
                            d.e_taxunitprice = Math.Abs(b.Price);
                            d.e_unitprice = d.e_taxunitprice / (1 + b.SalesTaxRate.Value / 100.00M);
                            d.e_quantity = b.Price < 0 ? b.Quantity * -1 : b.Quantity;
                            d.salestaxrate = b.SalesTaxRate.Value;
                            d.e_material_number1 = "";
                            var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.ProjectId);
                            d.jfzx_projectnumber = thisProject?.Code;
                            d.jfzx_unitcost = 0;
                            d.jfzx_supplier = b.AgentId.ToString().ToUpper();
                            d.jfzx_standardtotal = 0;
                            kingdeeCreditDetails.Add(d);
                            if (input.RebateType.HasValue)
                            {
                                d.jfzx_rebateType = (int)input.RebateType;
                            }

                            ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                            //明细总成本
                            if (b.TaxRate.HasValue)
                            {
                                d.jfzx_totalcostMany =
                                    Math.Round((b.ActualCost / (1 + b.TaxRate.Value / 100.00M)) * d.e_quantity, 2);
                            }
                            else
                            {
                                d.jfzx_totalcostMany = 0;
                            }


                            amount += d.e_unitprice * d.e_quantity;
                            jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                        });

                        //应收不含税总额
                        kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                        //应收不含税总额
                        kingdeeCredit.amount = kingdeeCredit.recamount > 0
                            ? Math.Abs(Math.Round(amount, 2))
                            : -Math.Abs(Math.Round(amount, 2));
                        //总成本
                        kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                        kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                        kingdeeCredits.Add(kingdeeCredit);

                        #endregion

                        var insertRes = await base.CreateCredit(credit);
                        index++;
                    }

                    #region 分配订单认款应收金额

                    var addrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    var delrrdcs = new List<RecognizeReceiveDetailCreditPo>();
                    //查询预收订单认款数据
                    var rrdcs = await _db.RecognizeReceiveDetailCredits
                        .Where(x => x.OrderNo == input.BillCode && string.IsNullOrEmpty(x.CreditCode)).AsNoTracking()
                        .ToListAsync();
                    var rriIds = rrdcs.Select(x => x.RecognizeReceiveItemId);
                    var rris = await _db.RecognizeReceiveItems.Include(x => x.RecognizeReceiveDetails)
                        .Where(x => rriIds.Any(p => p == x.Id)).AsNoTracking().ToListAsync();
                    //应收可分配集合
                    var creditSurplusBox = credits.Select(x => new CreditSurplusBoxDto
                    {
                        Code = x.BillCode,
                        CreditSurplusTotalValue = x.Value
                    }).ToList();
                    foreach (var rrdc in rrdcs)
                    {
                        var surplusAmount = rrdc.CurrentValue;
                        foreach (var c in credits)
                        {
                            if (surplusAmount <= 0)
                            {
                                //剩余分配金额为0
                                continue;
                            }

                            //获取应收剩余可分配金额
                            decimal? surplusCreditAmount = c.Value;
                            var singleSurplusBox = creditSurplusBox.FirstOrDefault(x => x.Code == c.BillCode);
                            if (singleSurplusBox != null)
                            {
                                surplusCreditAmount = singleSurplusBox.CreditSurplusTotalValue;
                            }

                            if (surplusCreditAmount <= 0)
                            {
                                continue;
                            }

                            //认款金额大于应收金额，则写入应收全部金额
                            if (surplusAmount >= c.Value)
                            {
                                //deepcopy并继续添加
                                var model = JsonConvert.DeserializeObject<RecognizeReceiveDetailCreditPo>(
                                    JsonConvert.SerializeObject(rrdc));
                                if (model == null)
                                {
                                    continue;
                                }

                                model.Id = Guid.NewGuid();
                                model.CreditCode = c.BillCode;
                                model.CreditId = c.Id;
                                model.CurrentValue = surplusCreditAmount;
                                addrrdcs.Add(model);
                                //分配完金额
                                creditSurplusBox.Where(x => x.Code == c.BillCode).ForEach(x =>
                                {
                                    x.CreditSurplusTotalValue -= model.CurrentValue;
                                });
                                surplusAmount -= model.CurrentValue;
                            }
                            else
                            {
                                //deepcopy
                                var model = JsonConvert.DeserializeObject<RecognizeReceiveDetailCreditPo>(
                                    JsonConvert.SerializeObject(rrdc));
                                if (model == null)
                                {
                                    continue;
                                }

                                model.Id = Guid.NewGuid();
                                model.CreditCode = c.BillCode;
                                model.CreditId = c.Id;
                                model.CurrentValue = surplusAmount;
                                addrrdcs.Add(model);
                                //分配完金额
                                creditSurplusBox.Where(x => x.Code == c.BillCode).ForEach(x =>
                                {
                                    x.CreditSurplusTotalValue -= surplusAmount;
                                });
                                surplusAmount = 0;
                                break;
                            }
                        }

                        delrrdcs.Add(rrdc);
                    }

                    if (addrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.AddRange(addrrdcs);
                    }

                    if (delrrdcs.Any())
                    {
                        _db.RecognizeReceiveDetailCredits.RemoveRange(delrrdcs);
                    }

                    #endregion


                    var kingdeeRes =
                        await _kingdeeApiClient.PushCreditsToKingdee(kingdeeCredits, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await _unitOfWork.CommitAsync();
                        await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                        {
                            CreditBillCodes = kingdeeCredits.Select(p => p.billno).ToList(),
                            RelateCodes = kingdeeCredits.Select(p => p.jfzx_ordernumber).ToList(),
                        });
                        await CreditAllocationNotice(credits, input, companyInfo, addrrdcs, rris);
                    }
                    else
                    {
                        throw new Exception("生成应收到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }
                else
                {
                    throw new Exception("未找到对应的公司");
                }
            }

            return ret;
        }

        /// <summary>
        /// 通知金蝶结算信息
        /// </summary>
        /// <param name="credits"></param>
        /// <param name="input"></param>
        /// <param name="companyInfo"></param>
        /// <param name="addrrdcs"></param>
        /// <param name="rris"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<BaseResponseData<int>> CreditAllocationNotice(List<CreditDto> credits, SaleOutput input,
            DaprCompanyInfoOutput companyInfo, List<RecognizeReceiveDetailCreditPo> addrrdcs,
            List<RecognizeReceiveItemPo> rris)
        {
            try
            {
                #region 通知金蝶结算信息

                //货款
                var kdInputGoods = new List<BatchSaveAcceptanceInput>();
                //暂收款
                var kdInputsTemps = new List<SavePaymentModificationInput>();
                var companyIdToDateMap = new Dictionary<string, DateTime>();

                foreach (var entity in rris)
                {
                    //通知金蝶
                    if (string.IsNullOrEmpty(entity.RelateCode))
                    {
                        //货款
                        var kdInput = new BatchSaveAcceptanceInput
                        {
                            operation = "update",
                            billno = entity.Code,
                            jfzx_amountfield = entity.RecognizeReceiveDetails.Sum(p => p.Value),
                            jfzx_gatheringamount = entity.ReceiveValue,
                            jfzx_gatheringdate = entity.ReceiveDate.ToString("yyyy-MM-dd HH:mm:ss"),
                            jfzx_gatheringnum = entity.ReceiveCode,
                            jfzx_gatheringorg = companyInfo.nameCode,
                            jfzx_payer = entity.CustomerId,
                            jfzx_bizorg = entity.BusinessDepId.ToString(),
                            billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                            acceptanceEntrys = new List<AcceptanceEntrysItemInput> { }
                        };
                        if (entity.Type == "应付")
                        {
                            kdInput.billtype = "ap_finarbill";
                        }

                        //结算明细
                        var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
                        foreach (var detail in entity.RecognizeReceiveDetails)
                        {
                            //var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                            var recognizeReceiveDetailCredit =
                                addrrdcs.Where(x => x.RecognizeReceiveDetailId == detail.Id).ToList();
                            foreach (var rrdc in recognizeReceiveDetailCredit)
                            {
                                if (string.IsNullOrEmpty(rrdc.CreditCode))
                                {
                                    //预收的不传结算明细
                                    continue;
                                }

                                var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                                var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                                settlementEntrie.receivableNumber = rrdc.CreditCode;
                                settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                                settlementEntrie.orderNumber = rrdc.OrderNo;
                                settlementEntrie.settleAmount = rrdc.CurrentValue;
                                settlementEntrie.projectsNumber =
                                    currentCredit != null ? currentCredit.ProjectCode : string.Empty;
                                settlementEntrie.revenueConfirm = false;
                                if (currentCredit != null && currentCredit.IsSureIncome.HasValue &&
                                    currentCredit.IsSureIncome == 1)
                                {
                                    settlementEntrie.revenueConfirm = true;
                                }

                                settlementEntries.Add(settlementEntrie);
                            }
                        }

                        kdInput.settlementEntries = settlementEntries;
                        kdInputGoods.Add(kdInput);
                    }
                    else
                    {
                        // 使用缓存中的实际日期
                        if (!companyIdToDateMap.TryGetValue(entity.CompanyId, out var actualDate))
                        {
                            actualDate = await _companyDateService.GetActualDateAsync(entity.CompanyId);
                            companyIdToDateMap[entity.CompanyId] = actualDate;
                        }

                        //收款调整单（暂收款转货款）
                        var kdInput = new SavePaymentModificationInput
                        {
                            operation = "update",
                            billno = entity.Code,
                            jfzx_sourceorder = entity.RelateCode,
                            jfzx_billtype = entity.Type == "负数应收" ? "ar_finarbill" : "cas_recbill",
                            jfzx_accbillno = entity.ReceiveCode,
                            org = companyInfo.nameCode,
                            jfzx_adjustmentdate = actualDate.ToString("yyyy-MM-dd"), //entity.ReceiveDate,
                            entryentity = new List<PaymentModificationEntryModel> { }
                        };
                        if (entity.Type == "应付")
                        {
                            kdInput.jfzx_billtype = "ap_finarbill";
                        }

                        //结算明细
                        var settlementEntries = new List<AcceptanceSettleEntriesItemInput>();
                        foreach (var detail in entity.RecognizeReceiveDetails)
                        {
                            //var project = salesDetails.FirstOrDefault(p => p.BillCode == detail.Code.Trim());
                            var recognizeReceiveDetailCredit =
                                addrrdcs.Where(x => x.RecognizeReceiveDetailId == detail.Id).ToList();
                            foreach (var rrdc in recognizeReceiveDetailCredit)
                            {
                                if (string.IsNullOrEmpty(rrdc.CreditCode))
                                {
                                    //预收的不传结算明细
                                    continue;
                                }

                                var currentCredit = credits.FirstOrDefault(x => x.BillCode == rrdc.CreditCode);
                                var settlementEntrie = new AcceptanceSettleEntriesItemInput();
                                settlementEntrie.receivableNumber = rrdc.CreditCode;
                                settlementEntrie.invoiceNo = rrdc.InvoiceNo;
                                settlementEntrie.orderNumber = rrdc.OrderNo;
                                settlementEntrie.settleAmount = rrdc.CurrentValue;
                                settlementEntrie.projectsNumber =
                                    currentCredit != null ? currentCredit.ProjectCode : string.Empty;
                                settlementEntrie.revenueConfirm = false;
                                if (currentCredit != null && currentCredit.IsSureIncome.HasValue &&
                                    currentCredit.IsSureIncome == 1)
                                {
                                    settlementEntrie.revenueConfirm = true;
                                }

                                settlementEntries.Add(settlementEntrie);
                            }
                        }

                        kdInput.settlementEntries = settlementEntries;
                        kdInputsTemps.Add(kdInput);
                    }
                }

                if (kdInputGoods.Any())
                {
                    var kdResult = await _kingdeeApiClient.PushBatchSaveAcceptances(kdInputGoods);
                    if (kdResult.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{kdResult.Message}");
                    }
                }

                if (kdInputsTemps.Any())
                {
                    string errMsg = string.Empty;
                    foreach (var kdInput in kdInputsTemps)
                    {
                        var kdResult = await _kingdeeApiClient.SavePaymentModification(kdInput);
                        if (kdResult.Code != CodeStatusEnum.Success)
                        {
                            errMsg += "[" + kdResult.Message + "]";
                        }

                        if (!string.IsNullOrEmpty(errMsg))
                        {
                            throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{errMsg}");
                        }
                    }
                }

                return BaseResponseData<int>.Success("通知成功");

                #endregion
            }
            catch (Exception ex)
            {
                throw new Exception($"通知金蝶订单对应认款结算信息失败，原因：{ex.Message}");
            }
        }

        public async Task<BaseResponseData<List<KingdeeCredit>>> GetServiceFeeKingdeeCreditParams(EventBusDTO dto)
        {
            var input = await _sellApiClient.GetTempSaleByCodeAsync(dto.BusinessCode);
            if (input == null || !input.SaleDetails.Any())
            {
                return BaseResponseData<List<KingdeeCredit>>.Failed(500, "未获取到上游数据");
            }

            var projectIds = input.SaleDetails.Select(p => p.ProjectId.Value).Distinct().ToList();
            //var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            var index = 1;
            var kingdeeCredits = new List<KingdeeCredit>();
            var credit = new CreditDto
            {
                //CompanyId = Guid.Parse(companyInfo.companyId),
                //CompanyName = companyInfo.companyName,
                //NameCode = companyInfo.nameCode,
                AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                Value = input.SaleDetails.Sum(p => p.Quantity * p.Price),
                BillCode = $"{input.BillCode}-{index.ToString().PadLeft(3, '0')}",
                BillDate = input.BillDate,
                CreatedBy = input.CreatedBy ?? "none",
                CreatedTime = DateTime.Now,
                CreditType = CreditTypeEnum.servicefee.ToString(),
                CustomerId = input.CustomerId,
                CustomerName = input.CustomerName,
                Id = Guid.NewGuid(),
                InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                ServiceId = input.ServiceId,
                RelateCode = input.BillCode,
                IsSureIncome = 1,
                //IsSureIncomeDate = DateTime.Now,
                OrderNo = input.Source == SaleSourceEnum.Spd ? input.RelateCode : input.BillCode,
                BusinessDeptFullName = input.businessDeptFullName,
                BusinessDeptFullPath = input.businessDeptFullPath,
                BusinessDeptId = input.businessDeptId.ToString(),
                SaleSystemId = input.SaleSystemId,
                SaleSystemName = input.SaleSystemName,
                SaleSource = input.Source,
                HospitalId = input.HospitalId,
                HospitalName = input.HospitalName,
                Note = input.Description,
                CustomerOrderCode = input.CustomerOrderCode,
                CustomerPersonName = input.CustomerPersonName,
                AgentName = string.Join(",", input.SaleDetails.Select(p => p.AgentName).Distinct().ToList()),
                ProducerName = string.Join(",", input.SaleDetails.Select(p => p.ProducerName).Distinct().ToList()),
            };
            credit.IsSureIncomeDate = credit.BillDate;
            credit.DeptName = input.DeptName;
            if (input.TempInventoryDetails != null && input.TempInventoryDetails.Any())
            {
                credit.DeptName = string.Join(",", input.TempInventoryDetails.Select(p => p.deptName).Distinct());
            }

            //if (!string.IsNullOrEmpty(input.RelateCode) && (input.RelateCode.Contains("-PSA-") || input.SaleType == SaleTypeEnum.SaleForB)) //原始订单号 //原始订单号
            {
                credit.OriginOrderNo = input.RelateCode;
            }

            var amount = 0m; //不含税总额
            var jfzx_alltotalcost = 0m;

            #region 包装金蝶应收参数

            var kingdeeCredit = new KingdeeCredit()
            {
                asstact_number1 = input.CustomerId,
                billno = credit.BillCode,
                billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                bizdate = credit.BillDate.Value,
                org_number = credit.NameCode,
                jfzx_businessnumber = input.businessDeptId.ToString(),
                jfzx_ordernumber = input.BillCode,
                jfzx_iscofirm = true,
                jfzx_creator = credit.CreatedBy ?? "none",
                jfzx_serviceid = credit.ServiceName,
            };

            kingdeeCredit.jfzx_rebate = input.RebateType.HasValue;
            var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
            input.SaleDetails.ForEach(b =>
            {
                var d = new KingdeeCreditDetail();
                d.e_expenseitem_number = KingdeeHelper.TransferSaleSubType(input.SaleSubType); //费用项目
                d.e_taxunitprice = Math.Abs(b.Price);
                d.e_unitprice = d.e_taxunitprice / (1 + b.SalesTaxRate.Value / 100.00M);
                d.e_quantity = b.Price < 0 ? b.Quantity * -1 : b.Quantity;
                d.salestaxrate = b.SalesTaxRate.Value;
                d.e_material_number1 = "";

                //d.jfzx_projectnumber = thisProject?.Code;
                d.jfzx_unitcost = 0;
                d.jfzx_supplier = b.AgentId.ToString().ToUpper();
                d.jfzx_standardtotal = 0;
                kingdeeCreditDetails.Add(d);
                if (input.RebateType.HasValue)
                {
                    d.jfzx_rebateType = (int)input.RebateType;
                }

                ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                //明细总成本
                if (b.TaxRate.HasValue)
                {
                    d.jfzx_totalcostMany =
                        Math.Round((b.ActualCost / (1 + b.TaxRate.Value / 100.00M)) * d.e_quantity, 2);
                }
                else
                {
                    d.jfzx_totalcostMany = 0;
                }


                amount += d.e_unitprice * d.e_quantity;
                jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
            });

            //应收不含税总额
            kingdeeCredit.recamount = Math.Round(credit.Value, 2);
            //应收不含税总额
            kingdeeCredit.amount = kingdeeCredit.recamount > 0
                ? Math.Abs(Math.Round(amount, 2))
                : -Math.Abs(Math.Round(amount, 2));
            //总成本
            kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

            kingdeeCredit.billEntryModels = kingdeeCreditDetails;
            kingdeeCredits.Add(kingdeeCredit);

            #endregion

            return new BaseResponseData<List<KingdeeCredit>>()
            {
                Code = CodeStatusEnum.Success,
                Data = kingdeeCredits
            };
        }
    }
}