﻿
using Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Outputs;
using Inno.CorePlatform.Finance.Domain.DomainObject;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs
{
    public class ProjectOutput : BaseOutput
    {
        /// <summary>
        /// 项目编码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 项目子类型
        /// </summary>
        public string subType { get; set; }
        /// <summary>
        /// 规则配置
        /// </summary>
        public List<RuleConfigOutput>? RuleConfigList { get; set; }
        /// <summary>
        /// 产品授权范围
        /// </summary>
        public List<AuthorizeOutput>? AuthorizeList { get; set; }

        public BusinessDeptOutput? BusinessDept { get; set; }
    }
    /// <summary>
    /// 规则配置
    /// </summary>
    public class RuleConfigOutput : BaseOutput
    {
        public List<CompanyOutput> Company { get; set; }
        public List<ProducerOutput> Producer { get; set; }
        public List<ServiceOutput> Service { get; set; }
        public List<AgentOutput> Agent { get; set; }
        public BusinessAttrOutput FirstBusinessAttr { get; set; }
        //public List<CheckBusinessRelationOutput> RuleRelations
        //{
        //    get
        //    {
        //        var result = from c in Company
        //                     from p in Producer
        //                     from a in Agent
        //                     from s in Service.DefaultIfEmpty()
        //                     select new CheckBusinessRelationOutput
        //                     {
        //                         CompanyId = c.Id,
        //                         ProducerId = p.Id,
        //                         AgentId = a.Id,
        //                         ServiceId = s?.Id,
        //                     };
        //        return result.ToList();
        //    }
        //}
    }
    public class RuleRelation
    {
        public string? CompanyId { get; set; }
        public string? ProducerId { get; set; }
        public string? AgentId { get; set; }
        public string? ServiceId { get; set; }
    }

    /// <summary>
    /// 业务单元
    /// </summary>
    public class ServiceOutput : BaseOutput
    {
        public ServiceOutput()
        {

        }
        public ServiceOutput(ConditionDataOutput? source)
        {
            if (source != null)
            {
                this.Id = source.value.FirstOrDefault();
                this.Name = source.names.FirstOrDefault();
            }

        }
    }
    /// <summary>
    /// 业务属性
    /// </summary>
    public class BusinessAttrOutput
    {
        public int Level { get; set; }
        public string Name { get; set; }
    }
    /// <summary>
    /// 授权范围
    /// </summary>
    public class AuthorizeOutput : BaseOutput { }

}
