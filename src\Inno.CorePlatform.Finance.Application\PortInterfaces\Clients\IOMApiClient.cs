﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.OM;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IOMApiClient
    {
        Task<OMOutput> CheckCreditPayQuota(FinanceCheckPayQuotaInput input);
        Task<OMOutput> FinanceCheckPayQuota(FinanceCheckPayQuotaInput input);
    }
    public class OMOutput
    {
        public int ifControl { get; set; }
        public string message { get; set; }

        /// <summary>
        /// 管控信息
        /// </summary>
        public List<PayControlOutput> payControlList { get; set; }
    }

    /// <summary>
    /// 管控信息输出
    /// </summary>
    public class PayControlOutput
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public string projectId { get; set; }

        /// <summary>
        /// 是否配置 0-否 1-是
        /// </summary>
        public int ifconfig { get; set; }

        /// <summary>
        /// 管控形式:0-不管控，1-限额管控，2-强管控
        /// </summary>
        public int? controlMode { get; set; }

        /// <summary>
        /// 采购限制额度
        /// </summary>
        public decimal? purchaseQuota { get; set; }

        /// <summary>
        /// 已使用采购预付金额
        /// </summary>
        public decimal? usedAmt { get; set; }

        /// <summary>
        /// 经销购货已入库金额
        /// </summary>
        public decimal? instoreAmt { get; set; }

        /// <summary>
        /// 剩余金额
        /// </summary>
        public decimal? balance { get; set; }
    }
}
