﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 付款单查询服务接口
    /// </summary>
    public interface IPaymentQueryService
    {
        /// <summary>
        /// 获取列表数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<PaymentQueryListOutput>, int)> GetListAsync(PaymentQueryInput query);

        /// <summary>
        /// 获取没有采购单号的付款单
        /// </summary>
        /// <returns></returns>
        Task<List<PaymentQueryOutput>> GetPaymentOfNoPurchaseCode(PaymentQueryInputApi input);
        /// <summary>
        /// 更据采购单号获取现金支付可使用总额度
        /// </summary>
        /// <param name="purchasecodes"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<PaymentLimitOutput>>> GetNonUseTotalValue(List<string> purchasecodes);
        /// <summary>
        /// 根据采购订单获取付款单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<PaymentQueryOutput>> GetByPurchaseCodes(PaymentWebApiInput input);
        Task<BaseResponseData<PaymentQueryListTabOutput>> GetTabCount(PaymentQueryInput input);


        /// <summary>
        /// 获取列表数据 (通过采购单号)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<PaymentQueryListOutput>, int)> GetListByPurchaseCodeAsync(PaymentQueryInput query);
        Task<List<PaymentQueryOutput>> GetByCodes(List<string> codes);
        /// <summary>
        /// 根据批量付款单号获取终端医院
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<List<HospitalExcelOutput>> GetHospitalsByPaymentAutoItemCodes(List<string?>? codes);

        /// <summary>
        /// 获取付款计划
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<List<PaymentDetailOutput>> GetPaymentPlan(QueryById query);

        /// <summary>
        /// 导出付款单清单（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(PaymentQueryInput query);

        /// <summary>
        /// 获取付款丹清单异步导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<PaymentQueryListOutput>, int)> DownloadPayment(PaymentQueryInput query);

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportPaymentPlan(QueryById query);
        /// <summary>
        /// 退款获取游离的付款单
        /// </summary>
        /// <returns></returns>
        Task<List<PaymentQueryOutput>> RefundApplyGetPayment(Guid agentId, Guid companyId, string projectCode);
        /// <summary>
        /// 获取游离的付款单通过code
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<List<PaymentQueryOutput>> RefundApplyGetPaymentByCodes(List<string> codes);

        /// <summary>
        ///
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<Payment> GetById(Guid id);

        /// <summary>
        ///
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> DeleteAttachFileIds(AddCashDiscountFileInput input);

        /// <summary>
        /// 通过采购单号和项目Id获取付款单信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<PaymentByPurchaseAndProjectOutput>> GetPaymentByPurchaseAndProject(PaymentByPurchaseAndProjectInput input);

        /// <summary>
        /// 导出付款单付款信息（协调服务导出）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<ExportTaskResDto>> ExportPaymentInfoAsync(PaymentQueryInput query);

        /// <summary>
        /// 导出付款单付款信息
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<PaymentPayInfoListOutput>>> DownloadPaymentInfoAsync(PaymentQueryInput query);
    }
}
