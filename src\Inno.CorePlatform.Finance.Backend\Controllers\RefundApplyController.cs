﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Refund;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using NPOI.OpenXmlFormats;
using System;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RefundApplyController : BaseController
    {
        private readonly ICreditQueryService _creditQueryService;
        private readonly ILogger<PaymentQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly DaprClient _daprClient;
        private readonly IRecognizeReceiveQueryService _recognizerReceiveQueryService;
        private readonly IRefundAppService _refundAppService;
        private readonly IPaymentQueryService _paymentQueryService;
        private readonly IAbtmentService _abtmentService;
        /// <summary>
        /// 构造
        /// </summary>
        public RefundApplyController(ICreditQueryService creditQueryService, ILogger<PaymentQueryController> logger, IKingdeeApiClient kingdeeApiClient, DaprClient daprClient, IRecognizeReceiveQueryService recognizerReceiveQueryService, IRefundAppService refundAppService, IPaymentQueryService paymentQueryService, IAbtmentService abtmentService, ISubLogService subLog) : base(subLog)
        {
            this._daprClient = daprClient;
            this._creditQueryService = creditQueryService;
            this._logger = logger;
            this._kingdeeApiClient = kingdeeApiClient;
            _recognizerReceiveQueryService = recognizerReceiveQueryService;
            _refundAppService = refundAppService;
            _paymentQueryService = paymentQueryService;
            _abtmentService = abtmentService;
        }

        /// <summary>
        /// 生成退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Create")]
        [InnoCheckPermission("metadata://fam/refundApply-Query/routes/createAndeditRefundApply")]
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> Create([FromBody] SaveOrUpdateRefundInput input)
        {
            try
            {
                if (input.refundType == "246")
                {
                    input.e_payeetype = "bd_supplier";
                }
                string companyId = "";
                if (!string.IsNullOrEmpty(CurrentUser.UserName)) //流程申请人
                {
                    if (input.model.Equals("update"))
                    {
                        if (CurrentUser.UserName != input.OAUserName)
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：不能操作别人的数据");
                        }
                    }
                    var responseData = await GetOAAccountList(new OAAccountListInput
                    {
                        Names = new List<string> { CurrentUser.UserName }
                    });
                    if (responseData != null && responseData.Data != null && responseData.Data.List != null && responseData.Data.List.Any())
                    {
                        input.OAUserName = responseData.Data.List.FirstOrDefault();
                    }
                    else
                    {
                        input.OAUserName = CurrentUser.UserName;
                    }
                }
                if (input.RefundParty == 0) //客商退我方
                {
                    if (input.list == null || input.list.Count == 0)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：请先选择收款单和游离付款单");
                    }
                    if (input.list.Count(p => !string.IsNullOrEmpty(p.paymentCode)) != input.list.Count(p => !string.IsNullOrEmpty(p.receiveCode)))
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：收款单和游离付款单数量不一致");
                    }
                    var receiveList = new List<RecognizeReceiveOutput>();
                    var paymentCodes = input.list.Select(p => p.paymentCode).ToList();
                    var paymentList = await _paymentQueryService.RefundApplyGetPaymentByCodes(paymentCodes);
                    if (input.list.Select(p => p.paymentCode).Distinct().Count() > 1 && input.list.Select(p => p.receiveCode).Distinct().Count() > 1)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：收款单和游离付款单可以多对一或一对多，不可多对多");
                    }
                    if (input.list.Select(p => p.paymentCode).Distinct().Count() == 1 && input.list.Select(p => p.receiveCode).Distinct().Count() == 1 && input.list.Count > 1)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：收款单和游离付款单可以多对一或一对多，不可多对多");
                    }
                    if (input.list.Select(p => p.paymentCode).Distinct().Count() > 1 && input.list.Select(p => p.paymentCode).Distinct().Count() < input.list.Count())
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：请勿重复选择相同的游离付款单");
                    }
                    if (input.list.Select(p => p.receiveCode).Distinct().Count() > 1 && input.list.Select(p => p.receiveCode).Distinct().Count() < input.list.Count())
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：请勿重复选择相同的收款单");
                    }
                    if (input.list.Select(p => p.paymentCode).Distinct().Count() == 1 && input.list.Select(p => p.receiveCode).Distinct().Count() > 1)
                    {
                        if (input.list.DistinctBy(p => p.paymentCode).FirstOrDefault().paymentAmount < input.list.Sum(p => p.receiveAmount))
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：收款单金额不能大于游离的付款单");
                        }
                    }
                    if (input.list.Select(p => p.receiveCode).Distinct().Count() == 1 && input.list.Select(p => p.paymentCode).Distinct().Count() > 1)
                    {
                        if (input.list.DistinctBy(p => p.receiveCode).FirstOrDefault().receiveAmount < input.list.Sum(p => p.paymentAmount))
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：游离的付款单不能大于收款单金额");
                        }
                    }
                    var RecPaySettleRequestDtoList = new List<RecPaySettleRequestDto>();
                    var result = new RecognizeReceiveOutput();
                    foreach (var item in input.list)
                    {
                        if (receiveList.Select(p => p.billno).Contains(item.receiveCode))
                        {
                            result = receiveList.Where(p => p.billno == item.receiveCode).FirstOrDefault();
                        }
                        else
                        {
                            var kdResult = await _recognizerReceiveQueryService.GetKdReceiveBills(new RecognizeReceiveInput
                            {
                                company = new List<string> { input.NameCode },
                                customerName = input.CustomerName,
                                type = new List<string> { ((int)RecognizeReceiveTypeEnum.RefundPurchasePayment).ToString() },
                                displayNearInt = 0,
                                code = item.receiveCode,
                            });
                            result = kdResult.FirstOrDefault();
                            receiveList.Add(result);
                        }
                        RecPaySettleRequestDtoList.Add(new RecPaySettleRequestDto()
                        {
                            mainBill = item.receiveCode,
                            mainSettleAmt = result.namountclaimed > paymentList.Find(p => p.Code == item.paymentCode).Value ? paymentList.Find(p => p.Code == item.paymentCode).Value : result.namountclaimed,
                            asstBill = string.IsNullOrEmpty(paymentList.Find(p => p.Code == item.paymentCode).OriginCode) ? item.paymentCode : paymentList.Find(p => p.Code == item.paymentCode).OriginCode,
                            asstSettleAmt = result.namountclaimed > paymentList.Find(p => p.Code == item.paymentCode).Value ? paymentList.Find(p => p.Code == item.paymentCode).Value : result.namountclaimed,
                        });


                    }
                    input.refundAllMoney = input.list.DistinctBy(p => p.paymentCode).Sum(p => p.paymentAmount).Value;
                    if (input.list.DistinctBy(p => p.paymentCode).Sum(p => p.paymentAmount) > input.list.DistinctBy(p => p.receiveCode).Sum(p => p.receiveAmount))
                    {
                        input.refundAllMoney = input.list.DistinctBy(p => p.receiveCode).Sum(p => p.receiveAmount).Value;
                    }
                    var refundDetails = new List<RefundDetailPo>();
                    foreach (var item in input.list)
                    {
                        refundDetails.Add(new RefundDetailPo()
                        {
                            PaymentCode = item.paymentCode,
                            PaymentAmount = item.paymentAmount,
                            ReceiveCode = item.receiveCode,
                            ReceiveAmount = item.receiveAmount,
                        });
                    }
                    await _kingdeeApiClient.ReceiveAbtPayment(new ReceiveAbtPaymentInput()
                    {
                        data = RecPaySettleRequestDtoList
                    });
                    var refundSaveInput = new RefundSaveInput()
                    {
                        BankAccount = input.bankAccount,
                        BankBranchName = input.bankBranchName,
                        BankBranchNumber = input.bankBranchNumber,
                        BankName = input.bankName,
                        BankNo = input.bankNo,
                        BillCode = input.billno,
                        BillDate = DateTime.Now,
                        BusinessDeptId = input.dept,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptFullName = input.businessDeptFullName,
                        CompanyId = Guid.Parse(input.company),
                        CompanyName = input.companyName,
                        CustomerId = Guid.Parse(input.client),
                        CustomerName = input.CustomerName,
                        ProjectCode = input.projectNumber,
                        ProjectId = Guid.Parse(input.projectId),
                        ProjectName = input.projectName,
                        NameCode = input.NameCode,
                        E_payeetype = input.e_payeetype,
                        RefundAllMoney = input.refundAllMoney,
                        List = refundDetails,
                        Remark = input.remark,
                        Status = RefundStatusEnum.Complate,
                        userName = CurrentUser.UserName,
                        MoneyNumber = "CNY",
                        PayModel = "",
                        RefundType = "",
                        CreatedTime = DateTimeOffset.Now,
                        TransferPostscript = input.transferPostscript
                    };
                    if (string.IsNullOrEmpty(refundSaveInput.BillCode))
                    {
                        //保存操作
                        var saveResult = await _refundAppService.SaveRefund(refundSaveInput);
                        if (saveResult.Code == CodeStatusEnum.Failed)
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作成功，但是本地退款单保存失败");
                        }
                    }
                    else
                    {
                        refundSaveInput.Id = Guid.Parse(input.refundItemId);
                        var saveResult = await _refundAppService.UpdateRefund(refundSaveInput, RefundStatusEnum.Complate);
                        if (saveResult.Code == CodeStatusEnum.Failed)
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作成功，但是本地退款单保存失败");
                        }
                    }
                    var count = await _abtmentService.ReceiveAbtPaymentSave(refundSaveInput.List, CurrentUser.UserName);
                    if (count == 0)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作成功，但是本地插入冲销信息失败");
                    }
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Success("操作成功");
                }
                else
                {
                     companyId = input.company;
                    input.company = input.NameCode;//金蝶公司传的是编码
                    if (input.model == "insert")
                    {
                        input.billno = "";
                    }
                    if (input.refundType == "203" || input.refundType == "240")//负数应收
                    {
                        var credits = await _creditQueryService.GetNonAbated(new GetCreditInput
                        {
                            CustomerId = Guid.Parse(input.client)
                        });
                        if (input.list != null && input.list.Any())
                        {
                            foreach (var item in input.list)
                            {
                                var credit = credits.Where(p => p.BillCode == item.minusNumber).FirstOrDefault();
                                if (credit != null && credit.NonAbatedValue.HasValue)
                                {
                                    if (Math.Abs(credit.NonAbatedValue.Value) < item.refundMoney)
                                    {
                                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, $"操作失败，原因：{item.minusNumber}退款金额超过未冲销金额");
                                    }
                                }
                            }
                            input.refundAllMoney = input.list.Sum(p => p.refundMoney);
                            input.moneyNumber = "CNY";
                            var groupLst = input.list.GroupBy(p => p.minusNumber).Select(p => new { p.Key, Count = p.Count() }).ToList();
                            if (groupLst.Exists(p => p.Count > 1))
                            {
                                return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：请勿重复选择相同的负数应收单");
                            }
                            if (input.list.Exists(p => string.IsNullOrEmpty(p.minusNumber)))
                            {
                                return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：请选择完负数应收单号后再操作");
                            }
                        }
                        else
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：负数应收没有选中应收数据");
                        }
                    }
                    if (input.refundAllMoney <= 0)
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：退款金额必须大于0");
                    }
                    // 核心平台校验一下退款金额不超过收款单被创建在认款单的总额（不管认款单是什么状态都增加校验）billno
                    if (input.list != null && !string.IsNullOrEmpty(input.list[0].receivablesNumber))
                    {
                        var recognizes = await _recognizerReceiveQueryService.GetItemByReceiveCode(input.list[0].receivablesNumber);
                        recognizes = recognizes.Where(p => p.Status != RecognizeReceiveItemStatusEnum.Canceled).ToList();
                        if (recognizes != null && recognizes.Any())
                        {
                            var ids = recognizes.Select(x => x.Id).ToList();
                            var details = await _recognizerReceiveQueryService.GetDetailByIds(ids);
                            var detailValue = details.Where(x => x.Status == RecognizeReceiveDetailEnum.Normal).Sum(x => x.Value);
                            decimal receiveValue = recognizes.Sum(p => p.ReceiveValue);
                            if (receiveValue - detailValue < input.refundAllMoney)
                            {
                                return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作失败，原因：退款金额不能超过收款单金额-已认款金额");
                            }
                        }
                    }
                }
                var ret = await _kingdeeApiClient.SaveOrUpdateRefund(input);
                if (ret.Code == CodeStatusEnum.Success)
                {
                    var refundDetails = new List<RefundDetailPo>();
                    foreach (var item in input.list)
                    {
                        refundDetails.Add(new RefundDetailPo()
                        {
                            MinusNumber = item.minusNumber,
                            RefundMoney = item.refundMoney,
                        });
                    }
                    var refundSaveInput = new RefundSaveInput()
                    {
                        BankAccount = input.bankAccount,
                        BankBranchName = input.bankBranchName,
                        BankBranchNumber = input.bankBranchNumber,
                        BankName = input.bankName,
                        BankNo = input.bankNo,
                        BillCode = input.billno,
                        BillDate = DateTime.Now,
                        BusinessDeptId = input.dept,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptFullName = input.businessDeptFullName,
                        CompanyId = Guid.Parse(companyId),
                        CompanyName = input.companyName,
                        CustomerId = Guid.Parse(input.client),
                        CustomerName = input.CustomerName,
                        ProjectCode = input.projectNumber,
                        ProjectId = string.IsNullOrEmpty(input.projectId) ? null : Guid.Parse(input.projectId),
                        ProjectName = input.projectName,
                        NameCode = input.NameCode,
                        E_payeetype = input.e_payeetype,
                        RefundAllMoney = input.refundAllMoney,
                        List = refundDetails,
                        Remark = input.remark,
                        Status = RefundStatusEnum.Complate,
                        userName = CurrentUser.UserName,
                        MoneyNumber = "CNY",
                        PayModel = input.payModel,
                        RefundType = input.refundType,
                        CreatedTime = DateTimeOffset.Now,
                        ReceivablesNumber = input.list[0] != null ? input.list[0].receivablesNumber : "",
                        TransferPostscript = input.transferPostscript
                    };
                    if (string.IsNullOrEmpty(refundSaveInput.BillCode))
                    {
                        //保存操作
                        refundSaveInput.BillCode = ret.Data.billno;//重新赋值金蝶的单号
                        var saveResult = await _refundAppService.SaveRefund(refundSaveInput);
                        if (saveResult.Code == CodeStatusEnum.Failed)
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作成功，但是本地退款单保存失败");
                        }
                    }
                    else
                    {
                        refundSaveInput.BillCode = ret.Data.billno;//重新赋值金蝶的单号
                        var saveResult = new BaseResponseData<int>();
                        if (input.refundItemId != null)
                        {
                            refundSaveInput.Id = Guid.Parse(input.refundItemId);
                            saveResult = await _refundAppService.UpdateRefund(refundSaveInput, RefundStatusEnum.waitAudit);
                        }
                        else
                        {
                            var changeStateInput = new RefundStateSynchronizationInput();
                            changeStateInput.Code = refundSaveInput.BillCode;
                            changeStateInput.Status = RefundStatusEnum.waitAudit;
                            saveResult = await _refundAppService.UpdateRefundState(changeStateInput);
                        }
                        if (saveResult.Code == CodeStatusEnum.Failed)
                        {
                            return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "操作成功，但是本地退款单保存失败");
                        }
                    }
                }
                return ret;
            }
            catch (Exception ex)
            {
                return new BaseResponseData<SaveOrUpdateRefundOutput>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 保存退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Save")]
        public async Task<BaseResponseData<int>> Save([FromBody] RefundSaveInput input)
        {
            try
            {
                if (input.RefundType == "246")
                {
                    input.E_payeetype = "bd_supplier";
                }
                input.userName = CurrentUser.UserName;
                if (input.RefundParty == 0)
                {
                    if (input.List == null || input.List.Count == 0)
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：请先选择收款单和游离付款单");
                    }
                    if (input.List.Count(p => !string.IsNullOrEmpty(p.PaymentCode)) != input.List.Count(p => !string.IsNullOrEmpty(p.ReceiveCode)))
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：收款单和游离付款单数量不一致");
                    }
                    var receiveList = new List<RecognizeReceiveOutput>();
                    var paymentList = await _paymentQueryService.RefundApplyGetPaymentByCodes(input.List.Select(p => p.PaymentCode).ToList());
                    if (input.List.Select(p => p.PaymentCode).Distinct().Count() > 1 && input.List.Select(p => p.ReceiveCode).Distinct().Count() > 1)
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：收款单和游离付款单可以多对一或一对多，不可多对多");
                    }
                    if (input.List.Select(p => p.PaymentCode).Distinct().Count() == 1 && input.List.Select(p => p.ReceiveCode).Distinct().Count() == 1 && input.List.Count > 1)
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：收款单和游离付款单可以多对一或一对多，不可多对多");
                    }
                    if (input.List.Select(p => p.PaymentCode).Distinct().Count() > 1 && input.List.Select(p => p.PaymentCode).Distinct().Count() < input.List.Count())
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：请勿重复选择相同的游离付款单");
                    }
                    if (input.List.Select(p => p.ReceiveCode).Distinct().Count() > 1 && input.List.Select(p => p.ReceiveCode).Distinct().Count() < input.List.Count())
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：请勿重复选择相同的收款单");
                    }
                    foreach (var item in input.List)
                    {
                        if (receiveList.Select(p => p.billno).Contains(item.ReceiveCode))
                            continue;
                        var result = await _recognizerReceiveQueryService.GetKdReceiveBills(new RecognizeReceiveInput
                        {
                            company = new List<string> { input.NameCode },
                            customerName = input.CustomerName,
                            type = new List<string> { ((int)RecognizeReceiveTypeEnum.RefundPurchasePayment).ToString() },
                            displayNearInt = 0,
                            code = item.ReceiveCode,
                        });
                        receiveList.AddRange(result);
                    }
                    input.RefundAllMoney = input.List.DistinctBy(p => p.PaymentCode).Sum(p => p.PaymentAmount).Value;
                    if (input.List.DistinctBy(p => p.PaymentCode).Sum(p => p.PaymentAmount) > input.List.DistinctBy(p => p.ReceiveCode).Sum(p => p.ReceiveAmount))
                    {
                        input.RefundAllMoney = input.List.DistinctBy(p => p.ReceiveCode).Sum(p => p.ReceiveAmount).Value;
                    }
                    if (input.List.Select(p => p.PaymentCode).Distinct().Count() == 1 && input.List.Select(p => p.ReceiveCode).Distinct().Count() > 1)
                    {
                        if (input.List.DistinctBy(p => p.PaymentCode).FirstOrDefault().PaymentAmount < input.List.Sum(p => p.ReceiveAmount))
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：收款单金额不能大于游离的付款单");
                        }
                    }
                    if (input.List.Select(p => p.ReceiveCode).Distinct().Count() == 1 && input.List.Select(p => p.PaymentCode).Distinct().Count() > 1)
                    {
                        if (input.List.DistinctBy(p => p.ReceiveCode).FirstOrDefault().ReceiveAmount < input.List.Sum(p => p.PaymentAmount))
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：游离的付款单不能大于收款单金额");
                        }
                    }
                }
                else if (input.RefundParty == 1)
                {
                    if (input.RefundType == "203" || input.RefundType == "240")//负数应收
                    {
                        var credits = await _creditQueryService.GetNonAbated(new GetCreditInput
                        {
                            CustomerId = input.CustomerId
                        });
                        if (input.List != null && input.List.Any())
                        {
                            foreach (var item in input.List)
                            {
                                var credit = credits.Where(p => p.BillCode == item.MinusNumber).FirstOrDefault();
                                if (credit != null && credit.NonAbatedValue.HasValue)
                                {
                                    if (Math.Abs(credit.NonAbatedValue.Value) < item.RefundMoney)
                                    {
                                        return BaseResponseData<int>.Failed(500, $"操作失败，原因：{item.MinusNumber}退款金额超过未冲销金额");
                                    }
                                }
                            }
                            input.RefundAllMoney = input.List.Sum(p => p.RefundMoney);
                            input.MoneyNumber = "CNY";
                            var groupLst = input.List.GroupBy(p => p.MinusNumber).Select(p => new { p.Key, Count = p.Count() }).ToList();
                            if (groupLst.Exists(p => p.Count > 1))
                            {
                                return BaseResponseData<int>.Failed(500, "操作失败，原因：请勿重复选择相同的负数应收单");
                            }
                            if (input.List.Exists(p => string.IsNullOrEmpty(p.MinusNumber)))
                            {
                                return BaseResponseData<int>.Failed(500, "操作失败，原因：请选择完负数应收单号后再操作");
                            }
                        }
                        else
                        {
                            return BaseResponseData<int>.Failed(500, "操作失败，原因：负数应收没有选中应收数据");
                        }
                    }
                    if (input.RefundAllMoney <= 0)
                    {
                        return BaseResponseData<int>.Failed(500, "操作失败，原因：退款金额必须大于0");
                    }
                    // 核心平台校验一下退款金额不超过收款单被创建在认款单的总额（不管认款单是什么状态都增加校验）billno
                    if (input.List != null && !string.IsNullOrEmpty(input.ReceivablesNumber))
                    {
                        var recognizes = await _recognizerReceiveQueryService.GetItemByReceiveCode(input.ReceivablesNumber);
                        recognizes = recognizes.Where(p => p.Status != RecognizeReceiveItemStatusEnum.Canceled).ToList();
                        if (recognizes != null && recognizes.Any())
                        {
                            var ids = recognizes.Select(x => x.Id).ToList();
                            var details = await _recognizerReceiveQueryService.GetDetailByIds(ids);
                            var detailValue = details.Where(x => x.Status == RecognizeReceiveDetailEnum.Normal).Sum(x => x.Value);
                            decimal receiveValue = recognizes.Sum(p => p.ReceiveValue);
                            if (receiveValue - detailValue < input.RefundAllMoney)
                            {
                                return BaseResponseData<int>.Failed(500, "操作失败，原因：退款金额不能超过收款单金额-已认款金额");
                            }
                        }
                    }
                }
                if (input.Id == Guid.Empty)
                {
                    input.Status = RefundStatusEnum.waitSubmit;
                    //保存操作
                    var ret = await _refundAppService.SaveRefund(input);
                    return ret;
                }
                else
                {
                    //保存操作
                    var ret = await _refundAppService.UpdateRefund(input, RefundStatusEnum.waitSubmit);
                    return ret;
                }


            }
            catch (Exception ex)
            {
                return new BaseResponseData<int>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 提交退款到金蝶
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitKingdee")]
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> SubmitKingdee([FromBody] RefundSubmitInput input)
        {
            //提交操作
            input.userName = CurrentUser.UserName;
            var ret = await _refundAppService.SubmitKingdee(input);
            return ret;
        }

        /// <summary>
        /// 生成退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Delete")]
        [InnoCheckPermission("metadata://fam/refundApply-Query/routes/deleteRefundApply")]
        public async Task<BaseResponseData<int>> Delete([FromBody] RefundDeleteInput input)
        {
            try
            {
                if (input.creator != CurrentUser.UserName)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：不能操作别人的数据");
                }
                if (string.IsNullOrEmpty(input.id))
                {
                    return await _kingdeeApiClient.RefundDelete(input);
                }
                else
                {
                    return await _refundAppService.DeleteRefund(Guid.Parse(input.id));
                }

            }
            catch (Exception ex)
            {
                return new BaseResponseData<int>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
        /// <summary>
        /// 获取OA账号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<ResponseData<string>> GetOAAccountList(OAAccountListInput input)
        {
            return await _daprClient.InvokeMethodAsync<OAAccountListInput, ResponseData<string>>(HttpMethod.Post, "uc-webapi", "/api/users/getoaaccounts", input);
        }
    }
}
