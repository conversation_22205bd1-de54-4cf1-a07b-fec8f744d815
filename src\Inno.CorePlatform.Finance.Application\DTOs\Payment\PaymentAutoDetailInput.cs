﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class PaymentAutoDetailInput
    {
        public Guid? Id { get; set; }

        /// <summary>
        /// 批量付款单Id
        /// </summary>
        public Guid? PaymentAutoItemId { get; set; }


        /// <summary>
        /// 应付付款计划明细Id
        /// </summary>
        public Guid DebtDetilId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }
        /// <summary>
        /// DebtDetail单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime? PaymentDate { get; set; }
        public string? companyName { get; set; }
    }
}
