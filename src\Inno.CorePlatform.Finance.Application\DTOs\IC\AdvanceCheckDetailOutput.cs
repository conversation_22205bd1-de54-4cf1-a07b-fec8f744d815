﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.IC
{ 
    public class AdvanceCheckDetailOutput
    { 
        public Guid Id { get; set; }

        public string companyId { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary> 
        public string businessUnitId { get; set; }

        public string businessUnitName { get; set; }

        /// <summary>
        /// 医院Id
        /// </summary>
        public string HospitalId { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 是否核准医院
        /// </summary>
        public string? IsVerify { get; set; }

        /// <summary>
        /// 是否全流程接管医院
        /// </summary>
        public string? IsTakeOver { get; set; }

        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        public decimal? FinanceDiscount { get; set; }

        /// <summary>
        /// 年化垫资利率（%）
        /// </summary> 
        public decimal? RateOfYear { get; set; }

        /// <summary>
        /// 合计折扣（%）
        /// </summary> 
        public decimal? TotalDiscount { get; set; }
        /// <summary>
        /// 垫资申请单号
        /// </summary>
        public string? AdvanceCode { get;  set; } 

        /// <summary>
        /// 销售账期天数
        /// </summary>
        public int? providPayDays { get; set; }

        /// <summary>
        /// 回款账期天数
        /// </summary>
        public int? returnMoneyDays { get; set; }

        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? advanceDays { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal? costDiscount { get; set; }

        /// <summary>
        /// 基础折扣
        /// </summary>
        public decimal? distributionDiscount { get; set; }


        /// <summary>
        /// SPD折扣
        /// </summary>
        public decimal? spdDiscount { get; set; }

        /// <summary>
        /// 垫资比例
        /// </summary>
        public decimal? advanceRatio { get; set; }
         
        /// <summary>
        /// 实际供应链金额折扣
        /// </summary>
        public decimal? actualFinanceDiscount { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? creditCode { get; set; }


        /// <summary>
        /// 应收日期
        /// </summary>
        public DateTime? creditDate { get; set; }


        /// <summary>
        /// 应收单金额
        /// </summary>
        public decimal? creditBillValue { get; set; }


        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? invoiceDate { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? debtCode { get; set; }

        /// <summary>
        /// 应付日期
        /// </summary>
        public DateTime? debtDate { get; set; }
         
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal? debtValue { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? receiveCode { get; set; }


        /// <summary>
        /// 收款日期
        /// </summary>
        public DateTime? receiveDate { get; set; }

        /// <summary>
        /// 计划回款日期
        /// </summary>
        public DateTime? plannedReceiveDate { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? paymentCode { get; set; }


        /// <summary>
        /// 付款日期
        /// </summary>
        public DateTime? paymentDate { get; set; }

        /// <summary>
        /// 预计付款日期
        /// </summary>
        public DateTime? estimatePayDate { get; set; }

        /// <summary>
        /// 应收Id
        /// </summary>
        public string creditId { get; set; }
        /// <summary>
        /// 应付计划id
        /// </summary>
        public string debtDetailId { get; set; }

        /// <summary>
        /// 提前还款利息
        /// </summary>
        public decimal? earlyReturnInterest { get; set; }

        /// <summary>
        /// 逾期利息
        /// </summary>
        public decimal? overdueInterest { get; set; }


        /// <summary>
        /// 垫资应收到期时间
        /// </summary>
        public DateTime? advanceExpireTime { get; set; }

        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? overdueDays { get; set; }

        /// <summary>
        /// 逾期状态
        /// </summary>
        public string? overdueStatus { get; set; }

        /// <summary>
        /// 资金占用金额
        /// </summary>
        public decimal? fundUsedValue { get; set; }

        /// <summary>
        /// 基础毛利
        /// </summary>
        public decimal? basicGrossProfit { get; set; }

        /// <summary>
        /// 垫资利息收入
        /// </summary>
        public decimal? intrestIncome { get; set; }

        /// <summary>
        /// 合计毛利
        /// </summary>
        public decimal? totalGrossProfit { get; set; }

        /// <summary>
        /// 校验
        /// </summary>
        public decimal? verify { get; set; }

        /// <summary>
        /// 开票后实际支付(天数)
        /// </summary>
        public int? actualPaydays { get; set; }

        /// <summary>
        /// 提示(放款风险)
        /// </summary>
        public string? hintRisk { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 批量付款单号
        /// </summary>
        public string? batchpaymentCode { get; set; }
    }
}
