﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class CustomerProductBillingInput
    {
        /// <summary>
        /// 销售子系统
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary> 
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary> 
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 货号Ids
        /// </summary> 
        public List<Guid>? ProductIds { get; set; }
    }

    public class CustomerProductBillingOutput
    {
        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 开票名称
        /// </summary>
        public string? InvoiceName { get; set; }

        /// <summary>
        /// 开票规格
        /// </summary>
        public string? InvoiceSpecification { get; set; }

        /// <summary>
        /// 开票单位
        /// </summary>
        public string? InvoiceUnit { get; set; }
    }
}
