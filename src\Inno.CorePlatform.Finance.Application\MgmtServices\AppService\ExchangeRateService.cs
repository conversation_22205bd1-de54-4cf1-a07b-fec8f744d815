﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class ExchangeRateService : IExchangeRateService
    {
        private readonly FinanceDbContext _db;
        private IKingdeeApiClient _kingdeeApiClient;
        public ExchangeRateService(FinanceDbContext db, IKingdeeApiClient kingdeeApiClient)
        {
            this._db = db;
            this._kingdeeApiClient = kingdeeApiClient;
        }
        public async Task<int> Add(ExchangeRateInput input)
        {
            var exchangeRatePo = input.Adapt<ExchangeRatePo>();
            await _db.ExchangeRates.AddAsync(exchangeRatePo);
            return await _db.SaveChangesAsync();
        }

        public async Task<GetExchangeRateOutput> GetByExchangeRate(GetExchangeRateInput input)
        {
            var exchange = await _db.ExchangeRates.Where(p => p.OrgcurName == input.OrgcurName && p.Effectdate == input.Effectdate&&p.CurName==input.CurName).FirstOrDefaultAsync();
            if (exchange != null)
            {
                return exchange.Adapt<GetExchangeRateOutput>();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 查询汇率
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<GetExchangeRateOutput>> GetByExchangeRateWithKD(GetExchangeRateInput input)
        {
            var ret = BaseResponseData<GetExchangeRateOutput>.Success("操作成功！");
            if (input.OrgcurName == input.CurName)
            {
                ret.Data = new GetExchangeRateOutput
                {
                    CurName = input.OrgcurName,
                    Effectdate = input.Effectdate,
                    Excval = 1,
                    Expirydate = DateTime.Now,
                    Indirectexrate = 1,
                    OrgcurName = input.OrgcurName,
                };
                return ret;
            }
            var exchange = await GetByExchangeRate(input);
            if (exchange == null)
            {
                var kind = await _kingdeeApiClient.ExchangeRateQuery(new ExchangeRateQueryInput
                {
                    cur_name = input.CurName,
                    effectdate = input.Effectdate.ToString("yyyy-MM-dd"),
                    orgcur_name = input.OrgcurName
                });
                if (kind.Code != CodeStatusEnum.Success)
                {
                    throw new Exception(kind.Message);
                }
                if (kind.Data == null || kind.Data.rows == null || !kind.Data.rows.Any())
                {
                    return BaseResponseData<GetExchangeRateOutput>.Failed(500, $"【金蝶】未获取到【{input.CurName}】与【{input.OrgcurName}】金蝶的汇率数据！");
                }
                var addPo = new ExchangeRateInput
                {
                    CurName = kind.Data.rows[0].cur_name,
                    Effectdate = DateTime.Parse(kind.Data.rows[0].effectdate),
                    Excval = kind.Data.rows[0].excval.Value,
                    Expirydate = input.Effectdate,
                    Indirectexrate = kind.Data.rows[0].indirectexrate.Value,
                    OrgcurName = kind.Data.rows[0].orgcur_name
                };
                var retAdd = await Add(addPo);
                ret.Data = addPo.Adapt<GetExchangeRateOutput>();
            }
            else
            {
                ret.Data = exchange;
            }
            return ret;
        }
    }
}
