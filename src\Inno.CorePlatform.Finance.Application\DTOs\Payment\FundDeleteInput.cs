﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    /// <summary>
    /// 删除付款申请入参
    /// </summary>
    public class FundDeleteInput
    {
        /// <summary>
        /// 1：删除预付款，2：删除批量付款，3：删除退款申请
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }
    }
}
