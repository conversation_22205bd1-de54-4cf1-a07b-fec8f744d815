﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IReconciliationLetterQueryService
    {
        /// <summary>
        /// 获取对账函tab页数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<ReconciliationLetterListTabOutput>> GetTabCount(ReconciliationLetterQueryInput query);
        /// <summary>
        /// 获取对账函列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<ReconciliationLetterListOutput>, int)> GetListAsync(ReconciliationLetterQueryInput query);
        /// <summary>
        /// 获取对账函详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<ReconciliationLetterDetailQueryOutput>, int)> GetListDetailAsync(ReconciliationLetterDetailQueryInput query);

        /// <summary>
        /// 获取对账函货号详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<ReconciliationLetterProductDetailQueryOutput>, int)> GetListProductDetailAsync(ReconciliationLetterDetailQueryInput query);
        Task<ReconciliationLetterItemPo> GetLetterItemByIdAsync(Guid id);
        Task<DetailSumCount> GetDetailSumCount(ReconciliationLetterDetailQueryInput input);
    }
}
