﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    public class BaseQueryOutput : BaseDtoWithBasicInfo<Guid>
    {
        public string StatusName
        {
            get
            {
                return ((PurchaseStatusEnums)Status).GetDescription();
            }
        }
    }
    /// <summary>
    /// 基本分页返回对象
    /// </summary>
    /// <typeparam name="ListDTO"></typeparam>
    public class BasePageResult<ListDTO>
    {
        public List<ListDTO> Data { get; set; }
        public List<ListDTO> List { get; set; }
        public int Total { get; set; }
    }
    /// <summary>
    /// 带页签的分页返回对象
    /// </summary>
    /// <typeparam name="ListDTO"></typeparam>
    /// <typeparam name="TabDTO"></typeparam>
    public class BasePageWithTabResult<ListDTO, TabDTO> : BasePageResult<ListDTO>
    {
        public TabDTO Tab { get; set; }
    }

}
