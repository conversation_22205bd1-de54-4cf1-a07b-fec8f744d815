﻿using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class InputBillDeatilQueryListOut
    {

        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }
        public Guid Id { get; set; }
        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string StoreInItemCode { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        public DateTime StoreInDate { get; set; }
        /// <summary>
        /// 累计到票数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 入库数量
        /// </summary>
        public decimal StroeInQuantity { get; set; }
        /// <summary>
        /// 未到票数量
        /// </summary>
        public decimal NoQuantity { get { return StroeInQuantity - Quantity; } }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }
        /// <summary>
        /// 进项发票Id
        /// </summary>
        public Guid InputBillId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string AgentName { get; set; }

        public List<string> storeInCodes { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

    }

    public class PurchaseInputBillOutput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime BillTime { get; set; }

        /// <summary>
        /// 票据类型 1,普票 2，专票
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>

        public DateTimeOffset CreatedTime { get; set; }


        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }


        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }

        /// <summary>
        /// 入库单号
        /// </summary>
        public string StoreInItemCode { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 状态 1=临时发票，2=已提交，3=正在匹配，9=忽略
        /// </summary>
        public int Status { get; set; }

        public string? PurchaseCode { get; internal set; }
        public decimal InvoiceAmount { get; internal set; }
        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid ProductNameId { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid ProductId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }
        /// <summary>
        /// 本次入票数量
        /// </summary>
        public decimal Quantity { get; set; }

    }

    public class PurchaseInputBillInput
    {
        public List<string?> Codes { get; set; }
    }
}
