﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Project.Backend.Controllers
{
    /// <summary>
    /// 泛微OA
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class WeaverController : ControllerBase
    {
        private readonly IWeaverApiClient weaverApiClient;
        private readonly IConfiguration _configuration;
        public WeaverController(IWeaverApiClient weaverApiClient,
                               
                                IConfiguration configuration)
        {
            this.weaverApiClient = weaverApiClient;
            _configuration = configuration;
        }     
        /// <summary>
        /// PC去审议
        /// </summary>
        /// <param name="requestId"></param>
        /// <returns></returns>
        [HttpGet("GetApprovedUrlForPC")]
        public IActionResult GetApprovedUrlForPC(long requestId)
        {
            BaseResponseData<string> res = BaseResponseData<string>.Success("操作成功！");
            try
            {
                res.Data = weaverApiClient.GetApprovedUrlForPC(requestId);
                return Ok(res);
            }
            catch (ApplicationException ae)
            {
                res = BaseResponseData<string>.Failed(0, ae.Message);
                return BadRequest(res);
            }
        }
        /// <summary>
        /// 删除oa流程
        /// </summary>
        /// <param name="requestId"></param>
        /// <param name="operatorName"></param>
        /// <returns></returns>
        [HttpGet("DelWorkFlow")]
        public async Task<IActionResult> DelWorkFlow(long requestId,string operatorName)
        {
            BaseResponseData<WeaverOutput<WeaverOutputData>> res = BaseResponseData<WeaverOutput<WeaverOutputData>>.Success("操作成功！");
            try
            {
                res.Data =await weaverApiClient.DelWorkFlow(operatorName,requestId);
                return Ok(res);
            }
            catch (ApplicationException ae)
            {
                res = BaseResponseData<WeaverOutput<WeaverOutputData>>.Failed(0, ae.Message);
                return BadRequest(res);
            }
        }


    }
}
