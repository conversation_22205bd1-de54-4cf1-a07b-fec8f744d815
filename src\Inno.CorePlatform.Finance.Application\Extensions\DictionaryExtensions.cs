﻿using System.Reflection;

namespace Inno.CorePlatform.Sell.Application.Extensions
{
    public static class DictionaryExtensions
    {
        /// <summary>
        /// 字典类型转实体 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dictionary"></param>
        /// <returns></returns>
        public static T ToObject<T>(this IDictionary<string, object> dictionary) where T : new()
        {
            T obj = new T();

            foreach (KeyValuePair<string, object> kvp in dictionary)
            {
                if (kvp.Value == null)
                    continue;
                PropertyInfo propertyInfo = obj.GetType().GetProperty(kvp.Key, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly);
                if (propertyInfo != null)
                {                 
                    if (propertyInfo.PropertyType == typeof(Guid) || propertyInfo.PropertyType == typeof(Guid?))
                    {
                        Guid guidValue;
                        Guid.TryParse(kvp.Value.ToString(), out guidValue);
                        propertyInfo.SetValue(obj, guidValue, null);
                    }
                    else if (propertyInfo.PropertyType.IsEnum)
                    {
                        propertyInfo.SetValue(obj, Enum.Parse(propertyInfo.PropertyType, kvp.Value.ToString()));
                    }
                    else
                        propertyInfo.SetValue(obj, Convert.ChangeType(kvp.Value, propertyInfo.PropertyType), null);
                }
            }

            return obj;
        }
        public static Dictionary<string, object> DtoToDictionary<T>(T input) where T : class
        {
            var dict = new Dictionary<string, object>();
            var properties = typeof(T).GetProperties(); // 获取所有属性

            foreach (var prop in properties)
            {
                if (prop.CanRead && prop.GetValue(input) != null)
                {
                    // 将属性名称和值添加到字典中
                    dict.Add(prop.Name, prop.GetValue(input));
                }
            }
            return dict;
        }
    }
}
