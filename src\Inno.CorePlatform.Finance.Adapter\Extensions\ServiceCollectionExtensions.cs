using Inno.CorePlatform.Finance.Adapter.Clients.Optimized;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加金蝶API客户端服务
        /// </summary>
        public static IServiceCollection AddKingdeeApiClients(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // 配置金蝶设置选项（使用现有的 KingdeeSetting）
            services.Configure<KingdeeSetting>(
                configuration.GetSection("KingdeeSetting"));

            // 配置新的API选项（如果需要）
            services.Configure<KingdeeApiOptions>(
                configuration.GetSection(KingdeeApiOptions.SectionName));

            // 注册HttpClient with 重试策略
            services.AddHttpClient<IKingdeeFinanceClient, KingdeeFinanceClient>((serviceProvider, client) =>
            {
                // 优先使用 KingdeeSetting 配置
                var kingdeeSetting = configuration.GetSection("KingdeeSetting").Get<KingdeeSetting>();
                if (kingdeeSetting != null && !string.IsNullOrEmpty(kingdeeSetting.Host))
                {
                    client.BaseAddress = new Uri(kingdeeSetting.Host);
                    client.Timeout = TimeSpan.FromMinutes(3); // 默认3分钟超时
                }
                else
                {
                    // 回退到 KingdeeApiOptions
                    var options = configuration.GetSection(KingdeeApiOptions.SectionName).Get<KingdeeApiOptions>();
                    if (options != null)
                    {
                        client.BaseAddress = new Uri(options.BaseUrl);
                        client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);
                    }
                }
            })
            .AddPolicyHandler((serviceProvider, request) =>
            {
                var logger = serviceProvider.GetRequiredService<ILogger<KingdeeFinanceClient>>();
                return KingdeeRetryPolicy.GetCombinedPolicy(logger);
            });

            return services;
        }

        /// <summary>
        /// 添加金蝶API客户端服务（简化版本）
        /// </summary>
        public static IServiceCollection AddKingdeeApiClientsSimple(this IServiceCollection services)
        {
            services.AddScoped<IKingdeeFinanceClient, KingdeeFinanceClient>();

            // 配置HttpClient
            services.AddHttpClient<KingdeeFinanceClient>()
                .AddPolicyHandler((serviceProvider, request) =>
                {
                    var logger = serviceProvider.GetRequiredService<ILogger<KingdeeFinanceClient>>();
                    return KingdeeRetryPolicy.GetRetryPolicy(logger);
                });

            return services;
        }
    }
}
