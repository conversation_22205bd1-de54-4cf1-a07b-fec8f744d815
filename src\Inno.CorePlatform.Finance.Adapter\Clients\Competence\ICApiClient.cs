﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
    {
        public class ICApiClient : BaseDaprApiClient<ICApiClient>, IICApiClient
        {
            private readonly ILogger<ICApiClient> _logger;
            private readonly DaprClient _daprClient;

            public ICApiClient(DaprClient daprClient, ILogger<ICApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
            {
                _daprClient = daprClient;
                _logger = logger;
            }

            /// <summary>
            /// 发票信息提交接口
            /// </summary>
            public async Task<BaseResponseData<string>> ICSPDSupplierFinish(SPDInvoiceInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<SPDInvoiceInput, BaseResponseData<string>>(input, AppCenter.ICSPDSupplierFinish, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 货票同行出库的发票信息
            /// </summary>
            public async Task<BaseResponseData<string>> ICSPDWithInTemp(SPDInvoiceInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<SPDInvoiceInput, BaseResponseData<string>>(input, AppCenter.ICSPDWithInTemp, RequestMethodEnum.POST);
            }
            /// <summary>
            /// 获取SPD金额
            /// </summary>
            public async Task<BaseResponseData<List<SPDInvoiceOutput>>> ICGetSPDAmount(SPDQueryAmountInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<SPDQueryAmountInput, BaseResponseData<List<SPDInvoiceOutput>>>(input, AppCenter.ICGetSPDAmount, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 获取SPD发票详情
            /// </summary>
            public async Task<BaseResponseData<List<SpdInvoiceApplyDetailOutput>>> ICSPDInvoiceApplyDetails(SpdInvoiceApplyDetailInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<SpdInvoiceApplyDetailInput, BaseResponseData<List<SpdInvoiceApplyDetailOutput>>>(input, AppCenter.ICSPDInvoiceApplyDetails, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 提交安贞发票上报
            /// </summary>
            public async Task<BaseResponseData<string>> AnthenUploadinvoice(UploadInvoiceAZDto input)
            {
                return await InvokeMethodWithQueryObjectAsync<UploadInvoiceAZDto, BaseResponseData<string>>(input, AppCenter.AnthenUploadinvoice, RequestMethodEnum.POST);
            }
            /// <summary>
            /// 提交利群发票上报
            /// </summary>
            public async Task<BaseResponseData<string>> VanxUploadinvoice(UploadInvoiceVanxDto input)
            {
                return await InvokeMethodWithQueryObjectAsync<UploadInvoiceVanxDto, BaseResponseData<string>>(input, AppCenter.VanxUploadinvoice, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 提交华东医院发票上报
            /// </summary>
            public async Task<BaseResponseData<object>> YiDaoUploadinvoice(UploadInvoiceYiDaoDto input)
            {
                return await InvokeMethodWithQueryObjectAsync<UploadInvoiceYiDaoDto, BaseResponseData<object>>(input, AppCenter.YiDaoUploadinvoice, RequestMethodEnum.POST);
            }
            /// <summary>
            /// 获取寄售垫资明细
            /// </summary>
            /// <returns></returns>
            public async Task<BaseResponseData<BasePagedData<AdvanceCheckDetailOutput>>> ICGetAdvanceCheckDetail(AdvanceCheckDetailInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<AdvanceCheckDetailInput, BaseResponseData<BasePagedData<AdvanceCheckDetailOutput>>>(input, AppCenter.GetAdvanceCheckDetai, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 阳采发票填报
            /// </summary>
            /// <returns></returns>
            public async Task<BaseResponseData<string>> ICPushSunPurchaseInvoice(SunPurchaseDto input)
            {
                return await InvokeMethodWithQueryObjectAsync<SunPurchaseDto, BaseResponseData<string>>(input, AppCenter.IC_SubmitSunPurchaseInvoice, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 同步阳采发票状态
            /// </summary>
            /// <returns></returns>
            public async Task<BaseResponseData<List<SearchInvoiceDetailOutput>>> SyncSunPurchaseInvoiceStatus(SearchInvoiceInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<SearchInvoiceInput, BaseResponseData<List<SearchInvoiceDetailOutput>>>(input, AppCenter.IC_SyncSunPurchaseInvoiceStatus, RequestMethodEnum.POST);
            }

            /// <summary>
            /// 推送认款发票至SPD
            /// </summary>
            public async Task<BaseResponseData<string>> PushRecognizeReceive(RecognizeReceivePushSPDInput input)
            {
                return await InvokeMethodWithQueryObjectAsync<RecognizeReceivePushSPDInput, BaseResponseData<string>>(input, AppCenter.PushRecognizeReceive, RequestMethodEnum.POST);
            }
            /// <summary>
            /// 获取退回明细
            /// </summary>
            /// <typeparam name="TResponse"></typeparam>
            /// <param name="request"></param>
            /// <returns></returns>
            public async Task<GetRefundStoreInDetailOutput> GetRefundStoreInDetail(string storeInCode)
            {
                var result = await InvokeMethodAsync<BaseResponseData<GetRefundStoreInDetailOutput>>($"{AppCenter.GetRefundStoreInDetail}?storeInCode={storeInCode}", RequestMethodEnum.POST);
                return result.Data;
            }
            public async Task<BaseResponseData<List<ApplyCodeOutput>>> GetSpdApplyCodeAsync(List<string> consumeCodes)
            {
                return await InvokeMethodWithQueryObjectAsync<List<string>, BaseResponseData<List<ApplyCodeOutput>>>(consumeCodes, AppCenter.QueryApplyCodes, RequestMethodEnum.POST);
            }

            protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
            {
                var res = await _daprClient.InvokeMethodAsync<TResponse>(request);

                return res;
            }


            protected override string GetAppId()
            {
                return AppCenter.IC_APPID;
            }
        }
    }
}
