﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Data;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class DebtDetailAuditOutput
    {
        /// <summary>
        /// 预计付款日期审核明细单号
        /// </summary>
  
        public string Code { get; set; }

        /// <summary>
        /// 付款计划明细id
        /// </summary>
        public Guid? DebtDetailId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DebtDetailQueryListOutput? DebtDetail { get; set; }

        /// <summary>
        /// 预计付款日期
        /// </summary>  

        public DateTime? OriginProbablyPayTime { get; set; }

        /// <summary>
        /// 当前预计付款日期
        /// </summary>  

        public DateTime? CurrentProbablyPayTime { get; set; }

        /// <summary>
        /// OARequestId
        /// </summary>
        public string? OARequestId { get; set; }

        /// <summary>
        /// 审核状态
        /// </summary>
        public StatusEnum? Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        public string? AttachFileIds { get; set; }
        
        public string? CreatedBy { get;set; }

        public DateTimeOffset? CreatedTime { get; set; }
    }
}
