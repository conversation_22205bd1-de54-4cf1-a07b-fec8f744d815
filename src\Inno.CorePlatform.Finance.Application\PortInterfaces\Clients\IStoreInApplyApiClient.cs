﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.StoreInApply;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IStoreInApplyApiClient
    {
        Task<StoreInApplyOutputDto> GetById(Guid Id);

        Task<CustomsPaymentInfo> GetCustomsInfoById(Guid id);

        Task<StoreInApplyGetListOutputDto> GetList(StoreInApplyGetInput input);
    }
}
