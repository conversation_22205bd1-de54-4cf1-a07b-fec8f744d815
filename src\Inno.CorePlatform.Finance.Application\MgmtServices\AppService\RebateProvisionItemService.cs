﻿using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class RebateProvisionItemService : IRebateProvisionItemService
    {
        public RebateProvisionItemService() 
        {

        }
        public Task<bool> AddRebateProvisionItems(List<RebateProvisionItemPo> items)
        {
            throw new NotImplementedException();
        }
    }
}
