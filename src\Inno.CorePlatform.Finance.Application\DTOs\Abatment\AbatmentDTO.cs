﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Abatment
{
    public class AbatmentDTO
    {
        /// <summary>
        /// 冲销的单据号
        /// </summary>
        public string CreditBillCode { get; set; }

        /// <summary>
        /// 冲销的单据类型
        /// </summary>
        public string CreditType { get; set; }

        /// <summary>
        /// 被冲销的单据号
        /// </summary>
        public string DebtBillCode { get; set; }

        /// <summary>
        /// 被重冲销的单据类型
        /// </summary>
        public string DebtType { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 冲销日期
        /// </summary>
        public DateTime Abtdate { get; set; }
    }
}
