﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply
{
    /// <summary>
    /// 出库申请列表查询入参
    /// </summary>
    public class StoreOutApplyInput
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// 关联单号集合
        /// </summary>
        public List<string>? RelateCodes { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int>? ReturnApplyStatus { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int>? OutboundStatus { get; set; }
    }
}
