﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CreditRepository : EfBaseRepository<Guid, Credit, CreditPo>, ICreditRepository
    {
        private FinanceDbContext _db;
        public CreditRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override async Task<int> UpdateAsync(Credit root)
        {
            var isExist = await _db.Credits.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("不存在！");
            }
 
           var po = root.Adapt<CreditPo>();

            _db.Credits.Update(po);

            return await _db.SaveChangesAsync();
        }



        protected override CreditPo CreateDeletingPo(Guid id)
        {
            return new CreditPo { Id = id };
        }

        protected override async Task<CreditPo> GetPoWithIncludeAsync(Guid id)
        {
            return await _db.Credits.FirstOrDefaultAsync(x => x.Id == id);
        }
        public async Task<Credit> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.Credits.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<Credit>();
        }

        public async Task<bool> IsExistForBill(string relateCode)
        {
            return await _db.Credits.AnyAsync(p => p.RelateCode == relateCode);
        }

        public async Task<int> UpdateManyAsync(List<Credit> lstCredit)
        {
            var lstPo = lstCredit.Adapt<List<CreditPo>>();

            _db.Credits.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }

        public async Task<List<Credit>> GetCreditsByStoreOutCodes(List<string> storeOutCodes)
        {
            var credits = await _db.Credits.Where(p => storeOutCodes.Contains(p.RelateCode)).ToListAsync();
            return credits.Adapt<List<Credit>>();
        }

        public async Task<List<Credit>> GetCreditsByRelateCode(string relatecode)
        {
            var query = _db.Credits.Where(p => p.RelateCode == relatecode && p.Mark != 89).AsNoTracking();
            var pos = await query.ToListAsync();
            return pos.Adapt<List<Credit>>();
        }

        public async Task<bool> DeleteCredits(List<Guid> ids)
        {
            var credits = await _db.Credits.Where(p => ids.Contains(p.Id)).ToListAsync();
            _db.Credits.RemoveRange(credits);
            return true;
        }
    }
}
