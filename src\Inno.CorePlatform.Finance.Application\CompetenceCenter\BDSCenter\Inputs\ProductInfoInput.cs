﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs
{
    public class ProductInfoInput : BDSBaseInput
    {
        public string? producerId { get; set; }
        public string? productNameId { get; set; }
        public List<string>? productNameIds { get; set; }
        public List<string>? newManageScopeIds { get; set; } = new List<string>() { };
        public List<string>? oldManageScopeIds { get; set; } = new List<string>() { };
    }
   
}
