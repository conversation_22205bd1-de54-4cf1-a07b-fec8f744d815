﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System.Collections.Generic;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 订单修订
    /// </summary>
    public class SellReviseAppService : BaseAppService, ISellReviseAppService
    {
        public IBDSApiClient _bDSApiClient;
        public ISellApiClient _sellApiClient;
        private IInventoryApiClient _inventoryApiClient;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryService;
        private readonly IBaseAllQueryService<PurchasePayPlanPo> _purchasePayPlanQueryService;
        private readonly IBaseAllQueryService<DebtDetailExcutePo> _debtDetailExcuteQueryService;
        private readonly IDebtDetailRepository _debtDetailRepository;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private IProjectMgntApiClient _projectMgntApiClient;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        protected readonly DaprClient _daprClient;
        private readonly FinanceDbContext _db;
        public SellReviseAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBaseAllQueryService<DebtDetailPo> debtDetailQueryService,
            IBaseAllQueryService<PurchasePayPlanPo> purchasePayPlanQueryService,
            IBaseAllQueryService<DebtDetailExcutePo> debtDetailExcuteQueryService,
            IDebtDetailRepository debtDetailRepository,
            IKingdeeApiClient kingdeeApiClient,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            ISellApiClient sellApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
        DaprClient daprClient,
           FinanceDbContext db,
        IAppServiceContextAccessor? contextAccessor) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._sellApiClient = sellApiClient;
            this._bDSApiClient = bDSApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _debtDetailQueryService = debtDetailQueryService;
            _purchasePayPlanQueryService = purchasePayPlanQueryService;
            _debtDetailExcuteQueryService = debtDetailExcuteQueryService;
            _projectMgntApiClient = projectMgntApiClient;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _db = db;
            _daprClient = daprClient;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(input.BusinessCode);
                //订单类型：修订，没有saledetail 但是有salerevisedetail
                if (saleOut == null || !saleOut.SaleDetails.Any())
                {
                    if (!saleOut.SaleReviseDetails.Any())
                    {
                        throw new Exception("订阅订单修订事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                    else
                    {
                        return BaseResponseData<int>.Success("修订成本，无需生成应收！");
                    }
                }
                var requestBody = JsonConvert.SerializeObject(input);
                var ret = await CreateCreditForSellRevise(saleOut, input.BusinessSubType, requestBody);
                return ret;
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅暂存核销事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }

        private async Task<BaseResponseData<int>> CreateCreditForSellRevise(SaleOutput input, string classify, string preRequestBody)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            if (input != null)
            {
                var check = await base.IsCreatedCreditForBill(input.BillCode);
                if (check)
                {
                    throw new Exception("该单据已生成过应收");
                }
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { input.CompanyId.ToString() }
                })).FirstOrDefault();
                if (companyInfo != null)
                {
                    var serviceIds = input.SaleDetails.Where(p => p.ServiceId.HasValue).Select(p => p.ServiceId.Value).Distinct().ToList();
                    var services = new List<ServiceMetaOutput>();
                    if (serviceIds.Any())
                    {
                        services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                        {
                            ids = serviceIds.Select(p => p.ToString()).ToList()
                        });
                    }
                    var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                    var productNameIds = input.SaleDetails.Select(p => p.ProductNameId).Distinct().ToList();
                    var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                    var projectIds = input.SaleDetails.Select(p => p.ProjectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    var jsonStr = JsonConvert.SerializeObject(input.SaleDetails);
                    // 先根据 Mark、ServiceId、ProjectId 分组
                    var initialGroup = input.SaleDetails.GroupBy(p => new { p.BatchId, p.OriginalId, p.Mark, p.ServiceId, p.ProjectId, p.ReviseRange }).Select(x => new SaleDetail
                    {
                        ReviseRange = x.Key.ReviseRange,
                        BatchId = x.Key.BatchId,
                        OriginalId = x.Key.OriginalId,
                        Mark = x.Key.Mark,
                        ProjectId = x.Key.ProjectId,
                        ServiceId = x.Key.ServiceId,
                        Amount = x.Sum(p => p.Amount),
                        // 存储原始数据引用
                        OriginalData = x.ToList()
                    }).ToList();
                    // 重组数据
                    var result = new List<SaleDetail>();
                    var add = new List<SaleDetail>();
                    var reduce = new List<SaleDetail>();
                    foreach (var group in initialGroup)
                    {
                        if (group.Amount > 0)
                        {
                            add.Add(group);
                        }
                        else
                        {
                            reduce.Add(group);
                        }
                    }
                    if (add.Any())
                    {
                        var a = add.GroupBy(p => new { p.Mark, p.ServiceId, p.ProjectId, p.ReviseRange }).Select(x => new SaleDetail
                        {
                            ReviseRange = x.Key.ReviseRange,
                            Mark = x.Key.Mark,
                            ProjectId = x.Key.ProjectId,
                            ServiceId = x.Key.ServiceId,
                            Amount = x.Sum(p => p.Amount),
                            // 合并原始数据引用
                            OriginalData = x.SelectMany(p => p.OriginalData).ToList()
                        }).ToList();
                        result.AddRange(a);
                    }
                    if (reduce.Any())
                    {
                        var r = reduce.GroupBy(p => new { p.Mark, p.ServiceId, p.ProjectId, p.ReviseRange }).Select(x => new SaleDetail
                        {
                            ReviseRange = x.Key.ReviseRange,
                            Mark = x.Key.Mark,
                            ProjectId = x.Key.ProjectId,
                            ServiceId = x.Key.ServiceId,
                            Amount = x.Sum(p => p.Amount),
                            // 合并原始数据引用
                            OriginalData = x.SelectMany(p => p.OriginalData).ToList()
                        }).ToList();
                        result.AddRange(r);
                    }

                    var index = 1;
                    var kingdeeCredits = new List<KingdeeCredit>();
                    var relateDebtDetailCreditIds = new List<RelateDebtDetailCreditId>();
                    foreach (var g in result)
                    {
                        var thisProjectInfo = projectInfo.FirstOrDefault(p => p.Id == g.ProjectId);
                        var nlst = g.OriginalData.ToList();
                        var credit = new CreditDto
                        {
                            CompanyId = Guid.Parse(companyInfo.companyId),
                            CompanyName = companyInfo.companyName,
                            NameCode = companyInfo.nameCode,
                            AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                            Value = g.Amount.HasValue ? g.Amount.Value : 0,
                            BillCode = $"{input.BillCode}-{index.ToString().PadLeft(3, '0')}",
                            BillDate = billDate, //input.BillDate,
                            CreatedBy = input.CreatedBy ?? "none",
                            CreatedTime = DateTime.Now,
                            Mark = (int)g.Mark,
                            CreditType = (int)g.Mark == 0 || (int)g.Mark == 3 ? CreditTypeEnum.selfrevise.ToString() : CreditTypeEnum.revise.ToString(),
                            CustomerId = input.CustomerId,
                            CustomerName = input.CustomerName,
                            Id = Guid.NewGuid(),
                            InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                            ServiceId = g.ServiceId,
                            RelateCode = input.BillCode,
                            IsSureIncome = 1,
                            OrderNo = input.BillCode,
                            ShipmentCode = input.Source == SaleSourceEnum.Spd ? input.RelateCode : "",
                            BusinessDeptFullName = input.businessDeptFullName,
                            BusinessDeptFullPath = input.businessDeptFullPath,
                            BusinessDeptId = input.businessDeptId.ToString(),
                            SaleSystemId = input.SaleSystemId,
                            SaleSystemName = input.SaleSystemName,
                            HospitalId = input.HospitalId,
                            HospitalName = input.HospitalName,
                            SaleSource = input.Source,
                            Note = input.Description,
                            ProjectName = thisProjectInfo?.Name,
                            ProjectId = g.ProjectId,
                            ProjectCode = thisProjectInfo?.Code,
                            CustomerOrderCode = input.CustomerOrderCode,
                            CustomerPersonName = input.CustomerPersonName,
                            SunPurchaseRelatecode = input.SunPurchaseRelatecode,
                            AgentName = string.Join(",", nlst.Select(p => p.AgentName).Distinct().ToList()),
                            ProducerName = string.Join(",", nlst.Select(p => p.ProducerName).Distinct().ToList()),
                            PriceSource = input.PriceSourceType == PriceSourceEnum.OPERATION_APPLY ? null : input.PriceSourceType,
                            ReviseRange = g.ReviseRange,
                            //IsInternalTransactions = input.Source == SaleSourceEnum.GroupDistribution ? true : false

                        };
                        //如果是旺店通的修订，默认是给个人消费者开票 CreditSaleSubType=CreditSaleSubTypeEnum.personal
                        if (input.Source == SaleSourceEnum.Wangdian)
                        {
                            credit.CreditSaleSubType = CreditSaleSubTypeEnum.personal;
                        }

                        credit.IsSureIncomeDate = credit.BillDate;
                        if (input.OriginalSaleType.HasValue && input.OriginalSaleType == SaleTypeEnum.ServiceFee)
                        {
                            credit.CreditType = CreditTypeEnum.servicefeerevise.ToString();
                        }
                        credit.DeptName = input.DeptName;
                        if (input.TempInventoryDetails != null && input.TempInventoryDetails.Any())
                        {
                            credit.DeptName = string.Join(",", input.TempInventoryDetails.Select(p => p.deptName).Distinct());
                        }
                        credit.OriginOrderNo = input.RelateCode;
                        if (g.ServiceId.HasValue)
                        {
                            credit.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.ServiceId.ToString().ToLower())?.name;
                        }
                        #region 包装金蝶应收参数
                        var kingdeeCredit = new KingdeeCredit()
                        {
                            asstact_number1 = input.CustomerId,
                            billno = credit.BillCode,
                            billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                            bizdate = credit.BillDate.Value,
                            org_number = credit.NameCode,
                            jfzx_businessnumber = input.businessDeptId.ToString(),
                            jfzx_ordernumber = input.BillCode,
                            jfzx_iscofirm = true,
                            jfzx_creator = credit.CreatedBy ?? "none",
                            jfzx_serviceid = credit.ServiceName,
                        };

                        kingdeeCredit.jfzx_rebate = input.RebateType.HasValue;
                        var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                        var amount = 0m;//不含税总额
                        var jfzx_alltotalcost = 0m;
                        nlst.ForEach(b =>
                        {
                            var d = new KingdeeCreditDetail();
                            d.e_taxunitprice = Math.Abs(b.Price);
                            d.e_unitprice = Math.Round(d.e_taxunitprice / (1 + b.SalesTaxRate.Value / 100.00M), 10);

                            d.e_quantity = b.IndexType == -1 ? b.Quantity * -1 : b.Quantity;
                            d.salestaxrate = b.SalesTaxRate.Value;
                            var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == b.ProductNameId);
                            if (thisProductInfo != null)
                            {
                                if (thisProductInfo.classificationNewGuid.HasValue)
                                {
                                    d.e_material_number1 = thisProductInfo.classificationNewGuid.ToString();
                                }
                                else
                                {
                                    d.e_material_number1 = thisProductInfo.classificationGuid.ToString();
                                }
                            }
                            var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.ProjectId);
                            d.jfzx_projectnumber = thisProject?.Code;

                            // 成本相关字段统一处理：如果应收子类型为平台，成本相关字段设置为 0
                            var isPlatformType = credit.CreditSaleSubType.HasValue && credit.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform;

                            //d.jfzx_unitcost = 0M;//先暂时为0，后续有寄售或时需要修改
                            if ((int)b.Mark == 0 || (int)b.Mark == 3)
                            {
                                d.jfzx_unitcost = isPlatformType ? 0 : 0;
                            }
                            else
                            {
                                ///找到修订明细，取影响的成本
                                var reviseCost = 0.00m;
                                if (b.IndexType == -1)
                                {
                                    var revise = input.SaleReviseDetails.Where(t => t.SaleDetailId == b.OriginalId && t.UnitCost < 0).ToList();
                                    reviseCost = Math.Round(revise.Sum(t => (t.UnitCost / (1 + t.TaxRate.Value / 100.00M)) * t.Quantity), 2);
                                }
                                else
                                {
                                    var revise = input.SaleReviseDetails.Where(t => t.SaleDetailId == b.OriginalId && t.UnitCost > 0).ToList();
                                    reviseCost = Math.Round(revise.Sum(t => (t.UnitCost / (1 + t.TaxRate.Value / 100.00M)) * t.Quantity), 2);
                                }
                                d.jfzx_unitcost = isPlatformType ? 0 : Math.Abs(reviseCost);
                            }
                            d.jfzx_supplier = b.AgentId.ToString().ToUpper();
                            if ((int)g.Mark != 0 && (int)g.Mark != 3)//寄售货需要将标准成本传入金蝶
                            {
                                d.jfzx_standardtotal = 0;
                            }
                            if (input.RebateType.HasValue)
                            {
                                d.jfzx_rebateType = (int)input.RebateType;
                            }
                            kingdeeCreditDetails.Add(d);
                            ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                            //明细总成本：如果是平台类型，总成本也为 0
                            d.jfzx_totalcostMany = isPlatformType ? 0 : (d.jfzx_unitcost < 0 ? -Math.Abs(d.jfzx_unitcost * d.e_quantity) : Math.Abs(d.jfzx_unitcost * d.e_quantity));
                            amount += d.e_unitprice * d.e_quantity;
                            jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                        });
                        //应收不含税总额
                        kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                        //应收不含税总额
                        kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                        //总成本
                        kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                        kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                        kingdeeCredits.Add(kingdeeCredit);
                        #endregion
                        var insertRes = await base.CreateCredit(credit);
                        index++;
                        if (g.ReviseRange.HasValue && g.ReviseRange == ReviseRangeEnum.CostAndPrice)
                        {
                            relateDebtDetailCreditIds.Add(new RelateDebtDetailCreditId
                            {
                                CreditId = credit.Id,
                                OrderNo = credit.OrderNo,
                                ServicId = credit.ServiceId,
                                ProjectId = credit.ProjectId,
                            });
                        }
                    }
                    var kingdeeRes = await _kingdeeApiClient.PushCreditsToKingdee(kingdeeCredits, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        if (relateDebtDetailCreditIds.Count() > 0)
                        {
                            await RelateDebtDetailAndCreditId(relateDebtDetailCreditIds);
                        }
                        await _unitOfWork.CommitAsync();

                        await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                        {
                            CreditBillCodes = kingdeeCredits.Select(p => p.billno).ToList(),
                            RelateCodes = kingdeeCredits.Select(p => p.jfzx_ordernumber).ToList(),
                        });
                    }
                    else
                    {
                        throw new Exception("生成应收到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }
                else
                {
                    throw new Exception("未找到对应的公司");
                }
            }
            return ret;
        }

        private async Task RelateDebtDetailAndCreditId(List<RelateDebtDetailCreditId> relateDebtDetailCreditIds)
        {
            var ordernos = relateDebtDetailCreditIds.Select(p => p.OrderNo).ToHashSet();
            var servicIds = relateDebtDetailCreditIds.Select(p => p.ServicId).ToHashSet();
            var projectId = relateDebtDetailCreditIds.Select(p => p.ProjectId).ToHashSet();
            var debtDetails = await _db.DebtDetails.Include(p => p.Debt).Where(p =>
            !string.IsNullOrEmpty(p.OrderNo) &&
            ordernos.Contains(p.OrderNo) &&
            servicIds.Contains(p.Debt.ServiceId) &&
            projectId.Contains(p.Debt.ProjectId) &&
            p.Debt.ReviseRange == ReviseRangeEnum.CostAndPrice).ToListAsync();
            foreach (var item in relateDebtDetailCreditIds)
            {
                var debtDetailsTemp = debtDetails.Where(p => p.OrderNo == item.OrderNo &&
                                                             p.Debt.ServiceId == item.ServicId &&
                                                             p.Debt.ProjectId == item.ProjectId).ToList();
                foreach (var debtDetail in debtDetailsTemp)
                {
                    if (debtDetail != null)
                    {
                        debtDetail.CreditId = item.CreditId;
                        debtDetail.UpdatedTime = DateTimeOffset.UtcNow;
                        debtDetail.OrderNo = item.OrderNo;
                    }
                }
            }
        }
        public async Task<BaseResponseData<List<KingdeeCredit>>> GetSaleReviseKingdeeCreditParams(EventBusDTO dto)
        {
            var input = await _sellApiClient.GetTempSaleByCodeAsync(dto.BusinessCode);
            if (input == null || !input.SaleDetails.Any())
            {
                return BaseResponseData<List<KingdeeCredit>>.Failed(500, "未获取到上游单据");
            }
            var groupDetail = input.SaleDetails.GroupBy(p => new { p.Mark, p.ServiceId, p.ProjectId }).ToList();

            var index = 1;

            var kingdeeCredits = new List<KingdeeCredit>();
            var Ids = new List<Guid>();
            foreach (var g in groupDetail)
            {
                var credit = new CreditDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    Value = g.ToList().Sum(p => p.Quantity * p.Price),
                    BillCode = $"{input.BillCode}-{index.ToString().PadLeft(3, '0')}",
                    BillDate = input.BillDate,
                    CreatedBy = input.CreatedBy ?? "none",
                    CreatedTime = DateTime.Now,
                    Mark = (int)g.Key.Mark,
                    CreditType = (int)g.Key.Mark == 0 || (int)g.Key.Mark == 3 ? CreditTypeEnum.selfrevise.ToString() : CreditTypeEnum.revise.ToString(),
                    CustomerId = input.CustomerId,
                    CustomerName = input.CustomerName,
                    Id = Guid.NewGuid(),
                    InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                    ServiceId = g.Key.ServiceId,
                    RelateCode = input.BillCode,
                    IsSureIncome = 1,
                    //IsSureIncomeDate = DateTime.Now,
                    OrderNo = input.BillCode,
                    ShipmentCode = input.Source == SaleSourceEnum.Spd ? input.RelateCode : "",
                    BusinessDeptFullName = input.businessDeptFullName,
                    BusinessDeptFullPath = input.businessDeptFullPath,
                    BusinessDeptId = input.businessDeptId.ToString(),
                    SaleSystemId = input.SaleSystemId,
                    SaleSystemName = input.SaleSystemName,
                    HospitalId = input.HospitalId,
                    HospitalName = input.HospitalName,
                    SaleSource = input.Source,
                    Note = input.Description,
                    CustomerOrderCode = input.CustomerOrderCode,
                    CustomerPersonName = input.CustomerPersonName,
                    AgentName = string.Join(",", g.Select(p => p.AgentName).Distinct().ToList()),
                    ProducerName = string.Join(",", g.Select(p => p.ProducerName).Distinct().ToList()),
                };
                //如果是旺店通的修订，默认是给个人消费者开票 CreditSaleSubType=CreditSaleSubTypeEnum.personal
                if (input.Source == SaleSourceEnum.Wangdian)
                {
                    credit.CreditSaleSubType = CreditSaleSubTypeEnum.personal;
                }
                credit.IsSureIncomeDate = credit.BillDate;
                credit.DeptName = input.DeptName;
                if (input.TempInventoryDetails != null && input.TempInventoryDetails.Any())
                {
                    credit.DeptName = string.Join(",", input.TempInventoryDetails.Select(p => p.deptName).Distinct());
                }
                //if (!string.IsNullOrEmpty(input.RelateCode) && (input.RelateCode.Contains("-PSA-") || input.SaleType == SaleTypeEnum.SaleForB)) //原始订单号
                {
                    credit.OriginOrderNo = input.RelateCode;
                }
                Ids.Add(credit.Id);
                #region 包装金蝶应收参数
                var kingdeeCredit = new KingdeeCredit()
                {
                    asstact_number1 = input.CustomerId,
                    billno = credit.BillCode,
                    billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                    bizdate = credit.BillDate.Value,
                    org_number = credit.NameCode,
                    jfzx_businessnumber = input.businessDeptId.ToString(),
                    jfzx_ordernumber = input.BillCode,
                    jfzx_iscofirm = true,
                    jfzx_creator = credit.CreatedBy ?? "none",
                    jfzx_serviceid = credit.ServiceName,
                };

                kingdeeCredit.jfzx_rebate = input.RebateType.HasValue;
                var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                var amount = 0m;//不含税总额
                var jfzx_alltotalcost = 0m;
                g.ToList().ForEach(b =>
                {
                    var d = new KingdeeCreditDetail();
                    d.e_taxunitprice = Math.Abs(b.Price);
                    d.e_unitprice = Math.Round(d.e_taxunitprice / (1 + b.SalesTaxRate.Value / 100.00M), 10);

                    d.e_quantity = b.IndexType == -1 ? b.Quantity * -1 : b.Quantity;
                    d.salestaxrate = b.SalesTaxRate.Value;

                    // 成本相关字段统一处理：如果应收子类型为平台，成本相关字段设置为 0
                    var isPlatformType = credit.CreditSaleSubType.HasValue && credit.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform;

                    //d.jfzx_unitcost = 0M;//先暂时为0，后续有寄售或时需要修改
                    if ((int)b.Mark == 0 && (int)b.Mark == 3)
                    {
                        d.jfzx_unitcost = isPlatformType ? 0 : 0;
                    }
                    else
                    {
                        ///找到修订明细，取影响的成本
                        var reviseCost = 0.00m;
                        if (b.IndexType == -1)
                        {
                            var revise = input.SaleReviseDetails.Where(t => t.SaleDetailId == b.OriginalId && t.UnitCost < 0).ToList();
                            reviseCost = Math.Round(revise.Sum(t => (t.UnitCost / (1 + t.TaxRate.Value / 100.00M)) * t.Quantity), 2);
                        }
                        else
                        {
                            var revise = input.SaleReviseDetails.Where(t => t.SaleDetailId == b.OriginalId && t.UnitCost > 0).ToList();
                            reviseCost = Math.Round(revise.Sum(t => (t.UnitCost / (1 + t.TaxRate.Value / 100.00M)) * t.Quantity), 2);
                        }
                        d.jfzx_unitcost = isPlatformType ? 0 : reviseCost;
                    }
                    d.jfzx_supplier = b.AgentId.ToString().ToUpper();
                    if ((int)g.Key.Mark != 0 && (int)g.Key.Mark != 3)//寄售货需要将标准成本传入金蝶
                    {
                        d.jfzx_standardtotal = 0;
                    }
                    if (input.RebateType.HasValue)
                    {
                        d.jfzx_rebateType = (int)input.RebateType;
                    }
                    kingdeeCreditDetails.Add(d);
                    ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                    //明细总成本：如果是平台类型，总成本也为 0
                    d.jfzx_totalcostMany = isPlatformType ? 0 : d.jfzx_unitcost * d.e_quantity;
                    amount += d.e_unitprice * d.e_quantity;
                    jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                });
                //应收不含税总额
                kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                //应收不含税总额
                kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                //总成本
                kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                kingdeeCredits.Add(kingdeeCredit);
                #endregion
                index++;
            }
            return new BaseResponseData<List<KingdeeCredit>>()
            {
                Code = CodeStatusEnum.Success,
                Data = kingdeeCredits
            };
        }
    }
}
