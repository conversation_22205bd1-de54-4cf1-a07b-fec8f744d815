﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class StoreOutWithoutFinanceAppService : BaseAppService, IStoreOutWithoutFinanceAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly ILogger<StoreOutWithoutFinanceAppService> _logger;

        public StoreOutWithoutFinanceAppService(ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork unitOfWork,
            IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IKingdeeApiClient kingdeeApiClient,
            ILogger<StoreOutWithoutFinanceAppService> logger,
            Func<int, TimeSpan> sleepDurationProvider = null
            )
            : base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            _inventoryApiClient = inventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
            _logger = logger;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreOutOutput storeout = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);

                    if (storeout == null || !storeout.Details.Any())
                    {
                        _logger.LogError($"订阅出库事件:{input.BusinessCode}-查询上游单据时未获取到相关数据");
                        throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                    if (string.IsNullOrEmpty(storeout.checker))
                    {
                        _logger.LogError($"订阅出库事件:{input.BusinessCode}-该单据没有复核人");
                        throw new Exception("该单据没有复核人");
                    }
                });
                if (storeout != null)
                {
                    var requestBody = JsonConvert.SerializeObject(input);
                    _logger.LogInformation($"订阅出库事件:{input.BusinessCode}-推送金蝶暂存出库参数:{requestBody}");
                    var ret = await PushToKingdee(new List<InventoryStoreOutOutput> { storeout }, input.BusinessSubType, requestBody, input.useBillDate, input.IsAutoBill);
                    _logger.LogInformation($"订阅出库事件:{input.BusinessCode}-推送金蝶暂存出库返回结果:{JsonConvert.SerializeObject(ret)}");
                    return ret;
                }
                else
                {
                    _logger.LogError($"订阅出库事件:{input.BusinessCode}-【库存】没有找到出库数据");
                    throw new Exception("【库存】没有找到出库数据");
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"订阅出库事件异常:{input.BusinessCode}-{ex.Message}");
                throw;
                //throw new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }

        private async Task<BaseResponseData<int>> PushToKingdee(
            List<InventoryStoreOutOutput> inputs,
            string classify,
            string preRequestBody,
            bool? useBillDate = false,
            bool? isAutoBill = false)
        {
            if (inputs == null || !inputs.Any()) return BaseResponseData<int>.Failed(500, "订阅入库事件出错，原因：未找到出库单数据");
            List<HoldStockRemovalInput> kdinputs = new List<HoldStockRemovalInput>();
            foreach (var input in inputs)
            {
                var inputKD = new HoldStockRemovalInput()
                {
                    billno = input.storeOutCode,
                    jfzx_date = DateTimeHelper.LongToDateTime(input.billDate),
                    jfzx_tallydate = DateTimeHelper.LongToDateTime(input.billDate),
                    jfzx_supplier = input.agentId?.ToString().ToUpper(),
                    jfzx_customer = input.customerId?.ToString().ToUpper(),
                    org = input.companyName,
                    jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                    jfzx_remake = input.remark ?? "无",
                    jfzx_creator = input.createdBy ?? "none",
                    StoreOutType = input.storeOutType,
                };
                var sysMonth = await _bDSApiClient.GetSystemMonth(input.companyId.ToString());
                DateTime.TryParse(sysMonth, out DateTime billDate);
                inputKD.jfzx_tallydate = billDate;

                if (!string.IsNullOrEmpty(input.signSysMonth))
                {
                    inputKD.jfzx_tallydate = DateTime.Parse(input.signSysMonth);
                }
                var fk_jfzx_totalsalescost = 0m;
                if (input.Details != null && input.Details.Any())
                {
                    var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                    var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                    inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
                    foreach (var detail in input.Details)
                    {
                        var thisProject = projectInfo.FirstOrDefault(t => t.Id == detail.projectId);
                        var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == detail.productNameId);
                        var jfzx_material = Guid.Empty;
                        if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                        {
                            jfzx_material = thisProductInfo.classificationNewGuid.Value;
                        }
                        else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                        {
                            jfzx_material = thisProductInfo.classificationGuid.Value;
                        }
                        var taxCost = detail.mark == 0 || detail.mark == 3 ? detail.unitCost.Value : detail.standardUnitCost.Value;
                        //var taxCost = detail.unitCost.Value;
                        var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                        //销售成本总额
                        var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                        if (detail.mark == 0 || detail.mark == 3) //mark=0 经销 mark=3 集团经销
                        {
                            fk_jfzx_totalsalescost += noTaxCost10 * detail.quantity;
                        }
                        else
                        {
                            fk_jfzx_totalsalescost += detail.standardUnitCost.Value * detail.quantity;
                        }
                        var detailInfo = new HoldStockRemovalDetail
                        {
                            jfzx_count = detail.quantity,
                            jfzx_material = jfzx_material.ToString().ToUpper(),
                            jfzx_model = detail.specification,
                            jfzx_projectnos = thisProject?.Code,
                            jfzx_suppliers = detail.agentId.Value.ToString().ToUpper(),
                            Mark = detail.mark,
                            jfzx_unitprice = detail.mark == 0 || detail.mark == 3 ? noTaxCost : detail.standardUnitCost.Value,
                        };
                        detailInfo.jfzx_sellingcost = detail.quantity * detailInfo.jfzx_unitprice;
                        if (detailInfo.jfzx_sellingcost != 0)
                        {
                            inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                        }
                    }
                    if (inputKD.holdStockRemovalEntrysModel.Count > 0)
                    {
                        kdinputs.Add(inputKD);
                    }
                }
                inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
            }
            if (kdinputs.Any())
            {
                var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(kdinputs, classify, preRequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, kingdeeRes.Message);
                }
            }
            //else
            //{
            //    return BaseResponseData<int>.Failed(500, "【库存】没有找到出库数据");
            //}
            return BaseResponseData<int>.Success("操作成功");

        }

        public async Task<BaseResponseData<int>> SignShipment(string tempStoreOutCodes)
        {
            BaseResponseData<int> ret = BaseResponseData<int>.Failed(500, "操作失败");
            if (!string.IsNullOrEmpty(tempStoreOutCodes))
            {
                var storeOutCodes = tempStoreOutCodes.Split(',').Distinct().ToList();
                var storeouts = new List<InventoryStoreOutOutput>();
                foreach (var storeOutCode in storeOutCodes)
                {
                    var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                    InventoryStoreOutOutput storeout = null;
                    await retryPolicy.ExecuteAsync(async () =>
                    {
                        storeout = await _inventoryApiClient.QueryStoreOutByCode(storeOutCode);

                        if (storeout == null || !storeout.Details.Any())
                        {
                            throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                        }
                        if (string.IsNullOrEmpty(storeout.checker))
                        {
                            throw new Exception("该单据没有复核人");
                        }
                    });
                    if (storeout != null)
                    {
                        storeouts.Add(storeout);
                    }
                }
                ret = await PushToKingdee(storeouts, "出库签收", tempStoreOutCodes);
            }
            return ret;

        }

        public async Task<BaseResponseData<HoldStockRemovalInput>> GetTempStoreOutKingdeeParams(string storeOutCode)
        {
            var input = await _inventoryApiClient.QueryStoreOutByCode(storeOutCode);

            if (input == null || !input.Details.Any())
            {
                return BaseResponseData<HoldStockRemovalInput>.Failed(500, "未查询到出库数据");
            }
            var inputKD = new HoldStockRemovalInput()
            {
                billno = input.storeOutCode,
                jfzx_date = DateTimeHelper.LongToDateTime(input.billDate),
                jfzx_tallydate = DateTimeHelper.LongToDateTime(input.billDate),
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = input.companyName,
                jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = input.createdBy ?? "none",
                StoreOutType = input.storeOutType
            };
            var sysMonth = await _bDSApiClient.GetSystemMonth(input.companyId.ToString());
            DateTime.TryParse(sysMonth, out DateTime billDate);
            inputKD.jfzx_tallydate = billDate;

            if (!string.IsNullOrEmpty(input.signSysMonth))
            {
                inputKD.jfzx_tallydate = DateTime.Parse(input.signSysMonth);
            }
            var fk_jfzx_totalsalescost = 0m;
            if (input.Details != null && input.Details.Any())
            {
                var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
                foreach (var detail in input.Details)
                {
                    var thisProject = projectInfo.FirstOrDefault(t => t.Id == detail.projectId);
                    var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == detail.productNameId);
                    var jfzx_material = Guid.Empty;
                    if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationNewGuid.Value;
                    }
                    else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationGuid.Value;
                    }
                    var taxCost = detail.mark == 0 || detail.mark == 3 ? detail.unitCost.Value : detail.standardUnitCost.Value;
                    //var taxCost = detail.unitCost.Value;
                    var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                                                                                                  //销售成本总额
                    var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                    if (detail.mark == 0 || detail.mark == 3)
                    {
                        fk_jfzx_totalsalescost += noTaxCost10 * detail.quantity;
                    }
                    else
                    {
                        fk_jfzx_totalsalescost += detail.standardUnitCost.Value * detail.quantity;
                    }
                    var detailInfo = new HoldStockRemovalDetail
                    {
                        jfzx_count = detail.quantity,
                        jfzx_material = jfzx_material.ToString().ToUpper(),
                        jfzx_model = detail.specification,
                        jfzx_projectnos = thisProject?.Code,
                        jfzx_suppliers = detail.agentId.Value.ToString().ToUpper(),
                        Mark = detail.mark,
                        jfzx_unitprice = detail.mark == 0 || detail.mark == 3 ? noTaxCost : detail.standardUnitCost.Value,
                    };
                    detailInfo.jfzx_sellingcost = detail.quantity * detailInfo.jfzx_unitprice;
                    inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                }

                inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
                return new BaseResponseData<HoldStockRemovalInput>()
                {
                    Code = CodeStatusEnum.Success,
                    Data = inputKD
                };
            }
            else
            {
                return new BaseResponseData<HoldStockRemovalInput>()
                {
                    Code = CodeStatusEnum.Failed,
                    Data = inputKD
                };
            }
        }
    }
}
