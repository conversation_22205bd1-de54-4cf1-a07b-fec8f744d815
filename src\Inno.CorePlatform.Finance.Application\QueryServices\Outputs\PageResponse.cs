﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 分页Response
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PageResponse<T> where T :  class
    {
        /// <summary>
        /// 数据
        /// </summary>
        public List<T>? List { get; set; }
        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
    }
}
