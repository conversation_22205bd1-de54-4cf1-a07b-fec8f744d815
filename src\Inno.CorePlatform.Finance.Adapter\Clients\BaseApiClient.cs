﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Purchase.Adapter.Clients
{
    public abstract class BaseApiClient<T>
    {
        protected readonly ILogger<T> _logger;
        protected BaseApiClient(ILogger<T> logger)
        {
            _logger = logger;
        }
    }
}
