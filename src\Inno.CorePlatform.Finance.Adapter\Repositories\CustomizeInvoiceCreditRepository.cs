﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CustomizeInvoiceCreditRepository : EfBaseRepository<Guid, CustomizeInvoiceCredit, CustomizeInvoiceCreditPo>, ICustomizeInvoiceCreditRepository
    {
        private FinanceDbContext _db;
        public CustomizeInvoiceCreditRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> DeleteByCode(string customizeInvoiceItemCode)
        {
            var customizeInvoiceCredits = _db.CustomizeInvoiceCredits.Where(p => p.CustomizeInvoiceItemCode == customizeInvoiceItemCode).ToList();
            if (customizeInvoiceCredits != null && customizeInvoiceCredits.Any())
            {
                _db.CustomizeInvoiceCredits.RemoveRange(customizeInvoiceCredits);
            }
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        public async Task<int> AddManyAsync(List<CustomizeInvoiceCredit> inputs)
        {
            if (inputs == null)
            {
                throw new Exception("数据不能为空！");
            }
            var customizes = inputs.Adapt<List<CustomizeInvoiceCreditPo>>();
            await _db.CustomizeInvoiceCredits.AddRangeAsync(customizes);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        public override Task<int> UpdateAsync(CustomizeInvoiceCredit root)
        {
            throw new NotImplementedException();
        }

        protected override CustomizeInvoiceCreditPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected async override Task<CustomizeInvoiceCreditPo> GetPoWithIncludeAsync(Guid id)
        {
            var po = await _db.CustomizeInvoiceCredits.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po;
        }

        Task<CustomizeInvoiceCredit?> IRepositorySupportCrud<CustomizeInvoiceCredit, Guid>.GetAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
