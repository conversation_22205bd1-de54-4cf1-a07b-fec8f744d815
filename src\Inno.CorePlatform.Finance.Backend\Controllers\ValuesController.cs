﻿using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ValuesController : ControllerBase
    {
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IAbtmentService _abtmentService;
        private ICheckDataWithKingdeeService _checkDataWithKingdeeService;
        public ValuesController(IKingdeeApiClient kingdeeApiClient, IEasyCachingProvider easyCaching, IAbtmentService abtmentService, ICheckDataWithKingdeeService checkDataWithKingdeeService)
        {
            _kingdeeApiClient = kingdeeApiClient;
            _easyCaching = easyCaching;
            _abtmentService = abtmentService;
            _checkDataWithKingdeeService = checkDataWithKingdeeService;
        }

        [HttpGet("getkingdeeapptoken")]
        public async Task<string> Test()
        {
            await _kingdeeApiClient.Test();
            return "";
        }

        //[HttpGet]
        //public async IEnumerable<string> Get()
        //{           
        //    return new string[] { "value6", "value2" };
        //}

        [HttpGet("testAbtForDebt")]
        public async Task<BaseResponseData<bool>> TestAbtForDebt()
        {
            List<GenerateAbtForDebtInput> lstInput = new List<GenerateAbtForDebtInput>();
            GenerateAbtForDebtInput input = new GenerateAbtForDebtInput();
            input.Value = 25.1m;
            input.BillCode = "ZXBD-ZZ-SO-2308-000016-001";
            lstInput.Add(input);

            GenerateAbtForDebtInput input2 = new GenerateAbtForDebtInput();
            input2.Value = 1000;
            input2.BillCode = "ZXBD-ZZ-SO-2309-000007-001";
            lstInput.Add(input2);

            Guid debtId = Guid.Parse("7B231F95-2DA9-4D2F-B20E-51C29B2E0AEB");

            var res = await _abtmentService.GenerateAbtForDebtAsync(debtId, lstInput, "suxin",true);
            if (res > 0)
            {
                return BaseResponseData<bool>.Success("冲销完成");
            }
            else
            {
                return BaseResponseData<bool>.Failed(0, "冲销失败");
            }

        }
        [HttpGet("testIncomeIsConfirm")]
        public async Task<BaseResponseData<int>> TestIncomeIsConfirm()
        {
            var res = await _kingdeeApiClient.PushIncomeIsCofirm(new Application.DTOs.KingdeeIncomeIsConfirm {  jfzx_iscofirm = true });
            return res;
        }

        [HttpPost("CheckDataWithKingdee")]
        public async Task<BaseResponseData<CheckDataWithKingdeeRes>> CheckDataWithKingdee(CheckDataWithKingdeeInputDto input)
        {
            var ret = await _checkDataWithKingdeeService.GetTempStoreOutDataAsync(input);
            return ret;
        }

        [HttpPost("CheckBillByBill")]
        public async Task<BaseResponseData<List<CheckByBillOutput>>> CheckBillByBill(CheckByBillInput checkByBillInput)
        {
            var ret = await _checkDataWithKingdeeService.CheckBillByBill(checkByBillInput);
            return ret;
        }
    }
}
