﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using NPOI.POIFS.Crypt.Dsig.Facets;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IAdvanceBusinessQueryService
    {
        public Task<(List<AdvanceBusinessOutput>, int)> GetAdvanceList(AdvanceBusinessInput query);

        public Task<(List<AdvanceBusinessDetailOutput>, int)> GetAdvanceDetails(AdvanceBusinessInput input);

        public Task<List<AdvanceFundBusinessDetailGroupOutput>> GetAdvanceDetailGroup(Guid advanceBusinessApplyId);
    }
}
