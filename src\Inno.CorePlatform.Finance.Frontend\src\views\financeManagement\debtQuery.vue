<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-debtQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>应付清单</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" :permission="crudPermission" :hiddenColumns="[]" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="searchCrud" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane split="horizontal" :default-percent="60">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" :hiddenColumns="[]" hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.abatedStatus" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`未冲销(${tabCount.nonAbatedCount})`" name="0" lazy />
                <el-tab-pane :label="`已冲销(${tabCount.abatedCount})`" name="1" lazy />
                <el-tab-pane :label="`全部(${tabCount.allCount})`" name="-1" lazy />
              </el-tabs>
            </template>
            <template #default>
              <!-- v-auth="functionUris.confirmAbatement" -->
              <inno-button-tooltip
                v-if="hasPermission(functionUris.confirmAbatement)"
                :disabled="crud.rowData?.abatedStatus!==0"
                type="primary"
                :ms-disabled="crud.rowDisabled"
                @click="abatementClick"
              >进行冲销</inno-button-tooltip>
              <inno-button-tooltip
                v-if="hasPermission(functionUris.cancelAbatement)"
                :disabled="!crud.rowData?.abatmentAmount>0"
                type="primary"
                :ms-disabled="crud.rowDisabled"
                @click="cancelAbatementClick"
              >撤销冲销</inno-button-tooltip>
              <!-- v-auth="functionUris.export" -->
              <inno-button-tooltip
                type="primary"
                icon="Download"
                v-if="hasPermission(functionUris.export)"
                :loading="downLoading"
                @click="
                  downloadAsync(
                    'api/DebtQuery/debt/export',
                    '应付清单',
                    crud.query
                  )
                "
              >导出数据</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef0"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crud.data"
            stripe
            show-summary
            :summary-method="getSummaries"
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="crud.selectionChangeHandler"
            @row-click="(e)=>{
              getDetailData(e)
            }"
          >
            <el-table-column type="selection" fixed="left" width="55"></el-table-column>
            <!-- <el-table-column fixed="left" width="55">
              <template #default="scope">
                <inno-table-checkbox
                  :checked="scope.row.id === crud.rowData.id"
                />
              </template>
            </el-table-column>-->
            <el-table-column property="billCode" label="应付单号" width="200" fixed="left" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="单据日期" property="billDate" width="120" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billDateS" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{
                scope.row.billDate === null
                ? ''
                : dateFormat(scope.row.billDate, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column label="公司" property="company" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="供应商" property="agents" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="核算部门" property="businessDeptFullName" width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.businessDeptFullName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="采购单号" property="purchaseCode" width="190" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.purchaseCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="采购合同单号" property="purchaseContactNo" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseContactNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目编号" property="projectCode" width="190" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="厂家单号" property="producerOrderNo" width="190" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.producerOrderNo" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.producerOrderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="关联单号" property="relateCode" width="190" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.relateCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detail(scope.row.relateCode)">{{ scope.row.relateCode }}</el-link>
                </inno-button-copy>
              </template>
            </el-table-column>
             <el-table-column label="进项票发票号" property="invoiceCode" width="190" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.invoiceCode" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detail(scope.row.invoiceCode)">{{ scope.row.invoiceCode }}</el-link>
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="币种" width="80" property="CoinName" show-overflow-tooltip sortable>
              <template #default="scope">{{ scope.row.coinName }}</template>
            </el-table-column>
            <el-table-column class-name="isSum" label="币种金额" width="100" property="value" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="人民币金额" width="120" property="rmbAmount" class-name="isSum" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.rmbAmount" format="0,0.00" />
              </template>
            </el-table-column>

            <el-table-column class-name="isSum" label="已冲销" property="abatmentAmount" width="120">
              <template #default="scope">
                <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="余额" property="leftAmount" class-name="isSum" width="120">
              <template #default="scope">
                <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="业务单元" property="service" width="100" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.servicesId" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="应付类型" property="debtType" width="100" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.debtType" :crud="crud" :column="column" />
              </template>
              <template #default="scope">
                {{
                scope.row.debtType === null
                ? ''
                : DebtTypeEnum[scope.row.debtType - 1]?.name
                }}
              </template>
            </el-table-column>
            <el-table-column label="确认损失" width="80" property="lossAgentBearValue" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.lossAgentBearValue!=null&&scope.row.lossAgentBearValue!=0?'是':'否' }}</template>
            </el-table-column>
            <el-table-column label="确认损失金额" width="120" property="lossAgentBearValue" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.lossAgentBearValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="90" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
              </template>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <span style="margin-left: 20px">
              金额：
              <inno-numeral :value="selectionValue" format="0,0.00" />
            </span>
            <div class="flex-1"></div>
            <inno-crud-pagination :crud="crud" />
          </div>
        </template>
        <template #paneR="{ full, onFull }"> 
          <inno-crud-operation style="padding: 0px" rightAdjust hidden-opts-right>
            <template #opts-left>
              <el-tabs v-model="setDetailTab" @tab-change="tabDetailActiveClick">
                <el-tab-pane :label="`付款计划`" name="paymentPlan"></el-tab-pane>
                <el-tab-pane :label="`执行明细`" name="executionDetail"></el-tab-pane>
                <el-tab-pane :label="`进项发票明细`" name="invoiceDetail"></el-tab-pane>
                <el-tab-pane :label="`冲销明细`" name="abatementDetail" v-if="crud.rowData.value < 0"></el-tab-pane>
              </el-tabs>
            </template>

            <template #right>
              <el-button v-if="setDetailTab === 'paymentPlan'&&isShowApplyPayTime===true" type="primary" @click="openOverdueClick">
                <!-- dialogAddRemark = true -->
                预计付款日期调整
              </el-button>
              <el-button v-if="setDetailTab === 'paymentPlan'&&isShowApplyPayTime===true" type="primary" @click="viewApprovalProcess">
                <!-- dialogAddRemark = true -->
                调整审批过程
              </el-button>

              <el-button type="primary" @click="onFull">
                <inno-svg-Icon class="icon" :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" />
              </el-button>
            </template>
          </inno-crud-operation>
          <!-- 付款计划 -->
          <el-table ref="tableRef1"
                    v-inno-loading="crud1.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    v-if="setDetailTab === 'paymentPlan'"
                    border
                    show-summary
                    :summary-method="getSummaries"
                    :data="crud1.data"
                    :row-class-name="crud1.tableRowClassName"
                    stripe
                    row-key="id"
                    :expand-row-keys="expands"
                    @row-click="clickRowHandle"
                    @selection-change="handleAllSelectionChange"> 
            <el-table-column fixed="left" width="50">
              <template #default="scope">
                <inno-table-checkbox :checked="scope.row.id === allDetailSelection[0]?.id" />
              </template>
            </el-table-column>
            <el-table-column label="账期类型" property="accountPeriodTypeStr" width="80" show-overflow-tooltip></el-table-column>
            <el-table-column label="预计付款日期" property="probablyPayTime" width="120" show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.probablyPayTime, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column label="收款单号" property="receiveCode" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.receiveCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="原始金额" property="originValue" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.originValue" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="基础折扣" property="distributionDiscount" show-overflow-tooltip />
            <el-table-column label="供应链金融折扣" property="financeDiscount" width="120" show-overflow-tooltip />
            <el-table-column label="SPD折扣" property="spdDiscount" show-overflow-tooltip />
            <el-table-column label="税率折扣" property="taxDiscount" show-overflow-tooltip />
            <el-table-column label="厂家折扣" property="costDiscount" show-overflow-tooltip />
            <el-table-column class-name="isSum" label="付款金额" property="value" min-width="80" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customer" width="130" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">
                  {{
                  scope.row.credit != null &&
                  scope.row.credit.customerName != null
                  ? scope.row.credit.customerName
                  : ''
                  }}
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="终端医院" property="hospitalName" show-overflow-tooltip />
            <el-table-column label="状态" property="status" width="80" show-overflow-tooltip>
              <template #default="scope">
                {{
                scope.row.status === null
                ? ''
                : scope.row.status === 0
                ? '待执行'
                : scope.row.status === 99
                ? '已完成'
                : ''
                }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button type="primary" text>展开明细</el-button>
                <el-button v-if="
                    (scope.row.accountPeriodType !== 0||scope.row.accountPeriodType !== 3) &&
                    scope.row.status !== 99
                  "
                           type="primary"
                           text
                           @click="splitDebtDetail(scope.row.id)">拆分</el-button>
              </template>
            </el-table-column>
            <el-table-column type="expand" width="1">
              <template #default>
                <el-table ref="tableRef4"
                          v-inno-loading="crud4.loading"
                          :row-class-name="crud4.tableRowClassName"
                          class="auto-layout-table"
                          highlight-current-row
                          border
                          :data="crud4.data"
                          stripe
                          style="padding: 10px">
                  <el-table-column label="计划单号" property="code" width="210" show-overflow-tooltip>
                    <template #default="scope_det">
                      <inno-button-copy :link="false">{{ scope_det.row.code }}</inno-button-copy>
                    </template>
                  </el-table-column>
                  <el-table-column label="账期类型" property="accountPeriodTypeStr" width="90" show-overflow-tooltip></el-table-column>
                  <el-table-column label="付款时间" property="paymentDate" width="120" show-overflow-tooltip>
                    <template #default="scope_det">
                      {{
                      dateFormat(scope_det.row.paymentDate, 'YYYY-MM-DD')
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column label="付款单号" property="paymentCode" width="470" show-overflow-tooltip>
                    <template #default="scope_det">
                      <inno-button-copy :link="false">{{ scope_det.row.paymentCode }}</inno-button-copy>
                    </template>
                  </el-table-column>

                  <el-table-column class-name="isSum" label="付款金额" property="value" show-overflow-tooltip>
                    <template #default="scope_det">
                      {{ scope_det.row.value }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
          </el-table>
          <!-- 执行明细 -->
          <el-table ref="tableRef2"
                    v-inno-loading="crud2.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    v-if="setDetailTab === 'executionDetail'"
                    border
                    show-summary
                    :summary-method="getSummaries"
                    :data="crud2.data"
                    height="100%"
                    stripe
                    :row-class-name="crud2.tableRowClassName"
                    @selection-change="crud2.selectionChangeHandler"
                    @row-click="crud2.rowClick">
            <el-table-column label="计划单号" property="code" width="220" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.code }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="账期类型" property="accountPeriodTypeStr" width="90" show-overflow-tooltip></el-table-column>
            <el-table-column label="付款时间" property="paymentDate" show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.paymentDate, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column label="付款单号" property="paymentCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.paymentCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="付款金额" property="value" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
          </el-table>
          <!-- 进项发票明细 -->
          <el-table ref="tableRef5"
                    v-inno-loading="crud5.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    border
                    show-summary
                    :summary-method="getSummaries"
                    :data="crud5.data"
                    height="100%"
                    stripe
                    v-if="setDetailTab === 'invoiceDetail'"
                    :row-class-name="crud5.tableRowClassName"
                    @selection-change="crud5.selectionChangeHandler"
                    @row-click="crud5.rowClick">
            <el-table-column prop="invoiceNumber" label="发票号" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="companName" label="公司名称" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="agentName" label="供应商名称" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.agentName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="billTime" label="开票时间" min-width="110">
              <template #default="scope">
                {{
                scope.row.billTime === null
                ? ''
                : dateFormat(scope.row.billTime, 'YYYY-MM-DD')
                }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="票据类型" />
            <el-table-column show-overflow-tooltip prop="createdTime" label="创建时间" min-width="110">
              <template #default="scope">
                {{ dateFormat(scope.row.createdTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="invoiceCode" label="发票代码" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.invoiceCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="purchaseDutyNumber" label="购买方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.purchaseDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="saleDutyNumber" label="销售方税号" min-width="130" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleDutyNumber }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column prop="statusName" label="发票状态" />

            <el-table-column sortable prop="notaxAmount" label="金额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.notaxAmount" format="0,0.0000" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="taxAmount" label="税额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column sortable prop="amount" label="总金额(元)" min-width="90">
              <template #default="scope">
                <inno-numeral :value="scope.row.amount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="110" :show-overflow-tooltip="true">
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.remark }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="70" fixed="right">
              <template #default="scope">
                <el-link v-if="scope.row.invoiceNumber"
                         style="font-size: 12px"
                         type="primary"
                         @click.stop="
                          downloadFile(scope.row.invoiceNumber, scope.row.invoiceCode)
                        ">查看附件</el-link>
              </template>
            </el-table-column>
          </el-table>
          <!--冲销明细-->
          <el-table ref="tableRef6"
                    v-inno-loading="crud6.loading"
                    class="auto-layout-table"
                    highlight-current-row
                    v-if="setDetailTab === 'abatementDetail'"
                    border
                    show-summary
                    :summary-method="getSummaries"
                    :data="crud6.data"
                    height="100%"
                    stripe
                    :row-class-name="crud6.tableRowClassName"
                    @selection-change="crud6.selectionChangeHandler"
                    @row-click="crud6.rowClick">
            <el-table-column label="应付单号" property="code" width="220" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ crud.rowData.billCode===scope.row.debtBillCode? scope.row.debtBillCode:scope.row.creditBillCode }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="被冲销的单号" property="creditBillCode" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ crud.rowData.billCode===scope.row.debtBillCode? scope.row.creditBillCode:scope.row.debtBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="冲销日期" property="abtdate" show-overflow-tooltip>
              <template #default="scope">
                {{ dateFormat(scope.row.abtdate, 'YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column class-name="isSum" label="冲销金额" property="value" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
          </el-table>
        </template>
      </inno-split-pane>
    </div>
    <el-dialog v-model="dialogVisible" title="应付冲销" width="1300px" @close="debtCancel" draggable>
      <el-form label-width="120px" class="demo-form-inline" :inline="true">
        <el-form-item label="应付/应收/收款单号">
          <el-input v-model="debtNoOfSearch" style="width: 240px" />
        </el-form-item>
        <el-form-item label="客户">
          <el-input v-model="debtCustomerSearch" style="width: 240px" />
        </el-form-item>
        <el-form-item label="单据日期">
          <el-date-picker
          style="width: 280px" 
            v-model="debtDateSearch"
            type="daterange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchDebt">查询</el-button>
        </el-form-item>
      </el-form>
      <el-tabs @tab-click="searchTab" v-model="activeName">
        <el-tab-pane :label="`应付列表`" name="debt"></el-tab-pane>
        <el-tab-pane :label="`应收列表`" name="credit" v-if="!(crud.rowData.value<0&&crud.rowData.debtType==16)"></el-tab-pane>
        <el-tab-pane :label="`收款列表`" name="receive" v-if="crud.rowData.value < 0&&crud.rowData.debtType!==16"></el-tab-pane>
        <el-tab-pane :label="`付款列表`" name="payment" v-if="crud.rowData.value > 0"></el-tab-pane>
      </el-tabs>
      <div style="height: 460px;">
            <inno-table-v2
              ref="tableRef3"
              v-inno-loading="crud3.loading"
              :columns="columns"
              :data="crud3.data"
              fixed
              border
              row-key="billCode"
              height="450"
              width="100%"
              selection
              :expanded-row-keys="expandRowKeys"
              @expanded-rows-change="(e) => {
                expandRowKeys = e
              }"
              :highlight-current-row="false"
              @selection-change="crud3.selectionChangeHandler"
              @row-click="crud3.singleSelection"
            >
            </inno-table-v2>
          </div>
      <!-- <el-table
        ref="tableRef3"
        class="auto-layout-table"
        border
        height="450"
        v-inno-loading="debtLoading"
        :data="crud3.data"
        :highlight-current-row="false"
        :row-class-name="tableRowClass"
        @selection-change="crud3.selectionChangeHandler"
        @row-click="crud3.singleSelection"
      >
        <el-table-column fixed="left" width="35" type="selection" />
        <el-table-column label="单号" property="billCode" show-overflow-tooltip width="235">
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="采购单号" v-if="activeName === 'debt'" property="purchaseCode" show-overflow-tooltip width="235">
          <template #default="scope">
            <inno-button-copy :link="false">{{ scope.row.purchaseCode }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="公司" property="companyName" show-overflow-tooltip />
        <el-table-column label="供应商/客户" property="agentName" show-overflow-tooltip v-if="activeName!=='debt'"/>
        <el-table-column label="供应商" property="agentName" show-overflow-tooltip v-if="activeName==='debt'"/>
        <el-table-column label="客户" property="customerName" show-overflow-tooltip v-if="activeName==='debt'"/>
        <el-table-column label="单据类型" v-if="activeName!=='receive'&&activeName!=='payment'" property="typeStr" show-overflow-tooltip>
          <template #default="scope">{{ scope.row.typeStr }}</template>
        </el-table-column>
        <el-table-column :label="activeName=='payment'?'付款日期':'单据时间'" property="billDate" sortable show-overflow-tooltip>
          <template #default="scope">{{ dateFormat(scope.row.billDate, 'YYYY-MM-DD') }}</template>
        </el-table-column>
        <el-table-column :label="activeName=='payment'?'付款单金额':'单据金额'" property="value" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.value" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="已冲销金额" property="abatmentAmount" v-if="activeName!=='receive'" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.abatmentAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="付款单余额" property="leftAmount" v-if="activeName=='payment'" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.leftAmount" format="0,0.00" />
          </template>
        </el-table-column>
        <el-table-column label="本次冲销金额" property="thisAbatmentAmount" show-overflow-tooltip>
          <template #default="scope">
            <el-input v-model="scope.row.thisAbatmentAmount" type="number" style="width: 100px" oninput="if(value.length > 10) value = value.slice(0,10)" />
          </template>
        </el-table-column>
      </el-table> -->

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="confirmAbatementClick">确定</el-button>
          <el-button @click="debtCancel">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="splitDlgShow" title="拆分付款计划" width="30%" draggable>
      <el-form :model="form" label-width="auto" style="max-width: 400px">
        <el-form-item label="请输入拆分金额：">
          <el-input v-model="splitAmount" placeholder="请输入拆分金额"/>
        </el-form-item>
      </el-form>
      <!-- <table width="100%">
        <tr>
          <td>
            <label>请输入拆分金额：</label>
          </td>
          <td>
            <el-input v-model="splitAmount" type="text" placeholder="请输入拆分金额" style="width: 350px"></el-input>
          </td>
        </tr>
      </table> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" :loading="splitLoading" @click="doSplit">确定</el-button>
          <el-button @click="splitDlgShow = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="abatementDlgShow" title="选择撤销明细" width="60%" draggable>
      <el-table 
        ref="tableRef7"
        class="auto-layout-table"
        border
        highlight-current-row
        height="450"
        v-inno-loading="crud7.loading"
        :data="crud7.data"
        stripe
        @selection-change="crud7.selectionChangeHandler"
        @row-click="crud7.singleSelection"
        >  
        <el-table-column fixed="left" width="35" type="selection" />
        <el-table-column label="应付单号" property="code" width="260" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ crud.rowData.billCode===scope.row.debtBillCode? scope.row.debtBillCode:scope.row.creditBillCode }}</inno-button-copy>
          </template>
        </el-table-column>

        <el-table-column label="被冲销的单号" property="creditBillCode" width="260" show-overflow-tooltip>
          <template #default="scope">
            <inno-button-copy :link="false">{{ crud.rowData.billCode===scope.row.debtBillCode? scope.row.creditBillCode:scope.row.debtBillCode }}</inno-button-copy>
          </template>
        </el-table-column>
        <el-table-column label="冲销日期" property="abtdate" show-overflow-tooltip>
          <template #default="scope">
            {{ dateFormat(scope.row.abtdate, 'YYYY-MM-DD') }}
          </template>
        </el-table-column>
        <el-table-column class-name="isSum" label="冲销金额" property="value" show-overflow-tooltip>
          <template #default="scope">
            <inno-numeral :value="scope.row.value" format="0,0.00" />
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" :loading="cancelAbatementLoading" @click="cancelAbatementSubmit">提交</el-button>
          <el-button @click="abatementDlgShow = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <approveProcess ref="approveProcessRef" />
    <OverdueAdjustment ref="overdueModel" @onLoadDetails="loadOverdueDetails"></OverdueAdjustment>
    <!-- 查看预付审批 -->
    <el-dialog v-model="dlgPrepaymentApprove" title="查看预计付款日期调整审批" width="75%" draggable>
      <template #header>
        <div>
          <label style="font-weight: bold; font-size: larger">查看预付审批</label>
          <!-- <el-button type="primary" class="btn-box" @click="openExplain">审批链说明</el-button> -->
        </div>
      </template>
      <template #default>
        <el-table ref="refTable" v-inno-loading="AdvancePayoading" max-height="350" style="width: 100%" border stripe :data="AdvancePayList">
          <el-table-column prop="code" :label="'单据编号'" min-width="100" show-overflow-tooltip>
            <template #default="scope">
              <inno-button-copy
                link
                @click.stop="
                  async () => {
                    await PrepaymentApproveDetailClick(scope.row);
                  }
                "
              >{{ scope.row.code }}</inno-button-copy>
            </template>
          </el-table-column>
          <el-table-column prop="status" :label="'审批状态'" min-width="100">
            <template #default="scope">{{queryStatus(scope.row.status)}}</template>
          </el-table-column>
          <el-table-column prop="originProbablyPayTime" :label="'原始预计付款日期'" min-width="100">
            <template #default="scope">{{ dateFormat(scope.row.originProbablyPayTime) }}</template>
          </el-table-column>
          <el-table-column property="currentProbablyPayTime" label="修改后预计付款日期" min-width="100" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.currentProbablyPayTime) }}</template>
          </el-table-column>
          <el-table-column property="createdByName" label="创建人" min-width="80" show-overflow-tooltip></el-table-column>

          <el-table-column prop="createdTime" min-width="120" :label="'创建时间'" show-overflow-tooltip>
            <template #default="scope">{{ dateFormat(scope.row.createdTime) }}</template>
          </el-table-column>

          <el-table-column prop="specification" :label="'审批过程'" min-width="120" show-overflow-tooltip>
            <template #default="scope">
              <!-- <span v-if="scope.row.services.length === 1">
                {{ scope.row.services[0].name }}
              </span>-->
              <el-button
                v-if="
                  scope.row.oaRequestId != null &&
                  scope.row.oaRequestId !== '' &&
                  scope.row.oaRequestId != 0
                "
                type="primary"
                size="small"
                link
                @click="openOaPage(scope.row.oaRequestId)"
              >查看审批过程</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dlgPrepaymentApprove = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive,
  nextTick
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import request from '@/utils/request';
import CRUD, { tableDrag,getUserNames } from '@inno/inno-mc-vue3/lib/crud';
import approveProcess from '@/component/ApproveProcess.vue';
import { ElTable, ElForm, ElMessageBox, ElMessage,ElLoading } from 'element-plus';
import { FileViewer } from '@inno/inno-mc-vue3/lib/components/fileUploader';
import { departmentAndCompanies } from '@inno/inno-mc-vue3/lib/components/crud';
import {
  DebtTypeEnum,
  CreditTypeEnum,
  AbatedStatus,
  InvoiceStatus,
  AccountPeriodTypeEnum
} from '@/api/metaInfo';
import _, { constant } from 'lodash';
  import { getTreeList, getDepartTree } from '@/api/bdsData';
  import { useRouter } from 'vue-router';
  import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
  import OverdueAdjustment from './components/overdueAdjustment.vue';
// import { el } from 'element-plus/es/locale';
  let router = useRouter();
// 设置另一个
const tableRef0 = ref<InstanceType<typeof ElTable>>();
const addRef = ref<InstanceType<typeof ElForm>>();
const dialogVisible = ref(false);
const splitDlgShow = ref(false);
const abatementDlgShow = ref(false);
let dataListBusinessDept = reactive([]);
const selectDetailId = ref('');
const splitAmount = ref('');
const setDetailTab = ref('paymentPlan');
const allDetailSelection = ref<any>([]);
const auditLogDialog = ref();
const approveProcessRef = ref();
const overdueModel = ref();
const debtLoading = ref(false);
// 审批弹框
const dlgPrepaymentApprove = ref(false);
const AdvancePayoading = ref(true);
const expandRowKeys = ref()
  let AdvancePayList = reactive < any > ([]);
  const isShowApplyPayTime = ref(false);//是否显示预计付款调整申请按钮
watch(
  () => splitAmount.value,
  () => {
    splitAmount.value = splitAmount.value
      .replace(/[^\d.]/g, '')
      .replace(/\.{2,}/g, '.')
      .replace('.', '$#$')
      .replace(/\./g, '')
      .replace('$#$', '.')
      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
      .replace(/^\./g, '');
  }
);
const form = reactive({

})
const crudPermission = ref({
  add: ['permission.add.uri'],
  //download: ['permission.export.uri']
});
const crud = CRUD(
  {
    title: '应付单',
    url: '/api/DebtQuery/GetList',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: { abatedStatus: '0' },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        loadTableData();
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          crud.singleSelection(crud.data[0]);
        }
        if (crud.query.abatedStatus === undefined) {
          crud.query.abatedStatus = '-1';
        }
      }
    },
    tablekey: 'tablekey0'
  },
  {
    table: tableRef0
  }
);
const tableRef1 = ref<InstanceType<typeof ElTable>>();
const crud1 = CRUD(
  {
    title: '付款计划',
    url: '/api/DebtQuery/GetListDetail',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      // [CRUD.HOOK.afterRefresh]: () => {
      //   //默认选中所有行
      //   if (crud1.data.length && crud1.data.length > 0) {
      //    for (let i = 0; i < crud1.data.length ; i++) {
      //      crud1.getTable().toggleRowSelection(crud1.data[i], true);
      //    }
      //   }
      // },
    },
    //  page: {
    //   // 页码
    //   page: 0,
    //   // 每页数据条数
    //   size:10,
    //   // 总数据条数
    //   total: 0
    // },
    tablekey: 'tableRef1'
  },
  {
    table: tableRef1
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '执行明细',
    url: '/api/DebtQuery/GetListDetailExcute',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef2'
  },
  {
    table: tableRef2
  }
);
const tableRef3 = ref();
const crud3 = CRUD(
  {
    title: '应付冲销',
    url: '',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef3'
  },
  {
    table: tableRef3
  }
);

const tableRef4 = ref<InstanceType<typeof ElTable>>();
const crud4 = CRUD(
  {
    title: '单个计划执行明细',
    url: '/api/DebtQuery/GetListDetailExcute',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    tablekey: 'tableRef4'
  },
  {
    table: tableRef4
  }
);
const tableRef5 = ref < InstanceType < typeof ElTable >> ();
  const crud5 = CRUD(
    {
      title: '进项发票明细',
      url: '/api/InputBillQuery/GetListInputBillByDebtCode',
      method: 'post',
      idField: 'id',
      query: {},
      resultKey: {
        list: 'list',
        total: 'total'
      },
      tablekey: 'tableRef5'
    },
    {
      table: tableRef5
    }
  );
  const tableRef6 = ref < InstanceType < typeof ElTable >> ();
  const crud6 = CRUD(
    {
      title: '冲销明细',
      url: '/api/AbatementQuery/GetList',
      method: 'post',
      idField: 'id',
      query: {},
      resultKey: {
        list: 'list',
        total: 'total'
      },
      tablekey: 'tableRef6'
    },
    {
      table: tableRef6
    }
);

const tableRef7 = ref<InstanceType<typeof ElTable>>();
const crud7 = CRUD(
  {
    title: '冲销明细',
    url: '/api/AbatementQuery/GetList',
    method: 'post',
    idField: 'id',
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
    },
    tablekey: 'tableRef7'
  },
  {
    table: tableRef7
  }
);
  const queryList = computed(() => {
  // 第二个第三个参数和原来使用一致，大多参数可以不传
  let items = departmentAndCompanies(
    crud,
    {
      key: 'businessDeptId',
      functionUri: 'metadata://pm/project-apply/routes/projectApply-index-search'
    },
    {
      key: 'companyId'
    }
  );
  return [
  {
    key: 'billDateS',
    endDate: 'billDateE',
    label: '单据日期',
    type: 'daterange',
    show: true
  },
  ...items,
  {
    key: 'projectNo',
    label: '项目',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/Authmeta`,
    labelK: 'name',
    valueK: 'code',
    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { status: 2, functionUri: 'metadata://fam' }
    },
    show: true
  },
  {
    key: 'abatedStatus',
    label: '冲销状态',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: AbatedStatus,
    show: true
  },
  {
    key: 'agentId',
    label: '供应商',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data' }
  },

  {
    key: 'debtTypes',
    label: '应付类型',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: DebtTypeEnum,
    show: true,
    multiple:true,
  },
  {
    key: 'servicesName',
    label: '业务单元',
    show: true
  },
  {
    key: 'billCode',
    label: '应付单号',
    show: true
  },
  {
    key: 'createdBy',
    label: '创建人',
    multiple: true,
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
    placeholder: '用户名称搜索',
    valueK: 'name',
    labelK: 'displayName',
    props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
    slots: {
      option: ({ item }) => (
        <>
          <span>{item.displayName}</span>
          <span style="float:right">{item.name}</span>
        </>
      )
    }
  },
  {
    key: 'purchaseCode',
    label: '采购单号',
    show: true
  },
  {
    key: 'producerOrderNo',
    label: '厂家单号',
    show: true
  },
  {
    key: 'relateCode',
    label: '关联单号',
    show: true
  },
  {
    key: 'orderNo',
    label: '订单号',
    show: true
  },
  {
    key: 'receiveCode',
    label: '收款单号',
    show: true
  },
  {
    key: 'invoiceCode',
    label: '进项票发票号',
    show: true
  },
  {
    key: 'customerId',
    label: '客户',
    method: 'post',
    type: 'remoteSelect',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    placeholder: '客户搜索',
    labelK: 'name',
    valueK: 'id',
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: true
  },
  {
    key: 'isNegative',
    label: '是否为负数应付',
    type: 'select',
    labelK: 'name',
    valueK: 'value',
    dataList: [
      {
        name: '是',
        value: true
      },
      {
        name: '否',
        value: false
      }
    ],
    show: true
  },
  {
    key: 'projectCode',
    label: '项目编号',
    show: true
  },
    {
    key: 'purchaseContactNo',
    label: '采购合同单号',
    show: true
    },
    {
    key: 'isChangeDebt',
    label: '切换核算部门自动单',
    type: 'select',
    labelK: 'name',
    valueK: 'value',
    dataList: [
      {
        name: '是',
        value: true
      },
      {
        name: '否',
        value: false
      }
    ],
    show: true
    },
  {
    key: 'isLossAgent',
    label: '是否确认损失',
    type: 'select',
    labelK: 'name',
    valueK: 'value',
    dataList: [
      {
        name: '是',
        value: true
      },
      {
        name: '否',
        value: false
      }
    ],
    show: true
  },{
    key: 'invoiceNumber',
    label: '进项发票号',
    show: true
    },
  ]
});
// const queryList = computed(() => [
//   {
//     key: 'billDateS',
//     endDate: 'billDateE',
//     label: '单据日期',
//     type: 'daterange',
//     show: true
//   },
//   {
//     show: true,
//     key: 'companyId',
//     label: '公司',
//     type: 'remoteSelect',
//     method: 'post',
//     url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
//     labelK: 'name',
//     valueK: 'id',
//     props: { KeyWord: 'name', resultKey: 'data.data' }
//   },
//   {
//     key: 'projectNo',
//     label: '项目',
//     type: 'remoteSelect',
//     method: 'post',
//     url: `${gatewayUrl}v1.0/pm-webapi/api/ProjectInfo/Authmeta`,
//     labelK: 'name',
//     valueK: 'code',
//     props: {
//       KeyWord: 'name',
//       resultKey: 'data.data',
//       queryData: { status: 2, functionUri: 'metadata://fam' }
//     },
//     show: true
//   },
//   {
//     key: 'abatedStatus',
//     label: '冲销状态',
//     type: 'select',
//     labelK: 'name',
//     valueK: 'id',
//     dataList: AbatedStatus,
//     show: true
//   },
//   {
//     key: 'agentId',
//     label: '供应商',
//     type: 'remoteSelect',
//     method: 'post',
//     url: `${gatewayUrl}v1.0/bdsapi/api/agents/meta`,
//     labelK: 'name',
//     valueK: 'id',
//     props: { KeyWord: 'name', resultKey: 'data.data' }
//   },

//   {
//     key: 'debtTypes',
//     label: '应付类型',
//     type: 'select',
//     labelK: 'name',
//     valueK: 'id',
//     dataList: DebtTypeEnum,
//     show: true,
//     multiple:true,
//   },
//   {
//     key: 'department',
//     label: '核算部门',
//     type: 'departmentSelect',
//     //options: dataListBusinessDept,
//     props: {
//     functionUri: 'metadata://fam',
//       queryData: { functionUri: 'metadata://fam' },
//       allSelectable: true
//     },
//     show: true
//   },
//   {
//     key: 'servicesName',
//     label: '业务单元',
//     // type: 'remoteSelect',
//     // method: 'post',
//     // url: `${gatewayUrl}v1.0/bdsapi/api/businessUnits/queryItem`,
//     // valueK: 'businessUnitID',
//     // labelK: 'businessUnitName',
//     // props: { KeyWord: 'nameLike', resultKey: 'data.data' },
//     show: true
//   },
//   {
//     key: 'billCode',
//     label: '应付单号',
//     show: true
//     // prepend: {
//     //   props: { style: { width: '50px' } },
//     //   key: 'billCodeQueryType',
//     //   label: '应付单号查询类型',
//     //   type: 'select',
//     //   labelK: 'name',
//     //   valueK: 'id',
//     //   dataList: [{ id: '1', name: '包含' },{ id: '2', name: '不包含' }]
//     // }
//   },
//   // {
//   //   key: 'createdBy',
//   //   label: '创建人',
//   //   type: 'remoteSelect',
//   //   method: 'post',
//   //   //url: `api/bff/GetUserByNames`,
//   //   url: `${gatewayUrl}v1.0/userapi/getlistbynames`,
//   //   labelK: 'displayName',
//   //   valueK: 'name',
//   //   props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
//   //   show: false
//   // },
//   {
//     key: 'createdBy',
//     label: '创建人',
//     multiple: true,
//     method: 'post',
//     type: 'remoteSelect',
//     url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
//     placeholder: '用户名称搜索',
//     valueK: 'name',
//     labelK: 'displayName',
//     props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
//     slots: {
//       option: ({ item }) => (
//         <>
//           <span>{item.displayName}</span>
//           <span style="float:right">{item.name}</span>
//         </>
//       )
//     }
//   },
//   {
//     key: 'purchaseCode',
//     label: '采购单号',
//     show: true
//   },
//   {
//     key: 'producerOrderNo',
//     label: '厂家单号',
//     show: true
//   },
//   {
//     key: 'relateCode',
//     label: '关联单号',
//     show: true
//   },
//   {
//     key: 'orderNo',
//     label: '订单号',
//     show: true
//   },
//   {
//     key: 'receiveCode',
//     label: '收款单号',
//     show: true
//   },
//   {
//     key: 'customerId',
//     label: '客户',
//     method: 'post',
//     type: 'remoteSelect',
//     url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
//     placeholder: '客户搜索',
//     labelK: 'name',
//     valueK: 'id',
//     props: { KeyWord: 'name', resultKey: 'data.data' },
//     show: true
//   },
//   {
//     key: 'isNegative',
//     label: '是否为负数应付',
//     type: 'select',
//     labelK: 'name',
//     valueK: 'value',
//     dataList: [
//       {
//         name: '是',
//         value: true
//       },
//       {
//         name: '否',
//         value: false
//       }
//     ],
//     show: true
//   },
//   {
//     key: 'projectCode',
//     label: '项目编号',
//     show: true
//   },
// ]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);
const selectionValue = computed(() => {
  let value = 0;
  crud.selections.forEach((item) => {
    if (item.value) {
      value += item.value;
    }
  });
  return value;
});
onMounted(() => {
  //获取核算部门下拉列表
  getDepartTree('metadata://fam/credit-debt-Query/routes/debtQuery-view').then(
    (res) => {
      const getid = (list) => {
        return list.map((i) => {
          return {
            ...i,
            children: i.extraInfo.children
              ? getid(i.extraInfo.children)
              : undefined
          };
        });
      };
      const tree1 = res.data.data;
      dataListBusinessDept.push(...getid(tree1));
    }
  );
  // 表头拖拽必须在这里执行
  // tableDrag(tableRef0);
  // tableDrag(tableRef1);
  // tableDrag(tableRef2);
  crud.toQuery();
  loadTableData();
});
//监听 crud.rowData.id
watch(
  () => crud.rowData.id,
  (n, o) => {
    crud1.data = [];
    if (n != null) {
      crud1.query = { debtId: crud.rowData.id, limit: 2000 };
      crud1.toQuery();
      if (crud.rowData.abatedStatus == 0) {
        isShowApplyPayTime.value = true
      } else {
        isShowApplyPayTime.value = false
      }
      
    }
  },
  { deep: true }
);
// watch(
//   () => crud1.selections,
//   (n, o) => {
//     if (n != null) {
//       crud2.query = { debtDetailIds: crud1.selections.map(v => {return v.id}),limit:2000};
//       crud2.toQuery();
//     } else {
//       crud2.query = { debtDetailIds:[],limit:2000 };
//       crud2.toQuery();
//     }
//   },
//   { deep: true }
// );
watch(
  () => crud.selections,
  (n, o) => {
    crud2.data = [];
    if (crud.rowData.id) {
      crud2.query = { debtId: crud.rowData.id, limit: 2000 };
      crud2.toQuery();
    }
  },
  { deep: true }
);
watch(
  () => crud.selections,
  (n, o) => {
    crud5.data = [];
    if (crud.rowData.id) {
      crud5.query = { storeInItemCode: crud.rowData.relateCode, debtType: crud.rowData.debtType, debtCode: crud.rowData.billCode, limit: 2000 };
      crud5.toQuery();
    }
  },
  { deep: true }
);
  watch(
    () => crud.selections,
    (n, o) => {
      crud6.data = [];
      if (crud.rowData.id) {
        crud6.query = { debtBillCode: crud.rowData.billCode, limit: 2000 };
        crud6.toQuery();
      }
    },
    { deep: true }
  );
//点击进行冲销
const abatementClick = async() => {
  dialogVisible.value = true;
  debtLoading.value = true;
  activeName.value = 'debt'; 
  crud3.loading = true;
  request({
    url: `/api/DebtQuery/GetAvailableAbatments`,
    method: 'POST',
    data: { debtId: crud.rowData.id }
  })
    .then((res) => {
      if (res.data.code === 200) {
        crud3.data = res.data.data;
        crud3.selections = [];
        crud3.data.map((item, index) => {
          if (item.defaultCheck) {
            nextTick(()=>{
              tableRef3.value.toggleRowSelection(item, true)
            })          
        }
      }) 
      debtLoading.value = false;
        debtNoOfSearch.value = '';
        debtCustomerSearch.value='';
        debtDateSearch.value = [];
      } else {
        debtLoading.value = false;
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    })
    .finally(() => {  
      crud3.loading = false;
    });;
};
//确定进行冲销
const confirmAbatementClick = () => {
  if (crud3.selections === null || crud3.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请勾选需要冲销的单据！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else if (activeName.value === 'payment' && crud3.selections.length > 1) {
    ElMessage({
      showClose: true,
      message: '只能选一个付款单冲销！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  //被冲销的应付单列表
  let _lstInput = crud3.selections.map((s) => {
    return { billCode: s.billCode, billValue:s.value, value: s.thisAbatmentAmount };
  });
  ElMessageBox.confirm('此操作将进行冲销, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    request({
      url: `/api/DebtQuery/DebtAbatments`,
      method: 'POST',
      data: { debtId: crud.rowData.id, lstInput: _lstInput, classify:activeName.value }
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '冲销成功！',
            type: 'success',
            duration: 3 * 1000
          });
          dialogVisible.value = false;
          crud.toQuery();
          loadTableData();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        } 
        loading.close();
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
        loading.close();
      });
  });
};

const functionUris = {
  confirmAbatement:'metadata://fam/credit-debt-Query/functions/confirmAbatement',
  export: 'metadata://fam/finance-Debt/functions/excute-export',
  cancelAbatement:'metadata://fam/credit-debt-Query/functions/cancelAbatement',
};

const splitDebtDetail = (detailId) => {
  splitDlgShow.value = true;
  selectDetailId.value = detailId;
  splitAmount.value = '';
};
let splitLoading = ref(false);
const doSplit = () => {
  if (!splitAmount.value || splitAmount.value - 0 <= 0) {
    ElMessage({
      showClose: true,
      message: '请输入拆分金额,并且大于0',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  splitLoading.value = true;
  request({
    url: 'api/DebtQuery/SplitDebtDetail',
    method: 'POST',
    data: { detailId: selectDetailId.value, amount: splitAmount.value }
  })
    .then((res) => {
      if (res.data.code == 200) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'success',
          duration: 3 * 1000
        });
        splitDlgShow.value = false;
        crud1.toQuery();
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .finally(() => {
      splitLoading.value = false;
    });
};

// 要展开的行，数值的元素是row的key值
let expands = ref([]);
const clickRowHandle = (row, column, event) => {
  tableRef1.value!.clearSelection();
  tableRef1.value!.toggleRowSelection(row)
  if (expands.value != null && expands.value.includes(row.id)) {
    expands.value = [];
  } else {
    expands.value = [row.id];
    crud4.query = { debtDetailIds: expands.value, limit: 2000 };
    crud4.toQuery();
  }
};
const debtNoOfSearch = ref('');
const debtCustomerSearch = ref('');
const debtDateSearch = ref([]);

//计算合计
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (['isSum'].includes(column.className)) {
      const values = data.map((item) => {
        // 后台初始化已做好计算，前端针对余额特殊处理移除
        // if (column.property === 'leftAmount') {
        //   if (item.value < 0) {
        //     return -Number(item[column.property]);
        //   }
        // }
        return Number(item[column.property]);
      });
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return parseFloat((prev + curr).toFixed(4));
          } else {
            return prev;
          }
        }, 0)}`;
        sums[index] = rbstateFormat(sums[index]);
      } else {
        sums[index] = '';
      }
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
// 合计金额千分位
const rbstateFormat = (cellValue) => {
  return asyncNumeral(cellValue, '0,0.00');
  // if (cellValue !== null) {
  //   cellValue = Number(cellValue).toFixed(4);
  //   cellValue += '';
  //   if (!cellValue.includes('.')) cellValue += '.';
  //   return cellValue
  //     .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
  //       return $1 + ',';
  //     })
  //     .replace(/\.$/, '');
  // }
};
let tabCount = ref({
  nonAbatedCount: 0,
  abatedCount: 0,
  allCount: 0
});
const tabhandleClick = () => {
  setDetailTab.value = 'paymentPlan'
  crud.toQuery();
  loadTableData();

};
const loadTableData = () => {
  request({
    url: '/api/DebtQuery/GetTabCount',
    data: {
      status: '-1',
      ...crud.query
    },
    method: 'post'
  }).then((res) => {
    tabCount.value = res.data.data;
  });
};
const searchCrud = () => {
  crud.query.abatedStatus = '-1';
  crud.toQuery();
};
let downLoading = ref(false);
const download = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      if (res.data.code === 500) {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      const filename = fileName || '';
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; //'application/vnd.ms-excel';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = filename + '导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
const downloadAsync = (
  url: String,
  fileName: String,
  data: any,
  type: String = 'post'
) => {
  downLoading.value = true;
  request({
    url: url,
    method: type,
    data: data,
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
  })
    .then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
          type: 'success',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return true;
      }
      else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
        downLoading.value = false;
        return false;
      }
      downLoading.value = false;
    })
    .catch((t) => {
      downLoading.value = false;
    });
};
const activeName = ref('debt');

const searchTab = (tab: TabsPaneContext, event: Event) => { 
  crud3.data = [];
  crud3.selections = [];
  tableRef3.value.clearSelection();
  if (tab.props.name == 'debt') {
    crud3.query.classify = 'debt';
  } else if (tab.props.name == 'credit') {
    crud3.query.classify = 'credit';
  } else if (tab.props.name == 'receive') {
    crud3.query.classify = 'receive';
  } else if (tab.props.name == 'payment') {
    crud3.query.classify = 'payment';
  }     
  crud3.query.debtId= crud.rowData.id,
  crud3.url = `/api/DebtQuery/GetAvailableAbatments`;
  crud3.toQuery(); 
};

const searchDebt = () => {
  crud3.url = `/api/DebtQuery/GetAvailableAbatments`;
  crud3.query = {
    billCode: debtNoOfSearch.value,
    customer:debtCustomerSearch.value,
    debtId: crud.rowData.id,
    classify:activeName.value,
    billDateStart: debtDateSearch.value[0],
    billDateEnd: debtDateSearch.value[1],
    limit: 20
  };
  crud3.toQuery();
};
const detail = (relateCode) => {
if (window.__MICRO_APP_ENVIRONMENT__) {
  router.goAppPage({
    path: '/inventory/docrelate', // fm为要跳转的子应用前缀
    query: {
      code: relateCode,
      title: '关联单据查询'
    },
    params: {
      // 是否需要刷新目标页面
      __reload: true
    }
  });
} else {
  console.error('非微前端环境');
}
}
//切换
const tabDetailActiveClick = async (tab: any) => { 
  if (crud.data && crud.data.length > 0) {
    if (tab === 'paymentPlan') {
      crud1.query = { debtId: crud.rowData.id, limit: 2000 };
      crud1.toQuery();
    } else if (tab === 'executionDetail') {
      crud2.query = { debtId: crud.rowData.id, limit: 2000 };
      crud2.toQuery();
    } else if (tab === 'invoiceDetail') {
      crud5.query = { storeInItemCode: crud.rowData.relateCode, debtType: crud.rowData.debtType, debtCode: crud.rowData.billCode, limit: 2000 };
      crud5.toQuery();
    } else if (tab === 'abatementDetail') {
      crud6.query = { debtBillCode: crud.rowData.billCode, limit: 2000 };
      crud6.toQuery();
    }
  }
  // setDetailTab.value = tab.props.name;
};
// 账期起始日期调整
const openOverdueClick = () => {
  if(allDetailSelection.value?.length > 0){
    let data = { ...allDetailSelection.value[0] }
   if (data.probablyPayTime === '' || data.probablyPayTime === null) {
      ElMessage.warning({ showClose: true, message: '没有预计付款日期时不允许修改预计付款日期！' });
    } else if (data.status == 99 || data.status == '99') {
      ElMessage.warning({ showClose: true, message: '已完成状态不能调整预计付款日期！' });
    } else if (data.isRelateBatchPayment == true) {
      ElMessage.warning({ showClose: true, message: '当前付款计划已被关联到批量付款明细，不能调整预计付款日期！' });
    } else {
      overdueModel.value.openOverdueDialog(data);
    }
  }else{
    ElMessage.warning({ showClose: true, message: '请选择需要调整的数据！' });
  }
};
const viewApprovalProcess= (row:any) => {
  if(allDetailSelection.value?.length > 0){
    AdvancePayList = [];
    let id = allDetailSelection.value[0].id;
    request({
    url: `/api/DebtQuery/GetDebtDetailListQueryAsync`,
    data:{id},
    method: 'post'
  }).then((res) => {
    if(res && res.data.code === 200){
      console.log(res.data.data,'这是返回的数据')
      AdvancePayList = res.data.data.list;
      dlgPrepaymentApprove.value = true;
      AdvancePayoading.value = false;
      getUserNames(AdvancePayList, ['createdBy']);
    }
  })
  }else{
    ElMessage.warning({ showClose: true, message: '请选择需要查看审批过程的数据！' });
  }
};
//点击行调用
const getDetailData = (e) => {
  console.log('eeeee---------', e);
  setDetailTab.value = 'paymentPlan';
  // crudDetail.query.purchaseOrderId = e.id;
  crud1.query = { debtId: e.id, limit: 2000 };
  crud1.toQuery();
  crud.singleSelection(e);
};
const handleAllSelectionChange = (items: any) => {
  allDetailSelection.value.splice(0);
  allDetailSelection.value = items;
}
const downloadFile = (invoiceNo, invoiceCode) => {
    request({
      url:
        '/api/InputBillQuery/GetKDFilePath?invoiceNo=' +
        invoiceNo +
        '&invoiceCode=' +
        invoiceCode,
      method: 'get'
    })
      .then((res) => {
        if (res.data.code == 200) {
          if (res.data.data != null && res.data.data.length > 0) {
            FileViewer.show(
              res.data.data.map((i) => i.previewAddress), // 可以为数组和逗号隔开的字符串
              0, // 默认打开的下标
              {} // FileViewer props
            );
          } else {
            ElMessage({
              showClose: true,
              message: '未找到金蝶附件，请稍后再试！',
              type: 'error',
              duration: 3 * 1000
            });
          }
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((t) => { });
  };
  //预付查看审批过程
const openOaPage = (requestId) => {
  console.log('requestId', requestId);
  approveProcessRef.value.requestId = requestId;
  approveProcessRef.value.dialogApproveProcessVisible = true;
  approveProcessRef.value.GetRemark();
  approveProcessRef.value.activeName = 'first';
};
const queryStatus = (status) =>{
  let text = ''
  switch(status){
    case -1:
    text = '全部'
    break;
    case 0:
    text = '待提交'
    break;
    case 1:
    text = '待审核'
    break;
    case 66:
    text = '已拒绝'
    break;
    case 99:
    text = '已完成'
    break;
    case 5000:
    text = '我的审批'
    break;
  }
  return text;
}
const loadOverdueDetails = () =>{
  crud1.query = { debtId: crud.rowData.id, limit: 2000 };
  crud1.toQuery();
}

const cancelAbatementClick = () => {
  abatementDlgShow.value = true;
  request({
    url: `/api/AbatementQuery/GetList`,
    method: 'POST',
    data: { debtBillCode: crud.rowData.billCode,filterLoss:true }
  })
    .then((res) => {
      if (res.data.code === 200) {
        crud7.data=res.data.data.list;
      } else {
        ElMessage({
          showClose: true,
          message: res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
    })
    .catch((err) => {
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      });
    })
    .finally(() => {   
    });
}

const cancelAbatementSubmit = () => {
  if (crud7.selections === null || crud7.selections.length < 1) {
    ElMessage({
      showClose: true,
      message: '请勾选需要撤销冲销的单据！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } 

  var types = [];
  for (var i = 0; i < crud7.selections.length; i++){
    if (!types.includes(crud7.selections[i].creditType)) {
      types.push(crud7.selections[i].creditType);
    }
    if (!types.includes(crud7.selections[i].debtType)) {
      types.push(crud7.selections[i].debtType);
    }
  }

  if (types.length > 2) {
    ElMessage({
      showClose: true,
      message: '请选择相同类型的冲销单！',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }

  let _lstInput = crud7.selections.map((s) => {
    return s.id ;
  });
  ElMessageBox.confirm('此操作将撤销冲销, 是否继续?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    request({
      url: `/api/DebtQuery/CancelAbatments`,
      method: 'POST',
      data: {debtId:crud.rowData.id , ids: _lstInput }
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '撤销冲销成功！',
            type: 'success',
            duration: 3 * 1000
          });
          abatementDlgShow.value = false;
          crud.toQuery();
          loadTableData();
        } else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        } 
        loading.close();
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
        loading.close();
      });
  });
}
const debtCancel =()=>{
  crud3.data = [];
  crud3.selections = [];
  tableRef3.value.clearSelection();
   dialogVisible.value = false;
}

const tableRowClass = (row) => {
  if (row.row.isOriginal === true) {
      return 'warning-row'
  } 
  return ''
}
const debtColumns = [
  {
    key: 'billCode',
    dataKey: 'billCode',
    title: '单号',
    fixed: 'left',
    width: 200,
    cellRenderer: ({ rowData }) => (
      <el-tooltip
        effect="dark"
        content={rowData.billCode}
        placement="top-start"
      >
      <inno-button-copy
        >
        {rowData.billCode}
        </inno-button-copy>
    </el-tooltip>
    )
  },
  {
    key: 'purchaseCode',
    dataKey: 'purchaseCode',
    title: '采购单号',
    width: 200,
    cellRenderer: ({ rowData }) => (
      activeName.value === 'debt'?
      <el-tooltip
        effect="dark"
        content={rowData.purchaseCode}
        placement="top-start"
      >
      <inno-button-copy>
       {rowData.purchaseCode}
      </inno-button-copy>
    </el-tooltip>:''
    )
  },
  {
    key: 'companyName',
    dataKey: 'companyName',
    title: '公司',
    width: 120,
    cellRenderer: ({  cellData: companyName }) => (
      <el-tooltip
        effect="dark"
        content={companyName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{companyName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'agentName',
    dataKey: 'agentName',
    title: '供应商',
    width: 120,
    cellRenderer: ({  cellData: agentName }) => (
      <el-tooltip
        effect="dark"
        content={agentName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{agentName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'customerName',
    dataKey: 'customerName',
    title: '客户',
    width: 120,
    cellRenderer: ({  cellData: customerName }) => (
      <el-tooltip
        effect="dark"
        content={customerName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{customerName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'typeStr',
    dataKey: 'typeStr',
    title: '单据类型',
    width: 120,
    cellRenderer: ({  cellData: typeStr }) => (
      <el-tooltip
        effect="dark"
        content={typeStr}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{typeStr}</p>
      </el-tooltip>
    )
  },
  {
    key: 'billDate',
    dataKey: 'billDate',
    title: '单据时间',
    width: 120,
    cellRenderer: ({ rowData }) => (
      dateFormat(rowData.billDate, 'YYYY-MM-DD')
    )
  },
  {
    key: 'value',
    dataKey: 'value',
    title: '单据金额',
    width: 120,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.value, 1),'0,0.00')
    )
  },
  {
    key: 'abatmentAmount',
    dataKey: 'abatmentAmount',
    title: '已冲销金额',
    width: 120,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.abatmentAmount, 1),'0,0.00')
    )
  },
  {
    key: 'thisAbatmentAmount',
    dataKey: 'thisAbatmentAmount',
    title: '本次冲销金额',
    fixed: 'right',
    width: 150,
    cellRenderer: ({ rowData }) => (
    <el-input-number style="width: 100%;" v-model={rowData.thisAbatmentAmount} min={0} controls={false}/>
    )
  }
]
const creditColumns = [
  {
    key: 'billCode',
    dataKey: 'billCode',
    title: '单号',
    fixed: 'left',
    width: 260,
    cellRenderer: ({ rowData }) => (
      <el-tooltip
        effect="dark"
        content={rowData.billCode}
        placement="top-start"
      >
      <inno-button-copy
        >
        {rowData.billCode}
        </inno-button-copy>
    </el-tooltip>
    )
  },
  {
    key: 'companyName',
    dataKey: 'companyName',
    title: '公司',
    width: 160,
    cellRenderer: ({  cellData: companyName }) => (
      <el-tooltip
        effect="dark"
        content={companyName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{companyName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'agentName',
    dataKey: 'agentName',
    title: '供应商/客户',
    width: 160,
    cellRenderer: ({  cellData: agentName }) => (
      <el-tooltip
        effect="dark"
        content={agentName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{agentName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'typeStr',
    dataKey: 'typeStr',
    title: '单据类型',
    width: 150,
    cellRenderer: ({  cellData: typeStr }) => (
      <el-tooltip
        effect="dark"
        content={typeStr}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{typeStr}</p>
      </el-tooltip>
    )
  },
  {
    key: 'billDate',
    dataKey: 'billDate',
    title: '单据时间',
    width: 160,
    cellRenderer: ({ rowData }) => (
      dateFormat(rowData.billDate, 'YYYY-MM-DD')
    )
  },
  {
    key: 'value',
    dataKey: 'value',
    title: '单据金额',
    width: 150,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.value, 1),'0,0.00')
    )
  },
  {
    key: 'abatmentAmount',
    dataKey: 'abatmentAmount',
    title: '已冲销金额',
    width: 150,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.abatmentAmount, 1),'0,0.00')
    )
  },
  {
    key: 'thisAbatmentAmount',
    dataKey: 'thisAbatmentAmount',
    title: '本次冲销金额',
    fixed: 'right',
    width: 150,
    cellRenderer: ({ rowData }) => (
    <el-input-number style="width: 100%;" v-model={rowData.thisAbatmentAmount} min={0} controls={false}/>
    )
  }
]
const receiveColumns = [
  {
    key: 'billCode',
    dataKey: 'billCode',
    title: '单号',
    fixed: 'left',
    width: 260,
    cellRenderer: ({ rowData }) => (
      <el-tooltip
        effect="dark"
        content={rowData.billCode}
        placement="top-start"
      >
      <inno-button-copy
        >
        {rowData.billCode}
        </inno-button-copy>
    </el-tooltip>
    )
  },
  {
    key: 'companyName',
    dataKey: 'companyName',
    title: '公司',
    width: 230,
    cellRenderer: ({  cellData: companyName }) => (
      <el-tooltip
        effect="dark"
        content={companyName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{companyName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'agentName',
    dataKey: 'agentName',
    title: '供应商/客户',
    width: 230,
    cellRenderer: ({  cellData: agentName }) => (
      <el-tooltip
        effect="dark"
        content={agentName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{agentName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'billDate',
    dataKey: 'billDate',
    title: '单据时间',
    width: 230,
    cellRenderer: ({ rowData }) => (
      dateFormat(rowData.billDate, 'YYYY-MM-DD')
    )
  },
  {
    key: 'value',
    dataKey: 'value',
    title: '单据金额',
    width: 230,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.value, 1),'0,0.00')
    )
  },
  {
    key: 'thisAbatmentAmount',
    dataKey: 'thisAbatmentAmount',
    title: '本次冲销金额',
    width: 200,
    fixed: 'right',
    cellRenderer: ({ rowData }) => (
    <el-input-number style="width: 100%;" v-model={rowData.thisAbatmentAmount} min={0} controls={false}/>
    )
  }
]
const paymentColumns =   [
  {
    key: 'billCode',
    dataKey: 'billCode',
    title: '单号',
    fixed: 'left',
    width: 220,
    cellRenderer: ({ rowData }) => (
      <el-tooltip
        effect="dark"
        content={rowData.billCode}
        placement="top-start"
      >
      <inno-button-copy
        >
        {rowData.billCode}
        </inno-button-copy>
    </el-tooltip>
    )
  },
  {
    key: 'companyName',
    dataKey: 'companyName',
    title: '公司',
    width: 180,
    cellRenderer: ({  cellData: companyName }) => (
      <el-tooltip
        effect="dark"
        content={companyName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{companyName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'agentName',
    dataKey: 'agentName',
    title: '供应商/客户',
    width: 180,
    cellRenderer: ({  cellData: agentName }) => (
      <el-tooltip
        effect="dark"
        content={agentName}
        placement="top-start"
      >
        <p style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 280px;display: inline-block;">{agentName}</p>
      </el-tooltip>
    )
  },
  {
    key: 'billDate',
    dataKey: 'billDate',
    title: '付款日期',
    width: 180,
    cellRenderer: ({ rowData }) => (
      dateFormat(rowData.billDate, 'YYYY-MM-DD')
    )
  },
  {
    key: 'value',
    dataKey: 'value',
    title: '付款单金额',
    width: 150,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.value, 1),'0,0.00')
    )
  },
  {
    key: 'abatmentAmount',
    dataKey: 'abatmentAmount',
    title: '已冲销金额',
    width: 150,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.abatmentAmount, 1),'0,0.00')
    )
  },
  {
    key: 'leftAmount',
    dataKey: 'leftAmount',
    title: '付款单余额',
    width: 160,
    cellRenderer: ({ rowData }) => (
      asyncNumeral(_.multiply(rowData.leftAmount, 1),'0,0.00')
    )
  },
  {
    key: 'thisAbatmentAmount',
    dataKey: 'thisAbatmentAmount',
    title: '本次冲销金额',
    fixed: 'right',
    width: 160,
    cellRenderer: ({ rowData }) => (
    <el-input-number style="width: 100%;" v-model={rowData.thisAbatmentAmount} min={0} controls={false}/>
    )
  }
];

const columns = computed(() => {
  if(activeName.value === 'debt'){
    return  [...debtColumns]
  }else if(activeName.value === 'receive'){
    return [...receiveColumns] 
  }else if(activeName.value === 'payment'){
    return [...paymentColumns]
  }else if(activeName.value === 'credit'){
    return [...creditColumns]
  }
  return []
})
</script>

<style scoped lang="scss">
.mycard_css {
  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  :deep(.el-tabs__content) {
    padding: 0px;
  }
}
:deep(.el-table__expand-column) {
  position: absolute;
  display: none;
}
:deep( .warning-row) {
  --el-table-tr-bg-color: var(--el-color-warning-light-3);
}
:deep( .warning-row:hover > td) {
  background-color: transparent !important;
}
:deep(.example-showcase .el-table-v2__overlay) {
  z-index: 9;
}
</style>