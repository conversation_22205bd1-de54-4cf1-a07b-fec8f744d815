using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 金蝶财务相关API客户端接口
    /// </summary>
    public interface IKingdeeFinanceClient
    {
        /// <summary>
        /// 保存财务应收单
        /// </summary>
        Task<KingdeeApiResult> PushCreditsToKingdeeAsync(
            List<KingdeeCredit> kingdeeCredits,
            string classify,
            string preRequestBody);

        /// <summary>
        /// 保存财务应付单
        /// </summary>
        Task<KingdeeApiResult> PushDebtsToKingdeeAsync(
            List<KingdeeDebt> kingdeeDebts,
            string classify,
            string preRequestBody);

        /// <summary>
        /// 更改收入确认标识到金蝶系统
        /// </summary>
        Task<KingdeeApiResult<int>> PushIncomeIsConfirmAsync(KingdeeIncomeIsConfirm incomeIsConfirm);

        /// <summary>
        /// 应付付款结算
        /// </summary>
        Task<KingdeeApiResult> PaymentSettlementAsync(List<PaymentSettlementInput> inputs);

        /// <summary>
        /// 应付冲应收
        /// </summary>
        Task<KingdeeApiResult> PayablesOffsetReceivablesAsync(List<PaymentSettlementInput> inputs);

        /// <summary>
        /// 多发票指定应付
        /// </summary>
        Task<KingdeeApiResult> ManyInvoiceSpecifyApFinAsync(ManyInvoiceSpecifyApFinInput input);

        /// <summary>
        /// 批量更新收款调整单订单号
        /// </summary>
        Task<KingdeeApiResult> BatchUpdatePaymentModificationAsync(List<BatchUpdateOrderNumberInput> input);

        /// <summary>
        /// 批量更新认款调整单订单号
        /// </summary>
        Task<KingdeeApiResult> BatchUpdateAcceptanceAsync(List<BatchUpdateOrderNumberInput> input);
    }
}
