{
  "AppId": "finance-backend",
  "ConnectionStrings": {
    //"RedisConnection": "localhost:6379"
    "RedisConnection": "*************:6389,password=zxrc607redis,connectRetry=3,connectTimeout=30000,syncTimeout=30000,responseTimeout=30000"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Serilog": {
    "ToAI": true,
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft.AspNetCore": "Warning"
      }
    }
  },
  //OA
  "WeaverOASetting": {
    "AppId": "A4IfT7T7a7dBUk45",
    "Secret": "<Secret>",
    "AESKey": "<Key>",
    "BaseUrl": "https://oa-sit.innostic.com:8077"
  },
  "INNO_SdkMode": "dev",
  "BaseUri": "https://core-sit.innostic.com/v1", //sit域名
  "SPDSetting": {
    "Host": "https://test1.innostic.com:27004",
    "Token": "C5852B68-428B-477C-B379-E76E1158F0DA"
  },
  "KingdeeSetting": {
    "AppId": "AccessToken",
    "AppSecret": "AccessToken#123.",
    "User": "***********",
    "Tenantid": "jfzx-dev",
    "AccountId": "1703802271915051008",
    "Host": "http://**************:58022/ierp"
  },
  //金蝶切库需更换配置:AccountId:2154545526576515072
  //"KingdeeSetting": {
  //  "AppId": "coreSystem",
  //  "Appsecret": "CorePass1234567899@1",
  //  "User": "***********",
  //  "Tenantid": "jfzx-prd",
  //  "AccountId": "1754698364466757632",
  //  "Host": "https://finance-uat.innostic.com"
  //},
  //"KingdeeSetting": {
  //  "AppId": "coreSystem",
  //  "Appsecret": "AccessToken#123.",
  //  "User": "***********",
  //  "Tenantid": "jfzx_sit",
  //  "AccountId": "1705918914778631168",
  //  "Host": "https://finance-qa.innostic.com"
  //},
  "AllowedHosts": "*",
  "HealthCheckUris": "http://localhost:3500/v1.0/healthz", //多个地址用 英文分号 ; 分隔
  "ReconciliationLetteTempUrl": "https://static.innostic.com/template/财务对账函模板（不带发票明细）.xlsx",
  //协调服务配置
  "coordinate": {
    "signSecretKey": "ckfByrUtNUMyjeKPgLsHnMgSdVMTCPUT",
    "reqUrl": "http://************:8080",
    "contextPath": "/xxl-job-admin"
  },
  //协调导出服务应用AppId
  "coordinateAppId": "qa-gateway"
}
