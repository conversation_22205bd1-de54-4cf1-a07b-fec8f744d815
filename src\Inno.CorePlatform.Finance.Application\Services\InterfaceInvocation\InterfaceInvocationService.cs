using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.ServiceFee;
using Inno.CorePlatform.Finance.Application.Helpers;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.MergeInputBills;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation
{
    /// <summary>
    /// 接口调用服务，集中处理所有与外部系统的交互
    /// </summary>
    public class InterfaceInvocationService : IInterfaceInvocationService
    {
        private readonly ILogger<InterfaceInvocationService> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IKingdeeFinanceClient _kingdeeFinanceClient;
        private readonly IManyInventoryApiClient _manyInventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly FinanceDbContext _db;
        private readonly MergeInputBillHelper _mergeInputBillHelper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="kingdeeApiClient">金蝶API客户端</param>
        /// <param name="manyInventoryApiClient">多对一勾稽库存能力中心客户端</param>
        /// <param name="bDSApiClient">BDS API客户端</param>
        /// <param name="purchaseApiClient">采购能力中心客户端</param>
        /// <param name="db">数据库上下文</param>
        /// <param name="mergeInputBillHelper">合并进项票辅助类</param>
        public InterfaceInvocationService(
            ILogger<InterfaceInvocationService> logger,
            IKingdeeApiClient kingdeeApiClient,
            IKingdeeFinanceClient kingdeeFinanceClient,
            IManyInventoryApiClient manyInventoryApiClient,
            IBDSApiClient bDSApiClient,
            IPurchaseApiClient purchaseApiClient,
            FinanceDbContext db,
            MergeInputBillHelper mergeInputBillHelper)
        {
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            _kingdeeFinanceClient = kingdeeFinanceClient;
            _manyInventoryApiClient = manyInventoryApiClient;
            _bDSApiClient = bDSApiClient;
            _purchaseApiClient = purchaseApiClient;
            _db = db;
            _mergeInputBillHelper = mergeInputBillHelper;
        }
        #region 金蝶相关接口
        /// <summary>
        /// 调用金蝶多发票指定应付接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <param name="shouldCreateDebtRecords">是否需要创建MergeInputBillDebts记录，默认为true</param>
        /// <returns>调用结果</returns>
        public async Task<KingdeeApiResult> InvokeKingdeeManyInvoiceSpecifyApFin(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string currentUserName,
            bool shouldCreateDebtRecords = true)
        {
            try
            {
                _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 开始调用金蝶多发票指定应付接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                // 获取系统月度
                var sysMonth = await _bDSApiClient.GetSystemMonth(mergeInputBill.CompanyId.ToString());
                sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM-dd");

                // 使用辅助类获取原始发票号列表
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeIdAsync(mergeInputBill.Id);

                // 构建多发票指定应付请求
                var manyInvoiceRequest = new ManyInvoiceSpecifyApFinInput
                {
                    coreBillNo = mergeInputBill.MergeInvoiceNumber,
                    invoiceno = originalInvoiceNumbersStr, // 使用原始发票号列表作为 invoiceno
                    finentry = []
                };

                // 创建一个列表用于存储需要记录到MergeInputBillDebt表的数据
                var mergeInputBillDebts = new List<MergeInputBillDebtPo>();

                // 按业务类型分组处理并排序（服务费排到最后）
                var businessTypeGroups = submitDetails.GroupBy(x => x.BusinessType)
                    .OrderBy(g => g.Key == (int)BusinessType.ServiceFeeProcurement ? int.MaxValue : g.Key);
                _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 按业务类型分组, 组数: {Count}", businessTypeGroups.Count());

                foreach (var group in businessTypeGroups)
                {
                    var businessType = (BusinessType)(group.Key ?? 0);
                    var details = group.ToList();

                    _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 处理业务类型: {BusinessType}, 明细数量: {Count}",
                        businessType, details.Count);

                    // 根据业务类型查找对应的应付单号
                    switch (businessType)
                    {
                        case BusinessType.DistributionPurchase: // 经销购货入库
                            var distributionPurchaseDebts = await ProcessDistributionPurchase(details, manyInvoiceRequest, sysMonth, currentUserName, mergeInputBill);
                            if (distributionPurchaseDebts != null && distributionPurchaseDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(distributionPurchaseDebts);
                            }
                            break;
                        case BusinessType.DistributionTransfer: // 经销调出
                            var distributionTransferDebts = await ProcessDistributionTransfer(details, manyInvoiceRequest, sysMonth, currentUserName, mergeInputBill);
                            if (distributionTransferDebts != null && distributionTransferDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(distributionTransferDebts);
                            }
                            break;
                        case BusinessType.ExchangeToReturn: // 换货转退货
                            var exchangeToReturnDebts = await ProcessExchangeToReturn(details, manyInvoiceRequest, sysMonth, currentUserName, mergeInputBill);
                            if (exchangeToReturnDebts != null && exchangeToReturnDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(exchangeToReturnDebts);
                            }
                            break;
                        case BusinessType.ConsignmentToPurchase: // 寄售转购货
                            var consignToPurchaseDebts = await ProcessConsignmentToPurchase(details, manyInvoiceRequest, sysMonth, currentUserName, mergeInputBill);
                            if (consignToPurchaseDebts != null && consignToPurchaseDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(consignToPurchaseDebts);
                            }
                            break;
                        case BusinessType.PurchaseRevision: // 购货修订
                            var otherDebts = await ProcessPurchaseRevision(details, manyInvoiceRequest, sysMonth, currentUserName, mergeInputBill);
                            if (otherDebts != null && otherDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(otherDebts);
                            }
                            break;
                        case BusinessType.ServiceFeeProcurement: // 服务费采购 - 单独处理
                            var serviceFeeDebts = await ProcessServiceFeeProcurement(details, manyInvoiceRequest, sysMonth, currentUserName);
                            if (serviceFeeDebts != null && serviceFeeDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(serviceFeeDebts);
                            }
                            break;
                        case BusinessType.LossRecognition: // 损失确认 - 财务内部处理
                            var lossRecognitionDebts = ProcessLossRecognition(details, manyInvoiceRequest, sysMonth, currentUserName);
                            if (lossRecognitionDebts != null && lossRecognitionDebts.Count > 0)
                            {
                                mergeInputBillDebts.AddRange(lossRecognitionDebts);
                            }
                            break;
                    }
                }
                // 调用金蝶接口
               // var kingdeeResult = await _kingdeeApiClient.ManyInvoiceSpecifyApFin(manyInvoiceRequest);
                var kingdeeResult = await _kingdeeFinanceClient.ManyInvoiceSpecifyApFinAsync(manyInvoiceRequest);

                // 如果调用失败，记录入参信息并尝试通过事件总线异步重试
                if (!kingdeeResult.IsSuccess)
                {
                    _logger.LogError("InvokeKingdeeManyInvoiceSpecifyApFin - 调用金蝶多发票指定应付接口失败, 错误: {ErrorMessage}, 入参: {RequestParams}, 明细数量: {EntryCount}, 尝试通过事件总线异步重试",
                        kingdeeResult.ErrorMessage, manyInvoiceRequest?.ToJson(), manyInvoiceRequest?.finentry.Count);

                    // 通过事件总线发布消息，异步重试
                    // await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-manyInvoiceSpecifyApFin", manyInvoiceRequest);
                    // 即使通过事件总线异步重试，也记录错误，但不抛出异常，继续执行后续逻辑
                    //_logger.LogWarning("InvokeKingdeeManyInvoiceSpecifyApFin - 已通过事件总线异步重试，继续执行后续逻辑");

                    throw new Exception($"【金蝶】多发票指定应付接口异常: {kingdeeResult.ErrorMessage}");
                }
                else
                {
                    _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 调用金蝶多发票指定应付接口成功");
                }
                // 根据参数决定是否保存MergeInputBillDebt记录
                if (shouldCreateDebtRecords && mergeInputBillDebts.Count > 0)
                {
                    _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 保存合并进项发票关联应付记录, 数量: {Count}", mergeInputBillDebts.Count);
                    await _db.MergeInputBillDebts.AddRangeAsync(mergeInputBillDebts);
                    await _db.SaveChangesAsync();
                }
                else if (!shouldCreateDebtRecords && mergeInputBillDebts.Count > 0)
                {
                    _logger.LogInformation("InvokeKingdeeManyInvoiceSpecifyApFin - 根据参数设置，跳过创建MergeInputBillDebts记录, 数量: {Count}", mergeInputBillDebts.Count);
                }


                return kingdeeResult;
            }
            catch (ApplicationException aex)
            {
                _logger.LogError(aex, "InvokeKingdeeManyInvoiceSpecifyApFin - 调用核心指定应付接口异常, 错误: {ErrorMessage}",
                    aex.Message);
                // 直接传递原始异常，保持错误信息格式一致
                throw aex;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeKingdeeManyInvoiceSpecifyApFin - 调用金蝶多发票指定应付接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"【金蝶】多发票指定应付接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用金蝶撤销多发票指定应付接口
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <param name="shouldDeleteDebtRecords">是否需要删除MergeInputBillDebts记录，默认为true</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<int>> InvokeKingdeeRevokeSpecifyApFin(Guid mergeInputBillId, string mergeInvoiceNumber, bool shouldDeleteDebtRecords = true)
        {
            try
            {
                _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 开始调用金蝶撤销多发票指定应付接口, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBillId, mergeInvoiceNumber);

                // 查询合并进项发票关联应付记录
                var mergeInputBillDebts = await _db.MergeInputBillDebts
                    .Where(x => x.MergeInputBillId == mergeInputBillId)
                    .ToListAsync();

                _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 找到合并进项发票关联应付记录, 数量: {Count}",
                    mergeInputBillDebts.Count);

                // 获取所有应付单号
                var debtCodes = mergeInputBillDebts
                    .Select(x => x.DebtCode)
                    .Where(x => !string.IsNullOrEmpty(x))
                    .Distinct()
                    .ToList();

                _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 找到应付单号, 数量: {Count}, 应付单号: {DebtCodes}",
                    debtCodes.Count, string.Join(",", debtCodes));

                // 获取系统月度
                var sysMonth = await GetSystemMonthAsync(mergeInputBillId);

                // 获取原始发票号列表字符串
                var originalInvoiceNumbers = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeIdAsync(mergeInputBillId);

                // 构建撤销请求
                var revokeRequest = new InputBillUnassignInput
                {
                    invoiceno = originalInvoiceNumbers,
                    user = "System", // 使用系统用户名
                    associatedDate = sysMonth
                };

                // 调用金蝶接口
                var result = await _kingdeeApiClient.InputBillUnassign(revokeRequest);

                // 检查结果
                bool isSuccess = false;
                if (result.Code != CodeStatusEnum.Success)
                {
                    _logger.LogError("InvokeKingdeeRevokeSpecifyApFin - 调用金蝶撤销多发票指定应付接口失败, 错误: {ErrorMessage}, 入参: {RequestParams}",
                        result.Message, revokeRequest?.ToJson());

                    // 如果是已存在的错误，视为成功
                    if (result.Message != null && result.Message.Contains("已存在"))
                    {
                        _logger.LogWarning("InvokeKingdeeRevokeSpecifyApFin - 数据已存在, 视为成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                            mergeInvoiceNumber);
                        isSuccess = true;
                    }
                    else
                    {
                        throw new ApplicationException("【金蝶】撤销多发票指定应付接口失败" + result?.Message);
                        // 通过事件总线异步重试
                        //await _daprClient.PublishEventAsync("pubsub-default", "fin-fin-inputBillUnassign", revokeRequest);
                        //_logger.LogWarning("InvokeKingdeeRevokeSpecifyApFin - 已通过事件总线异步重试");
                        //return result;
                    }
                }
                else
                {
                    isSuccess = true;
                }

                // 如果金蝶撤回接口调用成功，处理应付单金额
                if (isSuccess)
                {
                    try
                    {
                        // 根据参数决定是否删除MergeInputBillDebts记录
                        if (shouldDeleteDebtRecords && mergeInputBillDebts.Count > 0)
                        {
                            _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 删除MergeInputBillDebts记录");
                            await RestoreDebtAmountsAndRemoveRelationsAsync(mergeInputBillDebts, "InvokeKingdeeRevokeSpecifyApFin");
                        }
                        else if (!shouldDeleteDebtRecords)
                        {
                            _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 根据参数设置，跳过删除MergeInputBillDebts记录");
                        }

                        //// 处理服务费类型的应付单 - 直接查询提交明细
                        //_logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 处理服务费类型应付单");
                        //await ProcessServiceFeeDebtsAsync(mergeInputBillId, "InvokeKingdeeRevokeSpecifyApFin");
                    }
                    catch (Exception ex)
                    {
                        // 对于其他异常，记录日志并向用户提示
                        _logger.LogError(ex, "InvokeKingdeeRevokeSpecifyApFin - 还原应付单金额失败, 错误: {ErrorMessage}", ex.Message);
                        throw new Exception($"取消勾稽时还原应付单金额失败: {ex.Message}", ex);
                    }
                }

                _logger.LogInformation("InvokeKingdeeRevokeSpecifyApFin - 调用金蝶撤销多发票指定应付接口成功");
                return isSuccess ? BaseResponseData<int>.Success("操作成功") : result;
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeKingdeeRevokeSpecifyApFin - 调用金蝶撤销多发票指定应付接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"【金蝶】撤销多发票指定应付接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeKingdeeRevokeSpecifyApFin - 撤销多发票指定应付接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"【金蝶】撤销多发票指定应付接口异常: {ex.Message}", ex);
            }
        }

        #endregion

        #region 公共辅助方法

        /// <summary>
        /// 获取系统月度
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>系统月度（格式：yyyy-MM-dd）</returns>
        private async Task<string> GetSystemMonthAsync(Guid mergeInputBillId)
        {
            // 默认使用当前日期
            var sysMonth = DateTime.Now.ToString("yyyy-MM-dd");
            try
            {
                // 尝试获取系统月度
                var mergeInputBill = await _db.MergeInputBills
                    .FirstOrDefaultAsync(x => x.Id == mergeInputBillId);

                if (mergeInputBill != null && mergeInputBill.CompanyId != Guid.Empty)
                {
                    var companyId = mergeInputBill.CompanyId.ToString();
                    // 调用BDS API获取系统月度
                    sysMonth = await _bDSApiClient.GetSystemMonth(companyId);
                    sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM-dd");
                    _logger.LogInformation("GetSystemMonthAsync - 获取系统月度成功, 系统月度: {SysMonth}", sysMonth);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("GetSystemMonthAsync - 获取系统月度失败, 使用当前日期, 错误: {ErrorMessage}", ex.Message);
            }
            return sysMonth;
        }

        /// <summary>
        /// 还原应付单金额并删除关联记录
        /// </summary>
        /// <param name="mergeInputBillDebts">合并进项发票关联应付记录</param>
        /// <param name="logPrefix">日志前缀</param>
        /// <returns>处理结果</returns>
        private async Task RestoreDebtAmountsAndRemoveRelationsAsync(List<MergeInputBillDebtPo> mergeInputBillDebts, string logPrefix)
        {
            // 如果没有关联记录，直接返回
            if (mergeInputBillDebts.Count == 0)
            {
                _logger.LogWarning("{LogPrefix} - 未找到合并进项发票关联应付记录", logPrefix);
                return;
            }

            _logger.LogInformation("{LogPrefix} - 找到合并进项发票关联应付记录, 数量: {Count}", logPrefix, mergeInputBillDebts.Count);

            // 删除 MergeInputBillDebts 中对应的记录
            _db.MergeInputBillDebts.RemoveRange(mergeInputBillDebts);
            await _db.SaveChangesAsync();
            _logger.LogInformation("{LogPrefix} - 删除 MergeInputBillDebts 记录成功, 数量: {Count}", logPrefix, mergeInputBillDebts.Count);
        }

        /// <summary>
        /// 处理服务费类型的应付单
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="logPrefix">日志前缀</param>
        /// <returns>处理结果</returns>
        private async Task ProcessServiceFeeDebtsAsync(Guid mergeInputBillId, string logPrefix)
        {
            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(m => m.Id == mergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("{LogPrefix} - 未找到合并进项发票, MergeInputBillId: {MergeInputBillId}",
                        logPrefix, mergeInputBillId);
                    return;
                }

                // 查询提交明细，获取服务费业务单号（bussinessItemCode）
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(d => d.MergeInputBillId == mergeInputBillId && d.BusinessType == (int)BusinessType.ServiceFeeProcurement)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogInformation("{LogPrefix} - 未找到服务费提交明细，跳过处理", logPrefix);
                    return;
                }

                _logger.LogInformation("{LogPrefix} - 找到服务费提交明细, 数量: {Count}", logPrefix, submitDetails.Count);

                // 计算每个业务单号对应的总金额
                var businessCodeToAmountMap = submitDetails
                    .Where(d => !string.IsNullOrEmpty(d.BussinessItemCode))
                    .GroupBy(d => d.BussinessItemCode!)
                    .ToDictionary(
                        g => g.Key,
                        g => Math.Round(g.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity), 2)
                    );

                // 获取所有服务费业务单号
                var businessCodes = businessCodeToAmountMap.Keys.ToList();

                if (businessCodes.Count == 0)
                {
                    _logger.LogWarning("{LogPrefix} - 未找到有效的服务费业务单号，跳过处理", logPrefix);
                    return;
                }

                _logger.LogInformation("{LogPrefix} - 服务费业务单号数量: {Count}", logPrefix, businessCodes.Count);

                // 查询服务费类型的应付单 - 通过BillCode匹配bussinessItemCode
                var serviceDebts = await _db.Debts
                    .Where(d => d.DebtType == DebtTypeEnum.servicefee &&
                           d.BillCode != null && businessCodes.Contains(d.BillCode))
                    .ToListAsync();

                _logger.LogInformation("{LogPrefix} - 找到服务费应付单, 数量: {Count}", logPrefix, serviceDebts.Count);

                // 更新所有服务费应付单
                foreach (var debt in serviceDebts)
                {
                    // 记录更新前的值
                    var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
                    var oldInvoiceStatus = debt.InvoiceStatus;

                    // 计算需要减少的金额
                    decimal deductAmount = 0;
                    if (debt.BillCode != null && businessCodeToAmountMap.TryGetValue(debt.BillCode, out decimal amount))
                    {
                        deductAmount = amount;
                    }

                    // 更新InvoiceAmount，减去对应金额（保持原始符号）
                    if (deductAmount != 0) // 服务费需要处理正数和负数金额
                    {
                        // 直接减去金额，不做任何符号处理
                        debt.InvoiceAmount = oldInvoiceAmount - deductAmount;

                        _logger.LogInformation("{LogPrefix} - 更新服务费应付单金额, 应付单号: {BillCode}, 原金额: {OldAmount}, 减少金额: {DeductAmount}, 新金额: {NewAmount}",
                            logPrefix, debt.BillCode, oldInvoiceAmount, deductAmount, debt.InvoiceAmount);

                        // 如果InvoiceAmount为0，则重置入票状态
                        if (debt.InvoiceAmount == 0)
                        {
                            debt.InvoiceStatus = InvoiceStatusEnum.noninvoice;
                        }

                        // 从Note中移除发票号
                        string invoiceNumberPrefix = "发票号:";
                        if (debt.Note != null && debt.Note.Contains(invoiceNumberPrefix))
                        {
                            // 提取发票号部分
                            int startIndex = debt.Note.IndexOf(invoiceNumberPrefix) + invoiceNumberPrefix.Length;
                            int endIndex = debt.Note.IndexOf(';', startIndex);
                            if (endIndex == -1) endIndex = debt.Note.Length;

                            string invoiceNumbers = debt.Note[startIndex..endIndex];

                            // 移除当前发票号
                            string newInvoiceNumbers = string.Join(",",
                                invoiceNumbers.Split(',')
                                .Where(x => x.Trim() != mergeInputBill.MergeInvoiceNumber));

                            // 更新Note
                            if (string.IsNullOrEmpty(newInvoiceNumbers))
                            {
                                // 如果没有其他发票号，移除整个发票号部分
                                string oldText = $"{invoiceNumberPrefix}{invoiceNumbers}";
                                debt.Note = debt.Note.Replace(oldText, "").Trim();
                                if (debt.Note.StartsWith(';')) debt.Note = debt.Note[1..].Trim();
                            }
                            else
                            {
                                // 更新发票号部分
                                debt.Note = debt.Note.Replace(invoiceNumbers, newInvoiceNumbers);
                            }
                        }

                        // 更新应付单
                        _db.Debts.Update(debt);

                        _logger.LogInformation("{LogPrefix} - 更新服务费应付单, BillCode: {BillCode}, 原金额: {OldAmount}, 减少金额: {DeductAmount}, 新金额: {NewAmount}, 原状态: {OldStatus}, 新状态: {NewStatus}",
                            logPrefix, debt.BillCode, oldInvoiceAmount, deductAmount, debt.InvoiceAmount, oldInvoiceStatus, debt.InvoiceStatus);
                    }
                }

                // 保存更改
                var saveResult = await _db.SaveChangesAsync();
                _logger.LogInformation("{LogPrefix} - 保存服务费应付单更改到数据库, 影响行数: {SaveResult}", logPrefix, saveResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{LogPrefix} - 处理服务费应付单异常, 错误: {ErrorMessage}", logPrefix, ex.Message);
                throw;
            }
        }



        #endregion

        #region 金蝶参数处理方法

        /// <summary>
        /// 处理应付单金额分配的通用方法
        /// </summary>
        /// <param name="manyInvoiceRequest">金蝶请求</param>
        /// <param name="sysMonth">系统月份</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="businessCode">业务单号</param>
        /// <param name="debtList">应付单列表</param>
        /// <param name="totalAmount">总金额</param>
        /// <param name="logPrefix">日志前缀</param>
        /// <param name="isNegativeAmount">是否为负金额（经销调出使用负值）</param>
        /// <returns>处理结果</returns>
        private async Task<List<MergeInputBillDebtPo>> ProcessDebtAllocation(
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string? currentUserName,
            MergeInputBillPo mergeInputBill,
            string? businessCode,
            List<DebtPo> debtList,
            decimal totalAmount,
            string logPrefix,
            bool isNegativeAmount = false)
        {
            // 如果没有找到应付单，则给出提示
            if (debtList.Count == 0)
            {
                throw new ApplicationException($"业务单号：{businessCode}，未找到对应的应付单");
            }

            List<MergeInputBillDebtPo> mergeInputBillDebts;

            // 购货修订业务特殊处理，不需要校验应付金额是否足够，以submitdebt中的金额为准
            if (logPrefix == "ProcessPurchaseRevision")
            {
                _logger.LogInformation("{LogPrefix} - 购货修订业务特殊处理，不校验应付金额是否足够, 业务单号: {BusinessCode}, 总金额: {TotalAmount}",
                    logPrefix, businessCode, totalAmount);

                // 对于购货修订，直接使用第一个应付单，不进行金额分配校验
                var debt = debtList.First();

                // 创建合并进项发票关联应付记录
                var mergeInputBillDebt = new MergeInputBillDebtPo
                {
                    Id = Guid.NewGuid(),
                    MergeInputBillId = mergeInputBill.Id,
                    DebtId = debt.Id,
                    DebtCode = debt.BillCode,
                    DebtAmount = totalAmount,
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.Now
                };

                mergeInputBillDebts = new List<MergeInputBillDebtPo> { mergeInputBillDebt };

                _logger.LogInformation("{LogPrefix} - 购货修订业务特殊处理完成, 应付单号: {DebtCode}, 分配金额: {DebtAmount}",
                    logPrefix, mergeInputBillDebt.DebtCode, mergeInputBillDebt.DebtAmount);
            }
            else
            {
                // 使用辅助类分配应付单金额
                // 注意：如果金额分配不完，辅助类会抛出异常
                mergeInputBillDebts = await _mergeInputBillHelper.AllocateDebtAmountsAsync(
                    debtList,
                    totalAmount,
                    mergeInputBill.Id,
                    logPrefix,
                    isNegativeAmount,
                    businessCode ?? string.Empty);
            }

            // 将分配结果添加到金蝶明细中
            foreach (var debt in mergeInputBillDebts)
            {
                //金额为0不推送金蝶
                if (debt.DebtAmount!=0)
                {
                    // 添加到金蝶明细中
                    manyInvoiceRequest.finentry.Add(new ManyInvoiceSpecifyApFinEntryDto
                    {
                        associatedDate = sysMonth,
                        finNum = debt.DebtCode,
                        f_usedamt = debt.DebtAmount,
                        user = currentUserName ?? "System"
                    });

                    // 设置创建人和创建时间
                    debt.CreatedBy = currentUserName ?? "System";
                    debt.CreatedTime = DateTimeOffset.Now;
                }
                else
                {
                    _logger.LogInformation("{LogPrefix} - 金额为0不分配, 应付单号: {DebtCode}",
                   logPrefix, debt.DebtCode);
                }
            }

            return mergeInputBillDebts;
        }


        /// <summary>
        /// 处理经销购货入库业务类型
        /// </summary>
        private async Task<List<MergeInputBillDebtPo>> ProcessDistributionPurchase(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string currentUserName,
            MergeInputBillPo mergeInputBill)
        {
            var result = new List<MergeInputBillDebtPo>();

            // 获取业务单号列表
            var businessCodes = details.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Distinct()
                .ToList();

            // 获取采购单号列表 - 直接从PurchaseOrderCode字段获取
            var purchaseOrderCodes = details.Select(p => p.PurchaseOrderCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            // 使用辅助类查询应付单，过滤掉 AutoType 为 "98" 和 "99" 的记录
            var excludeAutoTypes = new List<string> { "98", "99" };

            // 合并业务单号和采购单号（过滤掉null值）
            var allCodes = new List<string>();
            foreach (var code in businessCodes)
            {
                if (!string.IsNullOrEmpty(code))
                {
                    allCodes.Add(code);
                }
            }
            foreach (var code in purchaseOrderCodes)
            {
                if (!string.IsNullOrEmpty(code))
                {
                    allCodes.Add(code);
                }
            }

            // 使用辅助类查询应付单
            var debts = await _mergeInputBillHelper.GetDebtsByRelateCodesAsync(allCodes, true, excludeAutoTypes);

            _logger.LogInformation("ProcessDistributionPurchase - 查询应付单, 业务单号数量: {BusinessCodeCount}, 采购单号数量: {PurchaseOrderCodeCount}, 查询到应付单数量: {DebtCount}",
                businessCodes.Count, purchaseOrderCodes.Count, debts.Count);

            foreach (var businessCode in businessCodes)
            {
                // 查找对应的应付单，优先使用采购单号匹配
                var debtList = debts.Where(d =>
                    // 优先使用采购单号匹配
                    (!string.IsNullOrEmpty(d.RelateCode) && d.RelateCode == businessCode)
                ).ToList();

                if (debtList.Count == 0)
                {
                    _logger.LogError("ProcessDistributionPurchase - 未找到对应的应付单, 业务单号/采购单号: {BusinessCode}", businessCode);
                    throw new ApplicationException($"经销购货入库业务类型 - 未找到对应的应付单, 业务单号/采购单号: {businessCode}");
                }

                _logger.LogInformation("ProcessDistributionPurchase - 找到对应的应付单, 业务单号/采购单号: {BusinessCode}, 应付单数量: {DebtCount}",
                    businessCode, debtList.Count);

                // 计算该业务单号下的总金额（根据业务单号分组 sum(quantity*unitcost) 然后四舍五入）
                var totalAmount = Math.Round(details.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => (q.TaxCost ?? 0) * q.MatchQuantity), 2);

                if (totalAmount <= 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 使用通用方法处理应付单金额分配
                var businessResult = await ProcessDebtAllocation(
                    manyInvoiceRequest,
                    sysMonth,
                    currentUserName,
                    mergeInputBill,
                    businessCode,
                    debtList,
                    totalAmount,
                    "ProcessDistributionPurchase",
                    false // 经销购货入库使用正值
                );

                result.AddRange(businessResult);
            }

            return result;
        }

        /// <summary>
        /// 处理经销调出业务类型
        /// </summary>
        private async Task<List<MergeInputBillDebtPo>> ProcessDistributionTransfer(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string currentUserName,
            MergeInputBillPo mergeInputBill)
        {
            var result = new List<MergeInputBillDebtPo>();

            // 获取业务单号列表
            var businessCodes = details.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            // 获取采购单号列表
            var purchaseOrderCodes = details.Select(p => p.PurchaseOrderCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0 && purchaseOrderCodes.Count == 0) return result;

            // 使用辅助类查询应付单，过滤掉 AutoType 为 "98" 和 "99" 的记录
            var excludeAutoTypes = new List<string> { "98", "99" };

            // 合并业务单号和采购单号
            var allCodes = new List<string>();
            allCodes.AddRange(businessCodes);
            allCodes.AddRange(purchaseOrderCodes);

            // 使用辅助类查询应付单
            var debts = await _mergeInputBillHelper.GetDebtsByRelateCodesAsync(allCodes, true, excludeAutoTypes);

            foreach (var businessCode in businessCodes)
            {
                // 获取当前业务单号对应的明细
                var currentDetails = details.Where(t => t.BussinessItemCode == businessCode).ToList();
                if (currentDetails.Count == 0) continue;

                // 获取当前业务单号对应的采购单号列表
                var currentPurchaseOrderCodes = currentDetails
                    .Select(p => p.PurchaseOrderCode)
                    .Where(c => !string.IsNullOrEmpty(c))
                    .Distinct()
                    .ToList();

                // 查找对应的应付单
                var debtList = debts.Where(d =>
                    d.RelateCode == businessCode
                ).ToList();

                if (debtList.Count == 0)
                {
                    _logger.LogError("ProcessDistributionTransfer - 未找到对应的应付单, 业务单号: {BusinessCode}", businessCode);
                    throw new ApplicationException($"经销调出业务类型 - 未找到对应的应付单, 业务单号: {businessCode}");
                }

                // 计算该业务单号下的总金额（根据业务单号分组 sum(quantity*unitcost) 然后四舍五入，经销调出保留负数）
                var totalAmount = Math.Round(details.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => (q.TaxCost ?? 0) * q.MatchQuantity), 2);

                // 经销调出的金额应该是负数，所以检查绝对值是否为0
                if (Math.Abs(totalAmount) <= 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 记录详细日志，帮助排查问题
                _logger.LogInformation("ProcessDistributionTransfer - 经销调出金额详情, 业务单号: {BusinessCode}, 总金额: {TotalAmount}, 明细数量: {DetailCount}",
                    businessCode, totalAmount, details.Where(t => t.BussinessItemCode == businessCode).Count());

                // 记录每个明细的金额计算过程
                foreach (var detail in details.Where(t => t.BussinessItemCode == businessCode))
                {
                    decimal amount = Math.Round((detail.TaxCost ?? 0) * detail.MatchQuantity, 2);
                    _logger.LogInformation("ProcessDistributionTransfer - 明细金额计算, 业务单号: {BusinessCode}, 品名: {ProductName}, 匹配数量: {MatchQuantity}, 含税单价: {TaxCost}, 计算金额: {Amount}",
                        businessCode, detail.ProductName, detail.MatchQuantity, detail.TaxCost, amount);
                }

                // 使用通用方法处理应付单金额分配
                var businessResult = await ProcessDebtAllocation(
                    manyInvoiceRequest,
                    sysMonth,
                    currentUserName,
                    mergeInputBill,
                    businessCode,
                    debtList,
                    totalAmount,
                    "ProcessDistributionTransfer",
                    true // 经销调出使用负值
                );

                result.AddRange(businessResult);
            }

            return result;
        }

        /// <summary>
        /// 处理换货转退货业务类型
        /// </summary>
        private async Task<List<MergeInputBillDebtPo>> ProcessExchangeToReturn(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string currentUserName,
            MergeInputBillPo mergeInputBill)
        {
            var result = new List<MergeInputBillDebtPo>();

            // 获取业务单号列表
            var businessCodes = details.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            // 使用辅助类查询应付单，过滤掉 AutoType 为 "98" 和 "99" 的记录
            var excludeAutoTypes = new List<string> { "98", "99" };

            // 使用辅助类查询应付单
            var debts = await _mergeInputBillHelper.GetDebtsByRelateCodesAsync(businessCodes, true, excludeAutoTypes);

            foreach (var businessCode in businessCodes)
            {
                // 查找对应的应付单
                var debtList = debts.Where(d => d.RelateCode == businessCode).ToList();
                if (debtList.Count == 0)
                {
                    _logger.LogError("ProcessExchangeToReturn - 未找到对应的应付单, 业务单号: {BusinessCode}", businessCode);
                    throw new ApplicationException($"换货转退货业务类型 - 未找到对应的应付单, 业务单号: {businessCode}");
                }

                // 计算该业务单号下的总金额（根据业务单号分组 sum(quantity*unitcost) 然后四舍五入）
                var totalAmount = Math.Round(details.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => (q.TaxCost ?? 0) * q.MatchQuantity), 2);

                if (totalAmount == 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 使用通用方法处理应付单金额分配
                var businessResult = await ProcessDebtAllocation(
                    manyInvoiceRequest,
                    sysMonth,
                    currentUserName,
                    mergeInputBill,
                    businessCode,
                    debtList,
                    totalAmount,
                    "ProcessExchangeToReturn",
                    true // 换货转退货使用负值，与经销调出保持一致
                );

                result.AddRange(businessResult);
            }

            return result;
        }

        /// <summary>
        /// 处理其他业务类型（寄售转购货、购货修订）
        /// </summary>
        private async Task<List<MergeInputBillDebtPo>> ProcessConsignmentToPurchase(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string currentUserName,
            MergeInputBillPo mergeInputBill)
        {
            var result = new List<MergeInputBillDebtPo>();

            // 获取业务单号列表
            var businessCodes = details.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            // 使用辅助类查询应付单，过滤掉 AutoType 为 "98" 和 "99" 的记录
            var excludeAutoTypes = new List<string> { "98", "99" };

            var allDebts = await _db.Debts
                .Where(d => !string.IsNullOrEmpty(d.BillCode) && businessCodes.Contains(d.BillCode))
                .Where(d => d.AutoType != "98" && d.AutoType != "99")
                .ToListAsync();

            // 对于寄售转购货，需要根据业务单号查找对应的应付单
            foreach (var businessCode in businessCodes)
            {
                if (string.IsNullOrEmpty(businessCode)) continue;

                // 查找对应的应付单
                var debtList = allDebts.Where(d => d.BillCode == businessCode).ToList();

                // 计算该业务单号下的总金额（使用含税金额TotalAmount而不是税额TaxAmount）
                var totalAmount = details.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => q.TotalAmount ?? 0);

                if (totalAmount == 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 如果没有找到应付单，抛出异常
                if (debtList.Count == 0)
                {
                    _logger.LogError("ProcessOtherBusinessTypes - 未找到对应的应付单, 业务单号: {BusinessCode}", businessCode);
                    throw new ApplicationException($"寄售转购货业务类型 - 未找到对应的应付单, 业务单号: {businessCode}");
                }
                else
                {
                    // 使用通用方法处理应付单金额分配
                    var businessResult = await ProcessDebtAllocation(
                        manyInvoiceRequest,
                        sysMonth,
                        currentUserName,
                        mergeInputBill,
                        businessCode,
                        debtList,
                        totalAmount,
                        "ProcessConsignmentToPurchase",
                        false
                    );

                    result.AddRange(businessResult);
                }
            }

            return result;
        }
        /// <summary>
        /// 购货修订
        /// </summary>
        /// <param name="details"></param>
        /// <param name="manyInvoiceRequest"></param>
        /// <param name="sysMonth"></param>
        /// <param name="currentUserName"></param>
        /// <param name="mergeInputBill"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        private async Task<List<MergeInputBillDebtPo>> ProcessPurchaseRevision(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string currentUserName,
            MergeInputBillPo mergeInputBill)
        {
            var result = new List<MergeInputBillDebtPo>();

            // 获取业务单号列表
            var businessCodes = details.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            // 使用辅助类查询应付单，过滤掉 AutoType 为 "98" 和 "99" 的记录
            var excludeAutoTypes = new List<string> { "98", "99" };

            var allDebts = await _db.Debts
                .Where(d => !string.IsNullOrEmpty(d.BillCode) && businessCodes.Contains(d.BillCode))
                .Where(d => d.AutoType != "98" && d.AutoType != "99")
                .ToListAsync();

            // 对于寄售转购货、购货修订，需要根据业务单号查找对应的应付单
            foreach (var businessCode in businessCodes)
            {
                if (string.IsNullOrEmpty(businessCode)) continue;

                // 查找对应的应付单
                var debtList = allDebts.Where(d => d.BillCode == businessCode).ToList();

                // 计算该业务单号下的总金额（使用含税金额TotalAmount而不是税额TaxAmount）
                var totalAmount = details.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => q.TotalAmount ?? 0);

                if (totalAmount == 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 如果没有找到应付单，抛出异常
                if (debtList.Count == 0)
                {
                    _logger.LogError("ProcessPurchaseRevision - 未找到对应的应付单, 业务单号: {BusinessCode}", businessCode);
                    throw new ApplicationException($"购货修订业务类型 - 未找到对应的应付单, 业务单号: {businessCode}");
                }
                else
                {

                    // 使用通用方法处理应付单金额分配
                    bool isNegativeAmount = totalAmount < 0;//true 代表负数
                    var businessResult = await ProcessDebtAllocation(
                        manyInvoiceRequest,
                        sysMonth,
                        currentUserName,
                        mergeInputBill,
                        businessCode,
                        debtList,
                        totalAmount,
                        "ProcessPurchaseRevision",
                        isNegativeAmount
                    );

                    result.AddRange(businessResult);
                }
            }

            return result;
        }

        /// <summary>
        /// 处理服务费采购业务类型 - 通过bussinessItemCode查找对应的应付单billCode
        /// </summary>
        private async Task<List<MergeInputBillDebtPo>> ProcessServiceFeeProcurement(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string? currentUserName)
        {
            // 服务费不需要创建MergeInputBillDebt记录，直接返回空列表
            var result = new List<MergeInputBillDebtPo>();

            // 筛选服务费类型的明细
            var serviceFeeDetails = details.Where(x => x.BusinessType == (int)BusinessType.ServiceFeeProcurement).ToList();
            if (serviceFeeDetails.Count == 0)
            {
                _logger.LogInformation("ProcessServiceFeeProcurement - 没有服务费类型的明细，跳过处理");
                return result;
            }

            // 获取业务单号列表
            var businessCodes = serviceFeeDetails.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            _logger.LogInformation("ProcessServiceFeeProcurement - 处理服务费采购, 业务单号数量: {Count}", businessCodes.Count);

            // 直接查询服务费类型的应付单，通过BillCode匹配bussinessItemCode
            var allDebts = await _db.Debts
                .Where(d => d.DebtType == DebtTypeEnum.servicefee &&
                       d.BillCode != null && businessCodes.Contains(d.BillCode))
                .Where(d => d.AutoType != "98" && d.AutoType != "99")
                .ToListAsync();

            _logger.LogInformation("ProcessServiceFeeProcurement - 查询到服务费应付单数量: {Count}", allDebts.Count);

            // 对于服务费采购，需要根据业务单号查找对应的应付单BillCode
            foreach (var businessCode in businessCodes)
            {
                if (string.IsNullOrEmpty(businessCode)) continue;

                // 计算该业务单号下的总金额（使用含税金额TotalAmount而不是税额TaxAmount）
                var totalAmount = serviceFeeDetails.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => q.TotalAmount ?? 0);

                if (totalAmount == 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 添加到金蝶明细中
                manyInvoiceRequest.finentry.Add(new ManyInvoiceSpecifyApFinEntryDto
                {
                    associatedDate = sysMonth,
                    finNum = businessCode,
                    f_usedamt = totalAmount,
                    user = currentUserName ?? "System"
                });

                _logger.LogInformation("ProcessServiceFeeProcurement - 添加服务费金蝶明细, 业务单号: {BusinessCode}, 总金额: {TotalAmount}",
                    businessCode, totalAmount);
            }

            // 服务费不需要创建MergeInputBillDebt记录，直接返回空列表
            return result;
        }

        /// <summary>
        /// 处理损失确认类型的应付单 - 参照服务费采购逻辑
        /// </summary>
        private List<MergeInputBillDebtPo> ProcessLossRecognition(
            List<MergeInputBillSubmitDetailPo> details,
            ManyInvoiceSpecifyApFinInput manyInvoiceRequest,
            string sysMonth,
            string? currentUserName)
        {
            // 损失确认不需要创建MergeInputBillDebt记录，直接返回空列表
            var result = new List<MergeInputBillDebtPo>();

            // 筛选损失确认类型的明细
            var lossRecognitionDetails = details.Where(x => x.BusinessType == (int)BusinessType.LossRecognition).ToList();
            if (lossRecognitionDetails.Count == 0)
            {
                _logger.LogInformation("ProcessLossRecognition - 没有损失确认类型的明细，跳过处理");
                return result;
            }

            // 获取业务单号列表
            var businessCodes = lossRecognitionDetails.Select(p => p.BussinessItemCode)
                .Where(c => !string.IsNullOrEmpty(c))
                .Select(c => c!) // 非空断言，因为已经过滤掉了null和空字符串
                .Distinct()
                .ToList();

            if (businessCodes.Count == 0) return result;

            _logger.LogInformation("ProcessLossRecognition - 处理损失确认, 业务单号数量: {Count}", businessCodes.Count);

            // 对于损失确认，需要根据业务单号查找对应的应付单BillCode
            foreach (var businessCode in businessCodes)
            {
                if (string.IsNullOrEmpty(businessCode)) continue;

                // 计算该业务单号下的总金额（使用TaxCost * MatchQuantity计算）
                var totalAmount = lossRecognitionDetails.Where(t => t.BussinessItemCode == businessCode)
                    .Sum(q => (q.TaxCost ?? 0) * q.MatchQuantity);

                if (totalAmount == 0) continue;

                // 格式化金额为两位小数
                totalAmount = decimal.Parse(totalAmount.ToString("F2"));

                // 添加到金蝶明细中
                manyInvoiceRequest.finentry.Add(new ManyInvoiceSpecifyApFinEntryDto
                {
                    associatedDate = sysMonth,
                    finNum = businessCode,
                    f_usedamt = totalAmount,
                    user = currentUserName ?? "System"
                });

                _logger.LogInformation("ProcessLossRecognition - 添加损失确认金蝶明细, 业务单号: {BusinessCode}, 总金额: {TotalAmount}",
                    businessCode, totalAmount);
            }

            // 损失确认不需要创建MergeInputBillDebt记录，直接返回空列表
            return result;
        }



        #endregion

        #region 库存相关接口
        /// <summary>
        /// 调用库存能力中心更新入库单明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateManyStoreInDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateManyStoreInDetail - 开始调用库存能力中心更新入库单明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 按品名ID、品名、税率、含税单价和业务单号分组
                var detailGroups = details.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.TaxRate,
                    x.TaxCost,
                    x.BussinessItemCode
                });
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInputBill.MergeInvoiceNumber);
                // 构建更新入库单明细的请求
                var updateDetails = new ManyStoreInUpdateInput
                {
                    invoiceNumber = originalInvoiceNumbersStr,
                    invoiceDate = new DateTimeOffset(mergeInputBill.MergeTime).ToUnixTimeMilliseconds(), // 使用MergeTime替代InvoiceDate
                    invoiceTypeStr = mergeInputBill.Type.GetDescription(), // 使用合并进项发票中的类型
                    inputInvoiceDetails = detailGroups.Select(group =>
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        var totalQuantity = groupDetails.Sum(d => d.MatchQuantity);
                        var totalAmount = groupDetails.Sum(d => d.TotalAmount ?? 0); // 使用含税金额TotalAmount而不是税额TaxAmount
                        var taxRate = key.TaxRate ?? 0;
                        var unitCost = key.TaxCost ?? 0;
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        _logger.LogInformation("InvokeUpdateManyStoreInDetail - 品名: {ProductName}, 总数量: {TotalQuantity}, 总金额: {TotalAmount}",
                            productName, totalQuantity, totalAmount);

                        return new ManyInputInvoiceDetail
                        {
                            businessCode = businessCode,
                            currentInvoiceQuantity = totalQuantity,
                            invoiceAmount = totalAmount,
                            productNameId = key.ProductNameId?.ToString() ?? string.Empty, // 直接使用分组键中的ProductNameId
                            productName = productName,
                            taxRate = taxRate,
                            unitCost = unitCost
                            // 不包含货号和厂家字段
                        };
                    }).ToList()
                };

                _logger.LogInformation("InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(updateDetails));

                // 调用库存能力中心接口更新入库单明细
                var result = await _manyInventoryApiClient.UpdateManyToManyStoreInDetailForFinance(updateDetails);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用库存能力中心更新入库单明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心更新入库单明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateManyStoreInDetail - 调用库存能力中心更新入库单明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心更新入库单明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用库存能力中心撤销入库单明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeInventoryStoreInDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeInventoryStoreInDetail - 开始调用库存能力中心撤销入库单明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                // 构建撤销请求，支持多个发票号
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                _logger.LogInformation("InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用库存能力中心接口撤销入库单明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreInDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口失败, 错误: 返回结果为空");
                    throw new Exception("撤销入库单明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销入库单明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeInventoryStoreInDetail - 调用库存能力中心撤销入库单明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销入库单明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用库存能力中心更新换货转退货明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateExchangeToReturnDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateExchangeToReturnDetail - 开始调用库存能力中心更新换货转退货明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 按品名ID、品名、税率、含税单价和业务单号分组
                var detailGroups = details.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.TaxRate,
                    x.TaxCost,
                    x.BussinessItemCode
                });
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInputBill.MergeInvoiceNumber);
                // 构建更新换货转退货明细的请求
                var updateDetails = new ManyInventoryExchangeToReturnUpdateDetail
                {
                    invoiceNumber = originalInvoiceNumbersStr,
                    invoiceDate = new DateTimeOffset(mergeInputBill.MergeTime).ToUnixTimeMilliseconds(), // 使用MergeTime替代InvoiceDate
                    invoiceTypeStr = mergeInputBill.Type.GetDescription(), // 使用合并进项发票中的类型
                    inputInvoiceDetails = detailGroups.Select(group =>
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        var totalQuantity = Math.Abs(groupDetails.Sum(d => d.MatchQuantity));
                        // 使用 MatchQuantity 和 TaxCost 计算含税金额
                        var totalAmount = Math.Abs(Math.Round(groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity), 2));
                        var taxRate = key.TaxRate ?? 0;
                        var unitCost = key.TaxCost ?? 0;
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        _logger.LogInformation("InvokeUpdateExchangeToReturnDetail - 品名: {ProductName}, 总数量: {TotalQuantity}, 总金额: {TotalAmount}",
                            productName, totalQuantity, totalAmount);

                        return new ManyInputInvoiceDetail
                        {
                            businessCode = businessCode,
                            currentInvoiceQuantity = totalQuantity,
                            invoiceAmount = totalAmount,
                            productNameId = key.ProductNameId?.ToString() ?? string.Empty, // 直接使用分组键中的ProductNameId
                            productName = productName,
                            taxRate = taxRate,
                            unitCost = unitCost
                            // 不包含货号和厂家字段
                        };
                    }).ToList()
                };

                _logger.LogInformation("InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(updateDetails));

                // 创建 ManyToManyInventoryStoreExchangeBackUpdateDetail 对象
                var manyToManyUpdateDetails = new ManyStoreExchangeBackUpdateInput
                {
                    invoiceNumber = updateDetails.invoiceNumber,
                    invoiceDate = updateDetails.invoiceDate,
                    invoiceTypeStr = updateDetails.invoiceTypeStr,
                    inputInvoiceDetails = updateDetails.inputInvoiceDetails
                };

                // 调用库存能力中心接口更新换货转退货明细
                var result = await _manyInventoryApiClient.UpdateManyToManyStoreExchangeBackDetailForFinance(manyToManyUpdateDetails);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用库存能力中心更新换货转退货明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心更新换货转退货明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateExchangeToReturnDetail - 调用库存能力中心更新换货转退货明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"更新换货转退货明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用库存能力中心撤销换货转退货明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeExchangeToReturnDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeExchangeToReturnDetail - 开始调用库存能力中心撤销换货转退货明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                // 构建撤销请求，支持多个发票号
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                _logger.LogInformation("InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用库存能力中心接口撤销换货转退货明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreExchangeBackDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用库存能力中心撤销换货转退货明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销换货转退货明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeExchangeToReturnDetail - 调用库存能力中心撤销换货转退货明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销换货转退货明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用库存能力中心更新经销调出明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateDistributionTransferDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateDistributionTransferDetail - 开始调用库存能力中心更新经销调出明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 按品名ID、品名、税率、含税单价和业务单号分组
                var detailGroups = details.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.TaxRate,
                    x.TaxCost,
                    x.BussinessItemCode
                });
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInputBill.MergeInvoiceNumber);
                // 构建更新经销调出明细的请求
                var updateDetails = new ManyInventoryDistributionTransferUpdateDetail
                {
                    invoiceNumber = originalInvoiceNumbersStr,
                    invoiceDate = new DateTimeOffset(mergeInputBill.MergeTime).ToUnixTimeMilliseconds(), // 使用MergeTime替代InvoiceDate
                    invoiceTypeStr = mergeInputBill.Type.GetDescription(), // 使用合并进项发票中的类型
                    inputInvoiceDetails = detailGroups.Select(group =>
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        var totalQuantity = Math.Abs(groupDetails.Sum(d => d.MatchQuantity));
                        // 使用 MatchQuantity 和 TaxCost 计算含税金额
                        var totalAmount = Math.Abs(Math.Round(groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity), 2));
                        var taxRate = key.TaxRate ?? 0;
                        var unitCost = key.TaxCost ?? 0;
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        _logger.LogInformation("InvokeUpdateDistributionTransferDetail - 品名: {ProductName}, 总数量: {TotalQuantity}, 总金额: {TotalAmount}",
                            productName, totalQuantity, totalAmount);

                        return new ManyInputInvoiceDetail
                        {
                            businessCode = businessCode,
                            currentInvoiceQuantity = totalQuantity,
                            invoiceAmount = totalAmount,
                            productNameId = key.ProductNameId?.ToString() ?? string.Empty, // 直接使用分组键中的ProductNameId
                            productName = productName,
                            taxRate = taxRate,
                            unitCost = unitCost
                            // 不包含货号和厂家字段
                        };
                    }).ToList()
                };

                _logger.LogInformation("InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(updateDetails));

                // 创建 ManyToManyInventoryStoreOutUpdateDetail 对象
                var manyToManyUpdateDetails = new ManyStoreOutUpdateInput
                {
                    invoiceNumber = updateDetails.invoiceNumber,
                    invoiceDate = updateDetails.invoiceDate,
                    invoiceTypeStr = updateDetails.invoiceTypeStr,
                    inputInvoiceDetails = updateDetails.inputInvoiceDetails
                };

                // 调用库存能力中心接口更新经销调出明细
                var result = await _manyInventoryApiClient.UpdateManyToManyStoreOutDetailForFinance(manyToManyUpdateDetails);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用库存能力中心更新经销调出明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心更新经销调出明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateDistributionTransferDetail - 调用库存能力中心更新经销调出明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"更新经销调出明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用库存能力中心撤销经销调出明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeDistributionTransferDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeDistributionTransferDetail - 开始调用库存能力中心撤销经销调出明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                // 创建 RevokeInventoryStoreInDetail 对象，支持多个发票号
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                _logger.LogInformation("InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用库存能力中心接口撤销经销调出明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreOutDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用库存能力中心撤销经销调出明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口成功");
                // 创建新的响应对象，确保类型正确
                return new BaseResponseData<bool>
                {
                    Code = result.Code,
                    Message = result.Message,
                    Data = true
                };
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销经销调出明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeDistributionTransferDetail - 调用库存能力中心撤销经销调出明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用库存能力中心撤销经销调出明细接口异常: {ex.Message}", ex);
            }
        }
        #endregion


        #region 采购相关接口
        /// <summary>
        /// 调用采购能力中心更新寄售转购货明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateConsignToPurchaseDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateConsignToPurchaseDetail - 开始调用采购能力中心更新寄售转购货明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 按品名ID、品名、税率、含税单价和业务单号分组
                var detailGroups = details.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.TaxRate,
                    x.TaxCost,
                    x.BussinessItemCode
                });

                // 构建更新寄售转购货明细的请求
                var updateDetails = new UpdateInvoiceQuantityForFinanceInvoiceInput
                {
                    invoiceNumber = mergeInputBill.MergeInvoiceNumber,
                    InvoiceFlag = true, // 入票
                    List = detailGroups.Select(group =>
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        var totalQuantity = (int)groupDetails.Sum(d => d.MatchQuantity);
                        var taxRate = key.TaxRate ?? 0;
                        var unitCost = key.TaxCost ?? 0;
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        // 计算匹配金额总和
                        var matchAmount = Math.Round(groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity), 2);

                        _logger.LogInformation("InvokeUpdateConsignToPurchaseDetail - 品名: {ProductName}, 总数量: {TotalQuantity}, 税率: {TaxRate}, 单价: {UnitCost}",
                            productName, totalQuantity, taxRate, unitCost);

                        return new UpdateInvoiceQuantityForFinanceInput
                        {
                            PurchaseOrderCode = businessCode,
                            InvoiceQuantity = totalQuantity,
                            UnitCost = unitCost,
                            TaxRate = taxRate,
                            ProductNameId = key.ProductNameId?.ToString() ?? string.Empty, // 直接使用分组键中的ProductNameId
                            ProductName = productName
                        };
                    }).ToList()
                };

                _logger.LogInformation("InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(updateDetails));

                // 调用采购能力中心接口更新寄售转购货明细
                var result = await _purchaseApiClient.UpdateConsignToPurchaseDetailGroupForInvoice(updateDetails);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口失败, 错误: 返回结果为空");
                    throw new ApplicationException("调用采购能力中心更新寄售转购货明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new ApplicationException(errorMessage);
                }

                _logger.LogInformation("InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口成功");
                return result;
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new ApplicationException($"调用采购能力中心更新寄售转购货明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateConsignToPurchaseDetail - 调用采购能力中心更新寄售转购货明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new ApplicationException($"调用采购能力中心更新寄售转购货明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用采购能力中心撤销寄售转购货明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeConsignToPurchaseDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeConsignToPurchaseDetail - 开始调用采购能力中心撤销寄售转购货明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);

                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("InvokeRevokeConsignToPurchaseDetail - 合并发票号为空，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(m => m.MergeInvoiceNumber == mergeInvoiceNumber)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("InvokeRevokeConsignToPurchaseDetail - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询提交明细
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(d => d.MergeInputBillId == mergeInputBill.Id && d.BusinessType == (int)BusinessType.ConsignmentToPurchase)
                    .ToListAsync();

                if (submitDetails == null || submitDetails.Count == 0)
                {
                    _logger.LogWarning("InvokeRevokeConsignToPurchaseDetail - 未找到寄售转购货提交明细, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokeConsignToPurchaseDetail - 找到寄售转购货提交明细, 数量: {Count}", submitDetails.Count);

                // 构建明细列表
                var detailList = submitDetails.Select(detail => new UpdateInvoiceQuantityForFinanceInput
                {
                    PurchaseOrderCode = detail.BussinessItemCode ?? string.Empty,
                    InvoiceQuantity = (int)detail.MatchQuantity, // 转换为int类型
                    UnitCost = detail.TaxCost ?? 0,
                    TaxRate = detail.TaxRate ?? 0,
                    ProductNameId = detail.ProductNameId?.ToString() ?? string.Empty,
                    ProductName = detail.ProductName ?? string.Empty
                }).ToList();

                // 构建撤销请求
                var revokeRequest = new UpdateInvoiceQuantityForFinanceInvoiceInput
                {
                    invoiceNumber = mergeInvoiceNumber,
                    InvoiceFlag = false, // 取消入票
                    List = detailList // 传递查询到的明细
                };

                _logger.LogInformation("InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用采购能力中心接口撤销寄售转购货明细
                var result = await _purchaseApiClient.UpdateConsignToPurchaseDetailGroupForInvoice(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口失败, 错误: 返回结果为空");
                    throw new Exception("撤销寄售转购货明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口成功");
                return result;
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用采购能力中心撤销寄售转购货明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeConsignToPurchaseDetail - 调用采购能力中心撤销寄售转购货明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用采购能力中心撤销寄售转购货明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用采购能力中心更新购货修订明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdatePurchaseRevisionDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 开始调用采购能力中心更新购货修订明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 首先按照金额正负分组
                var positiveDetails = details.Where(d => (d.TaxCost ?? 0) > 0).ToList();
                var negativeDetails = details.Where(d => (d.TaxCost ?? 0) < 0).ToList();

                _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 按金额正负分组, 正数金额明细数量: {PositiveCount}, 负数金额明细数量: {NegativeCount}",
                    positiveDetails.Count, negativeDetails.Count);

                // 构建更新购货修订明细的请求
                var updateDetails = new UpdateInvoiceAmountForFinanceInvoiceInput
                {
                    invoiceNumber = mergeInputBill.MergeInvoiceNumber,
                    InvoiceFlag = true, // 入票
                    List = []
                };

                // 处理正数金额明细
                if (positiveDetails.Count > 0)
                {
                    // 按品名ID、品名和业务单号分组
                    var positiveGroups = positiveDetails.GroupBy(x => new
                    {
                        x.ProductNameId,
                        x.ProductName,
                        x.BussinessItemCode
                    });

                    foreach (var group in positiveGroups)
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        // 使用 MatchQuantity 和 TaxCost 计算总金额
                        var totalAmount = groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity);
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 正数金额明细, 品名: {ProductName}, 总金额: {TotalAmount}",
                            productName, totalAmount);

                        updateDetails.List.Add(new UpdateInvoiceAmountForFinanceInput
                        {
                            PurchaseOrderCode = businessCode,
                            InvoiceAmount = totalAmount,
                            ProductNameId = key.ProductNameId?.ToString() ?? string.Empty,
                            ProductName = productName
                        });
                    }
                }

                // 处理负数金额明细
                if (negativeDetails.Count > 0)
                {
                    // 按品名ID、品名和业务单号分组
                    var negativeGroups = negativeDetails.GroupBy(x => new
                    {
                        x.ProductNameId,
                        x.ProductName,
                        x.BussinessItemCode
                    });

                    foreach (var group in negativeGroups)
                    {
                        var key = group.Key;
                        var productName = key.ProductName ?? string.Empty;
                        var groupDetails = group.ToList();

                        // 汇总同一分组下的数据
                        // 使用 MatchQuantity 和 TaxCost 计算总金额
                        var totalAmount =groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity);
                        var businessCode = key.BussinessItemCode ?? string.Empty;

                        _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 负数金额明细, 品名: {ProductName}, 总金额: {TotalAmount}",
                            productName, totalAmount);

                        updateDetails.List.Add(new UpdateInvoiceAmountForFinanceInput
                        {
                            PurchaseOrderCode = businessCode,
                            InvoiceAmount = totalAmount,
                            ProductNameId = key.ProductNameId?.ToString() ?? string.Empty,
                            ProductName = productName
                        });
                    }
                }

                _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(updateDetails));

                // 调用采购能力中心接口更新购货修订明细
                var result = await _purchaseApiClient.UpdateReviseOrderForFinanceInvoice(updateDetails);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口失败, 错误: 返回结果为空");
                    throw new Exception("更新购货修订明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                _logger.LogInformation("InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口成功");
                return result;
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用采购能力中心更新购货修订明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdatePurchaseRevisionDetail - 调用采购能力中心更新购货修订明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"更新购货修订明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调用采购能力中心撤销购货修订明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokePurchaseRevisionDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokePurchaseRevisionDetail - 开始调用采购能力中心撤销购货修订明细接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);

                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("InvokeRevokePurchaseRevisionDetail - 合并发票号为空，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(m => m.MergeInvoiceNumber == mergeInvoiceNumber)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("InvokeRevokePurchaseRevisionDetail - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询提交明细
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(d => d.MergeInputBillId == mergeInputBill.Id && d.BusinessType == (int)BusinessType.PurchaseRevision)
                    .ToListAsync();

                if (submitDetails == null || submitDetails.Count == 0)
                {
                    _logger.LogWarning("InvokeRevokePurchaseRevisionDetail - 未找到购货修订提交明细, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokePurchaseRevisionDetail - 找到购货修订提交明细, 数量: {Count}", submitDetails.Count);

                // 构建明细列表
                var detailList = BuildPurchaseRevisionDetailList(submitDetails, "InvokeRevokePurchaseRevisionDetail");

                // 构建撤销请求
                var revokeRequest = new UpdateInvoiceAmountForFinanceInvoiceInput
                {
                    invoiceNumber = mergeInvoiceNumber,
                    InvoiceFlag = false, // 取消入票
                    List = detailList // 传递查询到的明细
                };

                _logger.LogInformation("InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用采购能力中心接口撤销购货修订明细
                var result = await _purchaseApiClient.UpdateReviseOrderForFinanceInvoice(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口失败, 错误: 返回结果为空");
                    throw new Exception("调用采购能力中心撤销购货修订明细接口失败: 返回结果为空");
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 直接返回原始错误信息，不进行额外解析
                    throw new Exception(errorMessage);
                }

                // 如果接口调用成功，还原应付单金额
                var mergeInputBillDebts = await _db.MergeInputBillDebts
                    .Where(d => d.MergeInputBillId == mergeInputBill.Id)
                    .ToListAsync();

                if (mergeInputBillDebts.Count > 0)
                {
                    await RestoreDebtAmountsAndRemoveRelationsAsync(mergeInputBillDebts, "InvokeRevokePurchaseRevisionDetail");
                }

                _logger.LogInformation("InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口成功");
                return result;
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用采购能力中心撤销购货修订明细接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokePurchaseRevisionDetail - 调用采购能力中心撤销购货修订明细接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用采购能力中心撤销购货修订明细接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 构建购货修订明细列表
        /// </summary>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="logPrefix">日志前缀</param>
        /// <returns>购货修订明细列表</returns>
        private List<UpdateInvoiceAmountForFinanceInput> BuildPurchaseRevisionDetailList(
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string logPrefix)
        {
            var detailList = new List<UpdateInvoiceAmountForFinanceInput>();

            // 首先按照金额正负分组
            var positiveDetails = submitDetails.Where(d => (d.TaxCost ?? 0) > 0).ToList();
            var negativeDetails = submitDetails.Where(d => (d.TaxCost ?? 0) < 0).ToList();

            _logger.LogInformation("{LogPrefix} - 按金额正负分组, 正数金额明细数量: {PositiveCount}, 负数金额明细数量: {NegativeCount}",
                logPrefix, positiveDetails.Count, negativeDetails.Count);

            // 处理正数金额明细
            if (positiveDetails.Count > 0)
            {
                // 按品名ID、品名和业务单号分组
                var positiveGroups = positiveDetails.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.BussinessItemCode
                });

                foreach (var group in positiveGroups)
                {
                    var key = group.Key;
                    var productName = key.ProductName ?? string.Empty;
                    var groupDetails = group.ToList();

                    // 汇总同一分组下的数据
                    // 使用 MatchQuantity 和 TaxCost 计算总金额
                    var totalAmount =groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity);
                    var businessCode = key.BussinessItemCode ?? string.Empty;

                    _logger.LogInformation("{LogPrefix} - 正数金额明细, 品名: {ProductName}, 总金额: {TotalAmount}",
                        logPrefix, productName, totalAmount);

                    detailList.Add(new UpdateInvoiceAmountForFinanceInput
                    {
                        PurchaseOrderCode = businessCode,
                        InvoiceAmount = totalAmount,
                        ProductNameId = key.ProductNameId?.ToString() ?? string.Empty,
                        ProductName = productName
                    });
                }
            }

            // 处理负数金额明细
            if (negativeDetails.Count > 0)
            {
                // 按品名ID、品名和业务单号分组
                var negativeGroups = negativeDetails.GroupBy(x => new
                {
                    x.ProductNameId,
                    x.ProductName,
                    x.BussinessItemCode
                });

                foreach (var group in negativeGroups)
                {
                    var key = group.Key;
                    var productName = key.ProductName ?? string.Empty;
                    var groupDetails = group.ToList();

                    // 汇总同一分组下的数据
                    // 使用 MatchQuantity 和 TaxCost 计算总金额
                    var totalAmount = groupDetails.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity);
                    var businessCode = key.BussinessItemCode ?? string.Empty;

                    _logger.LogInformation("{LogPrefix} - 负数金额明细, 品名: {ProductName}, 总金额: {TotalAmount}",
                        logPrefix, productName, totalAmount);

                    detailList.Add(new UpdateInvoiceAmountForFinanceInput
                    {
                        PurchaseOrderCode = businessCode,
                        InvoiceAmount = totalAmount,
                        ProductNameId = key.ProductNameId?.ToString() ?? string.Empty,
                        ProductName = productName
                    });
                }
            }

            return detailList;
        }

        /// <summary>
        /// 调用服务费采购更新接口 - 直接更新Debt表的InvoiceAmount字段
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateServiceFeeProcurementDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 开始调用服务费采购更新接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 筛选服务费类型的明细
                var serviceFeeDetails = details.Where(x => x.BusinessType == (int)BusinessType.ServiceFeeProcurement).ToList();
                if (serviceFeeDetails.Count == 0)
                {
                    _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 没有服务费类型的明细，跳过调用");
                    return BaseResponseData<bool>.Success(true);
                }

                // 计算每个业务单号对应的金额
                var businessCodeToAmountMap = CalculateBusinessCodeToAmountMap(serviceFeeDetails);
                var billCodes = businessCodeToAmountMap.Keys.ToList();

                if (billCodes.Count == 0)
                {
                    _logger.LogWarning("InvokeUpdateServiceFeeProcurementDetail - 没有有效的应付单号，跳过更新");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 服务费业务单号数量: {Count}", billCodes.Count);

                // 查询服务费类型的应付单
                var debts = await _db.Debts
                    .Where(d => d.DebtType == DebtTypeEnum.servicefee &&
                           d.BillCode != null && billCodes.Contains(d.BillCode))
                    .ToListAsync();

                if (debts.Count == 0)
                {
                    _logger.LogWarning("InvokeUpdateServiceFeeProcurementDetail - 未找到对应的服务费应付记录，跳过更新");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 找到服务费应付记录数量: {Count}", debts.Count);

                // 更新 Debt 记录
                foreach (var debt in debts)
                {
                    var billCode = debt.BillCode;
                    if (string.IsNullOrEmpty(billCode) || !businessCodeToAmountMap.TryGetValue(billCode, out decimal invoiceAmount))
                    {
                        _logger.LogWarning("InvokeUpdateServiceFeeProcurementDetail - 应付单号 {BillCode} 没有对应的金额信息，跳过更新", billCode);
                        continue;
                    }

                    // 记录更新前的值
                    var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
                    var oldInvoiceStatus = debt.InvoiceStatus;

                    try
                    {
                        // 使用公共方法更新发票金额
                        UpdateDebtInvoiceAmountForSubmit(debt, invoiceAmount, "服务费");

                        // 更新发票号信息
                        UpdateDebtInvoiceNote(debt, mergeInputBill.MergeInvoiceNumber);

                        _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 更新应付记录: BillCode={BillCode}, 原InvoiceAmount={OldInvoiceAmount}, 新InvoiceAmount={NewInvoiceAmount}, 原InvoiceStatus={OldInvoiceStatus}, 新InvoiceStatus={NewInvoiceStatus}",
                            debt.BillCode, oldInvoiceAmount, debt.InvoiceAmount, oldInvoiceStatus, debt.InvoiceStatus);
                    }
                    catch (ApplicationException ex)
                    {
                        _logger.LogError("InvokeUpdateServiceFeeProcurementDetail - {ErrorMessage}", ex.Message);
                        throw;
                    }
                }

                // 保存更改到数据库
                var saveResult = await _db.SaveChangesAsync();
                _logger.LogInformation("InvokeUpdateServiceFeeProcurementDetail - 保存更改到数据库, 影响行数: {SaveResult}", saveResult);

                return BaseResponseData<bool>.Success(true);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateServiceFeeProcurementDetail - 调用服务费采购更新接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用服务费采购更新接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateServiceFeeProcurementDetail - 调用服务费采购更新接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"服务费采购更新接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 撤销服务费采购明细 - 直接更新Debt表的InvoiceAmount字段
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeServiceFeeProcurementDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 开始撤销服务费采购明细, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);

                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("InvokeRevokeServiceFeeProcurementDetail - 合并发票号为空，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(m => m.MergeInvoiceNumber == mergeInvoiceNumber)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("InvokeRevokeServiceFeeProcurementDetail - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询提交明细，获取服务费业务单号（bussinessItemCode）
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(d => d.MergeInputBillId == mergeInputBill.Id && d.BusinessType == (int)BusinessType.ServiceFeeProcurement)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 未找到服务费提交明细，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 找到服务费提交明细, 数量: {Count}", submitDetails.Count);

                // 计算每个业务单号对应的总金额
                var businessCodeToAmountMap = CalculateBusinessCodeToAmountMap(submitDetails);
                var businessCodes = businessCodeToAmountMap.Keys.ToList();

                if (businessCodes.Count == 0)
                {
                    _logger.LogWarning("InvokeRevokeServiceFeeProcurementDetail - 未找到有效的服务费业务单号，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 服务费业务单号数量: {Count}", businessCodes.Count);

                // 查询服务费类型的应付单
                var serviceDebts = await _db.Debts
                    .Where(d => d.DebtType == DebtTypeEnum.servicefee &&
                           d.BillCode != null && businessCodes.Contains(d.BillCode))
                    .ToListAsync();

                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 找到服务费应付单, 数量: {Count}", serviceDebts.Count);

                // 更新所有服务费应付单
                foreach (var debt in serviceDebts)
                {
                    // 记录更新前的值
                    var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
                    var oldInvoiceStatus = debt.InvoiceStatus;

                    // 计算需要减少的金额
                    decimal deductAmount = 0;
                    if (debt.BillCode != null && businessCodeToAmountMap.TryGetValue(debt.BillCode, out decimal amount))
                    {
                        deductAmount = amount;
                    }

                    try
                    {
                        // 使用公共方法更新发票金额
                        UpdateDebtInvoiceAmountForRevoke(debt, deductAmount, "服务费");

                        // 移除发票号信息
                        RemoveDebtInvoiceNote(debt, mergeInvoiceNumber);

                        // 更新应付单
                        _db.Debts.Update(debt);

                        _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 更新服务费应付单, BillCode: {BillCode}, 原金额: {OldAmount}, 减少金额: {DeductAmount}, 新金额: {NewAmount}, 原状态: {OldStatus}, 新状态: {NewStatus}",
                            debt.BillCode, oldInvoiceAmount, deductAmount, debt.InvoiceAmount, oldInvoiceStatus, debt.InvoiceStatus);
                    }
                    catch (ApplicationException ex)
                    {
                        _logger.LogError("InvokeRevokeServiceFeeProcurementDetail - {ErrorMessage}", ex.Message);
                        throw;
                    }
                }

                // 保存更改
                var saveResult = await _db.SaveChangesAsync();
                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 保存更改到数据库, 影响行数: {SaveResult}", saveResult);

                _logger.LogInformation("InvokeRevokeServiceFeeProcurementDetail - 撤销服务费采购明细成功");
                return BaseResponseData<bool>.Success(true);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeServiceFeeProcurementDetail - 撤销服务费采购明细超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"撤销服务费采购明细超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeServiceFeeProcurementDetail - 撤销服务费采购明细异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"撤销服务费采购明细异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 用于比较两个DebtPo对象是否相同的比较器
        /// </summary>
        private class DebtIdComparer : IEqualityComparer<DebtPo>
        {
            public bool Equals(DebtPo? x, DebtPo? y)
            {
                if (ReferenceEquals(x, y)) return true;
                if (x is null || y is null) return false;
                return x.Id == y.Id;
            }

            public int GetHashCode(DebtPo obj)
            {
                return obj != null ? obj.Id.GetHashCode() : 0;
            }
        }

        #region 债务发票金额处理公共方法

        /// <summary>
        /// 计算业务单号对应的金额映射
        /// </summary>
        /// <param name="details">提交明细</param>
        /// <returns>业务单号到金额的映射</returns>
        private static Dictionary<string, decimal> CalculateBusinessCodeToAmountMap(List<MergeInputBillSubmitDetailPo> details)
        {
            return details
                .Where(d => !string.IsNullOrEmpty(d.BussinessItemCode))
                .GroupBy(d => d.BussinessItemCode!)
                .ToDictionary(
                    g => g.Key,
                    g => Math.Round(g.Sum(d => (d.TaxCost ?? 0) * d.MatchQuantity), 2)
                );
        }

        /// <summary>
        /// 更新债务记录的发票号信息
        /// </summary>
        /// <param name="debt">债务记录</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        private static void UpdateDebtInvoiceNote(DebtPo debt, string mergeInvoiceNumber)
        {
            const string invoiceNumberPrefix = "发票号:";
            if (string.IsNullOrEmpty(debt.Note))
            {
                debt.Note = $"{invoiceNumberPrefix}{mergeInvoiceNumber}";
            }
            else if (!debt.Note.Contains(mergeInvoiceNumber))
            {
                if (debt.Note.Contains(invoiceNumberPrefix))
                {
                    debt.Note = debt.Note.Replace(invoiceNumberPrefix, $"{invoiceNumberPrefix}{mergeInvoiceNumber},");
                }
                else
                {
                    debt.Note = $"{debt.Note}; {invoiceNumberPrefix}{mergeInvoiceNumber}";
                }
            }
        }

        /// <summary>
        /// 从债务记录中移除发票号信息
        /// </summary>
        /// <param name="debt">债务记录</param>
        /// <param name="mergeInvoiceNumber">要移除的合并发票号</param>
        private static void RemoveDebtInvoiceNote(DebtPo debt, string mergeInvoiceNumber)
        {
            const string invoiceNumberPrefix = "发票号:";
            if (debt.Note != null && debt.Note.Contains(invoiceNumberPrefix))
            {
                // 提取发票号部分
                int startIndex = debt.Note.IndexOf(invoiceNumberPrefix) + invoiceNumberPrefix.Length;
                int endIndex = debt.Note.IndexOf(';', startIndex);
                if (endIndex == -1) endIndex = debt.Note.Length;

                string invoiceNumbers = debt.Note[startIndex..endIndex];

                // 移除当前发票号
                string newInvoiceNumbers = string.Join(",",
                    invoiceNumbers.Split(',')
                    .Where(x => x.Trim() != mergeInvoiceNumber));

                // 更新Note
                if (string.IsNullOrEmpty(newInvoiceNumbers))
                {
                    // 如果没有其他发票号，移除整个发票号部分
                    string oldText = $"{invoiceNumberPrefix}{invoiceNumbers}";
                    debt.Note = debt.Note.Replace(oldText, "").Trim();
                    if (debt.Note.StartsWith(';')) debt.Note = debt.Note[1..].Trim();
                }
                else
                {
                    // 更新发票号部分
                    debt.Note = debt.Note.Replace(invoiceNumbers, newInvoiceNumbers);
                }
            }
        }

        /// <summary>
        /// 校验债务记录的发票金额是否有效
        /// </summary>
        /// <param name="debt">债务记录</param>
        /// <param name="invoiceAmount">发票金额</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>校验是否通过</returns>
        private static bool ValidateDebtInvoiceAmount(DebtPo debt, decimal invoiceAmount, out string errorMessage)
        {
            var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
            var newInvoiceAmount = oldInvoiceAmount + invoiceAmount;
            
            if (Math.Abs(newInvoiceAmount) > Math.Abs(debt.Value))
            {
                decimal remainingAmount = debt.Value - oldInvoiceAmount;
                errorMessage = $"应付单{debt.BillCode}本次入票金额{invoiceAmount}，超过可关联的应付金额{remainingAmount}";
                return false;
            }
            
            errorMessage = string.Empty;
            return true;
        }

        /// <summary>
        /// 更新债务记录的发票金额 - 提交操作
        /// </summary>
        /// <param name="debt">债务记录</param>
        /// <param name="invoiceAmount">发票金额</param>
        /// <param name="debtTypeName">债务类型名称（用于错误消息）</param>
        private static void UpdateDebtInvoiceAmountForSubmit(DebtPo debt, decimal invoiceAmount, string debtTypeName)
        {
            // 先校验，不修改实体
            if (!ValidateDebtInvoiceAmount(debt, invoiceAmount, out string errorMessage))
            {
                throw new ApplicationException($"{debtTypeName}{errorMessage}");
            }
            
            // 校验通过后再修改
            var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
            debt.InvoiceAmount = oldInvoiceAmount + invoiceAmount;
            debt.InvoiceStatus = InvoiceStatusEnum.invoiced;
        }

        /// <summary>
        /// 更新债务记录的发票金额 - 撤销操作
        /// </summary>
        /// <param name="debt">债务记录</param>
        /// <param name="deductAmount">要减少的金额</param>
        /// <param name="debtTypeName">债务类型名称（用于错误消息）</param>
        private static void UpdateDebtInvoiceAmountForRevoke(DebtPo debt, decimal deductAmount, string debtTypeName)
        {
            // 记录更新前的值
            var oldInvoiceAmount = debt.InvoiceAmount ?? 0;

            if (deductAmount != 0)
            {
                // 检查恢复金额的绝对值是否大于原来的金额
                if (Math.Abs(deductAmount) > Math.Abs(oldInvoiceAmount))
                {
                    string errorMessage = $"{debtTypeName}应付单{debt.BillCode}数据异常：恢复金额绝对值{Math.Abs(deductAmount)}大于原金额绝对值{Math.Abs(oldInvoiceAmount)}，请检查数据";
                    throw new ApplicationException(errorMessage);
                }
                
                // 计算新的InvoiceAmount
                decimal newInvoiceAmount = oldInvoiceAmount - deductAmount;
                // 更新InvoiceAmount
                debt.InvoiceAmount = newInvoiceAmount;
            }

            // 如果InvoiceAmount为0，则重置入票状态
            if (debt.InvoiceAmount == 0)
            {
                debt.InvoiceStatus = InvoiceStatusEnum.noninvoice;
            }
        }

        #endregion


        #endregion

        #region 损失确认相关接口

        /// <summary>
        /// 调用损失确认更新接口 - 直接更新Debt表的InvoiceAmount字段
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeUpdateLossRecognitionDetail(MergeInputBillPo mergeInputBill, List<MergeInputBillSubmitDetailPo> details)
        {
            try
            {
                _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 开始调用损失确认更新接口, MergeInvoiceNumber: {MergeInvoiceNumber}, 明细数量: {Count}",
                    mergeInputBill.MergeInvoiceNumber, details.Count);

                // 筛选损失确认类型的明细
                var lossRecognitionDetails = details.Where(x => x.BusinessType == (int)BusinessType.LossRecognition).ToList();
                if (lossRecognitionDetails.Count == 0)
                {
                    _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 没有损失确认类型的明细，跳过调用");
                    return BaseResponseData<bool>.Success(true);
                }

                // 计算每个业务单号对应的总金额
                var businessCodeToAmountMap = CalculateBusinessCodeToAmountMap(lossRecognitionDetails);
                var businessCodes = businessCodeToAmountMap.Keys.ToList();

                if (businessCodes.Count == 0)
                {
                    _logger.LogWarning("InvokeUpdateLossRecognitionDetail - 没有有效的应付单号，跳过更新");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 损失确认业务单号数量: {Count}", businessCodes.Count);

                // 查询损失确认类型的应付单
                var debts = await _db.Debts
                    .Where(d => d.DebtType == DebtTypeEnum.lossrecognition &&
                           d.BillCode != null && businessCodes.Contains(d.BillCode))
                    .ToListAsync();

                if (debts.Count == 0)
                {
                    _logger.LogWarning("InvokeUpdateLossRecognitionDetail - 未找到对应的损失确认应付记录，跳过更新");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 找到损失确认应付记录数量: {Count}", debts.Count);

                // 更新 Debt 记录
                foreach (var debt in debts)
                {
                    var billCode = debt.BillCode;
                    if (string.IsNullOrEmpty(billCode) || !businessCodeToAmountMap.TryGetValue(billCode, out decimal invoiceAmount))
                    {
                        _logger.LogWarning("InvokeUpdateLossRecognitionDetail - 应付单号 {BillCode} 没有对应的金额信息，跳过更新", billCode);
                        continue;
                    }

                    // 记录更新前的值
                    var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
                    var oldInvoiceStatus = debt.InvoiceStatus;

                    try
                    {
                        // 使用公共方法更新发票金额
                        UpdateDebtInvoiceAmountForSubmit(debt, invoiceAmount, "损失确认");

                        // 更新发票号信息
                        UpdateDebtInvoiceNote(debt, mergeInputBill.MergeInvoiceNumber);

                        _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 更新应付记录: BillCode={BillCode}, 原InvoiceAmount={OldInvoiceAmount}, 新InvoiceAmount={NewInvoiceAmount}, 原InvoiceStatus={OldInvoiceStatus}, 新InvoiceStatus={NewInvoiceStatus}",
                            debt.BillCode, oldInvoiceAmount, debt.InvoiceAmount, oldInvoiceStatus, debt.InvoiceStatus);
                    }
                    catch (ApplicationException ex)
                    {
                        _logger.LogError("InvokeUpdateLossRecognitionDetail - {ErrorMessage}", ex.Message);
                        throw;
                    }
                }

                // 保存更改到数据库
                var saveResult = await _db.SaveChangesAsync();
                _logger.LogInformation("InvokeUpdateLossRecognitionDetail - 保存更改到数据库, 影响行数: {SaveResult}", saveResult);

                return BaseResponseData<bool>.Success(true);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeUpdateLossRecognitionDetail - 调用损失确认更新接口超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"调用损失确认更新接口超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeUpdateLossRecognitionDetail - 调用损失确认更新接口异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"损失确认更新接口异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 撤销损失确认明细 - 直接更新Debt表的InvoiceAmount字段
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        public async Task<BaseResponseData<bool>> InvokeRevokeLossRecognitionDetail(string mergeInvoiceNumber)
        {
            try
            {
                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 开始撤销损失确认明细, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);

                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("InvokeRevokeLossRecognitionDetail - 合并发票号为空，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(m => m.MergeInvoiceNumber == mergeInvoiceNumber)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("InvokeRevokeLossRecognitionDetail - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInvoiceNumber);
                    return BaseResponseData<bool>.Success(true);
                }

                // 查询提交明细，获取损失确认业务单号（bussinessItemCode）
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(d => d.MergeInputBillId == mergeInputBill.Id && d.BusinessType == (int)BusinessType.LossRecognition)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 未找到损失确认提交明细，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 找到损失确认提交明细, 数量: {Count}", submitDetails.Count);

                // 计算每个业务单号对应的总金额
                var businessCodeToAmountMap = CalculateBusinessCodeToAmountMap(submitDetails);
                var businessCodes = businessCodeToAmountMap.Keys.ToList();

                if (businessCodes.Count == 0)
                {
                    _logger.LogWarning("InvokeRevokeLossRecognitionDetail - 未找到有效的损失确认业务单号，跳过撤销");
                    return BaseResponseData<bool>.Success(true);
                }

                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 损失确认业务单号数量: {Count}", businessCodes.Count);

                // 查询损失确认类型的应付单
                var lossRecognitionDebts = await _db.Debts
                    .Where(d => d.DebtType == DebtTypeEnum.lossrecognition &&
                           d.BillCode != null && businessCodes.Contains(d.BillCode))
                    .ToListAsync();

                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 找到损失确认应付单, 数量: {Count}", lossRecognitionDebts.Count);

                // 更新所有损失确认应付单
                foreach (var debt in lossRecognitionDebts)
                {
                    // 记录更新前的值
                    var oldInvoiceAmount = debt.InvoiceAmount ?? 0;
                    var oldInvoiceStatus = debt.InvoiceStatus;

                    // 计算需要减少的金额
                    decimal deductAmount = 0;
                    if (debt.BillCode != null && businessCodeToAmountMap.TryGetValue(debt.BillCode, out decimal amount))
                    {
                        deductAmount = amount; // 传递原始金额给公共方法，让公共方法处理绝对值
                    }

                    try
                    {
                        // 使用公共方法更新发票金额
                        UpdateDebtInvoiceAmountForRevoke(debt, deductAmount, "损失确认");

                        // 移除发票号信息
                        RemoveDebtInvoiceNote(debt, mergeInvoiceNumber);

                        // 更新应付单
                        _db.Debts.Update(debt);

                        _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 更新损失确认应付单, BillCode: {BillCode}, 原金额: {OldAmount}, 减少金额: {DeductAmount}, 新金额: {NewAmount}, 原状态: {OldStatus}, 新状态: {NewStatus}",
                            debt.BillCode, oldInvoiceAmount, Math.Abs(deductAmount), debt.InvoiceAmount, oldInvoiceStatus, debt.InvoiceStatus);
                    }
                    catch (ApplicationException ex)
                    {
                        _logger.LogError("InvokeRevokeLossRecognitionDetail - {ErrorMessage}", ex.Message);
                        throw;
                    }
                }

                // 保存更改
                var saveResult = await _db.SaveChangesAsync();
                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 保存更改到数据库, 影响行数: {SaveResult}", saveResult);

                _logger.LogInformation("InvokeRevokeLossRecognitionDetail - 撤销损失确认明细成功");
                return BaseResponseData<bool>.Success(true);
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "InvokeRevokeLossRecognitionDetail - 撤销损失确认明细超时(60秒), 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"撤销损失确认明细超时(60秒): {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "InvokeRevokeLossRecognitionDetail - 撤销损失确认明细异常, 错误: {ErrorMessage}",
                    ex.Message);
                throw new Exception($"撤销损失确认明细异常: {ex.Message}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// 接口调用服务接口
    /// </summary>
    public interface IInterfaceInvocationService
    {
        /// <summary>
        /// 调用金蝶多发票指定应付接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>调用结果</returns>
        Task<KingdeeApiResult> InvokeKingdeeManyInvoiceSpecifyApFin(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string currentUserName,bool shouldCreateDebtRecords=true);

        /// <summary>
        /// 调用库存能力中心更新入库单明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateManyStoreInDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用库存能力中心撤销入库单明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeInventoryStoreInDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用金蝶撤销多发票指定应付接口
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<int>> InvokeKingdeeRevokeSpecifyApFin(Guid mergeInputBillId, string mergeInvoiceNumber, bool shouldCreateDebtRecords = true);

        /// <summary>
        /// 调用库存能力中心更新换货转退货明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateExchangeToReturnDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用库存能力中心撤销换货转退货明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeExchangeToReturnDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用库存能力中心更新经销调出明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateDistributionTransferDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用库存能力中心撤销经销调出明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeDistributionTransferDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用采购能力中心更新寄售转购货明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateConsignToPurchaseDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用采购能力中心撤销寄售转购货明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeConsignToPurchaseDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用采购能力中心更新购货修订明细接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdatePurchaseRevisionDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用采购能力中心撤销购货修订明细接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokePurchaseRevisionDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用服务费采购更新接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateServiceFeeProcurementDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用服务费采购撤销接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeServiceFeeProcurementDetail(string mergeInvoiceNumber);

        /// <summary>
        /// 调用损失确认更新接口
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">勾稽明细</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeUpdateLossRecognitionDetail(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details);

        /// <summary>
        /// 调用损失确认撤销接口
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>调用结果</returns>
        Task<BaseResponseData<bool>> InvokeRevokeLossRecognitionDetail(string mergeInvoiceNumber);
    }
}
