﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 获取原始开票明细查询 入参
    /// </summary>
    public class OriginDetailQueryInput : BaseQuery
    {

        /// <summary>
        /// 关联业务单号 多选
        /// </summary>
        public List<string?>? RelateCodes { get; set; }
        /// <summary>
        /// 订单号 多选
        /// </summary>
        public List<string?>? OrderNos { get; set; }
        /// <summary>
        /// 应收单号 多选
        /// </summary>
        public List<string?>? CreditBillCodes { get; set; }
        /// <summary>
        /// 付款单位id 多选
        /// </summary>
        public List<string?>? CustomerIds { get; set; }
        /// <summary>
        /// 付款单位名称 多选
        /// </summary>
        public List<string?>? CustomerNames { get; set; }
        /// <summary>
        /// 应收金额 多选
        /// </summary>
        public List<decimal>? Values { get; set; }
        /// <summary>
        /// 选择数据 多选
        /// </summary>
        public List<CreditQueryListOutput>? CheckData { get; set; }
    }

    public class OriginDetailQueryInputHand
    {
        public List<string> RelateCodes { get; set; } 
        /// <summary>
        /// 应收单号 多选
        /// </summary>
        public List<string> CreditBillCodes { get; set; }
    }
}
