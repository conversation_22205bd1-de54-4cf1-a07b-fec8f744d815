﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationStockQueryController : BaseController
    {
        public IStoreApiClient _storeApiClient;
        public readonly IReconciliationStockQueryService _reconciliationStockQueryService;
        public ReconciliationStockQueryController(IStoreApiClient storeApiClient, IReconciliationStockQueryService reconciliationStockQueryService, ISubLogService subLog) : base(subLog)
        {
            this._storeApiClient = storeApiClient;
            this._reconciliationStockQueryService = reconciliationStockQueryService;
        }
        [HttpPost("GetList")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListPages([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationStockQueryService.GetListPages(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        [HttpPost("GetListByAgent")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListByAgent([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationStockQueryService.GetListByAgent(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
        [HttpPost("GetListByCustomer")]
        public async Task<BaseResponseData<PageResponse<ReconciliationOutput>>> GetListByCustomer([FromBody] ReconciliationItemInput input)
        {
            var result = await _reconciliationStockQueryService.GetListByCustomer(input);
            var res = new BaseResponseData<PageResponse<ReconciliationOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<ReconciliationOutput>
                {
                    List = result.List,
                    Total = result.Total
                },
            };
            return res;
        }
    }
}
