﻿using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 应付
    /// </summary>
    public class DebtQueryOutput
    {
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }
        /// <summary>
        /// 相关联应收单Id
        /// </summary> 
        public Guid? CreditId { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 应付类型
        /// </summary>
        public DebtTypeEnum? DebtType { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public InvoiceStatusEnum? InvoiceStatus { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }

        /// <summary>
        /// 应付值
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 是否自动批量
        /// </summary>
        public int? Auto { get; set; }
        /// <summary>
        /// 自动批量类型
        /// </summary>
        public string? AutoType { get; set; }

        /// <summary>
        /// 自动批量类型名称
        /// </summary>
        public string? AutoTypeName { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>  
        public string? RelateCode { get; set; }

        public Guid? PaymentId { get; set; }

        public string? AccountPeriodScale { get; set; }


        public string? PerPaymentCodes { get; set; }

        /// <summary>
        /// 应付明细清单
        /// </summary>
        public List<DebtDetailQueryOutput>? DebtDetailList { get; set; } = new List<DebtDetailQueryOutput>();
    }

    /// <summary>
    /// 应付明细
    /// </summary>
    public class DebtDetailQueryOutput
    {
        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int AccountPeriodType { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }

        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }


        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 预付款日期
        /// </summary>  
        public DateTime? ProbablyPayTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 应付明细执行清单
        /// </summary>
        public List<DebtDetailExecuteQueryOutput>? DebtDetailExecuteList { get; set; } = new List<DebtDetailExecuteQueryOutput>();
    }

    /// <summary>
    /// 应付明细执行
    /// </summary>
    public class DebtDetailExecuteQueryOutput
    {
        /// <summary>
        /// 应付执行计划Id
        /// </summary>
        public Guid DebtDetailId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }
    }

    public class DebtOfProjectOutput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string DebtCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 实付金额
        /// </summary>
        public decimal ActualTotalValue { get; set; }

        /// <summary>
        /// 实付时间
        /// </summary>
        public DateTimeOffset AbatementTime { get; set; }
    }
    /// <summary>
    /// 查询应付单总金额出参
    /// </summary>
    public class DebtSumAmountQueryOutput
    {
        /// <summary>
        /// 项目单号 
        /// </summary> 
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 项目名称 
        /// </summary> 
        public string? ProjectName { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary> 
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 应付单总金额
        /// </summary>
        public decimal AllDebtAmount {  get; set; }
        /// <summary>
        /// 应付单总冲销金额
        /// </summary>
        public decimal AllAbatmentAmount { get; set; }
        /// <summary>
        /// 应付单总剩余金额
        /// </summary>

        public decimal AllBalanceAmount {  get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public long BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public long EndTime {  get; set; }
    }
}
