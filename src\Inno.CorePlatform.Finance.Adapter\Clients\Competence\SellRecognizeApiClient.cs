﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class SellRecognizeApiClient : BaseDaprApiClient<SellRecognizeApiClient>, ISellRecognizeApiClient
    {
        public SellRecognizeApiClient(DaprClient daprClient, ILogger<SellRecognizeApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }    
        public async Task<BaseResponseData<bool?>> CreateSellRecognizeReceive(List<SellRecognizeReceiveInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<SellRecognizeReceiveInput>, BaseResponseData<bool?>>(input, AppCenter.Sell_CreateSellRecognizeReceive, RequestMethodEnum.POST);
        }
        public async Task<BaseResponseData<bool?>> CancelSellRecognizeReceive(List<CancelReceiptInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<CancelReceiptInput>, BaseResponseData<bool?>>(input, AppCenter.Sell_CancelSellRecognizeReceive, RequestMethodEnum.POST);
        }

        public async Task<BaseResponseData<bool?>> Finished(string code)
        {
            return await InvokeMethodAsync<BaseResponseData<bool?>>(string.Format(AppCenter.Sell_RecognizeFinished, code), RequestMethodEnum.POST); 
        }

        public async Task<BaseResponseData<SaleSystemCompanyOutput?>> GetCompanyBySaleSystemId(string id)
        {
            return await InvokeMethodAsync<BaseResponseData<SaleSystemCompanyOutput?>>(string.Format(AppCenter.Sell_GetCompanyBySaleSystemId, id), RequestMethodEnum.POST);
        }

        public async Task<BaseResponseData<SaleSystemBaseOutput>> GetCompanyListBySaleSystemId(SaleSystemCompanyQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<SaleSystemCompanyQueryInput, BaseResponseData<SaleSystemBaseOutput>>(input, AppCenter.Sell_GetCompanyListBySaleSystemId, RequestMethodEnum.POST);
        }

        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }
        protected override string GetAppId()
        {
            return AppCenter.Sell_APPID;
        }

        public async Task<BaseResponseData<SellsDetailPageData>> GetSalesDetailByCodes(List<string> codes)
        {
            var p = new { pageIndex = 1, pageSize = 100000, saleCodes = codes };
            return await InvokeMethodWithQueryObjectAsync<object, BaseResponseData<SellsDetailPageData>>(p, AppCenter.Sell_SalesDetailQuery, RequestMethodEnum.POST);
        }
    }
}
