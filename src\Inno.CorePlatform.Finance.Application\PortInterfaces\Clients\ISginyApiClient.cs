﻿using EasyCaching.Core.Configurations;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sginy;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface ISginyApiClient
    {
        /// <summary>
        /// 创建跟台盘点
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns></returns>
        public Task<SginyInventoryCreateOutputDto> CreateSignyInventory(SginyInventoryCreateRequestDto request);
        /// <summary>
        /// 跟台-财务获取盘点统计数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ReconciliationOutput>> FinanceSummary(ReconciliationInput input);
        /// <summary>
        /// 跟台-根据id查询跟台手术数据信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<StageSurgeryOutput> StageSurgery_SelectById(StageSurgeryInput input);

        Task<CheckDataWithKingdeeOutputDto> QueryStageSurgeryToKingdee(CheckDataWithKingdeeInputDto input);
    }
}
