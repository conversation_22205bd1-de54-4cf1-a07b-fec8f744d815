﻿using Dapr.Client;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.UC;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class UCApiClient : BaseDaprApiClient<UCApiClient>, IUCApiClient
    {
        public UCApiClient(DaprClient daprClient, ILogger<UCApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        protected override string GetAppId()
        {

            return AppCenter.USERCENTER_APPID;
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetUserListByEmployeeIdsOutput>> GetUserListByEmployeeIds(List<string> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<string>, List<GetUserListByEmployeeIdsOutput>>(input, AppCenter.UC_GET_USERLISTBYEMPLOYEEIDS);
        }
        

    }
}
