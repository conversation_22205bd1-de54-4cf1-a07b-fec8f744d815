﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class CreditAgeOutput
    {
        public string CreditCode { get; set; }
        public string CustomerName { get; set; }
        public string CreditType { get; set; }
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 未确认收入的不含税金额
        /// </summary>
        public decimal UnSureAmount { get; set; }
        
        /// <summary>
        /// 已确认收入未开票的不含税金额
        /// </summary>
        public decimal SureAndNoInvoiceAmount { get; set; }

        /// <summary>
        /// 已确认收入已开票的不含税金额
        /// </summary>
        public decimal SureAndInvoiceAmount { get; set; }

        /// <summary>
        /// 未确认收入未开票的税额
        /// </summary>
        public decimal UnSureAndNoInvoiceTaxAmount { get; set; }

        /// <summary>
        /// 已确认收入未开票的税额
        /// </summary>
        public decimal SureAndNoInvoiceTaxAmount { get; set; }

        /// <summary>
        /// 已开票的税额
        /// </summary>
        public decimal InvoiceTaxAmount { get; set; }

        /// <summary>
        /// 冲销
        /// </summary>
        public decimal AbatementAmount { get; set; }

        /// <summary>
        /// 应收余额
        /// </summary>
        public decimal UnAbatementAmount { get { return Amount - AbatementAmount; } }
    }

    public class CreditBalanceOutput 
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 已收款金额
        /// </summary>
        public decimal ReceiveAmount { get; set; }

        /// <summary>
        /// 红冲金额
        /// </summary>
        public decimal RedAmount { get; set; } = 0.00m;

        /// <summary>
        /// 未收款金额
        /// </summary>
        public decimal UnReceiveAmount { get { return Amount - RedAmount - ReceiveAmount; } }

        /// <summary>
        /// 发票日期
        /// </summary>
        public DateTime InvoiceDate { get; set; } 
    }
    /// <summary>
    /// 应收明细
    /// </summary>
    public class CreditDetailsOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditNo { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }


        /// <summary>
        /// 未收款金额
        /// </summary>
        public decimal UnAbatmentAmount { get { return Amount - AbatmentAmount; } }

        /// <summary>
        /// 应收日期
        /// </summary>
        public DateTime CreditDate { get; set; }
    }
}
