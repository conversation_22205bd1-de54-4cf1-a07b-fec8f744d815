﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    public class FailureMsgInput
    {
        /// <summary>
        /// 能力中心appId
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// 订阅的主题
        /// </summary>
        public string? Topic { get; set; }

        /// <summary>
        /// 消息体
        /// </summary>
        public string? MsgBody { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string? FailReason { get; set; }

        /// <summary>
        /// 程序异常信息
        /// </summary>
        public string? ExceptionMessage { get; set; }
        /// <summary>
        /// 重试的回调方法路由 比如：/api/xxx/xxx，方法必须是post
        /// </summary>
        public string? CallBackMethodRoute { get; set; }
    }
}
