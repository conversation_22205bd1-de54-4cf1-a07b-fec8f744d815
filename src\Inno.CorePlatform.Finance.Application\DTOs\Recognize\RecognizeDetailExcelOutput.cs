﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    /// <summary>
    /// 认款明细导入出参
    /// </summary>
    public class RecognizeDetailExcelOutput
    {
        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 失败数
        /// </summary>
        public int FailNumber { get; set; }
        /// <summary>
        /// 失败错误信息文件id
        /// </summary>
        public Guid FailReportFileId { get; set; }
        /// <summary>
        /// 成功数
        /// </summary>
        public int SuccessNumber { get; set; }

    }
}
