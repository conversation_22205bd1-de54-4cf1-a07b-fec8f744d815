﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 账期起始日附件入参
    /// </summary>
    public class AccountPeriodDateAttachFileInput
    {
        public Guid DebtDetailAuditItemId { get; set; }

        public string? FileIds { get; set; }
        public string? FileId { get; set; }

    }
}
