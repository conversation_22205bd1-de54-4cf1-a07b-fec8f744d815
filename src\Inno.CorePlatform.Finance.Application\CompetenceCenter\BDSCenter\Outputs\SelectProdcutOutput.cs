﻿/**
* 命名空间：Inno.CorePlatform.StoreInApply.Application.ApplicationServices.ApplyBFFService.Outputs
* 
* 类 名：SelectProdcutOutput
* 
* 说 明：N/A
* 
* 作 者：陈忠阳（<EMAIL>）
*
* 时 间：2023/5/10 11:36:21
*/
using Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Outputs;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs
{
    /// <summary>
    /// 选货货品信息
    /// </summary>
    public class SelectProdcutOutput : BaseOutput
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public ProductNameOutput ProductName { get; set; }
        /// <summary>
        /// 货号是否做过首营
        /// </summary>
        public bool HaveFirstApprovelRecord { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardCost { get; set; }
        /// <summary>
        /// 库存数
        /// </summary>
        public int StoreQuantity { get; set; }
        /// <summary>
        /// 手术跟台数
        /// </summary>
        public int OperationQuantity { get; set; }
        /// <summary>
        /// 四级产品分类
        /// </summary>
        public string? FourthProductClassification { get; set; }
        /// <summary>
        /// 受托生产企业
        /// </summary>
        public string? MarkProducerName { get; set; }
        /// <summary>
        /// 新经营范围
        /// </summary>
        public string? NewRangeId { get; set; }
        /// <summary>
        /// 旧经营范围
        /// </summary>
        public string? OldRangeId { get; set; }
        /// <summary>
        /// 货品业务状态
        /// </summary>
        public ProductBusinessEnum ProductBusinessType { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string taxRate { get; set; }
        /// <summary>
        /// 显示货品业务状态
        /// </summary>
        public string DisplayProductBussiness
        {
            get
            {
                return this.ProductBusinessType.GetDescription();
            }
        }

    }
    public class ProductNameOutput : BaseOutput
    {
        public ProducerOutput Producer { get; set; }
    }
    /// <summary>
    /// 厂家
    /// </summary>
    public class ProducerOutput : BaseOutput
    {
        public ProducerOutput()
        {

        }
        public ProducerOutput(ConditionDataOutput? source)
        {
            if (source != null)
            {
                this.Id = source.value.FirstOrDefault();
                this.Name = source.names.FirstOrDefault();
            }

        }
    }
}
