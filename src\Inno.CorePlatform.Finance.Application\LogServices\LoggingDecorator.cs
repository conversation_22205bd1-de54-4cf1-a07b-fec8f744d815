﻿
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using System.Diagnostics;
using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Inno.CorePlatform.Finance.Application.LogServices
{

    public class LoggingDecorator<T> : DispatchProxy where T : class
    {
        private T _decorated = null!;
        private ISubLogService _logService = null!;
        private static readonly JsonSerializerOptions _jsonOptions = new()
        {
            WriteIndented = false,
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
        };

        protected override object? Invoke(MethodInfo? method, object?[]? args)
        {
            if (method == null || args == null) return null;

            try
            {
                // 1. 记录方法入参
                var parameters = method.GetParameters()
                    .Select((p, i) => new { p.Name, Value = args[i] })
                    .ToDictionary(p => p.Name!, p => p.Value);

                var className = typeof(T).Name;
                var jsonParams = JsonSerializer.Serialize(parameters, _jsonOptions);

                string fullMethodPath = $"{method.DeclaringType?.FullName}.{method.Name}";

                // 异步记录日志（fire-and-forget 模式）
                _ = _logService.LogAsync(fullMethodPath, jsonParams,"拦截器记录");

                // 2. 执行原始方法
                var result = method.Invoke(_decorated, args);

                // 3. 处理异步方法
                if (result is Task task)
                {
                    return HandleAsyncMethod(task);
                }

                return result;
            }
            catch (Exception ex)
            {
                // 可以在这里添加异常日志
                throw ex.InnerException ?? ex;
            }
        }

        private static async Task HandleAsyncMethod(Task task)
        {
            await task.ConfigureAwait(false);
        }

        public static T Create(T decorated, ISubLogService logService)
        {
            object proxy = Create<T, LoggingDecorator<T>>()!;
            var decorator = (LoggingDecorator<T>)proxy;
            decorator._decorated = decorated;
            decorator._logService = logService;
            return (T)proxy;
        }
    }
}
