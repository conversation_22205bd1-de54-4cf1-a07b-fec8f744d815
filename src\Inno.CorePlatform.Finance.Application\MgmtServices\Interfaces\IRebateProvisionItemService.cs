﻿using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 
    /// </summary>
    public interface IRebateProvisionItemService
    {

        Task<bool> AddRebateProvisionItems(List<RebateProvisionItemPo> items);
    }
}
