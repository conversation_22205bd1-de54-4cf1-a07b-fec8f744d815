﻿using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class PaymentAppService : IPaymentAppService
    {
        private readonly IPaymentRepository _paymentRepository;
        private readonly IUnitOfWorkFactory _uowFactory;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseAllQueryService<PaymentPo> _paymentQueryService;
        private readonly FinanceDbContext _db;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly DaprClient _daprClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly ILogger<PaymentAppService> _logger;
        private readonly IWeaverApiClient _weaverApiClient;

        public PaymentAppService(
                              IBaseAllQueryService<PaymentPo> paymentQueryService,
                              IPaymentRepository paymentRepository,
                              IUnitOfWorkFactory uowFactory,
                              IPurchaseApiClient purchaseApiClient,
                              IKingdeeApiClient kingdeeApiClient,
                              IExchangeRateService exchangeRateService,
                              DaprClient daprClient,
                              IBDSApiClient bDSApiClient,
                              IProjectMgntApiClient projectMgntApiClient,
                              FinanceDbContext db,
                              ILogger<PaymentAppService> logger,
                              IEasyCachingProvider easyCaching,
                              IWeaverApiClient weaverApiClient)
        {
            this._paymentQueryService = paymentQueryService;
            this._paymentRepository = paymentRepository;
            this._uowFactory = uowFactory;
            this._unitOfWork = _uowFactory.Create(paymentRepository);
            this._db = db;
            this._purchaseApiClient = purchaseApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._exchangeRateService = exchangeRateService;
            this._daprClient = daprClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._bDSApiClient = bDSApiClient;
            this._easyCaching = easyCaching;
            this._logger = logger;
            this._weaverApiClient = weaverApiClient;
        }
        public async Task<BaseResponseData<decimal>> ChangeByKingdeeAsync(List<KingdeePaymentInput> input)
        {
            var ret = BaseResponseData<decimal>.Success("操作成功！");
            var purchaseCode = input[0].PurchaseCode;//一笔采购单多比支付

            var payments = (await _paymentRepository.GetPaymentsBy(purchaseCode, new List<PaymentTypeEnum> { PaymentTypeEnum.Prepay, PaymentTypeEnum.Tariff, PaymentTypeEnum.ImportAddedTax })).
                           Where(p => string.IsNullOrEmpty(p.Code) && p.AdvancePayMode == AdvancePayModeEnum.NotUseQuota).ToList();


            if (payments != null && payments.Any())
            {
                var totalAmount = input.Sum(p => p.PayAmount);
                var payment = payments.Where(p => p.Value == totalAmount).FirstOrDefault();
                if (payment != null)
                {
                    if (totalAmount != payment.Value)
                    {
                        return BaseResponseData<decimal>.Failed(500, $"操作失败:实际付款金额与付款单金额不一致");
                    }
                    var addPayments = new List<Payment>();
                    foreach (var item in input)
                    {
                        var addPayment = new Payment();
                        addPayment = payment.Adapt<Payment>();
                        addPayment.Code = await generateCode(item.Code);
                        addPayment.OriginCode = item.Code;
                        addPayment.Id = Guid.NewGuid();
                        addPayment.Value = item.PayAmount;
                        addPayment.RMBAmount = item.PayAmount;
                        addPayment.PayClassify = item.PayClassify;
                        addPayment.PaymentDate = item.PaymentDate;
                        await InitRMBAmount(addPayment);
                        addPayments.Add(addPayment);
                    }

                    await _paymentRepository.DeleteAsync(payment.Id);
                    if ((await _paymentRepository.AddManyAsync(addPayments)) < 0)
                    {
                        ret = BaseResponseData<decimal>.Failed(500, "操作失败");
                    }
                    else
                    {
                        ret.Data = payment.Value;
                        ret.Message = payment.RelateId.HasValue ? payment.RelateId.ToString() : "";
                    }
                    await _unitOfWork.CommitAsync();
                    if (ret.Code == CodeStatusEnum.Success)
                    {
                        if (payment.Type == PaymentTypeEnum.Tariff || payment.Type == PaymentTypeEnum.ImportAddedTax)
                        {
                            var publishBodys = new List<CustomsOrderPaiedInput>
                            {
                               new CustomsOrderPaiedInput
                               {
                                    CustomsPaymentCode = payment.ProducerOrderNo,
                                    PaymentAmount = payment.Value,
                                    Tag = payment.Type==PaymentTypeEnum.ImportAddedTax ? "进口增值税" : "关税"
                               }
                            };
                            string jsonStr = JsonConvert.SerializeObject(publishBodys);
                            await _daprClient.PublishEventAsync<List<CustomsOrderPaiedInput>>("pubsub-default", "sia-customs-order-paied", publishBodys);
                        }
                        else
                        {
                            await _daprClient.PublishEventAsync<PurchaseOrder>("pubsub-default", "fam-purchase-payfinished", new PurchaseOrder(input[0].PurchaseCode, 0, Guid.Parse(ret.Message)));
                        }
                    }
                }
                else
                {
                    ret = BaseResponseData<decimal>.Failed(500, $"操作失败:采购单:{purchaseCode},没有找到未付的付款单");
                }
            }
            return ret;

            async Task<string> generateCode(string originCode)
            {
                string codeCachekey = $"paymentCode:{originCode}";
                var num = await _easyCaching.GetAsync<int>(codeCachekey);
                if (!num.HasValue)
                {
                    await _easyCaching.SetAsync(codeCachekey, 2, TimeSpan.FromHours(1));
                    return $"{originCode}-001";
                }

                await _easyCaching.SetAsync(codeCachekey, num.Value + 1, TimeSpan.FromHours(1));
                return $"{originCode}-{num.ToString().PadLeft(3, '0')}";
            }
        }

        private async Task InitRMBAmount(Payment payment)
        {
            if (payment.CoinCode != null && payment.CoinCode != "CNY")
            {
                var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                {
                    Effectdate = payment.BillDate,
                    OrgcurName = payment.CoinName
                });
                if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                {
                    throw new Exception("操作失败：未获取到汇率");
                }
                payment.RMBAmount = payment.Value * exchange.Data.Excval;
            }
            else
            {
                payment.RMBAmount = payment.Value;
            }
        }
        public async Task<Payment> GetById(Guid id)
        {
            return (await _paymentQueryService.GetAllListAsync(p => p.Id == id)).First().Adapt<Payment>();
        }

        /// <summary>
        /// 收款(退款)与预付单冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> AbatementPayment(KingdeeAbatementPaymentInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var payment = await _db.Payments.Where(p => p.Code.Contains(input.Code) &&
                                                        string.IsNullOrEmpty(p.PurchaseCode) &&
                                                        p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                        ).FirstOrDefaultAsync();
            if (payment == null)
                return BaseResponseData<int>.Failed(500, "操作失败，原因：未找到付款单或该采购单还未强制结束！");
            var debtDetails = await _db.DebtDetailExcutes.Where(p => p.PaymentCode == payment.Code).ToListAsync();
            var debtExcuteAmount = debtDetails.Sum(p => p.Value);
            if (input.Amount > payment.Value - debtExcuteAmount)
                return BaseResponseData<int>.Failed(500, "操作失败，原因：付款单余额不够！");
            var abatementPO = new AbatementPo
            {
                Value = input.Amount,
                Abtdate = DateTime.Now,
                CreatedBy = input.userName,
                CreatedTime = DateTime.Now,
                CreditBillCode = input.AbatementCode,
                CreditType = "return",//退款
                DebtBillCode = payment.Code,
                DebtType = "payment",
                Id = Guid.NewGuid(),
            };
            _db.Abatements.Add(abatementPO);
            await _db.SaveChangesAsync();
            return ret;
        }

        /// <summary>
        /// 退款与应付单冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ReturnAbatementDebt(KingdeeAbatementDebtInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var debt = await _db.Debts.Where(p => p.BillCode.Contains(input.Code) &&
                                                        p.AbatedStatus == AbatedStatusEnum.NonAbate
                                                        ).FirstOrDefaultAsync();
            if (debt == null)
                return BaseResponseData<int>.Failed(500, "操作失败，原因：未找到应付单！");

            if (Math.Abs(input.Amount) > Math.Abs(debt.Value))
                return BaseResponseData<int>.Failed(500, "操作失败，原因：应付单余额不够！");

            debt.AbatedStatus = AbatedStatusEnum.Abated;
            var abatementPO = new AbatementPo
            {
                Value = Math.Abs(input.Amount),
                Abtdate = DateTime.Now,
                CreatedBy = input.userName,
                CreatedTime = DateTime.Now,
                CreditBillCode = input.AbatementCode,
                CreditType = "debt",//退款
                DebtBillCode = debt.BillCode,
                DebtType = "debt",
                Id = Guid.NewGuid(),
            };
            _db.Abatements.Add(abatementPO);
            await _db.SaveChangesAsync();
            if (debt.Value < 0)
            {
                var debtAbtPurchasePubs = new List<DebtAbtPurchasePubInput>();
                debtAbtPurchasePubs.Add(new DebtAbtPurchasePubInput
                {
                    MinusDebtCode = debt.BillCode,
                    AbatementCode = input.AbatementCode,
                    AbatementAmount = Math.Abs(input.Amount)
                });
                _logger.LogWarning($"拆分的服务费采购：" + debtAbtPurchasePubs.ToJson());
                await _daprClient.PublishEventAsync<List<DebtAbtPurchasePubInput>>("pubsub-default", "finance-purchase-minusdebtabtpurchase", debtAbtPurchasePubs);

            }
            return ret;
        }

        /// <summary>
        /// 信用证支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> CreditPay(KindeeCreditPayInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var purchaseOrder = await _purchaseApiClient.GetSimpleByCode(input.PurchaseCode.Trim());

            if (purchaseOrder == null)
            {
                throw new Exception($"操作失败：采购单号：{input.PurchaseCode}，没有查到采购单");
            }
            var payment = new PaymentPo
            {
                Id = Guid.NewGuid(),
                AbatedStatus = AbatedStatusEnum.Abated,
                AdvancePayMode = AdvancePayModeEnum.NotUseQuota,
                AgentId = purchaseOrder.Agent.Id,
                AgentName = purchaseOrder.Agent.Name,
                BillDate = DateTime.Now,
                BusinessDeptFullName = purchaseOrder.BusinessDeptFullName,
                BusinessDeptFullPath = purchaseOrder.BusinessDeptFullPath,
                BusinessDeptId = purchaseOrder.BusinessDeptId,
                Code = input.Code,
                CoinCode = input.CoinCode,
                CoinName = input.CoinName,
                CompanyId = purchaseOrder.Consignee?.Id,
                CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                CreatedBy = purchaseOrder.CreatedBy,
                CreatedTime = DateTime.Now,
                CreditAmount = 0,
                PayClassify = input.PayClassify,
                NearOrderUseValue = 0,
                PaymentDate = input.PaymentDate,
                ProducerOrderNo = purchaseOrder.ProducerOrderNo,
                ProjectId = purchaseOrder?.Project == null ? Guid.Empty : Guid.Parse(purchaseOrder.Project.Id),
                ProjectCode = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Code,
                ProjectName = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Name,
                PurchaseCode = input.PurchaseCode.Trim(),
                RMBAmount = input.RMBAmount,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                Type = input.Type,
                Value = input.Value,
            };
            await _db.Payments.AddAsync(payment);

            var debts = await _db.Debts.Include(p => p.DebtDetails).ThenInclude(p => p.DebtDetailExcutes).Where(p => p.PurchaseCode == input.PurchaseCode && p.AbatedStatus != AbatedStatusEnum.NonAbate).ToListAsync();
            if (debts != null && debts.Any())
            {
                foreach (var debt in debts)
                {
                    //冲销
                    var abatement = new AbatementPo
                    {
                        Id = Guid.NewGuid(),
                        Abtdate = DateTime.Now,
                        CreatedBy = "none",
                        DebtType = "payment",
                        DebtBillCode = payment.Code,
                        CreditType = "debt",
                        CreditBillCode = debt.BillCode,
                        Value = debt.Value,
                    };
                    _db.Abatements.Add(abatement);
                    foreach (var subitem in debt.DebtDetails)
                    {
                        if (subitem.DebtDetailExcutes != null && subitem.DebtDetailExcutes.Any())
                        {
                            await PushPaymentSettlement_KD(debt, subitem.DebtDetailExcutes);
                        }
                    }

                }
            }
            await _db.SaveChangesAsync();
            return ret;
        }

        private async Task PushPaymentSettlement_KD(DebtPo debt, List<DebtDetailExcutePo> debtDetails)
        {
            var inputs = new List<PaymentSettlementInput>();
            foreach (var item in debtDetails)
            {
                var tempInput = new PaymentSettlementInput();
                if (debt.Value < 0)
                {
                    tempInput.mianbillno = item.PaymentCode;
                    tempInput.mianSettleAmt = debt.Value;
                    tempInput.asstbillno = debt.BillCode;
                    tempInput.asstSettleAmt = debt.Value;
                }
                else
                {
                    tempInput.mianbillno = debt.BillCode;
                    tempInput.mianSettleAmt = item.Value;
                    tempInput.asstbillno = item.PaymentCode;
                    tempInput.asstSettleAmt = item.Value;
                }
                if (item.Value < 0)
                {
                    tempInput.asstSettleAmt = tempInput.asstSettleAmt * -1;
                }
                inputs.Add(tempInput);
            }
            var ret = await _kingdeeApiClient.PaymentSettlement(inputs);
            if (ret.Code != CodeStatusEnum.Success)
            {
                throw new AppServiceException(ret.Message);
            }

        }

        /// <summary>
        /// 创建游离的付款单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> CreateNotProducerCodePayment(PaymentInputOfKd input)
        {
            var ret = BaseResponseData<string>.Success("操作成功！");
            var payment = input.Adapt<Payment>();
            var isExists = await _db.Payments.Where(p => p.Code == input.Code).FirstOrDefaultAsync();
            if (isExists != null)
            {
                ret.Message = "操作失败，付款单号已存在";
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }

            payment.AbatedStatus = AbatedStatusEnum.NonAbate;
            payment.Id = Guid.NewGuid();
            payment.Type = PaymentTypeEnum.Prepay;
            DateTime paymentDate = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            paymentDate = paymentDate.AddMilliseconds(input.PaymentDateTimestamp).ToUniversalTime().AddHours(8);
            payment.PaymentDate = paymentDate;
            var agent = await _bDSApiClient.GetAgentInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                id = input.AgentId.ToString()
            });
            if (agent == null || agent.Count <= 0)
            {
                ret.Message = "操作失败，供应Id在基础数据中不存在";
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
            payment.AgentName = agent.First().agentName;
            payment.BillDate = DateTime.Now;
            payment.OriginCode = payment.Code;
            if (!string.IsNullOrEmpty(payment.Code))
            {
                var nos = payment.Code.Split("-");
                if (payment.Code.Contains("PV-"))
                {
                    payment.OriginCode = nos[0] + "-" + nos[1] + "-" + nos[2];
                }
                else if (payment.Code.Contains("PA-"))
                {
                    payment.OriginCode = nos[0] + "-" + nos[1] + "-" + nos[2] + "-" + nos[3];
                }
            }

            var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput
            {
                nameCodeEq = input.CompanyNameCode
            });
            if (company == null || company.Count <= 0)
            {
                ret.Message = "操作失败，公司Id在基础数据中不存在";
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
            payment.CompanyId = Guid.Parse(company.First().companyId);
            payment.NameCode = input.CompanyNameCode;
            payment.CompanyName = company.First().companyName;
            payment.AdvancePayMode = AdvancePayModeEnum.NotUseQuota;

            var project = await _projectMgntApiClient.GetProjectInfoByProjectNo(input.ProjectCode);
            if (project == null || project.Count <= 0)
            {
                ret.Message = "操作失败，项目Code在数据中不存在";
                ret.Code = CodeStatusEnum.Failed;
                return ret;
            }
            payment.ProjectId = project.First().Id;
            payment.ProjectName = project.First().Name;
            await _paymentRepository.AddAsync(payment);
            await _unitOfWork.CommitAsync();

            return ret;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> FundDelete(FundDeleteInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            using var transaction = await _db.Database.BeginTransactionAsync();
            try
            {
                switch (input.Type)
                {
                    case 1:
                        var _payment = await _db.Payments.AsNoTracking().FirstOrDefaultAsync(w => w.PurchaseCode == input.Code && string.IsNullOrEmpty(w.Code));
                        if (_payment == null)
                        {
                            throw new AppServiceException("付款单不存在");
                        }
                        if (_payment.RelateId != null)
                        {
                            //游离付款单
                            var _freePayments = await _db.Payments.AsNoTracking().AnyAsync(w => w.RelateId == _payment.RelateId && w.PurchaseCode == _payment.PurchaseCode && w.Type == PaymentTypeEnum.Prepay && w.AbatedStatus == AbatedStatusEnum.NonAbate && !string.IsNullOrEmpty(w.Code));
                            if (_freePayments)
                            {
                                throw new AppServiceException("采购单使用了游离付款单不允许删除");
                            }

                            //负数应付、返利单
                            var _useDebts = await _db.DebtPaymentUseDetails.AsNoTracking().AnyAsync(p =>
                            p.UseCode == _payment.PurchaseCode && p.RelateId == _payment.RelateId);
                            if (_useDebts)
                            {
                                throw new AppServiceException("采购单使用了负数应付/返利单不允许删除");
                            }

                            var deleteResult = await _purchaseApiClient.DeleteAdvancePayById(new DeleteAdvancePayInput() { Id = _payment.RelateId.Value });
                            if (deleteResult.Code != CodeStatusEnum.Success)
                            {
                                throw new AppServiceException(deleteResult.Message);
                            }
                        }
                        _db.Payments.Remove(_payment);
                        break;
                    case 2:
                        var _paymentAuto = await _db.PaymentAutoItems.AsNoTracking().FirstOrDefaultAsync(w => w.Code == input.Code && w.Status != PaymentAutoItemStatusEnum.Completed);
                        if (_paymentAuto == null)
                        {
                            throw new AppServiceException("付款单不存在");
                        }
                        if (!int.TryParse(_paymentAuto.OARequestId, out int oARquestId))
                        {
                            throw new AppServiceException("付款单OARequestID不存在");
                        }

                        // 是否执行节点前附加操作，不传的话OA默认为true，会回调我们接口，导致重复解锁而报错，这里要传false
                        var interResult = await _weaverApiClient.Intervene(new Gateway.Client.Common.WeaverOA.InterveneInput()
                        {
                            requestId = oARquestId,
                            targetNodeType = 0,
                            otherParams = new Gateway.Client.Common.WeaverOA.OtherParams()
                            {
                                execPreNodeAction = false
                            }
                        });
                        if (interResult.Status && interResult.Code == 200)
                        {
                            var delResult = await _weaverApiClient.DelWorkFlow(_paymentAuto.CreatedBy, oARquestId);
                            if (delResult.Status && delResult.Code == 200)
                            {
                                await _db.PaymentAutoDetails.Where(x => x.PaymentAutoItemId == _paymentAuto.Id).ExecuteDeleteAsync();
                                await _db.PaymentAutoAgentBankInfos.Where(x => x.PaymentAutoItemId == _paymentAuto.Id).ExecuteDeleteAsync();
                                _db.PaymentAutoItems.Remove(_paymentAuto);
                            }
                        }
                        break;
                    case 3:
                        var _refund = await _db.RefundItem.AsNoTracking().FirstOrDefaultAsync(w => w.BillCode == input.Code);
                        if (_refund == null)
                        {
                            throw new AppServiceException("退款单不存在");
                        }
                        await _db.RefundDetails.Where(x => x.RefundItemId == _refund.Id).ExecuteDeleteAsync();
                        _db.RefundItem.Remove(_refund);
                        break;
                    default:
                        throw new AppServiceException("不支持的操作类型");
                }

                await _db.SaveChangesAsync();
                await transaction.CommitAsync();
                return ret;
            }
            catch (Exception ex)
            {
                _logger.LogError($"付款申请删除异常:{input.Code}-{ex.Message}");
                await transaction.RollbackAsync();
                throw;
            }
        }
    }
}
