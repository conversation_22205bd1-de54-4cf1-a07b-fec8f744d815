﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IBatchPaymentAppService
    {
        Task<int> CreateAsync(PaymentAutoItemInput input);

        Task<int> UpdateAsync(PaymentAutoItemInput input);

        /// <summary>
        /// 提交或撤回批量付款单（作废）
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [Obsolete]
        Task<int> SubmitOrCancelAsync(Guid id, string userName, int type = 0);
        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<bool> SubmitAsync(Guid id, string userName);
        Task RejectAsync(Guid id);
        Task LeaderAuditAsync(Guid id, string auditor);
        Task CompleteAsync(Guid id);

        Task<int> DeleteAsync(Guid id);

        Task<int> AddDetailAsync(List<PaymentAutoDetailInput> lstDetail, string userName);

        Task<int> DeleteDetailAsync(PaymentAutoDetailDeleteInput input);

        Task<int> ReAddDetailAsync(List<PaymentAutoDetailInput> lstDetail, string userName);

        /// <summary>
        /// 批量付款单执行
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<bool> ExcutePaymentAutoItemAsync(Guid id);
        /// <summary>
        /// 获得批量付款列表
        /// </summary>
        /// <returns></returns>
         //Task<Task> GetList();
        /// <summary>
        /// 获得明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        //Task<bool> GetDetails(Guid id);

        Task<BaseResponseData<int>> CreatePaymentAutoBankInfos(List<AgentBankInput> agentBanks);

        /// <summary>
        /// 修改备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UpdateRemark(UpdateRemarkInput input);

        /// <summary>
        /// 修改附言
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UpdateTransferDiscourse(UpdateTransferDiscourseInput input);
        Task<BaseResponseData<int>> SetAgentBank(AgentBankInput input);

        /// <summary>
        /// 导入付款明细
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="paymentAutoItemId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<PaymentExcelDetailOutput>> ExportBatchPaymentDetail(Guid fileId, Guid paymentAutoItemId, string userName);

        /// <summary>
        /// 导出账期数据（协调服务导出）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportAccountPeriodTask([FromBody] DebtDetailBulkQuery input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SetAgentPayClassifyAsync(List<AgentBankInput> input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SetAgentTransferDiscourseAsync(List<AgentBankInput> input);

        /// <summary>
        /// 设置现金折扣金额
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> SetDiscountAmountAsync(SetDiscountAmountInput input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> AttachFileIds(AddCashDiscountFileInput input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<PaymentAutoAgentBankInfo> GetById(Guid id);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> DeleteAgentAttachFileIds(AddCashDiscountFileInput input);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> SetBulkDiscountAmountAsync(List<PaymentDetailOutput> input);

        /// <summary>
        /// OA撤回
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool>> WithdrawAsync(Guid id, string userName);
    }
}
