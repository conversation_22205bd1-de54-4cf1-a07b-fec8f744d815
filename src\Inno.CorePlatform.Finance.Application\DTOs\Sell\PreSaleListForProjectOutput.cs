﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{

    public class PreSaleListForProjectOutput
    {
        /// <summary>
        /// 预订单ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 预订单编号
        /// </summary>
        public string BillCode { get; set; }


        /// <summary>
        /// 预订单编号
        /// </summary>
        public DateTimeOffset BillDate { get; set; }
         

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CompanyName { get; set; }


        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid CustomerId { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 销售单列表
        /// </summary>
        public List<SaleInfo> SaleList { get; set; } = new();

        /// <summary>
        /// 项目ID
        /// </summary>
        public Guid ProjectId { get; set; }


 
    }
    /// <summary>
    /// 销售单信息
    /// </summary>
    public class SaleInfo
    {
        /// <summary>
        /// 销售单ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 单据时间
        /// </summary>
        public DateTimeOffset BillDate { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public SaleStatusEnum Status { get; set; }


        /// <summary>
        /// 订单状态名称
        /// </summary>
        public string StatusName
        {
            get
            {
                return Status.GetDescription();
            }
        }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CompanyName { get; set; }


        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid CustomerId { get; set; }


        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }



        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        public string SaleTypeName
        {
            get
            {
                return SaleType.GetDescription();
            }
        }


        /// <summary>
        /// 原单号
        /// </summary>
        public string? OriginalSaleCode { get; set; }


        /// <summary>
        /// 创建人
        /// </summary>

        public string CreatedBy { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

    }
    public class QueryPreSaleListForProjectInput
    {
        /// <summary>
        /// 项目ID
        /// </summary>

        public List<Guid>? ProjectIds { get; set; }
    }
}
