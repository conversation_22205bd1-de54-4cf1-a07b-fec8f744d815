﻿using Dapr.Client;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.SendNotification;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    /// <summary>
    /// 发送消息通知客户端
    /// </summary>
    public class SendNotificationClient : BaseDaprApiClient<SendNotificationClient>, ISendNotificationClient
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public SendNotificationClient(DaprClient daprClient, ILogger<SendNotificationClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取AppId
        /// </summary>
        /// <returns></returns>
        protected override string GetAppId()
        {
            return AppCenter.BINDING_SENDNOTIFICATION;
        }

        /// <summary>
        /// 发送消息通知
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task SendNotification(MessageNotificationInput input)
        {
            await InvokeBindingAsync("create", input);
        }
    }
}
