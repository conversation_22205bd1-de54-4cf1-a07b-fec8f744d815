﻿using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class AdvanceBusinessRepository : IAdvanceBusinessRepository
    {
        public FinanceDbContext _dbContext;
        public AdvanceBusinessRepository(FinanceDbContext dbContext)
        {
            _dbContext = dbContext;
        }


        public bool UowJoined { get; set; }

        public async Task<bool> AddAdvanceDetails(List<AdvanceBusinessDetail> details)
        {
            var pos = details.Adapt<List<AdvanceBusinessDetailPO>>();
            await _dbContext.AdvanceBusinessDetail.AddRangeAsync(pos);
            if (UowJoined)
            {
                return false;
            }
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public Task<int> AddAsync(AdvanceBusinessApply root)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> DeleteAdvanceDetails(List<Guid> ids)
        {
            var pos = await _dbContext.AdvanceBusinessDetail.Where(p => ids.Contains(p.Id)).ToListAsync();
            if (pos.Count == 0)
            {
                return false;
            }
            _dbContext.AdvanceBusinessDetail.RemoveRange(pos);
            if (UowJoined)
            {
                return false;
            }
            await _dbContext.SaveChangesAsync();
            return true;
        }

        public Task<int> DeleteAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public Task<AdvanceBusinessApply?> GetAsync(Guid id)
        {
            throw new NotImplementedException();
        }
        public async Task<int> UpdateAsync(AdvanceBusinessApply root)
        {
            var po = root.Adapt<AdvanceBusinessApplyPO>();
            _dbContext.AdvanceBusinessApply.Update(po);
            if (UowJoined) return 0;
            return await _dbContext.SaveChangesAsync();
        }
        public async Task<int> AddManyAsync(List<AdvanceBusinessApply> list)
        {
            var lstPo = list.Adapt<List<AdvanceBusinessApplyPO>>();

            _dbContext.AdvanceBusinessApply.AddRange(lstPo);

            if (UowJoined) return 0;

            return await _dbContext.SaveChangesAsync();

        }
        public async Task<int> UpdateManyAsync(List<AdvanceBusinessApply> list)
        {
            //var lstPo = new List<AdvanceBusinessApplyPO>();
            //list.ForEach(t =>
            // {
            //     var po = t.Adapt<AdvanceBusinessApplyPO>();
            //     _dbContext.Entry(po).State = EntityState.Detached;
            //     lstPo.Add(po);
            // });
            var lstPo = list.Adapt<List<AdvanceBusinessApplyPO>>();
            _dbContext.AdvanceBusinessApply.UpdateRange(lstPo);
            if (UowJoined) return 0;
            return await _dbContext.SaveChangesAsync();
        }
        public async Task<List<AdvanceBusinessApply>> GetByCodes(List<string?> codes)
        {
            var list = await _dbContext.AdvanceBusinessApply.Where(t => codes.Contains(t.Code)).Select(t => t.Adapt<AdvanceBusinessApply>()).AsNoTracking().ToListAsync();
            return list;
        }
    }
}
