﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 特殊出库的单的财务处理，如报损，盘亏
    /// </summary>
    public interface ISpecialStoreOutAppService: IInventoryAppService
    {
        Task<BaseResponseData<int>> ExchangeLossConversion(EventBusDTO dto);

    }
}
