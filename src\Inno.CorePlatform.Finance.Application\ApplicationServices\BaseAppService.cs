﻿using Dapr.Client;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;



namespace Inno.CorePlatform.Finance.Application.ApplicationServices
{
    public abstract class SimpleBaseAppService<T>
    {
        /// <summary>
        /// 线上实时查询日志基类
        /// </summary>
        protected readonly ILogger<T> _logger;

        /// <summary>
        /// 基类边车服务
        /// </summary>
        protected readonly DaprClient _daprClient;


        public SimpleBaseAppService(
            DaprClient daprClient,
            ILogger<T> logger)
        {

            _logger = logger;
            _daprClient = daprClient;

        }

        /// <summary>
        /// 发送广播公共方法
        /// </summary>
        /// <typeparam name="TopicContent"></typeparam>
        /// <param name="topic"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        protected async Task SendBoardCast<TopicContent>(string topic, TopicContent content)
        {
            try
            {
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, topic, content);
            }
            catch (Exception)
            {
                throw new ApplicationException($"发送广播报错，入参{content}");
            }
        }
    }

    /// <summary>
    /// 基础Application Service 类，用于定义公用方法，及获取公用信息。
    /// 注：此基类依赖IHttpContextAccessor服务的注入
    /// </summary>
    public class BaseAppService<T>: SimpleBaseAppService<T>
    {
        /// <summary>
        /// 单号生成基础服务
        /// </summary>
        protected readonly ICodeGenClient _codeClient;

        /// <summary>
        /// 上下文获取器
        /// </summary>
        protected readonly IHttpContextAccessor _httpContextAccessor;

        /// <summary>
        /// 外部能力中心访问服务
        /// </summary>
        protected readonly IApplyBFFService _applyBFFService;

        /// <summary>
        /// 基类注入财务数据库上下文
        /// </summary>
        protected readonly FinanceDbContext _db;


        public BaseAppService(
            FinanceDbContext db,
            DaprClient daprClient,
            ICodeGenClient codeClient,
            ILogger<T> logger,
            IHttpContextAccessor httpContextAccessor,
            IApplyBFFService applyBFFService):base(daprClient, logger)
        {
            _db = db;
            _httpContextAccessor = httpContextAccessor;
            _codeClient = codeClient;
            _applyBFFService = applyBFFService;
        }


        /// <summary>
        /// 当前登录用户
        /// </summary>
        protected CurrentUserOutput CurrentUser
        {
            get
            {
                CurrentUserOutput userinfo = new CurrentUserOutput();

                if (_httpContextAccessor.HttpContext.User.Identity.IsAuthenticated)
                {
                    Guid.TryParse(_httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(t => t.Type == "sub")?.Value, out Guid userId);
                    userinfo.Id = userId;
                    userinfo.UserName = _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(t => t.Type == "upn")?.Value;
                    userinfo.Name = _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(t => t.Type == "name")?.Value;
                }
                return userinfo;
            }
        }

        /// <summary>
        /// 生成单号公共方法
        /// </summary>
        /// <param name="companyId">公司Id，必传，如果公司详情也传了，以公司详情对象为准</param>
        /// <param name="companyInfo">公司信息(可以不传)</param>
        /// <param name="deptShortName">核算部门简码</param>
        /// <param name="billType">单据简码</param>
        /// <param name="billDate">指定单据日期</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        protected async Task<(string, DateTime)> CreateBillCode(string companyId, CompanyInfoOutput? companyInfo = null, string deptShortName = "", string billType = "RTL", string? billDate = "")
        {
            if (string.IsNullOrWhiteSpace(deptShortName))
            {
                if (!CurrentUser.Id.HasValue)
                {
                    return (Guid.NewGuid().ToString(), DateTime.Now);
                }
            }
            if (companyInfo == null)
            {
                companyInfo = (await _applyBFFService.GetCompanyInfosAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { companyId } })).FirstOrDefault();
            }
            ApplyCodeInput input = new ApplyCodeInput()
            {
                BusinessArea = deptShortName,
                BillType = billType,
                DelayDays = companyInfo.DelayDays.HasValue ? companyInfo.DelayDays.Value : 6,
                Num = 1,
                CompanyCode = companyInfo.NameCode
            };
            string sysmonthResult = "";
            if (!string.IsNullOrEmpty(billDate))
            {
                sysmonthResult = billDate;
                input.ForceUseInputSysMonth = true;
            }
            else
            {
                sysmonthResult = await _applyBFFService.GetSysmonth(Guid.Parse(companyInfo.Id));

            }
            input.SysMonth = sysmonthResult;
            //生成单号

            var output = await _codeClient.ApplyCode(input);
            if (output.Status)
            {
                DateTime.TryParse(sysmonthResult, out DateTime timeValue);
                return (output.Codes.First(), timeValue);
            }
            else
            {
                throw new ApplicationException($"生成Code失败，{output.Msg}");
            }
        }

    }
}
