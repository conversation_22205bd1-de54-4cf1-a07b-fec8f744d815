﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class InputBillQueryInput: BaseQuery
    {
        /// <summary>
        /// 开票时间begin
        /// </summary>
        public string? beginCreatedTime { get; set; }

        /// <summary>
        /// 开票时间end
        /// </summary>
        public string? endCreatedTime { get; set; }

        /// <summary>
        /// 票据类型 1,普票 2，专票
        /// </summary>
        public int? Type { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNumber { get; set; }


        /// <summary>
        /// 公司名称
        /// </summary>
        public Guid? CompanyId { get; set; }


        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 供应商ids
        /// </summary>
        public List<Guid?>? AgentIds { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInItemCode {  get; set; }
        /// <summary>
        /// 是否已有发票明细 0全部 1是 2否
        /// </summary>
        public int HasDetail { get; set; }
        /// <summary>
        /// 应付类型
        /// </summary>
        public int? DebtType {  get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtCode {  get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 取消勾稽状态名称 (true=是, false=否)
        /// </summary>
        public bool? cancelReconciliationStatusName { get; set; }

        /// <summary>
        /// 取消勾稽时间开始
        /// </summary>
        public string? beginCancelReconciliationTime { get; set; }

        /// <summary>
        /// 取消勾稽时间结束
        /// </summary>
        public string? endCancelReconciliationTime { get; set; }
    }
}
