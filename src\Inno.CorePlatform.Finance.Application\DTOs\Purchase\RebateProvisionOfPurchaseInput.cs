﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class RebateProvisionOfPurchaseInput
    {
        public Guid CompanyId { get; set; }
    }

    public class RebateProvisionOfPurchaseOutput
    {
        /// <summary>
        /// 确认函日期
        /// </summary>
        public DateTime? ConfirmationDate { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? ConfirmTaxAmount { get; set; }

        /// <summary>
        /// 实际收到日期
        /// </summary>
        public DateTime? ActualarrivalDate { get; set; }

        /// <summary>
        /// 政策期限
        /// </summary>
        public string? Policydeadline { get; set; }

        /// <summary>
        /// 返利类型 A:平移返利, B:指标返利, C:补偿返利	
        /// </summary>
        public string? RebateType { get; set; }

        /// <summary>
        /// 发票日期/优惠卷日期
        /// </summary> 
        public DateTime? CouponDate { get; set; }

        /// <summary>
        /// 返利金额
        /// </summary>
        public decimal? RebateAmount { get; set; }

        /// <summary>
        /// 返利不含税金额
        /// </summary>
        public decimal? RebateTaxAmount { get; set; }

        /// <summary>
        /// 厂家红票发票号
        /// </summary>
        public string? Redinvoice { get; set; }

        /// <summary>
        /// 返利期间(摘要)
        /// </summary>
        public string? PeriodSummary { get; set; }


        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        public Guid ProjectId { get; set; }

        public string ProjectName { get; set; } = "";

        public string ProjectCode { get; set; } = "";

        /// <summary>
        /// 供应商Id
        /// </summary>   
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
    }
}
