using EasyCaching.Core;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Services;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Optimized
{
    /// <summary>
    /// 金蝶API客户端基类 - 提供通用功能
    /// </summary>
    public abstract class KingdeeBaseClient
    {
        protected readonly HttpClient _httpClient;
        protected readonly IEasyCachingProvider _easyCaching;
        protected readonly IConfiguration _configuration;
        protected readonly ISubLogService _logger;
        protected readonly IOptionsSnapshot<KingdeeSetting> _kingdeeSetting;
        protected readonly IDaprEventPublisher _daprEventPublisher;

        protected KingdeeBaseClient(
            HttpClient httpClient,
            IEasyCachingProvider easyCaching,
            IConfiguration configuration,
            ISubLogService logger,
            IOptionsSnapshot<KingdeeSetting> kingdeeSetting,
            IDaprEventPublisher daprEventPublisher)
        {
            _httpClient = httpClient;
            _easyCaching = easyCaching;
            _configuration = configuration;
            _logger = logger;
            _kingdeeSetting = kingdeeSetting;
            _daprEventPublisher = daprEventPublisher;
        }

        /// <summary>
        /// 获取访问令牌
        /// </summary>
        protected async Task<string> GetAccessTokenAsync()
        {
            var token = await _easyCaching.GetAsync<string>("kingdeeaccesstoken");
            if (token != null && !string.IsNullOrEmpty(token.Value))
            {
                return token.Value;
            }

            var appToken = await GetAppTokenAsync();
            return await LoginAsync(appToken);
        }

        /// <summary>
        /// 通用HTTP请求方法
        /// </summary>
        protected async Task<KingdeeApiResult<T>> ExecuteRequestAsync<T>(
            string endpoint,
            object requestData,
            string operationName,
            HttpMethod? method = null,
            bool logRequest = true)
        {
            method ??= HttpMethod.Post;

            try
            {
                var accessToken = await GetAccessTokenAsync();
                using var restClient = new RestClient(_httpClient);

                var request = CreateRequest(endpoint, accessToken, requestData, method);
                var requestBody = JsonConvert.SerializeObject(requestData);

                if (logRequest)
                {
                    await CreateSubLogAsync(SubLogSourceEnum.PushKingdee, requestBody, $"{operationName}-推送金蝶");
                }

                var responseContent = await ExecuteHttpRequestAsync(restClient, request, operationName);

                return ProcessKingdeeResponse<T>(responseContent, operationName);
            }
            catch (Exception ex)
            {
                _logger.LogAzure($"{operationName} - 调用金蝶接口异常", ex.Message.ToString(), operationName, Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult<T>.Failure($"【金蝶】{operationName}出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 无返回数据的HTTP请求方法
        /// </summary>
        protected async Task<KingdeeApiResult> ExecuteRequestAsync(
            string endpoint,
            object requestData,
            string operationName,
            HttpMethod? method = null,
            bool logRequest = true)
        {
            var result = await ExecuteRequestAsync<object>(endpoint, requestData, operationName, method, logRequest);

            if (result.IsSuccess)
            {
                return KingdeeApiResult.Success(result.RawResponse);
            }

            return KingdeeApiResult.Failure(result.ErrorMessage!, result.ErrorCode, result.IsBusinessError, result.RawResponse);
        }

        /// <summary>
        /// 带自定义幂等性Key的HTTP请求方法
        /// </summary>
        protected async Task<KingdeeApiResult> ExecuteRequestWithCustomIdempotencyAsync(
            string endpoint,
            object requestData,
            string operationName,
            string customIdempotencyKey,
            HttpMethod? method = null,
            bool logRequest = true)
        {
            method ??= HttpMethod.Post;

            try
            {
                var accessToken = await GetAccessTokenAsync();
                using var restClient = new RestClient(_httpClient);

                var request = CreateRequestWithCustomIdempotency(endpoint, accessToken, requestData, customIdempotencyKey, method);
                var requestBody = JsonConvert.SerializeObject(requestData);

                if (logRequest)
                {
                    await CreateSubLogAsync(SubLogSourceEnum.PushKingdee, requestBody, $"{operationName}-推送金蝶");
                }

                var responseContent = await ExecuteHttpRequestAsync(restClient, request, operationName);

                var result = ProcessKingdeeResponse<object>(responseContent, operationName);

                if (result.IsSuccess)
                {
                    return KingdeeApiResult.Success(result.RawResponse);
                }

                return KingdeeApiResult.Failure(result.ErrorMessage!, result.ErrorCode, result.IsBusinessError, result.RawResponse);
            }
            catch (Exception ex)
            {
                _logger.LogAzure($"{operationName} - 调用金蝶接口异常", ex.Message.ToString(), operationName, Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult.Failure($"【金蝶】{operationName}出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建HTTP请求
        /// </summary>
        private RestRequest CreateRequest(string endpoint, string accessToken, object requestData, HttpMethod method)
        {
            var request = new RestRequest($"{_kingdeeSetting.Value.Host}/{endpoint}");
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("accesstoken", accessToken);
            request.AddHeader("Idempotency-Key", Guid.NewGuid().ToString());

            if (method == HttpMethod.Post && requestData != null)
            {
                var para = requestData.GetType().GetProperty("data") != null ? requestData : new { data = requestData };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
            }

            return request;
        }

        /// <summary>
        /// 创建带自定义幂等性Key的HTTP请求
        /// </summary>
        private RestRequest CreateRequestWithCustomIdempotency(string endpoint, string accessToken, object requestData, string customIdempotencyKey, HttpMethod method)
        {
            var request = new RestRequest($"{_kingdeeSetting.Value.Host}/{endpoint}");
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("accesstoken", accessToken);
            request.AddHeader("Idempotency-Key", customIdempotencyKey);

            if (method == HttpMethod.Post && requestData != null)
            {
                var para = requestData.GetType().GetProperty("data") != null ? requestData : new { data = requestData };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
            }

            return request;
        }

        /// <summary>
        /// 执行HTTP请求
        /// </summary>
        private async Task<string> ExecuteHttpRequestAsync(
            RestClient restClient,
            RestRequest request,
            string operationName)
        {
            var restResponse = await restClient.PostAsync(request);
            _logger.LogAzure(operationName, $"{operationName}:{restResponse.Content}", operationName, Application.Enums.LogLevelEnum.Information);
            return restResponse.Content ?? string.Empty;
        }

        /// <summary>
        /// 处理金蝶API响应结果
        /// </summary>
        private KingdeeApiResult<T> ProcessKingdeeResponse<T>(string responseContent, string operationName)
        {
            if (string.IsNullOrEmpty(responseContent))
            {
                return KingdeeApiResult<T>.Failure($"【金蝶】{operationName}失败：响应内容为空", rawResponse: responseContent);
            }

            try
            {
                // 使用 object 作为中间类型来避免泛型约束问题
                var kingdeeResponse = JsonConvert.DeserializeObject<KingdeeBusinessResult<object>>(responseContent);

                if (kingdeeResponse == null)
                {
                    return KingdeeApiResult<T>.Failure($"【金蝶】{operationName}失败：响应反序列化为空", rawResponse: responseContent);
                }

                if (kingdeeResponse.status)
                {
                    var data = ConvertResponseData<T>(kingdeeResponse.data);
                    return KingdeeApiResult<T>.Success(data, responseContent);
                }

                // 处理错误情况
                var errorCode = kingdeeResponse.errorCode ?? "";
                var message = kingdeeResponse.message ?? "未知错误";

                // 处理特殊错误码 - 800通常表示数据已存在，这是业务错误而非系统错误
                if (errorCode == "800")
                {
                    return KingdeeApiResult<T>.BusinessError($"操作失败：{message}", errorCode, responseContent);
                }

                // 检查是否为其他业务错误（可根据实际情况扩展）
                bool isBusinessError = IsBusinessErrorCode(errorCode);

                return KingdeeApiResult<T>.Failure(
                    $"【金蝶】{operationName}失败：{message}",
                    errorCode,
                    isBusinessError,
                    responseContent);
            }
            catch (JsonException ex)
            {
                _logger.LogAzure($"金蝶响应JSON反序列化失败", $"响应：{responseContent},{ex.Message.ToString()}", "金蝶响应JSON反序列化失败", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult<T>.Failure(
                    $"【金蝶】{operationName}失败：响应格式错误 - {ex.Message}",
                    rawResponse: responseContent);
            }
            catch (Exception ex)
            {
                _logger.LogAzure($"转换金蝶响应数据失败", ex.Message.ToString(), "处理金蝶响应时发生未知错误", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult<T>.Failure(
                    $"【金蝶】{operationName}失败：处理响应时发生错误 - {ex.Message}",
                    rawResponse: responseContent);
            }
        }

        /// <summary>
        /// 转换响应数据
        /// </summary>
        private T? ConvertResponseData<T>(object? responseData)
        {
            if (responseData == null) return default;

            try
            {
                if (typeof(T).IsValueType)
                {
                    return (T)Convert.ChangeType(responseData, typeof(T));
                }
                else
                {
                    var dataJson = JsonConvert.SerializeObject(responseData);
                    return JsonConvert.DeserializeObject<T>(dataJson);
                }
            }
            catch (Exception ex)
            {
                _logger.LogAzure($"转换金蝶响应数据失败", ex.Message.ToString(), "使用默认值", Application.Enums.LogLevelEnum.Warning);
                return default;
            }
        }

        /// <summary>
        /// 判断是否为业务错误码
        /// </summary>
        private static bool IsBusinessErrorCode(string errorCode)
        {
            return errorCode switch
            {
                "800" => true,  // 数据已存在
                "801" => true,  // 数据不存在
                "802" => true,  // 数据状态不允许操作
                "803" => true,  // 业务规则验证失败
                _ => false
            };
        }

        /// <summary>
        /// 创建订阅日志（异步发布事件）
        /// </summary>
        protected async Task CreateSubLogAsync(SubLogSourceEnum source, string content, string operate)
        {
            try
            {
                await _logger.LogAsync(source.ToString(), content, operate);
            }
            catch (Exception ex)
            {
                _logger.LogAzure($"日志记录异常", ex.Message.ToString(), operate, Application.Enums.LogLevelEnum.Error);
            }
        }


        /// <summary>
        /// 获取应用令牌
        /// </summary>
        private async Task<string> GetAppTokenAsync()
        {
            using var restClient = new RestClient(_httpClient);
            var request = new RestRequest($"{_kingdeeSetting.Value.Host}/api/getAppToken.do");
            request.AddHeader("Content-Type", "application/json");

            var body = new
            {
                appId = _configuration["KingdeeSetting:AppId"],
                appSecret = _configuration["KingdeeSetting:AppSecret"],
                tenantid = _configuration["KingdeeSetting:Tenantid"],
                accountId = _configuration["KingdeeSetting:AccountId"],
                language = _configuration["KingdeeSetting:Language"]
            };

            request.AddParameter("application/json", body, ParameterType.RequestBody);
            var response = await restClient.PostAsync<KingdeeAuthResultDTO<ApptokenData>>(request);

            if (response == null || !response.status)
            {
                throw new Exception("获取金蝶Apptoken出错，请联系管理员");
            }

            return response.data?.app_token ?? throw new Exception("获取金蝶Apptoken失败：返回数据为空");
        }

        /// <summary>
        /// 登录获取访问令牌
        /// </summary>
        private async Task<string> LoginAsync(string appToken)
        {
            using var restClient = new RestClient(_httpClient);
            var request = new RestRequest($"{_kingdeeSetting.Value.Host}/api/login.do");
            request.AddHeader("Content-Type", "application/json");

            var body = new
            {
                user = _configuration["KingdeeSetting:User"],
                apptoken = appToken,
                tenantid = _configuration["KingdeeSetting:Tenantid"],
                accountId = _configuration["KingdeeSetting:AccountId"],
                usertype = _configuration["KingdeeSetting:Usertype"]
            };

            request.AddParameter("application/json", body, ParameterType.RequestBody);
            var response = await restClient.PostAsync<KingdeeAuthResultDTO<AccesstokenData>>(request);

            if (response == null || !response.status)
            {
                var errorDesc = response?.data?.error_desc ?? "未知错误";
                throw new Exception($"获取金蝶accesstoken出错，错误信息：{errorDesc}");
            }

            // 缓存访问令牌
            var accessToken = response.data?.access_token ?? throw new Exception("获取金蝶accesstoken失败：返回数据为空");
            await _easyCaching.SetAsync("kingdeeaccesstoken", accessToken, new TimeSpan(0, 59, 0));

            return accessToken;
        }
    }
}
