﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class RebateProvisionItemRepository : EfBaseRepository<Guid, RebateProvisionItem, RebateProvisionItemPo>, IRebateProvisionItemRepository
    {
        private readonly FinanceDbContext _db;
        public RebateProvisionItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<bool> AddRebateProvisionItems(List<RebateProvisionItem> items)
        {
            var toInsertItems = items.Adapt<List<RebateProvisionItemPo>>();
            await _db.RebateProvisionItem.AddRangeAsync(toInsertItems);
            await _db.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// 根据公司ID和单据日期范围查询返利计提记录
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>返利计提记录列表</returns>
        public async Task<List<RebateProvisionItem>> GetByCompanyIdAndDateRangeAsync(Guid companyId, DateTime startDate, DateTime endDate)
        {
            var rebateProvisionPos = await _db.RebateProvisionItem
                .AsNoTracking()
                .Where(r => r.CompanyId == companyId &&
                           r.BillDate >= startDate &&
                           r.BillDate <= endDate)
                .ToListAsync();

            return rebateProvisionPos.Adapt<List<RebateProvisionItem>>();
        }


        public Task<int> AddAsync(RebateProvisionItem root)
        {
            throw new NotImplementedException();
        }

       
        public Task<int> DeleteAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public Task<RebateProvisionItem?> GetAsync(Guid id)
        {
            throw new NotImplementedException();
        }


        public override Task<int> UpdateAsync(RebateProvisionItem root)
        {
            throw new NotImplementedException();
        }

        protected override RebateProvisionItemPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<RebateProvisionItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
