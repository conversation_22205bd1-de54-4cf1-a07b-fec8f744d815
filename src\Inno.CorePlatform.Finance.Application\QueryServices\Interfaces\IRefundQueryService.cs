﻿using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 退款查询
    /// </summary>
    public interface IRefundQueryService
    {
        /// <summary>
        /// 发票查询
        /// </summary>
        Task<(List<QueryPaymentRefundOutputData>, int)> GetListAsync(QueryPaymentRefundInput query);
    }
}
