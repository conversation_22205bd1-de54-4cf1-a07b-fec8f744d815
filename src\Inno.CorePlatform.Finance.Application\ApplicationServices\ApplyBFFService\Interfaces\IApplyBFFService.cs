﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces
{
    /// <summary>
    /// 外部能力中心接口聚合服务
    /// </summary>
    public interface IApplyBFFService
    {
        /// <summary>
        /// 获取系统月度
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<string> GetSysmonth(Guid companyId);

        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的公司
        /// </summary>
        /// <param name="input">入参，可以不填</param>
        /// <returns></returns>
        Task<List<CompanyOutput>> GetCompanyMetaListAsync(CompanyMetaInput input);
        /// <summary>
        /// 获取用户可操作的核算部门（带数据策略权限）
        /// 也可单独传核算部门Id获取核算部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BusinessDeptOutput>> GetBusinessDeptMetaAsync(BusinessDeptMetaInput input);
        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的医院权限
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<HospitalOutput>> GetHospitalMetaListAsync(HospitalMetaInput input);
        /// <summary>
        /// 获取客户部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompetenceCenter.BDSCenter.Outputs.CustomerDeptMetaInfoOutput>> GetCustomerDeptMetaInfos(BDSBaseInput input);
        /// <summary>
        /// 获取厂家信息（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ProducerOutput>> GetProducerMetaListAsync(ProducerMetaInput input);
        /// <summary>
        /// 根据UserId获取用户的信息
        /// 包含用户所属核算部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<UserInfoOutput> GetUserInfoByIdAsync(BDSBaseInput input);


        /// <summary>
        /// 获取公司详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompanyInfoOutput>> GetCompanyInfosAsync(BDSBaseInput input);


        /// <summary>
        /// 获取公司详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompetenceCenter.BDSCenter.Outputs.CompanyMetaInfosOut>> GetCompanyMetaInfosAsync(CompanyMetaInfosInput input);

        /// <summary>
        /// 获取供应商详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<AgentInfoOutput>> GetAgentInfosAsync(BDSBaseInput input);


        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的业务单元
        /// </summary>
        /// <returns></returns>
        Task<List<ServiceOutput>> GetServiceMetaListAsync(ServiceMetaInput input);
        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的供应商
        /// </summary>
        /// <returns></returns>
        Task<List<AgentOutput>> GetAgentMetaListAsync(AgentMetaInput input);



        /// <summary>
        /// 获取业务单元详情列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CompetenceCenter.BDSCenter.Outputs.ServiceOutput>> QueryServiceItem(BDSBaseInput input);
        /// <summary>
        /// 从用户中心，获取员工数据 byNames
        /// </summary>
        /// <returns></returns>
        Task<ResponseData<UserOutput>> GetUserByNamesAsync(GetUserInput input);
        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="intput"></param>
        /// <returns></returns>
        Task<CompetenceCenter.BDSCenter.Outputs.CustomerOutput> GetCustomer(BDSBaseInput intput);
    }
}
