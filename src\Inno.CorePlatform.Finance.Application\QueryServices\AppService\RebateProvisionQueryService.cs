﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using OfficeOpenXml;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class RebateProvisionQueryService : QueryAppService, IRebateProvisionQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _iBDSApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IRebateProvisionItemRepository _itemRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ICodeGenClient _codeGenClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="iBDSApiClient"></param>
        public RebateProvisionQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db,
            IRebateProvisionItemRepository itemRepository,
            IUnitOfWork unitOfWork,
            IBDSApiClient iBDSApiClient,
            IKingdeeApiClient kingdeeApiClient,
            ICodeGenClient codeGenClient,
            IProjectMgntApiClient projectMgntApiClient,
            IPCApiClient pCApiClient) :
            base(contextAccessor)
        {
            this._db = db;
            this._iBDSApiClient = iBDSApiClient;
            this._pCApiClient = pCApiClient;
            this._itemRepository = itemRepository;
            this._unitOfWork = unitOfWork;
            this._appServiceContextAccessor = contextAccessor;
            this._kingdeeApiClient = kingdeeApiClient;
            this._codeGenClient = codeGenClient;
            this._projectMgntApiClient = projectMgntApiClient;
        }


        /// <summary>
        /// 返利计提查询
        /// </summary>
        public async Task<(List<RebateProvisionItemQueryOutput>, int)> GetListAsync(RebateProvisionItemQueryInput input)
        {
            Expression<Func<RebateProvisionItemPo, bool>> exp = z => 1 == 1;
            var query = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(query);
            #region 查询条件
            exp = await InitExp(input, exp, strategry);
            #endregion
            IQueryable<RebateProvisionItemPo> baseQuery = _db.RebateProvisionItem.Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();
            //总条数
            var count = baseQuery.Count();
            //分页
            var list = await baseQuery.OrderByDescending(p => p.BillCode).ThenByDescending(p => p.CompanyName).Skip((input.page - 1) * input.limit).Take(input.limit).Select(z => z.Adapt<RebateProvisionItemQueryOutput>()).ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 数据策略权限增加(返利计提查询)
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<IQueryable<RebateProvisionItemQueryOutput>> AddStrategyQueryAsync(StrategyQueryInput queryModel, IQueryable<RebateProvisionItemQueryOutput> query)
        {
            if (queryModel != null)
            {
                var strategry = await _pCApiClient.GetStrategyAsync(queryModel);
                if (strategry != null && strategry.RowStrategies.Count > 0)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                    {
                        query = query.Where(z => 1 != 1);
                    }
                    query = await AnalysisStrategy(strategry.RowStrategies, query);
                }
                else
                {
                    query = query.Where(z => 1 != 1);
                }
            }

            return query;
        }
        /// <summary>
        /// 增加数据策略权限(返利计提查询)
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected async Task<IQueryable<RebateProvisionItemQueryOutput>> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<RebateProvisionItemQueryOutput> query)
        {
            foreach (var key in rowStrategies.Keys)
            {
                if (key.ToLower() == "company")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToGuidHashSet();
                        query = query.Where(z => z.CompanyId != null && strategList.Contains(z.CompanyId.Value));
                    }
                }
                if (key.ToLower() == "accountingdept")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToHashSet();
                        var details = await (from detail in _db.RebateProvisionDetail where strategList.Contains(detail.BusinessDeptId) select detail).ToListAsync();
                        var ids = details.Select(i => i.RebateProvisionItemId).ToList();
                        query = query.Where(z => ids.Contains(z.Id));
                    }
                }
                if (key.ToLower() == "customer")
                {
                    if (!rowStrategies[key].Any(s => s == "@all"))
                    {
                        var strategList = rowStrategies[key].ToGuidHashSet();
                        var details = await (from detail in _db.RebateProvisionDetail where strategList.Contains(detail.CustomerId.Value) select detail).ToListAsync();
                        var ids = details.Select(i => i.RebateProvisionItemId).ToList();
                        query = query.Where(z => ids.Contains(z.Id));
                    }
                }
            }
            return query;
        }

        /// <summary>
        /// 根据返利计提Id查询返利计提明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<(List<RebateProvisionDetailQueryOutput>, int)> GetDetailsByItemIdAsync(RebateProvisionDetailQueryInput input)
        {
            var Query = from details in _db.RebateProvisionDetail
                        where details.RebateProvisionItemId == input.RebateProvisionItemId
                        select new RebateProvisionDetailQueryOutput
                        {
                            RebateProvisionItemId = details.RebateProvisionItemId,
                            ConfirmationDate = details.ConfirmationDate,
                            ConfirmTaxAmount = details.ConfirmTaxAmount,
                            RebateType = details.RebateType,
                            RebateAmount = details.RebateAmount,
                            RebateTaxAmount = details.RebateTaxAmount,
                            NextAmount = details.NextAmount,
                            NextTaxAmount = details.NextTaxAmount,
                            NextRebateMethod = details.NextRebateMethod,
                            BusinessDeptId = details.BusinessDeptId,
                            BusinessDeptFullName = details.BusinessDeptFullName,
                            BusinessDeptFullPath = details.BusinessDeptFullPath,
                            ProjectName = details.ProjectName,
                            AgentId = details.AgentId,
                            PeriodSummary = details.PeriodSummary,
                            AgentName = details.AgentName,
                            CustomerId = details.CustomerId,
                            CustomerName = details.CustomerName
                        };

            int count = await Query.CountAsync();
            var list = await Query.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

            return (list, count);
        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Submit(Guid id)
        {
            var ret = BaseResponseData<string>.Failed(500, "操作失败");
            var rebateProvision = await _db.RebateProvisionItem.FirstOrDefaultAsync(p => p.Id == id && p.Status != StatusEnum.Complate);
            if (rebateProvision != null)
            {
                var rebateProvisionDetails = await _db.RebateProvisionDetail.Where(p => p.RebateProvisionItemId == id).ToListAsync();
                if (rebateProvisionDetails != null && rebateProvisionDetails.Any())
                {
                    var rebateProvisionSaveInput = new DTOs.RebateProvisionSaveInput
                    {
                        billno = rebateProvision.BillCode,
                        jfzx_provisiontype = (int)rebateProvision.ProvisionType,
                        org = rebateProvision.NameCode
                    };
                    var entrys = new List<RebateProvisionEntryModel>();
                    foreach (var detail in rebateProvisionDetails)
                    {
                        var entryModel = new RebateProvisionEntryModel
                        {
                            jfzx_actualarrivaldate = detail.ActualarrivalDate,
                            jfzx_bizorg = detail.BusinessDeptId,
                            jfzx_confirmationdate = detail.ConfirmationDate,
                            jfzx_confirmtaxexamount = detail.ConfirmTaxAmount,
                            jfzx_coupondate = detail.CouponDate,
                            jfzx_customer = detail.CustomerId.HasValue ? detail.CustomerId.ToString().ToUpper() : "",
                            jfzx_nextamount = detail.NextAmount,
                            jfzx_nextinvoiceorcoupon = detail.NextInvoiceOrCoupon ?? "",
                            jfzx_nextrebatemethod = detail.NextRebateMethod.HasValue ? detail.NextRebateMethod.ToString() : "",
                            jfzx_nexttaxamount = detail.NextTaxAmount,
                            jfzx_periodsummary = detail.PeriodSummary ?? "",
                            jfzx_policydeadline = detail.Policydeadline ?? "",
                            jfzx_projectno = detail.ProjectCode ?? "",
                            jfzx_rebateamount = detail.RebateAmount,
                            jfzx_rebatetaxamount = detail.RebateTaxAmount,
                            jfzx_rebatetype = detail.RebateType.HasValue ? detail.RebateType.ToString() : "",
                            jfzx_redinvoice = detail.Redinvoice ?? "",
                            jfzx_supplier = detail.AgentId.HasValue ? detail.AgentId.ToString().ToUpper() : "",
                        };
                        entrys.Add(entryModel);
                    }
                    rebateProvisionSaveInput.entrys = entrys;
                    ret = await _kingdeeApiClient.RebateProvisionSave(new List<DTOs.RebateProvisionSaveInput> { rebateProvisionSaveInput });

                }
                if (ret.Code == CodeStatusEnum.Success || rebateProvisionDetails == null || rebateProvisionDetails.Count() == 0)
                {
                    rebateProvision.Status = StatusEnum.Complate;
                    await _unitOfWork.CommitAsync();
                    ret.Code = CodeStatusEnum.Success;
                }
            }
            return ret;
        }

        /// <summary>
        /// 获取页签数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<RebateProvisionItemTabOutput> GetTabCountAsync([FromBody] RebateProvisionItemQueryInput input)
        {
            try
            {
                input.Status = -1;
                Expression<Func<RebateProvisionItemPo, bool>> expAll = z => 1 == 1;
                var query = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                var strategry = await _pCApiClient.GetStrategyAsync(query);
                #region 查询条件
                expAll = await InitExp(input, expAll, strategry);
                #endregion
                IQueryable<RebateProvisionItemPo> baseQuery = _db.RebateProvisionItem.Where(expAll).AsNoTracking();
                //分页
                var list = await baseQuery.Select(z => z.Adapt<RebateProvisionItemQueryOutput>()).ToListAsync();
                var result = new RebateProvisionItemTabOutput
                {
                    AllCount = list.Count(),
                    WaitSubmitCount = list.Where(t => t.Status == StatusEnum.waitSubmit).Count(),
                    WaitAuditCount = list.Where(t => t.Status == StatusEnum.waitAudit).Count(),
                    ComplateCount = list.Where(t => t.Status == StatusEnum.Complate).Count(),
                };
                return result;
            }
            catch (Exception ex)
            {
                var result = new RebateProvisionItemTabOutput
                {
                    AllCount = 0,
                    WaitSubmitCount = 0,
                    WaitAuditCount = 0
                };
                return result;
            }
        }

        private async Task<Expression<Func<RebateProvisionItemPo, bool>>> InitExp(RebateProvisionItemQueryInput input, Expression<Func<RebateProvisionItemPo, bool>> exp, StrategyQueryOutput? strategry)
        {
            var user = await _iBDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                else
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }

                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(input.BillDateBeging) && !string.IsNullOrWhiteSpace(input.BillDateEnd))
            {
                exp = exp.And(z => z.BillDate <= DateTimeHelper.GetDateTime(Convert.ToInt64(input.BillDateEnd)) && z.BillDate >= DateTimeHelper.GetDateTime(Convert.ToInt64(input.BillDateBeging)));
            }
            if (input.CompanyId.HasValue)
            {
                exp = exp.And(x => x.CompanyId.HasValue && x.CompanyId == input.CompanyId);
            }
            if (input.Status != -1)
            {
                exp = exp.And(x => (int)x.Status == input.Status);
            }
            return exp;
        }

        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> Create(RebateProvisionItemCreateInput input)
        {
            try
            {
                var companyInfo = (await _iBDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { input.CompanyId.ToString() }
                })).FirstOrDefault();

                var entity = new RebateProvisionItemPo
                {
                    CompanyId = input.CompanyId,
                    BillDate = Convert.ToDateTime(companyInfo.sysMonth),
                    BillCode = companyInfo.nameCode + $"-{DateTime.Now.ToString("yyMM")}-JT-0001",
                    CreatedBy = input.CurrentUser ?? "none",
                    CompanyName = companyInfo.companyName,
                    ProvisionType = input.ProvisionType,
                    Status = StatusEnum.waitSubmit,
                    NameCode = companyInfo.nameCode,
                    BusinessDeptFullPath = input.BusinessDeptFullPath,
                    BusinessDeptFullName = input.BusinessDeptFullName,
                    BusinessDeptId = input.BusinessDeptId,
                    BusinessDeptShortName = input.BusinessDeptShortName
                };
                entity.Id = Guid.NewGuid();
                await _db.AddAsync(entity);
                return await _unitOfWork.CommitAsync();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 移除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> Remove(RebateProvisionDetailQueryInput input)
        {
            try
            {
                var entity = await _db.RebateProvisionItem.Where(p => p.Id == input.RebateProvisionItemId).FirstOrDefaultAsync();
                if (entity != null)
                {
                    _db.RebateProvisionItem.Remove(entity);
                    var details = await (from detail in _db.RebateProvisionDetail where detail.RebateProvisionItemId == input.RebateProvisionItemId select detail).ToListAsync();
                    _db.RebateProvisionDetail.RemoveRange(details);
                }
                return _db.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 移除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<int> RemoveDetails(RebateProvisionDetailQueryInput input)
        {
            try
            {
                var entity = await _db.RebateProvisionItem.Where(p => p.Id == input.RebateProvisionItemId).FirstOrDefaultAsync();
                if (entity != null)
                {
                    var details = await (from detail in _db.RebateProvisionDetail where detail.RebateProvisionItemId == input.RebateProvisionItemId select detail).ToListAsync();
                    _db.RebateProvisionDetail.RemoveRange(details);
                }
                return _db.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<int> AddDetails(List<RebateProvisionDetail> inputs, Guid rebateProvisionItemId)
        {
            try
            {
                var entity = await _db.RebateProvisionItem.Where(p => p.Id == rebateProvisionItemId).FirstOrDefaultAsync();
                if (entity != null)
                {
                    var rebateProvisionDetails = inputs.Adapt<List<RebateProvisionDetailPo>>();
                    rebateProvisionDetails.ForEach(p =>
                    {
                        p.RebateProvisionItemId = rebateProvisionItemId;
                        p.Id = Guid.NewGuid();
                        p.RebateType = p.RebateType ?? RebateTypeOfKdEnum.A;
                    });
                    _db.RebateProvisionDetail.AddRange(rebateProvisionDetails);
                }
                return _db.SaveChanges();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 导入返利计提明细
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ImportDetailsData(Guid id, IFormFile file, Guid? userId, string? userName)
        {
            try
            {
                if (file == null)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "请上传导入文件"
                    };
                }
                // 已有详情明细
                var details = await _db.RebateProvisionDetail.Where(x => x.RebateProvisionItemId == id).ToListAsync();
                // 需要覆盖更新的数据集合
                var dellist = new List<RebateProvisionDetailPo>();
                Stream stream = file.OpenReadStream();
                var excelData = new List<RebateProvisionDetail>();
                using (ExcelPackage package = new ExcelPackage(stream))
                {
                    ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0];
                    int rowCount = worksheet.Dimension.Rows;
                    if (rowCount <= 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "请填写Excel文件中的数据"
                        };
                    }
                    for (var row = 2; row <= rowCount; row++)
                    {
                        // 返利方式填写则校验上游所有数据必填
                        if (worksheet.Cells[row, 3].Value != null)
                        {
                            if (worksheet.Cells[row, 4].Value == null || worksheet.Cells[row, 5].Value == null || worksheet.Cells[row, 12].Value == null)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请填写第{row}行返利方式相同颜色板块内容"
                                };
                            }
                        }
                        // 下家返利方式填写则校验下家所有数据必填
                        if (worksheet.Cells[row, 9].Value != null)
                        {
                            if (worksheet.Cells[row, 7].Value == null || worksheet.Cells[row, 8].Value == null || worksheet.Cells[row, 13].Value == null)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请填写第{row}行下家返利方式相同颜色板块内容"
                                };
                            }
                        }
                        // 二者都为空
                        if (worksheet.Cells[row, 3].Value == null && worksheet.Cells[row, 9].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"第{row}行两种返利方式必填其一"
                            };
                        }

                        if (worksheet.Cells[row, 10].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行的核算部门"
                            };
                        }
                        if (worksheet.Cells[row, 11].Value == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"请填写第{row}行的项目名称"
                            };
                        }
                        decimal confirmTaxAmount = 0;
                        if (worksheet.Cells[row, 2].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 2].Value.ToString(), out confirmTaxAmount);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的确认函不含税金"
                                };
                            }
                        }
                        decimal rebateAmount = 0;
                        if (worksheet.Cells[row, 4].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 4].Value.ToString(), out rebateAmount);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的返利金额"
                                };
                            }
                        }
                        decimal rebateTaxAmount = 0;
                        if (worksheet.Cells[row, 5].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 5].Value.ToString(), out rebateTaxAmount);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的不含税金额"
                                };
                            }
                        }
                        decimal nextAmount = 0;
                        if (worksheet.Cells[row, 7].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 7].Value.ToString(), out nextAmount);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的下家返利金额"
                                };
                            }
                        }
                        decimal nextTaxAmount = 0;
                        if (worksheet.Cells[row, 8].Value != null)
                        {
                            bool isValid = decimal.TryParse(worksheet.Cells[row, 8].Value.ToString(), out nextTaxAmount);
                            if (!isValid)
                            {
                                return new BaseResponseData<int>()
                                {
                                    Code = CodeStatusEnum.Failed,
                                    Message = $"请正确填写第{row}行的下家返利不含税金额"
                                };
                            }
                        }
                        // 具体的获取数据                        
                        var entity = new RebateProvisionDetail
                        {
                            Id = Guid.NewGuid(),
                            RebateProvisionItemId = id,
                            ConfirmationDate = worksheet.Cells[row, 1].Value != null ? Convert.ToDateTime(worksheet.Cells[row, 1].Value) : null,
                            ConfirmTaxAmount = confirmTaxAmount,
                            RebateType = worksheet.Cells[row, 3].Value != null ? EnumHelper.GetEnumByValue<RebateTypeOfKdEnum>(worksheet.Cells[row, 3].Value.ToString()) : RebateTypeOfKdEnum.A,
                            RebateAmount = rebateAmount,
                            RebateTaxAmount = rebateTaxAmount,
                            PeriodSummary = worksheet.Cells[row, 6].Value != null ? Utility.GetCleanedCellString(worksheet.Cells[row, 6].Value.ToString()) : string.Empty,
                            NextAmount = nextAmount,
                            NextTaxAmount = nextTaxAmount,
                            NextRebateMethod = worksheet.Cells[row, 9].Value != null ? EnumHelper.GetEnumByValue<NextRebateMethodEnum>(worksheet.Cells[row, 9].Value.ToString()) : NextRebateMethodEnum.A,
                            BusinessDeptFullName = worksheet.Cells[row, 10].Value != null ? Utility.GetCleanedCellString(worksheet.Cells[row, 10].Value.ToString()) : string.Empty,
                            ProjectName = worksheet.Cells[row, 11].Value != null ? Utility.GetCleanedCellString(worksheet.Cells[row, 11].Value.ToString()) : string.Empty,
                            AgentName = worksheet.Cells[row, 12].Value != null ? Utility.GetCleanedCellString(worksheet.Cells[row, 12].Value.ToString()) : string.Empty,
                            CustomerName = worksheet.Cells[row, 13].Value != null ? Utility.GetCleanedCellString(worksheet.Cells[row, 13].Value.ToString()) : string.Empty,
                        };
                        excelData.Add(entity);
                    }

                    if (excelData.Count != rowCount - 1)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = "Excel中存在单元格为空的数据，请检查"
                        };
                    }
                }

                #region 查询部门信息 
                GetFlatCheckedDeptsInput businessInput = new();
                businessInput.functionUri = StrategyConstant.AgentQuery_LIST_STRATEGY;
                var daprResult = await _iBDSApiClient.GetFlatCheckedDepts(businessInput, userName, userId.Value.ToString());
                #endregion

                #region 查询项目信息
                var projectNames = excelData.Select(x => x.ProjectName).ToList();
                var projResult = await _projectMgntApiClient.GetProjectListByNames(userId, projectNames, StrategyConstant.AgentQuery_LIST_STRATEGY);
                #endregion

                #region 查询供应商信息
                var agentNames = excelData.Select(x => x.AgentName).ToList();
                AgentMetaInput agentInput = new();
                //agentInput.functionUri = StrategyConstant.AgentQuery_LIST_STRATEGY;
                agentInput.names = agentNames;
                var agenResult = await _iBDSApiClient.GetAgentMetaListAsync(agentInput);
                #endregion

                #region 查询客户信息
                var customerNames = excelData.Select(x => x.CustomerName).ToList();
                BDSBaseInput custInput = new();
                custInput.names = customerNames;
                var custResult = await _iBDSApiClient.GetCustomerByNames(custInput);
                #endregion

                foreach (var item in excelData)
                {
                    var businessSingle = daprResult.FirstOrDefault(x => x.deptName == item.BusinessDeptFullName);
                    if (businessSingle == null)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"核算部门{item.BusinessDeptFullName}未找到，导入失败"
                        };
                    }
                    item.BusinessDeptId = businessSingle.id;
                    var projectSingle = projResult.FirstOrDefault(x => x.Name == item.ProjectName);
                    if (projectSingle == null)
                    {
                        return new BaseResponseData<int>()
                        {
                            Code = CodeStatusEnum.Failed,
                            Message = $"项目名称{item.ProjectName}未找到，导入失败"
                        };
                    }
                    item.ProjectId = projectSingle != null ? projectSingle.Id.Value : Guid.Empty;
                    item.ProjectCode = projectSingle != null ? projectSingle.Code : string.Empty;

                    if (!string.IsNullOrEmpty(item.AgentName))
                    {
                        var agentSingle = agenResult.FirstOrDefault(x => x.name == item.AgentName);
                        if (agentSingle == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"供应商{item.AgentName}未找到，导入失败"
                            };
                        }
                        item.AgentId = agentSingle != null ? Guid.Parse(agentSingle.id) : Guid.Empty;
                    }
                    if (!string.IsNullOrEmpty(item.CustomerName))
                    {
                        var customerSingle = custResult.FirstOrDefault(x => x.Name == item.CustomerName);
                        if (customerSingle == null)
                        {
                            return new BaseResponseData<int>()
                            {
                                Code = CodeStatusEnum.Failed,
                                Message = $"客户{item.CustomerName}未找到，导入失败"
                            };
                        }
                        item.CustomerId = customerSingle != null ? Guid.Parse(customerSingle.Id) : Guid.Empty;
                    }
                }

                #region 查询部门路径
                var businessDeptIds = excelData.Select(x => x.BusinessDeptId).ToList();
                var daprResult2 = await _iBDSApiClient.GetBusinessDeptListByIds(businessDeptIds);
                #endregion

                foreach (var item in excelData)
                {
                    var businessSingle = daprResult2.FirstOrDefault(x => x.id == item.BusinessDeptId);
                    item.BusinessDeptFullName = businessSingle != null ? businessSingle.deptFullName : string.Empty;
                    item.BusinessDeptFullPath = businessSingle != null ? businessSingle.path : string.Empty;
                }
                dellist = details;
                // 存在重复数据，覆盖更新（删除后重新添加）
                _db.RebateProvisionDetail.RemoveRange(dellist);
                var i = await AddDetails(excelData, id);
                if (i > 0)
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Success,
                        Message = "导入成功"
                    };
                }
                else
                {
                    return new BaseResponseData<int>()
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = "导入失败"
                    };
                }
            }
            catch (Exception ex)
            {
                return new BaseResponseData<int>()
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }

        }

        /// <summary>
        /// 获取未完成的返利计提单据（包含损失确认数据）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<NotFinishBillOutput>> GetNotFinishBillAsync(NotFinishBillQueryInput input)
        {
            try
            {
                var result = new List<NotFinishBillOutput>();

                // 1. 查询返利计提数据
                var rebateResult = await GetNotFinishRebateProvisionAsync(input);
                result.AddRange(rebateResult);

                // 2. 查询损失确认数据（临时草稿和待审核）
                var lossRecognitionResult = await GetNotFinishLossRecognitionAsync(input);
                result.AddRange(lossRecognitionResult);

                return result;
            }
            catch (Exception ex)
            {
                // 记录错误日志
                throw new Exception($"获取未完成单据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取未完成的返利计提单据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<List<NotFinishBillOutput>> GetNotFinishRebateProvisionAsync(NotFinishBillQueryInput input)
        {
            // 构建查询条件
            Expression<Func<RebateProvisionItemPo, bool>> exp = z => 1 == 1;

            // 过滤公司ID
            if (input.CompanyIds != null && input.CompanyIds.Count > 0)
            {
                var companyGuids = input.CompanyIds.Select(Guid.Parse).ToList();
                exp = exp.And(z => z.CompanyId.HasValue && companyGuids.Contains(z.CompanyId.Value));
            }

            // 过滤创建人（可选）
            if (input.CreatedBy != null && input.CreatedBy.Count > 0)
            {
                exp = exp.And(z => input.CreatedBy.Contains(z.CreatedBy));
            }

            // 只查询未完成的单据（状态不是已完成）
            exp = exp.And(z => z.Status != StatusEnum.Complate);

            // 查询数据
            var query = _db.RebateProvisionItem.Where(exp).AsNoTracking();

            var result = await query.Select(z => new NotFinishBillOutput
            {
                BillCode = z.BillCode ?? string.Empty,
                BillType = z.Status.ToString(),
                BillTypeDesc = "返利计提",
                BillDate = z.BillDate.HasValue ? ((DateTimeOffset)z.BillDate.Value).ToUnixTimeMilliseconds() : 0,
                BillStatusDesc = z.Status.GetDescription(),
                CreatedBy = z.CreatedBy ?? string.Empty,
                ProcessType = 3,
                ProcessTypeDesc = "系统提醒"
            }).ToListAsync();

            return result;
        }

        /// <summary>
        /// 获取未完成的损失确认单据（临时草稿和待审核）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<List<NotFinishBillOutput>> GetNotFinishLossRecognitionAsync(NotFinishBillQueryInput input)
        {
            // 构建查询条件
            Expression<Func<LossRecognitionItemPo, bool>> exp = z => 1 == 1;

            // 过滤公司ID
            if (input.CompanyIds != null && input.CompanyIds.Count > 0)
            {
                var companyGuids = input.CompanyIds.Select(Guid.Parse).ToList();
                exp = exp.And(z => z.CompanyId.HasValue && companyGuids.Contains(z.CompanyId.Value));
            }

            // 过滤创建人（可选）
            if (input.CreatedBy != null && input.CreatedBy.Count > 0)
            {
                exp = exp.And(z => input.CreatedBy.Contains(z.CreatedBy));
            }

            // 只查询临时草稿和待审核状态的损失确认单据
            exp = exp.And(z => z.Status == StatusEnum.waitSubmit || z.Status == StatusEnum.waitAudit);

            // 查询数据
            var query = _db.LossRecognitionItem.Where(exp).AsNoTracking();

            var result = await query.Select(z => new NotFinishBillOutput
            {
                BillCode = z.BillCode ?? string.Empty,
                BillType = z.Status != null ? z.Status.ToString() : string.Empty,
                BillTypeDesc = "损失确认",
                BillDate = ((DateTimeOffset)z.BillDate).ToUnixTimeMilliseconds(),
                BillStatusDesc = z.Status != null ? z.Status.GetDescription() : string.Empty,
                CreatedBy = z.CreatedBy ?? string.Empty,
                ProcessType = 1,
                ProcessTypeDesc = "挂起"
            }).ToListAsync();

            return result;
        }
    }
}
