using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 购货修订明细DTO
    /// </summary>
    public class GetReviseOrderForFinanceInvoiceOutput
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string purchaseOrderCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime billDate { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public string productNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public string agentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string agentName { get; set; }

        /// <summary>
        /// 生产商ID
        /// </summary>
        public string producerId { get; set; }

        /// <summary>
        /// 生产商名称
        /// </summary>
        public string producerName { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string spec { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string productId { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string productNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal quantity { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public string companyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string companyName { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxRate { get; set; }

        /// <summary>
        /// 可入票金额
        /// </summary>
        public decimal canInvoiceAmount { get; set; }
    }
}
