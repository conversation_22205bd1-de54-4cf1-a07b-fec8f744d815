﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class TempInventoryDetailOutput
    {
        public string id { get; set; }
        public Guid? businessUnitId { get; set; }
        public string businessUnitIdName { get; set; }
        public Guid agentId { get; set; }
        public string agentName { get; set; }
        /// <summary>
        /// 暂存科室id
        /// </summary>
        public string deptId { get; set; }
        /// <summary>
        /// 暂存科室
        /// </summary>
        public string deptName { get; set; }
        public string staffId { get; set; }
        public string staffName { get; set; }
        public string tempInventoryId { get; set; }
        public int mark { get; set; }
        public string markName { get; set; }
        public string traceCode { get; set; }
        public string barcode { get; set; }
        public string lotNo { get; set; }
        public string sn { get; set; }
        public string source { get; set; }
        public Guid productId { get; set; }
        public string productNo { get; set; }
        public string productName { get; set; }
        public string brand { get; set; }
        public string categoryName { get; set; }
        public string producerName { get; set; }
        public string specification { get; set; }
        public string packUnit { get; set; }
        public string productTypeDesc { get; set; }
        public string description { get; set; }
        public int quantity { get; set; }
        public decimal price { get; set; }
        public decimal unitCost { get; set; }
        public decimal? actualCost { get; set; }
        public decimal? costTotal { get; set; }
        public decimal? amount { get; set; }
        public decimal? taxRate { get; set; }
        public decimal? salesTaxRate { get; set; }
        public DateTime? storageTime { get; set; }
        public DateTime? produceDate { get; set; }
        public DateTime? useTime { get; set; }
        public DateTime? validDate { get; set; }
        public DateTime? writeOffTime { get; set; }
        public Guid? productNameId { get; set; }
        public Guid? projectId { get; set; }
        /// <summary>
        /// 销售明细Id
        /// </summary>
        public Guid? SaleDetailId { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? standardUnitCost { get; set; }
        /// <summary>
        /// 注册证号
        /// </summary>
        public string? RegNo { get; set; }

        /// <summary>
        /// 消费者单价（旺店通）
        /// </summary>
        public decimal? ConsumerPrice { get; set; }

        /// <summary>
        /// 积分红包单价（旺店通）
        /// </summary>
        public decimal? PlatformCouponPrice { get; set; }
        /// <summary>
        /// 平台红包是否需要开票，默认为空，为空的也是需要开票，只有为false就是不用开票
        /// </summary>
        public bool? RequiresPlatformCouponInvoice { get; set; }

        internal bool? IsNoNeedInvoice()
        {
            if (RequiresPlatformCouponInvoice.HasValue && RequiresPlatformCouponInvoice.Value == false)
            {
                return true;//不需要开票
            }
            else
            {
                return false;//需要开票
            }
        }
    }


}
