using System;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.ServiceFee
{
    /// <summary>
    /// 更新服务费入票信息请求
    /// </summary>
    public class UpdateServiceFeeForFinanceInvoiceInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNumber { get; set; }

        /// <summary>
        /// 入票标志，true=入票，false=撤销入票
        /// </summary>
        public bool InvoiceFlag { get; set; }

        /// <summary>
        /// 明细列表
        /// </summary>
        public List<UpdateServiceFeeForFinanceInput> List { get; set; }
    }

    /// <summary>
    /// 更新服务费入票明细
    /// </summary>
    public class UpdateServiceFeeForFinanceInput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 入票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public string ProductNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }
    }
}
