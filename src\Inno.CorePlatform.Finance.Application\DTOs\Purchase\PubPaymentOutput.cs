﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 强制结束发布游离付款单
    /// </summary>
    public class PubPaymentOutput
    {
        //游离付款单号
        public string Code { get; set; }
        //采购单号
        public string PurchaseCode { get; set; }
        //金额
        public decimal Amount { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string CoinCode { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string CoinName { get; set; }
    }
}
