﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IRecognizeReceiveQueryService
    {
        /// <summary>
        /// 获取认款清单
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<RecognizeReceiveItemOutPut>> GetListPages(RecognizeReceiveItemInput input);
        /// <summary>
        /// 获取认款清单页签数量
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCount(RecognizeReceiveItemInput input);
        /// <summary>
        /// 异步获取可认款金额
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<RemainingRecognizableAmountOutput>>> GetRemainingRecognizableAmountByCodes(List<string> codes);
        /// <summary>
        /// 获取认款款详情
        /// </summary>
        /// <returns></returns>
        Task<List<RecognizeReceiveDetailOutput>> GetDetails(Guid? id,int? Classify, RecognizeReceiveDetailEnum? status);
        /// <summary>
        /// 获取认款单详情
        /// </summary>
        /// <param name="ids">id</param>
        /// <param name="Classify">类型</param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<List<RecognizeReceiveDetailOutput>> GetDetailsByIdAndClassify(List<Guid> ids, int? Classify);
        Task<RecognizeReceiveDetailOutput> GetDetail(Guid? id);
        
        Task<List<RecognizeReceiveDetailOutput>> GetDetailByIds(List<Guid> ids);
        Task<List<RecognizeReceiveOutput>> GetKdReceiveBills(RecognizeReceiveInput input);
        Task<RecognizeReceiveItemOutPut> GetItemById(Guid id);
        Task<List<RecognizeReceiveItemOutPut>> GetItemByReceiveCode(string receiveCode);
        /// <summary>
        /// 根据业务单元获取认款清单
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<RecognizeReceiveItemOutPut>> GetListByServiceIdPages(RecognizeReceiveItemInput input);

        /// <summary>
        /// 业务单元端认款单查询（页签数量）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<RecognizeReceiveItemTabCount>> GetTabCountByService(RecognizeReceiveItemInput input);

        Task<BaseResponseData<bool>> IsExists(IsExistsInput input);

        /// <summary>
        /// 根据单号或者销售单号获取
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<List<RecognizeReceiveBatchQueryByCodeOutput>>> BatchQueryRecognizeReceiveByCode(RecognizeReceiveBatchQueryByCodeInput input);

        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<RecognizeReceiveDetailExportOutput>, int)> GetExportDetails(ExportDetailsInput input);
        /// <summary>
        /// 获取供应商退款冲销列
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<AgentRefundAbatementOutput>> GetAgentRefundAbatements(AgentRefundAbatementInput input);
    }
}
