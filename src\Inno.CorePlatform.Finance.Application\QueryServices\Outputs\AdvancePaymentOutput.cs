﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class AdvancePaymentOutput
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 提前付款名称
        /// </summary>
        [MaxLength(200)]
        public string Name { get; set; }
        /// <summary>
        /// 单号
        /// </summary> 
        [Comment("单号")]
        [MaxLength(200)]
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        [Comment("单据日期")]
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 取数开始时间
        /// </summary> 
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 取数结束时间
        /// </summary> 
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 回款天数
        /// </summary>
        public int? Day { get; set; }

        /// <summary>
        /// 月利率
        /// </summary> 
        public decimal? MonthRate { get; set; }
        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }
        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }
        /// <summary>
        /// 付供应商货款日期
        /// </summary>
        public DateTime? PaySupplierGoodsDate { get; set; }
        /// <summary>
        /// 垫资应收金额合计 
        /// </summary> 
        public decimal? AdvanceCreditAmount { get; set; }
        /// <summary>
        /// 含税垫资毛利合计
        /// </summary> 
        public decimal? AdvanceCreditTaxAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public AdvancePaymentStatusEnum Status { get; set; }
        /// <summary>
        /// 流程请求Id
        /// </summary> 
        public string? OARequestId { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary> 
        public DateTimeOffset? CreatedTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary> 
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 附件id
        /// </summary> 
        public string? AttachFileIds { get; set; }
    }

    /// <summary>
    /// 提前付款垫资 应付明细
    /// </summary>
    public class AdvancePaymentDebtDetailOutput
    {
        /// <summary>
        /// 应付id
        /// </summary>
        public Guid? DebtId { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum? AccountPeriodType { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期 4=验收账期 5=质保账期
        /// </summary>
        public string? AccountPeriodTypeStr
        {
            get
            {
                return AccountPeriodType.HasValue ? AccountPeriodType.GetDescription() : string.Empty;
            }
        }

        /// <summary>
        /// 开票日期
        /// </summary>  
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 回款天数 
        /// </summary>
        public int? ReturnDays { get; set; }

        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? EstimateReturnDate { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal? PaymentAmount { get; set; }

        /// <summary>
        /// 实际支付上游日期
        /// </summary>
        public DateTime? ActualPaymentDate { get; set; }

        /// <summary>
        /// 垫资天数
        /// </summary>
        public int? AdvancePaymentDays { get; set; }

        /// <summary>
        /// 月利率
        /// </summary> 
        public decimal? MonthRate { get; set; }

        /// <summary>
        /// 供应链金融折扣
        /// </summary> 
        public decimal? FinanceDiscount { get; set; }

        /// <summary>
        /// 垫资金额 
        /// </summary> 
        public decimal? AdvanceAmount { get; set; }

        /// <summary>
        /// 含税垫资毛利率
        /// </summary> 
        public decimal? AdvanceTaxAmount { get; set; }

        /// <summary>
        /// 含税垫资毛利率（实体）
        /// </summary> 
        public decimal? AdvanceCreditTaxAmount
        {
            get
            {
                return AdvanceTaxAmount;
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditValue { get; set; }

        /// <summary>
        /// 收付比 
        /// </summary> 
        public decimal? Rate { get; set; }
    }

    /// <summary>
    /// 提前付款垫资 产品明细
    /// </summary>
    public class AdvancePaymentProductDetailOutput
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 提前付款垫资Id
        /// </summary>
        public Guid? AdvancePaymentItemId { get; set; }

        /// <summary>
        /// 业务单据号
        /// </summary> 
        public string? BusinessBillNo { get; set; }

        /// <summary>
        /// 货号
        /// </summary> 
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary> 
        public string? ProductName { get; set; }

        /// <summary>
        /// 数量
        /// </summary> 
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位毛利
        /// </summary> 
        public decimal? Profit { get; set; }

        /// <summary>
        /// 毛利小计
        /// </summary> 
        public decimal? SubTotal { get; set; }

        /// <summary>
        /// 原始成本
        /// </summary> 
        public decimal? OriginalCost { get; set; }

        /// <summary>
        /// 原始售价
        /// </summary> 
        public decimal? OriginalSalePrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 采购明细id
        /// </summary>
        public Guid? PurchaseDetailId { get; set; }
    }

    /// <summary>
    /// 不同状态数量
    /// </summary>
    public class AdvancePaymentTabOutput
    {
        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 临时草稿数量 0
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 待审核 1
        /// </summary>
        public int AuditingCount { get; set; }
        /// <summary>
        /// 拒绝 66
        /// </summary>
        public int Refuse { get; set; }
        /// <summary>
        /// 已完成数量 99
        /// </summary>
        public int CompletedCount { get; set; }
        /// <summary>
        /// 我的审批 5000
        /// </summary>
        public int MyAuditCount { get; set; }
    }

    /// <summary>
    /// item数据
    /// </summary>
    public class AdvancePaymentItemInfo
    {
        /// <summary>
        /// 提前付款名称
        /// </summary>
        [MaxLength(200)]
        public string Name { get; set; }
        /// <summary>
        /// 单号
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }
        /// <summary>
        /// 项目单号 
        /// </summary> 
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 取数开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 取数结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 回款天数
        /// </summary>
        public int? Day { get; set; }

        /// <summary>
        /// 月利率
        /// </summary>
        public decimal? MonthRate { get; set; }
        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }
        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }
        /// <summary>
        /// 付供应商货款日期
        /// </summary>
        public DateTime? PaySupplierGoodsDate { get; set; }
        /// <summary>
        /// 垫资应收金额合计 
        /// </summary>
        public decimal? AdvanceCreditAmount { get; set; }
        /// <summary>
        /// 含税垫资毛利合计
        /// </summary>
        public decimal? AdvanceCreditTaxAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public AdvancePaymentStatusEnum Status { get; set; }

        /// <summary>
        /// 流程请求Id
        /// </summary>
        public string? OARequestId { get; set; }

        /// <summary>
        /// 附件id
        /// </summary>
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 提前付款垫资 应付明细
        /// </summary>
        public List<AdvancePaymentDebtDetailOutput>? AdvancePaymentDebtDetails { get; set; } = new List<AdvancePaymentDebtDetailOutput>();
        /// <summary>
        /// 提前付款垫资 产品明细
        /// </summary>
        public List<AdvancePaymentProductDetailOutput>? AdvancePaymentProductDetails { get; set; } = new List<AdvancePaymentProductDetailOutput>();
    }

    /// <summary>
    /// 详情数据
    /// </summary>
    public class AdvancePaymentDebtDetailInfo
    {
        /// <summary>
        /// 提前垫资单id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 提前付款垫资 应付明细
        /// </summary>
        public List<AdvancePaymentDebtDetailOutput>? AdvancePaymentDebtDetails { get; set; } = new List<AdvancePaymentDebtDetailOutput>();
        /// <summary>
        /// 提前付款垫资 产品明细
        /// </summary>
        public List<AdvancePaymentProductDetailOutput>? AdvancePaymentProductDetails { get; set; } = new List<AdvancePaymentProductDetailOutput>();

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
}
