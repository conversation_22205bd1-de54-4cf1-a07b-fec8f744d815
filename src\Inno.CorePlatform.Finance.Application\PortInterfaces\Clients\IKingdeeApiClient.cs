using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.DebtDetail;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 金蝶接口服务
    /// </summary>
    public interface IKingdeeApiClient
    {
        public Task Test();
        /// <summary>
        /// 获取金蝶AccessToken
        /// </summary>
        /// <returns></returns>
        Task<string> GetAccesstokenAsync();

        /// <summary>
        /// 推送应收到金蝶
        /// </summary>
        /// <param name="kingdeeCredits"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushCreditsToKingdee(List<KingdeeCredit> kingdeeCredits, string classify, string preRequestBody);

        /// <summary>
        /// 推送应付到金蝶
        /// </summary>
        /// <param name="kingdeeDebts"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushDebtsToKingdee(List<KingdeeDebt> kingdeeDebts, string classify, string preRequestBody);

        /// <summary>
        /// 推送付款申请到金蝶
        /// </summary>
        /// <param name="kingdeePayApply">付款申请数据（包含OA请求ID）</param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushPaymentApplyToKingdee(KingdeePayApplyDto kingdeePayApply);

        /// <summary>
        /// 批量付款执行，推送应付付款计划到金蝶系统
        /// </summary>
        /// <param name="lstDetail"></param>
        /// <returns></returns>
        Task<bool> PushDebtDetailAsync(List<DebtDetailInput> lstDetail);
        /// <summary>
        /// 更改收入确认标识到金蝶系统
        /// </summary>
        /// <param name="incomeIsConfirm"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushIncomeIsCofirm(KingdeeIncomeIsConfirm incomeIsConfirm);

        /// <summary>
        /// 查询收款单
        /// </summary>
        /// <param name="incomeIsConfirm"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<RecognizeReceiveOutput>>> PullReceiveBills(RecognizeReceiveInput kingdeeReceiveInput);

        Task<BaseResponseData<int>> ModifyCollectionType(List<modifyCollectionTypeInput> input);

        Task<BaseResponseData<int>> PushBathSaveReceiveBills(RecognizeReceiveBatchSaveInput Input);

        /// <summary>
        /// 保存开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <param name="flow">N=保存后悔删除，Y=保存后不会删除</param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushCustomizeInvoice(List<PushCustomizeInvoiceSaveInput> input, string flow = "Y");

        /// <summary>
        /// 保存付款调整单
        /// </summary>
        /// <param name="Inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SavePaymentAdjustment(List<SavePaymentAdjustmentInput> Inputs);
        /// <summary>
        /// 撤销发票
        /// </summary>
        /// <param name="Input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<int>> ReturnCustomizeInvoice(ReturnCustomizeInvoiceSaveInput Input);
        /// <summary>
        /// 暂存出库单保存
        /// </summary>
        /// <param name="inputs"></param>
        /// <param name="classify"></param>
        /// <param name="preRequestBody"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushStoreOutToKingdeeWithoutFinance(List<HoldStockRemovalInput> inputs, string classify, string preRequestBody);

        /// <summary>
        /// 暂存入库单保存
        /// </summary>
        /// <param name="inputs"></param>
        /// <param name="classify"></param>
        /// <param name="preRequestBody"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushStoreInToKingdeeWithoutFinance(List<HoldStorageInput> inputs, string classify, string preRequestBody);

        /// <summary>
        /// 获取回执单下载地址
        /// </summary>
        /// <param name="input"></param>
        /// <param name="type">单据类型(付款/退款:payBill,收款:recBill)</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<List<ReceiptNumberModelOutput>>> SelectTheReceiptNumber(List<ReceiptNumberModelInput> input,string type);

        /// <summary>
        /// 保存认款单
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushBatchSaveAcceptances(List<BatchSaveAcceptanceInput> inputs);

        /// <summary>
        /// 应付付款结算
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PaymentSettlement(List<PaymentSettlementInput> inputs);


        Task<BaseResponseData<int>> RollBackBill(RollBackBillDto rollBackBill);

        /// <summary>
        /// 金蝶财务数据盘点生成
        /// </summary>
        /// <param name="companyNameCode"></param>
        /// <param name="billDate"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CreateKingdeeInventory(string companyNameCode, DateTime billDate, string accountPeriod);

        /// <summary>
        /// 撤销销售认款
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelSaleReceive(string itemCode);

        /// <summary>
        /// 自动审核开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> AuditBillingApplication(AuditBillingApplicationInput input);

        /// <summary>
        /// 进项发票提交到金蝶
        /// </summary>
        /// <param name="inputBillSubmitDto"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SubInputBillToKingdee(KingdeeInputBillSubmitDto inputBillSubmitDto);

        /// <summary>
        /// 汇率查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<ExchangeRateQueryOutput>> ExchangeRateQuery(ExchangeRateQueryInput input);

        /// <summary>
        /// 信用证-保存业务申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SaveCoreToBizapply(saveCoreToBizapplyInput input);

        /// <summary>
        /// 信用证列表查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<getLetterCreditOutput>>> GetLetterCredit(getLetterCreditInput input);

        /// <summary>
        /// 发票附件查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<QueryInvoiceAttachmentOutput>>> QueryInvoiceAttachment(QueryInvoiceAttachmentInput input);

        /// <summary>
        /// 查询退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<QueryPaymentRefundOutput>> QueryPaymentRefund(QueryPaymentRefundInput input);

        /// <summary>
        /// 保存或更新退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SaveOrUpdateRefundOutput>> SaveOrUpdateRefund(SaveOrUpdateRefundInput input);

        /// <summary>
        /// 删除退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RefundDelete(RefundDeleteInput input);

        /// <summary>
        /// 保存或修改行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> SaveLineNameLineNumber(SaveLineNameLineNumberInput input);

        /// <summary>
        /// 删除行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DeleteLineNameLineNumber(DeleteLineNameLineNumberInput input);

        /// <summary>
        /// 发票识别查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> RecognitionCheck(RecognitionCheckInput input);

        /// <summary>
        /// 发票查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> InvoiceCheck(InvoiceCheckInput input);

        /// <summary>
        /// 查询结算方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SettlementtypeOutput>> GetSettlementtype(SettlementtypeInput input);

        /// <summary>
        /// 收款调整单保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SavePaymentModification(SavePaymentModificationInput input);

        /// <summary>
        /// 数电票发票单张查询
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<SimDataOutput>> GetDigitalInvoiceFile(SimData input);

        /// <summary>
        /// 红字确认单编号查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<QueryRedConfirmationFormNumberOutput>> QueryRedConfirmationFormList(QueryRedConfirmationFormNumberInput input);

        /// <summary>
        /// 红字确认单编号下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<RedConfirmationFormNumberListReturnData>> DownloadRedConfirmationFormList(DownloadRedConfirmationFormNumberInput input);

        /// <summary>
        /// 红字确认单生成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData> Generate(GenerateRedConfirmationFormNumberInput input);

        /// <summary>
        /// 保存返利计提
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> RebateProvisionSave(List<RebateProvisionSaveInput> input);

        /// <summary>
        /// 撤销认款
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceiveInvoice(string code);

        /// <summary>
        /// 撤销认款（包含明细）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceive(List<QuashAcceptancesRequestVo> input);

        /// <summary>
        /// 核心平台保存采购转固单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> BatchSavePurchaseCoreBill(List<PurchaseCoreBillInput> input);


        /// <summary>
        /// 核心平台删除采购转固单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> BatchDeletePurchaseCoreBill(DeletePurchaseCoreBillInput input);

        Task<BaseResponseData<List<CheckDataWithKingdeeOutputDto>>> QueryKingdeeTempStoreOutData(CheckDataWithKingdeeInputDtoForKingdee input);
        Task<BaseResponseData<int>> PayablesOffsetReceivables(List<PaymentSettlementInput> inputs);

        /// <summary>
        /// 多发票指定应付
        /// </summary>
        /// <param name="input">包含finentry数组、invoiceno和coreBillNo的请求对象</param>
        /// <returns>操作结果</returns>
        Task<BaseResponseData<int>> ManyInvoiceSpecifyApFin(ManyInvoiceSpecifyApFinInput input);

        /// <summary>
        /// 推送发票更改应收消息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData> ChangeInvoiceCreditRelationship(ChangeRelationshipKingdeeInput input);

        /// <summary>
        /// 发票查询子表初始化
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> InvoiceChildInit(List<InvoiceChildInitRequestVo> list);

        /// <summary>
        /// 打包发票发送至邮箱
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SendEmailInvoices(SendMailInvoicesInput input);
        Task<BaseResponseData<int>> BatchSaveBarterDisposeBill(List<BatchSaveBarterDisposeBillInput> input);
        Task<BaseResponseData<int>> RevcfmbillBatchSave(List<RevcfmbillBatchSaveInput> input);
        Task<BaseResponseData<int>> AssociatedInvoicing(AssociatedInvoicingInput input);
        Task<BaseResponseData<int>> RevcfmbillBatchisConfirm(List<RevcfmbillBatchiscofirmInput> inputs);
        Task<BaseResponseData<int>> InputBillUnassign(InputBillUnassignInput input);

        public Task<KindeeCheckBillResData> GetKingdeeCheckData(string billCode, string org_number);

        public Task<KindeeCheckBillResData> GetKingdeeTempStoreOutCheckData(string billCode, string org_number);

        public Task<KindeeCheckBillResData> GetKingdeeCreditCheckData(string billCode, string org_number);

        Task<KindeeCheckBillResData> GetKingdeeTempStoreInCheckData(string billCode, string org_number);
        Task<BaseResponseData<TaxClassCodeOutput>> TaxClassCode(TaxClassCodeInput input);
        Task<BaseResponseData<QueryBeBankOutput>> QueryBeBank(TaxClassCodeInput input);
        /// <summary>
        /// 应付退款结算
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DebtRefundSettle(List<DebtRefundSettleInput> inputs);

        /// <summary>
        /// 库存更换项目-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> BatchSaveBarterDisposeBill(List<ApBarterDisposeBillModel> inputs);

        /// <summary>
        /// 库存更换项目-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByDept(List<ApBarterDisposeBillModel> inputs);

        /// <summary>
        /// 暂存更换 - 财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByStaging(List<ApBarterDisposeBillModel> inputs);

        /// <summary>
        /// 获取金蝶退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<RefundResponseModelOutput> QueryKingdeePaymentRefund(QueryRefundDataInput input);

        /// <summary>
        /// 收款冲付款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<object> ReceiveAbtPayment(ReceiveAbtPaymentInput input);

        /// <summary>
        /// 应付冲销反结算
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> ReverseSettlement(ReverseSettlementInput input);
        /// <summary>
        /// 更换核算部门应收推送金蝶
        /// </summary>
        /// <param name="kingdeeCredits"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<int>> DeptPushCreditsToKingdee(List<KingdeeCredit> kingdeeCredits);

        /// <summary>
        /// 保存财务应付单关联单
        /// </summary>
        /// <param name="kingdeeAdjusts"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> PushDebtsAdjustToKingdee(List<KingdeeAdjustDebt> kingdeeAdjusts);
        /// <summary>
        /// 保存损失确认负数应付单
        /// </summary>
        /// <param name="saveLossRecognitionDebtInput"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SaveLossRecognitionDebt(List<SaveLossRecognitionDebtInput> saveLossRecognitionDebtInput);

        /// <summary>
        /// 保存损失确认负数应收单
        /// </summary>
        /// <param name="saveLossRecognitionCreditInput"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<int>> SaveLossRecognitionCredit(List<SaveLossRecognitionCreditInput> saveLossRecognitionCreditInput);

        /// <summary>
        /// 回滚损失确认负数应收单
        /// </summary>
        /// <param name="rollBackLossRecognitionCreditInput"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackLossRecognitionCredit(List<RollBackLossRecognitionCreditInput> rollBackLossRecognitionCreditInput);

        /// <summary>
        /// 回滚损失确认负数应付单
        /// </summary>
        /// <param name="rollBackLossRecognitionDebtInput"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RollBackLossRecognitionDebt(List<RollBackLossRecognitionDebtInput> rollBackLossRecognitionDebtInput);

        /// <summary>
        /// 查询在途结算数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<NoPostBackSettleResponseVo>>> QueryNoPostbackSettleBill(NoPostBackSettleRequestVo input);

        /// <summary>
        /// 查询合同台账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<QueryContractBillByKingdeeOutput>> QueryContractBill(QueryContractBillByKingdeeInput input);

        /// <summary>
        /// 保存中标服务费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SaveOrUpdateRefundOutput>> SavePrepayBill(PrepayBillInput input);
        Task<BaseResponseData<int>> saveRedBillingApplication(List<PrepayBillInput.saveRedBillingApplicationInput> input, string flow = "Y");
    }
}
