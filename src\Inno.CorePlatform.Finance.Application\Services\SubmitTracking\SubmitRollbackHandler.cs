using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.Helpers;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitTracking
{
    /// <summary>
    /// 提交回滚处理器
    /// </summary>
    public class SubmitRollbackHandler : ISubmitRollbackHandler
    {
        private readonly ILogger<SubmitRollbackHandler> _logger;
        private readonly IManyInventoryApiClient _manyInventoryApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly FinanceDbContext _dbContext;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IInterfaceInvocationService _interfaceInvocationService;
        private readonly MergeInputBillHelper _mergeInputBillHelper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="manyInventoryApiClient">多对一勾稽库存能力中心接口</param>
        /// <param name="kingdeeApiClient">金蝶API客户端</param>
        /// <param name="dbContext">数据库上下文</param>
        /// <param name="bDSApiClient">BDS API客户端</param>
        /// <param name="interfaceInvocationService">接口调用服务</param>
        /// <param name="mergeInputBillHelper">合并进项票辅助类</param>
        public SubmitRollbackHandler(
            ILogger<SubmitRollbackHandler> logger,
            IManyInventoryApiClient manyInventoryApiClient,
            IKingdeeApiClient kingdeeApiClient,
            FinanceDbContext dbContext,
            IBDSApiClient bDSApiClient,
            IInterfaceInvocationService interfaceInvocationService,
            MergeInputBillHelper mergeInputBillHelper)
        {
            _logger = logger;
            _manyInventoryApiClient = manyInventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _dbContext = dbContext;
            _bDSApiClient = bDSApiClient;
            _interfaceInvocationService = interfaceInvocationService;
            _mergeInputBillHelper = mergeInputBillHelper;
        }



        /// <summary>
        /// 回滚金蝶提交
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackKingdeeSubmitAsync(Guid mergeInputBillId, string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackKingdeeSubmitAsync - 开始回滚金蝶提交, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBillId, mergeInvoiceNumber);

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _dbContext.MergeInputBills
                    .FirstOrDefaultAsync(m => m.Id == mergeInputBillId);

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("RollbackKingdeeSubmitAsync - 未找到合并进项发票, MergeInputBillId: {MergeInputBillId}",
                        mergeInputBillId);
                    return false;
                }

                // 使用辅助类获取原始发票号列表
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeIdAsync(mergeInputBillId);


                // 获取系统月度
                var sysMonth = DateTime.Now.ToString("yyyy-MM-dd");
                try
                {
                    // 尝试获取系统月度
                    if (mergeInputBill.CompanyId != Guid.Empty)
                    {
                        var companyId = mergeInputBill.CompanyId.ToString();
                        // 调用BDS API获取系统月度
                        sysMonth = await _bDSApiClient.GetSystemMonth(companyId);
                        sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM-dd");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning("RollbackKingdeeSubmitAsync - 获取系统月度失败, 使用当前日期, 错误: {ErrorMessage}", ex.Message);
                }

                // 构建撤销请求
                var revokeRequest = new InputBillUnassignInput
                {
                    // 使用原始发票号列表，保持与提交时相同的顺序
                    invoiceno = originalInvoiceNumbersStr,
                    user = "System", // 使用系统用户名
                    associatedDate = sysMonth // 使用系统月度
                };

                _logger.LogInformation("RollbackKingdeeSubmitAsync - 调用金蝶撤销多发票指定应付接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用金蝶接口，传递的发票号与提交时保持一致的顺序
                var result = await _kingdeeApiClient.InputBillUnassign(revokeRequest);

                // 检查结果
                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackKingdeeSubmitAsync - 调用金蝶撤销多发票指定应付接口失败, 错误: {ErrorMessage}", errorMessage);

                    // 如果是已存在的错误，视为成功
                    if (result.Message != null && result.Message.Contains("已存在"))
                    {
                        _logger.LogWarning("RollbackKingdeeSubmitAsync - 数据已存在, 视为成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                            mergeInvoiceNumber);
                        return true;
                    }

                    return false;
                }

                _logger.LogInformation("RollbackKingdeeSubmitAsync - 调用金蝶撤销多发票指定应付接口成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackKingdeeSubmitAsync - 回滚金蝶提交失败, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBillId, mergeInvoiceNumber, ex.Message);
                return false;
            }
        }



        /// <summary>
        /// 回滚经销购货入库
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackDistributionPurchaseAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackDistributionPurchaseAsync - 开始回滚经销购货入库, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackDistributionPurchaseAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 创建 RevokeInventoryStoreInDetail 对象，支持多个发票号
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                // 调用库存能力中心接口撤销入库单明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreInDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackDistributionPurchaseAsync - 回滚经销购货入库失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackDistributionPurchaseAsync - 回滚经销购货入库失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackDistributionPurchaseAsync - 回滚经销购货入库成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInvoiceNumber);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackDistributionPurchaseAsync - 回滚经销购货入库失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚寄售转购货
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackConsignmentToPurchaseAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackConsignmentToPurchaseAsync - 开始回滚寄售转购货, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackConsignmentToPurchaseAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 直接调用InterfaceInvocationService中的InvokeRevokeConsignToPurchaseDetail方法
                // 该方法会根据合并勾稽单号查找提交明细，然后将对应的提交明细拼接好参数调用接口
                var result = await _interfaceInvocationService.InvokeRevokeConsignToPurchaseDetail(mergeInvoiceNumber);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackConsignmentToPurchaseAsync - 撤销寄售转购货失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackConsignmentToPurchaseAsync - 撤销寄售转购货失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackConsignmentToPurchaseAsync - 撤销寄售转购货成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackConsignmentToPurchaseAsync - 回滚寄售转购货失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚服务费采购
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackServiceFeeProcurementAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackServiceFeeProcurementAsync - 开始回滚服务费采购, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackServiceFeeProcurementAsync - 合并发票号为空，跳过撤销");
                    return true;
                }

                // 直接调用InterfaceInvocationService中的InvokeRevokeServiceFeeProcurementDetail方法
                // 该方法会直接更新Debt表中服务费应付的invoiceAmount字段
                var result = await _interfaceInvocationService.InvokeRevokeServiceFeeProcurementDetail(mergeInvoiceNumber);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackServiceFeeProcurementAsync - 撤销服务费采购失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackServiceFeeProcurementAsync - 撤销服务费采购失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackServiceFeeProcurementAsync - 撤销服务费采购成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackServiceFeeProcurementAsync - 回滚服务费采购失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚经销调出
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackDistributionTransferAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackDistributionTransferAsync - 开始回滚经销调出, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackDistributionTransferAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 创建 RevokeInventoryStoreInDetail 对象，支持多个发票号
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                _logger.LogInformation("RollbackDistributionTransferAsync - 调用库存能力中心撤销经销调出明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用库存能力中心接口撤销经销调出明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreOutDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackDistributionTransferAsync - 调用库存能力中心撤销经销调出明细接口失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackDistributionTransferAsync - 调用库存能力中心撤销经销调出明细接口失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackDistributionTransferAsync - 调用库存能力中心撤销经销调出明细接口成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackDistributionTransferAsync - 回滚经销调出失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚购货修订
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackPurchaseRevisionAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackPurchaseRevisionAsync - 开始回滚购货修订, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackPurchaseRevisionAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 直接调用InterfaceInvocationService中的InvokeRevokePurchaseRevisionDetail方法
                // 该方法会根据合并勾稽单号查找提交明细，然后将对应的提交明细拼接好参数调用接口
                var result = await _interfaceInvocationService.InvokeRevokePurchaseRevisionDetail(mergeInvoiceNumber);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackPurchaseRevisionAsync - 撤销购货修订失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackPurchaseRevisionAsync - 撤销购货修订失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackPurchaseRevisionAsync - 撤销购货修订成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackPurchaseRevisionAsync - 回滚购货修订失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚换货转退货
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackExchangeToReturnAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackExchangeToReturnAsync - 开始回滚换货转退货, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackExchangeToReturnAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 创建 RevokeInventoryStoreInDetail 对象，支持多个发票号
                var originalInvoiceNumbersStr = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeNumberAsync(mergeInvoiceNumber);
                var revokeRequest = new RevokeInventoryStoreInDetail
                {
                    invoiceNumber = [originalInvoiceNumbersStr]
                };

                _logger.LogInformation("RollbackExchangeToReturnAsync - 调用库存能力中心撤销换货转退货明细接口, 请求参数: {RequestParams}",
                    JsonConvert.SerializeObject(revokeRequest));

                // 调用库存能力中心接口撤销换货转退货明细
                var result = await _manyInventoryApiClient.RevokeManyToManyStoreExchangeBackDetail(revokeRequest);

                // 检查结果
                if (result == null)
                {
                    _logger.LogError("RollbackExchangeToReturnAsync - 调用库存能力中心撤销换货转退货明细接口失败, 错误: 返回结果为空");
                    return false;
                }

                if (result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result.Message ?? "未知错误";
                    _logger.LogError("RollbackExchangeToReturnAsync - 调用库存能力中心撤销换货转退货明细接口失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackExchangeToReturnAsync - 调用库存能力中心撤销换货转退货明细接口成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackExchangeToReturnAsync - 回滚换货转退货失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 回滚损失确认
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackLossRecognitionAsync(string mergeInvoiceNumber)
        {
            _logger.LogInformation("RollbackLossRecognitionAsync - 开始回滚损失确认, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInvoiceNumber);

            try
            {
                // 处理空合并发票号
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("RollbackLossRecognitionAsync - 合并发票号为空，跳过回滚");
                    return true;
                }

                // 直接调用InterfaceInvocationService中的InvokeRevokeLossRecognitionDetail方法
                // 损失确认是财务内部处理，不需要调用外部接口
                var result = await _interfaceInvocationService.InvokeRevokeLossRecognitionDetail(mergeInvoiceNumber);

                // 检查结果
                if (result == null || result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result?.Message ?? "返回结果为空";
                    _logger.LogError("RollbackLossRecognitionAsync - 撤销损失确认失败, 错误: {ErrorMessage}", errorMessage);
                    return false;
                }

                _logger.LogInformation("RollbackLossRecognitionAsync - 撤销损失确认成功");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackLossRecognitionAsync - 回滚损失确认失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                return false;
            }
        }
    }
}
