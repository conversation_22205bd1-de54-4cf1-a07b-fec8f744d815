﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Equipments;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IEquipmentsApiClient
    {
        Task<BaseResponseData<bool>> EquipIsAllFinishByPurchaseOrderCodeAsync(PurchaseOrderCodeInput input);
        Task<BaseResponseData<string>> UpdateEquipmentForPut(List<EquipmentInput> input);
    }
}
