﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class FinanceRollbackAppService : IFinanceRollbackAppService
    {
        private readonly ICreditRepository _creditRepository;
        private readonly IBaseAllQueryService<DebtDetailPo> _debtDetailQueryRep;
        private readonly IDebtRepository _debtRepository;
        private readonly IBaseAllQueryService<DebtPo> _debtQueryRep;
        private readonly IPurchaseOrderAppService _purchaseOrderAppService;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IUnitOfWork _unitOfWork;
        public FinanceRollbackAppService(ICreditRepository creditRepository,
            IBaseAllQueryService<DebtDetailPo> debtDetailQueryRep,
            IDebtRepository debtRepository,
            IPurchaseOrderAppService purchaseOrderAppService,
            IKingdeeApiClient kingdeeApiClient,
            IBaseAllQueryService<DebtPo> debtQueryRep,
            IUnitOfWork unitOfWork
            )
        {
            this._creditRepository = creditRepository;
            this._debtDetailQueryRep = debtDetailQueryRep;
            this._debtRepository = debtRepository;
            this._debtQueryRep = debtQueryRep;
            this._purchaseOrderAppService = purchaseOrderAppService;
            this._kingdeeApiClient = kingdeeApiClient;
            this._unitOfWork = unitOfWork;
        }

        public async Task<bool> RepaireDebtDiff(List<Guid> ids)
        {
            return await _debtRepository.RepaireDebtDiff(ids);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> RollBackCredit(string code)
        {
            var credits = await _creditRepository.GetCreditsByRelateCode(code);
            if (credits == null || !credits.Any())
            {
                return BaseResponseData<int>.Success("无需回滚");
            }
            var ids = credits.Select(x => x.Id).ToList();
            var query = _debtDetailQueryRep.GetIQueryable(p => p.CreditId.HasValue && ids.Contains(p.CreditId.Value));
            if ((await query.AnyAsync()))
            {
                return BaseResponseData<int>.Failed(500, "已拆分应付明细，无法回滚");
            }
            //回滚金蝶应收

            var creditCodes = credits.Select(p => p.BillCode);
            var data = new RollBackBillDto()
            {
                billType = "B",
                billnos = creditCodes.Select(p => new RollBackBillNo { billno = p }).ToList()
            };
            var kingdeeRoll = await _kingdeeApiClient.RollBackBill(data);
            if (kingdeeRoll.Code == CodeStatusEnum.Success)
            {
                await _creditRepository.DeleteCredits(ids);
                await _unitOfWork.CommitAsync();
                return BaseResponseData<int>.Success("操作成功");
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "回滚金蝶出错：" + kingdeeRoll.Message);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<BaseResponseData<int>> RollBackDebt(string code)
        {
            var query = _debtQueryRep.GetIQueryable(p => p.RelateCode == code);
            var debts = await query.ToListAsync();
            var debtIds = debts.Select(p => p.Id).ToList();
            var codes = debts.Select(p => p.BillCode).ToList();
            var data = new RollBackBillDto()
            {
                billType = "A",
                billnos = codes.Select(p => new RollBackBillNo { billno = p }).ToList()
            };
            var kingdeeRoll = await _kingdeeApiClient.RollBackBill(data);
            if (kingdeeRoll.Code == CodeStatusEnum.Success)
            {
                var res = await _debtRepository.DeleteByIds(debtIds); 
                await _unitOfWork.CommitAsync();
                return new BaseResponseData<int>()
                {
                    Data = res ? 1 : 0,
                    Code = CodeStatusEnum.Success
                };
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "回滚金蝶出错：" + kingdeeRoll.Message);
            }
        }

        /// <summary>
        /// 回归采购
        /// </summary>
        /// <param name="purchaseCode"></param>
        /// <returns></returns>
        public Task<BaseResponseData<int>> RollBackPurchase(string purchaseCode)
        {
            return _purchaseOrderAppService.RollBack(purchaseCode);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RollBackStoreIn(string code)
        {
            var data = new RollBackBillDto()
            {
                billType = "C",
                billnos = new List<RollBackBillNo>() { new RollBackBillNo() { billno = code } }
            };
            return await _kingdeeApiClient.RollBackBill(data);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RollBackStoreOut(string code)
        {
            var data = new RollBackBillDto()
            {
                billType = "D",
                billnos = new List<RollBackBillNo>() { new RollBackBillNo() { billno = code } }
            };
            return await _kingdeeApiClient.RollBackBill(data);
        }
    }
}
