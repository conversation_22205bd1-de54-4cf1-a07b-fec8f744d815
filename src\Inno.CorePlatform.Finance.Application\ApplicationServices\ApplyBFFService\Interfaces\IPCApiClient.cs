﻿using Inno.CorePlatform.Finance.Application.QueryServices;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 
    /// </summary>
    public interface IPCApiClient
    {
        /// <summary>
        /// 获取数据策略
        /// </summary>
        /// <param name="inputParam"></param>
        /// <returns></returns>
        Task<StrategyQueryOutput> GetStrategyAsync(StrategyQueryInput inputParam);
    }
}
