﻿using Inno.CorePlatform.Finance.Application.Common;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    public class InventoryStoreInOutput : BusinessDepartDTO
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid? productNameId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? productId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? projectId { get; set; }
        public string? projectCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? projectName { get; set; }
        /// <summary>
        ///   公司ID
        /// </summary>
        public Guid? companyId { get; set; }
        /// <summary>
        /// 上海建发致为医疗器械有限责任公司     公司名称
        /// </summary>
        public string? companyName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? businessUnitId { get; set; }

        public Guid? producerId { get; set; }
        public string? producerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? businessUnitName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 安波澜（北京）医疗设备有限公司
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? customerId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? customerName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? checkingReportCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public long? checkingDate { get; set; }
        /// <summary>
        ///    客户id
        /// </summary>
        public Guid? shipperId { get; set; }
        /// <summary>
        /// 安波澜（北京）医疗设备有限公司    发货者名称
        /// </summary>
        public string? shipperName { get; set; }

        public string? spdInvoiceCode { get; set; }
        /// <summary>
        /// 收货者ID
        /// </summary>
        public Guid? consigneeId { get; set; }
        /// <summary>
        ///     收货者名称
        /// </summary>
        public string? consigneeName { get; set; }
        /// <summary>
        ///       业务单元ID
        /// </summary>
        public Guid? serviceId { get; set; }
        /// <summary>
        ///      业务单元名称
        /// </summary>
        public string? serviceName { get; set; }
        /// <summary>
        /// 易动力传媒有限公司仓库  仓库名称
        /// </summary>
        public string? storeHouseName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeInApplyCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? receivingCode { get; set; }
        /// <summary>
        ///   入库单ID
        /// </summary>
        public Guid? storeInId { get; set; }
        /// <summary>
        ///        入库单号
        /// </summary>
        public string? storeInCode { get; set; }
        /// <summary>
        /// 换货转退货单号
        /// </summary>
        public string? billCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? receivingId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? checkingId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid? applyId { get; set; }
        /// <summary>
        ///       商品分类：0-医疗器械，1-非医疗器械, 99-不限
        /// </summary>
        public int? goodsCategory { get; set; }
        /// <summary>
        /// 关联单号  autoType = 4，5 表示直放订单，relateCode表示销售单号
        /// </summary>
        public string? relateCode { get; set; }
        /// <summary>
        ///       入库日期
        /// </summary>
        public long storeInDate { get; set; }
        /// <summary>
        /// 换货转退货日期
        /// </summary>
        public long billDate { get; set; }
        /// <summary>
        ///       入库状态
        /// </summary>
        public int? storeInStatus { get; set; }
        /// <summary>
        ///        质量状态：0合格 1不合格
        /// </summary>
        public int? qltyStatus { get; set; }
        /// <summary>
        ///         入库类型
        /// </summary>
        public int? storeInType { get; set; }
        /// <summary>
        ///        入库人
        /// </summary>
        public string? storeInBy { get; set; }
        /// <summary>
        ///   仓库唯一标识
        /// </summary>
        public Guid storeHouseId { get; set; }
        /// <summary>
        ///      说明
        /// </summary>
        public string? remark { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<StoreInDetailOutput>? storeInDetails { get; set; } = new List<StoreInDetailOutput>();
        /// <summary>
        /// 
        /// </summary>
        public string? storeInStatusDesc { get; set; }
        /// <summary>
        ///  入库 入库类型描述
        /// </summary>
        public string? storeInTypeDesc { get; set; }

        /// <summary>
        ///  文件附件
        /// </summary>
        public string? attachmentFilelds { get; set; }


        /// <summary>
        /// 明细
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();

        /// <summary>
        /// "事业部Id"
        /// </summary>
        public string? businessDeptId { get; set; }
        /// <summary>
        /// "事业部全路径Id"
        /// </summary>
        public string? businessDeptFullPath { get; set; }
        /// <summary>
        /// "事业部全名路径"
        /// </summary>
        public string? businessDeptFullName { get; set; }

        /// <summary>
        /// 入库单明细ID
        /// </summary>
        public string? storeInDetailId { get; set; }

        /// <summary>
        /// 预入库单明细ID
        /// </summary>
        public string? storeInPreDetailId { get; set; }

        /// <summary>
        /// 申请单明细ID
        /// </summary>
        public string? applyDetailId { get; set; }

        /// <summary>
        /// 申请单号
        /// </summary>
        public string? applyCode { get; set; }

        /// <summary>
        /// 箱子码
        /// </summary>
        public string? boxCode { get; set; }

        /// <summary>
        /// 套包码
        /// </summary>
        public string packageCode { get; set; }

        /// <summary>
        /// 入库数量  入库数量必须大于0
        /// </summary>
        public int? quantity { get; set; }

        public bool? autoTagging { get; set; }
        public long? validDate { get; set; }
        public string? productBarcode { get; set; }
        public string? lotNo { get; set; }
        public string? sn { get; set; }
        public long? produceDate { get; set; }
        public string? storeLocationId { get; set; }
        public string? storeLocationNo { get; set; }
        public string? certFileId { get; set; }
        public long? certLimitDate { get; set; }
        public decimal? unitCost { get; set; }
        public string? traceCode { get; set; }
        public decimal? taxRate { get; set; }
        public int? mark { get; set; }
        public decimal? invoiceQuantity { get; set; }
        public string? purchaseOrderId { get; set; }
        public string? purchaseOrderCode { get; set; }
        public string? purchaseOrderDetailId { get; set; }

        public string? registrationId { get; set; }
        public string? registrationNo { get; set; }

        public string? registerName { get; set; }

        public string? markProducerName { get; set; }
        public decimal? price { get; set; }

        public decimal? salesTaxRate { get; set; }

        public string? specification { get; set; }
        public string? modelNo { get; set; }
        public string? model { get; set; }

        public string? bdsPackUnit { get; set; }

        public double? noUnitCost { get; set; }
        public double? noUnitCostTotal { get; set; }
        public double? unitCostTotal { get; set; }

        public int rebate { get; set; }

        public int? relateCodeType { get; set; }
        /// <summary>
        /// 自动类型 autoType = 4，5 表示直放订单4，5 表示直放订单
        /// </summary>
        public int? autoType { get; set; }

    }

    public class StoreInDetailOutput
    {

        /// <summary>
        /// 入库单详情ID
        /// </summary>
        public string storeInDetailId { get; set; }

        public Guid? productNameId { get; set; }

        public string? productName { get; set; }
        public string? specification { get; set; }
        public int quantity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool? autoTagging { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double? validDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? productBarcode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? lotNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? sn { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double? produceDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? storeLocationNo { get; set; }

        public string? productNo { get; set; }

        public Guid productId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? registrationNo { get; set; }
        /// <summary>
        ///  追溯ID
        /// </summary>
        public string? traceCode { get; set; }
        /// <summary>
        ///  含税成本
        /// </summary>
        public decimal? unitCost { get; set; }
        /// <summary>
        ///     税率
        /// </summary>
        public decimal? taxRate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? mark { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? agentName { get; set; }
        public Guid? producerId { get; set; }
        public string? producerName { get; set; }
        public Guid? businessUnitId { get; set; }
        public string? businessUnitName { get; set; }

        public decimal? price { get; set; }

        public decimal? salesTaxRate { get; set; }

        /// <summary>
        /// 已入票数量
        /// </summary>
        public decimal? invoiceQuantity { get; set; }

        public Guid? purchaseOrderId { get; set; }

        public string? purchaseorderCode { get; set; }
        public Guid? projectId { get; set; }

        public string? producerOrderNo { get; set; }

        public decimal? standardUnitCost { get; set; }
        /// <summary>
        /// 人民币金额
        /// </summary>
        public decimal? rmbAmount { get; set; }
        /// <summary>
        /// 币种数据字典Code
        /// </summary>
        public string? coinCode { get; set; }
        /// <summary>
        /// 币种代码
        /// </summary>
        public string? coinAttribute { get; set; }
        /// <summary>
        /// 币种名称
        /// </summary>
        public string? coinName { get; set; }
        /// <summary>
        /// 原币单价
        /// </summary>
        public decimal? originCost { get; set; }
        /// <summary>
        /// 关税
        /// </summary>
        public decimal? tariffAmount { get; set; }
        /// <summary>
        /// 进口增值税金额
        /// </summary>
        public decimal? importAddAmount { get; set; }
        public decimal? originalPrice { get; set; }
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? saleCode { get; set; }
        /// <summary>
        /// 销售明细Id
        /// </summary>
        public Guid? saleDetailId { get; set; }
        /// <summary>
        /// 销售退回对应的是， 暂存核销明细id
        /// </summary>
        public Guid? relateId { get; set; }
    }

    public class LotInfo
    {

        public string? productNo { get; set; }
        public string? productName { get; set; }
        public Guid? productNameId { get; set; }
        /// <summary>
        ///    条码
        /// </summary>
        public string? barcode { get; set; }
        /// <summary>
        /// 经销出库Id
        /// </summary>
        public string? id { get; set; }
        /// <summary>
        /// 入库单明细ID
        /// </summary>
        public string? storeInDetailId { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public decimal? quantity { get; set; } = 0;

        /// <summary>
        ///  有效期
        /// </summary>
        public double? validDate { get; set; }
        /// <summary>
        ///  批号
        /// </summary>
        public string? lotNo { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        public string? sn { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        public long? produceDate { get; set; }
        /// <summary>
        /// 生产日期Str
        /// </summary>
        public string produceDateStr
        {
            get
            {
                var ret = string.Empty;
                if (produceDate.HasValue)
                {
                    return DateTimeHelper.LongToDateTime(produceDate.Value).ToString("yyyy-MM-dd");

                }
                return ret;
            }
        }
        /// <summary>
        ///  货号
        /// </summary>
        public Guid productId { get; set; }
        /// <summary>
        /// 已入发票数量
        /// </summary>
        public decimal? invoiceQuantity { get; set; } = 0;

        /// <summary>
        /// 本次入票数
        /// </summary>
        public decimal? thisinvoiceQuantity { get; set; }

        public decimal NoinvoiceQuantity { get { return (this.quantity ?? 0) - (this.invoiceQuantity ?? 0); } }

        public decimal? taxRate { get; set; }
        public decimal? unitCost { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? producerOrderNo { get; set; }

        /// <summary>
        /// 出库查询使用（unitCost -> settlementCost）
        /// </summary>
        public decimal? settlementCost { get; set; }

        /// <summary>
        /// 结算成本单价（换货转退货）
        /// </summary>
        public decimal? settlementUnitCost { get; set; }
    }

}
