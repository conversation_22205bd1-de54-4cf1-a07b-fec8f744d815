﻿
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.DDD;

namespace Inno.CorePlatform.Finance.Backend
{
    public class GlobalExceptionFilter : IExceptionFilter
    {
        private readonly ILogger<GlobalExceptionFilter> _logger;
        public  GlobalExceptionFilter(ILogger<GlobalExceptionFilter> logger)
        {
            _logger = logger;
        }

        public void OnException(ExceptionContext context)
        {
            //异常返回结果包装
            BaseResponseData<object> rspResult;
            if (context.Exception is DomainException)
            {
                rspResult = BaseResponseData<object>.Failed((int)CodeStatusEnum.Failed, context.Exception.Message);
                context.ExceptionHandled = true;
                context.Result = new InternalServerErrorObjectResult(rspResult);
            }
            else if(context.Exception is ApplicationException)
            {
                rspResult = BaseResponseData<object>.Failed((int)CodeStatusEnum.Failed, context.Exception.Message);
                context.ExceptionHandled = true;
                context.Result = new InternalServerErrorObjectResult(rspResult);
            }
        }

        public class InternalServerErrorObjectResult : ObjectResult
        {
            public InternalServerErrorObjectResult(object value) : base(value)
            {
                StatusCode = StatusCodes.Status200OK;
            }
        }
    }
}
