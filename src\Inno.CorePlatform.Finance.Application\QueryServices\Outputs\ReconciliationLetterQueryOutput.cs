﻿using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class ReconciliationLetterListTabOutput
    {
        /// <summary>
        /// 全部-1
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 待提交0
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 待审核1
        /// </summary>
        public int WaitAuditCount { get; set; }
        /// <summary>
        /// 已拒绝66
        /// </summary>
        public int RefuseCount { get; set; }
        /// <summary>
        /// 已完成99
        /// </summary>
        public int ComplateCount { get; set; }
        /// <summary>
        /// 我的5000
        /// </summary>
        public int MyCount { get; set; }

    }

    public class DetailSumCount
    {
        /// <summary>
        /// 明细金额（开票金额、应收金额）
        /// </summary>
        public decimal? ValueSum { get; set; }

        /// <summary>
        /// 已收明细金额（开票金额、应收金额）
        /// </summary>

        public decimal? ReceivedValueSum { get; set; }

        /// <summary>
        /// 未收明细金额（开票金额、应收金额）
        /// </summary>
        public decimal? NonReceivedValueSum { get; set; }
    }
    public class ReconciliationLetterListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 单号
        /// </summary>

        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 欠款金额
        /// </summary>
        public decimal? ArrearsAmount { get; set; }

        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime? Deadline { get; set; }

        /// <summary>
        /// 对账涵模板
        /// </summary>
        public ReconciliationLetterEnum? ReconciliationLetterTemplate { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary>
        public string? AttachFileIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 询证函确认附件Ids
        /// </summary>
        public string? ConfirmAttachFileIds { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum? Status { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// OA请求Id
        /// </summary>
        public string? OARequestId { get; set; }

        /// <summary>
        /// 是不是法人公司
        /// </summary>
        public bool? isCorporateCompany { get; set; }
        /// <summary>
        /// 回款金额
        /// </summary> 
        public decimal? ReceivedAmount { get; set; }

        /// <summary>
        /// 合计金额
        /// </summary> 
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>  
        public DateTime? StartDate { get; set; }
    }
}
