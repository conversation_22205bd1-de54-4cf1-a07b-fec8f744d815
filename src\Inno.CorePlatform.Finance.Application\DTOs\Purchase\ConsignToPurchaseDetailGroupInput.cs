﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class ConsignToPurchaseDetailGroupInput
    {
        public string? CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public string? AgentId { get; set; }
        public string? AgentName { get; set; }
        public string? Code { get; set; }
        public DateTimeOffset? StartTime { get; set; }
        public DateTimeOffset? EndTime { get; set; }
        public int? pageIndex { get; set; } = 1;
        public int? pageSize { get; set; } = 50;
        public StrategyQuery? StrategyQuery { get; set; } = new StrategyQuery
        {
            FunctionUri = "metadata://fam"
        };
        public List<string> Codes { get; set; }

        /// <summary>
        /// InvoiceStatus=2 全部,InvoiceStatus=0 未到齐,InvoiceStatus = 1 已到齐
        /// </summary>
        public int? InvoiceStatus { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public List<string>? ProductNameIds { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
    }
    public class StrategyQuery
    {
        public Guid? UserId { get; set; }
        public string? FunctionUri { get; set; }
    }
}
