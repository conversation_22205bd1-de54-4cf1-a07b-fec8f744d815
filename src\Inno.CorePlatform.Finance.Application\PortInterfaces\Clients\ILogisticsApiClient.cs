﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Inno.CorePlatform.Finance.Application.QueryServices.Inputs.BatchDownLoadInvoiceInput;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 接口
    /// </summary>
    public interface ILogisticsApiClient
    {
        /// <summary>
        /// 获取阳采发票配送点编码
        /// </summary>
        /// <param name="purchaseCode"></param>
        /// <returns></returns>
        Task<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>> getPsdByPurchaseCodeForPm(string purchaseCode);

        /// <summary>
        /// 获取阳采配送单详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ShycShipmentDetailOutput>>> getFullDetails(ShycShipmentDetaillnput query);
        /// <summary>
        /// 对账函获取物流打印模板
        /// </summary>
        /// <param name="letterPrintInput"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<HttpContent> GetReconciliationLetterPDFStream(ReconciliationLetterPrintInput letterPrintInput);
        /// <summary>
        /// 获取发票配送单导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<InvoiceShipmentOutput>>> getInvoiceShipmentExport(InvoiceShipmentInput query);
    }
}
