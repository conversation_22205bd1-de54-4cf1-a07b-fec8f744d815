using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    /// <summary>
    /// 多对一勾稽库存能力中心更新经销调出明细请求
    /// </summary>
    public class ManyInventoryDistributionTransferUpdateDetail
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNumber { get; set; }

        /// <summary>
        /// 发票日期（时间戳）
        /// </summary>
        public long invoiceDate { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        public string invoiceTypeStr { get; set; }

        /// <summary>
        /// 进项发票明细列表
        /// </summary>
        public List<ManyInputInvoiceDetail> inputInvoiceDetails { get; set; }
    }
}
