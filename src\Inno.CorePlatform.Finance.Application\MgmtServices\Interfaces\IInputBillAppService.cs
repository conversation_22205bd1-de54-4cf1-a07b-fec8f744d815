﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IInputBillAppService
    {
        /// <summary>
        /// 生成发票抬头和发票详情(金蝶调用)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> CreateInputBill(InputBillInputDTo input);


        /// <summary>
        /// 生成提交详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> CreateInputBillSubmit(List<StoreInDetaiSubmitInput> input, Guid InputBillId, bool IsAdd = false);

        /// <summary>
        /// 删除发票明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> DeleteBillSubumit(InputBillDetailDeleteInput input);

        /// <summary>
        /// 初始化发票抬头表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task InitInputBill(Guid guid, bool isSubmit = false);


        /// <summary>
        /// 提交发票
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        Task<int> SubmitInputBill(List<Guid> guid,string userName);

        /// <summary>
        /// 更新入库的详情(保证原子性 跨应用的事务)
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        Task<bool> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail>  inventoryStoreInUpdateDetails);


        /// <summary>
        /// 创建和编辑提交的数据一致性校验
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        Task<(bool,string)> SubmitInvioQuntityDataConsistent(StoreInDetailQueryInput query, List<LotInfo> StoreInDetail);
        Task<int> IgnoreBill(List<Guid> ids);

        Task DeleteInputBillDebt(string invoiceNo);
        Task<int> RestoreBill(List<Guid> ids);

        /// <summary>
        /// 平尾差
        /// </summary>
        /// <param name="guid"></param>
        /// <returns></returns>
        Task<int> EliminatingErrors(Guid id);

        /// <summary>
        /// 按单据勾稽进项发票
        /// </summary>
        /// <param name="inputBillId"></param>
        /// <param name="codes"></param>
        /// <param name="type">1-寄售转购货，2-经销入库</param>
        /// <returns></returns>
        Task<BaseResponseData<int>> AddDetailByCodes(Guid inputBillId, List<string> codes, int type = 1);
        /// <summary>
        /// 按Excel导入
        /// </summary>
        /// <param name="file"></param>
        /// <param name="isSubmit">是否提交</param>
        /// <returns></returns>
        Task<BaseResponseData<int>> ImportInvoiceForBusinessBill(IFormFile file,bool isSubmit);

        Task<BaseResponseData<int>> BatchImportInvoiceForBusinessBill(IFormFile file);

        /// <summary>
        /// 进项发票N对N批量导入明细（返回错误信息文件）
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> BatchImportInvoiceForBusinessBillExcel(IFormFile file);
        /// <summary>
        /// 取消勾稽进项票
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelBill(Guid id,string userName);
    }
}
