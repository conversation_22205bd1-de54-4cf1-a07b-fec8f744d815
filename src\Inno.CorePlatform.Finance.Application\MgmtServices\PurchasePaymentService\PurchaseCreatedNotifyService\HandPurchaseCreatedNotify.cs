﻿using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService
{
    public class HandPurchaseCreatedNotify
    {
        private IHandPurchaseCreatedNotify handPurchaseCreatedNotify;
        public HandPurchaseCreatedNotify(IHandPurchaseCreatedNotify handPurchaseCreatedNotify)
        {
            this.handPurchaseCreatedNotify = handPurchaseCreatedNotify;
        }
        public async Task Hand(EventBusDTO input, IServiceProvider serviceProvider)
        {
            await handPurchaseCreatedNotify.Hand(input, serviceProvider);
        }
    }
}
