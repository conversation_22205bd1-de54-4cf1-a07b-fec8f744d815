﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class BulkPaymentQueryInput : BaseQuery
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 明细单号
        /// </summary>
        public string? DetailCode { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public long? BillDateStart { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public long? BillDateEnd { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 预计付款开始日期
        /// </summary>
        public long? ProbablyPayTimeStart { get; set; }
        /// <summary>
        /// 预计付款结束日期
        /// </summary>
        public long? ProbablyPayTimeEnd { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 申请人集合
        /// </summary>
        public List<string>? CreatedBy { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set;}

        /// <summary>
        /// 采购合同号
        /// </summary>
        public string? PurchaseContactNo { get; set; }
    }
    /// <summary>
    /// 通过Id查询
    /// </summary>
    public class QueryById : BaseQuery
    {
        public Guid? userId { get; set; }

        public string? CurrentUserName { get; set; }
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 类型
        /// </summary>
        public int? Classify  { get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillCode { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServicesId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string? PaymentCode { get; set; }

        /// <summary>
        /// 账期类型
        /// </summary>
        public int? AccountPeriod { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public Decimal? Discount { get; set; }

        /// <summary>
        /// 终端客户，
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RecognizeReceiveDetailEnum? Status { get; set; }
    }
   public class UpdateTransferDiscourseInput
    {
        public Guid Id { get; set; }

        public string? TransferDiscourse { get; set; }
    }
    public class UpdateRemarkInput
    {
        public Guid Id { get; set; }

        public string? Remark { get; set; }
    }
}
