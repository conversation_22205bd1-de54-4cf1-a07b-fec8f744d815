﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class CustomsOrderPaiedInput
    {
        public string CustomsPaymentCode { get; set; }
        public decimal PaymentAmount { get; set; }
        public string Tag { get; set; }
    }
    public class ServiceFeeOrderPaiedInput
    {
        public string Code { get; set; }
        public decimal PaymentAmount { get; set; }
        public string Tag { get; set; }
    }

    public class OrderPaiedInput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        public string PaymentCode { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbtValue { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string DebtCode { get; set; }
    }

    public class DebtAbtPurchasePubInput
    {
        /// <summary>
        /// 负数应付单号
        /// </summary>
        public string MinusDebtCode { get; set; }
        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatementAmount { get; set; }
        /// <summary>
        /// 冲销单号
        /// </summary>
        public string AbatementCode { get; set; }
    }
}
