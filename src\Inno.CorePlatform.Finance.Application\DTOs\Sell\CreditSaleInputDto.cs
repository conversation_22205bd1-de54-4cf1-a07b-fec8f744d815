using System;
using System.Collections.Generic;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.SaleReturn;
using Inno.CorePlatform.Finance.Domain;
using Mapster;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    /// <summary>
    /// 用于创建应收的销售输出DTO
    /// </summary>
    public class CreditSaleInputDto
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司编号
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 核销明细
        /// </summary>
        public List<TempInventoryDetailOutput> TempInventoryDetails { get; set; }

        /// <summary>
        /// 事业部ID
        /// </summary>
        public int? businessDeptId { get; set; }

        /// <summary>
        /// 事业部全名
        /// </summary>
        public string businessDeptFullName { get; set; }

        /// <summary>
        /// 事业部全路径
        /// </summary>
        public string businessDeptFullPath { get; set; }

        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string SaleSystemName { get; set; }

        /// <summary>
        /// 订单来源
        /// </summary>
        public SaleSourceEnum Source { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; }

        /// <summary>
        /// 终端医院Id
        /// </summary>
        public string HospitalId { get; set; }

        /// <summary>
        /// 终端医院名称
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        public string CustomerOrderCode { get; set; }

        /// <summary>
        /// 客户联系人名称
        /// </summary>
        public string CustomerPersonName { get; set; }

        /// <summary>
        /// 阳采订单号
        /// </summary>
        public string SunPurchaseRelatecode { get; set; }

        /// <summary>
        /// 返利类型
        /// </summary>
        public RebateTypeEnum? RebateType { get; set; }
        //应收子类型 CreditTypeEnum
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 是否不需要开票 1:无需开票 0:需要开票
        /// </summary>
        public bool? IsNoNeedInvoice { get; set; }
        public bool NeedDebt { get; internal set; } = true;
        /// <summary>
        /// 是否需要生成应收
        /// </summary>
        public bool IsNeedCreateReceivable { get; set; } = true;


        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSourceType { get; set; }

        /// <summary>
        /// 从SaleOutput创建CreditSaleOutputDto，并将ConsumerPrice赋值给price
        /// </summary>
        public static CreditSaleInputDto FromSaleOutputWithConsumerPrice(SaleOutput saleOutput)
        {
            if (saleOutput == null)
            {
                throw new ArgumentNullException(nameof(saleOutput));
            }
            var result = saleOutput.Adapt<CreditSaleInputDto>();

            result.BillCode = saleOutput.BillCode;
            result.CreditSaleSubType = CreditSaleSubTypeEnum.personal;
            if (result.TempInventoryDetails != null)
            {
                // 只处理ConsumerPrice有值且大于0的记录
                var validDetails = result.TempInventoryDetails.Where(detail =>
                    detail.ConsumerPrice.HasValue);

                foreach (var detail in validDetails)
                {
                    detail.price = detail.ConsumerPrice.Value;
                }

                // 过滤掉不符合条件的记录
                result.TempInventoryDetails = validDetails.ToList();
            }

            return result;
        }
        /// <summary>
        /// 从SaleOutput创建CreditSaleOutputDto，并将PlatformCouponPrice赋值给price
        /// </summary>
        public static CreditSaleInputDto FromSaleOutputWithPlatformCouponPrice(SaleOutput saleOutput)
        {
            if (saleOutput == null)
            {
                throw new ArgumentNullException(nameof(saleOutput));
            }

            var result = saleOutput.Adapt<CreditSaleInputDto>();
            result.BillCode = saleOutput.BillCode;
            result.CreditSaleSubType = CreditSaleSubTypeEnum.platform;
            result.NeedDebt = false;
            var TempInventoryDetails = new List<TempInventoryDetailOutput>();
            if (result.TempInventoryDetails != null)
            {
                // 只处理PlatformCouponPrice有值且大于0的记录
                var validDetails = result.TempInventoryDetails.Where(detail =>
                    detail.PlatformCouponPrice.HasValue).GroupBy(g => new { g.productId });

                foreach (var detail in validDetails)
                {
                    var tempDetail = detail.FirstOrDefault();
                    result.IsNoNeedInvoice = tempDetail.IsNoNeedInvoice();//取第一条

                    tempDetail.price = detail.Sum(d => d.PlatformCouponPrice.Value * d.quantity);
                    tempDetail.quantity = 1;
                    TempInventoryDetails.Add(tempDetail);
                }
                result.TempInventoryDetails = TempInventoryDetails;
                //如果detail.price sum =0,则不需要生成应收
                if (result.TempInventoryDetails.Sum(d => d.price) == 0)
                {
                    result.IsNeedCreateReceivable = false;
                }
            }

            return result;
        }

        /// <summary>
        /// 从SaleOutput创建CreditSaleOutputDto，保持原始price不变
        /// </summary>
        public static CreditSaleInputDto FromSaleOutputWithOriginalPrice(SaleOutput saleOutput)
        {
            return saleOutput.Adapt<CreditSaleInputDto>();
        }


    }


    public class CreditSaleInvoiceToICInputDto
    {


        /// <summary>
        /// 应收单号
        /// </summary>
        public string? ReceivableNo { get; set; }

        /// <summary>
        /// 订单号(旺店通销售出库单号)
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 销售订单号（核心平台暂存核销单号）
        /// </summary>
        public string? SalesOrderNo { get; set; }
        /// <summary>
        /// 天猫原始订单号
        /// </summary>
        public string? OriginalOrderNo { get; set; }
        /// <summary>
        /// 待开票明细
        /// </summary>
        public List<TradeOrderDetailInput> Details { get; set; }
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string HospitalName { get; set; }
        public string HospitalId { get; set; }

        /// <summary>
        /// 退货广播
        /// </summary>
        /// <param name="saleReturn"></param>
        /// <param name="refundStoreInDetail"></param>
        /// <returns></returns>
        public static CreditSaleInvoiceToICInputDto FromRefundStoreIn(SaleReturnCreditInput saleReturn, IC.GetRefundStoreInDetailOutput refundStoreInDetail)
        {
            return new CreditSaleInvoiceToICInputDto
            {
                OrderNo = refundStoreInDetail.saleOutCode,
                ReceivableNo = refundStoreInDetail.tempInventoryCode + "-001",
                SalesOrderNo = refundStoreInDetail.tempInventoryCode,
                OriginalOrderNo = refundStoreInDetail.tmallOriginalCode,
                Details = saleReturn.StoreInDetails.Select(d => new TradeOrderDetailInput
                {
                    GoodsName = d.productName,
                    Specification = d.specification,
                    Quantity = d.quantity,
                    UnitPrice = d.price,
                    Amount = d.price * d.quantity,
                    TaxRate = d.taxRate
                }).ToList()
            };
        }
        /// <summary>
        /// 核销广播
        /// </summary>
        /// <param name="consumerPriceSaleOut"></param>
        /// <returns></returns>
        internal static CreditSaleInvoiceToICInputDto FromSaleOut(CreditSaleInputDto consumerPriceSaleOut)
        {
            return new CreditSaleInvoiceToICInputDto
            {
                ReceivableNo = consumerPriceSaleOut.BillCode + "-001",
                SalesOrderNo = consumerPriceSaleOut.BillCode,
                OriginalOrderNo = consumerPriceSaleOut.RelateCode,
                CompanyId = consumerPriceSaleOut.CompanyId,
                CompanyName = consumerPriceSaleOut.CompanyName,
                HospitalId = consumerPriceSaleOut.HospitalId,
                HospitalName = consumerPriceSaleOut.HospitalName,
                Details = consumerPriceSaleOut.TempInventoryDetails.Select(d => new TradeOrderDetailInput
                {
                    GoodsName = d.productName,
                    Specification = d.specification,
                    Quantity = d.quantity,
                    UnitPrice = d.price,
                    Amount = d.price * d.quantity,
                    TaxRate = d.salesTaxRate
                }).ToList()
            };
        }
    }
    public class TradeOrderDetailInput
    {
        /// <summary>
        /// 货物或应税劳务服务名称(开票名称)
        /// </summary>
        public string? GoodsName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? Amount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount
        {
            get
            {
                var taxamount = getTaxAmount(Amount, TaxRate);
                return Math.Round(taxamount, 2);
            }
        }

        private decimal getTaxAmount(decimal? amount, decimal? taxRate)
        {
            if (amount.HasValue && taxRate.HasValue)
            {
                return amount.Value / (1 + taxRate.Value / 100) * (taxRate.Value / 100);
            }
            return decimal.Zero;
        }
    }

}
