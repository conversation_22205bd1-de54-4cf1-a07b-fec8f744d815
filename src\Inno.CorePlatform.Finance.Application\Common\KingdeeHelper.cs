﻿using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.Common
{
    /// <summary>
    /// 金蝶辅助类
    /// </summary>
    public static class KingdeeHelper
    {
        /// <summary>
        /// 转换应收类型
        /// </summary>
        /// <param name="creditType"></param>
        /// <returns></returns>
        public static string TransferCreditType(string creditType)
        {
            switch (creditType)
            {
                case "revise":
                    return "ar_finarbill_BT1";
                case "origin":
                    return "ar_finarbill_BT2";
                case "_return":
                    return "ar_finarbill_BT3"; 
                case "sale":
                    return "ar_finarbill_BT4";
                case "outstore":
                    return "ar_finarbill_BT5";
                case "selforder":
                    return "ar_finarbill_BT6";
                case "selfreturn":
                    return "ar_finarbill_BT7";
                case "selfrevise":
                    return "ar_finarbill_BT8";
                case "rebate":
                    return "ar_finarbill_BT9";
                case "equipment":
                    return "ar_finarbill_BT10"; 
                case "servicefee":
                    return "ar_finarbill_BT11";
                case "servicefeerevise":
                    return "ar_finarbill_BT13";
                case "lossrecognition":
                    return "ar_finarbill_BT14";
                default:
                    return "";
            }
        }

        /// <summary>
        /// 转化应付类型
        /// </summary>
        /// <param name="debtType"></param>
        /// <returns></returns>
        public static string TransferDebtType(DebtTypeEnum debtType)
        {
            switch (debtType) 
            {
                case DebtTypeEnum.equipment:
                    return "ap_finapbill_BT13";
                case DebtTypeEnum.rebate:
                    return "ap_finapbill_BT10";
                case DebtTypeEnum.selfreturn:
                    return "ap_finapbill_BT6";
                case DebtTypeEnum.selfrevise:
                    return "ap_finapbill_BT8";
                case DebtTypeEnum.origin:
                    return "ap_finapbill_BT2";
                case DebtTypeEnum._return:
                    return "ap_finapbill_BT3";
                case DebtTypeEnum.funded:
                    return "ap_finapbill_BT12";
                case DebtTypeEnum.selforder:
                    return "ap_finapbill_BT5";
                case DebtTypeEnum.revise:
                    return "ap_finapbill_BT1";
                case DebtTypeEnum.orderbulk:
                    return "ap_finapbill_BT9";
                case DebtTypeEnum.grouporder:
                    return "ap_finapbill_BT11";
                case DebtTypeEnum.order:
                    return "ap_finapbill_BT4";
                case DebtTypeEnum.purchase:
                    return "ap_finapbill_BT7";
                case DebtTypeEnum.consignmentloss:
                case DebtTypeEnum.consignmentreturn:
                    return "ap_finapbill_BT14";
                case DebtTypeEnum.expenses:
                    return "ap_finapbill_BT15";
                case DebtTypeEnum.servicefee:
                    return "ap_finapbill_BT16";
                case DebtTypeEnum.exchangeback:
                    return "ap_finapbill_BT17";
                case DebtTypeEnum.lossrecognition:
                    return "ap_finapbill_BT18";
                default: return "";
            }
        }

        /// <summary>
        /// 转化费用项目
        /// </summary>
        /// <param name="serviceItemTypeName"></param>
        /// <returns></returns>
        public static string TransferServiceItemTypeCode(string serviceItemTypeName)
        {
            switch (serviceItemTypeName)
            {
                case "1": //维保服务
                    return "FYXM118";
                case "2": //技术服务
                    return "FYXM119";
                case "3": //租借服务
                    return "FYXM120";
                case "5": //SPD服务
                    return "FYXM121";
                case "6": //纯代服务
                    return "FYXM122";
                case "4": //仓储服务
                    return "FYXM123";
                case "7": //核心平台服务
                    return "FYXM124";
                case "8": //价外服务
                    return "FYXM125";
                default: return "";
            }
        }

        /// <summary>
        /// 转化费用项目
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string TransferSaleSubType(SaleSubTypeEnum type)
        {
            switch (type)
            {
                case SaleSubTypeEnum.MaintenanceService: //维保服务
                    return "FYXM118";
                case SaleSubTypeEnum.TechnicalService: //技术服务
                    return "FYXM119";
                case SaleSubTypeEnum.Lease: //租借服务
                    return "FYXM120";
                case SaleSubTypeEnum.SPD: //SPD服务
                    return "FYXM121";
                case SaleSubTypeEnum.PureGeneration: //纯代服务
                    return "FYXM122";
                case SaleSubTypeEnum.Storage: //仓储服务
                    return "FYXM123";
                case SaleSubTypeEnum.CorePlatform: //核心平台服务
                    return "FYXM124";
                case SaleSubTypeEnum.OutPrice: //价外服务
                    return "FYXM125";
                default: return "";
            }
        }
    }
}
