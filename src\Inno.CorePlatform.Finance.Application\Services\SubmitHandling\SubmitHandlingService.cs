using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation;
using Inno.CorePlatform.Finance.Application.Services.MatchCache;
using Inno.CorePlatform.Finance.Application.Services.SubmitTracking;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitHandling
{
    /// <summary>
    /// 提交处理服务，用于处理提交操作的业务逻辑
    /// </summary>
    public class SubmitHandlingService : ISubmitHandlingService
    {
        private readonly ILogger<SubmitHandlingService> _logger;
        private readonly FinanceDbContext _db;
        private readonly IInterfaceInvocationService _interfaceInvocationService;
        private readonly IMatchCacheManager _cacheManager;
        private readonly ISubmitRollbackHandler _submitRollbackHandler;
        private readonly IRevokeRecoveryHandler _revokeRecoveryHandler;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="db">数据库上下文</param>
        /// <param name="interfaceInvocationService">接口调用服务</param>
        /// <param name="cacheManager">匹配缓存管理器</param>
        /// <param name="submitRollbackHandler">提交回滚处理器</param>
        /// <param name="revokeRecoveryHandler">撤销恢复处理器</param>
        public SubmitHandlingService(
            ILogger<SubmitHandlingService> logger,
            FinanceDbContext db,
            IInterfaceInvocationService interfaceInvocationService,
            IMatchCacheManager cacheManager,
            ISubmitRollbackHandler submitRollbackHandler,
            IRevokeRecoveryHandler revokeRecoveryHandler)
        {
            _logger = logger;
            _db = db;
            _interfaceInvocationService = interfaceInvocationService;
            _cacheManager = cacheManager;
            _submitRollbackHandler = submitRollbackHandler;
            _revokeRecoveryHandler = revokeRecoveryHandler;
        }

        /// <summary>
        /// 提交匹配
        /// </summary>
        /// <param name="request">提交匹配请求</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>提交匹配响应</returns>
        public async Task<BaseResponseData<bool>> SubmitMatch(SubmitMatchRequest request, string currentUserName)
        {
            _logger.LogInformation("SubmitMatch - 开始提交匹配, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            // 声明变量，用于在finally块中释放锁
            Guid mergeInputBillId = request.MergeInputBillId;
            string? invoiceNumber = null;

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogError("SubmitMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "合并进项发票不存在");
                }

                // 保存发票号，用于在finally块中释放锁
                invoiceNumber = mergeInputBill.MergeInvoiceNumber;

                // 检查是否有正在进行的提交操作
                string? lockedInvoiceNumber = _cacheManager.GetLockedInvoiceNumber(request.MergeInputBillId);
                if (lockedInvoiceNumber != null)
                {
                    _logger.LogWarning("SubmitMatch - 单据正在提交中，请不要重复操作, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        request.MergeInputBillId, lockedInvoiceNumber);
                    return BaseResponseData<bool>.Failed(500, $"单据\"{lockedInvoiceNumber}\"正在提交中，请不要重复操作");
                }

                // 设置提交锁
                _cacheManager.SetSubmitLock(request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);
                _logger.LogInformation("SubmitMatch - 设置提交锁, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);

                // 查询提交明细
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId && x.TaxCost.HasValue)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogError("SubmitMatch - 没有匹配明细, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "没有匹配明细");
                }

                // 校验勾稽明细计算的含税金额和进项票的金额合计相差是否大于1元
                decimal totalTaxAmount = Math.Round(submitDetails.Sum(x => x.TaxCost.Value * x.MatchQuantity), 4);
                if (Math.Abs(Math.Abs(totalTaxAmount) - Math.Abs(mergeInputBill.Amount)) > 1)
                {
                    _logger.LogWarning("SubmitMatch - 勾稽明细计算的含税金额和进项票的金额合计相差大于1元, 勾稽明细含税金额: {TotalTaxAmount}, 进项票金额: {Amount}",
                        totalTaxAmount, mergeInputBill.Amount);
                    return BaseResponseData<bool>.Failed(500, "勾稽明细计算的含税金额和进项票的金额合计相差大于1元，请检查勾稽明细");
                }

                // 创建提交跟踪器
                var submitTracker = new SubmitTracker(_logger, request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);

                // 开启事务
                using var transaction = await _db.Database.BeginTransactionAsync();
                _logger.LogInformation("SubmitMatch - 开启事务");

                try
                {

                    // 第一步：调用金蝶接口，处理应付
                    var kingdeeResult = await _interfaceInvocationService.InvokeKingdeeManyInvoiceSpecifyApFin(
                        mergeInputBill, submitDetails, currentUserName);

                    // 记录金蝶接口调用成功
                    submitTracker.AddSuccessfulOperation(SubmitOperationType.KingdeeSubmit, null, kingdeeResult);

                    // 按业务类型分组并排序（服务费排到最后）
                    var businessTypeGroups = submitDetails.GroupBy(x => x.BusinessType)
                        .OrderBy(g => g.Key == (int)BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key);
                    _logger.LogInformation("SubmitMatch - 按业务类型分组, 组数: {Count}", businessTypeGroups.Count());

                    // 第二步：根据业务类型调用不同的接口
                    foreach (var group in businessTypeGroups)
                    {
                        var businessType = (BusinessType)(group.Key ?? 0);
                        var allDetails = group.ToList();
                        try
                        {
                            // 根据业务类型调用不同的接口
                            switch (businessType)
                            {
                                case BusinessType.DistributionPurchase:
                                    // 经销购货入库
                                    var distributionPurchaseResult = await _interfaceInvocationService.InvokeUpdateManyStoreInDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.DistributionPurchase, businessType, distributionPurchaseResult);
                                    break;
                                case BusinessType.ConsignmentToPurchase:
                                    // 寄售转购货
                                    var consignmentToPurchaseResult = await _interfaceInvocationService.InvokeUpdateConsignToPurchaseDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.ConsignmentToPurchase, businessType, consignmentToPurchaseResult);
                                    break;
                                case BusinessType.ServiceFeeProcurement:
                                    // 服务费采购
                                    var serviceFeeResult = await _interfaceInvocationService.
                                    InvokeUpdateServiceFeeProcurementDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.ServiceFeeProcurement, businessType, serviceFeeResult);
                                    break;
                                case BusinessType.DistributionTransfer:
                                    // 经销调出
                                    var distributionTransferResult = await _interfaceInvocationService.InvokeUpdateDistributionTransferDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.DistributionTransfer, businessType, distributionTransferResult);
                                    break;
                                case BusinessType.PurchaseRevision:
                                    // 购货修订
                                    var purchaseRevisionResult = await _interfaceInvocationService.InvokeUpdatePurchaseRevisionDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.PurchaseRevision, businessType, purchaseRevisionResult);
                                    break;
                                case BusinessType.ExchangeToReturn:
                                    // 换货转退货
                                    var exchangeToReturnResult = await _interfaceInvocationService.InvokeUpdateExchangeToReturnDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.ExchangeToReturn, businessType, exchangeToReturnResult);
                                    break;
                                case BusinessType.LossRecognition:
                                    // 损失确认 - 财务内部处理，不需要调用外部接口
                                    var lossRecognitionResult = await _interfaceInvocationService.InvokeUpdateLossRecognitionDetail(mergeInputBill, allDetails);
                                    submitTracker.AddSuccessfulOperation(SubmitOperationType.LossRecognition, businessType, lossRecognitionResult);
                                    break;
                                default:
                                    _logger.LogError("SubmitMatch - 不支持的业务类型: {BusinessType}", businessType);
                                    throw new ArgumentException($"不支持的业务类型: {businessType}");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "SubmitMatch - 处理业务类型 {BusinessType} 失败, 错误: {ErrorMessage}", businessType, ex.Message);

                            // 回滚事务
                            await transaction.RollbackAsync();

                            // 使用提交跟踪器执行回滚操作
                            await submitTracker.RollbackAsync(_submitRollbackHandler);

                            return BaseResponseData<bool>.Failed(500, $"处理业务类型 {businessType.GetDescription()} 失败: {ex.Message}");
                        }
                    }

                    // 第三步：更新合并进项发票状态并清空取消勾稽信息
                    mergeInputBill.Status = (int)MergeInputBillStatusEnum.Submitted; // 已提交
                    mergeInputBill.SubmitTime = DateTimeHelper.GetCurrentDate();
                    // 再次提交时清空取消勾稽信息
                    mergeInputBill.CancelReconciliationTime = null;
                    mergeInputBill.IsCancelledReconciliation = null;
                    _db.MergeInputBills.Update(mergeInputBill);

                    // 查询关联的原始进项票
                    var originalInputBillIds = await _db.MergeInputBillRelations
                        .Where(r => r.MergeInputBillId == request.MergeInputBillId)
                        .Select(r => r.InputBillId)
                        .ToListAsync();

                    if (originalInputBillIds.Any())
                    {
                        // 查询原始进项票
                        var originalInputBills = await _db.InputBills
                            .Where(x => originalInputBillIds.Contains(x.Id))
                            .ToListAsync();

                        // 更新原始进项票状态为"已提交"并清空取消勾稽信息
                        foreach (var inputBill in originalInputBills)
                        {
                            inputBill.Status = (int)InputBillStatusEnum.Submitted;
                            // 再次提交时清空取消勾稽信息
                            inputBill.CancelReconciliationTime = null;
                            inputBill.IsCancelledReconciliation = null;
                        }

                        _db.InputBills.UpdateRange(originalInputBills);
                        _logger.LogInformation("SubmitMatch - 更新原始进项票状态为已提交, 数量: {Count}", originalInputBills.Count);
                    }

                    _logger.LogInformation("SubmitMatch - 更新合并进项发票状态为已提交, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                    // 保存所有更改
                    await _db.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();

                    // 清除缓存
                    _cacheManager.ClearAllCache(request.MergeInputBillId);

                    _logger.LogInformation("SubmitMatch - 提交匹配成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Success(true);
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    // 使用提交跟踪器执行回滚操作
                    await submitTracker.RollbackAsync(_submitRollbackHandler);

                    _logger.LogError(ex, "SubmitMatch - 提交匹配失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, ex.Message);

                    return BaseResponseData<bool>.Failed(500, $"提交匹配失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SubmitMatch - 提交匹配失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                return BaseResponseData<bool>.Failed(500, $"提交匹配失败: {ex.Message}");
            }
            finally
            {
                // 释放提交锁
                if (invoiceNumber != null)
                {
                    _cacheManager.ReleaseSubmitLock(mergeInputBillId);
                    _logger.LogInformation("SubmitMatch - 释放提交锁, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                }
            }
        }

        /// <summary>
        /// 回滚提交
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>回滚结果</returns>
        private async Task<bool> RollbackSubmitAsync(string mergeInvoiceNumber, BusinessType businessType)
        {
            _logger.LogInformation("RollbackSubmitAsync - 开始回滚提交, MergeInvoiceNumber: {MergeInvoiceNumber}, BusinessType: {BusinessType}",
                mergeInvoiceNumber, businessType);

            try
            {
                // 首先尝试回滚金蝶接口
                try
                {
                    _logger.LogInformation("RollbackSubmitAsync - 尝试回滚金蝶接口, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInvoiceNumber);

                    // 查询合并进项发票ID
                    var mergeInputBill = await _db.MergeInputBills
                        .Where(x => x.MergeInvoiceNumber == mergeInvoiceNumber)
                        .FirstOrDefaultAsync();

                    if (mergeInputBill != null)
                    {
                        // 调用金蝶撤销多发票指定应付接口
                        var kingdeeResult = await _interfaceInvocationService.InvokeKingdeeRevokeSpecifyApFin(
                            mergeInputBill.Id, mergeInvoiceNumber);

                        if (kingdeeResult.Code == CodeStatusEnum.Success)
                        {
                            _logger.LogInformation("RollbackSubmitAsync - 金蝶接口回滚成功, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInvoiceNumber);
                        }
                        else
                        {
                            _logger.LogWarning("RollbackSubmitAsync - 金蝶接口回滚失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                                mergeInvoiceNumber, kingdeeResult.Message);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("RollbackSubmitAsync - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInvoiceNumber);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "RollbackSubmitAsync - 回滚金蝶接口失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                        mergeInvoiceNumber, ex.Message);
                    // 继续执行后续回滚操作，不中断流程
                }

                // 根据业务类型调用不同的回滚接口
                switch (businessType)
                {
                    case BusinessType.DistributionPurchase:
                        // 经销购货入库回滚
                        await _interfaceInvocationService.InvokeRevokeInventoryStoreInDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.ConsignmentToPurchase:
                        // 寄售转购货回滚
                        await _interfaceInvocationService.InvokeRevokeConsignToPurchaseDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.ServiceFeeProcurement:
                        // 服务费采购回滚
                        await _interfaceInvocationService.InvokeRevokeServiceFeeProcurementDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.DistributionTransfer:
                        // 经销调出回滚
                        await _interfaceInvocationService.InvokeRevokeDistributionTransferDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.PurchaseRevision:
                        // 购货修订回滚
                        await _interfaceInvocationService.InvokeRevokePurchaseRevisionDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.ExchangeToReturn:
                        // 换货转退货回滚
                        await _interfaceInvocationService.InvokeRevokeExchangeToReturnDetail(mergeInvoiceNumber);
                        break;
                    case BusinessType.LossRecognition:
                        // 损失确认回滚 - 财务内部处理
                        await _interfaceInvocationService.InvokeRevokeLossRecognitionDetail(mergeInvoiceNumber);
                        break;
                    default:
                        _logger.LogWarning("RollbackSubmitAsync - 不支持的业务类型: {BusinessType}", businessType);
                        break;
                }

                _logger.LogInformation("RollbackSubmitAsync - 回滚提交成功, MergeInvoiceNumber: {MergeInvoiceNumber}, BusinessType: {BusinessType}",
                    mergeInvoiceNumber, businessType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RollbackSubmitAsync - 回滚提交失败, MergeInvoiceNumber: {MergeInvoiceNumber}, BusinessType: {BusinessType}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, businessType, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 撤销匹配
        /// </summary>
        /// <param name="request">撤销匹配请求</param>
        /// <returns>撤销匹配响应</returns>
        public async Task<BaseResponseData<bool>> RevokeMatch(RevokeMatchRequest request)
        {
            _logger.LogInformation("RevokeMatch - 开始撤销匹配, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

            // 声明变量，用于在finally块中释放锁
            Guid mergeInputBillId = request.MergeInputBillId;
            string? invoiceNumber = null;

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogError("RevokeMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "合并进项发票不存在");
                }

                // 保存发票号，用于在finally块中释放锁
                invoiceNumber = mergeInputBill.MergeInvoiceNumber;

                // 检查状态，只有已提交状态才能取消勾稽
                if (mergeInputBill.Status != (int)MergeInputBillStatusEnum.Submitted)
                {
                    _logger.LogWarning("RevokeMatch - 当前状态不能取消勾稽, 状态: {Status}", ((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription());
                    return BaseResponseData<bool>.Failed(500, $"只有已提交状态才能取消勾稽，当前状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}");
                }

                // 检查是否有正在进行的撤销操作
                string? lockedInvoiceNumber = _cacheManager.GetLockedInvoiceNumber(request.MergeInputBillId);
                if (lockedInvoiceNumber != null)
                {
                    _logger.LogWarning("RevokeMatch - 单据正在撤销中，请不要重复操作, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        request.MergeInputBillId, lockedInvoiceNumber);
                    return BaseResponseData<bool>.Failed(500, $"单据\"{lockedInvoiceNumber}\"正在撤销中，请不要重复操作");
                }

                // 设置提交锁
                _cacheManager.SetSubmitLock(request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);
                _logger.LogInformation("RevokeMatch - 设置撤销锁, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);

                // 查询提交明细
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogWarning("RevokeMatch - 没有匹配明细, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "没有匹配明细");
                }

                // 创建撤销跟踪器，用于跟踪撤销接口调用并在失败时进行恢复（重新提交）
                var revokeTracker = new RevokeTracker(
                    _logger,
                    request.MergeInputBillId,
                    mergeInputBill.MergeInvoiceNumber,
                    mergeInputBill,
                    submitDetails,
                    "System" // 使用系统用户名，因为撤销操作不需要用户名
                );

                // 使用注入的撤销恢复处理器，用于在撤销失败时恢复系统状态

                // 开启事务
                using var transaction = await _db.Database.BeginTransactionAsync();
                _logger.LogInformation("RevokeMatch - 开启事务");

                try
                {
                    // 第一步：调用金蝶撤销多发票指定应付接口
                    _logger.LogInformation("RevokeMatch - 调用金蝶撤销多发票指定应付接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        mergeInputBill.MergeInvoiceNumber);

                    var kingdeeResult = await _interfaceInvocationService.InvokeKingdeeRevokeSpecifyApFin(
                        request.MergeInputBillId, mergeInputBill.MergeInvoiceNumber);

                    if (kingdeeResult.Code != CodeStatusEnum.Success)
                    {
                        _logger.LogError("RevokeMatch - 调用金蝶撤销多发票指定应付接口失败, 错误: {ErrorMessage}", kingdeeResult.Message);

                        // 如果是已存在的错误，视为成功，继续执行
                        if (kingdeeResult.Message == null || !kingdeeResult.Message.Contains("已存在"))
                        {
                            return BaseResponseData<bool>.Failed(500, $"【金蝶】撤销多发票指定应付接口失败: {kingdeeResult.Message}");
                        }

                        _logger.LogWarning("RevokeMatch - 金蝶接口返回已存在错误，视为成功，继续执行");
                    }

                    // 记录金蝶接口调用成功
                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.KingdeeRevoke, null, kingdeeResult);

                    // 按业务类型分组并排序（服务费排到最后）
                    var businessTypeGroups = submitDetails.GroupBy(x => x.BusinessType)
                        .OrderBy(g => g.Key == (int)BusinessType.ServiceFeeProcurement ? int.MaxValue : (int)g.Key);
                    _logger.LogInformation("RevokeMatch - 按业务类型分组, 组数: {Count}", businessTypeGroups.Count());

                    // 第二步：根据业务类型调用不同的回滚接口
                    foreach (var group in businessTypeGroups)
                    {
                        var businessType = (BusinessType)(group.Key ?? 0);
                        var allDetails = group.ToList();

                        _logger.LogInformation("RevokeMatch - 处理业务类型: {BusinessType}, 明细数量: {Count}",
                            businessType, allDetails.Count);

                        try
                        {
                            // 根据业务类型调用不同的回滚接口
                            switch (businessType)
                            {
                                case BusinessType.DistributionPurchase:
                                    // 经销购货入库回滚
                                    var distributionPurchaseResult = await _interfaceInvocationService.InvokeRevokeInventoryStoreInDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.DistributionPurchaseRevoke, businessType, distributionPurchaseResult);
                                    break;
                                case BusinessType.ConsignmentToPurchase:
                                    // 寄售转购货回滚
                                    var consignmentToPurchaseResult = await _interfaceInvocationService.InvokeRevokeConsignToPurchaseDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.ConsignmentToPurchaseRevoke, businessType, consignmentToPurchaseResult);
                                    break;
                                case BusinessType.ServiceFeeProcurement:
                                    // 服务费采购回滚
                                    var serviceFeeResult = await _interfaceInvocationService.InvokeRevokeServiceFeeProcurementDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.ServiceFeeProcurementRevoke, businessType, serviceFeeResult);
                                    break;
                                case BusinessType.DistributionTransfer:
                                    // 经销调出回滚
                                    var distributionTransferResult = await _interfaceInvocationService.InvokeRevokeDistributionTransferDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.DistributionTransferRevoke, businessType, distributionTransferResult);
                                    break;
                                case BusinessType.PurchaseRevision:
                                    // 购货修订回滚
                                    var purchaseRevisionResult = await _interfaceInvocationService.InvokeRevokePurchaseRevisionDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.PurchaseRevisionRevoke, businessType, purchaseRevisionResult);
                                    break;
                                case BusinessType.ExchangeToReturn:
                                    // 换货转退货回滚
                                    var exchangeToReturnResult = await _interfaceInvocationService.InvokeRevokeExchangeToReturnDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.ExchangeToReturnRevoke, businessType, exchangeToReturnResult);
                                    break;
                                case BusinessType.LossRecognition:
                                    // 损失确认回滚 - 财务内部处理
                                    var lossRecognitionRevokeResult = await _interfaceInvocationService.InvokeRevokeLossRecognitionDetail(mergeInputBill.MergeInvoiceNumber);
                                    revokeTracker.AddSuccessfulOperation(RevokeOperationType.LossRecognitionRevoke, businessType, lossRecognitionRevokeResult);
                                    break;
                                default:
                                    _logger.LogWarning("RevokeMatch - 不支持的业务类型: {BusinessType}", businessType);
                                    break;
                            }
                        }
                        catch (TimeoutException ex)
                        {
                            _logger.LogError(ex, "RevokeMatch - 处理业务类型 {BusinessType} 失败(接口调用超时60秒), 错误: {ErrorMessage}", businessType, ex.Message);

                            // 回滚事务
                            await transaction.RollbackAsync();

                            // 使用撤销跟踪器执行恢复操作（重新提交）
                            await revokeTracker.RecoverAsync(_revokeRecoveryHandler);

                            return BaseResponseData<bool>.Failed(500, $"处理业务类型 {businessType.GetDescription()} 失败(接口调用超时60秒): {ex.Message}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "RevokeMatch - 处理业务类型 {BusinessType} 失败, 错误: {ErrorMessage}", businessType, ex.Message);

                            // 回滚事务
                            await transaction.RollbackAsync();

                            // 使用撤销跟踪器执行恢复操作（重新提交）
                            await revokeTracker.RecoverAsync(_revokeRecoveryHandler);

                            return BaseResponseData<bool>.Failed(500, $"处理业务类型 {businessType.GetDescription()} 失败: {ex.Message}");
                        }
                    }

                    // 第三步：更新合并进项发票状态并记录取消勾稽信息
                    mergeInputBill.Status = (int)MergeInputBillStatusEnum.CompletedReconciliation; // 匹配完成
                    mergeInputBill.SubmitTime = null;
                    mergeInputBill.CancelReconciliationTime = DateTimeHelper.GetCurrentDate();
                    mergeInputBill.IsCancelledReconciliation = true;
                    _db.MergeInputBills.Update(mergeInputBill);

                    // 查询关联的原始进项票
                    var originalInputBillIds = await _db.MergeInputBillRelations
                        .Where(r => r.MergeInputBillId == request.MergeInputBillId)
                        .Select(r => r.InputBillId)
                        .ToListAsync();

                    if (originalInputBillIds.Count > 0)
                    {
                        // 查询原始进项票
                        var originalInputBills = await _db.InputBills
                            .Where(x => originalInputBillIds.Contains(x.Id))
                            .ToListAsync();

                        // 更新原始进项票状态为"匹配完成"并记录取消勾稽信息
                        foreach (var inputBill in originalInputBills)
                        {
                            inputBill.Status = (int)InputBillStatusEnum.CompletedReconciliation;
                            inputBill.CancelReconciliationTime = DateTimeHelper.GetCurrentDate();
                            inputBill.IsCancelledReconciliation = true;
                        }

                        _db.InputBills.UpdateRange(originalInputBills);
                        _logger.LogInformation("RevokeMatch - 更新原始进项票状态为匹配完成并记录取消勾稽信息, 数量: {Count}", originalInputBills.Count);
                    }

                    _logger.LogInformation("RevokeMatch - 更新合并进项发票状态为匹配完成, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                    // 保存所有更改
                    await _db.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();

                    // 清除缓存
                    _cacheManager.ClearAllCache(request.MergeInputBillId);

                    _logger.LogInformation("RevokeMatch - 撤销匹配成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Success(true);
                }
                catch (TimeoutException ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    // 使用撤销跟踪器执行恢复操作（重新提交）
                    await revokeTracker.RecoverAsync(_revokeRecoveryHandler);

                    _logger.LogError(ex, "RevokeMatch - 撤销匹配失败(接口调用超时60秒), MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, ex.Message);

                    return BaseResponseData<bool>.Failed(500, $"撤销匹配失败(接口调用超时60秒): {ex.Message}");
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    // 使用撤销跟踪器执行恢复操作（重新提交）
                    await revokeTracker.RecoverAsync(_revokeRecoveryHandler);

                    _logger.LogError(ex, "RevokeMatch - 撤销匹配失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, ex.Message);

                    return BaseResponseData<bool>.Failed(500, $"撤销匹配失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RevokeMatch - 撤销匹配失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                return BaseResponseData<bool>.Failed(500, $"撤销匹配失败: {ex.Message}");
            }
            finally
            {
                // 释放提交锁
                if (invoiceNumber != null)
                {
                    _cacheManager.ReleaseSubmitLock(mergeInputBillId);
                    _logger.LogInformation("RevokeMatch - 释放撤销锁, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                }
            }
        }

        /// <summary>
        /// 取消已提交勾稽
        /// </summary>
        /// <param name="request">取消已提交勾稽请求</param>
        /// <returns>取消已提交勾稽响应</returns>
        public async Task<BaseResponseData<bool>> CancelSubmittedMatch(CancelSubmittedMatchRequest request)
        {
            _logger.LogInformation("CancelSubmittedMatch - 开始取消已提交勾稽, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                request.MergeInputBillId, request.MergeInvoiceNumber);

            // 声明变量，用于在finally块中释放锁
            Guid mergeInputBillId = request.MergeInputBillId;
            string? invoiceNumber = request.MergeInvoiceNumber;

            try
            {
                // 查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .Where(x => x.Id == request.MergeInputBillId)
                    .FirstOrDefaultAsync();

                if (mergeInputBill == null)
                {
                    _logger.LogError("CancelSubmittedMatch - 合并进项发票不存在, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "合并进项发票不存在");
                }

                // 检查状态
                if (mergeInputBill.Status != (int)MergeInputBillStatusEnum.Submitted && mergeInputBill.Status != (int)MergeInputBillStatusEnum.CompletedReconciliation)
                {
                    _logger.LogWarning("CancelSubmittedMatch - 当前状态不能取消已提交勾稽, 状态: {Status}", mergeInputBill.Status);
                    return BaseResponseData<bool>.Failed(500, $"当前状态不能取消已提交勾稽，状态: {((MergeInputBillStatusEnum)mergeInputBill.Status).GetDescription()}");
                }

                // 查询提交明细
                var submitDetails = await _db.MergeInputBillSubmitDetails
                    .Where(x => x.MergeInputBillId == request.MergeInputBillId)
                    .ToListAsync();

                if (submitDetails.Count == 0)
                {
                    _logger.LogWarning("CancelSubmittedMatch - 没有匹配明细, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Failed(500, "没有匹配明细");
                }

                // 检查合并发票号是否为空
                if (string.IsNullOrEmpty(request.MergeInvoiceNumber))
                {
                    _logger.LogError("CancelSubmittedMatch - 合并发票号为空");
                    return BaseResponseData<bool>.Failed(500, "合并发票号不能为空");
                }

                // 检查是否有正在进行的撤销操作
                string? lockedInvoiceNumber = _cacheManager.GetLockedInvoiceNumber(request.MergeInputBillId);
                if (lockedInvoiceNumber != null)
                {
                    _logger.LogWarning("CancelSubmittedMatch - 单据正在撤销中，请不要重复操作, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        request.MergeInputBillId, lockedInvoiceNumber);
                    return BaseResponseData<bool>.Failed(500, $"单据\"{lockedInvoiceNumber}\"正在撤销中，请不要重复操作");
                }

                // 设置提交锁
                _cacheManager.SetSubmitLock(request.MergeInputBillId, request.MergeInvoiceNumber);
                _logger.LogInformation("CancelSubmittedMatch - 设置撤销锁, MergeInputBillId: {MergeInputBillId}, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    request.MergeInputBillId, request.MergeInvoiceNumber);

                // 开启事务
                using var transaction = await _db.Database.BeginTransactionAsync();
                _logger.LogInformation("CancelSubmittedMatch - 开启事务");

                try
                {
                    // 第一步：调用金蝶撤销多发票指定应付接口
                    _logger.LogInformation("CancelSubmittedMatch - 调用金蝶撤销多发票指定应付接口, MergeInvoiceNumber: {MergeInvoiceNumber}",
                        request.MergeInvoiceNumber);

                    var kingdeeResult = await _interfaceInvocationService.InvokeKingdeeRevokeSpecifyApFin(
                        request.MergeInputBillId, request.MergeInvoiceNumber);

                    if (kingdeeResult.Code != CodeStatusEnum.Success)
                    {
                        _logger.LogError("CancelSubmittedMatch - 调用金蝶撤销多发票指定应付接口失败, 错误: {ErrorMessage}", kingdeeResult.Message);

                        // 如果是已存在的错误，视为成功，继续执行
                        if (kingdeeResult.Message == null || !kingdeeResult.Message.Contains("已存在"))
                        {
                            return BaseResponseData<bool>.Failed(500, $"【金蝶】撤销多发票指定应付接口失败: {kingdeeResult.Message}");
                        }

                        _logger.LogWarning("CancelSubmittedMatch - 金蝶接口返回已存在错误，视为成功，继续执行");
                    }

                    // 第二步：按业务类型分组
                    var businessTypeGroups = submitDetails.GroupBy(x => x.BusinessType);
                    _logger.LogInformation("CancelSubmittedMatch - 按业务类型分组, 组数: {Count}", businessTypeGroups.Count());

                    // 根据业务类型调用不同的回滚接口
                    foreach (var group in businessTypeGroups)
                    {
                        var businessType = (BusinessType)(group.Key ?? 0);

                        _logger.LogInformation("CancelSubmittedMatch - 处理业务类型: {BusinessType}", businessType);

                        try
                        {
                            // 根据业务类型调用不同的回滚接口
                            switch (businessType)
                            {
                                case BusinessType.DistributionPurchase:
                                    // 经销购货入库回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeInventoryStoreInDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过经销购货入库回滚");
                                    }
                                    break;
                                case BusinessType.ConsignmentToPurchase:
                                    // 寄售转购货回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeConsignToPurchaseDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过寄售转购货回滚");
                                    }
                                    break;
                                case BusinessType.ServiceFeeProcurement:
                                    // 服务费采购回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeServiceFeeProcurementDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过服务费采购回滚");
                                    }
                                    break;
                                case BusinessType.DistributionTransfer:
                                    // 经销调出回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeDistributionTransferDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过经销调出回滚");
                                    }
                                    break;
                                case BusinessType.PurchaseRevision:
                                    // 购货修订回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokePurchaseRevisionDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过购货修订回滚");
                                    }
                                    break;
                                case BusinessType.ExchangeToReturn:
                                    // 换货转退货回滚
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeExchangeToReturnDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过换货转退货回滚");
                                    }
                                    break;
                                case BusinessType.LossRecognition:
                                    // 损失确认回滚 - 财务内部处理
                                    if (!string.IsNullOrEmpty(request.MergeInvoiceNumber))
                                    {
                                        await _interfaceInvocationService.InvokeRevokeLossRecognitionDetail(request.MergeInvoiceNumber);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("CancelSubmittedMatch - 合并发票号为空，跳过损失确认回滚");
                                    }
                                    break;
                                default:
                                    _logger.LogWarning("CancelSubmittedMatch - 不支持的业务类型: {BusinessType}", businessType);
                                    break;
                            }
                        }
                        catch (TimeoutException ex)
                        {
                            _logger.LogError(ex, "CancelSubmittedMatch - 处理业务类型 {BusinessType} 失败(接口调用超时60秒), 错误: {ErrorMessage}", businessType, ex.Message);
                            throw new Exception($"处理业务类型 {businessType.GetDescription()} 失败(接口调用超时60秒): {ex.Message}", ex);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "CancelSubmittedMatch - 处理业务类型 {BusinessType} 失败, 错误: {ErrorMessage}", businessType, ex.Message);
                            throw;
                        }
                    }

                    // 第三步：更新合并进项发票状态并记录取消勾稽信息
                    mergeInputBill.Status = (int)MergeInputBillStatusEnum.Temporary; // 临时状态
                    mergeInputBill.CancelReconciliationTime = DateTimeHelper.GetCurrentDate();
                    mergeInputBill.IsCancelledReconciliation = true;
                    // 注意：不清空SubmitTime，保留提交时间记录，下次提交时会覆盖
                    _db.MergeInputBills.Update(mergeInputBill);

                    // 查询关联的原始进项票
                    var originalInputBillIds = await _db.MergeInputBillRelations
                        .Where(r => r.MergeInputBillId == request.MergeInputBillId)
                        .Select(r => r.InputBillId)
                        .ToListAsync();

                    if (originalInputBillIds.Count > 0)
                    {
                        // 查询原始进项票
                        var originalInputBills = await _db.InputBills
                            .Where(x => originalInputBillIds.Contains(x.Id))
                            .ToListAsync();

                        // 更新原始进项票状态为"临时"，并记录取消勾稽信息
                        foreach (var inputBill in originalInputBills)
                        {
                            inputBill.Status = (int)InputBillStatusEnum.Temporary;
                            inputBill.CancelReconciliationTime = DateTimeHelper.GetCurrentDate();
                            inputBill.IsCancelledReconciliation = true;
                        }

                        _db.InputBills.UpdateRange(originalInputBills);
                        _logger.LogInformation("CancelSubmittedMatch - 更新原始进项票状态为临时并记录取消勾稽信息, 数量: {Count}", originalInputBills.Count);
                    }

                    _logger.LogInformation("CancelSubmittedMatch - 更新合并进项发票状态为临时, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);

                    // 第四步：删除提交明细
                    _db.MergeInputBillSubmitDetails.RemoveRange(submitDetails);
                    _logger.LogInformation("CancelSubmittedMatch - 删除提交明细, 数量: {Count}", submitDetails.Count);

                    // 保存所有更改
                    await _db.SaveChangesAsync();

                    // 提交事务
                    await transaction.CommitAsync();

                    // 清除缓存
                    _cacheManager.ClearAllCache(request.MergeInputBillId);

                    _logger.LogInformation("CancelSubmittedMatch - 取消已提交勾稽成功, MergeInputBillId: {MergeInputBillId}", request.MergeInputBillId);
                    return BaseResponseData<bool>.Success(true, "取消已提交勾稽成功");
                }
                catch (TimeoutException ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    _logger.LogError(ex, "CancelSubmittedMatch - 取消已提交勾稽失败(接口调用超时60秒), MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, ex.Message);

                    return BaseResponseData<bool>.Failed(500, $"取消已提交勾稽失败(接口调用超时60秒): {ex.Message}");
                }
                catch (Exception ex)
                {
                    // 回滚事务
                    await transaction.RollbackAsync();

                    _logger.LogError(ex, "CancelSubmittedMatch - 取消已提交勾稽失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        request.MergeInputBillId, ex.Message);

                    return BaseResponseData<bool>.Failed(500, $"取消已提交勾稽失败: {ex.Message}");
                }
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, "CancelSubmittedMatch - 取消已提交勾稽失败(接口调用超时60秒), MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                return BaseResponseData<bool>.Failed(500, $"取消已提交勾稽失败(接口调用超时60秒): {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CancelSubmittedMatch - 取消已提交勾稽失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                return BaseResponseData<bool>.Failed(500, $"取消已提交勾稽失败: {ex.Message}");
            }
            finally
            {
                // 释放提交锁
                if (invoiceNumber != null)
                {
                    _cacheManager.ReleaseSubmitLock(mergeInputBillId);
                    _logger.LogInformation("CancelSubmittedMatch - 释放撤销锁, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                }
            }
        }
    }

    /// <summary>
    /// 提交处理服务接口
    /// </summary>
    public interface ISubmitHandlingService
    {
        /// <summary>
        /// 提交匹配
        /// </summary>
        /// <param name="request">提交匹配请求</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>提交匹配响应</returns>
        Task<BaseResponseData<bool>> SubmitMatch(SubmitMatchRequest request, string currentUserName);

        /// <summary>
        /// 撤销匹配
        /// </summary>
        /// <param name="request">撤销匹配请求</param>
        /// <returns>撤销匹配响应</returns>
        Task<BaseResponseData<bool>> RevokeMatch(RevokeMatchRequest request);

        /// <summary>
        /// 取消已提交勾稽
        /// </summary>
        /// <param name="request">取消已提交勾稽请求</param>
        /// <returns>取消已提交勾稽响应</returns>
        Task<BaseResponseData<bool>> CancelSubmittedMatch(CancelSubmittedMatchRequest request);
    }

    /// <summary>
    /// 取消已提交勾稽请求
    /// </summary>
    public class CancelSubmittedMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并发票号
        /// </summary>
        public string? MergeInvoiceNumber { get; set; }
    }

    /// <summary>
    /// 提交匹配请求
    /// </summary>
    public class SubmitMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 撤销匹配请求
    /// </summary>
    public class RevokeMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }
}
