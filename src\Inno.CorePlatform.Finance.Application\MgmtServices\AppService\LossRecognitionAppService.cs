﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.LossRecognition;
using Inno.CorePlatform.Finance.Application.DTOs.OA;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Models.File;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class LossRecognitionAppService : ApplicationServices.BaseAppService<LossRecognitionAppService>, ILossRecognitionAppService
    {
        //todo:可以提到基类，把queryservice提到基类，子类只能出现派生基类
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IUnitOfWork _unitOfWork;
        //todo:提到基类里去实现oa请求隔离
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IConfiguration _configuration;
        private readonly IWeaverApiClient _weaverApiClient;

        /// <summary>
        /// 推送金蝶服务
        /// </summary>
        private readonly IKingdeeApiClient _kingdeeApiClient;

        //todo:冲销可以提到基类里，然后根据不同的业务进行不同的隔离拆分
        /// <summary>
        /// 冲销服务
        /// </summary>
        private readonly IAbtmentService _abtmentService;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;

        public LossRecognitionAppService(
            FinanceDbContext db,
            DaprClient daprClient,
            IUnitOfWork unitOfWork,
            IFileGatewayClient fileGatewayClient,
            ICreditQueryService creditQueryService,
            IConfiguration configuration,
            IWeaverApiClient weaverApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IAbtmentService abtmentService,
            ICodeGenClient codeClient,
            IBDSApiClient bDSApiClient,
            ILogger<LossRecognitionAppService> logger,
            IHttpContextAccessor httpContextAccessor,
            IInventoryMgmAppService inventoryMgmAppService,
            IApplyBFFService applyBFFService
            ) : base(db, daprClient, codeClient, logger, httpContextAccessor, applyBFFService)
        {
            _unitOfWork = unitOfWork;
            _bDSApiClient = bDSApiClient;
            _configuration = configuration;
            _fileGatewayClient = fileGatewayClient;
            _weaverApiClient = weaverApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _abtmentService = abtmentService;
            _inventoryMgmAppService = inventoryMgmAppService;
        } /// <summary>
          /// 删除
          /// </summary>
          /// <param name="id"></param>
          /// <returns></returns>
        public async Task<int> DeleteItemAsync(Guid? id)
        {
            var res = 0;
            var lossItem = await _db.LossRecognitionItem.Where(p => p.Id == id).FirstOrDefaultAsync();
            if (lossItem != null)
            {
                _db.LossRecognitionItem.Remove(lossItem);
                var lossDetails = await _db.LossRecognitionDetails.Where(p => p.LossRecognitionItemId == id).ToListAsync();
                if (lossDetails != null && lossDetails.Count != 0)
                {
                    _db.LossRecognitionDetails.RemoveRange(lossDetails);
                }
                res = await _unitOfWork.CommitAsync();
            }
            return res;
        }
        /// <summary>
        /// 删除详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<int> DeleteDetailItemAsync(Guid? id)
        {
            var res = 0;
            var lossItem = await _db.LossRecognitionDetails.Where(p => p.Id == id).FirstOrDefaultAsync();
            if (lossItem != null)
            {
                var loss = await _db.LossRecognitionItem.Where(p => p.Id == lossItem.LossRecognitionItemId).FirstOrDefaultAsync();
                if (loss != null)
                {
                    loss.CreditAmount = loss.CreditAmount - lossItem.Value;
                }
                _db.LossRecognitionDetails.Remove(lossItem);
                res = await _unitOfWork.CommitAsync();
            }
            return res;
        }

        public async Task CheckCanSubmit(LossRecognitionItemPo lossItem)
        {
            //校验是否当前公司处于盘点状态
            //var stocktaking = await _db.InventoryItem.FirstOrDefaultAsync(z => z.CompanyId == lossItem.CompanyId &&
            //Data.Utilities.Utility.GetCanNotSubmitStatus().ToHashSet().Contains(z.Status));
            //if (stocktaking != null)
            //{
            //    throw new ApplicationException($"{stocktaking.CompanyName}盘点已开始，不可提交损失确认申请");
            //}
            //var sysMonth = await _bDSApiClient.GetSystemMonth(lossItem.CompanyId.ToString());
            //await CheckSysMonth(lossItem.CompanyId.Value, sysMonth);
            var inventoryRet = await _inventoryMgmAppService.InventoryCheck(lossItem.CompanyId.Value);
            if (inventoryRet.Code != CodeStatusEnum.Success)
            {
                throw new ApplicationException($"{lossItem.CompanyName}盘点已开始，不可提交损失确认申请");
            }

            var allCreditCodes = lossItem.Details.Where(z => z.Classify == LossRecognitionDetailTypeEnum.Credit).Select(z => z.BillCode).ToHashSet();
            //校验是否有结算未完成的应收添加进来了
            //step 1:获取认款明细
            var allRecognizeDetailQuery = from rrc in _db.RecognizeReceiveDetailCredits.AsNoTracking()
                                          join ri in _db.RecognizeReceiveItems.AsNoTracking() on rrc.RecognizeReceiveItemId equals ri.Id
                                          where ri.Status == RecognizeReceiveItemStatusEnum.Completed && allCreditCodes.Contains(rrc.CreditCode)
                                          select rrc;
            var allRecognizeDetails = await allRecognizeDetailQuery.ToListAsync();
            //step 2:获取所有相关冲销明细
            var allCreditAbatements = await _db.Abatements.AsNoTracking().Where(z => (allCreditCodes.Contains(z.CreditBillCode) || allCreditCodes.Contains(z.DebtBillCode)) && z.CreatedBy != "LossRecognition").ToListAsync();
            //step 3:比较冲销金额和认款金额是否一致，一致就人为没有在途
            var groupRecognizeDetail = allRecognizeDetails.GroupBy(z => z.CreditCode).ToList();
            string errorMsg = string.Empty;
            foreach (var group in groupRecognizeDetail)
            {
                var recognizeValue = group.Sum(z => z.CurrentValue);
                var abatementValue = allCreditAbatements.Where(z => z.CreditBillCode == group.Key || z.DebtBillCode == group.Key).Sum(z => z.Value);
                if (recognizeValue != abatementValue)
                {
                    if (string.IsNullOrEmpty(errorMsg))
                    {
                        errorMsg = $"应收单：{group.Key}";
                    }
                    else
                    {
                        errorMsg += $",{group.Key}";
                    }

                }
            }
            if (errorMsg.Length > 0)
            {
                throw new ApplicationException($"{errorMsg}有未结算完的数据，请耐心等待结算数据回传后在添加损失确认");
            }
        }

        private async Task<string> CheckSysMonth(Guid companyId, string sysMonth)
        {
            sysMonth = DateTime.Parse(sysMonth).ToString("yyyy-MM");
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            if (DateTime.Parse(currentMonth) > DateTime.Parse(sysMonth))
            {
                var inventory = await _db.InventoryItem.FirstOrDefaultAsync(t => t.SysMonth == sysMonth && t.CompanyId == companyId);
                if (inventory != null)
                {
                    if (inventory.Status == 2)
                    {
                        throw new ApplicationException($"财务数据盘点中，无法进行此操作");
                    }
                    if (inventory.Status == 99)
                    {
                        DateTime.TryParse(sysMonth, out DateTime billDate);
                        if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                        {
                            throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                        }
                    }
                }
            }
            else
            {
                DateTime.TryParse(sysMonth, out DateTime billDate);
                if (billDate.Year != DateTime.Now.Year || billDate.Month != DateTime.Now.Month)
                {
                    throw new ApplicationException($"操作失败，原因：公司已完成本月度盘点，不允许操作，请下个月操作");
                }
            }
            return sysMonth;
        }
        /// <summary>
        /// 提交损失确认单
        /// </summary>
        /// <param name="id">损失确认id</param>
        /// <param name="user"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SubmitItemAsync(Guid? id, string user)
        {
            var res = BaseResponseData<string>.Success("操作成功");
            var loss = await _db.LossRecognitionItem.Include(o => o.Details).AsSingleQuery().FirstOrDefaultAsync(p => p.Id == id);
            if (loss != null)
            {
                await CheckCanSubmit(loss);
                if (loss.Status != StatusEnum.waitSubmit)
                {
                    res = BaseResponseData<string>.Failed(500, "只有待提交状态才能提交");
                    return res;
                }
                var fileDirectUrls = new BizFileDirectUrlOutput();
                if (!string.IsNullOrEmpty(loss.AttachFileIds))
                {
                    var attachFileIds = loss.AttachFileIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    fileDirectUrls = await _fileGatewayClient.GetFileDirectUrlsAsync(new BizFileDirectUrlInput() { Ids = attachFileIds.Select(z => new Guid(z)).ToArray() });
                }

                var details = await _db.LossRecognitionDetails.AsNoTracking()
                 .Where(z => z.LossRecognitionItemId == loss.Id)
                 .Select(d => new
                 {
                     d.Classify,
                     d.Value
                 })
                 .ToListAsync();

                decimal creditSum = 0;
                decimal debtSum = 0;

                foreach (var detail in details)
                {
                    if (detail.Classify == LossRecognitionDetailTypeEnum.Credit)
                    {
                        creditSum += detail.Value;
                    }
                    else if (detail.Classify == LossRecognitionDetailTypeEnum.Debt)
                    {
                        debtSum += detail.Value;
                    }
                }

                var lossAmount = creditSum - debtSum;
                #region 提交oa审核
                var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                {
                    BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                    {
                        Operator = loss.CreatedBy,
                        RequestName = $"【财务-损失确认申请】[{loss.BillCode}]-{loss.CompanyName}-{loss.CustomerName}",
                        Remark = "",
                        RequestId = 0,
                        RequestLevel = 1,

                    },
                    MainData = new Gateway.Common.WeaverOA.MainData
                    {
                        FCreatorID = loss.CreatedBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/LossRecognitionApply/audit?id={loss.Id}&{DateTime.Now.Microsecond}", //PC的Iframe地址,
                        Height_m = 500,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/finance/lossApplyEmbedPage?id={loss.Id}&{DateTime.Now.Microsecond}",//手机地址
                        CpDepartment = loss.BusinessDeptId,
                        CPcompanyCode = loss.NameCode,
                        //Condition = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                        Business_id = loss.Id.ToString(),
                        IsTreasurerAudit = 1,
                        LossAmount = lossAmount
                    },

                    OtherParams = new Gateway.Common.WeaverOA.OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                if (!string.IsNullOrEmpty(loss.AttachFileIds))
                {
                    oaInput.DetailData = new Gateway.Common.WeaverOA.DetailData
                    {

                        DT1 = new List<dynamic>
                            {
                                new
                                {
                                    htxywj=new List<dynamic>
                                    {   new{
                                            fileName= (fileDirectUrls==null||fileDirectUrls.Metadatas==null)?"":fileDirectUrls.Metadatas.FirstOrDefault().Name,
                                            filePath = (fileDirectUrls==null||fileDirectUrls.Urls==null)?"":fileDirectUrls.Urls.FirstOrDefault()
                                       }
                                    }

                                }
                            }
                    };
                }
                if (string.IsNullOrEmpty(loss.OARequestId))
                {
                    var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.LossRecognitionForm);
                    if (!oaRet.Status)
                    {
                        res = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return res;
                    }
                    loss.OARequestId = oaRet.Data.Requestid.ToString();
                }
                else
                {
                    oaInput.BaseInfo.RequestId = int.Parse(loss.OARequestId);
                    var oaRet = await _weaverApiClient.SubmitWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.LossRecognitionForm);
                    if (!oaRet.Status)
                    {
                        res = BaseResponseData<string>.Failed(500, oaRet.Msg);
                        return res;
                    }
                }
                #endregion
                loss.Status = StatusEnum.waitAudit;
                loss.UpdatedTime = DateTime.Now;
                loss.UpdatedBy = user;
                _db.Update(loss);
                await _unitOfWork.CommitAsync();

            }
            else
            {
                res = BaseResponseData<string>.Failed(500, "没有找到损失确认申请或者此损失确认申请已删除");
            }
            return res;
        }

        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BizFileUploadOutput>> GetAttachFile(LossRecognitionAttachFileInput input)
        {
            var loss = await _db.LossRecognitionItem.Where(c => c.Id == input.LossRecognitionItemId).AsNoTracking().FirstOrDefaultAsync();
            var bizFiles = new List<BizFileUploadOutput>();
            if (loss != null && !string.IsNullOrEmpty(loss.AttachFileIds))
            {
                var fileIds = loss.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                return bizFiles;
            }
            return bizFiles;
        }
        /// <summary>
        /// 创建损失确认单模板
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> CreateLossRecognition(CreateLossRecognitionInput input)
        {
            try
            {
                if (input.CreditType == CreditTypeEnum.sale)
                {
                    if (input.LossRecognitionDetail.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Debt).Count() == 0)
                    {
                        return BaseResponseData<string>.Failed(500, "编辑失败,应收类型为销售应收，应付必填");
                    }
                }
                var res = BaseResponseData<string>.Success("保存成功");
                var entity = new LossRecognitionItemPo
                {
                    CompanyId = input.CompanyId,
                    CustomerId = input.CustomerId,
                    CustomerName = input.CustomerName,
                    BillDate = DateTime.Now,
                    CreatedBy = input.CurrentUser ?? "none",
                    CreatedTime = DateTimeOffset.Now,
                    CompanyName = input.CompanyName,
                    CreditType = input.CreditType,
                    BillCode = "",
                    Status = StatusEnum.waitSubmit,
                    NameCode = input.NameCode,
                    Remark = input.Remark,
                    Id = Guid.NewGuid(),
                    AttachFileIds = input.AttachFileIds,
                    BusinessDeptFullName = input.BusinessDeptFullName,
                    BusinessDeptFullPath = input.BusinessDeptFullPath,
                    BusinessDeptId = input.BusinessDeptId,
                    CreditAmount = 0,
                    OARequestId = "",
                };
                //生成单号
                var codeResult = await CreateBillCode(entity.CompanyId.Value.ToString(), deptShortName: input.BusinessDeptShortName ?? "ZXBD", billType: "RTL");
                entity.BillCode = codeResult.Item1;
                entity.BillDate = codeResult.Item2;

                var detailList = new List<LossRecognitionDetailPo>();
                if (input.LossRecognitionDetail != null && input.LossRecognitionDetail.Count() > 0)
                {
                    foreach (var item in input.LossRecognitionDetail)
                    {
                        // 校验已使用金额不能超过确认坏账金额，参考服务费逻辑
                        if (item.InvoicedAmount.HasValue && item.BadAmount.HasValue)
                        {
                            if (Math.Abs(item.InvoicedAmount.Value) > Math.Abs(item.BadAmount.Value))
                            {
                                return BaseResponseData<string>.Failed(500, $"损失确认明细{item.BillCode}已使用金额{item.InvoicedAmount.Value}不能超过确认坏账金额{item.BadAmount.Value}");
                            }
                        }

                        detailList.Add(new LossRecognitionDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            CreatedTime = DateTimeOffset.Now,
                            Classify = item.Classify,
                            CreatedBy = input.CurrentUser ?? "none",
                            LossRecognitionItemId = entity.Id,
                            BadAmount = item.BadAmount,
                            AbatmentAmount = item.AbatmentAmount,
                            LeftAmount = item.LeftAmount,
                            Value = item.Value,
                            BillCode = item.BillCode,
                            BillDate = item.BillDate,
                            CustomerId = item.CustomerId,
                            AgentName = item.AgentName,
                            CustomerName = item.CustomerName,
                            HospitalId = item.HospitalId,
                            HospitalName = item.HospitalName,
                            ProjectCode = item.ProjectCode,
                            ProjectId = item.ProjectId,
                            ProjectName = item.ProjectName,
                            CreditBillCode = item.Classify == LossRecognitionDetailTypeEnum.Credit ? item.BillCode : item.CreditBillCode,
                            InvoicedAmount = item.InvoicedAmount ?? 0, // 初始化为0，参考服务费逻辑
                        });
                    }
                }
                entity.CreditAmount = detailList.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Credit).Sum(p => p.Value);
                await _db.LossRecognitionDetails.AddRangeAsync(detailList);
                await _db.LossRecognitionItem.AddAsync(entity);
                int count = await _unitOfWork.CommitAsync();
                if (count <= 0)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败");
                }
                res.Data = entity.Id.ToString();
                return res;
            }
            catch (Exception ex)
            {

                return BaseResponseData<string>.Failed(500, "保存失败!" + ex.Message);
            }
        }
        /// <summary>
        /// 更新损失确认单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> UpdateLossRecognition(CreateLossRecognitionInput input)
        {

            try
            {
                var res = BaseResponseData<string>.Success("编辑成功");
                var lossItem = await _db.LossRecognitionItem.FirstOrDefaultAsync(p => p.Id == input.Id && p.Status != StatusEnum.Complate);
                if (lossItem == null)
                {
                    return BaseResponseData<string>.Failed(500, "编辑失败,没有找到当前损失确认申请单");
                }

                if (lossItem.Status != StatusEnum.waitSubmit)
                {
                    return BaseResponseData<string>.Failed(500, "编辑失败,当前状态不允许编辑");
                }
                if (input.CreditType == CreditTypeEnum.sale)
                {
                    if (input.LossRecognitionDetail.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Debt).Count() == 0)
                    {
                        return BaseResponseData<string>.Failed(500, "编辑失败,应收类型为销售应收，应付必填");
                    }
                }
                lossItem.CompanyId = input.CompanyId;
                lossItem.CustomerId = input.CustomerId;
                lossItem.CustomerName = input.CustomerName;
                lossItem.CompanyName = input.CompanyName;
                lossItem.CreditType = input.CreditType;
                lossItem.NameCode = input.NameCode;
                lossItem.Remark = input.Remark;
                lossItem.AttachFileIds = input.AttachFileIds;
                lossItem.BusinessDeptFullName = input.BusinessDeptFullName;
                lossItem.BusinessDeptFullPath = input.BusinessDeptFullPath;
                lossItem.BusinessDeptId = input.BusinessDeptId;
                lossItem.UpdatedBy = input.CurrentUser;
                lossItem.UpdatedTime = DateTimeOffset.Now;
                var delDetail = await _db.LossRecognitionDetails.Where(p => p.LossRecognitionItemId == lossItem.Id).ToListAsync();
                _db.LossRecognitionDetails.RemoveRange(delDetail);//删除原来的明细
                var detailList = new List<LossRecognitionDetailPo>();
                if (input.LossRecognitionDetail != null && input.LossRecognitionDetail.Count() > 0) //带发票明细
                {
                    foreach (var item in input.LossRecognitionDetail)
                    {
                        // 校验已使用金额不能超过确认坏账金额，参考服务费逻辑
                        if (item.InvoicedAmount.HasValue && item.BadAmount.HasValue)
                        {
                            if (Math.Abs(item.InvoicedAmount.Value) > Math.Abs(item.BadAmount.Value))
                            {
                                return BaseResponseData<string>.Failed(500, $"损失确认明细{item.BillCode}已使用金额{item.InvoicedAmount.Value}不能超过确认坏账金额{item.BadAmount.Value}");
                            }
                        }

                        detailList.Add(new LossRecognitionDetailPo()
                        {
                            Id = Guid.NewGuid(),
                            CreatedTime = DateTimeOffset.Now,
                            Classify = item.Classify,
                            CreatedBy = input.CurrentUser ?? "none",
                            LossRecognitionItemId = lossItem.Id,
                            BadAmount = item.BadAmount,
                            AbatmentAmount = item.AbatmentAmount,
                            LeftAmount = item.LeftAmount,
                            Value = item.Value,
                            BillCode = item.BillCode,
                            BillDate = item.BillDate,
                            CustomerId = item.CustomerId,
                            AgentName = item.AgentName,
                            CustomerName = item.CustomerName,
                            HospitalId = item.HospitalId,
                            HospitalName = item.HospitalName,
                            ProjectCode = item.ProjectCode,
                            ProjectId = item.ProjectId,
                            ProjectName = item.ProjectName,
                            CreditBillCode = item.Classify == LossRecognitionDetailTypeEnum.Credit ? item.BillCode : item.CreditBillCode,
                            InvoicedAmount = item.InvoicedAmount ?? 0, // 初始化为0，参考服务费逻辑
                        });
                    }
                }
                lossItem.CreditAmount = detailList.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Credit).Sum(p => p.Value);
                await _db.LossRecognitionDetails.AddRangeAsync(detailList);//添加新的明细
                _db.LossRecognitionItem.Update(lossItem);
                int count = await _unitOfWork.CommitAsync();
                if (count <= 0)
                {
                    return BaseResponseData<string>.Failed(500, "编辑失败");
                }
                res.Data = lossItem.Id.ToString();
                return res;
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, "编辑失败!" + ex.Message);
            }
        }

        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<string>> UpdateStatus(Guid id, StatusEnum statusEnum)
        {

            var ret = BaseResponseData<string>.Success("操作成功！");
            var loss = await _db.LossRecognitionItem.Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            if (loss != null)
            {
                loss.Status = statusEnum;
                loss.UpdatedTime = DateTime.Now;
                _db.LossRecognitionItem.Update(loss);
            }
            else
            {
                return BaseResponseData<string>.Failed(500, "获取损失确认单失败!损失确认单Id:" + id);
            }
            await _unitOfWork.CommitAsync();
            return ret;
        }
        /// <summary>
        ///审核通过
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> AuditApproved(Guid id)
        {
            // 1:推送应收成功，2:推送应付成功
            var step = 0;
            //回滚金蝶的应收数据
            var rollBackLossRecognitionCreditInput = new List<RollBackLossRecognitionCreditInput>();
            //回滚金蝶的应付数据
            var rollBackLossRecognitionDebtInput = new List<RollBackLossRecognitionDebtInput>();
            try
            {
                var ret = BaseResponseData<string>.Success("操作成功！");
                var loss = await _db.LossRecognitionItem.FirstOrDefaultAsync(p => p.Id == id);
                if (loss == null)
                {
                    return BaseResponseData<string>.Failed(500, "获取损失确认单失败!损失确认单Id:" + id);
                }

                // 检查是否正在盘点，如果正在盘点则抛出异常
                await CheckInventoryStatusBeforeApproval(loss);

                // 如果已经是完成状态，直接返回成功
                if (loss.Status == StatusEnum.Complate)
                {
                    return BaseResponseData<string>.Success("损失确认单已完成！");
                }
                var detail = await _db.LossRecognitionDetails.Where(p => p.LossRecognitionItemId == id).ToListAsync();
                if (detail.Count == 0)
                {
                    return BaseResponseData<string>.Failed(500, "获取损失确认单详情失败!损失确认单Id:" + id);
                }
                var lossCreditDetails = detail.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Credit).ToList();
                if (lossCreditDetails.Count == 0)
                {
                    return BaseResponseData<string>.Failed(500, "损失确认单未包含应收!损失确认单Id:" + id);
                }

                //推送金蝶的应收数据
                var inputKeendeeCreditList = new List<SaveLossRecognitionCreditInput>();
                //推送金蝶的应付数据
                var inputKeendeeDebtList = new List<SaveLossRecognitionDebtInput>();

                #region 产生负数应收，并分配应收明细,如果没有开票明细，就无法开票
                //拿所有损失确认里的应收单号
                var oldCreditCodes = lossCreditDetails.Select(p => p.BillCode).ToHashSet();
                //获取报损单下所有的原始应收
                var allOldCredits = await _db.Credits.Include(x => x.CreditDetails).AsSingleQuery().Where(p => oldCreditCodes.Contains(p.BillCode)).ToListAsync();
                //获取原始应收所有的冲销记录，用于更新老的应收状态
                var allOldCreditAbatements = await _db.Abatements.Where(p => oldCreditCodes.Contains(p.DebtBillCode) || oldCreditCodes.Contains(p.CreditBillCode)).AsNoTracking().ToListAsync();
                var companyInfo = (await _applyBFFService.GetCompanyInfosAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { loss.CompanyId.ToString() } })).FirstOrDefault();
                if (companyInfo == null)
                {
                    throw new ApplicationException("公司信息不存在");
                }
                //所有新增的应收应付冲销记录
                var newAllAbatementList = new List<AbatementPo>();
                //应付冲销记录
                var newDebtAbatementList = new List<AbatementPo>();
                //新增负数应付记录
                var newCreditList = new List<CreditPo>();
                //新增负数应收记录
                var newDebtList = new List<DebtPo>();
                //新增付款计划
                var newDebtDetailList = new List<DebtDetailPo>();
                //新增付款计划执行明细
                var newDebtDetailExecuteList = new List<DebtDetailExcutePo>();
                //应收应付单号序列号
                int childCodeIndex = 1;
                //生成负数的应收
                foreach (var lossCredit in lossCreditDetails)
                {
                    var matchOldCredit = allOldCredits.FirstOrDefault(z => z.BillCode == lossCredit.BillCode);
                    var newCredit = Data.Utilities.Utility.DeepCopyCredit(matchOldCredit, "损失确认单");
                    //生成单号
                    //var codeResult = await CreateBillCode(companyInfo.Id.ToString(), companyInfo, loss.BillCode.Split('-').FirstOrDefault() ?? "ZXBD", billType: "RTLC");
                    //newCredit.BillCode = codeResult.Item1;
                    //newCredit.BillDate = codeResult.Item2;
                    newCredit.BillCode = loss.BillCode + "-" + childCodeIndex.ToString("0000");
                    newCredit.BillDate = loss.BillDate;
                    newCredit.CreatedBy = lossCredit.CreatedBy;
                    newCredit.UpdatedBy = lossCredit.UpdatedBy;
                    newCredit.Value = -lossCredit.LeftAmount;//新应收金额是负数
                    newCredit.AbatedStatus = AbatedStatusEnum.Abated;//新应收直接状态就是已冲销
                    // #115714 初始应收确认损失申请生成的应收需要标为无需开票
                    if (matchOldCredit.CreditType == CreditTypeEnum.origin)
                    {
                        newCredit.IsNoNeedInvoice = IsNoNeedInvoiceEnum.NoNeed;
                    }
                    //老应收记录报损金额
                    if (matchOldCredit.LossRecognitionValue.HasValue)
                        matchOldCredit.LossRecognitionValue += lossCredit.LeftAmount;
                    else
                        matchOldCredit.LossRecognitionValue = lossCredit.LeftAmount;
                    //如果制单的时候不允许在途应收能够损失确认，这里就不需要计算得出老应收冲销状态，可以直接把老应收冲销状态改为已冲销
                    var abates = allOldCreditAbatements.Where(p => p.DebtBillCode == matchOldCredit.BillCode || p.CreditBillCode == matchOldCredit.BillCode).ToList();
                    if (abates != null && (abates.Sum(p => p.Value) + lossCredit.LeftAmount) == matchOldCredit.Value)
                    {
                        matchOldCredit.AbatedStatus = AbatedStatusEnum.Abated;
                    }
                    //整体报损金额分配到应收明细，先将原始应收明细按金额大小逆排序
                    matchOldCredit.CreditDetails = matchOldCredit.CreditDetails.OrderBy(z => z.Amount).ToList();
                    //生成应收明细
                    var leftNewCreditAmount = lossCredit.LeftAmount;
                    foreach (var oldCreditDetail in matchOldCredit.CreditDetails)
                    {
                        //计算报损应收的应收明细应该怎么分配
                        if (oldCreditDetail.Amount >= leftNewCreditAmount)
                        {
                            //计算数量
                            var calcQuantity = -Math.Round(leftNewCreditAmount / oldCreditDetail.Price, 10);
                            //深拷贝应收明细
                            newCredit.CreditDetails.Add(Data.Utilities.Utility.DeepCopyCreditDetail(oldCreditDetail, newCredit, -leftNewCreditAmount, calcQuantity));
                            break;
                        }
                        else
                        {
                            leftNewCreditAmount -= oldCreditDetail.Amount.Value;
                            //深拷贝应收明细
                            newCredit.CreditDetails.Add(Data.Utilities.Utility.DeepCopyCreditDetail(oldCreditDetail, newCredit, -oldCreditDetail.Amount.Value, -oldCreditDetail.Quantity));
                        }

                    }
                    newCreditList.Add(newCredit);
                    childCodeIndex++;
                    //加入冲销表
                    newAllAbatementList.Add(new AbatementPo()
                    {
                        DebtBillCode = matchOldCredit.BillCode,
                        DebtType = "credit",
                        CreditBillCode = newCredit.BillCode,
                        CreditType = "credit",
                        Abtdate = DateTime.Now,
                        Value = Math.Abs(newCredit.Value),
                        CreatedTime = DateTime.Now,
                        CreatedBy = "LossRecognition"
                    });
                    //审批通过后，将应收余额的值传给金蝶。
                    inputKeendeeCreditList.Add(new SaveLossRecognitionCreditInput()
                    {
                        asstact_number = string.IsNullOrEmpty(newCredit.CustomerId.ToString().ToUpper()) ? loss.CustomerId.ToString().ToUpper() : newCredit.CustomerId.ToString().ToUpper(),
                        billno = newCredit.BillCode,
                        billtype_number = KingdeeHelper.TransferCreditType(newCredit.CreditType.ToString()),
                        bizdate = newCredit.BillDate.Value.ToString("yyyy-MM-dd"),
                        org_number = newCredit.NameCode,
                        jfzx_businessnumber = newCredit.BusinessDeptId,
                        jfzx_ordernumber = loss.BillCode,
                        jfzx_iscofirm = true,
                        jfzx_creator = newCredit.CreatedBy ?? "none",
                        jfzx_serviceid = newCredit.ServiceName,
                        associatedNumber = matchOldCredit.BillCode,
                        recamount = Math.Round(newCredit.Value, 2),
                        bookDate = DateTime.Now.ToString("yyyy-MM-dd"),
                        exchangerate = 1,
                        exratetable_number = "ERT-01",
                        paymode = "CREDIT",
                        payproperty_number = "1001",
                        asstacttype = "bd_customer",
                        recorg_number = newCredit.NameCode,
                        currency_number = "CNY",
                        department_number = newCredit.NameCode,
                        salesorg_number = newCredit.NameCode,
                    });
                    //回滚应收参数组装
                    rollBackLossRecognitionCreditInput.Add(new RollBackLossRecognitionCreditInput
                    {
                        billNo = newCredit.BillCode,
                    });
                }

                #endregion

                #region 产生负数应付
                //销售应收才会关联应付，生成原始应付的负数应付
                //#114778新增初始应收
                if (loss.CreditType == CreditTypeEnum.sale || loss.CreditType == CreditTypeEnum.origin || loss.CreditType == CreditTypeEnum.selforder) //销售应收 & 初始应收
                {
                    //应付
                    var detailDebts = detail.Where(p => p.Classify == LossRecognitionDetailTypeEnum.Debt).ToList();
                    //损失明细中记录的要损失确认的应付单号
                    var oldDebtCodes = detailDebts.Select(p => p.BillCode).ToHashSet();
                    if (detailDebts.Count == 0 && loss.CreditType == CreditTypeEnum.sale)
                    {
                        return BaseResponseData<string>.Failed(500, "获取损失确认单应付详情失败!损失确认单Id:" + id);
                    }

                    var oldDebts = await _db.Debts.Include(o => o.DebtDetails).Where(p => oldDebtCodes.Contains(p.BillCode)).ToListAsync();
                    var abatementDebtInputs = new List<LossAbtForDebtInput>();
                    foreach (var item in detailDebts)
                    {
                        var oldDebt = oldDebts.FirstOrDefault(p => p.BillCode == item.BillCode);
                        //0元定损金额
                        if (item.BadAmount == 0)
                        {
                            if (oldDebt != null)
                            {
                                //全部的付款计划里，没有预计付款日期的都写上预计付款日期
                                oldDebt.DebtDetails.ForEach(z =>
                                {
                                    if (!z.ProbablyPayTime.HasValue)
                                        z.ProbablyPayTime = DateTime.Now;
                                });
                                continue;
                            }
                        }

                        var newDebt = CopyNewDebt(oldDebt);
                        newDebt.CreatedBy = item.CreatedBy;
                        newDebt.UpdatedBy = item.UpdatedBy;
                        newDebt.Value = -item.BadAmount.Value;
                        newDebt.RMBAmount = newDebt.Value;
                        newDebt.AbatedStatus = AbatedStatusEnum.Abated;

                        //生成单号
                        //var codeResult = await CreateBillCode(companyInfo.Id.ToString(), companyInfo, loss.BillCode.Split('-').FirstOrDefault() ?? "ZXBD", billType: "RTLD");
                        //newDebt.BillCode = codeResult.Item1;
                        //newDebt.BillDate = codeResult.Item2;
                        newDebt.BillCode = loss.BillCode + "-" + childCodeIndex.ToString("0000");
                        newDebt.BillDate = loss.BillDate;
                        //更新老应付上的损失金额字段
                        if (!oldDebt.LossAgentBearValue.HasValue)
                        {
                            oldDebt.LossAgentBearValue = item.BadAmount.Value;
                        }
                        else
                        {
                            oldDebt.LossAgentBearValue += item.BadAmount.Value;
                        }
                        newDebtList.Add(newDebt);
                        childCodeIndex++;
                        abatementDebtInputs.Add(new LossAbtForDebtInput()
                        {
                            OldDebt = oldDebt,
                            NewDebt = newDebt
                        });

                        //推送金蝶参数拼装
                        inputKeendeeDebtList.Add(new SaveLossRecognitionDebtInput()
                        {
                            asstActNumber = newDebt.AgentId.ToString().ToUpper(),
                            billNo = newDebt.BillCode,
                            bizdate = newDebt.BillDate,
                            orgNumber = newDebt.NameCode,
                            payOrgNumber = newDebt.NameCode,
                            billTypeIdNumber = KingdeeHelper.TransferDebtType(newDebt.DebtType.Value),
                            businessNumber = newDebt.BusinessDeptId,
                            orderNumber = loss.BillCode,
                            creator = newDebt.CreatedBy,
                            currencyNumber = newDebt.CoinCode ?? "CNY",
                            priceTaxTotal = newDebt.Value,
                            amount = newDebt.Value,
                            associatedNumber = item.BillCode,
                            payPropertyNumber = "2001",
                            asstActType = "bd_supplier",
                            exchangeRate = 1,
                            bookDate = DateTime.Now.ToString("yyyy-MM-dd"),
                            exRateTableNumber = "ERT-01",
                            purMode = "CREDIT"
                        });

                        //回滚应付
                        rollBackLossRecognitionDebtInput.Add(new RollBackLossRecognitionDebtInput
                        {
                            billNo = newDebt.BillCode
                        });
                    }
                    //正负应付冲销
                    var abatementResult = _abtmentService.LossGenerateAbtForDebtAsync(abatementDebtInputs, loss.CreatedBy);
                    //加入总冲销记录
                    newAllAbatementList.AddRange(abatementResult.AddDebtAbatementDetails);
                    //加入付款计划添加明细
                    newDebtDetailList.AddRange(abatementResult.AddDebtDetails);
                    //加入付款计划执行明细
                    newDebtDetailExecuteList.AddRange(abatementResult.AddDebtDetailExcutes);
                }
                #endregion
                using (var trans = await _db.Database.BeginTransactionAsync())
                {
                    loss.Status = StatusEnum.Complate;
                    //添加总冲销记录
                    if (newAllAbatementList.Count > 0)
                    {
                        await _db.Abatements.AddRangeAsync(newAllAbatementList);
                    }
                    //添加新应付记录
                    if (newDebtList.Count > 0)
                    {
                        await _db.Debts.AddRangeAsync(newDebtList);
                    }
                    //添加应收记录
                    if (newCreditList.Count > 0)
                    {
                        await _db.Credits.AddRangeAsync(newCreditList);
                    }
                    //添加付款计划
                    if (newDebtDetailList.Count > 0)
                    {
                        await _db.DebtDetails.AddRangeAsync(newDebtDetailList);
                    }
                    //添加付款计划执行明细
                    if (newDebtDetailExecuteList.Count > 0)
                    {
                        await _db.DebtDetailExcutes.AddRangeAsync(newDebtDetailExecuteList);
                    }
                    await _db.SaveChangesAsync();
                    //记录日志
                    await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = $"损失确认-调用金蝶应收-Id:{loss.Id}-code:{loss.BillCode}", Content = inputKeendeeCreditList.ToJson(), Operate = "应收入参" });
                    //推送金蝶应收
                    var kingdeeCreditRes = await _kingdeeApiClient.SaveLossRecognitionCredit(inputKeendeeCreditList);
                    //记录日志
                    await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = $"损失确认-调用金蝶应收返回-Id:{loss.Id}-code:{loss.BillCode}", Content = kingdeeCreditRes.ToJson(), Operate = "应收出参" });

                    if (kingdeeCreditRes.Code != CodeStatusEnum.Success && kingdeeCreditRes.ErrorCode != 800)
                    {
                        return BaseResponseData<string>.Failed(500, $"推送金蝶应收失败：{kingdeeCreditRes.Message}");
                    }
                    step = 1;
                    //推送金蝶应付
                    if (inputKeendeeDebtList != null && inputKeendeeDebtList.Count > 0)
                    {

                        await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = $"损失确认-调用金蝶应付-Id:{loss.Id}-code:{loss.BillCode}", Content = inputKeendeeDebtList.ToJson(), Operate = "应付入参" });
                        var kingdeeDebtRes = await _kingdeeApiClient.SaveLossRecognitionDebt(inputKeendeeDebtList);
                        await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = $"损失确认-调用金蝶应付返回-Id:{loss.Id}-code:{loss.BillCode}", Content = kingdeeDebtRes.ToJson(), Operate = "应付出参" });

                        if (kingdeeDebtRes.Code != CodeStatusEnum.Success && kingdeeDebtRes.ErrorCode != 800)
                        {
                            var rollBackCreditRet = await RollBackCredit(rollBackLossRecognitionCreditInput);
                            if (rollBackCreditRet.Code != CodeStatusEnum.Success)
                            {
                                return BaseResponseData<string>.Failed(500, $"推送应付金蝶失败：{kingdeeDebtRes.Message}，且回滚应收失败：{rollBackCreditRet.Message}");
                            }
                            else
                            {
                                return BaseResponseData<string>.Failed(500, $"推送应付金蝶失败：{kingdeeDebtRes.Message}");
                            }
                        }
                    }
                    step = 2;
                    await trans.CommitAsync();
                }
                return ret;
            }
            catch (Exception ex)
            {
                await _daprClient.PublishEventAsync(DomainConstants.Default_PubSubName, DomainConstants.FINANCE_SAVE_SUBLOG, new SaveSubLogInput { Source = "损失确认-全局事务报错", Content = ex.Message, Operate = "OA回调触发" });
                if (step > 0)
                {
                    var rollBackCreditRet = await RollBackCredit(rollBackLossRecognitionCreditInput);
                    if (rollBackCreditRet.Code != CodeStatusEnum.Success)
                    {
                        return BaseResponseData<string>.Failed(500, $"损失确认申请审批通过失败：{ex.Message}，且回滚应收失败：{rollBackCreditRet.Message}");
                    }
                }
                if (step > 1)
                {
                    var rollBackDebtRet = await RollBackDebt(rollBackLossRecognitionDebtInput);
                    if (rollBackDebtRet.Code != CodeStatusEnum.Success)
                    {
                        return BaseResponseData<string>.Failed(500, $"损失确认申请审批通过失败：{ex.Message}，且回滚应付失败：{rollBackDebtRet.Message}");
                    }
                }
                return BaseResponseData<string>.Failed(500, $"损失确认申请审批通过失败：{ex.Message}");
            }
        }

        #region 回滚
        /// <summary>
        /// 回滚应收
        /// </summary>
        private async Task<BaseResponseData<int>> RollBackCredit(List<RollBackLossRecognitionCreditInput> rollBackLossRecognitionCreditInput)
        {
            //回滚金蝶应收
            var ret = await _kingdeeApiClient.RollBackLossRecognitionCredit(rollBackLossRecognitionCreditInput);
            return ret;
        }
        /// <summary>
        /// 回滚应付
        /// </summary>
        private async Task<BaseResponseData<int>> RollBackDebt(List<RollBackLossRecognitionDebtInput> rollBackLossRecognitionDebtInput)
        {
            //回滚金蝶应付
            var ret = await _kingdeeApiClient.RollBackLossRecognitionDebt(rollBackLossRecognitionDebtInput);
            return ret;
        }
        #endregion

        private DebtPo CopyNewDebt(DebtPo oldDebt)
        {
            if (oldDebt == null)
                return null;
            // 复制原 Credit 对象
            var newDebt = new DebtPo
            {
                BusinessDeptFullName = oldDebt.BusinessDeptFullName,
                BusinessDeptFullPath = oldDebt.BusinessDeptFullPath,
                BusinessDeptId = oldDebt.BusinessDeptId,
                CustomerId = oldDebt.CustomerId,
                AgentId = oldDebt.AgentId,
                AgentName = oldDebt.AgentName,
                CompanyId = oldDebt.CompanyId,
                CompanyName = oldDebt.CompanyName,
                CustomerName = oldDebt.CustomerName,
                NameCode = oldDebt.NameCode,
                Id = Guid.NewGuid(),
                CreatedBy = oldDebt.CreatedBy,
                CreatedTime = DateTime.Now,
                DebtType = DebtTypeEnum.lossrecognition,
                CoinCode = oldDebt.CoinCode,
                CoinName = oldDebt.CoinName,
                AutoTypeName = oldDebt.AutoTypeName,
                AccountPeriodScale = oldDebt.AccountPeriodScale,
                IsInnerAgent = oldDebt.IsInnerAgent,
                OrderNo = oldDebt.OrderNo,
                ProjectCode = oldDebt.ProjectCode,
                ProducerOrderNo = oldDebt.ProducerOrderNo,
                AbatedStatus = AbatedStatusEnum.NonAbate,
                ProjectId = oldDebt.ProjectId,
                Note = oldDebt.Note,
                PerPaymentCodes = oldDebt.PerPaymentCodes,
                RelateCode = oldDebt.RelateCode,
                ProjectName = oldDebt.ProjectName,
                PurchaseCode = oldDebt.PurchaseCode,
                ServiceId = oldDebt.ServiceId,
                TaxRate = oldDebt.TaxRate,
                ServiceName = oldDebt.ServiceName,
                PurchaseContactNo = oldDebt.PurchaseContactNo,
            };
            return newDebt;
        }

        /// <summary>
        /// 检查盘点状态，如果正在盘点则不允许归档
        /// </summary>
        /// <param name="loss">损失确认单</param>
        /// <returns></returns>
        private async Task CheckInventoryStatusBeforeApproval(LossRecognitionItemPo loss)
        {
            try
            {
                _logger.LogInformation("损失确认单 {BillCode} 开始检查盘点状态", loss.BillCode);

                // 检查是否正在盘点（状态为2）
                var inventoryCheckResult = await _inventoryMgmAppService.InventoryCheck(loss.CompanyId.Value);

                if (inventoryCheckResult.Code != CodeStatusEnum.Success)
                {
                    var errorMessage = $"损失确认单 {loss.BillCode} 归档失败：公司 {loss.CompanyName} 正在盘点中，盘点期间不允许损失确认单归档";
                    _logger.LogError("损失确认归档检查失败 - {ErrorMessage}", errorMessage);
                    throw new ApplicationException(errorMessage);
                }

                _logger.LogInformation("损失确认单 {BillCode} 盘点状态检查通过", loss.BillCode);
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查损失确认单 {loss.BillCode} 盘点状态时发生异常：{ex.Message}";
                _logger.LogError(ex, "损失确认归档盘点状态检查异常 - 损失确认单: {BillCode}, 公司: {CompanyName}, 错误: {ErrorMessage}",
                    loss.BillCode, loss.CompanyName, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }

        /// <summary>
        /// 完成盘点后恢复损失确认单据处理
        /// 参考采购完成盘点逻辑，将损失确认（临时草稿、待审核）状态的单据日期修改到当前日期，单号也需要修改到下一个月，然后同时调用oa接口，将原来的标题修改成修改后单据对应的内容
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="targetSysMonth">目标系统月度</param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> ResumeLossRecognitionAfterInventory(Guid companyId, string targetSysMonth)
        {
            try
            {
                _logger.LogInformation("开始处理公司 {CompanyId} 完成盘点后的损失确认单据恢复，目标系统月度: {TargetSysMonth}", companyId, targetSysMonth);

                // 查询待处理的损失确认单据（临时草稿=0、待审核=1状态）
                var pendingLossItems = await _db.LossRecognitionItem
                    .Where(x => x.CompanyId == companyId &&
                               (x.Status == StatusEnum.waitSubmit || x.Status == StatusEnum.waitAudit))
                    .ToListAsync();

                if (!pendingLossItems.Any())
                {
                    _logger.LogInformation("公司 {CompanyId} 没有需要处理的损失确认单据", companyId);
                    return BaseResponseData<string>.Success("没有需要处理的损失确认单据");
                }

                _logger.LogInformation("公司 {CompanyId} 找到 {Count} 个待处理的损失确认单据", companyId, pendingLossItems.Count);

                // 获取公司信息
                var companyInfo = (await _applyBFFService.GetCompanyInfosAsync(new CompetenceCenter.BDSCenter.BDSBaseInput { ids = new List<string> { companyId.ToString() } })).FirstOrDefault();
                if (companyInfo == null)
                {
                    var errorMsg = $"获取公司信息失败，公司ID: {companyId}";
                    _logger.LogError(errorMsg);
                    return BaseResponseData<string>.Failed(500, errorMsg);
                }

                var successCount = 0;
                var failureCount = 0;
                var errors = new List<string>();
                var oaTitleUpdateList = new List<UpdateOATitleInput>(); // 直接构建OA标题更新列表

                foreach (var lossItem in pendingLossItems)
                {
                    try
                    {
                        _logger.LogInformation("开始处理损失确认单据: {BillCode}, 状态: {Status}", lossItem.BillCode, lossItem.Status);

                        // 生成新的单号（使用目标系统月度）
                        var deptShortName = lossItem.BillCode?.Split('-').FirstOrDefault() ?? "ZXBD";
                        var codeResult = await CreateBillCode(companyId.ToString(), companyInfo, deptShortName, "RTL", targetSysMonth);
                        var newBillCode = codeResult.Item1;
                        var newBillDate = codeResult.Item2; // 使用当前日期

                        _logger.LogInformation("损失确认单据 {OldBillCode} 生成新单号: {NewBillCode}, 新日期: {NewBillDate}",
                            lossItem.BillCode, newBillCode, newBillDate);

                        // 更新单据信息
                        var oldBillCode = lossItem.BillCode;
                        lossItem.BillCode = newBillCode;
                        lossItem.BillDate = newBillDate;
                        lossItem.UpdatedTime = DateTimeOffset.Now;
                        lossItem.UpdatedBy = "System";

                        // 直接构建OA标题更新输入参数
                        if (!string.IsNullOrEmpty(lossItem.OARequestId))
                        {
                            var updateOATitleInput = new UpdateOATitleInput
                            {
                                RequestId = long.Parse(lossItem.OARequestId),
                                Name = $"【财务-损失确认申请】[{lossItem.BillCode}]-{lossItem.CompanyName}-{lossItem.CustomerName}"
                            };
                            oaTitleUpdateList.Add(updateOATitleInput);

                            _logger.LogInformation("准备更新损失确认单据 {BillCode} 的OA标题，OA请求ID: {OARequestId}",
                                lossItem.BillCode, lossItem.OARequestId);
                        }

                        _db.LossRecognitionItem.Update(lossItem);
                        successCount++;

                        _logger.LogInformation("损失确认单据处理成功: {OldBillCode} -> {NewBillCode}", oldBillCode, newBillCode);
                    }
                    catch (Exception ex)
                    {
                        failureCount++;
                        var errorMsg = $"处理损失确认单据 {lossItem.BillCode} 失败: {ex.Message}";
                        errors.Add(errorMsg);
                        _logger.LogError(ex, "处理损失确认单据失败: {BillCode}, 错误: {ErrorMessage}", lossItem.BillCode, ex.Message);
                    }
                }

                // 保存更改
                if (successCount > 0)
                {
                    await _unitOfWork.CommitAsync();
                    _logger.LogInformation("公司 {CompanyId} 损失确认单据处理完成，成功: {SuccessCount}, 失败: {FailureCount}",
                        companyId, successCount, failureCount);

                    // 批量更新OA标题
                    if (oaTitleUpdateList.Count > 0)
                    {
                        try
                        {
                            await PublishOATitleUpdateEvent(oaTitleUpdateList);
                            _logger.LogInformation("公司 {CompanyId} 批量发布OA标题更新事件完成，共 {Count} 个单据", companyId, oaTitleUpdateList.Count);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "公司 {CompanyId} 批量发布OA标题更新事件失败: {ErrorMessage}", companyId, ex.Message);
                            // OA标题更新失败不影响主流程，只记录错误
                        }
                    }
                }

                var resultMessage = $"处理完成，成功: {successCount}, 失败: {failureCount}";
                if (errors.Count > 0)
                {
                    resultMessage += $"，错误详情: {string.Join("; ", errors)}";
                }

                return failureCount == 0
                    ? BaseResponseData<string>.Success(resultMessage)
                    : BaseResponseData<string>.Failed(500, resultMessage);
            }
            catch (Exception ex)
            {
                var errorMsg = $"处理公司 {companyId} 损失确认单据恢复时发生异常: {ex.Message}";
                _logger.LogError(ex, "损失确认单据恢复处理异常 - 公司: {CompanyId}, 错误: {ErrorMessage}", companyId, ex.Message);
                return BaseResponseData<string>.Failed(500, errorMsg);
            }
        }







        /// <summary>
        /// 发布OA标题更新事件
        /// </summary>
        /// <param name="updateList">更新列表</param>
        /// <returns></returns>
        private async Task PublishOATitleUpdateEvent(List<UpdateOATitleInput> updateList)
        {
            await _daprClient.PublishEventAsync("pubsub-default", "all-sell-updateoarequestname", updateList);
        }
    }
}
