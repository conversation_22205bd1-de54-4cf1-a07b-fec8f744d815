using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    /// <summary>
    /// 多发票指定应付输入参数
    /// </summary>
    public class ManyInvoiceSpecifyApFinInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceno { get; set; }

        /// <summary>
        /// 核心单据号
        /// </summary>
        public string coreBillNo { get; set; }

        /// <summary>
        /// 对应应付明细
        /// </summary>
        public List<ManyInvoiceSpecifyApFinEntryDto> finentry { get; set; }
    }

    /// <summary>
    /// 多发票指定应付明细
    /// </summary>
    public class ManyInvoiceSpecifyApFinEntryDto
    {
        /// <summary>
        /// 关联日期
        /// </summary>
        public string associatedDate { get; set; }


        /// <summary>
        /// 应付单号
        /// </summary>
        public string finNum { get; set; }

        /// <summary>
        /// 对应应付金额
        /// </summary>
        public decimal f_usedamt { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string? operationType { get; set; } = "";

        /// <summary>
        /// 操作人
        /// </summary>
        public string user { get; set; }
    }
}
