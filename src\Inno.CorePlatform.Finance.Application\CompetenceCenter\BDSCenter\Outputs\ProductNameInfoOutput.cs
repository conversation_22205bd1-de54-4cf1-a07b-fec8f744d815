﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{

    public class ProductNameInfoData
    {

        public int Total { get; set; }
        public List<ProductNameInfoOutput> Data { get; set; }

    }
    public class ProductNameInfoOutput
    {
        /// <summary> 
        /// 货号类型:0-器械货号,1-工具货号,2-服务货号,3-药品货号
        /// </summary>
        public int? ifTool { get; set; }
        public Guid? classificationGuid { get; set; }
        public Guid? classificationNewGuid { get; set; }
        public Guid? producerId { get; set; }
        public string? producerName { get; set; }
        public Guid? productNameId { get; set; }
        public string? productName { get; set; }
        public Guid? productId { get; set; }
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? taxClassCode { get; set; }
        /// <summary>
        /// 税控分类编码
        /// </summary>
        public string? taxControlCode { get; set;}
    }
    public class ProductNoOuput
    {
        /// <summary> 
        /// 货号类型:0-器械货号,1-工具货号,2-服务货号,3-药品货号
        /// </summary>
        public int? ifTool { get; set; }
        public Guid productId { get; set; }
        public string productNo { get; set; }
        public string productName { get; set; }
        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? taxClassCode { get; set; }
        /// <summary>
        /// 税控分类编码
        /// </summary>
        public string? taxControlCode { get; set;}
        public string packUnit { get; set; }
        public string packUnitDesc { get; set; }
        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }

        /// <summary>
        /// 原始包装规格
        /// </summary>
        public string packDes { get; set; }


        /// <summary>
        /// 规格
        /// </summary>
        public string specification { get; set; }


        /// <summary>
        /// 开票名称
        /// </summary>
        public string invoiceName { get; set; }

        /// <summary>
        /// 是否高价值(0-否,1-是)
        /// </summary>
        public int? IfHighValue { get; set; }
    }
}
