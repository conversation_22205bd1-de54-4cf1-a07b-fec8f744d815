﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class PaymentInput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        [Comment("付款单号")]
        public string Code { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Comment("采购单号")]
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string? AgentName { get; set; }


        /// <summary>
        /// 付款单类型
        /// </summary>
        public PaymentTypeEnum Type { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        [Precision(18, 4)]
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }
    }



    public class PaymentWebApiInput
    {
        /// <summary>
        /// 采购单号
        /// </summary>
        public List<string> PurchaseCodes { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public PaymentTypeEnum? PaymentType { get; set; }

        /// <summary>
        /// 是否付款
        /// </summary>
        public bool? IsPaid { get; set; }


    }
    public class KingdeePaymentInput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        [Comment("付款单号")]
        public string Code { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Comment("采购单号")]
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary>
        public string? PayClassify { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal PayAmount { get; set; }

        /// <summary>
        /// 付款类型(A，B,C)
        /// </summary>
        public string? PayType { get; set; }

    }

    public class KingdeeRefundPaymentInput
    {
        /// <summary>
        /// 付款单号
        /// </summary>
        [Comment("付款单号")]
        public string PayBillNo{ get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        [Comment("付款执行单号")]
        public string? PayApplicationBillNo { get; set; }
        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary>
        public string? PayClassify { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal PayAmount { get; set; }
    }
    public class KingdeeAbatementDebtInput
    {

        /// <summary>
        /// 付款单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 冲销单号
        /// </summary>
        public string? AbatementCode { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string userName { get; set; }

    }
    public class KingdeeAbatementPaymentInput
    {

        /// <summary>
        /// 付款单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 冲销单号
        /// </summary>
        public string? AbatementCode { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string userName { get; set; }

    }

    public class KindeeCreditPayInput
    {

        /// <summary>
        /// 付款单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Comment("采购单号")]
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public PaymentTypeEnum Type { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        [Precision(18, 4)]
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 支付类型
        /// </summary>
        public string? PayClassify { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinCode { get; set; }

        /// <summary>
        /// 币种名称
        /// </summary>
        public string? CoinName { get; set; }

        /// <summary>
        /// 人民币金额
        /// </summary>
        public decimal? RMBAmount { get; set; }
    }

    /// <summary>
    ///
    /// </summary>
    public class SetDiscountAmountInput
    {
        /// <summary>
        ///
        /// </summary>
        public Guid PaymentItemId { get; set; }

        /// <summary>
        ///
        /// </summary>
        public List<Guid>? Ids { get; set; }

        /// <summary>
        ///
        /// </summary>
        public decimal LimitedDiscount { get; set; }
    }

    public class AddCashDiscountFileInput
    {
        public Guid Id { get; set; }
        public string? AttachFileIds { get; set; }
        public string? AttachFileId { get; set; }
    }

    /// <summary>
    /// 撤回入参
    /// </summary>
    public class WithdrawInput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
    }

    /// <summary>
    /// 通过采购单号和项目Id获取付款单信息输入参数
    /// </summary>
    public class PaymentByPurchaseAndProjectInput
    {
        /// <summary>
        /// 采购单号
        /// </summary>
        public string PurchaseCode { get; set; } = string.Empty;

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid ProjectId { get; set; }
    }
}
