﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Inno.CorePlatform.Sell.Application.Extensions
{
    /// <summary>
    /// Newtonsoft.Json datetime  序列化
    /// [FromQuery] 参数，无法序列化
    /// </summary>
    public class JsonNetTimestampConverter : DateTimeConverterBase
    {
        internal static readonly DateTime UnixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        /// <summary>
        /// Gets or sets a value indicating whether the dates before Unix epoch
        /// should converted to and from JSON.
        /// </summary>
        /// <value>
        /// <c>true</c> to allow converting dates before Unix epoch to and from JSON;
        /// <c>false</c> to throw an exception when a date being converted to or from JSON
        /// occurred before Unix epoch. The default value is <c>false</c>.
        /// </value>
        public bool AllowPreEpoch { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="UnixDateTimeConverter"/> class.
        /// </summary>
        public JsonNetTimestampConverter() : this(false)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="UnixDateTimeConverter"/> class.
        /// </summary>
        /// <param name="allowPreEpoch">
        /// <c>true</c> to allow converting dates before Unix epoch to and from JSON;
        /// <c>false</c> to throw an exception when a date being converted to or from JSON
        /// occurred before Unix epoch. The default value is <c>false</c>.
        /// </param>
        public JsonNetTimestampConverter(bool allowPreEpoch)
        {
            AllowPreEpoch = allowPreEpoch;
        }

        /// <summary>
        /// Writes the JSON representation of the object.
        /// </summary>
        /// <param name="writer">The <see cref="JsonWriter"/> to write to.</param>
        /// <param name="value">The value.</param>
        /// <param name="serializer">The calling serializer.</param>
        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            long seconds;

            if (value is DateTime dateTime)
            {
                seconds = (long)(dateTime.ToUniversalTime() - UnixEpoch).TotalMicroseconds;
            }
            else if (value is DateTimeOffset dateTimeOffset)
            {
                seconds = (long)(dateTimeOffset.ToUniversalTime() - UnixEpoch).TotalMicroseconds;
            }
            else
            {
                throw new JsonSerializationException("Expected date object value.");
            }
            if (!AllowPreEpoch && seconds < 0)
            {
                throw new JsonSerializationException("Cannot convert date value that is before Unix epoch of 00:00:00 UTC on 1 January 1970.");
            }

            writer.WriteValue(seconds);
        }

        /// <summary>
        /// Reads the JSON representation of the object.
        /// </summary>
        /// <param name="reader">The <see cref="JsonReader"/> to read from.</param>
        /// <param name="objectType">Type of the object.</param>
        /// <param name="existingValue">The existing property value of the JSON that is being converted.</param>
        /// <param name="serializer">The calling serializer.</param>
        /// <returns>The object value.</returns>
        public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            bool nullable = objectType.IsValueType && objectType.IsGenericType && objectType.GetGenericTypeDefinition() == typeof(Nullable<>);
            if (reader.TokenType == JsonToken.Null)
            {
                if (!nullable)
                {
                    throw new JsonSerializationException(string.Format("Cannot convert null value to {0}.", objectType));
                }

                return null;
            }

            long seconds;

            if (reader.TokenType == JsonToken.Integer)
            {
                seconds = (long)reader.Value!;
            }
            else if (reader.TokenType == JsonToken.String)
            {
                if (!long.TryParse((string)reader.Value!, out seconds))
                {
                    throw new JsonSerializationException(string.Format("Cannot convert invalid value to {0}.", objectType));
                }
            }
            else
            {
                throw new JsonSerializationException(string.Format("Unexpected token parsing date. Expected Integer or String, got {0}.", reader.TokenType));
            }

            if (AllowPreEpoch || seconds >= 0)
            {
                DateTime d = UnixEpoch.AddMilliseconds(seconds);
                Type? t = (nullable)
                    ? Nullable.GetUnderlyingType(objectType)
                    : objectType;
                if (t == typeof(DateTimeOffset))
                {
                    return new DateTimeOffset(d, TimeSpan.Zero);
                }
                return d;
            }
            else
            {
                throw new JsonSerializationException(string.Format("Cannot convert value that is before Unix epoch of 00:00:00 UTC on 1 January 1970 to {0}.", objectType));
            }
        }
    }
}
