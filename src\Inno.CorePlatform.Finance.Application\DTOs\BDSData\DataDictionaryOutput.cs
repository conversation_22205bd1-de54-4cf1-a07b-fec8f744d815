﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class DataDictionaryOutput
    {
        /// <summary>
        /// 字典CODE
        /// </summary>
        public string DictionaryCode { get; set; }

        /// <summary>
        /// 字典名称
        /// </summary>
        public string DictionaryName { get; set; }

        /// <summary>
        /// 字典属性
        /// </summary>

        public string? Attribute { get; set; }
    }
    public class DataDictionaryInput
    {
        /// <summary>
        /// 字典CODE
        /// </summary>
        public string DictionaryCode { get; set; }


        /// <summary>
        /// 字典类型
        /// </summary>
        public string DictionaryType { get; set; }
    }
}
