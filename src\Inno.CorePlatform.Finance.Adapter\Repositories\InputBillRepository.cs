﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InputBillRepository : EfBaseRepository<Guid, InputBill, InputBillPo>, IInputBillRepository
    {
        private FinanceDbContext _db;
        public InputBillRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }
        public async Task<int> UpdateManyAsync(List<InputBill> list)
        {
            var updatelist = list.Adapt<List<InputBillPo>>();
            _db.InputBills.UpdateRange(updatelist);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public override async Task<int> UpdateAsync(InputBill root)
        {
            var isExist = await _db.InputBills.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("发票信息不存在！");
            }

            var po = root.Adapt<InputBillPo>();
            _db.InputBills.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        protected override InputBillPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<InputBillPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
