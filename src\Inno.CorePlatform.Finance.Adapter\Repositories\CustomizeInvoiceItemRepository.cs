﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class CustomizeInvoiceItemRepository : EfBaseRepository<Guid, CustomizeInvoiceItem, CustomizeInvoiceItemPo>, ICustomizeInvoiceItemRepository
    {
        private FinanceDbContext _db;
        public CustomizeInvoiceItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public override async Task<int> UpdateAsync(CustomizeInvoiceItem root)
        {
            var isExist = await _db.CustomizeInvoiceItem.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("不存在！");
            }

            var po = root.Adapt<CustomizeInvoiceItemPo>();

            _db.CustomizeInvoiceItem.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }

        protected override CustomizeInvoiceItemPo CreateDeletingPo(Guid id)
        {
            return new CustomizeInvoiceItemPo { Id = id };
        }

        protected override Task<CustomizeInvoiceItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
        public async Task<CustomizeInvoiceItem> GetWithNoTrackAsync(Guid id)
        {
            var po = await _db.CustomizeInvoiceItem.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<CustomizeInvoiceItem>();
        }
    }
}
