﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Records;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InventoryRepository : EfBaseRepository<Guid, InventoryItem, InventoryItemPo>, IInventoryRepository
    {
        private readonly FinanceDbContext _db;
        public InventoryRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> AddMany(List<InventoryItem> items)
        {
            var list = items.Adapt<List<InventoryItemPo>>();
            await _db.InventoryItem.AddRangeAsync(list);
            return await _db.SaveChangesAsync();
        }

        public async Task<InventoryItem> GetUnFinishInventoryItem(Guid companyId)
        {
            var item = await _db.InventoryItem.Where(p => p.Status < 2 && p.CompanyId == companyId).FirstOrDefaultAsync();
            if (item == null)
            {
                return null;
            }
            return item.Adapt<InventoryItem>();
        }

        public override async Task<int> UpdateAsync(InventoryItem root)
        {
            var po = root.Adapt<InventoryItemPo>();
            _db.InventoryItem.Update(po);
            return await _db.SaveChangesAsync();
        }

        /// <summary>
        /// 使用 EF Core 变更跟踪进行字段级别更新，解决并发问题
        /// 核心原理：只更新实际修改的字段，避免全字段覆盖
        /// </summary>
        /// <param name="inventoryItemId">盘点单ID</param>
        /// <param name="fieldUpdates">字段更新字典</param>
        /// <returns>受影响的行数</returns>
        public async Task<int> UpdateSpecificFields(Guid inventoryItemId, Dictionary<string, string> fieldUpdates)
        {
            if (fieldUpdates.Count == 0)
                return 0;

            // 使用 FindAsync 获取被 EF Core 跟踪的实体
            var entity = await _db.InventoryItem.FindAsync(inventoryItemId);
            if (entity == null)
                return 0;

            // 只修改需要更新的字段，EF Core 会跟踪这些变更
            foreach (var fieldUpdate in fieldUpdates)
            {
                switch (fieldUpdate.Key)
                {
                    case "Store":
                        entity.Store = fieldUpdate.Value;
                        break;
                    case "TempStore":
                        entity.TempStore = fieldUpdate.Value;
                        break;
                    case "Operation":
                        entity.Operation = fieldUpdate.Value;
                        break;
                    case "Exchange":
                        entity.Exchange = fieldUpdate.Value;
                        break;
                    case "SureIncomeCode":
                        entity.SureIncomeCode = fieldUpdate.Value;
                        break;
                    case "CreditRecordCode":
                        entity.CreditRecordCode = fieldUpdate.Value;
                        break;
                    case "ReceivedNoInvoiceRecordCode":
                        entity.ReceivedNoInvoiceRecordCode = fieldUpdate.Value;
                        break;
                    case "DebtRecordCode":
                        entity.DebtRecordCode = fieldUpdate.Value;
                        break;
                    case "PaymentRecordCode":
                        entity.PaymentRecordCode = fieldUpdate.Value;
                        break;
                    case "AdvanceRecordCode":
                        entity.AdvanceRecordCode = fieldUpdate.Value;
                        break;
                    case "Status":
                        if (int.TryParse(fieldUpdate.Value, out var status))
                        {

                            entity.Status = status;
                            // 如果状态更新为完成，设置完成时间
                            if (status == 99)
                            {
                                entity.FinishTime = DateTimeOffset.UtcNow;
                            }
                        }
                        break;
                    case "IsActualInventoryCompleted":
                        if (bool.TryParse(fieldUpdate.Value, out var isCompleted))
                        {
                            entity.IsActualInventoryCompleted = isCompleted;
                        }
                        break;
                }
            }

            // 更新时间戳
            entity.UpdatedTime = DateTimeOffset.UtcNow;

            // 保存更改，EF Core 只会更新被修改的字段，实现字段级别的原子更新
            return await _db.SaveChangesAsync();
        }

        public async Task<List<InventoryItem>> GetInventoriesBySysMonth(string sysMonth)
        {
            var items = await _db.InventoryItem.Where(p => p.SysMonth == sysMonth).ToListAsync();
            return items.Adapt<List<InventoryItem>>();
        }

        protected override InventoryItemPo CreateDeletingPo(Guid id)
        {
           throw new NotImplementedException();
        }

        protected override async Task<InventoryItemPo> GetPoWithIncludeAsync(Guid id)
        {
            // InventoryItemPo 没有导航属性，直接返回实体即可
            return await _db.InventoryItem.FirstOrDefaultAsync(x => x.Id == id);
        }
    }
}
