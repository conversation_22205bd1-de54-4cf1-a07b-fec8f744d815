﻿using Inno.CorePlatform.Common.DDD;
using MediatR;
using Microsoft.Extensions.Logging;

namespace  Inno.CorePlatform.Finance.Application
{
    public class MediatrDomainEventDispatcher : IDomainEventDispatcher
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MediatrDomainEventDispatcher> _log;
        public MediatrDomainEventDispatcher(IMediator mediator, ILogger<MediatrDomainEventDispatcher> log)
        {
            _mediator = mediator;
            _log = log;
        }

        public MediatrDomainEventDispatcher()
        {
            
        }
        public async Task Dispatch(IDomainEvent devent)
        {
            var domainEventNotification = createDomainEventNotification(devent);
            if (domainEventNotification == null) return;
            _log.LogDebug("Dispatching Domain Event as MediatR notification.  EventType: {eventType}", devent.GetType());
            await _mediator.Publish(domainEventNotification);
        }

        private INotification createDomainEventNotification(IDomainEvent domainEvent)
        {
            var genericDispatcherType = typeof(DomainEventNotification<>).MakeGenericType(domainEvent.GetType());
            var den = Activator.CreateInstance(genericDispatcherType, domainEvent);
            if (den != null) return (INotification)den;
            return null;
        }

        public async Task DispathForEntity(IEntityWithEvent entity)
        {
            IDomainEvent dev;
            while (entity.DomainEvents.TryTake(out dev))
                await this.Dispatch(dev);
        }
    }
}
