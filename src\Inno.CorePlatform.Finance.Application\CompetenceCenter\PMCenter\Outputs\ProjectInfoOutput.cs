﻿/**
* 命名空间：Inno.CorePlatform.Purchase.Application.DTOs.ProjectData
* 
* 类 名：DaprProjectItem
* 
* 说 明：N/A
* 
* 作 者：陈忠阳（<EMAIL>）
*
* 时 间：2023/3/29 16:44:50
*/

using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;



namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Outputs
{
    
    public class DaprGroupProjectOutput
    {
        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 项目子类型
        /// </summary>
        public string subType { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public BusinessDeptOutput businessDept { get; set; }
        /// <summary>
        /// 集团公司
        /// </summary>
        public List<CompanyMetaOutput> GroupCompanies { get; set; }
        /// <summary>
        /// 项目包含的供应商
        /// </summary>
        public List<AgentMetaOutput> Agents { get; set; }
        /// <summary>
        /// 子公司
        /// </summary>
        public List<CompanyMetaOutput> ChildCompanies { get; set; }
    }
    /// <summary>
    /// 项目信息
    /// </summary>
    public class ProjectInfoOutput
    {
        /// <summary>
        /// ID
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 项目子类型
        /// </summary>
        public string subType { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public BusinessDeptOutput businessDept { get; set; }

        /// <summary>
        /// 规则配置
        /// </summary>
        public List<DaprRuleConfigOutput>? ruleConfig { get; set; }

        /// <summary>
        /// 产品授权范围
        /// </summary>
        public ProjectProductLineScope? productLineScope { get; set; }
    }
    public class ProjectProductLineScope
    {
        public List<ProjectProducerOutput>? producers { get; set; }
        public List<ProjectProductOutput>? products { get; set; }
        public List<ProjectCustomerOutput>? customers { get; set; }
        public List<ProjectAgentOutput>? agents { get; set; }
        public List<ProjectServiceOutput>? services { get; set; }
    }
    public class ProjectServiceOutput
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    public class ProjectAgentOutput
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    public class ProjectCustomerOutput
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    public class ProjectProductOutput
    {
        public string id { get; set; }
        public string name { get; set; }
        public string manageLevel { get; set; }
        public string registerNo { get; set; }
    }
    public class ProjectProducerOutput
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    public class ProjectCompanyOutput
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    /// <summary>
    /// 核算部门
    /// </summary>
    public class BusinessDeptOutput
    {
        public List<string> orignValue { get; set; } = new List<string>();
        public string deptShortName { get; set; }
        public List<string> paths { get; set; } = new List<string>();
        public string name { get; set; }
    }

    /// <summary>
    /// 项目信息中的规则配置内容
    /// </summary>
    public class DaprRuleConfigOutput
    {
        public string id { get; set; }
        public string sourcePreAuditId { get; set; }
        public string name { get; set; }
        public int order { get; set; }
        public int weight { get; set; }
        public List<ConditionDataOutput>? conditionData { get; set; }
        public AccountDataOutput? accountData { get; set; }
        public DiscountDataOutput? discountData { get; set; }
        public List<BusinessAttributesOutput>? businessAttrs { get; set; }

    }
    public class ConditionDataOutput
    {
        public string code { get; set; }
        public string name { get; set; }
        public string[] names { get; set; }
        public string[] value { get; set; }
        public string type { get; set; }
    }
    public class AccountDataOutput
    {
        public string groupId { get; set; }
        public string name { get; set; }
        public List<AccountListItemOutput>? accountList { get; set; }

    }

    public class AccountListItemOutput
    {
        public List<ResultMetasOutput>? resultMetas { get; set; }
    }
    public class ResultMetasOutput
    {
        public string code { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public string unit { get; set; }
        public string value { get; set; }
    }
    public class BusinessAttributesOutput
    {
        public int level { get; set; }
        public string code { get; set; }
        public string name { get; set; }
    }
    public class DiscountDataOutput
    {
        public string groupId { get; set; }
        public string name { get; set; }
        public List<ResultMetasOutput>? resultMetas { get; set; }
    }
}
