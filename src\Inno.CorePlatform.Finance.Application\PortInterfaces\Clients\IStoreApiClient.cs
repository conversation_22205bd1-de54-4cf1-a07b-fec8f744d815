﻿using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IStoreApiClient
    { 
        Task<QueryReconIncomeOutput> QueryIncomeCostRec(ReconciliationInput input);

        /// <summary>
        /// 获取存货对账信息数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<QueryReconForFmOutput> QueryReconForFm(ReconciliationInput input);
    }
}
