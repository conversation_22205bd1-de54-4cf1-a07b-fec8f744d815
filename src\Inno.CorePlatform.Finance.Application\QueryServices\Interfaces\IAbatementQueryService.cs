﻿
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 冲销查询
    /// </summary>
    public interface IAbatementQueryService
    {
        /// <summary>
        /// 冲销查询
        /// </summary>
        Task<(List<AbatementQueryListOutput>, int)> GetListAsync(AbatementQueryInput query);
    }
}
