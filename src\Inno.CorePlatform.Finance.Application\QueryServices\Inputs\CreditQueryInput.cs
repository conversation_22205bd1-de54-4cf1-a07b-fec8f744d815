﻿using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 应收查询，入参
    /// </summary>
    public class CreditQueryInput : BaseQuery
    {
        /// <summary>
        /// 三方开票申请单号
        /// </summary>
        public string? ShipmentCode { get; set; }
        /// <summary>
        /// 红字消耗单号
        /// </summary>
        public string? RedReversalConsumNo { get; set; }
        /// <summary>
        /// 是否包含0元
        /// </summary>
        public int? isContainZero { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 新应收单号（用于查询发票调整应收号）
        /// </summary>
        public string? NewBillCode { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }
        /// <summary>
        /// 发票状态
        /// </summary>
        public InvoiceStatusEnum? InvoiceStatus { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditType { get; set; }
        /// <summary>
        /// 应收类型（多选）
        /// </summary>
        public List<string?>? CreditTypes { get; set; }
        /// <summary>
        /// 应付类型
        /// </summary>
        public UnitTypeEnum? DebtorType { get; set; }

        /// <summary>
        /// 业务单元 Id
        /// </summary>
        public Guid? ServicesId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServicesName { get; set; }

        /// <summary>
        /// 收款单位 Id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 付款单位 Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public List<string?>? CreatedBy { get; set; }
        /// <summary>
        /// 收款状态
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 是否逾期
        /// </summary>
        public int? IsOverdue { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 核算部门
        /// </summary>
        public string? department { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 是否运营制作未开票
        /// </summary>
        public string? isNotInvoice { get; set; }
        public Guid UserId { get; set; }

        /// <summary>
        /// 是否需要开票
        /// </summary>
        public IsNoNeedInvoiceEnum? IsNoNeedInvoice { get; set; }

        /// <summary>
        /// 销售子系统
        /// </summary>
        public string? SaleSystemName { get; set; }
        public string? CurrentUserName { get; set; }
        public bool? IsgreaterThanZero { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string? DeptName { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 是否符合更改应收关系
        /// </summary>
        public bool? IsChangeRelationship { get; set; }

        /// <summary>
        /// 签收日期 开始
        /// </summary>
        public long? IsSureIncomeDateS { get; set; }
        /// <summary>
        /// 签收日期 开始
        /// </summary>
        public DateTime? IsSureIncomeDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return IsSureIncomeDateS != null ? new DateTime(tricks_1970 + long.Parse(IsSureIncomeDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 签收日期 结束
        /// </summary>
        public long? IsSureIncomeDateE { get; set; }
        /// <summary>
        /// 签收日期 结束
        /// </summary>
        public DateTime? IsSureIncomeDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return IsSureIncomeDateE != null ? new DateTime(tricks_1970 + long.Parse(IsSureIncomeDateE.Value + "0000")) : null;
            }
        }

        /// <summary>
        /// 应收是否未开票
        /// </summary>
        public bool? IsNotHaveInvoice { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 订货人
        /// </summary>
        public string? CustomerPersonName { get; set; }


        /// <summary>
        /// 客户id集合
        /// </summary>
        public List<Guid>? CustomerIds { get; set; }

        /// <summary>
        /// 选中的单号
        /// </summary>
        public List<string>? BillCodes { get; set; }
        /// <summary>
        /// 开票对象
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 是否坏账
        /// </summary>
        public bool? IsLossRecognition { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedByName { get; set; }

        /// <summary>
        /// 价格来源
        /// </summary>
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 是否预开票发票关联条件
        /// </summary>
        public bool? IsPreAssociation { get; set; }
    }
    /// <summary>
    /// 应收明细查询，入参
    /// </summary>
    public class CreditDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 应收Id
        /// </summary>
        public Guid? CreditId { set; get; }

    }

    public class NoNeedInvoiceInput
    {
        public List<string> CreditBillCodes { get; set; }
        public string? UserName { get; set; }
    }
    public class CreditQueryTotalInput
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { set; get; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid CustomerId { set; get; }
    }
    /// <summary>
    /// 应收确认收入，入参
    /// </summary>
    public class ConfirmReceiptInput : BaseQuery
    {
        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { set; get; }

        /// <summary>
        /// 应收单Ids
        /// </summary>
        public List<Guid> CreditIds { set; get; }
        /// <summary>
        /// 修改人
        /// </summary>
        public string? UpdatedBy { get; set; }
    }


    public class CreditOfProjectOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string CreditCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal TotalValue { get; set; }

        /// <summary>
        /// 实收金额
        /// </summary>
        public decimal ActualTotalValue { get; set; }

        /// <summary>
        /// 实收时间
        /// </summary>
        public DateTimeOffset AbatementTime { get; set; }
        public Guid? CompanyId { get; set; }
        public string CompanyName { get; set; }
        public CreditTypeEnum? CreditType { get; set; }
        public string? CreditTypeStr
        {
            get
            {
                return CreditType.GetDescription();
            }
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public class SplitDebtDetailInputDto
    {
        /// <summary>
        /// 应付明细id
        /// </summary>
        public Guid DetailId { get; set; }
        /// <summary>
        /// 拆分金额
        /// </summary>
        public decimal Amount { get; set; }
    }
    /// <summary>
    /// 根据发票号获取终端客户入参参
    /// </summary>
    public class CreditOfHospitalInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public List<string?> InvoiceNos { get; set; }
    }
    /// <summary>
    /// 根据发票号获取终端客户出参
    /// </summary>
    public class CreditOfHospitalOutput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 终端客户ID
        /// </summary>
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端客户名
        /// </summary>
        public string? HospitalName { get; set; }
    }
    public class CreditBatchDto
    {
        public Guid Id { get; set; }
        public string BillCode { get; set; }
    }

    public class CreditDetailsInput : BaseQuery
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }

        /// <summary>
        /// 红字消耗单号
        /// </summary>
        public string? RedReversalConsumNo { get; set; }

        /// <summary>
        /// 三方开票申请单号
        /// </summary>
        public string? ShipmentCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 客户id集合
        /// </summary>
        public List<Guid>? CustomerIds { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 应收类型（多选）
        /// </summary>
        public List<string?>? CreditTypes { get; set; }

        /// <summary>
        /// 是否包含0元
        /// </summary>
        public int? isContainZero { get; set; }

        /// <summary>
        /// 销售子系统
        /// </summary>
        public string? SaleSystemName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string? DeptName { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public List<string?>? CreatedBy { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }

        /// <summary>
        /// 订货人
        /// </summary>
        public string? CustomerPersonName { get; set; }
        /// <summary>
        /// 开票对象
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }

        /// <summary>
        /// 应收单据日期 开始
        /// </summary>
        public long? BillDateS { get; set; }
        /// <summary>
        /// 单据日期 开始
        /// </summary>
        public DateTime? BillDateStart
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateS != null ? new DateTime(tricks_1970 + long.Parse(BillDateS.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 应收单据日期 结束
        /// </summary>
        public long? BillDateE { get; set; }
        /// <summary>
        /// 单据日期 结束
        /// </summary>
        public DateTime? BillDateEnd
        {
            get
            {
                long tricks_1970 = new DateTime(1970, 1, 1, 8, 0, 0).Ticks;//1970年1月1日刻度
                return BillDateE != null ? new DateTime(tricks_1970 + long.Parse(BillDateE.Value + "0000")) : null;
            }
        }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
        public Guid? UserId { get; set; }

        public string? CurrentUserName { get; set; }
        public string? ProductNo { get;  set; }
    }

    public class CreditForPreInvoiceInput : BaseQuery
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 预开票编码
        /// </summary>
        public string PreCustomizeInvoiceCode { get; set; } 
    }
}

