﻿using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService
{
    public interface IHandPurchaseCreatedNotify
    {
        Task Hand(EventBusDTO input, IServiceProvider serviceProvider);
    }
}
