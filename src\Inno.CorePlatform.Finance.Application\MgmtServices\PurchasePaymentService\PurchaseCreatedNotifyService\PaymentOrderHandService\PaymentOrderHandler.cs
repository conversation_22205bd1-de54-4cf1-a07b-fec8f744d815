﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService.PaymentOrderHandService
{
    public abstract class PaymentOrderHandler
    {
        public PaymentOrderHandler NextHandler { get; set; }
        /// <summary>
        /// 总预付金额
        /// </summary>
        public decimal TotalPreAmount { get; set; }
        /// <summary>
        /// 已处理金额
        /// </summary>
        public decimal HandedAmount { get; set; } = 0;
        /// <summary>
        /// 付款单号
        /// </summary>
        public string Code { get; set; }
        protected IServiceProvider _serviceProvider { get; set; }
        protected PurchaseOutPut CurrentPurchaseOrder { get; set; }
        protected AdvancePayOutput CurrentAdvancePay { get;set; }
        protected IPaymentRepository _paymentRepository { get; set; }
        public List<SavePaymentAdjustmentInput> ListPushKdPaymentUseInfos { get; set; } = new List<SavePaymentAdjustmentInput>();
        public PaymentOrderHandler(decimal totalPreAmount, string code, IServiceProvider serviceProvider, PurchaseOutPut purchaseOutPut, AdvancePayOutput currentAdvancePay)
        {
            TotalPreAmount = totalPreAmount;
            Code = code;
            _serviceProvider = serviceProvider;
            _paymentRepository=_serviceProvider.GetService<IPaymentRepository>();
            CurrentPurchaseOrder = purchaseOutPut;
            CurrentAdvancePay = currentAdvancePay;
            ListPushKdPaymentUseInfos = new List<SavePaymentAdjustmentInput>();
        }
        /// <summary>
        /// 处理付款单,返回剩余未处理完的金额
        /// </summary>
        /// <returns></returns>
        public abstract Task<decimal> HandPaymentOrder();
        protected async Task InitRMBAmount(PurchaseOutPut? purchaseOrder, Payment payment)
        {
            if (purchaseOrder?.TradeType != null && purchaseOrder?.TradeType == TradeTypeEnums.External)
            {
                if (purchaseOrder.ExternalTradeInfo.CoinName != "人民币")
                {
                    IExchangeRateService _exchangeRateService = _serviceProvider.GetService<IExchangeRateService>();
                    var exchange = await _exchangeRateService.GetByExchangeRateWithKD(new DTOs.Payment.GetExchangeRateInput
                    {
                        Effectdate = DateTime.Now,
                        OrgcurName = purchaseOrder.ExternalTradeInfo.CoinName
                    });
                    if (exchange == null || exchange.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception("操作失败：未获取到汇率");
                    }
                    payment.RMBAmount = payment.Value * exchange.Data.Excval;
                }
                else
                {
                    payment.RMBAmount = payment.Value;
                }
            }
            else
            {
                payment.RMBAmount = payment.Value;
            }
        }
        protected async Task<Payment> AddPayment(string newCode,AdvancePayOutput advance, PurchaseOutPut? purchaseOrder, decimal paymentValue, string createdBy, bool isNoMaster, DateTime? PaymentDate, string? originCode = null)
        {
            var payment = new Payment
            {
                Id = Guid.NewGuid(),
                BillDate = DateTime.Now,
                AbatedStatus = AbatedStatusEnum.NonAbate,
                AgentId = purchaseOrder.Agent?.Id,
                AgentName = purchaseOrder.Agent?.Name,
                CompanyId = purchaseOrder.Consignee?.Id,
                CompanyName = (purchaseOrder.Consignee?.Name) ?? string.Empty,
                NameCode = (purchaseOrder.Consignee?.NameCode) ?? string.Empty,
                CreatedBy = createdBy ?? "none",
                UpdatedBy = createdBy ?? "none",
                CreatedTime = DateTimeOffset.UtcNow,
                ServiceId = purchaseOrder.Service?.Id,
                ServiceName = purchaseOrder.Service?.Name,
                PurchaseCode = isNoMaster ? "" : purchaseOrder.Code,
                PaymentDate = isNoMaster ? PaymentDate : null,
                Code = newCode,
                ProducerOrderNo = isNoMaster ? "" : purchaseOrder.ProducerOrderNo,
                Value = paymentValue,
                Type = PaymentTypeEnum.Prepay,
                RelateId = isNoMaster?null:advance.Id,
                AdvancePayMode = isNoMaster?null:advance.AdvancePayMode,
                CreditAmount = 0,
                BusinessDeptFullPath = purchaseOrder.businessDeptFullPath,
                BusinessDeptFullName = purchaseOrder.businessDeptFullName,
                BusinessDeptId = purchaseOrder.businessDeptId.ToString(),
                CoinCode = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "CNY" : purchaseOrder.ExternalTradeInfo.CoinAttribute,
                CoinName = purchaseOrder?.TradeType == null || purchaseOrder?.TradeType == TradeTypeEnums.Internal ? "人民币" : purchaseOrder.ExternalTradeInfo.CoinName,
                ProjectId = purchaseOrder?.Project == null ? Guid.Empty : Guid.Parse(purchaseOrder.Project.Id),
                ProjectCode = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Code,
                ProjectName = purchaseOrder?.Project == null ? string.Empty : purchaseOrder.Project.Name,
                PurchaseContactNo = isNoMaster?null:purchaseOrder?.Contract?.Code,
                CustomerId = isNoMaster?null:purchaseOrder?.Hospital?.Id,
                CustomerName = isNoMaster ? null : purchaseOrder?.Hospital?.Name,
                OriginCode = originCode,
            };
            await InitRMBAmount(purchaseOrder, payment);
            if (payment.Value > 0)
            {
                await _paymentRepository.AddAsync(payment);
            }
            return payment;
        }
        /// <summary>
        /// 添加需要推送给金蝶的消息
        /// </summary>
        /// <param name="adAmount">本次调整金额</param>
        /// <param name="payment"></param>
        /// <param name="jfzx_orderno"></param>
        /// <param name="businessDeptId">核算部门id</param>
        /// <param name="jfzx_adorderno"></param>
        protected void AddPaymentAdjustmentInput(decimal adAmount,Payment payment, string jfzx_orderno, string? businessDeptId, string jfzx_adorderno = "")
        {
            if (string.IsNullOrWhiteSpace(payment.OriginCode))
            {
                throw new ApplicationException($"游离付款单的原始单号不能为空，游离付款单：{payment.Code}");
            }
            var temp = new SavePaymentAdjustmentInput
            {
                jfzx_adamount = adAmount,
                jfzx_orderno = jfzx_orderno,
                jfzx_adorderno = jfzx_adorderno,
                jfzx_org = businessDeptId,
                jfzx_paymentno = payment.OriginCode,
                jfzx_projectno = payment.ProjectCode
            };
            //if (!string.IsNullOrEmpty(temp.jfzx_paymentno))
            //{
            //    var codes = payment.Code.Split("-");
            //    if (codes[0].Contains("PV") && codes.Length > 3) //长度大于3说明：有个拆单 字符加过-001
            //    {
            //        temp.jfzx_paymentno = codes[0] + "-" + codes[1] + "-" + codes[2];
            //    }
            //}
            ListPushKdPaymentUseInfos.Add(temp);
        }
    }
}
