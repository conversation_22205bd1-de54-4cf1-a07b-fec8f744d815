﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IAdvancePaymentQueryService
    {
        /// <summary>
        /// 获取单头列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PageResponse<AdvancePaymentOutput>> GetAdvancePaymentList(AdvancePaymentInput input);

        /// <summary>
        /// 获取明细列表(应收明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>   
        Task<PageResponse<AdvancePaymentDebtDetailOutput>> GetAdvancePaymentDebtDetails(AdvancePaymentDetailInput input);


        /// <summary>
        /// 获取明细列表(货品明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>  
        Task<PageResponse<AdvancePaymentProductDetailOutput>> GetAdvancePaymentProductDetails(AdvancePaymentDetailInput input);

        /// <summary>
        /// 获取tab数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<AdvancePaymentTabOutput>> GetTabCountAsync(AdvancePaymentInput input);

        /// <summary>
        /// 获取明细数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> GetDetailsInfo(AdvancePaymentDebtDetailQueryInput input);

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> Save(SaveAdvancePaymentInput input);

        /// <summary>
        /// 重新支付上游日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> ReSetDate(SaveAdvancePaymentInput input);

        /// <summary>
        /// 重新计算含税垫资毛利
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<decimal?>> ReSetAmount(ReComputeInput input);

        /// <summary>
        /// 分摊到货品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> AllocateToGoods(AdvancePaymentDebtDetailInfo input);

        /// <summary>
        /// 提交到OA审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SubmitOA(AdvancePaymentDebtDetailInfo input);

        /// <summary>
        /// 删除付款计划明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeleteAdvancePaymentDebtDetails(AdvancePaymentDebtDetailInfo input);

        /// <summary>
        /// 根据id获取详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<AdvancePaymentItemInfo>> GetItemById(AdvancePaymentDetailInput input);

        /// <summary>
        /// 根据id删除数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DeleteItemById(AdvancePaymentDetailInput input);

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UploadAttachFileIds(AdvancePaymentItemAttachFileInput input);

        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BizFileUploadOutput>> GetAttachFile(AdvancePaymentItemAttachFileQueryInput input);
    }
}
