using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 寄售转购货明细DTO
    /// </summary>
    public class ConsignToPurchaseDetailDto
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string purchaseOrderCode { get; set; }
        /// <summary>
        /// 业务单号
        /// </summary>
        public DateTime billDate { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string spec { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string model { get; set; }

        /// <summary>
        /// 生产商ID
        /// </summary>
        public string producerId { get; set; }

        /// <summary>
        /// 生产商名称
        /// </summary>
        public string producerName { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public string productNameId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string productName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string productNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal quantity { get; set; }

        /// <summary>
        /// 已入票数量
        /// </summary>
        public decimal invoiceQuantity { get; set; }

        /// <summary>
        /// 可入票数量
        /// </summary>
        public decimal canInvoiceQuantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal unitCost { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal taxRate { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public string? productId { get;  set; }
    }



}
