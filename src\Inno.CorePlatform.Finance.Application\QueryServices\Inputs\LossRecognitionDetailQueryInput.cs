﻿using Inno.CorePlatform.Finance.Application.ApplicationServices;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 损失确认申请单详情查询，入参
    /// </summary>
    public class LossRecognitionDetailQueryInput : Inno.CorePlatform.Common.DDD.Query.PageInput
    {
        /// <summary>
        /// 损失确认申请单Id
        /// </summary>
        public Guid LossRecognitionItemId { set; get; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public LossRecognitionDetailTypeEnum? Classify { get; set; }

    }
}
