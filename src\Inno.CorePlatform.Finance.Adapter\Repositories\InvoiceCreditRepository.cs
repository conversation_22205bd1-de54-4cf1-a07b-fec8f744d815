﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Invoice;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InvoiceCreditRepository : EfBaseRepository<Guid, InvoiceCredit, InvoiceCreditPo>, IInvoiceCreditRepository
    {
        private FinanceDbContext _db;
        public InvoiceCreditRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }
        public async Task<int> AddManyAsync(List<InvoiceCredit> inputs)
        {
            if (inputs == null)
            {
                throw new Exception("数据不能为空！");
            }
            var invoiceCreditPos = inputs.Adapt<List<InvoiceCreditPo>>();
            await _db.InvoiceCredits.AddRangeAsync(invoiceCreditPos);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();
        }
        public override Task<int> UpdateAsync(InvoiceCredit root)
        {
            throw new NotImplementedException();
        }

        protected override InvoiceCreditPo CreateDeletingPo(Guid id)
        {
            return new InvoiceCreditPo { Id = id };
        }

        protected override Task<InvoiceCreditPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
