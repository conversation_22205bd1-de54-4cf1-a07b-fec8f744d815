﻿using Inno.CorePlatform.Finance.Domain.DomainObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{

    public class CompanyMetaInfosOut
    {


        /// <summary>
        /// 
        /// </summary>
        public string companyId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string companyName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string companyShortName { get; set; }

        public string? businessDeptFullName { get; set; }
        public string? businessDeptFullPath { get; set; }
        public int? businessDeptId { get; set; }
        /// <summary>
        /// 销售税率
        /// </summary>
        public string? taxRate { get; set; }


    }
    public class DaprCompanyInfoOutput
    {
        public string companyId { get; set; }
        public string companyName { get; set; }
        public string companyShortName { get; set; }
        public bool? containValidCerts { get; set; }
        public int? delayDays { get; set; }
        public string latestUniCode { get; set; }
        public string nameCode { get; set; }
        public List<BusinessRange> newManageScopeRes { get; set; } = new List<BusinessRange>();
        public List<BusinessRange> oldManageScopeRes { get; set; } = new List<BusinessRange>();
        public string remark { get; set; }
        public int? sort { get; set; }
        public string sysMonth
        {
            get; set;

        }
        /// <summary>
        /// 开票规格默认值
        /// </summary>
        public string InvoiceSpec { get; set; }
        /// <summary>
        /// 开票规格默认值
        /// </summary>
        public string InvoiceSpecDesc {  get; set; }
        public int Status { get;  set; }
    }
    public class BasicCompanyOutpupt
    {
        public string companyId { get; set; }
        public string companyName { get; set; }
    }

    public class StaffCompanyOutput : BasicCompanyOutpupt
    {
        public string companyShortName { get; set; }
    }
    public class CompanyMetaOutput
    {
        public string id { get; set; }
        public string name { get; set; }
        public ExtraInfo extraInfo { get; set; }
    }
    public class ExtraInfo
    {
        public string nameCode { get; set; }
    }
}
