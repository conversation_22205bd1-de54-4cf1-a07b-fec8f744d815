﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Mapster;
using MathNet.Numerics;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 冲销查询
    /// </summary>
    public class AbatementQueryService : QueryAppService, IAbatementQueryService
    {
        /// <summary>
        /// 注入数据库查询
        /// </summary>
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _iBDSApiClient;
        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="iBDSApiClient"></param>
        public AbatementQueryService(IAppServiceContextAccessor? contextAccessor,
            FinanceDbContext db, IBDSApiClient iBDSApiClient) :
            base(contextAccessor)
        {
            this._db = db;
            this._iBDSApiClient = iBDSApiClient;
        }

        /// <summary>
        /// 根据应付单获取冲销列表 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<AbatementQueryListOutput>, int)> GetListAsync(AbatementQueryInput query)
        {
            Expression<Func<AbatementPo, bool>> exp = z => 1 == 1;

            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.DebtBillCode))
            {
                exp = exp.And(z => (z.DebtBillCode != null && z.DebtBillCode == query.DebtBillCode)
                    ||(z.CreditBillCode != null && z.CreditBillCode == query.DebtBillCode));
            }
            else if (!string.IsNullOrWhiteSpace(query.CreditBillCode))
            {
                exp = exp.And(z => (z.CreditBillCode != null && z.CreditBillCode == query.CreditBillCode)
                 || (z.DebtBillCode != null && z.DebtBillCode == query.CreditBillCode));
            } 
            else { exp = exp.And(z => 1 != 1); }

            if (query.FilterLoss.HasValue && query.FilterLoss.Value)
            {
                exp = exp.And(z => z.CreatedBy != "LossRecognition" && z.CreatedBy != "AutoAbatementByAdvance");
            }
            #endregion

            IQueryable<AbatementPo> baseQuery = _db.Abatements.Where(exp).AsNoTracking();

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Select(z => z.Adapt<AbatementQueryListOutput>()).ToListAsync();
            if (list != null && list.Any())
            {
                var billCodes = list.Select(x => x.CreditBillCode).ToList();
                var debts = await _db.Debts.Where(x => !string.IsNullOrEmpty(x.BillCode) && billCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                foreach (var item in list)
                {
                    var debt = debts.FirstOrDefault(x => x.BillCode == item.CreditBillCode);
                    if (debt != null)
                    {
                        item.CustomerId = debt.CustomerId;
                        item.CustomerName = debt.CustomerName;
                    }
                }
                if (query.CustomerId.HasValue)
                {
                    list = list.Where(x=>x.CustomerId == query.CustomerId).Skip((query.page - 1) * query.limit).Take(query.limit).ToList();
                }
            }

            return (list, count);
        }
    }
}
