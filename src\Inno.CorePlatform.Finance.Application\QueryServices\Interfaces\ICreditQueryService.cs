﻿
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 应收查询
    /// </summary>
    public interface ICreditQueryService
    {
        /// <summary>
        /// 根据relateCode获取应收
        /// </summary>
        /// <param name="relateCode"></param>
        /// <returns></returns>
        Task<List<CreditQueryListOutput>> GetByRelateCode(string relateCode);

        /// <summary>
        /// 根据订单号得到应收
        /// </summary>
        /// <param name="orderNos"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByOrderNos(List<string> orderNos);
        /// <summary>
        /// 根据发票号获取终端客户
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<CreditOfHospitalOutput>> GetCreditHospitalByInvoiceNo(CreditOfHospitalInput input);

        /// <summary>
        /// 销项发票查询
        /// </summary>
        Task<(List<InvoiceCreditQueryListOutput>, int)> GetInvoiceCreditListAsync(InvoiceCreditQueryInput query, bool isExport = false);

        /// <summary>
        /// 应收查询
        /// </summary>
        Task<(List<CreditQueryListOutput>, int)> GetListAsync(CreditQueryInput query);

        /// <summary>
        /// 应收查询（批量认款）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditQueryListOutput>, int)> GetListByRecognizeReceiveAsync(CreditQueryInput query);

        /// <summary>
        ///导出关联发票的产品清单
        /// </summary>
        /// <returns></returns>        
         Task<MemoryStream> CreditHasInvoiceExport(CreditQueryInput query);
        /// <summary>
        /// 应收查询
        /// </summary>
        Task<(List<CreditDetailQueryListOutput>, int)> GetListDetailAsync(CreditDetailQueryInput query);

        /// <summary>
        /// 获取为冲销额度
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<decimal>> GetTotalValue(CreditQueryTotalInput input);
        /// <summary>
        /// 根据订单号获取应收
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        Task<List<Credit>> GetByOrderNo(string orderNo, Guid? serviceId);
        /// <summary>
        /// 获取个人应收
        /// </summary>
        /// <param name="originOrderNos"></param>
        /// <returns></returns>
        Task<List<CreditWithDetailOutput>> GetByOriginOrderNos(List<string> originOrderNos);

        /// <summary>
        /// 获取客户负数应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<Credit>> GetNonAbated(GetCreditInput input);
        Task<List<CreditQueryListOutput>> GetByRelateCodes(List<string> relateCodes);
        Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount(CreditQueryInput query);
        Task<BaseResponseData<List<CreditOfProjectOutput>>> GetCreditByInvoiceNo(CreditOfDeliveryInput input);

        /// <summary>
        /// 按公司获取应收账龄数据
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<CreditAgeOutput>>> GetCreditAgeData(Guid companyId);
        /// <summary>
        /// 获取符合更换关系的应收数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditQueryListOutput>, int)> GetCompliantRelationshipList(CreditQueryInput query);

        /// <summary>
        /// 获取符合更换应收关系的应收id
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="customerId"></param>
        /// <param name="invoiceNo"></param>
        /// <returns></returns>
        Task<List<Guid>> GetUninvoicedReturnIds(Guid? companyId, Guid? customerId, string? invoiceNo);

        /// <summary>
        /// 根据OrderNo获取订单id
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> GetOrderId([FromBody] CreditQueryInput query);
        /// <summary>
        /// 获取发票
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="customerId"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        Task<List<CreditBalanceOutput>> GetCreditInvoiceBalanceByCustomerId(Guid companyId, Guid customerId, System.DateTime date);
        /// <summary>
        /// 获取应收
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="customerId"></param>
        /// <param name="date"></param>
        /// <returns></returns>
        Task<List<CreditDetailsOutput>> GetCreditBalanceByCustomerId(Guid companyId, Guid customerId, System.DateTime date);

        /// <summary>
        /// 分批收入确认
        /// </summary>
        /// <param name="input"></param>
        Task<(List<CreditSureIncomeQueryOutput>, int)> GetPartialIncome(CreditSureIncomeQueryInput input);

        /// <summary>
        /// 导出应收清单任务
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditListExportOutput>, int)> CreditListExportAsync(CreditQueryInput query);

        /// <summary>
        /// 销项发票导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<InvoiceCreditExportListOutput>, int)> InvoiceCreditListExportAsync(InvoiceCreditQueryInput query);

        /// <summary>
        /// 根据认款明细code和类型获取应收信息
        /// </summary>
        /// <returns></returns>
        Task<List<CreditInfo>?> GetCreditInfoByRecognizeReceiveCode(RecognizeReceiveCreditInput input);

        /// <summary>
        /// 应收单列表查询，损失确认单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditQueryListOutput>, int)> GetListByLossRecognitionAsync(CreditQueryInput query);
        Task<(List<CreditDetailOutput>, int)> GetCreditDetailsAsync(CreditDetailsInput query);
        Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByCreditDetailIdsAsync(List<Guid> creditDetailIds);
        Task<List<OriginDetailOutput>> GetCreditInvoiceDetailTempsByUserNameAsync(string userName);
        Task<BaseResponseData<string>> AddCreditInvoiceDetailTemps(List<CreditDetailOutput> inputs, string currentUserName);
        Task<(List<CreditQueryListOutput>, int)> GetListForPreInvoiceAsync(CreditForPreInvoiceInput input);
        Task<BaseResponseData<bool>> DeleteCreditInvoiceDetailTempsByOriginDetailIdsAsync(List<string> originDetailIds);
    }
}
