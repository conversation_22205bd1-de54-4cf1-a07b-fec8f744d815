﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Newtonsoft.Json;
using Npoi.Mapper;
using Polly;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class SpecialStoreOutAppService : BaseAppService, ISpecialStoreOutAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly ICodeGenClient _codeGenClient;
        public SpecialStoreOutAppService(ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork unitOfWork,
            IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IPurchaseApiClient purchaseApiClient,
            ICodeGenClient codeGenClient,
            Func<int, TimeSpan> sleepDurationProvider = null)
            : base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            _inventoryApiClient = inventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _codeGenClient = codeGenClient;
            _projectMgntApiClient = projectMgntApiClient;
            _purchaseApiClient = purchaseApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreOutOutput storeout = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);

                    if (storeout == null || !storeout.Details.Any())
                    {
                        throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                    if (string.IsNullOrEmpty(storeout.checker))
                    {
                        throw new Exception("该单据没有复核人");
                    }
                });
                var requestBody = JsonConvert.SerializeObject(input);
                //经销退货  经销报损 集团退货出库 集团销毁出库
                if (storeout.relateCodeType == 601 || storeout.relateCodeType == 603 || storeout.relateCodeType == 114 || storeout.relateCodeType == 115)
                {
                    return await Deal601603(storeout, input.BusinessSubType, requestBody, input.useBillDate);
                }
                //寄售退货
                //else if (storeout.relateCodeType == 602)
                //{
                //    return await Deal602(storeout, input.BusinessSubType, requestBody);
                //}
                //寄售报损 寄售盘亏
                else if (
                    storeout.relateCodeType == 604 ||
                    storeout.relateCodeType == 1201 ||
                    storeout.relateCodeType == 602 ||
                    storeout.relateCodeType == 108)
                {
                    return await Deal6041201(storeout, input.BusinessSubType, requestBody, input.useBillDate);
                }
                //经销盘亏
                else if (storeout.relateCodeType == 1202)
                {
                    return await Deal1202(storeout, input.BusinessSubType, requestBody);
                }
                //集团货不合格品处理,不做处理，也不抛异常
                else if (storeout.relateCodeType == 109)
                {
                    return await Deal109(storeout, input.BusinessSubType, requestBody);
                }
                else
                {
                    throw new Exception("不匹配的出库类型和子类型");
                }
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应付代码出错");
            }
        }

        public async Task<BaseResponseData<int>> ExchangeLossConversion(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreExchangeBackOutput inventoryStoreExchange = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    inventoryStoreExchange = await _inventoryApiClient.QueryByCodeForFinance(input.BusinessCode);

                    if (inventoryStoreExchange == null || !inventoryStoreExchange.details.Any())
                    {
                        throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                });
                var requestBody = JsonConvert.SerializeObject(input);

                var check = await base.IsCreatedDebtForBill(input.BusinessCode);
                if (check)
                {
                    throw new Exception("该单据已生成过应付");
                }
                var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = new List<string> { inventoryStoreExchange.companyId.ToString() }
                })).FirstOrDefault();

                var productIds = inventoryStoreExchange.details.Select(p => p.productId).Distinct().ToList();
                var inputs = new List<ProductIdAndAgentIdInput>();
                foreach (var item in productIds)
                {
                    inputs.Add(new ProductIdAndAgentIdInput()
                    {
                        agentId = inventoryStoreExchange.agentId.ToString(),
                        productId = item
                    });
                }
                inputs = inputs.Distinct().ToList();
                var productNameInfos = await _bDSApiClient.GetProductbyIdsAsync(productIds, inventoryStoreExchange.companyId.ToString(), inputs);
                var projectInfo = (await _projectMgntApiClient.GetProjectInfoByIds(new List<Guid>() { inventoryStoreExchange.projectId }))?.FirstOrDefault();
                var groupDetails = inventoryStoreExchange.details.GroupBy(p => new { p.billCode, p.businessUnitId });
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(inventoryStoreExchange.companyId.ToString()));
                var agents = await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { inventoryStoreExchange.agentId });
                var index = 1;
                var insertList = new List<DebtDto>();
                var kingdeeDebts = new List<KingdeeDebt>();

                #region 处理应付
                foreach (var g in groupDetails)
                {
                    PurchaseQueryInfoSimpleOutput purchaseOrder = null;
                    if (!string.IsNullOrEmpty(g.FirstOrDefault()?.storeOutCode))
                    {
                        purchaseOrder = await _purchaseApiClient.GetSimpleByCode(g.FirstOrDefault().storeOutCode);
                    }
                    var debt = new DebtDto
                    {
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        BillCode = $"{inventoryStoreExchange.billCode}-{index.ToString().PadLeft(3, '0')}",
                        BillDate = inventoryStoreExchange.billDate.HasValue ? DateTimeHelper.LongToDateTime(inventoryStoreExchange.billDate.Value) : null,
                        CreatedBy = inventoryStoreExchange.createdBy != null ? inventoryStoreExchange.createdBy : "none",
                        CreatedTime = DateTime.UtcNow,
                        Id = Guid.NewGuid(),
                        Value = -Math.Round(Math.Abs(g.Sum(p => p.quantity * (p.settlementUnitCost ?? 0))), 2),
                        AgentId = inventoryStoreExchange.agentId,
                        AgentName = inventoryStoreExchange.agentName,
                        IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                        CompanyId = inventoryStoreExchange.companyId,
                        CompanyName = inventoryStoreExchange.companyName,
                        RelateCode = inventoryStoreExchange.billCode,
                        DebtType = DebtTypeEnum.exchangeback,
                        NameCode = companyInfo.nameCode,
                        ServiceId = g.Key.businessUnitId,
                        BusinessDeptFullName = inventoryStoreExchange.businessDeptFullName,
                        BusinessDeptFullPath = inventoryStoreExchange.businessDeptFullPath,
                        BusinessDeptId = inventoryStoreExchange.businessDeptId,
                        ProjectId = projectInfo?.Id,
                        ProjectCode = projectInfo?.Code,
                        ProjectName = projectInfo?.Name,
                        OrderNo = purchaseOrder?.Code,
                        PurchaseCode = purchaseOrder?.Code,
                        PurchaseContactNo = purchaseOrder?.Contract?.Code,
                        ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                        RMBAmount = -Math.Abs(g.Sum(p => p.quantity * (p.settlementUnitCost ?? 0))),
                        CoinCode = string.IsNullOrEmpty(g.First().coinAttr) || g.First().coinAttr == "CNY" ? "CNY" : g.First().coinAttr,
                        CoinName = string.IsNullOrEmpty(g.First().coinName) || g.First().coinAttr == "CNY" ? "人民币" : g.First().coinName,
                        ServiceName = g.FirstOrDefault()?.businessUnitName,
                    };
                    if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                    {
                        debt.RebateNo = purchaseOrder.RelateCode;
                    }
                    #region 包装金蝶应付参数
                    if (debt.Value != 0)
                    {
                        InitKingdeeDebt2(productNameInfos, kingdeeDebts, g, debt, inventoryStoreExchange.billCode);
                    }
                    #endregion
                    if (debt.Value != 0)
                    {
                        insertList.Add(debt);
                    }
                    index++;
                }
                if (kingdeeDebts.Any())
                {
                    var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, "", "");
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                        await base.CreateManyDebts(insertList);
                        await _unitOfWork.CommitAsync();
                        var debtIds = insertList.Select(p => p.Id).Distinct().ToList();
                        await _debtRepository.RepaireDebtDiff(debtIds);
                    }
                    else
                    {
                        throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                    }
                }
                #endregion

                #region 处理凭证
                await PushToKingdee2(inventoryStoreExchange, productNameInfos, new List<ProjectInfo>() { projectInfo });
                #endregion

                return BaseResponseData<int>.Success("操作成功");
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应付代码出错");
            }

        }

        /// <summary>
        /// 经销退货处理
        /// 结算价=0，不生成应付，生成财务凭证
        /// 结算价=成本价，生成负数应付，不生成财务凭证
        /// 结算价大于0并且小于成本价,生成应付和财务凭证
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> Deal601603(InventoryStoreOutOutput input, string classify, string preRequestBody, bool? useBillDate = false)
        {
            var check = await base.IsCreatedDebtForBill(input.storeOutCode);
            if (check)
            {
                throw new Exception("该单据已生成过应付");
            }
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            var serviceIds = input.Details.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
            var services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
            {
                ids = serviceIds.Select(p => p.ToString()).ToList()
            });
            var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
            var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
            var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            var groupDetails = input.Details.GroupBy(p => new { p.businessUnitId, p.projectId, p.purchaseOrderCode, p.mark });
            var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
            var agents = input.agentId != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { input.agentId.Value }) : null;
            var index = 1;
            var insertList = new List<DebtDto>();
            var kingdeeDebts = new List<KingdeeDebt>();
            if (string.IsNullOrEmpty(input.checker))
            {
                throw new Exception("该单据没有复核人");
            }
            #region 处理应付
            foreach (var g in groupDetails)
            {
                PurchaseQueryInfoSimpleOutput purchaseOrder = null;
                if (!string.IsNullOrEmpty(g.Key.purchaseOrderCode))
                {
                    purchaseOrder = await _purchaseApiClient.GetSimpleByCode(g.Key.purchaseOrderCode);
                }
                var thisProjectInfo = projectInfo.FirstOrDefault(t => t.Id == g.Key.projectId);
                var debt = new DebtDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                    //BillDate = billDate,
                    BillDate = (useBillDate.HasValue && useBillDate.Value ? DateTimeHelper.LongToDateTime(input.billDate) : billDate),
                    CreatedBy = purchaseOrder == null ? input.checker : purchaseOrder.CreatedBy,
                    CreatedTime = DateTime.UtcNow,
                    Id = Guid.NewGuid(),
                    Value = -Math.Round(Math.Abs(g.Sum(p => p.quantity * (p.unitCost ?? 0))), 2),
                    AgentId = input.agentId,
                    AgentName = input.agentName,
                    IsInnerAgent = agents?.FirstOrDefault()?.agentIsZXInternal,
                    CompanyId = input.companyId,
                    CompanyName = companyInfo.companyName,
                    RelateCode = input.storeOutCode,
                    DebtType = DebtTypeEnum.selfreturn,
                    NameCode = companyInfo.nameCode,
                    ServiceId = g.Key.businessUnitId,
                    BusinessDeptFullName = input.businessDeptFullName,
                    BusinessDeptFullPath = input.businessDeptFullPath,
                    BusinessDeptId = input.businessDeptId.ToString(),
                    ProjectId = g.Key.projectId,
                    ProjectCode = thisProjectInfo?.Code,
                    ProjectName = thisProjectInfo?.Name,
                    OrderNo = g.Key.purchaseOrderCode,
                    PurchaseCode = g.Key.purchaseOrderCode,
                    PurchaseContactNo = purchaseOrder?.Contract?.Code,
                    ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                    RMBAmount = -Math.Abs(g.Sum(p => p.quantity * (p.rmbAmount ?? 0))),
                    CoinCode = string.IsNullOrEmpty(g.First().coinAttribute) || g.First().coinAttribute == "CNY" ? "CNY" : g.First().coinAttribute,
                    CoinName = string.IsNullOrEmpty(g.First().coinName) || g.First().coinAttribute == "CNY" ? "人民币" : g.First().coinName,
                    Mark = g.Key.mark,
                    IsInternalTransactions = Utility.IsInternalTransactions(input.relateCodeType),
                };
                if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                {
                    debt.RebateNo = purchaseOrder.RelateCode;
                }
                if (g.Key.businessUnitId.HasValue)
                {
                    debt.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.Value.ToString().ToLower())?.name;
                }
                #region 包装金蝶应付参数
                if (debt.Value != 0)
                {
                    InitKingdeeDebt(productNameInfos, kingdeeDebts, g, debt, input.storeOutCode, input.relateCodeType);
                }
                #endregion
                if (debt.Value != 0)
                {
                    insertList.Add(debt);
                }
                index++;
            }
            if (kingdeeDebts.Any())
            {

                var requestBody = JsonConvert.SerializeObject(input);
                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, classify, preRequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    await base.CreateManyDebts(insertList);
                    await _unitOfWork.CommitAsync();
                    var debtIds = insertList.Select(p => p.Id).Distinct().ToList();
                    await _debtRepository.RepaireDebtDiff(debtIds);
                }
                else
                {
                    throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            #endregion

            #region 处理凭证
            await PushToKingdee(601, input, productNameInfos, projectInfo, classify, preRequestBody);
            #endregion

            return BaseResponseData<int>.Success("操作成功");
        }

        private async Task<BaseResponseData<int>> Deal109(InventoryStoreOutOutput input, string classify, string preRequestBody)
        {
            var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
            var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
            var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            return await PushToKingdee(109, input, productNameInfos, projectInfo, classify, preRequestBody);
        }

        private async Task<BaseResponseData<int>> Deal6041201(InventoryStoreOutOutput input, string classify, string preRequestBody, bool? useBillDate = false)
        {
            var check = await base.IsCreatedDebtForBill(input.storeOutCode);
            if (check)
            {
                throw new Exception("该单据已生成过应付");
            }
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            var serviceIds = input.Details.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
            var services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
            {
                ids = serviceIds.Select(p => p.ToString()).ToList()
            });
            var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
            var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
            var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            var groupDetails = input.Details.GroupBy(p => new { p.businessUnitId, p.projectId, p.mark });
            var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
            var traceInfos = new List<TraceCodeOutput>();
            var traceCodes = input.Details.Where(p => p.mark == 0 || p.mark == 3).Select(p => p.traceCode).Distinct().ToList();
            var agents = input.agentId != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { input.agentId.Value }) : null;
            if (traceCodes.Any())
            {
                traceInfos = await _inventoryApiClient.QueryTraceInfoByCodes(traceCodes);
            }
            var index = 1;
            var insertList = new List<DebtDto>();
            var kingdeeDebts = new List<KingdeeDebt>();
            if (string.IsNullOrEmpty(input.checker))
            {
                throw new Exception("该单据没有复核人");
            }
            #region 处理应付
            foreach (var g in groupDetails)
            {
                var thisProjectInfo = projectInfo.FirstOrDefault(t => t.Id == g.Key.projectId);
                var thisTrance = new TraceCodeOutput();
                PurchaseQueryInfoSimpleOutput purchaseOrder = null;
                var firstDetail = g.FirstOrDefault();
                if (firstDetail != null && !string.IsNullOrEmpty(firstDetail.traceCode))
                {
                    thisTrance = traceInfos.FirstOrDefault(t => t.TraceCode == firstDetail.traceCode);
                    if (thisTrance != null && !string.IsNullOrEmpty(thisTrance?.PurchaseOrderCode))
                    {
                        purchaseOrder = await _purchaseApiClient.GetSimpleByCode(thisTrance.PurchaseOrderCode);
                    }
                }
                var debt = new DebtDto
                {
                    AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                    BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                    //BillDate = billDate,
                    BillDate = (useBillDate.HasValue && useBillDate.Value ? DateTimeHelper.LongToDateTime(input.billDate) : billDate),
                    CreatedBy = purchaseOrder == null ? input.checker : purchaseOrder.CreatedBy,
                    CreatedTime = DateTime.UtcNow,
                    Id = Guid.NewGuid(),
                    Value = Math.Round(g.Sum(p => p.quantity * (p.unitCost ?? 0)), 2),
                    AgentId = input.agentId,
                    AgentName = input.agentName,
                    IsInnerAgent = agents?.FirstOrDefault().agentIsZXInternal,
                    CompanyId = input.companyId,
                    CompanyName = companyInfo.companyName,
                    RelateCode = input.storeOutCode,
                    DebtType = input.relateCodeType == 602 ? DebtTypeEnum.consignmentreturn : DebtTypeEnum.consignmentloss,
                    NameCode = companyInfo.nameCode,
                    ServiceId = g.Key.businessUnitId,
                    BusinessDeptFullName = input.businessDeptFullName,
                    BusinessDeptFullPath = input.businessDeptFullPath,
                    BusinessDeptId = input.businessDeptId.ToString(),
                    ProjectId = g.Key.projectId,
                    ProjectCode = thisProjectInfo?.Code,
                    ProjectName = thisProjectInfo?.Name,
                    OrderNo = input.storeOutCode,
                    PurchaseContactNo = purchaseOrder?.Contract?.Code,
                    ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                    PurchaseCode = thisTrance?.PurchaseOrderCode,
                    DebtDetails = new List<DebtDetail>(),
                    RMBAmount = (g.Sum(p => p.quantity * (p.rmbAmount ?? 0))),
                    CoinCode = string.IsNullOrEmpty(g.First().coinAttribute) || g.First().coinAttribute == "CNY" ? "CNY" : g.First().coinAttribute,
                    CoinName = string.IsNullOrEmpty(g.First().coinName) || g.First().coinAttribute == "CNY" ? "人民币" : g.First().coinName,
                    Mark = g.Key.mark,
                    IsInternalTransactions = Utility.IsInternalTransactions(input.relateCodeType),
                };
                if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                {
                    debt.RebateNo = purchaseOrder.RelateCode;
                }

                if (debt.Value > 0)
                {
                    var outPut = await _codeGenClient.ApplyCode(new ApplyCodeInput
                    {
                        BusinessArea = debt.BillCode.Split('-')[0],
                        BillType = "DPP",
                        SysMonth = companyInfo.sysMonth,
                        DelayDays = companyInfo.delayDays.HasValue ? companyInfo.delayDays.Value : 6,
                        Num = 1,
                        CompanyCode = companyInfo.nameCode
                    });
                    var newDetail = new DebtDetail()
                    {
                        DebtId = debt.Id,
                        AccountPeriodType = 1,
                        ProbablyPayTime = DateTime.Now,
                        Code = outPut.Codes.First(),
                        Status = DebtDetailStatusEnum.WaitExecute,
                        Value = debt.Value,
                        CreatedTime = DateTime.Now,
                        CreatedBy = "none",
                        Id = Guid.NewGuid()
                    };
                    debt.DebtDetails.Add(newDetail);
                }
                if (g.Key.businessUnitId.HasValue)
                {
                    debt.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.Value.ToString().ToLower())?.name;
                }
                #region 包装金蝶应付参数
                if (debt.Value != 0)
                {
                    InitKingdeeDebt(productNameInfos, kingdeeDebts, g, debt, input.storeOutCode, input.relateCodeType);
                }
                #endregion
                if (debt.Value != 0)
                {
                    insertList.Add(debt);
                }
                index++;
            }
            if (kingdeeDebts.Any())
            {
                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts, classify, preRequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    await base.CreateManyDebts(insertList);
                    await _unitOfWork.CommitAsync();
                }
                else
                {
                    throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            #endregion 
            #region 处理凭证
            var type = 604;
            if (input.relateCodeType == 108)
            {
                type = 108;
            }
            if (input.relateCodeType == 602)
            {
                type = 602;
            }
            await PushToKingdee(type, input, productNameInfos, projectInfo, classify, preRequestBody);
            #endregion

            return BaseResponseData<int>.Success("操作成功");
        }

        private async Task<BaseResponseData<int>> Deal1202(InventoryStoreOutOutput input, string classify, string preRequestBody)
        {
            var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
            var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
            var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
            var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
            return await PushToKingdee(1202, input, productNameInfos, projectInfo, classify, preRequestBody);
        }

        private static void InitKingdeeDebt(
            List<ProductNameInfoOutput> productNameInfos,
            List<KingdeeDebt> kingdeeDebts,
            IGrouping<object, Detail> g,
            DebtDto debt,
            string storeOutCode,
            int? relateCodeType)
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = storeOutCode,
                jfzx_creator = debt.CreatedBy ?? "none",
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                currency_number = debt.CoinCode ?? "CNY",
                pricetaxtotal4 = debt.Value,
            };
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
            var amount = 0m;
            g.ToList().GroupBy(a => new { a.productId, a.unitCost, a.taxRate, a.rmbAmount }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                d.taxrate = t.First().importBusinessFlag != 1 ? t.Key.taxRate.Value : 0;

                if (relateCodeType == 601 || relateCodeType == 603 || relateCodeType == 114 || relateCodeType == 115)
                {
                    d.quantity = -Math.Abs(t.Sum(b => b.quantity));
                }
                else
                {
                    d.quantity = Math.Abs(t.Sum(b => b.quantity));
                }
                d.pricetax = Math.Abs(t.Key.unitCost.Value);
                d.e_amountbaseMany = t.Key.rmbAmount.HasValue ? t.Key.rmbAmount.Value * Math.Abs(d.quantity) : 0;
                if (kingdeeDebt.pricetaxtotal4 < 0)
                {
                    d.e_amountbaseMany = -Math.Abs(d.e_amountbaseMany);
                }
                d.jfzx_project_number = debt.ProjectCode;//项目单号 预留
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == t.First().productNameId);
                if (thisProductInfo.classificationNewGuid.HasValue)
                {
                    d.material_number1 = thisProductInfo.classificationNewGuid.ToString();
                }
                else
                {
                    d.material_number1 = thisProductInfo.classificationGuid.ToString();
                }
                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + d.taxrate / 100.00M)), 20);
                amount += d.price2 * d.quantity;
            });
            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);
            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);
        }

        private static void InitKingdeeDebt2(
            List<ProductNameInfoOutput> productNameInfos,
            List<KingdeeDebt> kingdeeDebts,
            IGrouping<object, InventoryStoreExchangeBackDetailOutput> g,
            DebtDto debt,
            string storeOutCode
            )
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = storeOutCode,
                jfzx_creator = debt.CreatedBy ?? "none",
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                currency_number = debt.CoinCode ?? "CNY",
                pricetaxtotal4 = debt.Value,
            };
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
            var amount = 0m;
            g.ToList().GroupBy(a => new { a.productId, a.settlementUnitCost }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                d.taxrate = t.FirstOrDefault()?.settlementTaxRate ?? 0;
                d.quantity = -Math.Abs(t.Sum(b => b.quantity));

                d.pricetax = Math.Abs(t.Key.settlementUnitCost.Value);
                d.e_amountbaseMany = t.FirstOrDefault().settlementUnitCostOrigin.HasValue ? t.FirstOrDefault().settlementUnitCostOrigin.Value * Math.Abs(d.quantity) : 0;
                if (kingdeeDebt.pricetaxtotal4 < 0)
                {
                    d.e_amountbaseMany = -Math.Abs(d.e_amountbaseMany);
                }
                d.jfzx_project_number = debt.ProjectCode;//项目单号 预留
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productId == t.First().productId);
                if (thisProductInfo.classificationNewGuid.HasValue)
                {
                    d.material_number1 = thisProductInfo.classificationNewGuid.ToString();
                }
                else
                {
                    d.material_number1 = thisProductInfo.classificationGuid.ToString();
                }
                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + d.taxrate / 100.00M)), 20);
                amount += d.price2 * d.quantity;
            });
            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);
            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);
        }

        private async Task<BaseResponseData<int>> PushToKingdee(int type,
            InventoryStoreOutOutput input,
            List<ProductNameInfoOutput> productNameInfos,
            List<ProjectInfo> projectInfos,
            string classify,
            string preRequestBody)
        {

            var inputKD = new HoldStockRemovalInput()
            {
                billno = input.storeOutCode,
                jfzx_date = DateTimeHelper.LongToDateTime(input.billDate),
                jfzx_tallydate = DateTimeHelper.LongToDateTime(input.billDate),
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = input.companyName,
                jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = input.createdBy ?? "none",
                StoreOutType = input.storeOutType
            };
            if (!string.IsNullOrEmpty(input.signSysMonth))
            {
                inputKD.jfzx_tallydate = DateTime.Parse(input.signSysMonth);
            }
            inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
            var details = input.Details;
            if (type == 601)
            {
                //stockUnitCost-unitCost
                details = input.Details.Where(p => p.stockUnitCost - Math.Abs(p.unitCost.Value) != 0).ToList();
                if (!details.Any())
                {
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
            if (type == 604 || type == 108 || type == 602)
            {
                details = new List<Detail>();
                //stockUnitCost-unitCost 
                var detailmark0 = input.Details.Where(p => p.mark == 0 || p.mark == 3).ToList();
                var detailmark1 = input.Details.Where(p => p.mark == 1).ToList();
                var detailmark2 = input.Details.Where(p => p.mark == 2).ToList();
                if (detailmark0.Any())
                {
                    var tempdetails = detailmark0.Where(p => (p.stockUnitCost - Math.Abs(p.unitCost.Value) != 0)).ToList();
                    details.AddRange(tempdetails);
                }
                if (detailmark1.Any())
                {
                    //var tempdetails = detailmark1.Where(p => (p.standardUnitCost - Math.Abs(p.unitCost.Value) != 0)).ToList();
                    details.AddRange(detailmark1);
                }
                if (detailmark2.Any())
                {
                    //var tempdetails = detailmark2.Where(p => (p.standardUnitCost - Math.Abs(p.unitCost.Value) != 0)).ToList();
                    details.AddRange(detailmark2);
                }
                if (!details.Any())
                {
                    return BaseResponseData<int>.Success("操作成功");
                }
            }
            if (type == 1202)
            {
                details = input.Details;
                //防止上游在经销盘亏中写price的值，经销盘亏不应该有price值
                details.ForEach(p =>
                {
                    p.stockUnitCost = Math.Abs(p.unitCost.Value);
                    p.unitCost = 0;
                    p.price = 0;
                });
            }
            foreach (var detail in details)
            {
                var thisProject = projectInfos.FirstOrDefault(t => t.Id == detail.projectId);
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == detail.productNameId);
                var jfzx_material = Guid.Empty;
                if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationNewGuid.Value;
                }
                else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationGuid.Value;
                }
                //stockunitCost-unitCost
                var taxCost = detail.mark == 0 || detail.mark == 3 ? (detail.stockUnitCost.Value - Math.Abs(detail.unitCost.Value)) : detail.standardUnitCost.Value - Math.Abs(detail.unitCost.Value);
                //var taxCost = detail.unitCost.Value;
                var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                //销售成本总额
                var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                //2是集团寄售  1是寄售  0是经销
                var detailInfo = new HoldStockRemovalDetail
                {
                    jfzx_count = detail.quantity,
                    jfzx_material = jfzx_material.ToString().ToUpper(),
                    jfzx_model = detail.specification,
                    jfzx_projectnos = thisProject?.Code,
                    Mark = detail.mark,
                    jfzx_suppliers = detail.agentId.Value.ToString().ToUpper(),
                    jfzx_unitprice = detail.mark == 0 || detail.mark == 3 ? noTaxCost : detail.standardUnitCost.Value - Math.Abs(detail.unitCost.Value),
                };
                if (type == 602 || type == 604 || type == 108)
                {
                    detailInfo.jfzx_unitprice = detail.standardUnitCost.Value;//寄售货直接给标准成本
                }
                detailInfo.jfzx_sellingcost = detailInfo.jfzx_unitprice * detail.quantity;
                if (detailInfo.jfzx_sellingcost != 0)
                {
                    inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                }
            }
            inputKD.fk_jfzx_totalsalescost = Math.Round(inputKD.holdStockRemovalEntrysModel.Sum(p => p.jfzx_sellingcost), 2);
            if (inputKD.holdStockRemovalEntrysModel.Count() > 0)
            {
                var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(new List<HoldStockRemovalInput> { inputKD }, classify, preRequestBody);
                if (kingdeeRes.Code != CodeStatusEnum.Success)
                {
                    throw new Exception(kingdeeRes.Message);
                }
            }
            return BaseResponseData<int>.Success("操作成功");
        }

        private async Task<BaseResponseData<int>> PushToKingdee2(InventoryStoreExchangeBackOutput input, List<ProductNameInfoOutput> productNameInfos, List<ProjectInfo> projectInfos)
        {

            var inputKD = new HoldStockRemovalInput()
            {
                billno = input.billCode,
                jfzx_date = input.billDate.HasValue ? DateTimeHelper.LongToDateTime(input.billDate.Value) : DateTime.Now,
                jfzx_tallydate = input.billDate.HasValue ? DateTimeHelper.LongToDateTime(input.billDate.Value) : DateTime.Now,
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                //jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = input.companyName,
                jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = string.IsNullOrEmpty(input.createdBy) ? "none" : input.createdBy,
                StoreOutType = 22
            };

            inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();
            var details = input.details;
            var fk_jfzx_totalsalescost = 0m;

            foreach (var detail in details)
            {
                var thisProject = projectInfos.FirstOrDefault(t => t.Id == input.projectId);
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productId == detail.productId);
                var jfzx_material = Guid.Empty;
                if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationNewGuid.Value;
                }
                else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationGuid.Value;
                }

                var noTaxCost = Math.Round((Math.Abs(detail.unitCost ?? 0) - Math.Abs(detail.settlementUnitCost ?? 0)) / (1 + detail.settlementTaxRate.Value / 100.00M), 2);//不含税成本
                fk_jfzx_totalsalescost += noTaxCost * detail.quantity;
                var detailInfo = new HoldStockRemovalDetail
                {
                    Mark = 0,
                    jfzx_count = detail.quantity,
                    jfzx_material = jfzx_material.ToString().ToUpper(),
                    jfzx_model = detail.specification,
                    jfzx_projectnos = thisProject?.Code,
                    jfzx_suppliers = input.agentId.ToString().ToUpper(),
                    jfzx_unitprice = noTaxCost,
                };
                detailInfo.jfzx_sellingcost = detailInfo.jfzx_unitprice * detail.quantity;
                if (detail.unitCost != detail.settlementUnitCost && detailInfo.jfzx_unitprice != 0)
                {
                    inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                }
            }
            inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
            if (inputKD.holdStockRemovalEntrysModel.Count > 0)
            {
                var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(new List<HoldStockRemovalInput> { inputKD }, null, null);
                if (kingdeeRes.Code != CodeStatusEnum.Success)
                {
                    throw new Exception(kingdeeRes.Message);
                }
            }

            return BaseResponseData<int>.Success("操作成功");
        }

        private async Task<BaseResponseData<int>> PushToKingdeeNew(int type,
            InventoryStoreOutOutput input,
            List<ProductNameInfoOutput> productNameInfos,
            List<ProjectInfo> projectInfos)
        {
            // 出库子类型，*********** 604分别代表经销退货、寄售退货、经销报损、寄售报损、
            var jfzx_type = string.Empty;
            if (type == 603)
            {
                jfzx_type = "01";
            }
            else if (type == 604)
            {
                jfzx_type = "02";
            }
            var inputKD = new BatchSaveBarterDisposeBillInput()
            {
                billno = input.storeOutCode,
                jfzx_org = input.companyName,
                jfzx_bizdate = DateTimeHelper.LongToDateTime(input.billDate).ToString("yyyy-MM-dd"),
                jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_type = jfzx_type,
                jfzx_remark = input.remark ?? "无",
            };

            inputKD.jfzx_materialentity = new List<ApBarterDisposeEntryModel>();
            var details = input.Details;
            var fk_jfzx_totalsalescost = 0m;

            foreach (var detail in details)
            {
                var thisProject = projectInfos.FirstOrDefault(t => t.Id == detail.projectId);
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == detail.productNameId);
                var jfzx_material = Guid.Empty;
                if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationNewGuid.Value;
                }
                else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                {
                    jfzx_material = thisProductInfo.classificationGuid.Value;
                }
                var taxCost = detail.mark == 0 || detail.mark == 3 ? (detail.stockUnitCost.Value - Math.Abs(detail.unitCost.Value)) : detail.standardUnitCost.Value - Math.Abs(detail.unitCost.Value);

                var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                //销售成本总额
                var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                if (detail.mark == 0 || detail.mark == 3)
                {
                    fk_jfzx_totalsalescost += noTaxCost10 * detail.quantity;
                }
                else
                {
                    fk_jfzx_totalsalescost += detail.standardUnitCost.Value * detail.quantity;
                }
                var detailInfo = new ApBarterDisposeEntryModel
                {
                    jfzx_project = thisProject.Code,
                    jfzx_materialnumber = jfzx_material.ToString().ToUpper(),
                    jfzx_qty = detail.quantity,
                    jfzx_bizorg = input.businessDeptId.ToString(),

                };
                inputKD.jfzx_materialentity.Add(detailInfo);
            }
            var kingdeeRes = await _kingdeeApiClient.BatchSaveBarterDisposeBill(new List<BatchSaveBarterDisposeBillInput> { inputKD });
            if (kingdeeRes.Code == CodeStatusEnum.Success)
            {

            }
            else
            {
                throw new Exception(kingdeeRes.Message);
            }
            return BaseResponseData<int>.Success("操作成功");
        }
    }
}
