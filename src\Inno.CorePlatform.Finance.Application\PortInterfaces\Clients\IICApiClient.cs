﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IICApiClient
    { /// <summary>
      /// 发票信息提交接口
      /// </summary>
        Task<BaseResponseData<string>> ICSPDSupplierFinish(SPDInvoiceInput input);

        /// <summary>
        /// 货票同行出库的发票信息
        /// </summary>
        Task<BaseResponseData<string>> ICSPDWithInTemp(SPDInvoiceInput input);

        /// <summary>
        /// 获取SPD金额
        /// </summary>
        Task<BaseResponseData<List<SPDInvoiceOutput>>> ICGetSPDAmount(SPDQueryAmountInput input);
        Task<BaseResponseData<BasePagedData<AdvanceCheckDetailOutput>>> ICGetAdvanceCheckDetail(AdvanceCheckDetailInput input);

        /// <summary>
        /// 阳采发票填报
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> ICPushSunPurchaseInvoice(SunPurchaseDto input);

        /// <summary>
        /// 同步阳采发票状态
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<SearchInvoiceDetailOutput>>> SyncSunPurchaseInvoiceStatus(SearchInvoiceInput input);

        /// <summary>
        /// 认款发票推送至SPD
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> PushRecognizeReceive(RecognizeReceivePushSPDInput input);

        /// <summary>
        /// 获取SPD发票详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<SpdInvoiceApplyDetailOutput>>> ICSPDInvoiceApplyDetails(SpdInvoiceApplyDetailInput input);

        /// <summary>
        /// 安贞发票上报
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> AnthenUploadinvoice(UploadInvoiceAZDto input);
        Task<GetRefundStoreInDetailOutput> GetRefundStoreInDetail(string storeInCode);
        Task<BaseResponseData<List<ApplyCodeOutput>>> GetSpdApplyCodeAsync(List<string> consumeCodes);
        Task<BaseResponseData<string>> VanxUploadinvoice(UploadInvoiceVanxDto input);
        Task<BaseResponseData<object>> YiDaoUploadinvoice(UploadInvoiceYiDaoDto input);
    }
}
