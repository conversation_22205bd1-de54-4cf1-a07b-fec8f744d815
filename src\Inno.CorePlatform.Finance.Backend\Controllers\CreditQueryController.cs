﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 应收查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CreditQueryController : BaseController
    {
        private readonly ICreditQueryService _creditQueryService;
        private readonly ICreditAppService _creditAppService;
        private readonly ILogger<CreditQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly ISellApiClient _sellApiClient;
        /// <summary>
        /// 应收查询
        /// </summary>
        public CreditQueryController(ICreditQueryService creditQueryService,
            ICreditAppService creditAppService,
            ILogger<CreditQueryController> logger,
            IPCApiClient pCApiClient,
            IBDSApiClient bDSApiClient,
            IInvoiceQueryService invoiceQueryService,
            IKingdeeApiClient kingdeeApiClient, ISubLogService subLog) : base(subLog)
        {
            _creditQueryService = creditQueryService;
            _creditAppService = creditAppService;
            _logger = logger;
            _pCApiClient = pCApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _invoiceQueryService = invoiceQueryService;
            _bDSApiClient = bDSApiClient;
        }
        /// <summary>
        /// 获取应收单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListALL")]
        public async Task<ResponseData<CreditQueryListOutput>> GetListALL([FromBody] CreditQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetListAsync(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 预开票关联应收查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListForPreInvoice")]
        public async Task<ResponseData<CreditQueryListOutput>> GetListForPreInvoiceAsync([FromBody] CreditForPreInvoiceInput query)
        {
            try
            { 
                var (list, count) = await _creditQueryService.GetListForPreInvoiceAsync(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        ///导出关联发票的产品清单
        /// </summary>
        /// <returns></returns>
        [HttpPost("credit/export")]
        public async Task<IActionResult> CreditHasInvoiceExport([FromBody] CreditQueryInput query)
        {
            query.page = 1;
            query.limit = int.MaxValue;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;
            var stream = await _creditQueryService.CreditHasInvoiceExport(query);
            return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }
        [HttpPost("exportCreditTask")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportCreditTask([FromBody] CreditQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _creditAppService.ExportCreditTask(query);
        }
        /// <summary>
        /// 获取应收单列表数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount([FromBody] CreditQueryInput query)
        {
            query.UserId = CurrentUser.Id.Value;
            return await _creditQueryService.GetTabCount(query);
        }
        /// <summary>
        /// 获取应收单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<CreditQueryListOutput>> GetList([FromBody] CreditQueryInput query)
        {
            try
            {
                if (!query.IsNoNeedInvoice.HasValue)
                {
                    query.IsNoNeedInvoice = Domain.IsNoNeedInvoiceEnum.Need;
                }
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetListAsync(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    
        /// <summary>
        /// 获取应收单明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetail")]
        public async Task<ResponseData<CreditDetailQueryListOutput>> GetListDetail([FromBody] CreditDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _creditQueryService.GetListDetailAsync(query);
                return new ResponseData<CreditDetailQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditDetailQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 确认收入
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ConfirmReceipt")]
        public async Task<ResponseData<ConfirmReceiptOutput>> ConfirmReceipt([FromBody] ConfirmReceiptInput query)
        {
            try
            {
                query.UpdatedBy = CurrentUser.UserName;
                var (count, msg) = await _creditAppService.ConfirmReceipt(query);
                if (count > 0)
                {
                    return new ResponseData<ConfirmReceiptOutput>
                    {
                        Code = 200,
                        Data = new Data<ConfirmReceiptOutput>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<ConfirmReceiptOutput>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                return new ResponseData<ConfirmReceiptOutput>
                {
                    Code = 500,
                    Msg = ex.Message
                };
            }
        }
        /// <summary>
        /// 反向确认
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("ReverseConfirm")]
        public async Task<ResponseData<ConfirmReceiptOutput>> ReverseConfirm([FromBody] ConfirmReceiptInput query)
        {
            try
            {
                query.UpdatedBy = CurrentUser.UserName;
                var (count, msg) = await _creditAppService.ReverseConfirm(query);
                if (count > 0)
                {
                    return new ResponseData<ConfirmReceiptOutput>
                    {
                        Code = 200,
                        Data = new Data<ConfirmReceiptOutput>
                        {
                            Total = count,
                        }
                    };
                }
                else
                {
                    return new ResponseData<ConfirmReceiptOutput>
                    {
                        Code = 500,
                        Msg = msg
                    };
                }
            }
            catch (Exception ex)
            {
                return new ResponseData<ConfirmReceiptOutput>
                {
                    Code = 500,
                    Msg = ex.Message
                };
            }
        }

        /// <summary>
        /// 销项发票查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInvoiceCreditList")]
        public async Task<BaseResponseData<PageResponse<InvoiceCreditQueryListOutput>>> GetInvoiceCreditListAsync([FromBody] InvoiceCreditQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetInvoiceCreditListAsync(query);
                return new BaseResponseData<PageResponse<InvoiceCreditQueryListOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<InvoiceCreditQueryListOutput>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        /// <summary>
        ///导出销项发票
        /// </summary>
        /// <returns></returns>
        [HttpPost("ExportInvoiceCreditList")]
        public async Task<IActionResult> ExportInvoiceCreditList([FromBody] InvoiceCreditQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetInvoiceCreditListAsync(query, true);

                List<InvoiceCreditExportListOutput> InvoiceCreditExportListOutputlist = list.Adapt<List<InvoiceCreditExportListOutput>>();
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.Cells[1, 1].Value = "发票号";
                    worksheet.Cells[1, 2].Value = "发票代码";
                    worksheet.Cells[1, 3].Value = "发票类型";
                    worksheet.Cells[1, 4].Value = "开票日期";
                    worksheet.Cells[1, 5].Value = "客户";
                    worksheet.Cells[1, 6].Value = "公司";
                    worksheet.Cells[1, 7].Value = "应收单号";
                    worksheet.Cells[1, 8].Value = "关联单号";
                    worksheet.Cells[1, 9].Value = "订单号";
                    worksheet.Cells[1, 10].Value = "开票名称";
                    worksheet.Cells[1, 11].Value = "开票规格";
                    worksheet.Cells[1, 12].Value = "数量";
                    worksheet.Cells[1, 13].Value = "单位";
                    worksheet.Cells[1, 14].Value = "开票申请单号";
                    worksheet.Cells[1, 15].Value = "核算部门";
                    worksheet.Cells[1, 16].Value = "税额(元)";
                    worksheet.Cells[1, 17].Value = "税率(%)";
                    worksheet.Cells[1, 18].Value = "含税单价(元)";
                    worksheet.Cells[1, 19].Value = "含税总金额(元)";
                    worksheet.Cells[1, 20].Value = "不含税单价(元)";
                    worksheet.Cells[1, 21].Value = "不含税总金额(元)";
                    worksheet.Cells[1, 22].Value = "创建日期";
                    worksheet.Cells[1, 23].Value = "创建人";
                    worksheet.Cells[1, 24].Value = "业务单元";
                    worksheet.Cells[1, 25].Value = "状态";
                    worksheet.Cells[1, 26].Value = "备注";

                    var dataList = InvoiceCreditExportListOutputlist;
                    int row = 2;
                    foreach (var item in dataList)
                    {
                        worksheet.Cells[row, 1].Value = item.InvoiceNo;
                        worksheet.Cells[row, 2].Value = item.InvoiceCode;
                        worksheet.Cells[row, 3].Value = item.Type;
                        worksheet.Cells[row, 4].Value = row == 1 ? item.InvoiceTime : Convert.ToDateTime(item.InvoiceTime).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 5].Value = item.CustomerName;
                        worksheet.Cells[row, 6].Value = item.CompanyName;
                        worksheet.Cells[row, 7].Value = item.BillCode;
                        worksheet.Cells[row, 8].Value = item.RelateCode;
                        worksheet.Cells[row, 9].Value = item.OrderNo;
                        worksheet.Cells[row, 10].Value = item.ProductName;
                        worksheet.Cells[row, 11].Value = item.ProductNo;
                        //worksheet.Cells[row, 12].Value = item.Specification;
                        worksheet.Cells[row, 12].Value = item.Quantity;
                        worksheet.Cells[row, 13].Value = item.PackUnit;
                        worksheet.Cells[row, 14].Value = item.Code;
                        worksheet.Cells[row, 15].Value = item.BusinessDeptFullName;
                        worksheet.Cells[row, 16].Value = Convert.ToDouble(item.TaxAmount);
                        worksheet.Cells[row, 17].Value = item.TaxRate;
                        worksheet.Cells[row, 18].Value = Convert.ToDouble(item.Price);
                        worksheet.Cells[row, 19].Value = Convert.ToDouble(item.Value);
                        worksheet.Cells[row, 20].Value = Convert.ToDouble(item.NoTaxAmountPrice);
                        worksheet.Cells[row, 21].Value = Convert.ToDouble(item.TotalNoTaxAmountPrice);
                        worksheet.Cells[row, 16].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 18].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 19].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 20].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 21].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 22].Value = row == 1 ? item.CreatedTime : Convert.ToDateTime(item.CreatedTime).ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 23].Value = item.CreatedBy;
                        worksheet.Cells[row, 24].Value = item.ServiceName;
                        worksheet.Cells[row, 25].Value = item.StatusStr;
                        worksheet.Cells[row, 26].Value = item.Remark;
                        row++;
                    }
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }

            }
            catch (Exception ex)
            {

                return StatusCode(500, $"出现错误: {ex.Message}");
            }
        }
        /// <summary>
        /// 导出销项发票任务
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("exportInvoiceCreditTask")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoiceCreditTask([FromBody] InvoiceCreditQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.UserName = CurrentUser.UserName;
            return await _creditAppService.ExportInvoiceCreditTask(query);
        }

        [HttpGet("GetKDFilePath")]
        public async Task<BaseResponseData<List<QueryInvoiceAttachmentOutput>>> GetKDFilePath(string invoiceNo, string? invoiceCode)
        {
            var ret = BaseResponseData<List<QueryInvoiceAttachmentOutput>>.Failed(500, "没有找到发票数据");
            var invoice = await _invoiceQueryService.GetInvoiceByInvoiceNo(invoiceNo);
            if (invoice != null)
            {
                if (!string.IsNullOrEmpty(invoice.Type) && invoice.Type.Contains("数电"))
                {
                    var companys = await _bDSApiClient.GetCompanyInfoAsync(new BDSBaseInput()
                    {
                        ids = new List<string> { invoice.CompanyId.ToString() }
                    });
                    var retDigital = await _kingdeeApiClient.GetDigitalInvoiceFile(new SimData
                    {
                        invoiceNum = invoiceNo,
                        sellerTaxpayerId = companys[0].latestUniCode
                    });
                    if (retDigital.Code == CodeStatusEnum.Success)
                    {
                        ret.Data = new List<QueryInvoiceAttachmentOutput> {
                          new QueryInvoiceAttachmentOutput{
                             invoiceNo=  invoiceNo,
                             address= retDigital.Data.ofdFileUrl,
                             previewAddress= retDigital.Data.invoiceFileUrl,
                             ofdFileUrl= retDigital.Data.ofdFileUrl,
                             xmlFileUrl= retDigital.Data.xmlFileUrl,
                             pdfFileUrl= retDigital.Data.invoiceFileUrl,
                          }
                        };
                        ret.Code = CodeStatusEnum.Success;
                    }
                    else
                    {
                        ret.Message = retDigital.Message;
                    }

                }
                else
                {
                    ret = await _kingdeeApiClient.QueryInvoiceAttachment(new QueryInvoiceAttachmentInput
                    {
                        invoiceNo = invoiceNo,
                        invoiceCode = invoiceCode ?? "",
                        invoiceType = "XX"
                    });
                }
            }

            return ret;
        }


        /// <summary>
        /// 下载应收账龄
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet("DownloadCreditAge")]

        public async Task<IActionResult> DownloadCreditAge(Guid companyId)
        {
            var data = await _creditQueryService.GetCreditAgeData(companyId);
            var datas = data.Data;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            var stream = new MemoryStream();
            using (var package = new ExcelPackage(stream))
            {
                if (datas.Any())
                {

                    var worksheet1 = package.Workbook.Worksheets.Add("应收账龄");
                    #region 
                    worksheet1.Cells[1, 1].Value = "单据编号";
                    worksheet1.Cells[1, 2].Value = "客户单位";
                    worksheet1.Cells[1, 3].Value = "单据类型";
                    worksheet1.Cells[1, 4].Value = "应收总额";
                    worksheet1.Cells[1, 5].Value = "待确认收入";
                    worksheet1.Cells[1, 6].Value = "收入-未开票";
                    worksheet1.Cells[1, 7].Value = "收入-已开票";
                    worksheet1.Cells[1, 8].Value = "待确认税额";
                    worksheet1.Cells[1, 9].Value = "税额-未开票";
                    worksheet1.Cells[1, 10].Value = "税额-已开票";
                    worksheet1.Cells[1, 11].Value = "冲销金额(元)";
                    worksheet1.Cells[1, 12].Value = "余额";

                    int row = 2;
                    foreach (var item in datas)
                    {
                        worksheet1.Cells[row, 1].Value = item.CreditCode;
                        worksheet1.Cells[row, 2].Value = item.CustomerName;
                        worksheet1.Cells[row, 3].Value = item.CreditType;
                        worksheet1.Cells[row, 4].Value = item.Amount;
                        worksheet1.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 5].Value = item.UnSureAmount;
                        worksheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 6].Value = item.SureAndNoInvoiceAmount;
                        worksheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 7].Value = item.SureAndInvoiceAmount;
                        worksheet1.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 8].Value = item.UnSureAndNoInvoiceTaxAmount;
                        worksheet1.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 9].Value = item.SureAndNoInvoiceTaxAmount;
                        worksheet1.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 10].Value = item.InvoiceTaxAmount;
                        worksheet1.Cells[row, 10].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 11].Value = item.AbatementAmount;
                        worksheet1.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                        worksheet1.Cells[row, 12].Value = item.UnAbatementAmount;
                        worksheet1.Cells[row, 12].Style.Numberformat.Format = "#,##0.00";
                        row++;
                    }
                    #endregion
                }
                package.SaveAs(stream);
                stream.Position = 0;
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
        }

        /// <summary>
        /// 获取符合更换关系的应收数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetCompliantRelationshipList")]
        public async Task<ResponseData<CreditQueryListOutput>> GetCompliantRelationshipList([FromBody] CreditQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetCompliantRelationshipList(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据订单billcode获取id
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetOrderId")]
        public async Task<BaseResponseData<string>> GetOrderId([FromBody] CreditQueryInput query)
        {
            try
            {
                var ret = await _creditQueryService.GetOrderId(query);
                return ret;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 分批确认收入查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPartialIncome")]
        public async Task<BaseResponseData<PageResponse<CreditSureIncomeQueryOutput>>> GetPartialIncome([FromBody] CreditSureIncomeQueryInput query)
        {
            try
            {
                var (list, count) = await _creditQueryService.GetPartialIncome(query);
                return new BaseResponseData<PageResponse<CreditSureIncomeQueryOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<CreditSureIncomeQueryOutput>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }

        }


    }
}

