<template>
  <div class="app-page-container">
    <div class="app-page-header">
      <el-breadcrumb separator-icon="ArrowRight">
        <!-- <el-breadcrumb-item :to="{ name: 'financeManagement-creditQuery' }">财务管理</el-breadcrumb-item> -->
        <el-breadcrumb-item>运营制作开票（应收）</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="flex-1"></div>
      <inno-crud-operation :crud="crud" hidden-opts-left>
        <template #default>
          <inno-search-input v-model="crud.query.searchKey" @search="crud.toQuery" />
        </template>
      </inno-crud-operation>
    </div>
    <div class="app-page-body" style="padding-top: 0">
      <inno-query-operation v-model:query-list="queryList" :crud="crud" />
      <inno-split-pane :default-percent="60" split="horizontal">
        <template #paneL="{ full, onFull }">
          <inno-crud-operation :crud="crud" border hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs v-model="crud.query.isNoNeedInvoice" class="demo-tabs" @tab-change="tabhandleClick">
                <el-tab-pane :label="`待开发票列表`" name="0" lazy />
                <el-tab-pane :label="`无需开发票列表`" name="1" lazy />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip
                type="success"
                icon="Select"
                :disabled="!(
                                   crud.selections &&
                                   crud.selections.length>
                0 &&
                crud.selections[0].saleSource === 60
                )
                "
                @click="generateServiceCustomizeInvoice"
              >一键生成服务费申开单</inno-button-tooltip>
              <inno-button-tooltip
                type="warning"
                icon="CircleCloseFilled"
                :disabled="crud.selections.length &&
        crud.selections[0]?.isNoNeedInvoiceInt === 1
        "
                @click="noNeedInvoice"
              >无需开票</inno-button-tooltip>
              <!--<inno-button-tooltip type="primary" :disabled="!(crud.data && crud.data.length > 0)" @click="exportData">导出数据</inno-button-tooltip>-->
              <inno-button-tooltip
                type="primary"
                :disabled="!(crud.data && crud.data.length > 0)"
                @click="downloadAsync(
                          'api/CustomizeInvoice/exportCustomizeInvoiceTask',
                          '运营制作开票'
                        )"
              >导出数据</inno-button-tooltip>
              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneL' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef"
            v-inno-loading="crud.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crud.data"
            stripe
            :row-class-name="crud.tableRowClassName"
            @sort-change="crud.sortChange"
            @selection-change="handleAllSelectionChange"
            @row-click="rowClick"
          >
            <el-table-column type="selection" fixed="left" width="45" />
            <el-table-column label="应收单号" property="billCode" width="230" fixed="left" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.billCode" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.billCode }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="关联单号" property="relateCode" width="220" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.relateCode" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="true">
                  <el-link style="font-size: 12px;color:#123170" @click="detailRelate(scope.row.relateCode)">
                    {{
                    scope.row.relateCode
                    }}
                  </el-link>
                </inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.orderNo" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="原始订单号" property="originOrderNo" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.originOrderNo" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originOrderNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="三方开票申请单号" property="shipmentCode" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.shipmentCode" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.shipmentCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="红字消耗单号" property="redReversalConsumNo" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.redReversalConsumNo" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.redReversalConsumNo }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="公司" property="companyName" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.companyId" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.companyName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户" property="customerName" width="200" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.customerIds" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="部门" property="deptName" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.deptName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="业务单元" property="serviceName" width="180" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.serviceName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="项目名称" property="projectName" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.projectName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订货人" property="customerPersonName" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerPersonName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="客户订单号" property="customerOrderCode" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.customerOrderCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" sortable width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-numeral :value="scope.row.value" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="已开票金额" property="invoiceAmount" width="120" show-overflow-tooltip sortable>
              <template #default="scope">{{ scope.row.invoiceAmount }}</template>
            </el-table-column>
            <el-table-column label="采购成本金额" property="purchaseCost" width="120" show-overflow-tooltip sortable>
              <template #default="scope">{{ scope.row.purchaseCost }}</template>
            </el-table-column>
            <el-table-column label="销售子系统" property="saleSystemName" width="110" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.saleSystemName" :crud="crud" :column="column" isInput />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.saleSystemName }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="应收类型" property="service" width="125" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditTypeStr }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="开票对象" property="creditSaleSubType" width="125" show-overflow-tooltip>
              <template #default="scope">{{ scope.row.creditSaleSubType===1?'消费者':scope.row.creditSaleSubType===2?'平台':'' }}</template>
            </el-table-column>
            <el-table-column label="备注" property="note" width="125" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.note }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="创建日期" property="createdTime" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ dateFormat(scope.row.createdTime, 'YYYY-MM-DD') }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="创建人" property="createdByName" width="85" show-overflow-tooltip>
              <template #header="{ column }">
                <inno-header-filter :config="queryObject.createdBy" :crud="crud" :column="column" />
              </template>
              <template #default="scope">{{ scope.row.createdByName }}</template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            已选择 {{ crud.selections.length }} 条
            <span style="margin-left: 20px; font-weight: 600;">合计金额：{{ amountTotal.toFixed(4) }}</span>
            <span style="margin-left: 20px; font-weight: 600;">合计开票金额：{{ invoiceAmountTotal.toFixed(4) }}</span>
            <div class="flex-1" />
            <inno-crud-pagination :crud="crud" :pageSizes="[20, 30, 50, 100, 200, 300, 500,600]" />
          </div>
        </template>
        <template #paneR="{ full, onFull }">
          <inno-query-operation v-model:query-list="queryListDetail" :crud="crud2" />
          <inno-crud-operation :crud="crud2" border hidden-opts-right style="padding: 0">
            <template #opts-left>
              <el-tabs>
                <el-tab-pane :label="`开票明细信息`" />
              </el-tabs>
            </template>
            <template #default>
              <inno-button-tooltip type="danger" icon="Search" :disabled="!(crud.selections && crud.selections.length > 0)" @click="getOriginDetail">获取原始开票明细</inno-button-tooltip>
              <!-- v-auth="functionUris.customizeInvoice" -->
              <inno-button-tooltip
                v-if="crud.selections[0]?.isNoNeedInvoiceInt !== 1 && hasPermission(functionUris.customizeInvoice)"
                type="success"
                icon="Select"
                :disabled="!(crud2.data && crud2.data.length > 0)"
                :loading="saveLoading"
                @click="save"
              >保存</inno-button-tooltip>
              <!-- v-auth="functionUris.customizeInvoice" -->
              <inno-button-tooltip v-if="hasPermission(functionUris.customizeInvoice) && crud.rowData.saleSource !== 60" type="primary" @click="openImportModel">导入开票</inno-button-tooltip>
              <inno-button-tooltip type="warning" :disabled="!(crud2.data && crud2.data.length > 0)" @click="mergeByUpdate" style="display:none">修订合并</inno-button-tooltip>
              <!-- SPD专用 -->
              <inno-button-tooltip
                type="primary"
                :disabled="!(crud2.data && crud2.data.length > 0)"
                v-if="isSPD && !isNumderSplit"
                @click="mergeByProductNamePriceOriginProductNo"
              >按开票名称、单价、原始规格合并明细</inno-button-tooltip>
              <inno-button-tooltip type="primary" :disabled="!(crud2.data && crud2.data.length > 0)" v-if="!isSPD  && !isNumderSplit" @click="mergeByProductNamePrice">按开票名称、单价合并明细</inno-button-tooltip>
              <inno-button-tooltip
                type="primary"
                :disabled="!(crud2.data && crud2.data.length > 0&& (!crud2.data.some(item => item['originDetailId'].includes(','))))"
                v-if="!isSPD && isNumderSplit"
                @click="mergeByProductQuantity"
              >按原始明细、修订明细合并明细</inno-button-tooltip>
              <inno-button-tooltip type="primary" :disabled="!(crud2.data && crud2.data.length > 0)" v-if="!isSPD && !isNumderSplit" @click="mergeByProductNamePriceProductNo">按开票名称、单价、规格合并明细</inno-button-tooltip>
              <inno-button-tooltip type="primary" :disabled="!(crud2.data && crud2.data.length > 0)" v-if="!isSPD && !isNumderSplit" @click="mergeByProductNameOriginalPrice">按开票名称、单价、原价合并明细</inno-button-tooltip>
              <inno-button-tooltip
                type="primary"
                :disabled="!(crud2.data && crud2.data.length > 0)"
                v-if="!isSPD && !isNumderSplit"
                @click="mergeByProductNameOriginalPriceProductNo"
              >按开票名称、单价、原价、规格合并明细</inno-button-tooltip>
              <inno-button-tooltip type="primary" :disabled="!(crud2.data && crud2.data.length > 0)" @click="clearSpec">清空规格</inno-button-tooltip>

              <el-button @click="onFull" type="primary">
                <inno-svg-Icon :icon-class="full === 'paneR' ? 'restore' : 'enlarge'" class="icon" />
              </el-button>
            </template>
          </inno-crud-operation>
          <el-table
            ref="tableRef2"
            v-inno-loading="crud2.loading"
            class="auto-layout-table"
            highlight-current-row
            border
            :data="crud2.data"
            stripe
            :row-class-name="crud2.tableRowClassName"
            @sort-change="crud2.sortChange"
            @selection-change="crud2.selectionChangeHandler"
            @row-click="crud2.rowClick"
          >
            <el-table-column type="selection" fixed="left" width="35" />
            <el-table-column label="产品名称" property="originProductName" width="150" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObjectDetail.originProductName" :crud="crud2" :column="column" />
              </template>
              <template #default="scope">{{ scope.row.originProductName }}</template>
            </el-table-column>
            <el-table-column label="货物或应税劳务服务名称(开票名称)" property="productName" width="260" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObjectDetail.productName" :crud="crud2" :column="column" />
              </template>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.productName" maxlength="200"></el-input>
                <span v-else>{{ scope.row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原始单位" property="originPackUnit" width="70" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originPackUnit }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="计量单位" property="packUnit" width="70" show-overflow-tooltip>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.packUnit" maxlength="50"></el-input>
                <span v-else>{{ scope.row.packUnit }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原始规格" property="originSpecification" width="100" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originSpecification }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="原始货号包装规格" property="originalPackSpec" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.originalPackSpec }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="是否高值" property="ifHighValue" width="120" show-overflow-tooltip>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.ifHighValue == 1 ? '是' : '否' }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" property="specification" width="100" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input v-if="true" v-model="scope.row.specification" maxlength="50"></el-input>
                <span v-else>{{ scope.row.specification }}</span>
              </template>
            </el-table-column>
            <el-table-column label="数量" property="quantity" width="140" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input-number v-if="true" v-model="scope.row.quantity" style="width: 100%;" :precision="11" :controls="false" @input="(value)=>quantityChange(value,scope.row)"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="单价" property="price" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input-number v-if="true" style="width: 100%;" v-model="scope.row.price" :precision="4" :controls="false" @input="(value)=>priceChange(value,scope.row)"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="原价" property="originalPrice" width="120" show-overflow-tooltip sortable>
              <template #default="scope">
                <el-input-number v-if="true" v-model="scope.row.originalPrice" :precision="4" style="width: 100%;" :controls="false"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="金额" property="value" width="120" show-overflow-tooltip sortable>
              <template #default="scope">{{ asyncNumeral(scope.row.value, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="可开金额" property="noInvoiceAmount" width="100" show-overflow-tooltip sortable>
              <template #default="scope">{{ asyncNumeral(scope.row.noInvoiceAmount, '0,0.00') }}</template>
            </el-table-column>
            <el-table-column label="税率" property="taxRate" width="70" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.taxRate" format="0,0" />
              </template>
            </el-table-column>
            <el-table-column label="税额" property="taxAmount" width="80" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-numeral :value="scope.row.taxAmount" format="0,0.00" />
              </template>
            </el-table-column>
            <el-table-column label="价格类型" property="priceSource" width="100" show-overflow-tooltip>
              <template v-slot:header>
                价格类型
                <el-popover placement="top-start" :width="200" trigger="hover" content="描述：任意明细包含集采则是集采类型">
                  <template #reference>
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.priceSource==1?"集采":(scope.row.priceSource==0?"非集采":"") }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="应收单号" property="creditBillCode" width="150" show-overflow-tooltip sortable>
              <template #header="{ column }">
                <inno-header-filter :config="queryObjectDetail.creditBillCode" :crud="crud2" :column="column" />
              </template>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.creditBillCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="关联单号" property="relateCode" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.relateCode }}</inno-button-copy>
              </template>
            </el-table-column>
            <el-table-column label="订单号" property="orderNo" width="150" show-overflow-tooltip sortable>
              <template #default="scope">
                <inno-button-copy :link="false">{{ scope.row.orderNo }}</inno-button-copy>
              </template>
            </el-table-column>

            <el-table-column label="客户" property="customerName" show-overflow-tooltip sortable minWidth="150">
              <template #default="scope">{{ scope.row.customerName }}</template>
            </el-table-column>
            <el-table-column label="原始明细Id" property="originDetailId" show-overflow-tooltip width="550">
              <template #default="scope">{{ scope.row.originDetailId }}</template>
            </el-table-column>
          </el-table>
          <div class="app-page-footer background">
            共 {{ crud2.data.length }} 条 ；
            <span v-if="crud2.data.length > 0">
              数量合计：
              <inno-numeral :value="dataSum(crud2.data.map((d) => d.quantity))" format="0,0" />；
              金额合计：
              <inno-numeral :value="detailAmountTotal" format="0,0.000" />；
              可开金额合计：
              <inno-numeral :value="dataSum(crud2.selections.map((d) => d.noInvoiceAmount))" format="0,0.000" />；
            </span>
            <div class="flex-1" />
          </div>
        </template>
      </inno-split-pane>
    </div>
    <el-dialog v-model="companyDialogVisible" title="创建申开单" style="width:36.4%">
      <el-form>
        <el-form-item label="开票公司主体" required>
          <inno-remote-select
            v-model="companyId"
            is-guid="2"
            default-first-option
            :queryData="{
        functionUri: 'metadata://fam'
      }"
            placeholder="请选择或填写公司"
            :url="gatewayUrl + 'v1.0/bdsapi/api/companies/meta'"
          />
        </el-form-item>
        <el-form-item label="开票项目主体" required>
          <inno-remote-select
            v-model="projectId"
            :queryData="{
        functionUri: 'metadata://fam',
        status: 2
      }"
            is-guid="2"
            default-first-option
            placeholder="请选择项目"
            :url="gatewayUrl + 'v1.0/pm-webapi/api/ProjectInfo/Authmeta'"
          />
        </el-form-item>
        <el-form-item label="税率或征收率" required>
          <el-input v-model="taxRate" type="number"></el-input>
        </el-form-item>
        <el-form-item label="税收分类编码" required>
          <el-input v-model="taxTypeNo"></el-input>
        </el-form-item>
        <el-form-item label="推送客户邮箱" required>
          <el-checkbox v-model="isPushCustomerEmail" style="float:left;">是否推送客户邮箱</el-checkbox>
          <!-- <el-input style="width:26.5rem;margin-left:1rem;" v-if="isPushCustomerEmail" v-model="customerEmail" 
          v-email-validator clearable placeholder="请输入客户邮箱" @change="validateEmail"></el-input>-->
        </el-form-item>
        <el-form-item label="开票产品名称">
          <el-input v-model="productName"></el-input>
        </el-form-item>
        <el-form-item label="开票产品规格">
          <el-input v-model="specification"></el-input>
        </el-form-item>
        <el-form-item label="开票计量单位">
          <el-input v-model="unit"></el-input>
        </el-form-item>
        <el-form-item label="发票备注说明">
          <el-input v-model="remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="companyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="generateServiceCustomizeInvoiceOpt">生成服务费开票单</el-button>
        </span>
      </template>
    </el-dialog>
    <BatchInvoicingImport ref="ImportDetail" @onImportSuccess="onImportSuccess" @onImportDetailSuccess="onImportDetailSuccess"></BatchInvoicingImport>
  </div>
</template>
<script lang="tsx" setup>
import {
  ref,
  onBeforeMount,
  onMounted,
  onActivated,
  computed,
  watch,
  reactive,
  nextTick
} from 'vue';
import { dateFormat } from '@inno/inno-mc-vue3/lib/utils/filters';
import CRUD, { tableDrag } from '@inno/inno-mc-vue3/lib/crud';
import {
  ElTable,
  ElForm,
  ElMessage,
  ElMessageBox,
  ElLoading,
  ElSelect
} from 'element-plus';
import { getTreeList, getDepartTree } from '@/api/bdsData';
import {
  ContainZeroEnum,
  whetherGroup,
  DebtTypeEnum,
  CreditTypeEnum,
  AbatedStatus,
  InvoiceStatus
} from '@/api/metaInfo';
import request from '@/utils/request';
import { useRouter } from 'vue-router';
import BatchInvoicingImport from './components/batchInvoicingImport.vue';
import { hasPermission } from '@inno/inno-mc-vue3/lib/permission';
import { asyncNumeral } from '@inno/inno-mc-vue3/lib/components/numeral';
import { Decimal } from 'decimal.js';
import { CheckWDTMerge } from './apis/api';
import { useColumnStrategies } from '@inno/inno-mc-vue3/lib/utils/hooks';
let router = useRouter();
const isSPD = ref(false);
const tableRef = ref<InstanceType<typeof ElTable>>();
const amountTotal = ref(0);
const detailAmountTotal = ref(0);
const invoiceAmountTotal = ref(0);
const isNumderSplit = ref(false);
const ImportDetail = ref();
const isMerge = ref(true);
const strategies = ref({
  hidden: []
});
const crud = CRUD(
  {
    title: '待开发票列表',
    url: '/api/CreditQuery/GetList',
    method: 'post',
    idField: 'id',
    userNames: ['createdBy'],
    query: { isNotInvoice: 'notInvoice' },
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
        if (crud.data.length && crud.data.length > 0) {
          const values = crud.data.map((item) => (item.value * 10000) / 10000);
          amountTotal.value = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
          const items = crud.data.map((item) => (item.invoiceAmount * 10000) / 10000);
          invoiceAmountTotal.value = items.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
        }
      }
    },
    tablekey: 'tablekey'
  },
  {
    table: tableRef
  }
);
const tableRef2 = ref<InstanceType<typeof ElTable>>();
const crud2 = CRUD(
  {
    title: '开票明细信息',
    url: '',
    method: 'post',
    idField: 'originDetailId',
    userNames: ['createdBy'],
    query: {},
    resultKey: {
      list: 'list',
      total: 'total'
    },
    hooks: {
      [CRUD.HOOK.afterRefresh]: () => {
        //默认选中第一行
      }
    },
    tablekey: 'tablekey2'
  },
  {
    table: tableRef2
  }
);
//高级查询
const queryList = computed(() => [
  {
    key: 'billCode',
    label: '应收单号',
    show: true
  },
  {
    key: 'relateCode',
    label: '关联单号',
    show: true
  },
  {
    key: 'originOrderNo',
    label: '原始订单号',
    show: true
  },
  {
    key: 'redReversalConsumNo',
    label: '红字消耗单号',
    show: true
  },  {
    key: 'shipmentCode',
    label: '三方开票申请单号',
    show: true
  },
  {
    key: 'orderNo',
    label: '订单号',
    show: true
  },
  {
    key: 'customerIds',
    label: '客户',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/customers/meta`,
    labelK: 'name',
    valueK: 'id',
    multiple: true,
    props: { KeyWord: 'name', resultKey: 'data.data' },
    show: true
  },
  {
    key: 'companyId',
    label: '公司',
    type: 'remoteSelect',
    method: 'post',
    url: `${gatewayUrl}v1.0/bdsapi/api/companies/meta`,
    labelK: 'name',
    valueK: 'id',

    props: {
      KeyWord: 'name',
      resultKey: 'data.data',
      queryData: { functionUri: 'metadata://fam' },
      autoLoad: true,
    },
    show: true
  },
  {
    key: 'creditTypes',
    label: '应收类型',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    multiple: true,
    dataList: CreditTypeEnum,
    show: true
  },
  {
    key: 'isContainZero',
    label: '是否包含0元',
    type: 'select',
    labelK: 'name',
    valueK: 'id',
    dataList: ContainZeroEnum,
    show: true
  },
  {
    key: 'saleSystemName',
    label: '销售子系统',
    show: true
  },
  {
    key: 'note',
    label: '备注',
    show: true
  },
  {
    key: 'deptName',
    label: '部门',
    show: true
  },
  {
    key: 'createdBy',
    label: '创建人',
    multiple: true,
    method: 'post',
    type: 'remoteSelect',
    url: `${window.gatewayUrl}v1.0/userapi/getlistbynames`,
    placeholder: '用户名称搜索',
    valueK: 'name',
    labelK: 'displayName',
    props: { KeyWord: 'displayName', resultKey: 'data.data.list' },
    slots: {
      option: ({ item }) => (
        <>
          <span>{item.displayName}</span>
          <span style="float:right">{item.name}</span>
        </>
      )
    }
  },
  {
    key: 'projectName',
    label: '项目名称',
    show: true
  },
  {
    key: 'customerOrderCode',
    label: '客户订单号',
    show: true
  },
  {
    key: 'customerPersonName',
    label: '订货人',
    show: true
  },
  {
    key: 'creditSaleSubType',
    label: '开票对象',
    type: 'select',
    labelK: 'label',
    valueK: 'value',
    dataList: [
      {
        label: '消费者',
        value: 1
      },
      {
        label: '平台',
        value: 2
      }
    ],
    show: false
  }
]);
const queryObject = computed(() =>
  Object.fromEntries(queryList.value.map((item) => [item.key, item]))
);

const queryListDetail = computed(() => [
  {
    key: 'productName',
    label: '产品名称',
    show: true
  },
  {
    key: 'creditBillCode',
    label: '应收单号',
    show: true
  }
]);
const queryObjectDetail = computed(() =>
  Object.fromEntries(queryListDetail.value.map((item) => [item.key, item]))
);
const oldData = reactive([]);
watch(
  () => crud2.query,
  (n, o) => {
    if (n != null) {
      crud2.data = OriginDetailList.value;
      console.log('原始值', OriginDetailList);
      if (n.productName != null && n.productName.length > 0) {
        crud2.data = crud2.data.filter(
          (p) => p.productName.indexOf(n.productName) > -1
        );
      }
      if (n.creditBillCode != null && n.creditBillCode.length > 0) {
        console.log('进来', n.creditBillCode);
        crud2.data = crud2.data.filter(
          (p) => p.creditBillCode.indexOf(n.creditBillCode) > -1
        );
      }
      // crud2.data=crud2.data.filter(p=>p.productName.indexOf(n.productName)>-1&&p.creditBillCode.indexOf(n.creditBillCode)>-1)
    }
  },
  { deep: true }
);

watch(
  () => crud.selections,
  (n, o) => {
    if (n != null) {
      crud.selections.forEach((item) => { 
        if (item.orderNo!=null&&item.orderNo.includes('RT')) {
          isSPD.value = true;
        } else {
          if (item.saleSource !== 60) {
            isSPD.value = false;
          } else {
            if (item.creditTypeStr !== '服务费应收') {
              isSPD.value = true;
            } else {
              isSPD.value = false;
            }
          }
        }
      });
      isGetDetail = false
    }
  },
  { deep: true }
);

onMounted(() => {
  // 表头拖拽必须在这里执行
  tableDrag(tableRef);
  crud.toQuery();
  const strategiesConfig = {
    functionUri: 'metadata://fam/CustomizeInvoice/routes/index-search',
    url:
      window.gatewayUrl +
      `v1.0/sia-backend/api/purchaseapply/getStrategy?functionUri=metadata://fam/CustomizeInvoice/routes/index-search`,
    method: 'get'
  };
  strategies.value = useColumnStrategies(strategiesConfig);
});
const companyObj = ref({ nameCode: '', companyId: '', companyName: '' });
const projectObj = ref({ projectId: '', projectName: '' });
let OriginDetailList = ref([]);
// 定义加载状态
const isFetchingOriginDetail = ref(false);
//获取原始开票明细
const getOriginDetail = () => {
  if (isFetchingOriginDetail.value) {
    ElMessage({
      showClose: true,
      message: '正在获取原始开票明细，请稍后再试',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  }
  let isEqual = hasMultipleCreditSaleSubTypes(crud.selections);
  if (isEqual) {
    ElMessage({
      showClose: true,
      message: '请勾选待开票对象相同的待开发票应收单',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  let isConsumer = crud.selections.findIndex((item:any) => item.creditSaleSubType === 1);
  let isOriginOrder = areOriginOrderNosSame(crud.selections);
  if(isConsumer !== -1 && !isOriginOrder){
    ElMessage({
      showClose: true,
      message: '开票对象为消费者时，必须选择原始订单号一致的待开发票应收单！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  crud2.data = [];
  if (crud.selections && crud.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选待开发票应收单',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  
  productNamePriceOriginDisable = ref(true);
  let _companyIdsToRepeat = crud.selections.map((s) => s.companyId);
  let _customerIdsToRepeat = crud.selections.map((s) => s.customerId);
  if (
    ToRepeat(_companyIdsToRepeat).length != 1 ||
    ToRepeat(_customerIdsToRepeat).length != 1
  ) {
    ElMessage({
      showClose: true,
      message: '请勾选相同客户与公司',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }

  // SPD校验.
  var i = 0;
  crud.selections.forEach((item) => {
    if (item.saleSource === 60 && item.creditTypeStr !== '服务费应收') {
      i++;
    }
  }) 
  companyObj.value = {
    nameCode: crud.selections[0].nameCode,
    companyId: crud.selections[0].companyId,
    companyName: crud.selections[0].companyName
  };

  projectObj.value = {
    projectId: crud.selections[0].projectId,
    projectName: crud.selections[0].projectName
  };

  let _customerIds = crud.selections.map((s) => s.customerId);
  let _customerNames = crud.selections.map((s) => s.customerName);
  let _creditBillCodes = crud.selections.map((s) => s.billCode);
  let _relateCodes = crud.selections.map((s) => s.relateCode);
  let _orderNos = crud.selections.map((s) => s.orderNo);
  let _values = crud.selections.map((s) => s.value);

  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
 // 设置加载状态为true
  isFetchingOriginDetail.value = true;
  request({
    url: `/api/CustomizeInvoice/GetOriginDetail`,
    method: 'POST',
    data: {
      relateCodes: _relateCodes,
      orderNos: _orderNos,
      creditBillCodes: _creditBillCodes,
      customerIds: _customerIds,
      customerNames: _customerNames,
      values: _values,
      checkData: crud.selections
    }
  })
    .then((res) => {
      if (res.data.code === 200) {
        let origindata = res.data.data.list;
        OriginDetailList.value = JSON.parse(JSON.stringify(origindata));
        
        let items = origindata.findIndex(el=>el.originalId!==null&&el.originalId!==''&&el.originalId!==undefined);
        if(items!==-1){
          isNumderSplit.value = true;
        }else{
          isNumderSplit.value = false;
        }
        crud2.data = origindata;
        isGetDetail = true;
        calculatedAmount(origindata);
      } else {
        crud2.data = [];

        ElMessage({
          showClose: true,
          message: res.data.msg != null ? res.data.msg : res.data.message,
          type: 'error',
          duration: 3 * 1000
        });
      }
      loading.close();
    })
    .catch((err) => {
      crud2.data = [];
      ElMessage({
        showClose: true,
        message: err,
        type: 'error',
        duration: 3 * 1000
      }); 
    })
    .finally(() => {
      // 请求结束后重置加载状态
      isFetchingOriginDetail.value = false;
      loading.close();
    });;

};
// 校验销售子类型
const hasMultipleCreditSaleSubTypes = (array:any) => {
    // 创建一个对象来存储不同的creditSaleSubType值
    const typeCounts = {};
 
    // 遍历数组
    for (let item of array) {
        const type = item.creditSaleSubType;
        
        // 如果type已经在对象中，则计数+1，否则初始化为1
        if (typeCounts[type]) {
            typeCounts[type]++;
        } else {
            typeCounts[type] = 1;
        }
    }
 
    // 计算有多少种不同的类型
    const uniqueTypes = Object.keys(typeCounts).length;
 
    // 返回是否有两种或以上的类型
    return uniqueTypes > 1;
}
const rowClick = (row) => {
  crud.singleSelection(row);
  crud2.data = [];
};
//按【产品名称、单价】 合并明细
const mergeByProductNamePrice =async () => {
  let isExist = crud.selections.findIndex(item=>item.creditSaleSubType === 1);
  let isOrdrts = checkOrders(crud.selections);
  if(isExist !== -1 && !isOrdrts){
    ElMessage({
      showClose: true,
      message: '所选数据中存在不同的原始订单号，无法进行合并！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if(await margeCheck()){
      if (sunPurchaseMergeCheck()) {
      let mergeData = mergeByProductNamePriceOpt(
        JSON.parse(JSON.stringify(crud2.selections))
      );
      crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
      mergeData.forEach((m) => {
        crud2.data.push(m);
      });
      }
    }
  }
};

//按产品名称和单价合并明细
const mergeByProductNamePriceOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productName == arr[j].productName &&
        arr[i].price == arr[j].price
      ) {
        arr[i].specification = '各型'; 
        arr[i].quantity = isNaN(arr[j].quantity)
          ? 0
          : new Decimal(arr[i].quantity).add(parseFloat(arr[j].quantity)); 
          
        arr[i].taxAmount = isNaN(arr[j].taxAmount)
          ? 0
          : new Decimal(arr[i].taxAmount).add(parseFloat(arr[j].taxAmount)); ;
           
        arr[i].value = isNaN(arr[j].value)
          ? 0
          : new Decimal(arr[i].value).add(parseFloat(arr[j].value));
   
        arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));
          
        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};
let productNamePriceOriginDisable = ref(true);
//按货号产品，数量合并
const mergeByProductQuantity =async () => {
  let isExist = crud.selections.findIndex(item=>item.creditSaleSubType === 1);
  let isOrdrts = checkOrders(crud.selections);
  if(isExist !== -1 && !isOrdrts){
    ElMessage({
      showClose: true,
      message: '所选数据中存在不同的原始订单号，无法进行合并！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if(await margeCheck()){
      if (sunPurchaseMergeCheck()) {
      // let mergeData = mergeByProductQuantityOpt(
      //   JSON.parse(JSON.stringify(crud2.selections))
      // );
        let mergeData = queryProductNamePriceOriginProductNo(JSON.parse(JSON.stringify(crud2.selections))) || [];
        if (!mergeData.some(item => item['originDetailId'].includes(','))) {
          ElMessage({
            showClose: true,
            message: '没有满足合并条件的明细数据，合并失败！',
            type: 'error',
            duration: 3 * 1000
          });
          return ;
        }
        if (mergeData?.length == 0) {
          return;
        }
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        }); 
        ElMessage({
            showClose: true,
            message: '合并成功！',
            type: 'success',
            duration: 3 * 1000
          });
      }
    }
  }
};
//按产品名称和单价合并明细 数量不变，金额相加
const mergeByProductQuantityOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productNo == arr[j].productNo &&
        arr[i].quantity == arr[j].quantity
      ) {
        if (Math.abs(arr[i].price) > Math.abs(arr[j].price)) { 
          arr[i].quantity = arr[i].quantity;
        } else {
          arr[i].quantity = arr[j].quantity;
        }
        //价格相减
        arr[i].price = new Decimal(parseFloat(arr[i].price)).add(new Decimal(parseFloat(arr[j].price)));
        arr[i].value = new Decimal(parseFloat(arr[i].quantity)).mul(new Decimal(parseFloat(arr[i].price))); 
        arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));

        arr[i].taxAmount =new Decimal(Math.abs(arr[i].value)).sub(Math.abs(arr[i].value) / (1 + arr[i].taxRate / 100))
        arr[i].taxAmount = arr[i].value > 0 ? arr[i].taxAmount : -arr[i].taxAmount;
        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};

//按【产品名称、单价、规格】 合并 明细
const mergeByProductNamePriceProductNo =async () => {
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if (await margeCheck()) {
      if (sunPurchaseMergeCheck()) {
        let mergeData = mergeByProductNamePriceProductNoOpt(
          JSON.parse(JSON.stringify(crud2.selections))
        );
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        });
      }
    }
  }
};
//按产品名称、单价以及规格合并明细
const mergeByProductNamePriceProductNoOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productName == arr[j].productName &&
        arr[i].price == arr[j].price &&
        arr[i].specification === arr[j].specification
      ) {
        arr[i].quantity = isNaN(arr[j].quantity)
          ? 0
          :  new Decimal(arr[i].quantity).add(parseFloat(arr[j].quantity));
        arr[i].taxAmount = isNaN(arr[j].taxAmount)
          ? 0
          : new Decimal(arr[i].taxAmount).add(parseFloat(arr[j].taxAmount));
        arr[i].value = isNaN(arr[j].value)
          ? 0
          : new Decimal(arr[i].value).add(parseFloat(arr[j].value));

        arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));
          

        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
         // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};

//按【产品名称、单价、原始规格】 合并 明细
const mergeByProductNamePriceOriginProductNo =async () => {
  let isExist = crud.selections.findIndex(item=>item.creditSaleSubType === 1);
  let isOrdrts = checkOrders(crud.selections);
  if(isExist !== -1 && !isOrdrts){
    ElMessage({
      showClose: true,
      message: '所选数据中存在不同的原始订单号，无法进行合并！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if (await margeCheck()) {
      if (sunPurchaseMergeCheck()) {
        let mergeData = mergeByProductNamePriceOriginProductNoOpt(
          JSON.parse(JSON.stringify(crud2.selections))
        );
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        });
      }
    }
  }
};
//按产品名称、单价以及规格合并明细
const mergeByProductNamePriceOriginProductNoOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productName == arr[j].productName &&
        arr[i].price == arr[j].price &&
        arr[i].productId === arr[j].productId
      ) {
        arr[i].quantity = isNaN(arr[j].quantity)
          ? 0
          :  new Decimal(arr[i].quantity).add(parseFloat(arr[j].quantity));
        arr[i].taxAmount = isNaN(arr[j].taxAmount)
          ? 0
          : new Decimal(arr[i].taxAmount).add(parseFloat(arr[j].taxAmount)); ;
      
          arr[i].value = isNaN(arr[j].value)
          ? 0
          : new Decimal(arr[i].value).add(parseFloat(arr[j].value)); ;

          arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));

        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }

        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};

const queryProductNamePriceOriginProductNo = (arr:any) =>{
  let returnList:any = []; 
  let fushuList:any = []
  let sourceList:any = []
  arr.map(el=>{
    if(el.originalId !== ''&& el.originalId !== null && el.originalId !== undefined){
      fushuList.push(el) 
    }
    if(el.saleDetailId !== ''&& el.saleDetailId !== null && el.saleDetailId !== undefined){
      sourceList.push(el) 
    }
    if((el.saleDetailId === ''|| el.saleDetailId === null || el.saleDetailId === undefined)&&(el.originalId === ''|| el.originalId === null || el.originalId === undefined)){
      returnList.push(el)
    }
  });
  
  // if (sourceList.length <= 0 || fushuList.length <= 0) {
  //   ElMessage({
  //     showClose: true,
  //     message: '没有满足合并条件的明细数据，合并失败！',
  //     type: 'error',
  //     duration: 3 * 1000
  //   });
  //   return ;
  // }
  
  fushuList.sort((a, b) => a.quantity - b.quantity);
fushuList.forEach(item1 => {
  // 在 arr2 中查找匹配的项
  const matchingItem2 = sourceList.find(item2 => 
      item2.saleDetailId === item1.originalId && 
      item2.quantity === item1.quantity
  );
  if (matchingItem2) {
      // 如果找到匹配项，合并 arr1 和 arr2 中的对象
      let newPrice = new Decimal(item1.price).add(new Decimal(matchingItem2.price)).toNumber()
      const mergedItem = { ...item1,
        // price : newPrice,
        // noInvoiceAmount : asyncNumeral(new Decimal(newPrice).mul(new Decimal(item1.quantity)).toNumber(), '0,0.00'),
        // value : asyncNumeral(new Decimal(newPrice).mul(new Decimal(item1.quantity)).toNumber(), '0,0.00'),
        noInvoiceAmount : new Decimal(newPrice).mul(new Decimal(item1.quantity)).toNumber(),
        value : new Decimal(newPrice).mul(new Decimal(item1.quantity)).toNumber(),
        taxAmount :  new Decimal((new Decimal(new Decimal(newPrice).mul(new Decimal(item1.quantity)).toNumber()).div(new Decimal(1.13)))).mul(new Decimal(0.13)),
        creditBillCode:matchingItem2.creditBillCode !== item1.creditBillCode? matchingItem2.creditBillCode + ',' + item1.creditBillCode:matchingItem2.creditBillCode,
        relateCode:matchingItem2.relateCode !== item1.relateCode? matchingItem2.relateCode + ',' + item1.relateCode:matchingItem2.relateCode,
        orderNo:matchingItem2.orderNo !== item1.orderNo? matchingItem2.orderNo + ',' + item1.orderNo:matchingItem2.orderNo,
        originDetailId:matchingItem2.originDetailId !== item1.originDetailId? matchingItem2.originDetailId + ',' + item1.originDetailId:matchingItem2.originDetailId,
        agentId:matchingItem2.agentId !== item1.agentId? matchingItem2.agentId + ',' + item1.agentId:matchingItem2.agentId,
        ifHighValue:matchingItem2.ifHighValue === 1 || item1.ifHighValue === 1?1:null,
        priceSource :matchingItem2.priceSource === 1 || item1.priceSource === 1?1:(matchingItem2.priceSource === 0 || item1.priceSource === 0?0:null)
        };
        returnList.push(mergedItem);
      let num = fushuList.indexOf( item1);
      if (num > -1) {
        fushuList.splice(num, 1); // 从索引处删除一个元素
      }
      let index = sourceList.indexOf(matchingItem2);
      if (index > -1) {
        sourceList.splice(index, 1); // 从索引处删除一个元素
      }
  }
});
  sourceList.forEach(element => {
    fushuList.forEach(item => {
      if(element.saleDetailId === item.originalId ){
        if(element.quantity-item.quantity >= 0 ){
          element.quantity = new Decimal(element.quantity).sub(new Decimal(item.quantity)).toNumber() ;
          item.price = new Decimal(element.price).add(new Decimal(item.price)).toNumber();
          // item.taxAmount = new Decimal(element.taxAmount).add(new Decimal(item.taxAmount)).toNumber(); (金额/(1+0.13))*0.13
          item.noInvoiceAmount = new Decimal(item.price).mul(new Decimal(item.quantity)).toNumber();
          item.value = new Decimal(item.price).mul(new Decimal(item.quantity)).toNumber();
          item.taxAmount =  new Decimal((new Decimal(item.value).div(new Decimal(1.13)))).mul(new Decimal(0.13));
          // item.value = asyncNumeral(new Decimal( isString(item.price)?item.price.replace(/,/g, ''):item.price).mul(new Decimal(item.quantity)).toNumber(), '0,0.00');
          // item.taxAmount =  new Decimal((new Decimal(isString(item.value)?item.value.replace(/,/g, ''):item.value).div(new Decimal(1.13)))).mul(new Decimal(0.13));

          if(item.creditBillCode !== element.creditBillCode){
            item.creditBillCode = item.creditBillCode + ',' + element.creditBillCode;
          }
          if(item.relateCode !== element.relateCode){
            item.relateCode = item.relateCode + ',' + element.relateCode;
          }
          if(item.orderNo !== element.orderNo){
            item.orderNo = item.orderNo + ',' + element.orderNo;
          }
          if(item.originDetailId !== element.originDetailId){
            item.originDetailId =  item.originDetailId + ',' + element.originDetailId;
          }
          if(item.agentId !== element.agentId){
            item.agentId =  item.agentId + ',' + element.agentId;
          }
          if (item.ifHighValue == 1 || element.ifHighValue == 1) {
            item.ifHighValue = 1
          }
          // 处理价格类型
          if (item.priceSource === 1 || element.priceSource === 1) {
             item.priceSource = 1
          }else{
            if (item.priceSource === 0 || element.priceSource === 0) {
              item.priceSource = 0
            }
          }
          returnList.push(item);
        }else{
          returnList.push(item);
        }
      }
    });
    if(element.quantity !== 0){
      element.noInvoiceAmount = new Decimal(element.price).mul(new Decimal(element.quantity)).toNumber();
      element.value = new Decimal(element.price).mul(new Decimal(element.quantity));
      returnList.push(element);
    }
  });
  fushuList.forEach(itemB => {
        const isMatched = sourceList.some(itemA => itemA.saleDetailId === itemB.originalId);
        if (!isMatched) {
          returnList.push(itemB);
        }
    });

  // console.log(returnList,'这是处理以后的数据==============================')
  return returnList;
}
function isString(value:any) {
  return typeof value === "string" || value instanceof String;
}
 
//按【产品名称、单价、原价】 合并明细
const mergeByProductNameOriginalPrice = async () => {
  let isExist = crud.selections.findIndex(item=>item.creditSaleSubType === 1);
  let isOrdrts = checkOrders(crud.selections);
  if(isExist !== -1 && !isOrdrts){
    ElMessage({
      showClose: true,
      message: '所选数据中存在不同的原始订单号，无法进行合并！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if (await margeCheck()) {
      if (sunPurchaseMergeCheck()) {
        let mergeData = mergeByProductNameOriginalPriceOpt(
          JSON.parse(JSON.stringify(crud2.selections))
        );
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        });
      }
    }
  }
};

//按【产品名称、单价、原价】合并明细
const mergeByProductNameOriginalPriceOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productName == arr[j].productName &&
        arr[i].price == arr[j].price &&
        arr[i].originalPrice == arr[j].originalPrice
      ) {
        arr[i].specification = '各型';
        arr[i].quantity = isNaN(arr[j].quantity)
          ? 0
          :  new Decimal(arr[i].quantity).add(parseFloat(arr[j].quantity));
        arr[i].taxAmount = isNaN(arr[j].taxAmount)
          ? 0
          : new Decimal(arr[i].taxAmount).add(parseFloat(arr[j].taxAmount)); ; 
          arr[i].value = isNaN(arr[j].value)
          ? 0
          : new Decimal(arr[i].value).add(parseFloat(arr[j].value)); ;

          arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));
        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};
//按【产品名称、单价、规格、原价】 合并 明细
const mergeByProductNameOriginalPriceProductNo =async () => {
  let isExist = crud.selections.findIndex(item=>item.creditSaleSubType === 1);
  let isOrdrts = checkOrders(crud.selections);
  if(isExist !== -1 && !isOrdrts){
    ElMessage({
      showClose: true,
      message: '所选数据中存在不同的原始订单号，无法进行合并！',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if (await margeCheck()) {
      if (sunPurchaseMergeCheck()) {
        let mergeData = mergeByProductNameOriginalPriceProductNoOpt(
          JSON.parse(JSON.stringify(crud2.selections))
        );
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        });
      }
    }
  }
};

//按【产品名称、单价、规格、原价】合并明细
const mergeByProductNameOriginalPriceProductNoOpt = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (
        arr[i].productName == arr[j].productName &&
        arr[i].price == arr[j].price &&
        arr[i].specification === arr[j].specification &&
        arr[i].originalPrice == arr[j].originalPrice
      ) {
       arr[i].quantity = isNaN(arr[j].quantity)
          ? 0
          :  new Decimal(arr[i].quantity).add(parseFloat(arr[j].quantity));
        arr[i].taxAmount = isNaN(arr[j].taxAmount)
          ? 0
          : new Decimal(arr[i].taxAmount).add(parseFloat(arr[j].taxAmount)); ; 
          arr[i].value = isNaN(arr[j].value)
          ? 0
          : new Decimal(arr[i].value).add(parseFloat(arr[j].value)); ;

          arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));
        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};
//阳采合并校验
const sunPurchaseMergeCheck = () => {
  let _saleSourceToRepeat = crud.selections.map((s) => s.saleSource);
  let _orderNoToRepeat = crud.selections.map((s) => s.orderNo);
  let _productNoToRepeat = crud2.selections.map((s) => s.productNo);
  if (crud.selections.length === 1 && crud.selections[0].saleSource === 110) {
    ElMessage({
      showClose: true,
      message: '单个阳采应收单不可合并明细',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  else if (crud.selections.length > 1 //多个
    && ToRepeat(_saleSourceToRepeat).length == 1
    && ToRepeat(_saleSourceToRepeat)[0] == 110 //阳采
    && ToRepeat(_orderNoToRepeat).length == 1 //相同订单号
    && ToRepeat(_productNoToRepeat).length != 1 //不同货号
  ) {
    ElMessage({
      showClose: true,
      message: '多个阳采应收单，订单号相同，货号不同，数量不为正负，不可合并明细',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  } else {
    return true;
  }
}
//合并金额 数量 单价 校验
const margeCheck = () => { 
   return request({
      url: `/api/CustomizeInvoice/MargeCheck`,
      method: 'POST',
      data: {details:crud2.selections,source:0}
    })
      .then((res) => {
        if (res.data.code === 200) { 
          return true;
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
        return false;
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
        return false;
      })

}
//修订合并
const mergeByUpdate =async () => {
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要合并的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    if (await margeCheck()) {
      if (sunPurchaseMergeCheck()) {
        let mergeData = mergeByUpdateOpt(
          JSON.parse(JSON.stringify(crud2.selections))
        );
        crud2.data = crud2.data.filter((d) => !crud2.selections.includes(d));
        mergeData.forEach((m) => {
          crud2.data.push(m);
        });
      }
    }
  }
};
//修订合并
const mergeByUpdateOpt = (arr) => {
  debugger;
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i].originalId == arr[j].originalId&&arr[i].originalId!=null) { 
        if (Math.abs(arr[i].price) > Math.abs(arr[j].price)) { 
          arr[i].quantity = arr[i].quantity;
        } else {
          arr[i].quantity = arr[j].quantity;
        }
        //价格相减
        arr[i].price = new Decimal(parseFloat(arr[i].price)).sub(new Decimal(parseFloat(arr[j].price)))
        arr[i].price = Math.abs(arr[i].price);
        arr[i].value =new Decimal(parseFloat(arr[i].quantity)).mul(new Decimal(parseFloat(arr[i].price))) 
        arr[i].noInvoiceAmount = isNaN(arr[j].noInvoiceAmount)
          ? 0
          : new Decimal(arr[i].noInvoiceAmount).add(parseFloat(arr[j].noInvoiceAmount));

        arr[i].taxAmount =new Decimal(Math.abs(arr[i].value)).sub(Math.abs(arr[i].value) / (1 + arr[i].taxRate / 100))
        arr[i].taxAmount = arr[i].value > 0 ? arr[i].taxAmount : -arr[i].taxAmount;
        if (arr[i].creditBillCode.indexOf(arr[j].creditBillCode) < 0) {
          arr[i].creditBillCode =
            arr[i].creditBillCode + ',' + arr[j].creditBillCode;
        }
        if (arr[i].relateCode.indexOf(arr[j].relateCode) < 0) {
          arr[i].relateCode = arr[i].relateCode + ',' + arr[j].relateCode;
        }
        if (
          arr[i].orderNo != null &&
          arr[i].orderNo.indexOf(arr[j].orderNo) < 0
        ) {
          arr[i].orderNo = arr[i].orderNo + ',' + arr[j].orderNo;
        }
        if (
          arr[i].originDetailId != null &&
          arr[i].originDetailId.indexOf(arr[j].originDetailId) < 0
        ) {
          arr[i].originDetailId =
            arr[i].originDetailId + ',' + arr[j].originDetailId;
        }
        if (
          arr[i].agentId != null &&
          arr[i].agentId.indexOf(arr[j].agentId) < 0
        ) {
          arr[i].agentId =
            arr[i].agentId + ',' + arr[j].agentId;
        }
        if (arr[i].ifHighValue == 1 || arr[j].ifHighValue == 1) {
          arr[i].ifHighValue = 1
        }
        // 处理价格类型
        if (arr[i].priceSource === 1 || arr[j].priceSource === 1) {
          arr[i].priceSource = 1;
        }else{
          if (arr[i].priceSource === 0 || arr[j].priceSource === 0) {
            arr[i].priceSource = 0;
          }
        }
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
};
//清空规格
const clearSpec = () => {
  // crud2.data.forEach((item) => {
  //   item.specification = ''
  // });
  console.log(JSON.stringify(crud2.selections))
  if (crud2.selections && crud2.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请勾选要清空规格的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  } else {
    crud2.selections.forEach((item) => {
      item.specification = ''
    });
  }
}
//数组去重复
const ToRepeat = (arr) => {
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[i] == arr[j]) {
        arr.splice(j, 1);
        j--;
      }
    }
  }
  return arr;
}; 
const getCurrentRow = (row) => {
  // currentOriginDetailId = row.originDetailId;
  
};
//数组求和
const dataSum = (arr) => {
  var s = 0;
  arr.forEach((val) => {
    s =new Decimal(s).add(parseFloat(val));
  }, 0);
  return s;
};
const saveLoading = ref(false);

//保存
const save = async() => {
 if (isFetchingOriginDetail.value) {
 ElMessage({
   showClose: true,
   message: '正在获取原始开票明细，请稍后再试',
   type: 'warning',
   duration: 3 * 1000
 });
 return;
  }
  if (crud.selections && crud.selections.length == 0) {
    ElMessage({
      showClose: true,
      message: '请选择要开票单的应收单',
      type: 'error',
      duration: 3 * 1000
    });
    return;
  }
  if((crud.selections.findIndex(el=>el.originOrderNo===null || el.originOrderNo === undefined || el.originOrderNo ==='')===-1) && (crud.selections.findIndex(el=>el.creditSaleSubType !==null && el.creditSaleSubType  !== undefined && el.creditSaleSubType  !=='')!==-1)&& (crud.selections.findIndex(el=>el.creditSaleSubType ===1) > -1)){
    let postData:any =[]
    crud.selections.map(el=>{
      postData.push({
        billCode: el.billCode,
        originOrderNo: el.originOrderNo
      })
    })
    let details = {
      details:postData
    }
    let res = await CheckWDTMerge(details);
    if(res &&  res.data.code !== 200){
      ElMessage({
        showClose: true,
        message: res.data.message,
        type: 'error',
        duration: 3 * 1000
      });
      return false;
    }
  }
  
  saveLoading.value = true;
  var checkQuantity = true;
  var highvalueMap = new Map();
  if (crud2.data === null || crud2.data.length > 0) {
    crud2.data.forEach((c2) => {
      if (!highvalueMap.has(c2.ifHighValue)) {
        highvalueMap.set(c2.ifHighValue, 1)
      }
      if (c2.quantity === '' || c2.quantity === undefined) {
        checkQuantity = false;
      }
      (c2.nameCode = companyObj.value.nameCode),
        (c2.companyId = companyObj.value.companyId),
        (c2.companyName = companyObj.value.companyName);
    });

    if (highvalueMap.has(1) && (highvalueMap.has(null) || highvalueMap.has(0))) {
      ElMessageBox.confirm(
        '您的开票明细中同时存在高值和低值明细，是否确认提交?',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          if (!checkQuantity) {
            ElMessage({
              showClose: true,
              message: '请完整填写明细中的数量',
              type: 'error',
              duration: 3 * 1000
            });
            return;
          } 
          crud2.data.forEach(element => {
            element.value = asyncNumeral(element.value,'0,0.00')
          });
          request({
            url: `/api/CustomizeInvoice/SaveCustomizeInvoice`,
            method: 'POST',
            data: { detailList: crud2.data, originDetailList: OriginDetailList.value, creditSaleSubType:crud.selections[0].creditSaleSubType}
          })
            .then((res) => {
              if (res.data.code === 200) {
                crud2.data = [];
                crud.toQuery();
                ElMessage({
                  showClose: true,
                  message: '保存成功!单号：' + res.data.msg,
                  type: 'success',
                  duration: 3 * 1000
                });
              } else {
                ElMessage({
                  showClose: true,
                  message: res.data.msg != null ? res.data.msg : res.data.message,
                  type: 'error',
                  duration: 3 * 1000
                });
              }
            })
            .catch((err) => {
              ElMessage({
                showClose: true,
                message: err,
                type: 'error',
                duration: 3 * 1000
              });
            })
            .finally(() => { 
              // 请求结束后重置加载状态
              isFetchingOriginDetail.value = false;
              saveLoading.value = false;
            });
        })
        .catch(() => {
          saveLoading.value = false;
        })

    } else {
      if (!checkQuantity) {
        ElMessage({
          showClose: true,
          message: '请完整填写明细中的数量',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      } 
      crud2.data.forEach(element => {
        element.value = asyncNumeral(element.value,'0,0.00')
      });
      request({
        url: `/api/CustomizeInvoice/SaveCustomizeInvoice`,
        method: 'POST',
        data: { detailList: crud2.data, originDetailList: OriginDetailList.value, creditSaleSubType:crud.selections[0].creditSaleSubType}
      })
        .then((res) => {
          if (res.data.code === 200) {
            crud2.data = [];
            crud.toQuery();
            ElMessage({
              showClose: true,
              message: '保存成功!单号：' + res.data.msg,
              type: 'success',
              duration: 3 * 1000
            });
          } else {
            ElMessage({
              showClose: true,
              message: res.data.msg != null ? res.data.msg : res.data.message,
              type: 'error',
              duration: 3 * 1000
            });
          }
        })
        .catch((err) => {
          ElMessage({
            showClose: true,
            message: err,
            type: 'error',
            duration: 3 * 1000
          });
        })
        .finally(() => {     
          // 请求结束后重置加载状态
          isFetchingOriginDetail.value = false;
          saveLoading.value = false;
        });
    }
  } else {
    ElMessage({
      showClose: true,
      message: '没有要保存的开票明细',
      type: 'error',
      duration: 3 * 1000
    });
    saveLoading.value = false;
  }
};

let companyDialogVisible = ref(false);
let companyId = ref('');
let projectId = ref('');
let taxRate = ref(6);
let taxTypeNo = ref('*********');
let productName = ref('服务费');
let specification = ref('服务费');
let unit = ref('');
let remark = ref('');
const isPushCustomerEmail = ref(false);
const customerEmail = ref('');
const isEmailValid = ref(false);
 
//一键生成服务费申开单
const generateServiceCustomizeInvoice = () => {
  companyId.value = crud.selections[0].companyId;
  projectId.value = crud.selections[0].projectId;
  remark.value = crud.selections[0].note;
  productName.value = '服务费';
  specification.value = '服务费';
  unit.value = '';
  companyDialogVisible.value = true;
};
const generateServiceCustomizeInvoiceOpt = () => {
  if (isFetchingOriginDetail.value) {
    ElMessage({
      showClose: true,
      message: '正在提交，请稍后再试',
      type: 'warning',
      duration: 3 * 1000
    });
    return;
  } 
  if (companyId.value == null || companyId.value == '' || companyId.value == undefined || companyId.value.length <= 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：请选择开票的公司主体',
      type: 'error',
      duration: 3 * 1000
    });
  }
  else if (projectId.value == null || projectId.value == '' || projectId.value == undefined) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：请选择开票的项目主体',
      type: 'error',
      duration: 3 * 1000
    });
  }
  else if (taxRate.value == null || taxRate.value == 0) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：税率或征收率不能为空或者为0',
      type: 'error',
      duration: 3 * 1000
    });
  }
  else if (
    taxTypeNo.value == null ||
    taxTypeNo.value == '' ||
    taxTypeNo.value.length <= 0
  ) {
    ElMessage({
      showClose: true,
      message: '操作失败，原因：税收分类编码不能为空',
      type: 'error',
      duration: 3 * 1000
    });
  }
  else {
    const loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    // 设置加载状态为true
    isFetchingOriginDetail.value = true;
    request({
      url: `/api/CustomizeInvoice/generateServiceCustomizeInvoice`,
      method: 'POST',
      data: {
        companyId: companyId.value,
        projectId: projectId.value,
        taxTypeNo: taxTypeNo.value,
        taxRate: taxRate.value,
        productName: productName.value,
        specification: specification.value,
        unit: unit.value,
        remark: remark.value,
        isPushCustomerEmail: isPushCustomerEmail.value,
        customerEmail: customerEmail.value
      }
    })
      .then((res) => {
        if (res.data.code === 200) {
          crud2.data = [];
          crud.toQuery();
          companyDialogVisible.value = false;
          ElMessage({
            showClose: true,
            message: '操作成功!',
            type: 'success',
            duration: 3 * 1000
          });
        } else {
          ElMessage({
            showClose: true,
            message: res.data.msg != null ? res.data.msg : res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
        }
      })
      .catch((err) => {
        ElMessage({
          showClose: true,
          message: err,
          type: 'error',
          duration: 3 * 1000
        });
      })
      .finally(() => {
       // 请求结束后重置加载状态
        isFetchingOriginDetail.value = false;
        loading.close();
      });
  }
};
const functionUris = {
  customizeInvoice: 'metadata://fam/CustomizeInvoice'
};
//无需开票
const noNeedInvoice = () => {
  ElMessageBox.confirm('确认无需开票, 是否继续?', '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (crud.selections && crud.selections.length <= 1) {
        ElMessage({
          showClose: true,
          message: '操作失败，原因：无需开发票的应收单，必须是2条及以上的应收单',
          type: 'error',
          duration: 3 * 1000
        });
        return;
      } else {
        let _creditBillCodes = crud.selections.map((s) => s.billCode);
        request({
          url: `/api/Credit/noNeedInvoice`,
          method: 'POST',
          data: {
            creditBillCodes: _creditBillCodes
          }
        })
          .then((res) => {
            if (res.data.code === 200) {
              crud.toQuery();
              ElMessage({
                showClose: true,
                message: '操作成功！',
                type: 'success',
                duration: 3 * 1000
              });
            } else {
              ElMessage({
                showClose: true,
                message: res.data.message,
                type: 'error',
                duration: 3 * 1000
              });
            }
          })
          .catch((err) => {
            ElMessage({
              showClose: true,
              message: err,
              type: 'error',
              duration: 3 * 1000
            });
          });
      }
    })
};
const tabhandleClick = () => {
  crud.toQuery();
};
//是否获取原始开票明细
let isGetDetail = ref(false);
//导出数据
const exportData = async () => {

  const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  var exportType = ''
  var dataParams = '';
  if (crud.selections.length > 0 && isGetDetail) {//获取选中的主单加明细
    exportType = '/api/CustomizeInvoice/ExportCustomizeInvoiceItemAndDetail'
    let _customerIds = crud.selections.map((s) => s.customerId);
    let _customerNames = crud.selections.map((s) => s.customerName);
    let _creditBillCodes = crud.selections.map((s) => s.billCode);
    let _relateCodes = crud.selections.map((s) => s.relateCode);
    let _orderNos = crud.selections.map((s) => s.orderNo);
    let _values = crud.selections.map((s) => s.value);

    dataParams = {
      relateCodes: _relateCodes,
      orderNos: _orderNos,
      creditBillCodes: _creditBillCodes,
      customerIds: _customerIds,
      customerNames: _customerNames,
      values: _values,
      checkData: crud.selections
    }
  } else if (crud.selections.length > 0 && isGetDetail == false) {
    exportType = '/api/CustomizeInvoice/ExportCustomizeInvoiceItemByIds'
    let _customerIds = crud.selections.map((s) => s.customerId);
    let _customerNames = crud.selections.map((s) => s.customerName);
    let _creditBillCodes = crud.selections.map((s) => s.billCode);
    let _relateCodes = crud.selections.map((s) => s.relateCode);
    let _orderNos = crud.selections.map((s) => s.orderNo);
    let _values = crud.selections.map((s) => s.value);

    dataParams = {
      relateCodes: _relateCodes,
      orderNos: _orderNos,
      creditBillCodes: _creditBillCodes,
      customerIds: _customerIds,
      customerNames: _customerNames,
      values: _values,
      checkData: crud.selections
    }

  } else {  //获取所有主单
    exportType = '/api/CustomizeInvoice/ExportCustomizeInvoiceItem'
    dataParams = crud.query;
  }
  await request({
    url: exportType,
    data: dataParams,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      loading.close();
      const xlsx =
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx });
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '运营制作开票导出文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch(() => {
      loading.close();
    });
  console.log(JSON.stringify(crud.selections));
};
const detailRelate = (relateCode) => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    router.goAppPage({
      path: '/inventory/docrelate', // fm为要跳转的子应用前缀
      query: {
        code: relateCode,
        title: '关联单据查询'
      },
      params: {
        // 是否需要刷新目标页面
        __reload: true
      }
    });
  } else {
    console.error('非微前端环境');
  }
}
function handleAllSelectionChange(items: any,isAll?: any) {
  crud.selections = items;
  if (items.length > 0) {
    const values = items.map((item) => (item.value * 10000) / 10000);
    amountTotal.value = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
    const invoiceValues = items.map((item) => (item.invoiceAmount * 10000) / 10000);
    invoiceAmountTotal.value = invoiceValues.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);

  } else {
    const values = crud.data.map((item) => (item.value * 10000) / 10000);
    amountTotal.value = values.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
    const invoiceValues = crud.data.map((item) => (item.invoiceAmount * 10000) / 10000);
    invoiceAmountTotal.value = invoiceValues.reduce((prev, curr) => (prev * 10000 + curr * 10000) / 10000, 0);
  }
  
}
//导入
const openImportModel = () => {
  let isEqual = hasMultipleCreditSaleSubTypes(crud.selections);
  if (isEqual) {
    ElMessage({
      showClose: true,
      message: '请勾选待开票对象相同的开发票应收单',
      type: 'error',
      duration: 3 * 1000
    });
    return false;
  }
  ImportDetail.value.detailImportModel.showDialog = true;
}
//导入成功回调
const onImportSuccess = async (datas: any) => {
  crud.toQuery();
};
const onImportDetailSuccess = async (datas: any) => {
  crud2.data = datas.creditDetails;
  OriginDetailList.value = datas.creditDetails;
  crud.data = datas.credits;
  tableRef.value?.toggleAllSelection();  
};
const quantityChange = (value:any,row:any) =>{
  if(value){
    row.value = new Decimal(Number(value)).mul(new Decimal(Number(row.price))).toNumber()
  }
  calculatedAmount(crud2.data);
}
const priceChange = (value:any,row:any) =>{
  if(value){
    row.value = new Decimal(Number(value)).mul(new Decimal(Number(row.quantity))).toNumber() 
  }
  calculatedAmount(crud2.data);
}
const calculatedAmount = (data:any) =>{
  detailAmountTotal.value = data.reduce((accumulator:any, currentValue:any) => {
    return new Decimal(Number(accumulator)).add(new Decimal(Number(currentValue.value))).toNumber() 
  }, 0);
}
  let downLoading = ref(false);
  //协调服务导出
  const downloadAsync = (
    url: String,
    fileName: String,
    type: String = 'post'
  ) => {
    downLoading.value = true;
    let _creditBillCodes = crud.selections.map((s) => s.billCode);
    crud.query.BillCodes = _creditBillCodes;
    request({
      url: url,
      method: type,
      data: crud.query,
      dataType: 'json',
      headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    })
      .then((res) => {
        if (res.data.code === 200) {
          ElMessage({
            showClose: true,
            message: '已发送到后台导出中，导出文件可在消息列表附件中下载',
            type: 'success',
            duration: 3 * 1000
          });
          downLoading.value = false;
          return true;
        }
        else {
          ElMessage({
            showClose: true,
            message: res.data.message,
            type: 'error',
            duration: 3 * 1000
          });
          downLoading.value = false;
          return false;
        }
      })
      .catch((t) => {
        downLoading.value = false;
      });
  };
  const checkOrders = (array) => {
    if (array.length === 0) {
        return false;
    }
    let firstOrderOriginOrderNo = array[0].originOrderNo;
    let allCreditSaleSubTypeAreOne = array[0].creditSaleSubType === 1;
 
    // 遍历数组中的每个订单
    for (let i = 1; i < array.length; i++) {
        if (array[i].originOrderNo !== firstOrderOriginOrderNo) {
            return false;
        }
        if (array[i].creditSaleSubType !== 1) {
            return false;
        }
    }
    return true;
}
const areOriginOrderNosSame = (array:any) => {
    if (array.length === 0) {
        return true;
    }
    const firstOriginOrderNo = array[0].originOrderNo;
    for (let i = 1; i < array.length; i++) {
        if (array[i].originOrderNo !== firstOriginOrderNo) {
            return false;
        }
    }
    return true;
}
</script>
