﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Newtonsoft.Json;
using Polly;
namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class FreeGoodsStoreOutAppService : BaseAppService, IFreeGoodsStoreOutAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        public FreeGoodsStoreOutAppService(ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork unitOfWork,
            IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IKingdeeApiClient kingdeeApiClient,
            Func<int, TimeSpan> sleepDurationProvider = null)
            : base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            _inventoryApiClient = inventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreOutOutput storeout = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);

                    if (storeout == null || !storeout.Details.Any())
                    {
                        throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                    if (string.IsNullOrEmpty(storeout.checker))
                    {
                        throw new Exception("该单据没有复核人");
                    }
                });
                if (storeout != null)
                {
                    var requestBody = JsonConvert.SerializeObject(input);
                    var ret = await PushToKingdee(new List<InventoryStoreOutOutput> { storeout }, input.BusinessSubType, requestBody);
                    return ret;
                }
                else
                {
                    throw new Exception("【库存】没有找到出库数据");
                }

            }
            catch
            {
                throw;
                //throw new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }

        private async Task<BaseResponseData<int>> PushToKingdee(List<InventoryStoreOutOutput> inputs, string classify, string preRequestBody)
        {
            if (inputs == null || !inputs.Any()) return BaseResponseData<int>.Failed(500, "订阅入库事件出错，原因：未找到出库单数据");
            List<HoldStockRemovalInput> kdinputs = new List<HoldStockRemovalInput>();
            foreach (var input in inputs)
            {
                var inputKD = new HoldStockRemovalInput()
                {
                    billno = input.storeOutCode,
                    jfzx_date = DateTimeHelper.LongToDateTime(input.billDate),
                    jfzx_tallydate = DateTimeHelper.LongToDateTime(input.billDate),
                    //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                    jfzx_customer = input.customerId?.ToString().ToUpper(),
                    org = input.companyName,
                    jfzx_businessorg = input.businessDeptId.ToString().ToUpper(),
                    jfzx_remake = input.remark ?? "无",
                    jfzx_creator = input.createdBy ?? "none",
                    StoreOutType = input.storeOutType
                };
                var sysMonth = await _bDSApiClient.GetSystemMonth(input.companyId.ToString());
                DateTime.TryParse(sysMonth, out DateTime billDate);
                inputKD.jfzx_tallydate = billDate;

                if (input.Details != null && input.Details.Any())
                {
                    var fk_jfzx_totalsalescost = 0m;
                    var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                    var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                    var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                    var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                    inputKD.holdStockRemovalEntrysModel = new List<HoldStockRemovalDetail>();

                    foreach (var detail in input.Details)
                    {
                        var thisProject = projectInfo.FirstOrDefault(t => t.Id == detail.projectId);
                        var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == detail.productNameId);
                        var jfzx_material = Guid.Empty;
                        if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                        {
                            jfzx_material = thisProductInfo.classificationNewGuid.Value;
                        }
                        else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                        {
                            jfzx_material = thisProductInfo.classificationGuid.Value;
                        }
                        var taxCost = detail.mark == 0 || detail.mark == 3 ? detail.unitCost.Value : detail.standardUnitCost.Value;
                        //var taxCost = detail.unitCost.Value;
                        var noTaxCost = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 2);//不含税成本
                        //销售成本总额
                        var noTaxCost10 = Math.Round(taxCost / (1 + detail.taxRate.Value / 100.00M), 10);//不含税成本
                        if (detail.mark == 0 || detail.mark == 3)
                        {
                            fk_jfzx_totalsalescost += noTaxCost10 * detail.quantity;
                        }
                        else
                        {
                            fk_jfzx_totalsalescost += detail.standardUnitCost.Value * detail.quantity;
                        }

                        var detailInfo = new HoldStockRemovalDetail
                        {
                            jfzx_count = detail.quantity,
                            jfzx_material = jfzx_material.ToString().ToUpper(),
                            jfzx_model = detail.specification,
                            jfzx_projectnos = thisProject?.Code,
                            jfzx_suppliers = detail.agentId.Value.ToString().ToUpper(),
                            Mark = detail.mark,
                            jfzx_unitprice = detail.mark == 0 || detail.mark == 3 ? noTaxCost : detail.standardUnitCost.Value,
                            taxrateid_number = classify=="样品出库"? getTaxrateidNumber(detail.taxRate) : getTaxrateidNumber(detail.priceTaxRate),
                            jfzx_tax = getTaxAmount(detail.price, detail.priceTaxRate),
                            jfzx_expectsellprice = detail.price,
                            jfzx_expectsellamount = detail.quantity * detail.price,

                        };
                        detailInfo.jfzx_purtax = Math.Round(taxCost - detailInfo.jfzx_unitprice, 2);
                        detailInfo.jfzx_sellingcost = detail.quantity * detailInfo.jfzx_unitprice;
                        if (detailInfo.jfzx_sellingcost != 0 || detailInfo.jfzx_expectsellprice != 0)
                        {
                            inputKD.holdStockRemovalEntrysModel.Add(detailInfo);
                        }
                    }
                    inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
                    if (inputKD.holdStockRemovalEntrysModel.Count() > 0)
                    {
                        kdinputs.Add(inputKD);
                    }
                }
            }
            if (kdinputs.Any())
            {
                var kingdeeRes = await _kingdeeApiClient.PushStoreOutToKingdeeWithoutFinance(kdinputs, classify, preRequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                }
                else
                {
                    return BaseResponseData<int>.Failed(500, kingdeeRes.Message);
                }
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "【库存】没有找到出库数据");
            }
            return BaseResponseData<int>.Success("操作成功");

        }

        public async Task<BaseResponseData<int>> SignShipment(string tempStoreOutCodes)
        {
            BaseResponseData<int> ret = BaseResponseData<int>.Failed(500, "操作失败");
            if (!string.IsNullOrEmpty(tempStoreOutCodes))
            {
                var storeOutCodes = tempStoreOutCodes.Split(',').ToList();
                var storeouts = new List<InventoryStoreOutOutput>();
                foreach (var storeOutCode in storeOutCodes)
                {
                    var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                    InventoryStoreOutOutput storeout = null;
                    await retryPolicy.ExecuteAsync(async () =>
                    {
                        storeout = await _inventoryApiClient.QueryStoreOutByCode(storeOutCode);

                        if (storeout == null || !storeout.Details.Any())
                        {
                            throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                        }
                        if (string.IsNullOrEmpty(storeout.checker))
                        {
                            throw new Exception("该单据没有复核人");
                        }
                    });
                    if (storeout != null)
                    {
                        storeouts.Add(storeout);
                    }
                }
                ret = await PushToKingdee(storeouts, "出库签收", tempStoreOutCodes);

            }
            return ret;

        }

        /// <summary>
        /// 获取税率编码
        /// </summary>
        /// <param name="taxRate"></param>
        /// <returns></returns>
        private string getTaxrateidNumber(decimal? taxRate)
        {
            if (taxRate > 0)
            {
                return $"V{taxRate}".Trim('0').Trim('.');
            }
            else
            {
                return "V0";
            }
        }

        /// <summary>
        /// 获取税额
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="taxRate"></param>
        /// <returns></returns>
        private decimal? getTaxAmount(decimal? amount, decimal? taxRate)
        {
            return amount / (1 + taxRate / 100) * (taxRate / 100);
        }
    }
}
