using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitTracking
{
    /// <summary>
    /// 提交跟踪器，用于跟踪已成功调用的接口，以便在失败时进行回滚
    /// </summary>
    public class SubmitTracker
    {
        private readonly ILogger _logger;
        private readonly List<SubmitOperation> _successfulOperations = new List<SubmitOperation>();
        private readonly Guid _mergeInputBillId;
        private readonly string _mergeInvoiceNumber;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        public SubmitTracker(ILogger logger, Guid mergeInputBillId, string mergeInvoiceNumber)
        {
            _logger = logger;
            _mergeInputBillId = mergeInputBillId;
            _mergeInvoiceNumber = mergeInvoiceNumber;
        }

        /// <summary>
        /// 添加成功操作
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="data">操作数据</param>
        public void AddSuccessfulOperation(SubmitOperationType operation, BusinessType? businessType, object data)
        {
            _successfulOperations.Add(new SubmitOperation
            {
                OperationType = operation,
                BusinessType = businessType,
                Data = data
            });

            _logger.LogInformation("SubmitTracker - 添加成功操作, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}",
                operation, businessType, _mergeInputBillId);
        }

        /// <summary>
        /// 执行回滚操作
        /// </summary>
        /// <param name="rollbackHandler">回滚处理器</param>
        /// <returns>回滚结果</returns>
        public async Task<bool> RollbackAsync(ISubmitRollbackHandler rollbackHandler)
        {
            _logger.LogWarning("SubmitTracker - 开始执行回滚操作, 成功操作数量: {Count}, MergeInputBillId: {MergeInputBillId}",
                _successfulOperations.Count, _mergeInputBillId);

            // 反向遍历成功操作列表，按照与提交相反的顺序进行回滚
            for (int i = _successfulOperations.Count - 1; i >= 0; i--)
            {
                var operation = _successfulOperations[i];
                try
                {
                    _logger.LogInformation("SubmitTracker - 回滚操作, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}",
                        operation.OperationType, operation.BusinessType, _mergeInputBillId);

                    switch (operation.OperationType)
                    {
                        case SubmitOperationType.KingdeeSubmit:
                            // 回滚金蝶接口调用
                            await rollbackHandler.RollbackKingdeeSubmitAsync(_mergeInputBillId, _mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.DistributionPurchase:
                            // 回滚经销购货入库
                            await rollbackHandler.RollbackDistributionPurchaseAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.ConsignmentToPurchase:
                            // 回滚寄售转购货
                            await rollbackHandler.RollbackConsignmentToPurchaseAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.ServiceFeeProcurement:
                            // 回滚服务费采购
                            await rollbackHandler.RollbackServiceFeeProcurementAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.DistributionTransfer:
                            // 回滚经销调出
                            await rollbackHandler.RollbackDistributionTransferAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.PurchaseRevision:
                            // 回滚购货修订
                            await rollbackHandler.RollbackPurchaseRevisionAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.ExchangeToReturn:
                            // 回滚换货转退货
                            await rollbackHandler.RollbackExchangeToReturnAsync(_mergeInvoiceNumber);
                            break;
                        case SubmitOperationType.LossRecognition:
                            // 回滚损失确认
                            await rollbackHandler.RollbackLossRecognitionAsync(_mergeInvoiceNumber);
                            break;
                        default:
                            _logger.LogWarning("SubmitTracker - 不支持的操作类型: {OperationType}, MergeInputBillId: {MergeInputBillId}",
                                operation.OperationType, _mergeInputBillId);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "SubmitTracker - 回滚操作失败, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        operation.OperationType, operation.BusinessType, _mergeInputBillId, ex.Message);
                    // 继续回滚其他操作
                }
            }

            _logger.LogInformation("SubmitTracker - 回滚操作完成, MergeInputBillId: {MergeInputBillId}", _mergeInputBillId);
            return true;
        }
    }

    /// <summary>
    /// 提交操作类型
    /// </summary>
    public enum SubmitOperationType
    {
        /// <summary>
        /// 金蝶提交
        /// </summary>
        KingdeeSubmit,

        /// <summary>
        /// 经销购货入库
        /// </summary>
        DistributionPurchase,

        /// <summary>
        /// 寄售转购货
        /// </summary>
        ConsignmentToPurchase,

        /// <summary>
        /// 服务费采购
        /// </summary>
        ServiceFeeProcurement,

        /// <summary>
        /// 经销调出
        /// </summary>
        DistributionTransfer,

        /// <summary>
        /// 购货修订
        /// </summary>
        PurchaseRevision,

        /// <summary>
        /// 换货转退货
        /// </summary>
        ExchangeToReturn,

        /// <summary>
        /// 损失确认
        /// </summary>
        LossRecognition
    }

    /// <summary>
    /// 提交操作
    /// </summary>
    public class SubmitOperation
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public SubmitOperationType OperationType { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public BusinessType? BusinessType { get; set; }

        /// <summary>
        /// 操作数据
        /// </summary>
        public object Data { get; set; }
    }

    /// <summary>
    /// 提交回滚处理器接口
    /// </summary>
    public interface ISubmitRollbackHandler
    {
        /// <summary>
        /// 回滚金蝶提交
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackKingdeeSubmitAsync(Guid mergeInputBillId, string mergeInvoiceNumber);

        /// <summary>
        /// 回滚经销购货入库
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackDistributionPurchaseAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚寄售转购货
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackConsignmentToPurchaseAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚服务费采购
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackServiceFeeProcurementAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚经销调出
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackDistributionTransferAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚购货修订
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackPurchaseRevisionAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚换货转退货
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackExchangeToReturnAsync(string mergeInvoiceNumber);

        /// <summary>
        /// 回滚损失确认
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>回滚结果</returns>
        Task<bool> RollbackLossRecognitionAsync(string mergeInvoiceNumber);
    }
}
