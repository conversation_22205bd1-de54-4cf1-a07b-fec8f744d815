﻿using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IReconciliationIncomeQueryService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<ReconciliationOutput>> GetListPages(ReconciliationItemInput input);
        Task<PageResponse<ReconciliationOutput>> GetListByAgent(ReconciliationItemInput input);
        Task<PageResponse<ReconciliationOutput>> GetListByCustomer(ReconciliationItemInput input);


        Task<PageResponse<ReconciliationOutput>> GetListExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfCustomerExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfBillExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfAgentBillExport(ReconciliationItemExportInput input);
    }
}
