﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment
{
    public class GetAdvancePaymentCreditInput
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 业务单元id
        /// </summary>
        public Guid ServiceId { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid CustomerId { get; set; }
        /// <summary>
        /// 回款天数
        /// </summary>
        public int ReturnDays { get; set; }  
        /// <summary>
        /// 月利率
        /// </summary>
        public decimal MonthRate { get; set; }
        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }
        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }
        /// <summary>
        /// 付供应商货款日期
        /// </summary> 
        public DateTime? PaySupplierGoodsDate { get; set; }
        /// <summary>
        /// 单头id
        /// </summary>
        public Guid AdvancePaymentItemId { get; set; }
    }

    /// <summary>
    /// 获取付款计划组件入参
    /// </summary>
    public class AdvancePaymentDebtDetailQueryInput
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public Guid ServiceId { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 项目当前Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillNo { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 回款天数
        /// </summary>
        public int ReturnDays { get; set; }

        /// <summary>
        /// 月利率
        /// </summary>
        public decimal MonthRate { get; set; }

        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }

        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }

        /// <summary>
        /// 付供应商货款日期
        /// </summary> 
        public DateTime? PaySupplierGoodsDate { get; set; }

        /// <summary>
        /// 单头id
        /// </summary>
        public Guid? AdvancePaymentItemId { get; set; }

        /// <summary>
        /// 取数开始时间
        /// </summary> 
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 取数结束时间
        /// </summary> 
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary> 
        public string? CreateBy { get; set; }
    }

    /// <summary>
    /// 保存入参
    /// </summary>
    public class SaveAdvancePaymentInput
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 提前付款名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 单号
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 核算部门Id路径 
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 核算部门简码
        /// </summary>
        public string? BusinessDeptShortName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary> 
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 取数开始时间
        /// </summary> 
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 取数结束时间
        /// </summary> 
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 回款天数
        /// </summary>
        public int Day { get; set; }

        /// <summary>
        /// 月利率
        /// </summary> 
        public decimal MonthRate { get; set; }
        /// <summary>
        /// 确认支付日期方式
        /// </summary>
        public ConfirmPaymentDateModeEnum? ConfirmPaymentDateMode { get; set; }

        /// <summary>
        /// 付供应商货款天数
        /// </summary>
        public int? PaySupplierGoodsDay { get; set; }

        /// <summary>
        /// 付供应商货款日期
        /// </summary>
        public DateTime? PaySupplierGoodsDate { get; set; }

        /// <summary>
        /// 垫资应收金额合计 
        /// </summary> 
        public decimal? AdvanceCreditAmount { get; set; }

        /// <summary>
        /// 含税垫资毛利合计
        /// </summary> 
        public decimal? AdvanceCreditTaxAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public AdvancePaymentStatusEnum Status { get; set; }

        /// <summary>
        /// 流程请求Id
        /// </summary> 
        public string? OARequestId { get; set; }

        /// <summary>
        /// 付款计划明细
        /// </summary>
        public List<AdvancePaymentDebtDetailOutput>? advancePaymentDebtDetails { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }

        /// <summary>
        /// 附件ids
        /// </summary>
        public string? AttachFileIds { get; set; }
    }

    /// <summary>
    /// 重新计算含税垫资毛利入参
    /// </summary>
    public class ReComputeInput
    {
        /// <summary>
        /// 垫资单id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 垫资金额 
        /// </summary> 
        public decimal? AdvanceAmount { get; set; }

        /// <summary>
        /// 收付比 
        /// </summary> 
        public decimal? Rate { get; set; }

        /// <summary>
        /// 供应链金融折扣
        /// </summary> 
        public decimal? FinanceDiscount { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string? CreateBy { get; set; }
    }
}
