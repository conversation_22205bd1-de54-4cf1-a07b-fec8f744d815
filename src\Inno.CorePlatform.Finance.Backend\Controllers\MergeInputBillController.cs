﻿﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 合并进项发票控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class MergeInputBillController : ControllerBase
    {
        private readonly IMergeInputBillAppService _mergeInputBillAppService;
        private readonly ILogger<MergeInputBillController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="mergeInputBillAppService">合并进项发票应用服务</param>
        /// <param name="logger">日志记录器</param>
        public MergeInputBillController(IMergeInputBillAppService mergeInputBillAppService, ILogger<MergeInputBillController> logger)
        {
            _mergeInputBillAppService = mergeInputBillAppService;
            _logger = logger;
        }

        /// <summary>
        /// 创建合并进项发票
        /// </summary>
        /// <param name="request">创建合并进项发票请求</param>
        /// <returns>创建合并进项发票响应</returns>
        [HttpPost("create")]
        public async Task<BaseResponseData<CreateMergeInputBillResponse>> CreateMergeInputBill([FromBody] CreateMergeInputBillRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.CreateMergeInputBill(request);
                return new BaseResponseData<CreateMergeInputBillResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response,
                    Message = $"创建合并进项发票成功，单号：{response.MergeInvoiceNumber}"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<CreateMergeInputBillResponse>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 查询合并进项发票列表
        /// </summary>
        /// <param name="request">查询合并进项发票列表请求</param>
        /// <returns>查询合并进项发票列表响应</returns>
        [HttpPost("list")]
        public async Task<BaseResponseData<QueryMergeInputBillListResponse>> QueryMergeInputBillList([FromBody] QueryMergeInputBillListRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.QueryMergeInputBillList(request);
                return new BaseResponseData<QueryMergeInputBillListResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<QueryMergeInputBillListResponse>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }


        /// <summary>
        /// 还原合并进项发票
        /// </summary>
        /// <param name="request">还原合并进项发票请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("restore")]
        public async Task<BaseResponseData<bool>> RestoreMergeInputBill([FromBody] RestoreMergeInputBillRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.RestoreMergeInputBill(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "还原合并进项发票成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 保存勾稽结果
        /// </summary>
        /// <param name="request">保存勾稽结果请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("saveMatch")]
        public async Task<BaseResponseData<bool>> SaveMatch([FromBody] SaveMatchRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.SaveMatch(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "保存勾稽结果成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 提交勾稽结果
        /// </summary>
        /// <param name="request">提交勾稽结果请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("submitMatch")]
        public async Task<BaseResponseData<bool>> SubmitMatch([FromBody] SubmitMatchRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.SubmitMatch(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "提交勾稽结果成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取合并进项发票明细
        /// </summary>
        /// <param name="request">获取合并进项发票明细请求</param>
        /// <returns>合并进项发票明细分页数据（带合计）</returns>
        [HttpPost("getMergeInputBillDetail")]
        public async Task<BaseResponseData<PagedDataWithSummary<MergeInputBillDetailItem>>> GetMergeInputBillDetail([FromBody] GetMatchedDetailsRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.GetMergeInputBillDetail(request);
                return new BaseResponseData<PagedDataWithSummary<MergeInputBillDetailItem>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<PagedDataWithSummary<MergeInputBillDetailItem>>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 删除匹配明细
        /// </summary>
        /// <param name="request">删除匹配明细请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("deleteMatch")]
        public async Task<BaseResponseData<bool>> DeleteMatch([FromBody] DeleteMatchRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.DeleteMatch(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "删除匹配明细成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取缓存中的明细数据
        /// </summary>
        /// <param name="request">获取缓存中的明细数据请求</param>
        /// <returns>缓存中的明细数据</returns>
        [HttpPost("getCacheDetails")]
        public async Task<BaseResponseData<GetCacheDetailsResponse>> GetCacheDetails([FromBody] GetCacheDetailsRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.GetCacheDetails(request);
                return new BaseResponseData<GetCacheDetailsResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<GetCacheDetailsResponse>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取待提交的明细
        /// </summary>
        /// <param name="request">获取待提交的明细请求</param>
        /// <returns>待提交的明细分页数据（带合计）</returns>
        [HttpPost("getSubmitDetails")]
        public async Task<BaseResponseData<PagedDataWithSummary<SubmitDetailItem>>> GetSubmitDetails([FromBody] GetSubmitDetailsRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.GetSubmitDetails(request);
                return new BaseResponseData<PagedDataWithSummary<SubmitDetailItem>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<PagedDataWithSummary<SubmitDetailItem>>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 开始异步匹配
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("startAsyncMatch")]
        public async Task<BaseResponseData<bool>> StartAsyncMatch([FromBody] StartAsyncMatchRequest request)
        {
            try
            {
                _logger.LogInformation("开始异步匹配, MergeInputBillId: {MergeInputBillId}, IsReload: {IsReload}",
                    request.MergeInputBillId, request.IsReload);
                return await _mergeInputBillAppService.StartAsyncMatch(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始异步匹配失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    request.MergeInputBillId, ex.Message);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message,
                    Data = false
                };
            }
        }



        /// <summary>
        /// 获取发票金额统计
        /// </summary>
        /// <param name="request">获取发票金额统计请求</param>
        /// <returns>发票金额统计响应</returns>
        [HttpPost("getInvoiceAmountStatistics")]
        public async Task<BaseResponseData<GetInvoiceAmountStatisticsResponse>> GetInvoiceAmountStatistics([FromBody] GetInvoiceAmountStatisticsRequest request)
        {
            try
            {
                var response = await _mergeInputBillAppService.GetInvoiceAmountStatistics(request);
                return new BaseResponseData<GetInvoiceAmountStatisticsResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<GetInvoiceAmountStatisticsResponse>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 撤销勾稽结果
        /// </summary>
        /// <param name="request">撤销勾稽结果请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("revokeMatch")]
        public async Task<BaseResponseData<bool>> RevokeMatch([FromBody] RevokeMatchRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.RevokeMatch(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "撤销勾稽结果成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 取消已提交勾稽
        /// </summary>
        /// <param name="request">取消已提交勾稽请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("cancelSubmittedMatch")]
        public async Task<BaseResponseData<bool>> CancelSubmittedMatch([FromBody] CancelSubmittedMatchRequest request)
        {
            try
            {
                var result = await _mergeInputBillAppService.CancelSubmittedMatch(request);
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Data = result,
                    Message = "取消已提交勾稽成功"
                };
            }
            catch (Exception ex)
            {
                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取匹配查询条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>匹配查询条件</returns>
        [HttpGet("getMatchQueryCondition/{mergeInputBillId}")]
        public async Task<BaseResponseData<GetMatchableDocumentsRequest>> GetMatchQueryCondition(Guid mergeInputBillId)
        {
            try
            {
                _logger.LogInformation("获取匹配查询条件, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                var queryCondition = await _mergeInputBillAppService.GetMatchQueryCondition(mergeInputBillId);
                return new BaseResponseData<GetMatchableDocumentsRequest>
                {
                    Code = CodeStatusEnum.Success,
                    Data = queryCondition,
                    Message = "获取匹配查询条件成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取匹配查询条件失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    mergeInputBillId, ex.Message);
                return new BaseResponseData<GetMatchableDocumentsRequest>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取缓存中的匹配条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>缓存中的匹配条件</returns>
        [HttpGet("getCachedMatchCondition/{mergeInputBillId}")]
        public async Task<BaseResponseData<GetCachedMatchConditionResponse>> GetCachedMatchCondition(Guid mergeInputBillId)
        {
            try
            {
                _logger.LogInformation("获取缓存中的匹配条件, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);
                var request = new GetCachedMatchConditionRequest { MergeInputBillId = mergeInputBillId };
                var response = await _mergeInputBillAppService.GetCachedMatchCondition(request);
                return new BaseResponseData<GetCachedMatchConditionResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = response,
                    Message = "获取缓存中的匹配条件成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取缓存中的匹配条件失败, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    mergeInputBillId, ex.Message);
                return new BaseResponseData<GetCachedMatchConditionResponse>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = ex.Message
                };
            }
        }
    }
}
