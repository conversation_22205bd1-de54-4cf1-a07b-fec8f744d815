﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class AdvancePaymentInput : BaseQuery
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string? Name { get; set; } 
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public string? BillDateS { get; set; }

        public DateTime DateS
        {
            get
            {
                if (!string.IsNullOrEmpty(BillDateS))
                {
                    return DateTimeHelper.LongToDateTime(Convert.ToInt64(BillDateS));
                }
                return DateTime.Now;
            }
        }

        /// <summary>
        /// 结束日期
        /// </summary>
        public string? BillDateE { get; set; }

        public DateTime DateE
        {
            get
            {
                if (!string.IsNullOrEmpty(BillDateS))
                {
                    return DateTimeHelper.LongToDateTime(Convert.ToInt64(BillDateE));
                }
                return DateTime.Now;
            }
        }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }
         
        /// <summary>
        /// 状态
        /// </summary>
        public AdvancePaymentStatusEnum? Status { get; set; } 
    }
    /// <summary>
    /// 查询明细入参
    /// </summary>
    public class AdvancePaymentDetailInput
    {
        /// <summary>
        /// 单据Id
        /// </summary>
        public Guid AdvancePaymentItemId { get; set; }
    }

    /// <summary>
    /// 上传附件入参
    /// </summary>
    public class AdvancePaymentItemAttachFileInput
    {
        public Guid AdvancePaymentItemId { get; set; }

        public string? AttachFileIds { get; set; }
        public string? AttachFileId { get; set; }

    }

    /// <summary>
    /// 生成购货修订订单入参
    /// </summary>
    public class PurchaseDetailsInput
    {
        /// <summary>
        /// 寄售转购货单头ID
        /// </summary>
        public Guid? PurchaseOrderId { get; set; }

        /// <summary>
        /// 寄售转购货单号
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 寄售转购货明细ID
        /// </summary>
        public Guid? PurchaseOrderDetailId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 寄售转购货修改前单价
        /// </summary>
        public decimal? BeforeCost { get; set; }

        /// <summary>
        /// 寄售转购货修改后单价
        /// </summary>
        public decimal? AfterCost { get; set; }

        /// <summary>
        /// 提前垫资付款单号
        /// </summary>
        public string? AdvancePaymentCode { get; set; }
    }

    /// <summary>
    /// 查看附件
    /// </summary>
    public class AdvancePaymentItemAttachFileQueryInput
    {
        public Guid AdvancePaymentItemId { get; set; }

        public string? FileIds { get; set; }
        public string? FileId { get; set; }
    }
}
