﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Projects
{
    public class GetContractDelayInfoListInput
    {
        public Guid? CompanyId { get; set; }
        public List<ContractDelayList> ContractDelayList { get; set; } = new List<ContractDelayList>();
    }

    public class ContractDelayList
    {
        public List<Guid?> ProjectIds { get; set; }
        public Guid? AgentId { get; set; }
        public Guid CustomerId { get; set; }
    }
}
