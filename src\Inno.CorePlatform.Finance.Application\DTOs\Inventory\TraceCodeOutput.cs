﻿using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    /// <summary>
    /// 追溯码信息
    /// </summary>
    public class TraceCodeOutput
    {
        /// <summary>
        /// 追溯码
        /// </summary>
        public string TraceCode { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public Guid ProductId { get; set; }
        /// <summary>
        /// 采购单Id
        /// </summary>
        public Guid? PurchaseId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string PurchaseOrderCode { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string StoreInCode { get; set; }

        /// <summary>
        /// 入库类型(1-经销购货入库)
        /// </summary>
        public int? StoreInType { get; set; }
        /// <summary>
        /// 采购单明细ID
        /// </summary>
        public Guid? PurchaseOrderDetailId { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 含税成本
        /// </summary>
        public decimal unitCost { get; set; }
    }

    /// <summary>
    /// 应付明细拆分的DTO类
    /// </summary>
    public class DebtDetailUpdateDto
    {
        /// <summary>
        /// 应付明细Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public AccountPeriodTypeEnum AccountPeriodType { get; set; }

        public decimal? Discount { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }

        public Debt? Debt { get; set; }
        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }


        public Credit? Credit { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }
        /// <summary>
        /// 认款单号
        /// </summary> 
        public string? RecognizeReceiveCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }
        /// <summary>
        /// 1-需要更新的，2-需要插入的
        /// </summary>

        public int Type { get; set; }

        /// <summary>
        /// 预计付款日期
        /// </summary>
        public DateTime? ProbablyPayTime { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string PurchaseCode { get; set; }
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedTime { get; set; } = DateTime.Now;
        public string UpdatedBy { get; set; } = "";
        public DateTime UpdatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNo { get; set; }

        /// <summary>
        /// 账期天数
        /// </summary>
        public decimal? AccountPeriodDays { get; set; }

        public string DetailId { get; set; }
    }
}
