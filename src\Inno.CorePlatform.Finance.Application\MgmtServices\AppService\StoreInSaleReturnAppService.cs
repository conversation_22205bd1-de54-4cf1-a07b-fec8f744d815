﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Credits;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.SaleReturn;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.EntityFrameworkCore;
using Npoi.Mapper;
using Polly;
using System.Text;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 销售调回入库
    /// </summary>
    public class StoreInSaleReturnAppService : BaseAppService, IStoreInSaleReturnAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IStoreInApplyApiClient _storeInApplyApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        private readonly ISellApiClient _sellApiClient;
        private readonly IICApiClient _icapiClient;
        private readonly DaprClient _daprClient;
        private readonly FinanceDbContext _db;
        private readonly ICreditAppService _creditAppService;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        public StoreInSaleReturnAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IStoreInApplyApiClient storeInApplyApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            IAppServiceContextAccessor? contextAccessor,
            ISellApiClient sellApiClient,
            DaprClient daprClient,
            FinanceDbContext db,
            ICreditAppService creditAppService,
             ICustomizeInvoiceQueryService customizeInvoiceQueryService,
             IICApiClient icapiClient,
            Func<int, TimeSpan> sleepDurationProvider = null
           ) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._sellApiClient = sellApiClient;
            this._bDSApiClient = bDSApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._storeInApplyApiClient = storeInApplyApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
            this._daprClient = daprClient;
            this._db = db;
            this._creditAppService = creditAppService;
            this._customizeInvoiceQueryService = customizeInvoiceQueryService;
            this._icapiClient = icapiClient;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            StringBuilder stringBuilder = new StringBuilder();
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreInOutput storein = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storein = await _inventoryApiClient.QueryStoreInByCode(input.BusinessCode);
                    if (storein == null)
                    {
                        throw new Exception("未查询到入库单");
                    }
                    if (storein == null || !storein.storeInDetails.Any())
                    {
                        throw new Exception("订阅销售调回入库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                });
                var check = await base.IsCreatedCreditForBill(input.BusinessCode);
                if (check)
                {
                    if (input.IsAutoBill.HasValue && input.IsAutoBill.Value)
                    {
                        return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                    }
                    else
                    {
                        throw new Exception("该单据已生成过应收");
                    }
                }
                if (storein != null && storein.relateCodeType.HasValue && storein.relateCodeType == 16)
                {
                    stringBuilder.AppendLine($"1.[旺店通-退货应收]开始调用集成中心接口{storein.relateCode}、{storein.storeInCode}、{storein.storeInApplyCode},{DateTime.Now}");
                    //根据入口单号调用销售接口生成个人和平台的负数应收,调用集成中心接口获取对应销售的信息
                    var refundStoreInDetail = await _icapiClient.GetRefundStoreInDetail(storein.relateCode);
                    stringBuilder.AppendLine($"2.[旺店通-退货应收]开始处理消费者金额:{DateTime.Now}");
                    var consumerPriceRefundStoreIn = SaleReturnCreditInput.FromRefundStoreWithConsumerPrice(storein, refundStoreInDetail, input);
                    var ret1 = await CreateCreditForSellReturn(consumerPriceRefundStoreIn);

                    stringBuilder.AppendLine($"3.[旺店通-退货应收]生成消费者应收成功:{DateTime.Now}");
                    var platformCouponPriceRefundStoreIn = SaleReturnCreditInput.FromRefundStoreWithPlatformCouponPrice(storein, refundStoreInDetail, input);
                    if (platformCouponPriceRefundStoreIn.IsNeedCreateReceivable)
                    {
                        await Task.Delay(9000);
                        var ret2 = await CreateCreditForSellReturn(platformCouponPriceRefundStoreIn, ret1.Data);
                        stringBuilder.AppendLine($"4.[旺店通-退货应收]生成平台应收成功,{DateTime.Now}");
                    }
                    else
                    {
                        stringBuilder.AppendLine($"4.[旺店通-退货应收]红包金额为0，无需生成应收,{DateTime.Now}");
                    }
                    var creditSaleInvoiceInput = CreditSaleInvoiceToICInputDto.FromRefundStoreIn(consumerPriceRefundStoreIn, refundStoreInDetail);
                    await _daprClient.PublishEventAsync(
                           "pubsub-default",
                           "fam-ic-createdinvoicecredit",
                            creditSaleInvoiceInput);
                    stringBuilder.AppendLine($"5.[旺店通-退货应收]发布应收事件成功,{creditSaleInvoiceInput.ToJson()},{DateTime.Now}");

                    await _db.SaveChangesAsync();
                    await _subLogService.LogAsync("StoreInSaleReturnAppService/PullIn", stringBuilder.ToString(), "推送集成平台-旺店通-退货应收-成功");
                    return ret1;
                }
                else
                {
                    var saleReturnCreditInput = SaleReturnCreditInput.FromStoreIn(storein, input);
                    var ret = await CreateCreditForSellReturn(saleReturnCreditInput);
                    return ret;
                }
            }
            catch (Exception ex)
            {
                stringBuilder.AppendLine(ex.Message);
                stringBuilder.AppendLine(input?.ToJson());
                await _subLogService.LogAsync("StoreInSaleReturnAppService/PullIn", stringBuilder.ToString(), "推送集成平台-退货应收-失败", Enums.LogLevelEnum.Error);
                throw;// new Exception("订阅销售调回入库事件出错，可能是上游单据接口异常，或者生成应收代码出错");
            }
        }
        /// <summary>
        /// 销售调回应收
        /// </summary>
        /// <param name="input">销售调回应收输入参数</param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateCreditForSellReturn(SaleReturnCreditInput input, int index = 1)
        {
            var ret = BaseResponseData<int>.Success("操作成功");

            List<CreditSaleDataDTOs> creditSaleList = new List<CreditSaleDataDTOs>();
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.CompanyId.ToString() }
            })).FirstOrDefault();

            if (companyInfo != null)
            {
                //var storeinApply = await _storeInApplyApiClient.GetById(input.ApplyId.Value);
                var serviceIds = input.StoreInDetails.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
                var services = new List<ServiceMetaOutput>();
                if (serviceIds.Any())
                {
                    services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                    {
                        ids = serviceIds.Select(p => p.ToString()).ToList()
                    });
                }
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                var productNameIds = input.StoreInDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                var projectIds = input.StoreInDetails.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var shipmentCode = string.Empty;
                //拿到销售调回所有的订单号
                var allSaleCodes = input.StoreInDetails.Select(z => z.saleCode).Distinct().ToList();
                var allSaleDatas = await _sellApiClient.GetSaleList(new GetSaleListInput() { BillCodes = allSaleCodes, PageNum = 1, PageSize = int.MaxValue });
                foreach (var item in allSaleCodes)
                {
                    if (!string.IsNullOrEmpty(item) && item.ToUpper().Contains("-SA-"))
                    {
                        shipmentCode = input.SpdInvoiceCode;
                        creditSaleList.Add(GetSaleData(item, allSaleDatas));
                    }
                }
                if (!string.IsNullOrEmpty(input.tempInventoryCode) && input.tempInventoryCode.Contains("SA"))
                {
                    shipmentCode = input.SpdInvoiceCode;
                    creditSaleList.Add(await GetSaleData(input.tempInventoryCode));
                }
                var groupDetail = input.StoreInDetails.GroupBy(p => new { p.mark, p.businessUnitId, p.projectId, p.saleCode });
                if (input.StoreInDetails.Where(p => !p.agentId.HasValue || !p.price.HasValue).Any())
                {
                    throw new Exception("上游数据有误，该数据存在供应商Id为空或价格为空的数据");
                }

                var kingdeeCredits = new List<KingdeeCredit>();
                var Ids = new List<Guid>();
                List<CreditDto> credits = new List<CreditDto>();
                foreach (var g in groupDetail)
                {
                    var thisProjectInfo = projectInfo.FirstOrDefault(p => p.Id == g.Key.projectId);
                    var creditSale = creditSaleList.Where(p => p.saleCode == g.Key.saleCode).FirstOrDefault();
                    var credit = new CreditDto
                    {
                        PurchaseCost = input.CreditSaleSubType.HasValue && input.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform ? 0 : -(g.Sum(p => p.quantity * (p.unitCost ?? 0))),
                        CompanyId = Guid.Parse(companyInfo.companyId),
                        CompanyName = companyInfo.companyName,
                        NameCode = companyInfo.nameCode,
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        Value = -(g.ToList().Sum(p => p.quantity * (p.price ?? 0))),
                        BillCode = $"{input.StoreInCode}-{index.ToString().PadLeft(3, '0')}",
                        BillDate = (input.UseBillDate ? DateTimeHelper.LongToDateTime(input.StoreInDate) : billDate),
                        CreatedBy = input.StoreInBy ?? "none",
                        CreatedTime = DateTime.Now,
                        Mark = g.Key.mark,
                        CreditType = (int)g.Key.mark == 0 || g.Key.mark == 3 ? CreditTypeEnum.selfreturn.ToString() : CreditTypeEnum._return.ToString(),
                        CustomerId = input.ShipperId,
                        CustomerName = input.ShipperName,
                        Id = Guid.NewGuid(),
                        InvoiceStatus = (int)InvoiceStatusEnum.noninvoice,
                        ServiceId = g.Key.businessUnitId,
                        RelateCode = input.StoreInCode,
                        IsSureIncome = 1,
                        BusinessDeptFullName = input.BusinessDeptFullName,
                        BusinessDeptFullPath = input.BusinessDeptFullPath,
                        BusinessDeptId = input.BusinessDeptId.ToString(),
                        OrderNo = g.Key.saleCode,
                        SaleSystemId = creditSale == null ? Guid.Empty : creditSale.saleSystemId,
                        ShipmentCode = shipmentCode,
                        SaleSystemName = creditSale == null ? "" : creditSale.saleSystemName,
                        HospitalId = creditSale == null ? "" : creditSale.hospitalId,
                        HospitalName = creditSale == null ? "" : creditSale.hospitalName,
                        SaleSource = creditSale == null ? null : creditSale.source,
                        SaleType = creditSale == null ? null : creditSale.creditSaleType,
                        SaleCode = g.Key.saleCode,
                        Note = input.Remark,
                        DeptName = creditSale == null ? "" : creditSale.deptName,
                        ProjectName = thisProjectInfo?.Name,
                        ProjectId = g.Key.projectId,
                        ProjectCode = thisProjectInfo?.Code,
                        OriginOrderNo = creditSale == null ? "" : creditSale.orginOrderNo,
                        CustomerOrderCode = creditSale == null ? "" : creditSale.customerOrderCode,
                        CustomerPersonName = creditSale == null ? "" : creditSale.customerPersonName,
                        SunPurchaseRelatecode = creditSale == null ? "" : creditSale.sunPurchaseRelatecode,
                        AgentName = string.Join(",", g.Select(p => p.agentName).Distinct().ToList()),
                        ProducerName = string.Join(",", g.Select(p => p.producerName).Distinct().ToList()),
                        CreditSaleSubType = input.CreditSaleSubType,
                        //IsInternalTransactions = Utility.IsInternalTransactions(input.RelateCodeType),

                    };
                    //SPD销售调回(经仓)=816,SPD红冲消耗销售调回=817
                    if (input.RelateCodeType == 816 || input.RelateCodeType == 817)
                    {
                        credit.RedReversalConsumNo = input.RelateCode;
                    }
                    credit.IsSureIncomeDate = credit.BillDate;
                    Ids.Add(credit.Id);
                    if (g.Key.businessUnitId.HasValue)
                    {
                        credit.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.ToString().ToLower())?.name;
                    }
                    var amount = 0m;//不含税总额
                    var jfzx_alltotalcost = 0m;
                    #region 包装金蝶应收参数
                    var kingdeeCredit = new KingdeeCredit()
                    {
                        asstact_number1 = credit.CustomerId.Value,
                        billno = credit.BillCode,
                        billtype_number = KingdeeHelper.TransferCreditType(credit.CreditType),
                        bizdate = credit.BillDate.Value,
                        org_number = credit.NameCode,
                        jfzx_businessnumber = input.BusinessDeptId,
                        jfzx_ordernumber = input.StoreInCode,
                        jfzx_iscofirm = true,
                        jfzx_creator = credit.CreatedBy ?? "none",
                        jfzx_serviceid = credit.ServiceName,
                    };

                    kingdeeCredit.jfzx_rebate = creditSale == null ? null : creditSale.rebateType.HasValue;
                    var kingdeeCreditDetails = new List<KingdeeCreditDetail>();
                    g.ToList().GroupBy(a => new { a.productId, a.price, a.projectId, a.salesTaxRate, a.agentId, a.unitCost, a.taxRate, a.standardUnitCost }).ForEach(b =>
                    {
                        if (!b.Key.salesTaxRate.HasValue)
                        {
                            throw new Exception("生成应收到金蝶系统失败，原因：salesTaxRate 销售税率为空");
                        }
                        var d = new KingdeeCreditDetail();
                        d.e_taxunitprice = b.Key.price.Value;
                        d.e_unitprice = d.e_taxunitprice / (1 + b.Key.salesTaxRate.Value / 100.00M);
                        d.e_quantity = b.Sum(c => c.quantity) * -1;
                        d.salestaxrate = b.Key.salesTaxRate.Value;
                        var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == b.First().productNameId);
                        if (thisProductInfo.classificationNewGuid.HasValue)
                        {
                            d.e_material_number1 = thisProductInfo.classificationNewGuid.ToString();
                        }
                        else
                        {
                            d.e_material_number1 = thisProductInfo.classificationGuid.ToString();
                        }
                        d.jfzx_outbound_type = creditSale == null ? "" : creditSale.saleType;
                        var thisProject = projectInfo.FirstOrDefault(t => t.Id == b.Key.projectId);
                        d.jfzx_projectnumber = thisProject?.Code;

                        // 成本相关字段统一处理：如果应收子类型为平台，成本相关字段设置为 0
                        var isPlatformType = input.CreditSaleSubType.HasValue && input.CreditSaleSubType.Value == CreditSaleSubTypeEnum.platform;
                        var unitCostExcludingTax = isPlatformType ? 0 : Math.Round(b.Key.unitCost.Value / (1 + b.Key.taxRate.Value / 100.00M), 2);

                        d.jfzx_unitcost = unitCostExcludingTax;
                        d.jfzx_supplier = b.Key.agentId.ToString().ToUpper();
                        if (g.Key.mark != 0 && g.Key.mark != 3)//寄售货需要将标准成本传入金蝶
                        {
                            d.jfzx_standardtotal = b.Key.standardUnitCost.Value;
                        }
                        kingdeeCreditDetails.Add(d);
                        if (creditSale != null && creditSale.rebateType.HasValue)
                        {
                            d.jfzx_rebateType = (int)creditSale.rebateType;
                        }
                        ////////////////////金蝶应收应付单传值增加总额字段////////////////////////////
                        //明细总成本：如果是平台类型，总成本也为 0
                        d.jfzx_totalcostMany = isPlatformType ? 0 : (b.Key.unitCost / (1 + b.Key.taxRate / 100.00M)) * d.e_quantity;
                        d.jfzx_totalcostMany = Math.Round(d.jfzx_totalcostMany ?? 0, 2);
                        amount += d.e_unitprice * d.e_quantity;
                        jfzx_alltotalcost += d.jfzx_totalcostMany.HasValue ? d.jfzx_totalcostMany.Value : 0;
                    });

                    //应收不含税总额
                    kingdeeCredit.recamount = Math.Round(credit.Value, 2);
                    //应收不含税总额
                    kingdeeCredit.amount = kingdeeCredit.recamount > 0 ? Math.Abs(Math.Round(amount, 2)) : -Math.Abs(Math.Round(amount, 2));
                    //总成本
                    kingdeeCredit.jfzx_alltotalcost = Math.Round(jfzx_alltotalcost, 2);

                    kingdeeCredit.billEntryModels = kingdeeCreditDetails;
                    kingdeeCredits.Add(kingdeeCredit);
                    #endregion
                    credits.Add(credit);
                    await base.CreateCredit(credit);
                    index++;
                }

                var kingdeeRes = await _kingdeeApiClient.PushCreditsToKingdee(kingdeeCredits, input.Classify, input.RequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    var orderNos = credits.Select(p => p.OrderNo).Distinct().ToList();
                    await ConfirmReceipt(orderNos);
                    await _unitOfWork.CommitAsync();
                    await _customizeInvoiceQueryService.GetOriginDetailAsync(new OriginDetailQueryInput
                    {
                        CreditBillCodes = kingdeeCredits.Select(p => p.billno).ToList(),
                        RelateCodes = kingdeeCredits.Select(p => p.jfzx_ordernumber).ToList(),
                    });
                }
                else
                {
                    throw new Exception("生成应收到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            ret.Data = index;
            return ret;
        }
        private async Task<CreditSaleDataDTOs> GetSaleData(string code)
        {
            var saleData = new CreditSaleDataDTOs();
            var saleOut = await _sellApiClient.GetTempSaleByCodeAsync(code);
            if (saleOut != null)
            {
                saleData.saleSystemName = saleOut.SaleSystemName;
                saleData.saleSystemId = saleOut.SaleSystemId;
                saleData.hospitalId = saleOut.HospitalId;
                saleData.hospitalName = saleOut.HospitalName;
                saleData.rebateType = saleOut.RebateType;
                saleData.source = saleOut.Source;
                saleData.creditSaleType = saleOut.SaleType;
                if (saleOut.SaleType == SaleTypeEnum.Temp)
                {
                    saleData.saleType = "B";
                }
                if (saleOut.SaleType == SaleTypeEnum.SaleOut)
                {
                    saleData.saleType = "A";
                }
                saleData.deptName = saleOut.DeptName;
                if (saleOut.TempInventoryDetails != null && saleOut.TempInventoryDetails.Any())
                {
                    saleData.deptName = string.Join(",", saleOut.TempInventoryDetails.Select(p => p.deptName).Distinct());
                }
                //if (!string.IsNullOrEmpty(saleOut.RelateCode) && (saleOut.RelateCode.Contains("-PSA-") || saleOut.SaleType == SaleTypeEnum.SaleForB)) //原始订单号
                {
                    saleData.orginOrderNo = saleOut.RelateCode;
                }

                saleData.customerOrderCode = saleOut.CustomerOrderCode;
                saleData.customerPersonName = saleOut.CustomerPersonName;
                saleData.sunPurchaseRelatecode = saleOut.SunPurchaseRelatecode;
                saleData.saleCode = code;
            }
            return saleData;
        }
        private CreditSaleDataDTOs GetSaleData(string code, List<SaleOutput> saleOuts)
        {
            var saleData = new CreditSaleDataDTOs();
            var saleOut = saleOuts.Where(p => p.BillCode == code).FirstOrDefault();
            if (saleOut != null)
            {
                saleData.saleSystemName = saleOut.SaleSystemName;
                saleData.saleSystemId = saleOut.SaleSystemId;
                saleData.hospitalId = saleOut.HospitalId;
                saleData.hospitalName = saleOut.HospitalName;
                saleData.rebateType = saleOut.RebateType;
                saleData.source = saleOut.Source;
                saleData.creditSaleType = saleOut.SaleType;
                if (saleOut.SaleType == SaleTypeEnum.Temp)
                {
                    saleData.saleType = "B";
                }
                if (saleOut.SaleType == SaleTypeEnum.SaleOut)
                {
                    saleData.saleType = "A";
                }
                saleData.deptName = saleOut.DeptName;
                if (saleOut.TempInventoryDetails != null && saleOut.TempInventoryDetails.Any())
                {
                    saleData.deptName = string.Join(",", saleOut.TempInventoryDetails.Select(p => p.deptName).Distinct());
                }
                //if (!string.IsNullOrEmpty(saleOut.RelateCode) && (saleOut.RelateCode.Contains("-PSA-") || saleOut.SaleType == SaleTypeEnum.SaleForB)) //原始订单号
                {
                    saleData.orginOrderNo = saleOut.RelateCode;
                }

                saleData.customerOrderCode = saleOut.CustomerOrderCode;
                saleData.customerPersonName = saleOut.CustomerPersonName;
                saleData.sunPurchaseRelatecode = saleOut.SunPurchaseRelatecode;
                saleData.saleCode = code;
            }
            return saleData;
        }
        private async Task<BaseResponseData<int>> ConfirmReceipt(List<string?> orderNos)
        {
            try
            {
                var ret = BaseResponseData<int>.Success("操作成功");
                var credits = await _db.Credits.Where(p => (!p.IsSureIncome.HasValue || p.IsSureIncome.Value != 1) && orderNos.Contains(p.OrderNo)).ToListAsync();
                if (credits != null && credits.Any())
                {
                    DateTime now = DateTime.Now;
                    DateTime newDateTime = new DateTime(now.Year, now.Month, now.Day, now.Hour + 8, now.Minute, now.Second);
                    var data = new List<IncomeIsConfirmData>();
                    foreach (var credit in credits)
                    {
                        data.Add(new IncomeIsConfirmData()
                        {
                            date = newDateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            billno = credit.BillCode,
                        });
                    }
                    var incomeIsConfirm = new KingdeeIncomeIsConfirm() { data = data, jfzx_iscofirm = true };
                    var king = await _kingdeeApiClient.PushIncomeIsCofirm(incomeIsConfirm);
                    if (king != null && king.Code == CodeStatusEnum.Success)
                    {
                        foreach (var credit in credits)
                        {
                            credit.IsSureIncome = 1;
                            credit.IsSureIncomeDate = newDateTime;
                            credit.UpdatedBy = "none";
                            credit.UpdatedTime = newDateTime;
                        }
                    }
                    else
                    {
                        ret = BaseResponseData<int>.Failed(500, king == null || string.IsNullOrEmpty(king.Message) ? "" : king.Message);
                    }
                }
                return ret;
            }
            catch (Exception)
            {

                throw;
            }
        }
    }

}
