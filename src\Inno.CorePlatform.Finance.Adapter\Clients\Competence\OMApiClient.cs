﻿using Dapr.Client;
using Google.Rpc;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.OM;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MongoDB.Bson.IO;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    internal class OMApiClient : BaseDaprApiClient<OMApiClient>, IOMApiClient
    {
        public OMApiClient(DaprClient daprClient, ILogger<OMApiClient> logger, IHttpContextAccessor httpContextAccessor) :
            base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<OMOutput> FinanceCheckPayQuota(FinanceCheckPayQuotaInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<FinanceCheckPayQuotaInput, OMOutput>(input, AppCenter.OM_FinanceCheckPayQuota, RequestMethodEnum.POST);
        }

        public async Task<OMOutput> CheckCreditPayQuota(FinanceCheckPayQuotaInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<FinanceCheckPayQuotaInput, OMOutput>(input, AppCenter.OM_CheckCreditPayQuota, RequestMethodEnum.POST);
        }
        protected override string GetAppId()
        {
            return AppCenter.OM_APPID;
        }
    }

  
}
