﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService;
using Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.DebtDetailAggregate;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Inno.CorePlatform.Gateway.Models.File;
using Inno.CorePlatform.ServiceClient;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MongoDB.Driver.Linq;
using NetTopologySuite.Index.HPRtree;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.SS.Formula.Functions;
using System.Linq;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class AdvancePaymentQueryService : ApplicationServices.BaseAppService<AdvancePaymentQueryService>, IAdvancePaymentQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        private readonly IWeaverApiClient _weaverApiClient;
        private readonly IPCApiClient _pcApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _configuration;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IBaseAllQueryService<InventoryItemPo> _inventoryQueryService;
        public AdvancePaymentQueryService(
            FinanceDbContext db,
            DaprClient daprClient,
            IBDSApiClient bDSApiClient, 
            IFileGatewayClient fileGatewayClient,
            IAppServiceContextAccessor appServiceContextAccessor,
            IWeaverApiClient weaverApiClient,
            IPCApiClient pcApiClient,
            IPurchaseApiClient purchaseApiClient,
            IUnitOfWork unitOfWork,
            IConfiguration configuration,
            ILogger<AdvancePaymentQueryService> logger,
            IHttpContextAccessor httpContextAccessor,
            IApplyBFFService applyBFFService,
            IInventoryMgmAppService inventoryMgmAppService,
            IBaseAllQueryService<InventoryItemPo> inventoryQueryService,
            ICodeGenClient codeClient) : base(db, daprClient, codeClient, logger, httpContextAccessor, applyBFFService)
        {
            _db = db;
            _bDSApiClient = bDSApiClient;
            _appServiceContextAccessor = appServiceContextAccessor;
            _pcApiClient = pcApiClient;
            _weaverApiClient = weaverApiClient;
            _purchaseApiClient = purchaseApiClient;
            _unitOfWork = unitOfWork;
            _configuration = configuration;
            _fileGatewayClient = fileGatewayClient;
            _inventoryQueryService = inventoryQueryService;
            _inventoryMgmAppService = inventoryMgmAppService;
        }
        /// <summary>
        /// 获取单头列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PageResponse<AdvancePaymentOutput>> GetAdvancePaymentList(AdvancePaymentInput input)
        {
            Expression<Func<AdvancePaymentItemPo, bool>> exp = z => 1 == 1;
            exp = await InitExp(input, exp);
            var query = _db.AdvancePaymentItem.Where(exp);
            #region 排序
            query = query.OrderByDescending(z => z.CreatedTime);
            #endregion
            //总条数
            var count = await query.CountAsync();
            var list = await query.Skip((input.page - 1) * input.limit).
                       Take(input.limit).
                       Select(z => z.Adapt<AdvancePaymentOutput>()).AsNoTracking().ToListAsync();
            return new PageResponse<AdvancePaymentOutput>() { List = list, Total = count };
        }

        /// <summary>
        /// 获取明细列表(应收明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>  
        public async Task<PageResponse<AdvancePaymentDebtDetailOutput>> GetAdvancePaymentDebtDetails(AdvancePaymentDetailInput input)
        {
            var list = await _db.AdvancePaymentDebtDetails.
                        Where(p => p.AdvancePaymentItemId == input.AdvancePaymentItemId).
                        Select(z => z.Adapt<AdvancePaymentDebtDetailOutput>()).AsNoTracking().ToListAsync();
            return new PageResponse<AdvancePaymentDebtDetailOutput>() { List = list, Total = list.Count };
        }


        /// <summary>
        /// 获取明细列表(货品明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>  
        public async Task<PageResponse<AdvancePaymentProductDetailOutput>> GetAdvancePaymentProductDetails(AdvancePaymentDetailInput input)
        {
            var list = await _db.AdvancePaymentProductDetails.
                        Where(p => p.AdvancePaymentItemId == input.AdvancePaymentItemId).
                        Select(z => z.Adapt<AdvancePaymentProductDetailOutput>()).AsNoTracking().ToListAsync();

            return new PageResponse<AdvancePaymentProductDetailOutput>() { List = list, Total = list.Count };
        }

        private async Task<Expression<Func<AdvancePaymentItemPo, bool>>> InitExp(AdvancePaymentInput query, Expression<Func<AdvancePaymentItemPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            //获取用户数据策略
            var input = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };
            var strategry = await _pcApiClient.GetStrategyAsync(input);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("project"))
                {
                    exp = exp.And(z => 1 != 1);
                    return exp;
                }
                if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))
                {
                    exp = exp.And(z => EF.Functions.Like(z.BillCode, $"%{query.searchKey}%")
                            || EF.Functions.Like(z.ServiceName, $"%{query.searchKey}%")
                            || EF.Functions.Like(z.CompanyName, $"%{query.searchKey}%"));
                }
                if (query.BillDateS != null && query.BillDateE != null)
                {
                    exp = exp.And(z => (z.BillDate != null && z.BillDate.Value >= query.DateS && z.BillDate.Value <= query.DateE));
                }
                if (query.ServiceId != null)
                {
                    exp = exp.And(z => z.ServiceId == query.ServiceId);
                }
                if (!string.IsNullOrEmpty(query.ServiceName))
                {
                    exp = exp.And(z => z.ServiceName != null && z.ServiceName.Contains(query.ServiceName));
                }
                if (query.CompanyId != null)
                {
                    exp = exp.And(z => z.CompanyId == query.CompanyId);
                }
                if (query.CustomerId != null)
                {
                    exp = exp.And(z => z.CustomerId == query.CustomerId);
                }
                if (!string.IsNullOrWhiteSpace(query.BillCode))
                {
                    exp = exp.And(z => EF.Functions.Like(z.BillCode ?? "", $"%{query.BillCode}%"));
                }
                if (!string.IsNullOrWhiteSpace(query.Name))
                {
                    exp = exp.And(z => EF.Functions.Like(z.BillCode ?? "", $"%{query.Name}%"));
                }
                if (query.Id != null)
                {
                    exp = exp.And(z => z.Id == query.Id);
                }
                if (query.Status == AdvancePaymentStatusEnum.My)
                {
                    var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(_appServiceContextAccessor.Get().UserName, WorkFlowCode.AdvancePaymentForm.GetDescription()));
                    var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                    exp = exp.And(t => t.OARequestId != null && oARequestIds.Contains(t.OARequestId));
                }
                if (query.Status != AdvancePaymentStatusEnum.My && !query.Id.HasValue)
                {
                    if (query.Status.HasValue)
                    {
                        if (query.Status.Value != AdvancePaymentStatusEnum.All)
                        {
                            exp = exp.And(z => z.Status == query.Status.Value);
                        }
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }
                        else if (key.ToLower() == "project")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => !t.ProjectId.HasValue || strategList.Contains(t.ProjectId.Value));
                            }
                        }
                        else if (key == "service")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.ServiceId.Value));
                            }
                        }
                        else if (key == "accountingDept")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToHashSet();
                                exp = exp.And(t => strategList.Contains(t.BusinessDeptId));
                            }
                        }
                        else if (key == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => !t.CustomerId.HasValue || strategList.Contains(t.CustomerId.Value));
                            }
                        }
                    }
                }
            }
            return exp;
        }

        /// <summary>
        /// 获取tab数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<AdvancePaymentTabOutput>> GetTabCountAsync(AdvancePaymentInput input)
        {
            var ret = BaseResponseData<AdvancePaymentTabOutput>.Success("获取成功！");
            input.Status = AdvancePaymentStatusEnum.All;
            Expression<Func<AdvancePaymentItemPo, bool>> exp = z => 1 == 1;
            exp = await InitExp(input, exp);
            var query = _db.AdvancePaymentItem.Where(exp);


            var myAuditList = await query.ToListAsync();

            //获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = _appServiceContextAccessor.Get().UserId, functionUri = "metadata://fam" };
            var strategys = await _pcApiClient.GetStrategyAsync(strategryquery);

            if (strategys != null)
            {
                var rowStrategies = strategys.RowStrategies;
                if (!rowStrategies.Keys.Contains("company") || !rowStrategies.Keys.Contains("accountingDept"))
                {
                    return ret;
                }
            }

            var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(_appServiceContextAccessor.Get().UserName ?? default, WorkFlowCode.AdvancePaymentForm.GetDescription()));
            var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
            var res = await query.AsNoTracking().ToListAsync();
            var result = new AdvancePaymentTabOutput
            {
                AllCount = res.Count(),
                Refuse = res.Where(t => t.Status == AdvancePaymentStatusEnum.Refuse).Count(),
                AuditingCount = res.Where(t => t.Status == AdvancePaymentStatusEnum.WaitAudit).Count(),
                WaitSubmitCount = res.Where(t => t.Status == AdvancePaymentStatusEnum.WaitSubmit && t.CreatedBy == _appServiceContextAccessor.Get().UserName).Count(),
                MyAuditCount = myAuditList.Where(t => t.Status == AdvancePaymentStatusEnum.WaitAudit &&
                                              t.OARequestId != null &&
                                              oARequestIds.Contains(t.OARequestId)).Count(),
            };
            result.CompletedCount = res.Where(t => t.Status == AdvancePaymentStatusEnum.Complate).Count();
            ret.Data = result;
            return ret;
        }


        /// <summary>
        /// 拉取详情数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> GetDetailsInfo(AdvancePaymentDebtDetailQueryInput input)
        {
            try
            {
                var ret = BaseResponseData<AdvancePaymentDebtDetailInfo>.Success("操作成功！");
                Expression<Func<DebtPo, bool>> exp = z => z.CompanyId == input.CompanyId && z.ServiceId == input.ServiceId && z.CustomerId == input.CustomerId;
                if (!string.IsNullOrEmpty(input.BusinessDeptId))
                {
                    exp = exp.And(x => x.BusinessDeptId == input.BusinessDeptId);
                }
                if (input.ProjectId.HasValue)
                {
                    exp = exp.And(x => x.ProjectId == input.ProjectId);
                }
                if (!string.IsNullOrEmpty(input.DebtBillNo))
                {
                    exp = exp.And(x => x.BillCode == input.DebtBillNo);
                }
                if (!string.IsNullOrEmpty(input.PurchaseCode))
                {
                    exp = exp.And(x => x.PurchaseCode == input.PurchaseCode);
                }
                if (input.AgentId.HasValue)
                {
                    exp = exp.And(x => x.AgentId == input.AgentId.Value);
                }
                if (input.StartTime.HasValue && input.EndTime.HasValue)
                {
                    var startTime = input.StartTime.Value.AddHours(8);
                    var endTime = input.EndTime.Value.AddHours(8);
                    if (startTime == endTime)
                    {
                        endTime = endTime.AddDays(1);
                    }
                    exp = exp.And(x => x.BillDate >= startTime && x.BillDate <= endTime);
                }
                // 已经存在的应付不允许再拉取到
                var existsDebtBillCodes = await (from apdd in _db.AdvancePaymentDebtDetails join api in _db.AdvancePaymentItem on apdd.AdvancePaymentItemId equals api.Id where api.Status == AdvancePaymentStatusEnum.WaitSubmit || api.Status == AdvancePaymentStatusEnum.WaitAudit select apdd.DebtBillNo).ToListAsync();
                if (existsDebtBillCodes != null && existsDebtBillCodes.Any())
                {
                    exp = exp.And(x => !string.IsNullOrEmpty(x.BillCode) && !existsDebtBillCodes.Contains(x.BillCode));
                }
                var debtsAll = await _db.Debts.Where(exp).AsNoTracking().ToListAsync();
                var data = new AdvancePaymentDebtDetailInfo();
                var advancePaymentDebtDetails = new List<AdvancePaymentDebtDetailOutput>();
                var advancePaymentProductDetails = new List<AdvancePaymentProductDetailOutput>();
                if (debtsAll.Any())
                {
                    #region 提前付款垫资 应付明细
                    var debtAllIds = debtsAll.Select(p => p.Id).ToHashSet();
                    var debtDetails = await _db.DebtDetails.Include(p => p.Debt).Where(p => p.DebtId.HasValue &&
                                                                       debtAllIds.Contains(p.DebtId.Value) &&
                                                                       p.AccountPeriodType == 0 &&
                                                                       !p.ProbablyPayTime.HasValue &&
                                                                       !p.BackPayTime.HasValue &&
                                                                       p.Status == DebtDetailStatusEnum.WaitExecute).AsNoTracking().ToListAsync();
                    var debtIds = debtDetails.Select(x => x.DebtId).Distinct().ToHashSet();
                    var debts = debtsAll.Where(p => debtIds.Contains(p.Id)).ToList();
                    var debtDetailCreditIds = debtDetails.Select(p => p.CreditId).ToList();
                    var debtDetailCreditHashSet = debtDetailCreditIds.ToHashSet();
                    var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId.HasValue && debtDetailCreditHashSet.Contains(p.CreditId.Value)).ToListAsync();
                    var invoiceNos = invoiceCredits.Select(x => x.InvoiceNo).ToList();
                    var ciList = await (from i in _db.Invoices
                                 join ci in _db.CustomizeInvoiceItem on i.CustomizeInvoiceCode equals ci.Code into ciGroup
                                 from ci in ciGroup.DefaultIfEmpty()
                                 where invoiceNos.Contains(i.InvoiceNo) && ci.ChangedStatus.HasValue
                                 select i.InvoiceNo).Distinct().ToListAsync();
                    if (ciList != null && ciList.Any())
                    {
                        var redChongInvoiceCreditIds = invoiceCredits.Where(x => ciList.Contains(x.InvoiceNo)).Select(x => x.CreditId).ToList();
                        //排除存在发票红冲的应收
                        debtDetailCreditIds = debtDetailCreditIds.Except(redChongInvoiceCreditIds).ToList();
                    }
                    //排除存在认款的应收
                    var existRecognizeReceiveCreditIds = await _db.RecognizeReceiveDetailCredits.Where(x => debtDetailCreditHashSet.Contains(x.CreditId)).Select(x => x.CreditId).ToListAsync();
                    debtDetailCreditIds = debtDetailCreditIds.Except(existRecognizeReceiveCreditIds).ToList();

                    var credits = await _db.Credits.Where(x => x.InvoiceStatus == InvoiceStatusEnum.invoiced && debtDetailCreditIds.Contains(x.Id)).AsNoTracking().ToListAsync();
                    //找出应收对应的所有应付（不适配以上条件）
                    var exceedDebtDetails = await _db.DebtDetails.Include(x => x.Debt).Where(x => debtDetailCreditHashSet.Contains(x.CreditId)).AsNoTracking().ToListAsync();
                    foreach (var debt in debts)
                    {
                        //取得当前回款账期对应应收
                        var currentCreditIds = debtDetails.Where(x => x.DebtId == debt.Id).Select(x => x.CreditId).ToHashSet();
                        var currentCredits = credits.Where(x => currentCreditIds.Contains(x.Id)).ToList();
                        var currentCreditsValue = currentCredits.Sum(x => x.Value);
                        //应付只会对应一个应收,多个回款账期也只对应一个
                        var firstDebtDetail = debtDetails.FirstOrDefault(x => x.DebtId == debt.Id);
                        if (firstDebtDetail == null)
                        {
                            continue;
                        }
                        //合并所有回款账期金额
                        firstDebtDetail.Value = debtDetails.Where(x => x.DebtId == debt.Id).Sum(x => x.Value);
                        var creditId = firstDebtDetail != null && firstDebtDetail.CreditId.HasValue ? firstDebtDetail.CreditId.Value : Guid.Empty;
                        var credit = credits.FirstOrDefault(x => x.Id == creditId);
                        if (credit == null)
                        {
                            continue;
                        }
                        //实际支付上游日期
                        var actualPaymentDate = credit.BillDate;
                        if (input.ConfirmPaymentDateMode == ConfirmPaymentDateModeEnum.Days)
                        {
                            actualPaymentDate = actualPaymentDate.Value.AddDays(input.PaySupplierGoodsDay.HasValue ? input.PaySupplierGoodsDay.Value : 0);
                            if (actualPaymentDate <= DateTime.Now)
                            {
                                actualPaymentDate = DateTime.Now;
                            }
                        }
                        else
                        {
                            actualPaymentDate = input.PaySupplierGoodsDate.Value;
                        }
                        var invoiceCredit = invoiceCredits.Where(p => p.InvoiceTime.HasValue && p.CreditId == creditId).OrderBy(p => p.InvoiceTime).FirstOrDefault();
                        if (invoiceCredit == null)
                        {
                            continue;
                        }
                        //预计回款日期
                        var estimateReturnDate = invoiceCredit.InvoiceTime.Value.AddDays(input.ReturnDays);
                        //垫资天数=预计回款日期-实际支付上游日期
                        var advancePaymentDays = (estimateReturnDate - actualPaymentDate).Value.Days;
                        //供应链金融折扣
                        var financeDiscount = input.MonthRate * advancePaymentDays / 30;
                        //当前应收对应应付总金额
                        var currentDebts = exceedDebtDetails.Where(x => x.CreditId == creditId).Select(x => x.Debt).DistinctBy(x=>x.BillCode).ToList();
                        var currentDebtsValue = currentDebts.Sum(x => x.Value);
                        //回款账期付款计划在应付的比例
                        var radio = firstDebtDetail.Value / currentDebtsValue;
                        //垫资金额
                        var advanceTaxAmount = currentCreditsValue * radio * financeDiscount / 100;
                        var model = new AdvancePaymentDebtDetailOutput();
                        model.DebtBillNo = debt.BillCode;
                        model.DebtId = debt.Id;
                        model.OrderNo = credit != null ? credit.OrderNo : string.Empty;
                        model.AccountPeriodType = AccountPeriodTypeEnum.Repayment;
                        model.EstimateReturnDate = estimateReturnDate;
                        model.AgentId = debt.AgentId;
                        model.AgentName = debt.AgentName;
                        model.PaymentAmount = firstDebtDetail.Value;
                        model.ActualPaymentDate = actualPaymentDate;
                        model.AdvancePaymentDays = advancePaymentDays;
                        model.MonthRate = input.MonthRate;
                        model.FinanceDiscount = Math.Round(financeDiscount, 2);
                        model.AdvanceAmount = Math.Round(currentCreditsValue * radio, 2);
                        model.AdvanceTaxAmount = Math.Round(advanceTaxAmount, 2);
                        model.ReturnDays = input.ReturnDays;
                        model.InvoiceTime = invoiceCredit.InvoiceTime;
                        model.PurchaseCode = firstDebtDetail.PurchaseCode;
                        model.CreditValue = Math.Round(currentCreditsValue * radio, 2);
                        model.Rate = currentDebtsValue / credit.Value;
                        advancePaymentDebtDetails.Add(model);
                    }
                    data.AdvancePaymentDebtDetails = advancePaymentDebtDetails;
                    #endregion

                    #region 提前付款垫资 产品明细
                    //var purInput = new QueryOrderDetailsByPurchaseCodeInput();
                    //purInput.PurchaseOrderCodes = debtDetails.Select(x => x.PurchaseCode).ToList();
                    //var purRet = await _purchaseApiClient.GetOrderDetailsByPurchaseCode(purInput);
                    //if (purRet != null && purRet.Any())
                    //{
                    //    foreach (var pur in purRet)
                    //    {
                    //        advancePaymentProductDetails.Add(new AdvancePaymentProductDetailOutput
                    //        {
                    //            BusinessBillNo = pur.PurchaseOrder?.Code,
                    //            ProductNo = pur.Product?.ProductNo,
                    //            ProductName = pur.Product?.Name,
                    //            Quantity = pur.Quantity,
                    //            Profit = 0, //前端用户输入
                    //            SubTotal = 0, //前端用户输入
                    //            OriginalSalePrice = pur.OriginDiscountAmount,
                    //            OriginalCost = pur.OriginCost
                    //        });
                    //    }
                    //}
                    //data.AdvancePaymentProductDetails = advancePaymentProductDetails;
                    #endregion
                }
                ret.Data = data;
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 重新支付上游日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> ReSetDate(SaveAdvancePaymentInput input)
        {
            try
            {
                var ret = BaseResponseData<AdvancePaymentDebtDetailInfo>.Success("设定成功！");
                if (input.advancePaymentDebtDetails == null || !input.advancePaymentDebtDetails.Any())
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "未获取到数据");
                }
                var item = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.Id);
                if (item == null)
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "请先保存数据或拉取数据");
                }
                if (item.AdvancePaymentDebtDetails != null && item.AdvancePaymentDebtDetails.Any())
                {
                    // 清除旧的关联数据
                    _db.AdvancePaymentDebtDetails.RemoveRange(item.AdvancePaymentDebtDetails);
                    item.AdvancePaymentDebtDetails?.Clear(); // 清除导航属性
                }
                var debtBillNos = input.advancePaymentDebtDetails.Select(x => x.DebtBillNo).ToList();
                var debts = await _db.Debts.Where(x => debtBillNos.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                var debtIds = debts.Select(x => x.Id).ToHashSet();
                var debtDetails = await _db.DebtDetails.Include(p => p.Debt).Where(p => p.DebtId.HasValue &&
                                                                   debtIds.Contains(p.DebtId.Value) &&
                                                                   p.AccountPeriodType == 0 &&
                                                                   !p.ProbablyPayTime.HasValue &&
                                                                   !p.BackPayTime.HasValue &&
                                                                   p.Status == DebtDetailStatusEnum.WaitExecute).AsNoTracking().ToListAsync();
                var debtDetailCreditIds = debtDetails.Select(p => p.CreditId).ToHashSet();
                var invoiceCredits = await _db.InvoiceCredits.Where(p => p.CreditId.HasValue && debtDetailCreditIds.Contains(p.CreditId.Value)).ToListAsync();
                var credits = await _db.Credits.Where(x => x.InvoiceStatus == InvoiceStatusEnum.invoiced && debtDetailCreditIds.Contains(x.Id)).AsNoTracking().ToListAsync();

                var data = new AdvancePaymentDebtDetailInfo();
                var advancePaymentDebtDetails = new List<AdvancePaymentDebtDetailOutput>();
                if (debts.Any())
                {
                    int index = 0;
                    foreach (var debt in debts)
                    {
                        //取得当前回款账期对应应收
                        var currentCreditIds = debtDetails.Where(x => x.DebtId == debt.Id).Select(x => x.CreditId).ToHashSet();
                        var currentCredits = credits.Where(x => currentCreditIds.Contains(x.Id)).ToList();
                        var currentCreditsValue = currentCredits.Sum(x => x.Value);
                        //应付只会对应一个应收,多个回款账期也只对应一个
                        var firstDebtDetail = debtDetails.FirstOrDefault(x => x.DebtId == debt.Id);
                        if (firstDebtDetail == null)
                        {
                            continue;
                        }
                        //合并所有回款账期金额
                        firstDebtDetail.Value = debtDetails.Where(x => x.DebtId == debt.Id).Sum(x => x.Value);
                        var creditId = firstDebtDetail != null && firstDebtDetail.CreditId.HasValue ? firstDebtDetail.CreditId.Value : Guid.Empty;
                        var credit = credits.FirstOrDefault(x => x.Id == creditId);
                        if (credit == null)
                        {
                            continue;
                        }
                        //实际支付上游日期
                        if (!input.advancePaymentDebtDetails[index].ActualPaymentDate.HasValue)
                        {
                            return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, $"第{index+1}行未获取到支付上游日期");
                        }
                        var actualPaymentDate = input.advancePaymentDebtDetails[index].ActualPaymentDate.Value.AddHours(8);
                        var invoiceCredit = invoiceCredits.Where(p => p.InvoiceTime.HasValue && p.CreditId == creditId).OrderBy(p => p.InvoiceTime).FirstOrDefault();
                        if (invoiceCredit == null)
                        {
                            continue;
                        }
                        //预计回款日期
                        var estimateReturnDate = invoiceCredit.InvoiceTime.Value.AddDays(input.Day);
                        //垫资天数=预计回款日期-实际支付上游日期
                        var advancePaymentDays = (estimateReturnDate - actualPaymentDate).Days;
                        //供应链金融折扣
                        var financeDiscount = input.MonthRate * advancePaymentDays / 30;
                        var debtDetailsTemp = debtDetails.Where(p => p.CreditId == credit.Id).ToList();
                        //当前应收对应应付总金额
                        var currentDebts = debtDetails.Where(x => x.CreditId == creditId).Select(x => x.Debt).DistinctBy(x => x.BillCode).ToList();
                        var currentDebtsValue = currentDebts.Sum(x => x.Value);
                        //回款账期付款计划在应付的比例
                        var radio = firstDebtDetail.Value / currentDebtsValue;
                        //垫资金额
                        var advanceTaxAmount = currentCreditsValue * radio * financeDiscount / 100;
                        var model = new AdvancePaymentDebtDetailOutput();
                        model.DebtBillNo = debt.BillCode;
                        model.DebtId = debt.Id;
                        model.OrderNo = credit != null ? credit.OrderNo : string.Empty;
                        model.AccountPeriodType = AccountPeriodTypeEnum.Repayment;
                        model.EstimateReturnDate = estimateReturnDate;
                        model.AgentId = debt.AgentId;
                        model.AgentName = debt.AgentName;
                        model.PaymentAmount = firstDebtDetail.Value;
                        model.ActualPaymentDate = actualPaymentDate;
                        model.AdvancePaymentDays = advancePaymentDays;
                        model.MonthRate = input.MonthRate;
                        model.FinanceDiscount = Math.Round(financeDiscount, 2);
                        model.AdvanceAmount = Math.Round(currentCreditsValue * radio, 2);
                        model.AdvanceTaxAmount = Math.Round(advanceTaxAmount, 2);
                        model.ReturnDays = input.Day;
                        model.InvoiceTime = invoiceCredit.InvoiceTime;
                        model.PurchaseCode = firstDebtDetail.PurchaseCode;
                        model.CreditValue = Math.Round(currentCreditsValue * radio, 2); ;
                        model.Rate = radio;
                        advancePaymentDebtDetails.Add(model);
                        index++;
                    }
                    data.AdvancePaymentDebtDetails = advancePaymentDebtDetails;
                }
                ret.Data = data;
                //付款计划明细表
                var addApdds = data.AdvancePaymentDebtDetails.Adapt<List<AdvancePaymentDebtDetailPo>>();
                if (addApdds != null && addApdds.Any())
                {
                    addApdds.ForEach(x =>
                    {
                        x.AdvancePaymentItemId = item.Id;
                        x.CreatedBy = input.CreateBy ??= "none";
                        x.UpdatedBy = input.CreateBy ??= "none";
                        x.UpdatedTime = DateTimeOffset.UtcNow;
                    });
                    await _db.AdvancePaymentDebtDetails.AddRangeAsync(addApdds);
                    await _unitOfWork.CommitAsync();
                }
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 重新计算含税垫资毛利
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<decimal?>> ReSetAmount([FromBody] ReComputeInput input)
        {
            try
            {
                var ret = BaseResponseData<decimal?>.Success("重新计算成功！");
                if (!input.AdvanceAmount.HasValue)
                {
                    return BaseResponseData<decimal?>.Failed(500, "未获取到垫资金额");
                }
                if (!input.FinanceDiscount.HasValue)
                {
                    return BaseResponseData<decimal?>.Failed(500, "未获取到供应链金融折扣");
                }
                if (!input.Rate.HasValue)
                {
                    return BaseResponseData<decimal?>.Failed(500, "未获取到收付比");
                }
                var amount = input.AdvanceAmount.Value * input.Rate.Value * input.FinanceDiscount.Value;
                ret.Data = amount;
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<decimal?>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> Save(SaveAdvancePaymentInput input)
        {
            try
            {
                //提前垫资主表id
                var itemId = Guid.NewGuid();
                if (input.advancePaymentDebtDetails == null || !input.advancePaymentDebtDetails.Any())
                {
                    return BaseResponseData<string>.Failed(500, "请至少勾选一条数据");
                }
                //新增or修改
                if (!input.Id.HasValue)
                {
                    //生成单号
                    var codeResult = await CreateBillCode(input.CompanyId.Value.ToString(), deptShortName: input.BusinessDeptShortName ?? "ZXBD", billType: "ADV");
                    var advancePaymentItem = input.Adapt<AdvancePaymentItemPo>();
                    advancePaymentItem.Id = itemId;
                    advancePaymentItem.StartTime = input.StartTime.HasValue ? input.StartTime.Value.AddHours(8) : input.StartTime;
                    advancePaymentItem.EndTime = input.EndTime.HasValue ? input.EndTime.Value.AddHours(8) : input.EndTime;
                    advancePaymentItem.Status = AdvancePaymentStatusEnum.WaitSubmit;
                    advancePaymentItem.BillCode = codeResult.Item1;
                    advancePaymentItem.BillDate = codeResult.Item2;
                    advancePaymentItem.CreatedBy = input.CreateBy ??= "none";
                    await _db.AdvancePaymentItem.AddAsync(advancePaymentItem);
                    //付款计划明细表
                    var advancePaymentDebtDetails = input.advancePaymentDebtDetails.Adapt<List<AdvancePaymentDebtDetailPo>>();
                    foreach (var x in advancePaymentDebtDetails)
                    {
                        if (x.AdvanceTaxAmount.HasValue && x.AdvanceTaxAmount.Value < 0)
                        {
                            return BaseResponseData<string>.Failed(500, "付款计划中垫资毛利为负数，不允许保存");
                        }
                        if (x.FinanceDiscount.HasValue && x.FinanceDiscount.Value < 0)
                        {
                            return BaseResponseData<string>.Failed(500, "付款计划中供应链金融折扣为负数，不允许保存");
                        }
                        if (x.AdvancePaymentDays.HasValue && x.AdvancePaymentDays.Value < 0)
                        {
                            return BaseResponseData<string>.Failed(500, "付款计划中垫资天数为负数，不允许保存");
                        }
                        x.AdvancePaymentItemId = itemId;
                        x.CreatedBy = input.CreateBy ??= "none";
                    }
                    await _db.AdvancePaymentDebtDetails.AddRangeAsync(advancePaymentDebtDetails);
                    //货品明细表
                    var advancePaymentProductDetails = new List<AdvancePaymentProductDetailPo>();
                    //获取货品明细
                    var debtIds = input.advancePaymentDebtDetails.Select(x => x.DebtId).ToHashSet();
                    var debts = await _db.Debts.Where(x => debtIds.Contains(x.Id)).AsNoTracking().ToListAsync();
                    var purInput = new QueryOrderDetailsByPurchaseCodeInput();
                    purInput.PurchaseOrderCodes = input.advancePaymentDebtDetails.Select(x => x.PurchaseCode).ToList();
                    var purRet = await _purchaseApiClient.GetOrderDetailsByPurchaseCode(purInput);
                    if (purRet.list != null && purRet.list.Any())
                    {
                        foreach (var pur in purRet.list)
                        {
                            advancePaymentProductDetails.Add(new AdvancePaymentProductDetailPo
                            {
                                Id = Guid.NewGuid(),
                                AdvancePaymentItemId = itemId,
                                BusinessBillNo = pur.PurchaseOrder?.Code,
                                ProductNo = pur.Product?.ProductNo,
                                ProductName = pur.Product?.Name,
                                Quantity = pur.Quantity,
                                Profit = 0, //前端用户输入
                                SubTotal = 0, //前端用户输入
                                OriginalSalePrice = pur.BDetail?.Price * pur.Quantity,
                                OriginalCost = pur.Cost * pur.Quantity,
                                PurchaseCode = pur.PurchaseOrder?.Code,
                                CreatedBy = input.CreateBy ??= "none"
                            });
                        }
                    }
                    if (advancePaymentProductDetails.Any())
                    {
                        await _db.AdvancePaymentProductDetails.AddRangeAsync(advancePaymentProductDetails);
                    }
                }
                else
                {
                    itemId = input.Id.Value;
                    var item = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.Id);
                    if (item != null)
                    {
                        if (item.AdvancePaymentDebtDetails != null && item.AdvancePaymentDebtDetails.Any())
                        {
                            // 清除旧的关联数据
                            _db.AdvancePaymentDebtDetails.RemoveRange(item.AdvancePaymentDebtDetails);
                            item.AdvancePaymentDebtDetails?.Clear(); // 清除导航属性
                        }
                        if (item.AdvancePaymentProductDetails != null && item.AdvancePaymentProductDetails.Any())
                        {
                            // 清除旧的关联数据
                            _db.AdvancePaymentProductDetails.RemoveRange(item.AdvancePaymentProductDetails);
                            item.AdvancePaymentProductDetails?.Clear(); // 清除导航属性
                        }
                        //修改主体信息
                        if (input.StartTime.HasValue && input.EndTime.HasValue)
                        {
                            item.StartTime = input.StartTime.Value.AddHours(8);
                            item.EndTime = input.EndTime.Value.AddHours(8);
                        }
                        item.Day = input.Day;
                        item.MonthRate = input.MonthRate;
                        item.ConfirmPaymentDateMode = input.ConfirmPaymentDateMode;
                        item.PaySupplierGoodsDate = input.PaySupplierGoodsDate;
                        item.Remark = input.Remark;
                        if (!string.IsNullOrEmpty(input.AttachFileIds))
                        {
                            item.AttachFileIds = input.AttachFileIds;
                        }
                        if (input.AdvanceCreditAmount.HasValue)
                        {
                            item.AdvanceCreditAmount = input.AdvanceCreditAmount;
                        }

                        //付款计划明细表
                        var advancePaymentDebtDetails = input.advancePaymentDebtDetails.Adapt<List<AdvancePaymentDebtDetailPo>>();
                        foreach (var x in advancePaymentDebtDetails)
                        {
                            if (x.AdvanceTaxAmount.HasValue && x.AdvanceTaxAmount.Value < 0)
                            {
                                return BaseResponseData<string>.Failed(500, "付款计划中垫资毛利为负数，不允许保存");
                            }
                            if (x.FinanceDiscount.HasValue && x.FinanceDiscount.Value < 0)
                            {
                                return BaseResponseData<string>.Failed(500, "付款计划中供应链金融折扣为负数，不允许保存");
                            }
                            if (x.AdvancePaymentDays.HasValue && x.AdvancePaymentDays.Value < 0)
                            {
                                return BaseResponseData<string>.Failed(500, "付款计划中垫资天数为负数，不允许保存");
                            }
                            if (x.AdvanceTaxAmount > x.PaymentAmount)
                            {
                                return BaseResponseData<string>.Failed(500, "含税垫资毛利不能大于付款金额");
                            }
                            if (x.AdvanceAmount > x.CreditValue)
                            {
                                return BaseResponseData<string>.Failed(500, "垫资金额不能大于对应应收金额");
                            }
                            x.AdvancePaymentItemId = itemId;
                            x.CreatedBy = input.CreateBy ??= "none";
                            x.UpdatedBy = input.CreateBy ??= "none";
                            x.UpdatedTime = DateTimeOffset.UtcNow;
                        }
                        await _db.AdvancePaymentDebtDetails.AddRangeAsync(advancePaymentDebtDetails);
                        //货品明细表
                        var advancePaymentProductDetails = new List<AdvancePaymentProductDetailPo>();
                        //获取货品明细
                        var debtBillNos = input.advancePaymentDebtDetails.Select(x => x.DebtBillNo).ToHashSet();
                        var debts = await _db.Debts.Where(x => debtBillNos.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                        var purInput = new QueryOrderDetailsByPurchaseCodeInput();
                        purInput.PurchaseOrderCodes = input.advancePaymentDebtDetails.Select(x => x.PurchaseCode).ToList();
                        var purRet = await _purchaseApiClient.GetOrderDetailsByPurchaseCode(purInput);
                        if (purRet.list != null && purRet.list.Any())
                        {
                            foreach (var pur in purRet.list)
                            {
                                advancePaymentProductDetails.Add(new AdvancePaymentProductDetailPo
                                {
                                    Id = Guid.NewGuid(),
                                    AdvancePaymentItemId = item.Id,
                                    BusinessBillNo = pur.PurchaseOrder?.Code,
                                    ProductNo = pur.Product?.ProductNo,
                                    ProductName = pur.Product?.Name,
                                    Quantity = pur.Quantity,
                                    Profit = 0, //前端用户输入
                                    SubTotal = 0, //前端用户输入
                                    OriginalSalePrice = pur.BDetail?.Price * pur.Quantity,
                                    OriginalCost = pur.Cost * pur.Quantity,
                                    PurchaseCode = pur.PurchaseOrder?.Code,
                                    PurchaseDetailId = !string.IsNullOrEmpty(pur.Id) ? Guid.Parse(pur.Id) : Guid.Empty,
                                    PurchaseOrderId = !string.IsNullOrEmpty(pur.PurchaseOrder?.Id) ? Guid.Parse(pur.PurchaseOrder.Id) : Guid.Empty,
                                    CreatedBy = input.CreateBy ??= "none"
                                });
                            }
                        }
                        //修改再走一遍分摊
                        var addProducts = new List<AdvancePaymentProductDetailPo>();
                        if (advancePaymentProductDetails.Any())
                        {
                            // 分组处理
                            var groupedData = advancePaymentProductDetails
                                .GroupBy(d => d.PurchaseCode ?? "NULL")
                                .ToDictionary(
                                    g => g.Key,
                                    g => g.ToList()
                                );

                            foreach (var kvp in groupedData)
                            {
                                //当前采购单总垫资金额
                                var totalValue = advancePaymentDebtDetails.Where(x=>x.PurchaseCode == kvp.Key).Sum(x => x.AdvanceTaxAmount);
                                if (!totalValue.HasValue)
                                {
                                    totalValue = 0;
                                }

                                //货品明细容器
                                var productDetails = kvp.Value.OrderByDescending(p => p.Quantity).ToList();
                                decimal totalCost = productDetails.Where(p => p.OriginalCost.HasValue).Sum(p => p.OriginalCost.Value);
                                // 特判：总成本为0时均分
                                if (totalCost == 0)
                                {
                                    foreach (var p in productDetails)
                                    {
                                        p.SubTotal = Math.Round(totalValue.Value / productDetails.Count, 2);
                                        p.Profit = p.SubTotal / p.Quantity;
                                        p.UpdatedBy = input.CreateBy;
                                        p.UpdatedTime = DateTime.Now;
                                        addProducts.Add(p);
                                    }
                                }
                                else
                                {
                                    List<decimal> adjustedAmounts = new List<decimal>();
                                    // 2. 处理前N-1行（确保除得尽）
                                    for (int i = 0; i < productDetails.Count - 1; i++)
                                    {
                                        var product = productDetails[i];
                                        // 理论比例金额
                                        decimal theoretical = totalValue.Value != 0m ? (product.OriginalCost.Value / totalCost) * totalValue.Value : 0;
                                        decimal step = 0.01m * product.Quantity.Value; // 基础步长 = 0.01×数量

                                        // 关键：将金额对齐到步长的整数倍
                                        decimal aligned = Math.Round(theoretical / step, MidpointRounding.AwayFromZero) * step;
                                        adjustedAmounts.Add(aligned);
                                        product.SubTotal = aligned;
                                        product.Profit = product.SubTotal / product.Quantity;
                                        product.UpdatedBy = input.CreateBy;
                                        product.UpdatedTime = DateTime.Now;
                                        addProducts.Add(product);
                                    }

                                    // 3. 计算最后一行金额（承接误差）
                                    decimal sumPrev = adjustedAmounts.Sum();
                                    AdvancePaymentProductDetailPo lastProduct = productDetails.Last();
                                    var curremtTotalQuantity = lastProduct.Quantity;
                                    lastProduct.SubTotal = totalValue - sumPrev;// 总额 - 前N-1行和
                                    var modulusValue = lastProduct.SubTotal % lastProduct.Quantity;
                                    if (modulusValue == 0 || curremtTotalQuantity == 1)
                                    {
                                        lastProduct.AdvancePaymentItem = null;
                                        lastProduct.Id = Guid.NewGuid();
                                        lastProduct.Profit = lastProduct.SubTotal / lastProduct.Quantity;
                                        lastProduct.UpdatedBy = input.CreateBy;
                                        lastProduct.UpdatedTime = DateTime.Now;
                                        addProducts.Add(lastProduct);
                                    }
                                    else
                                    {
                                        var settings = new JsonSerializerSettings
                                        {
                                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore, // 忽略循环引用
                                            NullValueHandling = NullValueHandling.Ignore // 忽略null值
                                        };
                                        var newProduct = JsonConvert.DeserializeObject<AdvancePaymentProductDetailPo>(
                                            JsonConvert.SerializeObject(lastProduct, settings),
                                            settings);

                                        var currentTotalValue = lastProduct.SubTotal - modulusValue;

                                        lastProduct.AdvancePaymentItem = null;
                                        lastProduct.Id = Guid.NewGuid();
                                        lastProduct.Profit = currentTotalValue / curremtTotalQuantity;
                                        lastProduct.UpdatedBy = input.CreateBy;
                                        lastProduct.UpdatedTime = DateTime.Now;
                                        lastProduct.Quantity = curremtTotalQuantity - 1;
                                        lastProduct.SubTotal = lastProduct.Profit * (curremtTotalQuantity - 1); // 总额 - 前N-1行和
                                        lastProduct.OriginalSalePrice = lastProduct.OriginalSalePrice / curremtTotalQuantity * (curremtTotalQuantity - 1);
                                        lastProduct.OriginalCost = lastProduct.OriginalCost / curremtTotalQuantity * (curremtTotalQuantity - 1);
                                        addProducts.Add(lastProduct);
                                        adjustedAmounts.Add(lastProduct.SubTotal.Value);


                                        if (newProduct != null)
                                        {
                                            newProduct.AdvancePaymentItem = null;
                                            newProduct.Id = Guid.NewGuid();
                                            newProduct.AdvancePaymentItemId = lastProduct.AdvancePaymentItemId;
                                            newProduct.Profit = (currentTotalValue / curremtTotalQuantity) + modulusValue;
                                            newProduct.SubTotal = newProduct.Profit;
                                            newProduct.Quantity = 1;
                                            newProduct.OriginalSalePrice = newProduct.OriginalSalePrice / curremtTotalQuantity;
                                            newProduct.OriginalCost = newProduct.OriginalCost / curremtTotalQuantity;
                                            newProduct.UpdatedBy = input.CreateBy;
                                            newProduct.UpdatedTime = DateTime.Now;
                                            addProducts.Add(newProduct);
                                            adjustedAmounts.Add(newProduct.SubTotal.Value);
                                        }
                                    }
                                    //addProducts.Add(lastProduct);
                                }
                            }
                            if (!addProducts.Any())
                            {
                                addProducts = advancePaymentProductDetails;
                            }
                            _db.AdvancePaymentProductDetails.AddRange(addProducts);
                        }
                        item.AdvanceCreditAmount = advancePaymentDebtDetails.Sum(x => x.AdvanceAmount);
                        item.AdvanceCreditTaxAmount = advancePaymentDebtDetails.Sum(x => x.AdvanceTaxAmount);
                        item.UpdatedBy = input.CreateBy;
                        item.UpdatedTime = DateTimeOffset.UtcNow;
                        _db.AdvancePaymentItem.Update(item);
                    }
                }
                var res = await _unitOfWork.CommitAsync();
                var ret = BaseResponseData<string>.Success("保存成功");
                ret.Data = itemId.ToString();
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 分摊到货品(已弃用)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> AllocateToGoods(AdvancePaymentDebtDetailInfo input)
        {
            try
            {
                if (input.AdvancePaymentDebtDetails == null || !input.AdvancePaymentDebtDetails.Any())
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "付款计划明细为空，分摊失败");
                }
                var item = await _db.AdvancePaymentItem.AsNoTracking().Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.Id);
                if (item == null)
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "请先添加付款计划明细再行操作");
                }
                if (item.Status != AdvancePaymentStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "状态错误");
                }
                var appds = await _db.AdvancePaymentProductDetails.Where(x => x.AdvancePaymentItemId == item.Id).AsNoTracking().ToListAsync();
                if (appds == null || !appds.Any())
                {
                    return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, "货品明细为空，分摊失败");
                }
                _db.AdvancePaymentProductDetails.RemoveRange(appds);
                var debtBillNos = item.AdvancePaymentDebtDetails.Select(x => x.DebtBillNo).ToHashSet();
                var debts = await _db.Debts.Where(x => !string.IsNullOrEmpty(x.BillCode) && debtBillNos.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                //货品明细表
                var advancePaymentProductDetails = new List<AdvancePaymentProductDetailPo>();
                //货品明细总数
                var total = appds.Count;
                //已分摊金额
                var allocatedValue = 0M;
                //总垫资金额
                var totalValue = item.AdvancePaymentDebtDetails.Sum(x => x.AdvanceTaxAmount);
                if (!totalValue.HasValue)
                {
                    totalValue = 0;
                }

                //货品明细容器
                var productDetails = appds.OrderByDescending(p => p.Quantity).ToList();
                decimal totalCost = productDetails.Where(p => p.OriginalCost.HasValue).Sum(p => p.OriginalCost.Value);
                // 特判：总成本为0时均分
                if (totalCost == 0)
                {
                    foreach (var p in productDetails)
                    {
                        p.AdvancePaymentItem = null;
                        p.Id = Guid.NewGuid();
                        p.SubTotal = Math.Round(totalValue.Value / productDetails.Count, 2);
                        p.Profit = p.SubTotal / p.Quantity;
                        p.UpdatedBy = input.CreateBy;
                        p.UpdatedTime = DateTime.Now;
                        advancePaymentProductDetails.Add(p);
                    }
                }
                else
                {
                    List<decimal> adjustedAmounts = new List<decimal>();
                    // 2. 处理前N-1行（确保除得尽）
                    for (int i = 0; i < productDetails.Count - 1; i++)
                    {
                        var product = productDetails[i];
                        // 理论比例金额
                        decimal theoretical = totalValue.Value != 0m ? (product.OriginalCost.Value / totalCost) * totalValue.Value : 0;
                        decimal step = 0.01m * product.Quantity.Value; // 基础步长 = 0.01×数量

                        // 关键：将金额对齐到步长的整数倍
                        decimal aligned = Math.Round(theoretical / step, MidpointRounding.AwayFromZero) * step;
                        adjustedAmounts.Add(aligned);
                        product.AdvancePaymentItem = null;
                        product.Id = Guid.NewGuid();
                        product.SubTotal = aligned;
                        product.Profit = product.SubTotal / product.Quantity;
                        product.UpdatedBy = input.CreateBy;
                        product.UpdatedTime = DateTime.Now;
                        advancePaymentProductDetails.Add(product);
                    }

                    decimal sumPrev = adjustedAmounts.Sum();
                    AdvancePaymentProductDetailPo lastProduct = productDetails.Last();
                    var curremtTotalQuantity = lastProduct.Quantity;
                    lastProduct.SubTotal = totalValue - sumPrev;// 总额 - 前N-1行和
                    // 3. 计算最后一行金额（承接误差）
                    var modulusValue = lastProduct.SubTotal % lastProduct.Quantity;
                    if (modulusValue == 0)
                    {
                        lastProduct.AdvancePaymentItem = null;
                        lastProduct.Id = Guid.NewGuid();
                        lastProduct.Profit = lastProduct.SubTotal / lastProduct.Quantity;
                        lastProduct.UpdatedBy = input.CreateBy;
                        lastProduct.UpdatedTime = DateTime.Now;
                        advancePaymentProductDetails.Add(lastProduct);
                    }
                    else
                    {
                        var settings = new JsonSerializerSettings
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore, // 忽略循环引用
                            NullValueHandling = NullValueHandling.Ignore // 忽略null值
                        };
                        var newProduct = JsonConvert.DeserializeObject<AdvancePaymentProductDetailPo>(
                            JsonConvert.SerializeObject(lastProduct, settings),
                            settings);

                        var currentTotalValue = lastProduct.SubTotal - modulusValue;

                        lastProduct.AdvancePaymentItem = null;
                        lastProduct.Id = Guid.NewGuid();
                        lastProduct.Profit = currentTotalValue / curremtTotalQuantity;
                        lastProduct.UpdatedBy = input.CreateBy;
                        lastProduct.UpdatedTime = DateTime.Now;
                        lastProduct.Quantity = curremtTotalQuantity - 1; 
                        lastProduct.SubTotal = lastProduct.Profit * (curremtTotalQuantity - 1); // 总额 - 前N-1行和
                        advancePaymentProductDetails.Add(lastProduct);
                        adjustedAmounts.Add(lastProduct.SubTotal.Value);


                        if (newProduct != null)
                        {
                            newProduct.AdvancePaymentItem = null;
                            newProduct.Id = Guid.NewGuid();
                            newProduct.AdvancePaymentItemId = lastProduct.AdvancePaymentItemId;
                            newProduct.Profit = (currentTotalValue / curremtTotalQuantity) + modulusValue;
                            newProduct.SubTotal = newProduct.Profit;
                            newProduct.Quantity = 1;
                            newProduct.UpdatedBy = input.CreateBy;
                            newProduct.UpdatedTime = DateTime.Now;
                            advancePaymentProductDetails.Add(newProduct);
                            adjustedAmounts.Add(newProduct.SubTotal.Value);
                        }
                    }
                }

                if (advancePaymentProductDetails.Any())
                {
                    _db.AdvancePaymentProductDetails.AddRange(advancePaymentProductDetails);
                    var res = await _unitOfWork.CommitAsync();
                    var ret = BaseResponseData<AdvancePaymentDebtDetailInfo>.Success("操作成功！");
                    ret.Data = input;
                    return ret;
                }
                return BaseResponseData<AdvancePaymentDebtDetailInfo>.Success("操作成功！");
            }
            catch (Exception ex)
            {
                return BaseResponseData<AdvancePaymentDebtDetailInfo>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 提交到OA审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SubmitOA(AdvancePaymentDebtDetailInfo input)
        {
            try
            {
                var item = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.Id);
                if (item == null)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，请先添加付款计划明细再行操作");
                }
                if (item.Status != AdvancePaymentStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，状态错误");
                }
                if (item.AdvancePaymentProductDetails == null || !item.AdvancePaymentProductDetails.Any())
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，未获取到货品明细信息");
                }
                if (item.AdvancePaymentDebtDetails == null || !item.AdvancePaymentDebtDetails.Any())
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，未获取到付款计划明细信息");
                }
                var totalValueByDebtDetail = item.AdvancePaymentDebtDetails.Sum(x => x.AdvanceTaxAmount);
                var totalValueByProductDetail = item.AdvancePaymentProductDetails.Sum(x => x.SubTotal);
                if (totalValueByDebtDetail != totalValueByProductDetail)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，货品明细毛利小计总和不等于付款计划垫资金额毛利，请先操作分摊到货品！");
                }
                //库存盘点校验
                if (!item.CompanyId.HasValue)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，未获取到公司信息！");
                }
                var inventoryRet = await _inventoryMgmAppService.InventoryCheck(item.CompanyId.Value);
                if (inventoryRet.Code != CodeStatusEnum.Success)
                {
                    return BaseResponseData<string>.Failed(500, "保存失败，存在未完成的盘点数据");
                }
                //foreach (var appd in item.AdvancePaymentProductDetails)
                //{
                //    if (appd.Profit == 0)
                //    {
                //        return BaseResponseData<string>.Failed(500, "保存失败，货品明细存在单位毛利为0的数据，请先分摊到货品");
                //    }
                //}
                foreach (var apdd in item.AdvancePaymentDebtDetails)
                {
                    if (apdd.AdvancePaymentDays < 0)
                    {
                        return BaseResponseData<string>.Failed(500, "保存失败，付款计划明细存在垫资天数为负数的数据");
                    }
                    if (apdd.FinanceDiscount < 0)
                    {
                        return BaseResponseData<string>.Failed(500, "保存失败，付款计划明细存在供应链金融折扣为负数的数据");
                    }
                }
                var oaInput = new Gateway.Common.WeaverOA.WeaverInput
                {
                    BaseInfo = new Gateway.Common.WeaverOA.BaseInfo
                    {
                        Operator = input.CreateBy,
                        RequestName = $"【财务-提前垫资】{item.BillCode}",
                    },
                    MainData = new Gateway.Common.WeaverOA.MainData
                    {
                        FCreatorID = input.CreateBy,
                        Iframe_link = $"{_configuration["BaseUri"]}/fam/advanceCapitalApply/audit?id={item.Id}", //PC的Iframe地址,
                        Height_m = 600,
                        Iframe_link_m = $"{_configuration["BaseUri"].Replace("/v1", "")}/oamobile/#/finance/advancePaymentEmbedPage?id={item.Id}",
                        CpDepartment = item.BusinessDeptId,
                        CPcompanyCode = item.NameCode,
                        Condition = DateTime.Now.ToString("yyyy-MM-dd hh:mm:ssss"),
                        Business_id = item.Id.ToString()
                    },
                    OtherParams = new Gateway.Common.WeaverOA.OtherParams
                    {
                        IsNextFlow = 1,
                    }
                };
                if (string.IsNullOrEmpty(item.OARequestId))
                {
                    var oaRet = await _weaverApiClient.CreateWorkFlow(oaInput, Gateway.Common.WeaverOA.WorkFlowCode.AdvancePaymentForm);
                    if (!oaRet.Status)
                    {
                        return BaseResponseData<string>.Failed(500, oaRet.Msg);
                    }
                    item.Status = AdvancePaymentStatusEnum.WaitAudit;
                    item.OARequestId = oaRet.Data.Requestid.ToString();
                    await _unitOfWork.CommitAsync();
                }
                return BaseResponseData<string>.Success("操作成功！");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 删除付款计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> DeleteAdvancePaymentDebtDetails(AdvancePaymentDebtDetailInfo input)
        {
            try
            {
                var item = await _db.AdvancePaymentItem.FirstOrDefaultAsync(x => x.Id == input.Id);
                if (item == null)
                {
                    return BaseResponseData<string>.Failed(500, "请先添加付款计划明细再行操作");
                }
                if (item.Status != AdvancePaymentStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<string>.Failed(500, "状态错误");
                }
                if (input.AdvancePaymentDebtDetails == null || !input.AdvancePaymentDebtDetails.Any())
                {
                    return BaseResponseData<string>.Failed(500, "请选择要删除的付款计划明细");
                }
                var purchaseCodes = input.AdvancePaymentDebtDetails.Select(x => x.PurchaseCode).ToHashSet();
                if (purchaseCodes.Any())
                {
                    // 删除付款计划明细
                    var advancePaymentDebtDetails = await _db.AdvancePaymentDebtDetails.Where(x => x.AdvancePaymentItemId == item.Id && purchaseCodes.Contains(x.PurchaseCode)).AsNoTracking().ToListAsync();
                    if (advancePaymentDebtDetails.Any())
                    {
                        _db.AdvancePaymentDebtDetails.RemoveRange(advancePaymentDebtDetails);
                    }
                    // 删除货品明细
                    var advancePaymentProductDetails = await _db.AdvancePaymentProductDetails.Where(x => x.AdvancePaymentItemId == item.Id && purchaseCodes.Contains(x.PurchaseCode)).AsNoTracking().ToListAsync();
                    if (advancePaymentProductDetails.Any())
                    {
                        _db.AdvancePaymentProductDetails.RemoveRange(advancePaymentProductDetails);
                    }
                    await _unitOfWork.CommitAsync();
                }
                return BaseResponseData<string>.Success("操作成功！");
            }
            catch (Exception ex)
            {
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 根据id获取详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<AdvancePaymentItemInfo>> GetItemById(AdvancePaymentDetailInput input)
        {
            try
            {
                var ret = BaseResponseData<AdvancePaymentItemInfo>.Success("查询成功！");
                var item = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.AdvancePaymentItemId);
                if (item == null)
                {
                    return BaseResponseData<AdvancePaymentItemInfo>.Failed(500, "数据不存在或已被删除");
                }
                var model = item.Adapt<AdvancePaymentItemInfo>();
                model.AdvancePaymentDebtDetails = item.AdvancePaymentDebtDetails.Adapt<List<AdvancePaymentDebtDetailOutput>>();
                model.AdvancePaymentProductDetails = item.AdvancePaymentProductDetails.Adapt<List<AdvancePaymentProductDetailOutput>>();
                ret.Data = model;
                return ret;
            }
            catch (Exception ex)
            {
                return BaseResponseData<AdvancePaymentItemInfo>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 根据id删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> DeleteItemById(AdvancePaymentDetailInput input)
        {
            try
            {
                var item = await _db.AdvancePaymentItem.Include(x => x.AdvancePaymentDebtDetails).Include(x => x.AdvancePaymentProductDetails).FirstOrDefaultAsync(x => x.Id == input.AdvancePaymentItemId);
                if (item == null)
                {
                    return BaseResponseData<int>.Failed(500, "数据不存在或已被删除");
                }
                if (item.Status != AdvancePaymentStatusEnum.WaitSubmit)
                {
                    return BaseResponseData<int>.Failed(500, "只有待提交状态的单据才能删除");
                }
                if (item.AdvancePaymentDebtDetails != null && item.AdvancePaymentDebtDetails.Any())
                {
                    _db.AdvancePaymentDebtDetails.RemoveRange(item.AdvancePaymentDebtDetails);
                }
                if (item.AdvancePaymentProductDetails != null && item.AdvancePaymentProductDetails.Any())
                {
                    _db.AdvancePaymentProductDetails.RemoveRange(item.AdvancePaymentProductDetails);
                }
                _db.AdvancePaymentItem.Remove(item);
                await _unitOfWork.CommitAsync();
                return BaseResponseData<int>.Success("操作成功！");
            }
            catch (Exception ex)
            {
                return BaseResponseData<int>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        public async Task<BaseResponseData<int>> UploadAttachFileIds(AdvancePaymentItemAttachFileInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功！");

            var item = await _db.AdvancePaymentItem.FirstOrDefaultAsync(x => x.Id == input.AdvancePaymentItemId);
            if (item == null)
            {
                return BaseResponseData<int>.Failed(500, "数据不存在或已被删除");
            }
            if (item.Status != AdvancePaymentStatusEnum.WaitSubmit)
            {
                return BaseResponseData<int>.Failed(500, "只有待提交状态的单据才能上传附件");
            }
            item.AttachFileIds = item.AttachFileIds + "," + input.AttachFileIds;
            _db.AdvancePaymentItem.Update(item);
            ret.Data = await _unitOfWork.CommitAsync();
            if (ret.Data <= 0)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败！请再次操作，谢谢！");
            }
            return ret;
        }

        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BizFileUploadOutput>> GetAttachFile(AdvancePaymentItemAttachFileQueryInput input)
        {
            var item = await _db.AdvancePaymentItem.FirstOrDefaultAsync(x => x.Id == input.AdvancePaymentItemId);
            var bizFiles = new List<BizFileUploadOutput>();
            if (item != null && !string.IsNullOrEmpty(item.AttachFileIds))
            {
                var fileIds = item.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedBy = string.IsNullOrEmpty(file.UploadedBy) ? "系统附件" : file.UploadedBy;
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
                return bizFiles;
            }
            return bizFiles;
        }
    }
}
