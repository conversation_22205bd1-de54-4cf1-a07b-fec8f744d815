﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.AdvancePayment;
using Inno.CorePlatform.Finance.Application.DTOs.LossRecognition;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NPOI.Util;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 提现付款垫资查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AdvancePaymentQueryController : BaseController
    {
        private readonly IAdvancePaymentQueryService _advancePaymentQueryService;
        public AdvancePaymentQueryController(IAdvancePaymentQueryService advancePaymentQueryService, ISubLogService subLog) : base(subLog)
        {
            _advancePaymentQueryService = advancePaymentQueryService;
        }

        /// <summary>
        /// 获取提现付款垫资列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<AdvancePaymentOutput>> GetAdvancePaymentList([FromBody] AdvancePaymentInput input)
        { 
            var ret = await _advancePaymentQueryService.GetAdvancePaymentList(input);
            return new ResponseData<AdvancePaymentOutput>
            {
                Code = 200,
                Data = new Data<AdvancePaymentOutput>
                {
                    List = ret.List,
                    Total = ret.Total,
                }
            };
        }

        /// <summary>
        /// 获取付款计划垫资详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetDebtDetails")]
        public async Task<ResponseData<AdvancePaymentDebtDetailOutput>> GetAdvancePaymentDebtDetails([FromBody] AdvancePaymentDetailInput input)
        { 
            var ret = await _advancePaymentQueryService.GetAdvancePaymentDebtDetails(input);
            return new ResponseData<AdvancePaymentDebtDetailOutput>
            {
                Code = 200,
                Data = new Data<AdvancePaymentDebtDetailOutput>
                {
                    List = ret.List,
                    Total = ret.Total,
                }
            };
        }

        /// <summary>
        /// 获取提现付款垫资产品详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetProductDetails")]
        public async Task<ResponseData<AdvancePaymentProductDetailOutput>> GetAdvancePaymentProductDetails([FromBody] AdvancePaymentDetailInput input)
        {
            var ret = await _advancePaymentQueryService.GetAdvancePaymentProductDetails(input);
            return new ResponseData<AdvancePaymentProductDetailOutput>
            {
                Code = 200,
                Data = new Data<AdvancePaymentProductDetailOutput>
                {
                    List = ret.List,
                    Total = ret.Total,
                }
            };
        }

        /// <summary>
        /// 获取提现付款垫资统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<AdvancePaymentTabOutput>> GetTabCountAsync([FromBody] AdvancePaymentInput input)
        {
            var ret = await _advancePaymentQueryService.GetTabCountAsync(input);
            return ret;
        }

        /// <summary>
        /// 获取付款计划
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetDetailsInfo")]
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> GetDetailsInfo([FromBody] AdvancePaymentDebtDetailQueryInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.GetDetailsInfo(input);
            return ret;
        }

        /// <summary>
        /// 获取付款计划 - 确定
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Save")]
        public async Task<BaseResponseData<string>> Save([FromBody] SaveAdvancePaymentInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.Save(input);
            return ret;
        }

        /// <summary>
        /// 重新设定支付上游日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ReSetDate")]
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> ReSetDate([FromBody] SaveAdvancePaymentInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.ReSetDate(input);
            return ret;
        }

        /// <summary>
        /// 修改垫资金额
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ReSetAmount")]
        public async Task<BaseResponseData<decimal?>> ReSetAmount([FromBody] ReComputeInput input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.ReSetAmount(input);
            return ret;
        }

        /// <summary>
        /// 分摊到货品
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AllocateToGoods")]
        public async Task<BaseResponseData<AdvancePaymentDebtDetailInfo>> AllocateToGoods([FromBody] AdvancePaymentDebtDetailInfo input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.AllocateToGoods(input);
            return ret;
        }

        /// <summary>
        /// 提交到OA审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SubmitOA")]
        public async Task<BaseResponseData<string>> SubmitOA([FromBody] AdvancePaymentDebtDetailInfo input)
        {
            input.CreateBy = CurrentUser.UserName;
            var ret = await _advancePaymentQueryService.SubmitOA(input);
            return ret;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAdvancePaymentDebtDetails")]
        public async Task<BaseResponseData<string>> DeleteAdvancePaymentDebtDetails([FromBody] AdvancePaymentDebtDetailInfo input)
        {
            var ret = await _advancePaymentQueryService.DeleteAdvancePaymentDebtDetails(input);
            return ret;
        }

        /// <summary>
        /// 根据id获取详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetItemById")]
        public async Task<BaseResponseData<AdvancePaymentItemInfo>> GetItemById([FromBody] AdvancePaymentDetailInput input)
        {
            var ret = await _advancePaymentQueryService.GetItemById(input);
            return ret;
        }

        /// <summary>
        /// 删除整单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteItemById")]
        public async Task<BaseResponseData<int>> DeleteItemById([FromBody] AdvancePaymentDetailInput input)
        {
            var ret = await _advancePaymentQueryService.DeleteItemById(input);
            return ret;
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        [HttpPost("AttachFileIds")]
        public async Task<BaseResponseData<int>> AttachFileIds(AdvancePaymentItemAttachFileInput input)
        {
            var ret = await _advancePaymentQueryService.UploadAttachFileIds(input);
            return ret;
        }

        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(AdvancePaymentItemAttachFileQueryInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            ret.Data = await _advancePaymentQueryService.GetAttachFile(input);
            return ret;
        }

    }
}
