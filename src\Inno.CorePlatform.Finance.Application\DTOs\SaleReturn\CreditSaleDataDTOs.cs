﻿using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.SaleReturn
{
    public class CreditSaleDataDTOs
    {
        public string saleCode { get; set; }
        public string saleSystemName { get; set; }
        public Guid? saleSystemId { get; set; }
        public string saleType { get; set; }
        public string? hospitalId { get; set; }
        public string? hospitalName { get; set; }
        public SaleSourceEnum? source { get; set; }
        public SaleTypeEnum? creditSaleType { get; set; }
        public RebateTypeEnum? rebateType { get; set; }
        public string deptName { get; set; }
        public string projectCode { get; set; }
        public Guid? projectId { get; set; }
        public string projectName { get; set; }
        public string orginOrderNo { get; set; }
        public string? customerOrderCode { get; set; }
        public string? sunPurchaseRelatecode { get; set; }
        public string? customerPersonName { get; set; }
    }
}
