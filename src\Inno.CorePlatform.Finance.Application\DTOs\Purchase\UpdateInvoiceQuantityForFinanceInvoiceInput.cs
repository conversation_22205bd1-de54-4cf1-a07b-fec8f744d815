using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 更新寄售转购货入票数量（进项票）输入参数
    /// </summary>
    public class UpdateInvoiceQuantityForFinanceInvoiceInput
    {
        /// <summary>
        /// 勾稽合并单号
        /// </summary>
        public string invoiceNumber { get; set; }

        /// <summary>
        /// 是否入票 默认true 入票 false 取消入票
        /// </summary>
        public bool InvoiceFlag { get; set; } = true;

        /// <summary>
        /// 更新明细列表
        /// </summary>
        public List<UpdateInvoiceQuantityForFinanceInput> List { get; set; }
    }

    /// <summary>
    /// 寄售转购货更新入参（进项票）
    /// </summary>
    public class UpdateInvoiceQuantityForFinanceInput
    {
        /// <summary>
        /// 购货修订单号
        /// </summary>
        public string PurchaseOrderCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int InvoiceQuantity { get; set; }

        /// <summary>
        /// 成本
        /// </summary>
        public decimal UnitCost { get; set; }

        /// <summary>
        /// 税率,
        /// 增加原因：为了预防国家调整税率，导致前后两个月的货税率不一致而出现修订金额入票金额出错
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public string ProductNameId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }
    }
}
