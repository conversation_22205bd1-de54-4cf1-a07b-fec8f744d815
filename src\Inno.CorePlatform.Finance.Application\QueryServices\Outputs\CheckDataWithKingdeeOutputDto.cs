﻿using Inno.CorePlatform.Finance.Application.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class CheckDataWithKingdeeOutputDto
    {
        public List<CodeList>? CodeList { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class CodeList
    {
        public decimal Amount { get; set; }
        public string? Code { get; set; }
        public long Date { get; set; }
        public string FinanceDate { get; set; }
        public string DateStr { get { return (DateTimeHelper.LongToDateTime(Date)).ToString("yyyy-MM-dd"); } }
    }

    public class CheckDataWithKingdeeRes
    {
        public CheckDataWithKingdeeOutputDto CoreSysData { get; set; }
        public CheckDataWithKingdeeOutputDto KingdeeData { get; set; }

        /// <summary>
        /// 核心平台多出的单据
        /// </summary>
        public List<string> CoreSysMore { get; set; } = new List<string>();

        /// <summary>
        /// 金蝶多出的单据
        /// </summary>
        public List<string> KingdeeMore { get; set; } = new List<string>();
    }

    public class CheckDataWithKingdeeInputDto
    {
        public Guid CompanyId { get; set; }
        public long StartTime { get; set; }
        public long EndTime { get; set; }
    }

    public class CheckDataWithKingdeeInputDtoForKingdee
    {
        public List<string> CompanyId { get; set; }
        public long startDate { get; set; }
        public long endDate { get; set; }
    }

    public class KingdeeCheckBillRes
    {
        public KindeeCheckBillResData data { get; set; }
    }

    public class KindeeCheckBillResData
    {
        public int pageNo { get; set; }
        public int pageSize { get; set; }
        public List<CheckByBillForKingdeeOutput> rows { get; set; }
    }

    public class CheckByBillForKingdeeOutput
    {
        public string id { get; set; }

        /// <summary>
        /// 不含税总额-应收接口同
        /// </summary>
        public decimal jfzx_hxamount { get; set; }

        /// <summary>
        /// 含税总成本
        /// </summary>
        public decimal jfzx_hxpricetaxtotal { get; set; }

        /// <summary>
        /// 暂存出库总成本
        /// </summary>
        public decimal jfzx_totalsalescost { get; set; }

        /// <summary>
        /// 应收含税总额  
        /// </summary>
        public decimal jfzx_hxrecamount { get; set; }

        /// <summary>
        /// 应收不含税成本-总
        /// </summary>
        public decimal jfzx_hxtotalcost { get; set; }
    }

    public class CheckByBillForCoreSysOutput
    {
        /// <summary>
        /// 含税总额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 不含税总额
        /// </summary>
        public decimal NoTaxTotalAmount { get; set; }

        /// <summary>
        /// 含税总成本
        /// </summary>
        public decimal TotalCost { get; set; }
    }

    public class CheckByBillOutput
    {
        public string BillCode { get; set; }
        public string KingdeeCode { get; set; }
        public CheckByBillForKingdeeOutput KingdeeOutput { get; set; }
        public CheckByBillForCoreSysOutput CoreSysOutput { get; set; }
        public int Match { get; set; }
        public string Desc { get; set; }
    }

    public class CheckByBillInput
    {
        public string CompanyCode { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }


        public string? BillCode { get; set; }

        /// <summary>
        /// 允许的误差
        /// </summary>
        public decimal AllowDiff { get; set; } = 5;
    }

    public class WaybillInfoDetailsInputData
    {
        public Guid? CompanyId { get; set; }
        public Guid? AgentId { get; set; }
    }
    public class WaybillInfoDetailsInput
    {
        public List<WaybillInfoDetailsInputData> WaybillInfoDetails { get; set; }
    }
    public class WaybillInfoDetailsOutput
    {
        public List<WaybillInfoDetailsOutputData>? WaybillInfoDetails { get; set; }
    }
    public class WaybillInfoDetailsOutputData
    {
        public Guid CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public Guid AgentId { get; set; }
        public string? AgentName { get; set; }
        public bool? WaybillInfoTag { get; set; }
        public List<string>? InvalidStoreInCodes { get; set; }
    }
    public class HospitalDeliveryNonPassStoreHouseValidSwitchDto
    {
        public bool FinanceIsOpen { get; set; }
    }
}

