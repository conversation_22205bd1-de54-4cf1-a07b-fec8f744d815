﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs
{
    /// <summary>
    /// 供应商
    /// </summary>
    public class AgentOutput : BaseOutput
    {
        public AgentOutput()
        {

        }
        public AgentOutput(ConditionDataOutput? source)
        {
            if (source != null)
            {
                this.Id = source.value.FirstOrDefault();
                this.Name = source.names.FirstOrDefault();
            }
            
        }
    }
    public class AgentInfoOutput: AgentOutput
    {
        /// <summary>
        /// 社会统一信用代码
        /// </summary>
        public string SocialCode { get; set; }
        /// <summary>
        /// 法人代表
        /// </summary>
        public string LegalPerson { get; set; }
        /// <summary>
        /// 供货类别
        /// </summary>
        public string AgentPropertyDesc { get; set; }
        /// <summary>
        /// 新经营范围
        /// </summary>
        public List<NewBusinessRangeOutput>? NewBusinessRanges { get; set; }
        /// <summary>
        /// 旧经营范围
        /// </summary>
        public List<OldBusinessRangeOutput>? OldBusinessRanges { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string TaxRate { get; set; }
    }
}
