﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    public class CustomizeInvoiceItemQueryInput : BaseQuery
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 付款单位id 
        /// </summary>
        public string? CustomerId { get; set; }
        public Guid? CustomizeInvoiceClassifyId { get; set; }
        /// <summary>
        /// 是否提交（推送金碟）
        /// </summary>
        public int? IsPush { get; set; }
        public CustomizeInvoiceStatusEnum? Status { get; set; }
        public string? UserName { get; set; }
        public Guid? CompanyId { get; set; }
    }

    public class CustomizeInvoiceClassifyQueryInput : BaseQuery
    {
        public Guid? CustomerId { get; set; }

        public Guid? CompanyId { get; set; }
        public CustomizeInvoiceStatusEnum? Status { get; set; }
        public CustomizeInvoiceStatusEnum? InvoiceStatus { get; set; }
        public string? searchKey { get; set; }
        public string? UserName { get; set; }
        public Guid? UserId { get; set; }
        public Guid? Id { get; set; }
        public string? CustomizeInvoiceItemCode { get; set; }
        public string? SaleSystemName { get; set; }
        /// <summary>
        ///制单人
        /// </summary>
        public List<string?>? createdBy { get; set; }
        /// <summary>
        ///制单时间开始
        /// </summary>
        public long? billDateFrom { get; set; }
        /// <summary>
        ///制单时间结束
        /// </summary>
        public long? billDateTo { get; set; }

        /// <summary>
        ///制单时间开始
        /// </summary>
        public DateTime? billDate1
        {
            get
            {
                if (billDateFrom.HasValue)
                {
                    return DateTimeHelper.LongToDateTime(billDateFrom.Value);
                }
                else
                {
                    return null;
                }
            }
        }
        /// <summary>
        ///制单时间结束
        /// </summary>
        public DateTime? billDate2
        {
            get
            {
                if (billDateTo.HasValue)
                {
                    return DateTimeHelper.LongToDateTime(billDateTo.Value);
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelationCodeOfClassify { get; set; }
        /// <summary>
        /// 红冲单号
        /// </summary>
        public string? RelationCode { get; set; }
        public string? CreditCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 是否有附件
        /// </summary>
        public int? IsAttachment { get; set; }
        /// <summary>
        /// 开票申请单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 开票类型
        /// </summary>
        public CustomizeInvoiceClassifyEnum? Classify { get; set; }
    }
    public class GenerateServiceCustomizeInvoiceInput
    {
        public Guid CompanyId { get; set; }
        public Guid ProjectId { get; set; }
        public string TaxTypeNo { get; set; }
        public decimal TaxRate { get; set; }
        public string? ProductName { get; set; }
        public string? Specification { get; set; }
        public string? Unit { get; set; }
        public string? Remark { get; set; }
        public bool IsPushCustomerEmail { get; set; }
        public string? CustomerEmail { get; set; }
    }
    public class CustomizeInvoiceClassifyInput
    {
        public CustomizeInvoiceClassifyOutput classifyOutput { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CreateBy { get; set; }

        /// <summary>
        /// 开票主体
        /// </summary>
        public customerInvoice? CustomerInvoice { get; set; }

        /// <summary>
        /// 是否推送默认（开票人）邮箱
        /// </summary>
        public bool? IsPushDefaultEmail { get; set; }

        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
    }
    public class CustomizeInvoiceDetailBatchInput
    {
        /// <summary>
        /// 运营制作开票单明细Ids
        /// </summary>
        public List<Guid> CustomizeInvoiceDetailIds { get; set; }
    }
    public class CustomizeInvoiceDetailInput
    {
        /// <summary>
        /// 运营制作开票单明细Id
        /// </summary>
        public Guid CustomizeInvoiceDetailId { get; set; }
    }
    public class CustomizeInvoiceDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }
        /// <summary>
        /// Opt=2 红冲单据
        /// </summary>
        public int? Opt { get; set; }
    }
    public class DeleteCustomizeInvoiceInput : BaseQuery
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
    public class EditCustomizeInvoiceInput : BaseQuery
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }
        /// <summary>
        /// 开票类型
        /// </summary>
        public InvoiceTypeEnum InvoiceType { get; set; }
        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 审批备注
        /// </summary>
        public string? ApproveRemark { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 蓝字红冲金额
        /// </summary>  
        public decimal? BlueRedInvoiceAmount { get; set; }

        /// <summary>
        /// 申请方 2=销方申请，1=购方申请-未抵扣，0=购方申请-已抵扣
        /// </summary>
        public int? RedOffsetOpter { get; set; }

        /// <summary>
        /// 冲红原因 销货退回=1 开票有误=2 服务中止=3 销售折让=4
        /// </summary>
        public int? RedOffsetReason { get; set; }

    }

    public class EditBatchCustomizeInvoiceInput
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public List<Guid> CustomizeInvoiceItemIds { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 审批备注
        /// </summary>
        public string? ApproveRemark { get; set; }
    }
    public class CreateOffsetInvoiceInput
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 蓝字红冲金额
        /// </summary> 
        public decimal? BlueRedInvoiceAmount { get; set; }
        /// <summary>
        /// 申请方 1=销方申请，2=购方申请-未抵扣，3=购方申请-已抵扣
        /// </summary>
        public int? RedOffsetOpter { get; set; }

        /// <summary>
        /// 冲红原因 销货退回=1 开票有误=2 服务中止=3 销售折让=4
        /// </summary>
        public int? RedOffsetReason { get; set; }

        public string? CreatedBy { get; set; }
        /// <summary>
        /// 红冲方式0=整单红冲，1=部分红冲
        /// </summary>
        public int redWay { get; set; }

        /// <summary>
        /// 部分红冲明细
        /// </summary>
        public List<CustomizeInvoiceDetailOutput>? RedDetails { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public List<string>? CreditBillCodes { get; set; }
        public int? IsNoRedConfirm { get; set; }
    }
    public class SubmitCustomizeInvoiceInput : BaseQuery
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }
        public Guid? CustomizeInvoiceClassifyId { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
    public class RecallCustomizeInvoiceInput : BaseQuery
    {
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceClassifyId { get; set; }

        /// <summary>
        /// 运营制作开票单单号
        /// </summary>
        public string CustomizeInvoiceClassifyCode { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
    public class CustomizeInvoiceAttachFileInput
    {
        public Guid CustomizeInvoiceItemId { get; set; }
        public List<Guid>? CustomizeInvoiceItemIds { get; set; }

        public string? AttachFileIds { get; set; }
        public string? AttachFileId { get; set; }

    }
    public class SaveCustomizeInvoiceDetailInput : BaseQuery
    {
        /// <summary>
        ///开票单
        /// </summary>
        public List<CustomizeInvoiceItem> ItemList { get; set; }

        /// <summary>
        ///开票单明细
        /// </summary>
        public List<CustomizeInvoiceDetail> DetailList { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
    public class SetAsAnotherInvoiceInput : BaseQuery
    {
        /// <summary>
        ///开票单
        /// </summary>
        public List<CustomizeInvoiceItem> ItemList { get; set; }

        /// <summary>
        ///开票单明细
        /// </summary>
        public List<CustomizeInvoiceDetail> DetailList { get; set; }


        /// <summary>
        ///选中的开票单明细
        /// </summary>
        public List<CustomizeInvoiceDetail>? SelDetailList { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
        /// <summary>
        /// 没有值，或者最后一个明细设置为开票单的时候 修改单头
        /// </summary>
        public bool IsBatchSetAsAnotherInvoice { get; set; } = false;
        public Guid? CustomizeInvoiceClassifyId { get; internal set; }
    }

    public class SplitSubmitInput
    {
        /// <summary>
        /// 申开单明细Id
        /// </summary>
        public Guid CustomizeInvoiceDetailId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 开票名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 类型（0=按数量拆分 1=按份数拆分）
        /// </summary>
        public int? Classify { get; set; }
    }

    /// <summary>
    /// 设置为另一个开票分类入参
    /// </summary>
    public class SetAnotherCicInput
    {
        /// <summary>
        /// 开票申请单集合
        /// </summary>
        public List<CustomizeInvoiceItemOutput>? List { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
    }
}
