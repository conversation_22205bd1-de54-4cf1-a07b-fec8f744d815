﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Refund
{
    /// <summary>
    /// 退款单状态回写入参
    /// </summary>
    public class RefundStateSynchronizationInput
    {
        /// <summary>
        /// 退款单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RefundStatusEnum Status { get; set; }
    }
}
