﻿﻿using Inno.CorePlatform.Common.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills
{
    /// <summary>
    /// 带合计的分页数据
    /// </summary>
    /// <typeparam name="T">列表项类型</typeparam>
    public class PagedDataWithSummary<T> : BasePagedData<T>
    {
        /// <summary>
        /// 合计数据
        /// </summary>
        public SummaryData Summary { get; set; }
    }
}
