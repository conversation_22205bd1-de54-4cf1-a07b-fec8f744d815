﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    /// <summary>
    /// 批量付款详情
    /// </summary>
    public class PaymentDetailOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int? AccountPeriodType { get; set; }
        /// <summary>
        /// 账期类型描述
        /// </summary>
        public string? AccountPeriodDescription { get { return this.AccountPeriodType != null ? ((AccountPeriodTypeEnum)this.AccountPeriodType).GetDescription() : ""; } }
        /// <summary>
        /// 业务单元
        /// </summary>

        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 应收冲销金额
        /// </summary>
        public decimal? CreditAbatedValue { get; set; }
        /// <summary>
        /// 应付明细Id
        /// </summary>
        public Guid? DebtDetailId { get; set; }
        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillCode { get; set; }
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal? DebtValue { get; set; }
        /// <summary>
        /// 应付冲销金额
        /// </summary>
        public decimal? DebtAbatedValue { get; set; }
        /// <summary>
        /// 本次付款金额
        /// </summary>
        public decimal DebtDetailValue { get; set; }
        /// <summary>
        /// 应付余额
        /// </summary>
        public decimal? DebtBalance { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal? Discount { get; set; }
        /// <summary>
        /// 折前金额
        /// </summary>
        public decimal? OriginValue { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }
        /// <summary>
        /// 收款日期
        /// </summary>
        public DateTime? ReceiveDate { get; set; }
        /// <summary>
        /// 付款单位
        /// </summary>
        public string? Payer { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal? ReceiveAmount { get; set; }
        /// <summary>
        /// 医院名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 医院Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinName { get; set; }
        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 采购合同单号
        /// </summary>
        public string? PurchaseContactNo { get; set; }
        public string? PaymentCode { get; set; }
        public string? OrderNo { get; set; }
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 终端医院名称
        /// </summary>
        public string? HospitalName { get; set; }
        public Guid CreditId { get; internal set; }
        /// <summary>
        /// 发票日期
        /// </summary>
        public string? InvoiceTime { get; set; }

        /// <summary>
        ///采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }
        /// <summary>
        /// 进项票金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }

        /// <summary>
        /// 进项发票号
        /// </summary>
        public string? InvoiceCodes { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        public DateTime? ProbablyPayTime { get; internal set; }


        /// <summary>
        /// 发票列表
        /// </summary>
        public List<InvoiceInfo>? InvoiceInfoLst { get; set; }

        /// <summary>
        /// 现金折扣金额
        /// </summary>
        public decimal? LimitedDiscount { get; set; }


        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
    }

    /// <summary>
    /// 导出发票信息
    /// </summary>
    public class InvoiceInfo
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 发票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }
        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal? WriteOffAmount { get; set; }
        /// <summary>
        /// 冲销日期
        /// </summary>
        public DateTime? Abtdate { get; set; }
        /// <summary>
        /// 收款认领时间
        /// </summary>
        public string? RecognizeDate { get; set; }
        /// <summary>
        /// 应收单金额
        /// </summary>
        public List<decimal>? CreditValueLst { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string? Currency { get; set; }
    }
    /// <summary>
    /// 批量付款详情——按供应商聚合
    /// </summary>
    public class PaymentAggratByAgent
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? Sum { get; set; }
        /// <summary>
        /// 批量付款单Id
        /// </summary>
        public Guid PaymentAutoItemId { get; set; }
        public List<AgentBank> BankInfo { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        public string? PayClassify { get; set; }

        /// <summary>
        /// 附件Ids
        /// </summary> 
        public string? AttachFileIds { get; set; }
    }

    public class AgentBank
    {
        public Guid PaymentAutoItemId { get; set; }
        public Guid AgentId { get; set; }
        public string Account { get; set; }
        public string Bank { get; set; }
        public string BankCode { get; set; }
        public string BankNo { get; set; }
        public string? AgentName { get; set; }
        public string BankName { get { return Bank; } }
        public string AccountName { get { return Account; } }
        public int IsSelected { get; set; }
        public string? PayClassify { get; set; }
        public string? TransferDiscourse { get; set; }
        public string? RegisterAddressName { get; set; }
        public int? type { get; set; }
        #region 境外供应商支付
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        public string? Contractno { get; set; }

        /// <summary>
        /// 境外/跨境支付0:否,1:是
        /// </summary>
        public int? Paymentabroad { get; set; }

        /// <summary>
        /// 交易编码
        /// </summary>
        public string? Transactioncoding { get; set; }

        /// <summary>
        /// 交易附言
        /// </summary>
        public string? Postscript { get; set; }

        /// <summary>
        /// 本笔款项是否为保税货物下推0:否,1:是
        /// </summary>
        public int? Ynpush { get; set; }

        /// <summary>
        /// 国内外费用承担方A:汇款人,B:收款人,C:共同人
        /// </summary>
        public string? CostBearingParty { get; set; }
        /// <summary>
        /// 国内外费用承担方A:汇款人,B:收款人,C:共同人
        /// </summary>
        public string? CostBearingPartyStr
        {
            get
            {
                var ret = "";
                if (!string.IsNullOrEmpty(CostBearingParty))
                {
                    if (CostBearingParty.ToLower() == "a")
                    {
                        ret = "汇款人OUR";
                    }
                    else if (CostBearingParty.ToLower() == "b")
                    {
                        ret = "收款人BEN";
                    }
                    else if (CostBearingParty.ToLower() == "c")
                    {
                        ret = "共同人SHA";
                    }

                }
                return ret;
            }
        }
        /// <summary>
        /// 海关进口货物报关商品
        /// </summary>
        public string? importGoods { get; set; }

        #endregion
    }

    public class AgentBankInput
    {
        public Guid PaymentAutoItemId { get; set; }
        public Guid AgentId { get; set; }
        public string? AgentName { get; set; }

        public string? AccountName { get; set; }

        public string? Account { get; set; }
        public string? Bank { get; set; }

        public string? BankName { get; set; }
        public string? BankCode { get; set; }
        public string? PayClassify { get; set; }

        public string? InvoiceNo { get; set; }
        public string? Contractno { get; set; }
        public int? Paymentabroad { get; set; }
        public string? Transactioncoding { get; set; }
        public string? Postscript { get; set; }
        public int? Ynpush { get; set; }
        public string? CostBearingParty { get; set; }
        public string? importGoods { get; set; }
        public decimal? Amount { get; set; }
        public string? TransferDiscourse { get; set; }
        public int? Type { get; set; }
    }

    /// <summary>
    /// 不同状态数量
    /// </summary>
    public class BulkPaymentTabOutput
    {
        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 临时草稿数量 0
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 审批中数量
        /// </summary>
        public int AuditingCount { get; set; }
        /// <summary>
        /// 待执行数量 2
        /// </summary>
        public int WaitExecuteCount { get; set; }
        /// <summary>
        /// 已完成数量 99
        /// </summary>
        public int CompletedCount { get; set; }
        /// <summary>
        /// 我的审批 5000
        /// </summary>
        public int MyAuditCount { get; set; }
    }

    /// <summary>
    /// 选择组件返回Dto
    /// </summary>
    public class SelectAssemblyOutput
    {
        public string? id { get; set; }
        public string? name { get; set; }
    }

    public class PaymentAutoDetailOfPurchaseOutput
    {
        /// <summary>
        ///批量 付款单号
        /// </summary>
        public string PaymentCode { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }


        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 应付单号
        /// </summary>
        public string? DebtBillCode { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        public string? CoinName { get; set; }
        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal DebtValue { get; set; }
        /// <summary>
        /// 应付冲销金额
        /// </summary>
        public decimal? DebtAbatedValue { get; set; }
        /// <summary>
        /// 本次付款金额
        /// </summary>
        public decimal DebtDetailValue { get; set; }
        /// <summary>
        /// 应付余额
        /// </summary>
        public decimal? DebtBalance { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public decimal? Discount { get; set; }
        /// <summary>
        /// 折前金额
        /// </summary>
        public decimal? OriginValue { get; set; }

        /// <summary>
        /// 账期类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int? AccountPeriodType { get; set; }
        /// <summary>
        /// 账期类型描述
        /// </summary>
        public string? AccountPeriodDescription { get { return this.AccountPeriodType != null ? ((AccountPeriodTypeEnum)this.AccountPeriodType).GetDescription() : ""; } }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }
        /// <summary>
        /// 状态
        /// </summary> 
        public PaymentAutoItemStatusEnum Status { get; set; }

        /// <summary>
        /// 状态
        /// </summary> 
        public string StatusName
        {
            get
            {
                return this.Status.GetDescription();
            }
        }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; internal set; }
    }

    public class PaymentAutoDetailOfPurchaseInput
    {
        public List<string> Codes { get; set; }
    }
}
