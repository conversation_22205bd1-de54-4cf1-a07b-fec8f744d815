﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 寄售垫资服务
    /// </summary>
    public interface IAdvanceBusinessService
    {
        Task CreateAdvanceAsync(List<Guid> creditIds);
        /// <summary>
        /// 订阅垫资单消息
        /// </summary>
        /// <param name="advanceBusinessApplies"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> AdvanceSubAsync(List<AdvanceBusinessApply> advanceBusinessApplies);
    }
}
