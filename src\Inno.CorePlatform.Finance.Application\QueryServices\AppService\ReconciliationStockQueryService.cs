﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class ReconciliationStockQueryService : IReconciliationStockQueryService
    {
        private readonly FinanceDbContext _db;
        public readonly List<string> StockTypes = new List<string> { "发出商品(寄售)", "发出商品(经销+进口经销)", "库存商品(寄售)", "库存商品(经销)", "库存商品(进口)" };
        public ReconciliationStockQueryService(FinanceDbContext db)
        {
            _db = db;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListPages(ReconciliationItemInput input)
        {
            try
            {
                var query = _db.ReconciliationStockDetail.AsNoTracking();
                //分页
                var list = await query.Where(p => p.ReconciliationItemId == input.ReconciliationItemId&& StockTypes.Contains(p.BillTypeStr)).GroupBy(p => p.ReconciliationItemId).Select(z => new ReconciliationOutput
                {
                    Cost = query.Sum(z => z.Cost),
                    CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                    Income = z.Sum(z => z.Income),
                    IncomeOfNoTax = z.Sum(z => z.IncomeOfNoTax),
                    KisData = 2
                }).ToListAsync();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 根据存货供应商和客户进行分组
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListByAgent(ReconciliationItemInput input)
        {
            try
            {
                var query = _db.ReconciliationStockDetail.AsNoTracking();
                //分页
                var list = await query.OrderByDefault(input.sort).Where(x => x.ReconciliationItemId == input.ReconciliationItemId && StockTypes.Contains(x.BillTypeStr)).GroupBy(x => new { x.AgentId, x.CustomerId }).Select(z => new ReconciliationOutput
                {
                    IncomeOfNoTax = z.Sum(z => z.IncomeOfNoTax),
                    CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                    Income = z.Sum(z => z.Income),
                    Cost = z.Sum(z => z.Cost),
                    AgentId = z.Key.AgentId,
                    CustomerId = z.Key.CustomerId,
                    KisData = 2
                }).ToListAsync();
                list = list.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 根据客户进行分组
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListByCustomer(ReconciliationItemInput input)
        {
            try
            {
                var query = _db.ReconciliationStockDetail.AsNoTracking();
                //分页
                var list = await query.OrderByDefault(input.sort).Where(x => x.ReconciliationItemId == input.ReconciliationItemId && x.AgentId == input.AgentId && StockTypes.Contains(x.BillTypeStr)).GroupBy(x => new { x.AgentId, x.CustomerId, x.SaleOrderNo }).Select(z => new ReconciliationOutput
                {
                    IncomeOfNoTax = z.Sum(z => z.IncomeOfNoTax),
                    CostOfNoTax = z.Sum(z => z.CostOfNoTax),
                    Income = z.Sum(z => z.Income),
                    Cost = z.Sum(z => z.Cost),
                    AgentId = z.Key.AgentId,
                    CustomerId = z.Key.CustomerId,
                    SaleOrderNo = z.Key.SaleOrderNo,
                    KisData = 2
                }).ToListAsync();
                list = list.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        #region 导出查询
        /// <summary>
        /// 按科目
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfCompanyExport(ReconciliationItemExportInput input)
        {
            try
            {
                //分页
                var list = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && StockTypes.Contains(p.BillTypeStr))
                    .GroupBy(p => new { p.ReconciliationItemId, p.BillTypeStr })
                    .Select(g => new ReconciliationOutput
                    {
                        BillTypeStr = g.Key.BillTypeStr,
                        Cost = g.Sum(z => z.Cost),
                        CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                        Income = g.Sum(z => z.Income),
                        IncomeOfNoTax = g.Sum(z => z.IncomeOfNoTax),
                        KisData = 2
                    }).ToListAsync();
                list = list.Where(p => p.CostOfNoTax > 0).ToList();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 按科目
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfAgentCustomerExport(ReconciliationItemExportInput input)
        {
            try
            {
                //分页
                var list = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && p.BillTypeStr == input.BillTypeStr && StockTypes.Contains(p.BillTypeStr))
                    .GroupBy(p => new { p.ReconciliationItemId, p.AgentId, p.AgentName, p.CustomerId, p.CustomerName })
                    .Select(g => new ReconciliationOutput
                    {
                        AgentName = g.Key.AgentName,
                        CustomerName = g.Key.CustomerName,
                        Cost = g.Sum(z => z.Cost),
                        CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                        Income = g.Sum(z => z.Income),
                        IncomeOfNoTax = g.Sum(z => z.IncomeOfNoTax),
                        KisData = 2
                    }).OrderBy(p => p.CustomerName).ThenBy(p => p.AgentName).ToListAsync();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        /// <summary>
        /// 按科目
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfAgentExport(ReconciliationItemExportInput input)
        {
            try
            {
                //分页
                var list = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && p.BillTypeStr == input.BillTypeStr && StockTypes.Contains(p.BillTypeStr))
                    .GroupBy(p => new { p.ReconciliationItemId, p.AgentId, p.AgentName })
                    .Select(g => new ReconciliationOutput
                    {
                        AgentName = g.Key.AgentName,
                        Cost = g.Sum(z => z.Cost),
                        CostOfNoTax = g.Sum(z => z.CostOfNoTax),
                        Income = g.Sum(z => z.Income),
                        IncomeOfNoTax = g.Sum(z => z.IncomeOfNoTax),
                        KisData = 2
                    }).OrderBy(p => p.AgentName).ToListAsync();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 按发生额
        /// </summary>
        /// <returns></returns>
        public async Task<PageResponse<ReconciliationOutput>> GetListOfChangeAmountExport(ReconciliationItemExportInput input)
        {
            try
            {
                //分页
                var list = await _db.ReconciliationStockDetail.Where(p => p.ReconciliationItemId == input.ReconciliationItemId && p.ChangeAmount.HasValue && p.BillTypeStr != "库存商品(进口)")
                    .GroupBy(p => new { p.ReconciliationItemId, p.AgentId, p.AgentName, p.CustomerId, p.CustomerName, p.BillTypeStr, p.SaleOrderNo, p.OrderNo, p.Mark })
                    .Select(g => new ReconciliationOutput
                    {
                        AgentName = g.Key.AgentName,
                        CustomerName = g.Key.CustomerName,
                        BillTypeStr = g.Key.BillTypeStr,
                        SaleOrderNo = g.Key.SaleOrderNo,
                        OrderNo = g.Key.OrderNo,
                        Mark = g.Key.Mark,
                        ChangeAmount = g.Sum(z => z.ChangeAmount),
                        StandardUnitCost = g.Sum(z => z.StandardUnitCost),
                    }).OrderBy(p => p.BillTypeStr).ThenBy(p => p.SaleOrderNo).ThenBy(p => p.AgentName).AsNoTracking().ToListAsync();
                var count = list.Count;
                return new PageResponse<ReconciliationOutput>() { List = list, Total = count };
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }
        #endregion
    }
}
