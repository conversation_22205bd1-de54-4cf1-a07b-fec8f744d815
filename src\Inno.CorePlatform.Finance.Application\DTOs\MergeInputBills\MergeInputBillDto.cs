﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.Enums;

namespace Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills
{
    /// <summary>
    /// 创建合并进项发票请求
    /// </summary>
    public class CreateMergeInputBillRequest
    {
        /// <summary>
        /// 原始进项发票ID列表
        /// </summary>
        public List<Guid> InputBillIds { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 开始日期 - 对应发票的开票时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期 - 对应发票的开票时间
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// 创建合并进项发票响应
    /// </summary>
    public class CreateMergeInputBillResponse
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项票单号
        /// </summary>
        public string MergeInvoiceNumber { get; set; }
    }

    /// <summary>
    /// 查询合并进项发票列表请求
    /// </summary>
    public class QueryMergeInputBillListRequest
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商ID列表（前端传入）
        /// </summary>
        public List<Guid>? AgentIds { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public MergeInputBillStatusEnum? Status { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; } = 10;

        /// <summary>
        /// 发票号（查询原始进项票）
        /// </summary>
        public string? InvoiceNumber { get; set; }

        /// <summary>
        /// 合并进项票单号
        /// </summary>
        public string? MergeInvoiceNumber { get; set; }

        /// <summary>
        /// 票据类型
        /// </summary>
        public InputBillTypeEnum? Type { get; set; }

        /// <summary>
        /// 合并时间开始
        /// </summary>
        public DateTime? MergeTimeStart { get; set; }

        /// <summary>
        /// 合并时间结束
        /// </summary>
        public DateTime? MergeTimeEnd { get; set; }

        /// <summary>
        /// 开票时间开始 - 对应发票的开票时间（前端传入，仅用于列表查询条件）
        /// </summary>
        public DateTime? BeginCreatedTime { get; set; }

        /// <summary>
        /// 开票时间结束 - 对应发票的开票时间（前端传入，仅用于列表查询条件）
        /// </summary>
        public DateTime? EndCreatedTime { get; set; }

        /// <summary>
        /// 提交时间开始（前端传入，仅用于列表查询条件）
        /// </summary>
        public DateTime? SubmitTimeStart { get; set; }

        /// <summary>
        /// 提交时间结束（前端传入，仅用于列表查询条件）
        /// </summary>
        public DateTime? SubmitTimeEnd { get; set; }

        /// <summary>
        /// 业务单号（查询合并进项票提交明细）
        /// </summary>
        public string? BusinessItemCode { get; set; }

        /// <summary>
        /// 业务单号（前端传入）
        /// </summary>
        public string? StoreInItemCode { get; set; }

        /// <summary>
        /// 货号（查询合并进项票明细或提交明细）
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 型号（查询合并进项票明细或提交明细）
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 是否已有发票明细 0=全部 1=是 2=否
        /// </summary>
        public int? HasDetail { get; set; }

        /// <summary>
        /// 创建人（单个筛选）
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 创建人列表（多选筛选）
        /// </summary>
        public List<string>? CreatedByList { get; set; }

        /// <summary>
        /// 取消勾稽状态名称 (true=是, false=否)
        /// </summary>
        public bool? cancelReconciliationStatusName { get; set; }

        /// <summary>
        /// 取消勾稽时间开始
        /// </summary>
        public DateTime? beginCancelReconciliationTime { get; set; }

        /// <summary>
        /// 取消勾稽时间结束
        /// </summary>
        public DateTime? endCancelReconciliationTime { get; set; }
    }

    /// <summary>
    /// 合并进项发票列表项
    /// </summary>
    public class MergeInputBillListItem
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 合并发票号
        /// </summary>
        public string MergeInvoiceNumber { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 合并时间
        /// </summary>
        public DateTime MergeTime { get; set; }

        /// <summary>
        /// 票据类型 1=普票，2=专票
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 票据类型描述
        /// </summary>
        public string? TypeDesc { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }

        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 状态 1=临时，2=已提交，3=正在匹配，4=匹配完成，9=忽略
        /// </summary>
        public MergeInputBillStatusEnum Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDesc { get; set; }

        /// <summary>
        /// 取消勾稽时间
        /// </summary>
        public DateTime? CancelReconciliationTime { get; set; }

        /// <summary>
        /// 是否已取消勾稽
        /// </summary>
        public bool? IsCancelledReconciliation { get; set; }

        /// <summary>
        /// 取消勾稽状态名称
        /// </summary>
        public string? CancelReconciliationStatusName
        {
            get
            {
                return IsCancelledReconciliation.HasValue ? (IsCancelledReconciliation.Value ? "是" : "否") : "";
            }
        }

        /// <summary>
        /// 原始进项发票数量
        /// </summary>
        public int OriginalInputBillCount { get; set; }

        /// <summary>
        /// 原始进项发票列表
        /// </summary>
        public List<MergeInputBillListOriginalItem>? OriginalInputBills { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }
    }

    /// <summary>
    /// 合并进项发票列表中的原始进项发票项
    /// </summary>
    public class MergeInputBillListOriginalItem
    {
        /// <summary>
        /// 进项发票ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime BillTime { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InputBillStatusEnum Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDesc => Status.GetDescription();
        /// <summary>
        /// 票据类型
        /// </summary>
        public InputBillTypeEnum Type { get; set; }

        /// <summary>
        /// 票据类型描述
        /// </summary>
        public string TypeDesc => Type.GetDescription();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyName { get;  set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string AgentName { get;  set; }
        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }
        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 取消勾稽时间
        /// </summary>
        public DateTime? CancelReconciliationTime { get; set; }

        /// <summary>
        /// 是否已取消勾稽
        /// </summary>
        public bool? IsCancelledReconciliation { get; set; }

        /// <summary>
        /// 取消勾稽状态名称
        /// </summary>
        public string? CancelReconciliationStatusName
        {
            get
            {
                return IsCancelledReconciliation.HasValue ? (IsCancelledReconciliation.Value ? "是" : "否") : "";
            }
        }
    }

    /// <summary>
    /// 查询合并进项发票列表响应
    /// </summary>
    public class QueryMergeInputBillListResponse
    {
        /// <summary>
        /// 合并进项发票列表
        /// </summary>
        public List<MergeInputBillListItem> Items { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; }
    }

    /// <summary>
    /// 查询合并进项发票详情请求
    /// </summary>
    public class QueryMergeInputBillDetailRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 合并进项发票明细项
    /// </summary>
    public class MergeInputBillDetailItem
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 含税金额（计算属性：TaxCost * Quantity）
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 对应发票号列表
        /// </summary>
        public List<string> InvoiceNumbers { get; set; } = new List<string>();

        /// <summary>
        /// 匹配成功金额
        /// </summary>
        public decimal MatchedAmount { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }

        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }
    }

    /// <summary>
    /// 原始进项发票项
    /// </summary>
    public class OriginalInputBillItem
    {
        /// <summary>
        /// 进项发票ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanName { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime BillTime { get; set; }

        /// <summary>
        /// 票据类型
        /// </summary>
        public InputBillTypeEnum Type { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InputBillStatusEnum Status { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string PurchaseDutyNumber { get; set; }

        /// <summary>
        /// 销售方税号
        /// </summary>
        public string SaleDutyNumber { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 明细汇总信息
    /// </summary>
    public class FooterResult
    {
        /// <summary>
        /// 合并进项票明细 - 数量合计
        /// </summary>
        public decimal TotalDetailQuantity { get; set; }

        /// <summary>
        /// 合并进项票明细 - 不含税金额合计
        /// </summary>
        public decimal TotalDetailNoTaxAmount { get; set; }

        /// <summary>
        /// 合并进项票明细 - 税额合计
        /// </summary>
        public decimal TotalDetailTaxAmount { get; set; }

        /// <summary>
        /// 合并进项票明细 - 含税金额合计
        /// </summary>
        public decimal TotalDetailTotalAmount { get; set; }

        /// <summary>
        /// 待提交明细 - 本次入票数量合计
        /// </summary>
        public decimal TotalMatchQuantity { get; set; }

        /// <summary>
        /// 待提交明细 - 金额合计（不含税金额）
        /// </summary>
        public decimal TotalNoTaxAmount { get; set; }

        /// <summary>
        /// 待提交明细 - 税额合计
        /// </summary>
        public decimal TotalTaxAmount { get; set; }

        /// <summary>
        /// 待提交明细 - 含税金额合计
        /// </summary>
        public decimal TotalTotalAmount { get; set; }

        /// <summary>
        /// 待提交明细 - 匹配金额合计
        /// </summary>
        public decimal TotalMatchedAmount { get; set; }
    }

    /// <summary>
    /// 查询合并进项发票详情响应
    /// </summary>
    public class QueryMergeInputBillDetailResponse
    {
        /// <summary>
        /// 合并进项发票明细
        /// </summary>
        public List<MergeInputBillDetailItem> MergeInputBillDetails { get; set; } = new List<MergeInputBillDetailItem>();

        /// <summary>
        /// 待提交的明细
        /// </summary>
        public List<SubmitDetailItem> SubmitDetails { get; set; } = new List<SubmitDetailItem>();

        /// <summary>
        /// 汇总信息
        /// </summary>
        public FooterResult Footer { get; set; } = new FooterResult();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }
    }

    /// <summary>
    /// 还原合并进项发票请求
    /// </summary>
    public class RestoreMergeInputBillRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 获取可勾稽单据列表请求
    /// </summary>
    public class GetMatchableDocumentsRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票明细ID
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 开始日期 - 对应发票的开票时间（用于自动匹配，带时区信息）
        /// </summary>
        public DateTimeOffset? StartDate { get; set; }

        /// <summary>
        /// 结束日期 - 对应发票的开票时间（用于自动匹配，带时区信息）
        /// </summary>
        public DateTimeOffset? EndDate { get; set; }

        /// <summary>
        /// 业务类型接口更新状态字典，记录每种业务类型是否已经请求过接口数据
        /// </summary>
        public Dictionary<BusinessType, bool> InterfaceUpdateStatus { get; set; } = new Dictionary<BusinessType, bool>();


        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 匹配类型
        /// </summary>
        public int? MatchType { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public BusinessType? BusinessType { get; set; }



        /// <summary>
        /// 匹配精度（品名/货号）
        /// </summary>
        public MatchPrecisionEnum MatchPrecision { get; set; } = MatchPrecisionEnum.ProductName;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 开票明细列表
        /// </summary>
        public List<InvoiceDetailItem>? InvoiceDetails { get; set; }
    }

    /// <summary>
    /// 开票明细项
    /// </summary>
    public class InvoiceDetailItem
    {
        /// <summary>
        /// 发票明细ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 开票名称（格式为 *医疗器械/其它*品名）
        /// </summary>
        public string InvoiceName { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 规格（对应的是货号，可空）
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 开票数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
    }

    /// <summary>
    /// 可勾稽单据项
    /// </summary>
    public class MatchableDocumentItem
    {
        /// <summary>
        /// 唯一标识符，用于快速匹配
        /// </summary>
        public string MatchKey { get; set; }

        /// <summary>
        /// 默认构造函数，用于JSON反序列化
        /// </summary>
        public MatchableDocumentItem()
        {
            // 不在默认构造函数中生成MatchKey，避免反序列化时重新生成
        }
        /// <summary>
        /// 带参构造函数
        /// </summary>
        /// <param name="businessType">业务类型</param>
        /// <param name="businessCode">业务单号</param>
        /// <param name="businessDate">业务日期</param>
        /// <param name="productName">品名</param>
        /// <param name="productNo">货号</param>
        /// <param name="quantity">数量</param>
        /// <param name="taxCost">含税单价</param>
        /// <param name="taxRate">税率</param>
        /// <param name="invoicedQuantity">已入票数量</param>
        /// <param name="productId">货号ID</param>
        /// <param name="productNameId">品名ID</param>
        public MatchableDocumentItem(BusinessType businessType, string businessItemCode, DateTime businessDate,
                                   string productName, string productNo, decimal quantity,
                                   decimal taxCost, decimal taxRate, decimal invoicedQuantity = 0,
                                   Guid? productId = null, Guid? productNameId = null)
        {
            BusinessType = businessType;
            BusinessItemCode = businessItemCode;
            BusinessCode = businessItemCode; // 为了兼容性，设置为相同的值
            BusinessDate = businessDate;
            ProductName = productName;
            ProductNo = productNo;

            // 对于购货修订和服务费，设置数量为1，这样金额就等于单价
            if (businessType == BusinessType.PurchaseRevision || businessType == BusinessType.ServiceFeeProcurement)
            {
                Quantity = 1;
                // taxCost已经是金额，不需要再乘以数量
            }
            else
            {
                Quantity = quantity;
            }

            ProductId = productId;
            ProductNameId = productNameId;

            // 设置税率和含税单价
            TaxRate = taxRate;
            TaxCost = taxCost;

            // 计算不含税单价（使用10位小数精度）
            NoTaxCost = taxRate > 0 ? Math.Round(taxCost / (1 + taxRate / 100), 10) : taxCost;

            InvoicedQuantity = invoicedQuantity;
            MatchQuantity = 0; // 初始已匹配数量为0
            CurrentMatchQuantity = 0; // 初始本次匹配数量为0

            // 生成唯一标识符，结合业务单号、业务明细单号、品名/货号、含税单价和税率
            GenerateMatchKey();
        }
        /// <summary>
        /// 业务类型
        /// </summary>
        public BusinessType BusinessType { get; set; }

        /// <summary>
        /// 业务类型描述
        /// </summary>
        public string BusinessTypeDescription
        {
            get
            {
                return BusinessType.GetDescription();
            }
        }

        /// <summary>
        /// 是否为特殊业务类型（服务费或购货修订）
        /// </summary>
        public bool IsSpecialBusinessType => BusinessType == BusinessType.ServiceFeeProcurement ||
                                           BusinessType == BusinessType.PurchaseRevision;

        /// <summary>
        /// 业务单号（已弃用，请使用BusinessItemCode）
        /// </summary>
        [Obsolete("请使用BusinessItemCode替代")]
        public string BusinessCode { get; set; }

        /// <summary>
        /// 业务明细单号（主要的排序和匹配依据）
        /// </summary>
        public string BusinessItemCode { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public DateTime BusinessDate { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount => TaxCost * Quantity;

        /// <summary>
        /// 含税金额
        /// </summary>
        public decimal TotalAmount => TaxCost * Quantity;

        /// <summary>
        /// 已入票数量
        /// </summary>
        public decimal InvoicedQuantity { get; set; }

        /// <summary>
        /// 可入票数量
        /// </summary>
        public decimal AvailableQuantity => Quantity - InvoicedQuantity;

        /// <summary>
        /// 已匹配数量（历史累计匹配数量）
        /// 注意：对于服务费业务类型（BusinessType.ServiceFeeProcurement），此字段表示已匹配金额而不是数量
        /// </summary>
        public decimal MatchQuantity { get; set; }

        /// <summary>
        /// 本次匹配数量（当前操作的匹配数量）
        /// 注意：对于服务费业务类型（BusinessType.ServiceFeeProcurement），此字段表示本次匹配金额而不是数量
        /// </summary>
        public decimal CurrentMatchQuantity { get; set; }

        /// <summary>
        /// 剩余可匹配数量
        /// 注意：对于服务费业务类型（BusinessType.ServiceFeeProcurement），此属性表示剩余可匹配金额而不是数量
        /// </summary>
        public decimal RemainingQuantity => AvailableQuantity - MatchQuantity;

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        public decimal TaxAmount => TaxCost * Quantity;

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal NoTaxAmount => NoTaxCost * Quantity;

        /// <summary>
        /// 业务金额（对于购货修订和服务费和损失确认，直接使用TaxCost；对于其他业务类型，使用TaxCost * MatchQuantity）
        /// </summary>
        public decimal BusinessAmount
        {
            get
            {
                // 对于购货修订、服务费和损失确认，直接返回TaxCost
                if (BusinessType == BusinessType.PurchaseRevision || BusinessType == BusinessType.ServiceFeeProcurement || BusinessType == BusinessType.LossRecognition)
                {
                    return TaxCost;
                }

                // 对于其他业务类型，返回TaxCost * MatchQuantity
                return MatchQuantity != 0 ? Math.Round(TaxCost * Math.Abs(MatchQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 不含税业务金额
        /// </summary>
        public decimal NoTaxBusinessAmount
        {
            get
            {
                // 对于购货修订、服务费和损失确认，直接返回NoTaxCost
                if (BusinessType == BusinessType.PurchaseRevision || BusinessType == BusinessType.ServiceFeeProcurement || BusinessType == BusinessType.LossRecognition)
                {
                    return NoTaxCost;
                }

                // 对于其他业务类型，返回NoTaxCost * MatchQuantity
                return MatchQuantity != 0 ? Math.Round(NoTaxCost * Math.Abs(MatchQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 已入票金额
        /// </summary>
        public decimal InvoicedAmount
        {
            get
            {
                // 对于购货修订，直接返回InvoicedQuantity
                if (BusinessType == BusinessType.PurchaseRevision)
                {
                    return InvoicedQuantity;
                }

                // 对于服务费，返回MatchQuantity（用户手动匹配时输入的金额）
                if (BusinessType == BusinessType.ServiceFeeProcurement)
                {
                    return MatchQuantity;
                }

                // 对于其他业务类型，返回TaxCost * InvoicedQuantity
                return InvoicedQuantity != 0 ? Math.Round(TaxCost * Math.Abs(InvoicedQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 本次入票金额
        /// </summary>
        public decimal CurrentMatchAmount
        {
            get
            {
                // 对于购货修订和服务费，直接返回CurrentMatchQuantity
                if (BusinessType == BusinessType.PurchaseRevision || BusinessType == BusinessType.ServiceFeeProcurement)
                {
                    return CurrentMatchQuantity;
                }

                // 对于其他业务类型，返回TaxCost * CurrentMatchQuantity
                return CurrentMatchQuantity != 0 ? Math.Round(TaxCost * Math.Abs(CurrentMatchQuantity), 4) : 0;
            }
        }

        /// <summary>
        /// 匹配金额 - 统一计算所有业务类型的匹配金额
        /// 对于普通业务类型：匹配金额 = 匹配数量 × 含税单价
        /// 对于特殊业务类型（服务费、购货修订）：匹配金额 = 匹配数量（实际上是金额）
        /// </summary>
        public decimal MatchedAmount => IsSpecialBusinessType
            ? MatchQuantity
            : MatchQuantity * TaxCost;

        /// <summary>
        /// 本次匹配金额 - 统一计算所有业务类型的本次匹配金额
        /// </summary>
        public decimal CurrentMatchedAmount => IsSpecialBusinessType
            ? CurrentMatchQuantity
            : CurrentMatchQuantity * TaxCost;

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 合并进项发票明细ID
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        public string? ContractNo { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 厂家ID
        /// </summary>
        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 匹配精度
        /// </summary>
        public MatchPrecisionEnum MatchPrecision { get; set; } = MatchPrecisionEnum.ProductName;

        /// <summary>
        /// 生成匹配键，使用GUID格式
        /// </summary>
        private void GenerateMatchKey()
        {
            // 只有当MatchKey为空时才生成新的MatchKey
            if (string.IsNullOrEmpty(MatchKey))
            {
                // 使用GUID作为匹配键，更高效且避免冲突
                MatchKey = Guid.NewGuid().ToString();
            }
        }

        /// <summary>
        /// 更新匹配数量
        /// 注意：对于服务费业务类型（BusinessType.ServiceFeeProcurement），此方法更新的是匹配金额而不是数量
        /// </summary>
        /// <param name="newMatchQuantity">新的匹配数量（对于服务费，表示新的匹配金额）</param>
        public void UpdateMatchQuantity(decimal newMatchQuantity)
        {
            MatchQuantity = newMatchQuantity;
            // 不再自动更新 CurrentMatchQuantity，保持其独立性
        }

        /// <summary>
        /// 更新本次匹配数量
        /// 注意：对于服务费业务类型（BusinessType.ServiceFeeProcurement），此方法更新的是本次匹配金额而不是数量
        /// </summary>
        /// <param name="newCurrentMatchQuantity">新的本次匹配数量（对于服务费，表示新的本次匹配金额）</param>
        public void UpdateCurrentMatchQuantity(decimal newCurrentMatchQuantity)
        {
            CurrentMatchQuantity = newCurrentMatchQuantity;
        }


    }

    /// <summary>
    /// 获取可勾稽单据列表响应
    /// </summary>
    public class GetMatchableDocumentsResponse
    {
        /// <summary>
        /// 可勾稽单据列表
        /// </summary>
        public List<MatchableDocumentItem> Items { get; set; }
    }

    /// <summary>
    /// 保存勾稽结果请求
    /// </summary>
    public class SaveMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 匹配明细
        /// </summary>
        public List<MatchDetailItem> MatchDetails { get; set; }
    }

    /// <summary>
    /// 匹配明细项
    /// </summary>
    public class MatchDetailItem
    {
        public decimal InvoicedQuantity { get; set; }

        /// <summary>
        /// 合并进项发票明细ID
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public BusinessType BusinessType { get; set; }

        /// <summary>
        /// 业务类型描述
        /// </summary>
        public string BusinessTypeDescription
        {
            get
            {
                return BusinessType.GetDescription();
            }
        }

        /// <summary>
        /// 业务单号（已弃用，请使用BusinessItemCode）
        /// </summary>
        [Obsolete("请使用BusinessItemCode替代")]
        public string BusinessCode { get; set; }

        /// <summary>
        /// 业务明细单号（主要的排序和匹配依据）
        /// </summary>
        public string BusinessItemCode { get; set; }

        /// <summary>
        /// 业务日期
        /// </summary>
        public DateTime BusinessDate { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 可入票数量
        /// </summary>
        public decimal AvailableQuantity { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 已匹配数量（历史累计匹配数量）
        /// </summary>
        public decimal MatchQuantity { get; set; }

        /// <summary>
        /// 本次匹配数量（当前操作的匹配数量）
        /// </summary>
        public decimal CurrentMatchQuantity { get; set; }

        /// <summary>
        /// 匹配精度
        /// </summary>
        public MatchPrecisionEnum MatchPrecision { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public int OperationType { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 匹配键，用于快速匹配
        /// </summary>
        public string? MatchKey { get; set; }

        /// <summary>
        /// 厂家ID
        /// </summary>
        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 匹配金额，用于购货修订业务类型
        /// 当业务类型为购货修订时，此属性用于赋值给taxcost
        /// </summary>
        public decimal MatchedAmount { get; set; }
        /// <summary>
        /// 当前匹配金额
        /// </summary>
        public decimal CurrentMatchAmount { get;  set; }
    }

    /// <summary>
    /// 提交勾稽结果请求
    /// </summary>
    public class SubmitMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 获取已匹配的明细请求
    /// </summary>
    public class GetMatchedDetailsRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 页码，从1开始
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; } = 10;
    }

    /// <summary>
    /// 取消勾稽结果请求
    /// </summary>
    public class RevokeMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票号
        /// </summary>
        public string? MergeInvoiceNumber { get; set; }
    }

    /// <summary>
    /// 删除匹配明细请求
    /// </summary>
    public class DeleteMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 匹配明细ID（已弃用，请使用MatchDetailIds）
        /// </summary>
        [Obsolete("请使用MatchDetailIds替代")]
        public Guid MatchDetailId { get; set; }

        /// <summary>
        /// 匹配明细ID数组，支持多选删除
        /// </summary>
        public List<Guid> MatchDetailIds { get; set; } = new List<Guid>();
    }

    /// <summary>
    /// 查询缓存数据的参数对象
    /// </summary>
    public class QueryCacheParams
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 业务类型，可选
        /// </summary>
        public BusinessType? BusinessType { get; set; }

        /// <summary>
        /// 业务单号，可选（已弃用，请使用BusinessItemCode）
        /// </summary>
        [Obsolete("请使用BusinessItemCode替代")]
        public string? BusinessCode { get; set; }

        /// <summary>
        /// 业务明细单号，可选
        /// </summary>
        public string? BusinessItemCode { get; set; }

        /// <summary>
        /// 业务单据开始时间，可选
        /// </summary>
        public DateTime? BusinessDateStart { get; set; }

        /// <summary>
        /// 业务单据结束时间，可选
        /// </summary>
        public DateTime? BusinessDateEnd { get; set; }

        /// <summary>
        /// 货号，可选
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 产品名称，可选
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 业务类型接口更新状态字典，记录每种业务类型是否已经请求过接口数据
        /// </summary>
        public Dictionary<BusinessType, bool> InterfaceUpdateStatus { get; set; } = new Dictionary<BusinessType, bool>();

        /// <summary>
        /// 规格，可选
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号，可选
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 厂家名称，可选
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 厂家订单号，可选
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 采购订单号，可选
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 税率，可选
        /// </summary>
        public decimal? TaxRate { get; set; }
    }

    /// <summary>
    /// 获取缓存中的明细数据请求
    /// </summary>
    public class GetCacheDetailsRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 业务类型，可选
        /// </summary>
        public BusinessType? BusinessType { get; set; }

        /// <summary>
        /// 业务单号，可选（已弃用，请使用BusinessItemCode）
        /// </summary>
        [Obsolete("请使用BusinessItemCode替代")]
        public string? BusinessCode { get; set; }

        /// <summary>
        /// 业务明细单号，可选
        /// </summary>
        public string? BusinessItemCode { get; set; }

        /// <summary>
        /// 业务单据开始时间，可选
        /// </summary>
        public DateTime? BusinessDateStart { get; set; }

        /// <summary>
        /// 业务单据结束时间，可选
        /// </summary>
        public DateTime? BusinessDateEnd { get; set; }

        /// <summary>
        /// 货号，可选
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 产品名称，可选
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 规格，可选
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号，可选
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 厂家名称，可选
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 厂家订单号，可选
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 采购订单号，可选
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 税率，可选
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 业务类型接口更新状态字典，记录每种业务类型是否已经请求过接口数据
        /// </summary>
        public Dictionary<BusinessType, bool>? InterfaceUpdateStatus { get; set; }

        /// <summary>
        /// 页码，从1开始
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; } = 10;

        /// <summary>
        /// 转换为查询缓存参数对象
        /// </summary>
        /// <returns>查询缓存参数对象</returns>
        public QueryCacheParams ToQueryCacheParams()
        {
            return new QueryCacheParams
            {
                MergeInputBillId = MergeInputBillId,
                BusinessType = BusinessType,
#pragma warning disable CS0618 // 类型或成员已过时
                BusinessCode = BusinessCode,
#pragma warning restore CS0618 // 类型或成员已过时
                BusinessItemCode = BusinessItemCode,
                // 确保日期只包含日期部分，不包含时间部分
                BusinessDateStart = BusinessDateStart?.Date,
                // 确保结束日期包含当天的所有时间
                BusinessDateEnd = BusinessDateEnd?.Date,
                ProductNo = ProductNo,
                ProductName = ProductName,
                Specification = Specification,
                Model = Model,
                ProducerName = ProducerName,
                ProducerOrderNo = ProducerOrderNo,
                PurchaseOrderCode = PurchaseOrderCode,
                TaxRate = TaxRate,
                // 传递接口更新状态
                InterfaceUpdateStatus = InterfaceUpdateStatus ?? new Dictionary<BusinessType, bool>()
            };
        }
    }

    /// <summary>
    /// 获取缓存中的明细数据响应
    /// </summary>
    public class GetCacheDetailsResponse
    {
        /// <summary>
        /// 缓存中的明细数据
        /// </summary>
        public Dictionary<BusinessType, List<MatchableDocumentItem>> CacheDetails { get; set; }

        /// <summary>
        /// 匹配状态缓存
        /// </summary>
        public Dictionary<BusinessType, bool> MatchStatus { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; }
    }

    /// <summary>
    /// 开始异步匹配请求
    /// </summary>
    public class StartAsyncMatchRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 开始日期 - 接口业务单据对应的时间（带时区信息）
        /// </summary>
        public DateTimeOffset? StartDate { get; set; }

        /// <summary>
        /// 结束日期 -  接口业务单据对应的时间（带时区信息）
        /// </summary>
        public DateTimeOffset? EndDate { get; set; }

        /// <summary>
        /// 是否重新匹配（如果为true，则删除原有匹配记录，重新匹配）
        /// </summary>
        public bool IsReload { get; set; } = false;
    }

    /// <summary>
    /// 获取待提交的明细请求
    /// </summary>
    public class GetSubmitDetailsRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并进项发票明细ID（可空）
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 关键字查询，可以对品名/货号/规格/型号/业务单据等进行模糊匹配
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 页码，从1开始
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页数量
        /// </summary>
        public int Limit { get; set; } = 10;
    }

    /// <summary>
    /// 待提交的明细项
    /// </summary>
    public class SubmitDetailItem
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 合并进项发票明细ID
        /// </summary>
        public Guid? MergeInputBillDetailId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 业务单号（已弃用，请使用BusinessItemCode）
        /// </summary>
        [Obsolete("请使用BusinessItemCode替代")]
        public string? BusinessCode { get; set; }

        /// <summary>
        /// 业务明细单号（主要的排序和匹配依据）
        /// </summary>
        public string? BusinessItemCode { get; set; }

        /// <summary>
        /// 业务时间
        /// </summary>
        public string? BusinessDate { get; set; }

        /// <summary>
        /// 已匹配数量（历史累计匹配数量）
        /// </summary>
        public decimal MatchQuantity { get; set; }

        /// <summary>
        /// 本次匹配数量（当前操作的匹配数量）
        /// </summary>
        public decimal CurrentMatchQuantity { get; set; }

        /// <summary>
        /// 匹配维度
        /// </summary>
        public MatchPrecisionEnum MatchPrecision { get; set; }

        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal? TaxCost { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal? NoTaxCost { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? NoTaxAmount { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 匹配金额
        /// 对于普通业务类型：匹配金额 = 匹配数量 × 含税单价
        /// 对于服务费和购货修订：匹配金额 = 匹配数量（实际上是金额）
        /// </summary>
        public decimal? MatchedAmount { get; set; }

        /// <summary>
        /// 业务金额（对于购货修订和服务费，直接使用TaxCost；对于其他业务类型，使用TaxCost * MatchQuantity）
        /// </summary>
        public decimal? BusinessAmount
        {
            get
            {
                if (!TaxCost.HasValue) return null;

                // 对于购货修订、服务费和损失确认，直接返回TaxCost
                if (BusinessType == 4 || BusinessType == 2 || BusinessType == 7) // PurchaseRevision=4, ServiceFeeProcurement=2, LossRecognition=7
                {
                    return TaxCost.Value;
                }

                // 对于其他业务类型，返回TaxCost * MatchQuantity
                return MatchQuantity != 0 ? Math.Round(TaxCost.Value * Math.Abs(MatchQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 不含税业务金额
        /// </summary>
        public decimal? NoTaxBusinessAmount
        {
            get
            {
                if (!NoTaxCost.HasValue) return null;

                // 对于购货修订、服务费和损失确认，直接返回NoTaxCost
                if (BusinessType == 4 || BusinessType == 2 || BusinessType == 7) // PurchaseRevision=4, ServiceFeeProcurement=2, LossRecognition=7
                {
                    return NoTaxCost.Value;
                }

                // 对于其他业务类型，返回NoTaxCost * MatchQuantity
                return MatchQuantity != 0 ? Math.Round(NoTaxCost.Value * Math.Abs(MatchQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 已入票金额 - 这个属性在SubmitDetailItem中不需要，因为它没有InvoicedQuantity属性
        /// 如果需要，可以在后端计算并设置
        /// </summary>
        public decimal? InvoicedAmount { get; set; }

        /// <summary>
        /// 本次入票金额
        /// </summary>
        public decimal? CurrentMatchAmount
        {
            get
            {
                if (!TaxCost.HasValue) return null;

                // 对于购货修订和服务费，直接返回CurrentMatchQuantity
                if (BusinessType == 4 || BusinessType == 2) // PurchaseRevision=4, ServiceFeeProcurement=2
                {
                    return CurrentMatchQuantity;
                }

                // 对于其他业务类型，返回TaxCost * CurrentMatchQuantity
                return CurrentMatchQuantity != 0 ? Math.Round(TaxCost.Value * Math.Abs(CurrentMatchQuantity), 2) : 0;
            }
        }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public int? BusinessType { get; set; }

        /// <summary>
        /// 业务类型描述
        /// </summary>
        public string BusinessTypeDescription => ((BusinessType)(BusinessType ?? 0)).GetDescription();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 厂家ID
        /// </summary>
        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 匹配键，用于快速匹配
        /// </summary>
        public string? MatchKey { get; set; }

        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string? PurchaseOrderCode { get; set; }

        /// <summary>
        /// 匹配维度描述
        /// </summary>
        public string MatchPrecisionDesc => MatchPrecision.GetDescription();
    }

    /// <summary>
    /// 获取发票金额统计请求
    /// </summary>
    public class GetInvoiceAmountStatisticsRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 获取发票金额统计响应
    /// </summary>
    public class GetInvoiceAmountStatisticsResponse
    {
        /// <summary>
        /// 发票明细总金额
        /// </summary>
        public decimal TotalInvoiceAmount { get; set; }

        /// <summary>
        /// 待提交发票金额
        /// </summary>
        public decimal TotalSubmitAmount { get; set; }

        /// <summary>
        /// 差值（发票总金额 - 待提交发票金额）
        /// </summary>
        public decimal DifferenceAmount { get; set; }

        /// <summary>
        /// 是否已全部匹配（差值小于0.01）
        /// </summary>
        public bool IsFullyMatched => Math.Abs(DifferenceAmount) < 0.01m;
    }

    /// <summary>
    /// 获取缓存中的匹配条件请求
    /// </summary>
    public class GetCachedMatchConditionRequest
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }
    }

    /// <summary>
    /// 获取缓存中的匹配条件响应
    /// </summary>
    public class GetCachedMatchConditionResponse
    {
        /// <summary>
        /// 匹配条件
        /// </summary>
        public GetMatchableDocumentsRequest MatchCondition { get; set; }

        /// <summary>
        /// 匹配状态
        /// </summary>
        public Dictionary<BusinessType, bool> MatchStatus { get; set; }

        /// <summary>
        /// 是否存在缓存条件
        /// </summary>
        public bool HasCachedCondition => MatchCondition != null;
    }
}
