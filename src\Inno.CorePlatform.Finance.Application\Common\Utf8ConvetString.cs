﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Common
{
    public static class Utf8ConvetString
    {
        /// <summary>
        /// 获取utf-8编码的中文
        /// </summary>
        /// <returns></returns>
        public static string GetUtf8string(string str) {
            byte[] utf8Bytes=Encoding.UTF8.GetBytes(str);
            return Encoding.UTF8.GetString(utf8Bytes);
        }
    }
}
