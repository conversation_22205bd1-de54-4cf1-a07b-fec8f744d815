﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 付款单查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PaymentQueryController : BaseController
    {
        private readonly IPaymentQueryService _paymentQueryService;
        private readonly ILogger<PaymentQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IFileGatewayClient _fileGatewayClient;

        /// <summary>
        /// 构造
        /// </summary>
        public PaymentQueryController(IPaymentQueryService paymentQueryService, ILogger<PaymentQueryController> logger, IKingdeeApiClient kingdeeApiClient,
            IFileGatewayClient fileGatewayClient, ISubLogService subLog) : base(subLog)
        {
            _paymentQueryService = paymentQueryService;
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            this._fileGatewayClient = fileGatewayClient;
        }
        /// <summary>
        /// 获取付款单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<PaymentQueryListOutput>> GetList([FromBody] PaymentQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                var (list, count) = await _paymentQueryService.GetListAsync(query);
                return new ResponseData<PaymentQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<PaymentQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取付款单列表数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<PaymentQueryListTabOutput>> GetTabCount([FromBody] PaymentQueryInput query)
        {
            query.UserId = CurrentUser.Id.Value;
            return await _paymentQueryService.GetTabCount(query);
        }
        /// <summary>
        /// 获取金蝶附件
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet("GetKDFilePath")]
        public async Task<BaseResponseData<List<ReceiptNumberModelOutput>>> GetKDFilePath(string code)
        {
            if (code.ToUpper().Contains("PV-"))
            {
                code = code.Split('-')[0] + "-" + code.Split('-')[1] + "-" + code.Split('-')[2];
            }
            else if (code.ToUpper().Contains("PA-"))
            {
                code = code.Split('-')[0] + "-" + code.Split('-')[1] + "-" + code.Split('-')[2] + "-" + code.Split('-')[3];
            }
            else
            {
                code = code.Split('-')[0];
            }
            var ret = await _kingdeeApiClient.SelectTheReceiptNumber(new List<ReceiptNumberModelInput> {
                new ReceiptNumberModelInput {
                     paymentNum = code,
                }
            }, type: "payBill");
            return ret;
        }
        /// <summary>
        /// 获取导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExportList")]
        public async Task<IActionResult> GetExportList([FromBody] PaymentQueryInput query)
        {
            try
            {
                query.limit = 5000;//最多查询五千行导出
                query.UserId = CurrentUser.Id.Value;
                var (list, count) = await _paymentQueryService.GetListAsync(query);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "付款单号";
                    worksheet.Cells[1, 2].Value = "公司";
                    worksheet.Cells[1, 3].Value = "供应商";
                    worksheet.Cells[1, 4].Value = "业务单元";
                    worksheet.Cells[1, 5].Value = "类型";
                    worksheet.Cells[1, 6].Value = "单据日期";
                    worksheet.Cells[1, 7].Value = "实际付款日期";
                    worksheet.Cells[1, 8].Value = "币种";
                    worksheet.Cells[1, 9].Value = "币种金额";
                    worksheet.Cells[1, 10].Value = "人民币金额";
                    worksheet.Cells[1, 11].Value = "已冲销";
                    worksheet.Cells[1, 12].Value = "余额";
                    worksheet.Cells[1, 13].Value = "采购单号";
                    worksheet.Cells[1, 14].Value = "采购合同单号";
                    worksheet.Cells[1, 15].Value = "厂家单号";
                    worksheet.Cells[1, 16].Value = "项目名称";
                    worksheet.Cells[1, 17].Value = "批量付款单号";
                    worksheet.Cells[1, 18].Value = "终端医院";
                    worksheet.Cells[1, 19].Value = "客户";
                    #endregion

                    #region 获取医院数据
                    var paymentAutoItemCodes = list.Select(x => x.PaymentAutoItemCode).ToList();
                    var hospitals = await _paymentQueryService.GetHospitalsByPaymentAutoItemCodes(paymentAutoItemCodes);
                    #endregion

                    #region 数据
                    int row = 2;
                    foreach (var item in list)
                    {
                        // 终端医院字符串
                        string hosStr = string.Empty;
                        var hoslists = hospitals.Where(x => x.PaymentAutoItemCode == item.PaymentAutoItemCode && x.AgentId == item.AgentId && x.Code == item.Code).ToList();
                        if (hoslists.Any())
                        {
                            hosStr = string.Join(",", hoslists.Where(x => !string.IsNullOrEmpty(x.HospitalName)).Select(x => x.HospitalName).ToList());
                        }
                        worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 10].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 12].Style.Numberformat.Format = "#,##0.00";

                        worksheet.Cells[row, 1].Value = item.Code;
                        worksheet.Cells[row, 2].Value = item.CompanyName;
                        worksheet.Cells[row, 3].Value = item.AgentName;
                        worksheet.Cells[row, 4].Value = item.ServiceName;
                        worksheet.Cells[row, 5].Value = item.TypeDisplay;
                        worksheet.Cells[row, 6].Value = item.BillDate.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 7].Value = item.PaymentDate != DateTime.MinValue ? item.PaymentDate.ToString("yyyy-MM-dd") : "";
                        worksheet.Cells[row, 8].Value = item.CoinName;
                        worksheet.Cells[row, 9].Value = decimal.Parse(item.Value.ToString("#0.0000"));
                        worksheet.Cells[row, 10].Value = item.RMBAmount.HasValue ? decimal.Parse(item.RMBAmount.Value.ToString("#0.0000")) : 0m;
                        worksheet.Cells[row, 11].Value = decimal.Parse(item.AbatedValue.ToString("#0.0000"));
                        worksheet.Cells[row, 12].Value = decimal.Parse(item.RemainValue.ToString("#0.0000"));
                        worksheet.Cells[row, 13].Value = item.PurchaseCode;
                        worksheet.Cells[row, 14].Value = item.PurchaseContactNo;
                        worksheet.Cells[row, 15].Value = item.ProducerOrderNo;
                        worksheet.Cells[row, 16].Value = item.ProjectName;
                        worksheet.Cells[row, 17].Value = item.PaymentAutoItemCode;
                        worksheet.Cells[row, 18].Value = hosStr;
                        worksheet.Cells[row, 18].Value = item.CustomerName; 
                        row++;
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                     
                }
                 
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("payment/export")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync([FromBody] PaymentQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _paymentQueryService.ExportAsync(query);
        }


        /// <summary>
        /// 获取付款明细
        /// </summary>
        /// <param name="query">批量付款单Id</param>
        /// <returns></returns>
        [HttpPost("GetPaymentPlan")]
        public async Task<BaseResponseData<PageResponse<PaymentDetailOutput>>> GetPaymentPlan([FromBody] QueryById query)
        {
            query.userId = CurrentUser.Id.Value;
            var result = await _paymentQueryService.GetPaymentPlan(query);
            var res = new BaseResponseData<PageResponse<PaymentDetailOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<PaymentDetailOutput>
                {
                    List = result,
                    Total = result.Count
                },
            };
            return res;
        }


        /// <summary>
        /// 导出付款明细
        /// </summary>
        /// <param name="query">批量付款单Id</param>
        /// <returns></returns>
        [HttpPost("ExportPaymentPlan")]

        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportPaymentPlan([FromBody] QueryById query)
        {
            query.page = 1;
            query.limit = 20;
            query.userId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _paymentQueryService.ExportPaymentPlan(query);
        }

        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(AddCashDiscountFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            Payment payment = await _paymentQueryService.GetById(input.Id);
            if (!string.IsNullOrEmpty(payment.AttachFileIds))
            {
                var fileIds = payment.AttachFileIds?.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var bizFiles = new List<BizFileUploadOutput>();
                foreach (var fileId in fileIds)
                {
                    if (!string.IsNullOrWhiteSpace(fileId))
                    {
                        var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                        if (file != null)
                        {
                            file.UploadedTime = Convert.ToDateTime(file.UploadedTime).AddHours(8).ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
                            bizFiles.Add(file);
                        }
                    }
                }
#if DEBUG
                bizFiles.Add(new BizFileUploadOutput { Id = Guid.Parse(fileIds[0]), Length = 1000, Name = "测试" });
#endif
                ret.Data = bizFiles;

            }
            return ret;
        }

        /// <summary>
        /// 删除供应商附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds")]
        public async Task<BaseResponseData<bool>> DeleteAttachFileIds(AddCashDiscountFileInput input)
        {
            return await _paymentQueryService.DeleteAttachFileIds(input);
        }

        /// <summary>
        /// 导出付款信息
        /// </summary>
        /// <returns></returns>
        [HttpPost("exportPaymentInfo")]
        public async Task<BaseResponseData<ExportTaskResDto>> ExportPaymentInfoAsync([FromBody] PaymentQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser!.Id!.Value;
            query.CurrentUserName = CurrentUser.UserName;
            return await _paymentQueryService.ExportPaymentInfoAsync(query);
        }
    }
}
