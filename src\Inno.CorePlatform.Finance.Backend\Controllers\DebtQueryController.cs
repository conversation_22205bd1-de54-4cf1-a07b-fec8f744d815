﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 应付查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class DebtQueryController : BaseController
    {
        private readonly IDebtQueryService _debtQueryService;
        private readonly IAbtmentService _abtmentService;
        private readonly ILogger<DebtQueryController> _logger;
        private readonly ICreditAppService _creditAppService;

        /// <summary>
        /// 应付查询
        /// </summary>
        public DebtQueryController(IDebtQueryService debtQueryService, ICreditAppService creditAppService, IAbtmentService abtmentService, ILogger<DebtQueryController> logger, ISubLogService subLog) : base(subLog)
        {
            _debtQueryService = debtQueryService;
            _abtmentService = abtmentService;
            _creditAppService = creditAppService;
            _logger = logger;
        }

        /// <summary>
        /// 获取应付单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<DebtQueryListOutput>> GetList([FromBody] DebtQueryInput query)
        {
            try
            {
                query.userId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _debtQueryService.GetListAsync(query);
                return new ResponseData<DebtQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取应付单列表数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<CreditQueryListTabOutput>> GetTabCount([FromBody] DebtQueryInput query)
        {
            query.userId = CurrentUser.Id.Value;
            return await _debtQueryService.GetTabCount(query);
        }
        /// <summary>
        /// 获取应付单计划明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetail")]
        public async Task<ResponseData<DebtDetailQueryListOutput>> GetListDetail([FromBody] DebtDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _debtQueryService.GetListDetailAsync(query);
                return new ResponseData<DebtDetailQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtDetailQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取付款计划查询明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetailQuery")]
        public async Task<ResponseData<DebtDetailQueryListOutput>> GetListDetailQuery([FromBody] DebtDetailQueryInput query)
        {
            try
            {
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                var (list, count) = await _debtQueryService.GetListDetailQueryAsync(query);
                return new ResponseData<DebtDetailQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtDetailQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取付款预警列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetDebtPayWarnningList")]
        public async Task<ResponseData<DebtDetailQueryListOutput>> GetDebtPayWarnningList(DebtDetailQueryInput query)
        {
            try
            {
                query.Warnning = true;
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                query.Status = Domain.DebtDetailStatusEnum.WaitExecute;
                query.AbatedStatus = Domain.AbatedStatusEnum.NonAbate;
                var (list, count) = await _debtQueryService.GetListDetailQueryAsync(query);
                return new ResponseData<DebtDetailQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtDetailQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取付款预警总数
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetDebtPayWarnningCount")]
        public async Task<BaseResponseData<int>> GetDebtPayWarnningCount(DebtDetailQueryInput query)
        {
            try
            {
                query.Warnning = true;
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                query.Status = Domain.DebtDetailStatusEnum.WaitExecute;
                query.AbatedStatus = Domain.AbatedStatusEnum.NonAbate;
                var count = await _debtQueryService.GetDebtPayWarnningCount(query);
                return new BaseResponseData<int>
                {
                    Code = CodeStatusEnum.Success,
                    Data = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 按公司获取应付单计划明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDetailsByCompanies")]
        public async Task<BaseResponseData<PageResponse<DebtDetailBulkOutput>>> GetDetailsByCompanies([FromBody] DebtDetailBulkQuery query)
        {
            try
            {
                query.UserId = CurrentUser.Id;
                (var list, var count) = await _debtQueryService.GetDetailsByCompanies(query);
                var res = new BaseResponseData<PageResponse<DebtDetailBulkOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<DebtDetailBulkOutput>
                    {
                        List = list,
                        Total = count
                    },
                    Total = count
                };
                return res;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取应付单执行明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetailExcute")]
        public async Task<ResponseData<DebtDetailExcuteQueryListOutput>> GetListDetailExcute([FromBody] DebtDetailExcuteQueryInput query)
        {
            try
            {
                var (list, count) = await _debtQueryService.GetListDetailExcuteAsync(query);
                return new ResponseData<DebtDetailExcuteQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtDetailExcuteQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        /// <summary>
        /// 获取可冲销的应付
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetAvailableAbatments")]
        public async Task<BaseResponseData<List<AvailableAbatmentsOutput>>> GetAvailableAbatments([FromBody] AvailableAbatmentsInput query)
        {
            return await _debtQueryService.GetAvailableAbatmentsAsync(query.DebtId, query.billCode, CurrentUser.Id.Value, query.classify ?? "debt", query.customer, query.billDateStart, query.billDateEnd);
        }
        /// <summary>
        /// 应付冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>

        [HttpPost("DebtAbatments")]
        public async Task<ResponseData<DebtAbatmentOutput>> DebtAbatments([FromBody] DebtAbatmentInput input)
        {
            try
            {
                if (input.LstInput != null && input.LstInput.Any())
                {
                    var res = 0;
                    if (input.classify == "credit")
                    {
                        res = await _abtmentService.GenerateAbtForDebtToCreditAsync(input.DebtId, input.LstInput, CurrentUser.UserName, true);
                    }
                    else if (input.classify == "debt")
                    {
                        res = await _abtmentService.GenerateAbtForDebtAsync(input.DebtId, input.LstInput, CurrentUser.UserName, true);

                    }
                    else if (input.classify == "receive")
                    {
                        res = await _abtmentService.GenerateAbtForReceiveAsync(input.DebtId, input.LstInput, CurrentUser.UserName, true);
                    }
                    else if (input.classify == "payment")
                    {
                        res = await _abtmentService.GenerateAbtForPaymentAsync(input.DebtId, input.LstInput, CurrentUser.UserName, true);
                    }
                    //如果应付input.DebtId==服务费，或者input.LstInput 里面有服务费的应付。冲销完成后调用已付款的消息队列发消息给采购
                    if (res > 0)
                    {
                        //服务费付款后通知采购 
                        var debtCodes = input.LstInput.Select(p => p.BillCode).Distinct().ToList();
                        var debtList = new List<DebtPo>();
                        var debt = await _debtQueryService.GetDebtByIdAsync(input.DebtId);
                        if (debt != null)
                        {
                            debtList.Add(debt);
                        }
                        if (input.classify == "debt")
                        {
                            await _abtmentService.ServicePaiedPub(debtList, debtCodes);
                        }
                        else
                        {
                            await _abtmentService.ServicePaiedPub(debtList);
                        }
                        await _abtmentService.MinusDebtAbtPurchasePub(input.DebtId, input.LstInput);
                    }
                    return new ResponseData<DebtAbatmentOutput>
                    {
                        Code = 200,
                        Data = new Data<DebtAbatmentOutput>
                        {
                            Total = res,
                        }
                    };
                }
                else
                {
                    throw new AppServiceException("请传入被冲销的应付单列表");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 拆分
        /// </summary> 
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("SplitDebtDetail")]
        public async Task<BaseResponseData<int>> SplitDebtDetail(SplitDebtDetailInputDto data)
        {
            return await _creditAppService.SplitDebtDetail(data.DetailId, data.Amount);
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        //[HttpPost("debt/export")]
        //public async Task<IActionResult> DebtHasInvoiceExport([FromBody] DebtQueryInput query)
        //{
        //    query.page = 1;
        //    query.limit = int.MaxValue;
        //    query.userId = CurrentUser.Id.Value;
        //    query.CurrentUserName = CurrentUser.UserName;
        //    var stream = await _debtQueryService.DebtHasInvoiceExport(query);
        //    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //}

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("debt/export")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync([FromBody] DebtQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.userId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _debtQueryService.ExportAsync(query);
        }

        /// <summary>
        /// 下载付款预警列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("DownLoadDebtPayWarnningList")]
        public async Task<IActionResult> DownLoadDebtPayWarnningList(DebtDetailQueryInput query)
        {
            try
            {
                query.page = 1;
                query.limit = int.MaxValue;
                query.Warnning = true;
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                var stream = await _debtQueryService.DownLoadDebtPayWarnningList(query);
                return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取付款计划清单导出数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetExportPaymentList")]
        public async Task<IActionResult> GetExportPaymentList([FromBody] DebtDetailQueryInput query)
        {
            try
            {
                query.page = 1;
                query.limit = 5000;//最多查询五千行导出
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                var (list, count) = await _debtQueryService.GetListDetailQueryAsync(query);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "付款计划单号";
                    worksheet.Cells[1, 2].Value = "应付单号";
                    worksheet.Cells[1, 3].Value = "账期类型";
                    worksheet.Cells[1, 4].Value = "预计付款日期";
                    worksheet.Cells[1, 5].Value = "收款单号";
                    worksheet.Cells[1, 6].Value = "订单号";
                    worksheet.Cells[1, 7].Value = "核算部门";
                    worksheet.Cells[1, 8].Value = "公司";
                    worksheet.Cells[1, 9].Value = "供应商";
                    worksheet.Cells[1, 10].Value = "业务单元";
                    worksheet.Cells[1, 11].Value = "付款金额";
                    worksheet.Cells[1, 12].Value = "客户";
                    worksheet.Cells[1, 13].Value = "项目名称";
                    worksheet.Cells[1, 14].Value = "状态";
                    #endregion



                    #region 数据
                    int row = 2;
                    foreach (var item in list)
                    {
                        worksheet.Cells[row, 12].Style.Numberformat.Format = "#,##0.00";

                        worksheet.Cells[row, 1].Value = item.Code;
                        worksheet.Cells[row, 2].Value = item.Debt.BillCode;
                        worksheet.Cells[row, 3].Value = item.AccountPeriodTypeStr;
                        worksheet.Cells[row, 4].Value = item.ProbablyPayTime.Value.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 5].Value = item.ReceiveCode;
                        worksheet.Cells[row, 6].Value = item.OrderNo;
                        worksheet.Cells[row, 7].Value = item.Debt.BusinessDeptFullName;
                        worksheet.Cells[row, 8].Value = item.Debt.CompanyName;
                        worksheet.Cells[row, 9].Value = item.Debt.AgentName;
                        worksheet.Cells[row, 10].Value = item.Debt.ServiceName;
                        worksheet.Cells[row, 11].Value = decimal.Parse(item.Value.ToString("#0.00"));
                        worksheet.Cells[row, 12].Value = (item.Credit != null && item.Credit.CustomerName != null) ? item.Credit.CustomerName : "";

                        worksheet.Cells[row, 13].Value = item.Debt.ProjectName;
                        string status = "";
                        if (item.Status == Domain.DebtDetailStatusEnum.WaitExecute)
                        {
                            status = "待执行";
                        }
                        if (item.Status == Domain.DebtDetailStatusEnum.Completed)
                        {
                            status = "已完成";
                        }
                        worksheet.Cells[row, 14].Value = status;
                        row++;
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                }

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 保存提交账期起始日申请，然后推到OA审批
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveAccountPeriodEdit")]
        public async Task<BaseResponseData<string>> SaveAccountPeriodEdit([FromBody] AccountPeroidEditInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            return await _debtQueryService.SaveAccountPeriodEdit(input);
        }
        /// <summary>
        /// 查询账期起始日申请列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDebtDetailListQueryAsync")]
        public async Task<ResponseData<DebtDetailAuditOutput>> GetDebtDetailListQueryAsync([FromBody] DebtDetailQueryInput query)
        {
            try
            {
                //增加数据策略权限
                query.StrategyQuery = new StrategyQueryInput()
                {
                    userId = CurrentUser.Id,
                    functionUri = "metadata://fam"
                };
                var (list, count) = await _debtQueryService.GetDebtDetailListQueryAsync(query);
                return new ResponseData<DebtDetailAuditOutput>
                {
                    Code = 200,
                    Data = new Data<DebtDetailAuditOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 查看账期起始日申请附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile(AccountPeriodDateAttachFileInput input)
        {
            var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
            ret.Data = await _debtQueryService.GetAttachFile(input);
            return ret;
        }
        /// <summary>
        /// 获取tab数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDebtDetailTabCount")]
        public async Task<BaseResponseData<DebtDetailListTableOutput>> GetTabCount([FromBody] DebtDetailQueryInput query)
        {
            //增加数据策略权限
            query.StrategyQuery = new StrategyQueryInput()
            {
                userId = CurrentUser.Id,
                functionUri = "metadata://fam"
            };
            return await _debtQueryService.GetTabCount(query);
        }

        /// <summary>
        /// 撤销冲销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CancelAbatments")]
        public async Task<BaseResponseData<bool>> CancelAbatmentAsync(CancelAbatmentInput input)
        {
            return await _debtQueryService.CancelAbatmentAsync(input);
        }

        /// <summary>
        /// 更换应付核算部门
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ChangeBusinessDept")]
        public async Task<BaseResponseData<bool>> ChangeBusinessDeptAsync(ChangeBusinessDeptInput input)
        {
            try
            {
                return await _debtQueryService.ChangeBusinessDeptAsync(input);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

    }
}

