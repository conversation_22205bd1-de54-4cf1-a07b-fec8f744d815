using Dapr.Client;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    /// <summary>
    /// EDA失败消息处理客户端
    /// </summary>
    public class EDAFailureMsgClient : BaseDaprApiClient<EDAFailureMsgClient>, IEDAFailureMsgClient
    {
        public EDAFailureMsgClient(DaprClient daprClient, ILogger<EDAFailureMsgClient> logger, IHttpContextAccessor httpContextAccessor)
            : base(daprClient, logger, httpContextAccessor)
        {
        }

        /// <summary>
        /// 获取应用ID（失败消息绑定不需要特定的AppId）
        /// </summary>
        /// <returns></returns>
        protected override string GetAppId()
        {
            return "binding-output-failuremsg";
        }

        /// <summary>
        /// 发送失败消息到重试队列
        /// </summary>
        /// <param name="input">失败消息输入</param>
        /// <returns></returns>
        public async Task SendFailureMsg(FailureMsgInput input)
        {
            if (!string.IsNullOrWhiteSpace(input.FailReason))
            {
                input.FailReason = input.FailReason + $",时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}";
            }
            await InvokeBindingAsync("create", input);
        }
    }
}
