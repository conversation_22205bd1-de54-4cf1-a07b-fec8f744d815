﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class GetStaffInfoInputDto
    {
        public string userId { get; set; }
        public string? userName { get; set; }
    }
    public class StaffsInput
    {
        [JsonProperty("id")]
        public string Id { get; set; }
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }
        [JsonProperty("name")]
        public string Name { get; set; }
        [JsonProperty("names")]
        public List<string> Names { get; set; }
    }
    public class GetUserInput {
        /// <summary>
        /// 用户名
        /// </summary> 
        public string? DisplayName { get; set; }
        /// <summary>
        /// 用户名
        /// </summary> 
        public List<string>? Names { get; set; }

        public int? Page { get; set; } = 1;
        public int? Limit { get; set; } = 20;
    }
}
