﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class PurchaseQueryInfoSimpleOutput : BaseDtoWithBasicInfo<Guid>
    {

        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; } = "";
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTimeOffset? BillDate { get; set; }
        /// <summary>
        /// 供应商信息
        /// </summary>
        public Agent? Agent { get; set; }
        /// <summary>
        /// 厂家信息
        /// </summary>
        public Producer? Producer { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public Service? Service { get; set; }
     
        /// <summary>
        /// 关联项目
        /// </summary>
        public Project? Project { get; set; }
        /// <summary>
        /// 采购订单类型
        /// </summary>
        public PurchaseTypeEnums Type { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = "";
        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; } = ""; 
        /// <summary>
        /// 事业部Id
        /// </summary>
        public string BusinessDeptId { get; set; }
        /// <summary>
        /// 事业部全名路径
        /// </summary>
        public string BusinessDeptFullName { get; set; }
        /// <summary>
        /// 事业部全路径Id
        /// </summary>
        public string BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 事业部短码
        /// </summary>
        public string BusinessDeptShortName { get; set; } 
        /// <summary>
        /// 厂家订单号
        /// </summary>
        public string? ProducerOrderNo { get; set; }
        public int PurchaseContractType { get; set; }
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName
        {
            get
            {
                return ((PurchaseStatusEnums)Status).GetDescription();
            }
        }
        /// <summary>
        /// 收货方（公司）
        /// </summary>
        public IdNameCodeClass? Consignee { get; set; }
        /// <summary>
        /// 采购合同
        /// </summary>
        public ContractOutPut? Contract { get; set; } 
    }

    public enum PurchaseTypeEnums
    {
        /// <summary>
        /// 采购订单
        /// </summary>
        [Description("采购订单")]
        Normal = 1,
        /// <summary>
        /// 寄售转购货
        /// </summary>
        [Description("寄售转购货")]
        OrderToPurchase = 2,
        /// <summary>
        /// 购货修订
        /// </summary>
        [Description("购货修订")]
        FixPurchaseOrder = 3,
        /// <summary>
        /// 订单修订
        /// </summary>
        [Description("订单修订")]
        ReviseSale = 4,
        /// <summary>
        /// 定价寄售修订
        /// 主要是返利的时候，针对定价寄售转购货订单生成的修订单类型
        /// </summary>
        [Description("寄售修订")]
        FixOrder = 5,
        /// <summary>
        /// 采购初始化
        /// </summary>
        [Description("采购初始化")]
        InitPurchaseOrder = 6
    }
}
