﻿
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RecognizeReceiveController : BaseController
    {
        private readonly IRecognizeReceiveAppService _recognizeReceiveService;
        private readonly IBaseAllQueryService<RecognizeReceiveDetailPo> _receiveDetailQueryService;
        private readonly ILogger<RecognizeReceiveController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ISPDApiClient _SPDApiClient;
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IInvoiceCreditQueryService _invoiceCreditQueryService;
        private readonly ICreditQueryService _creditQueryService;
        private readonly ISellApiClient _sellApiClient;
        private readonly Common.Clients.Interfaces.IProjectManageClient _projectApiClient;
        private readonly IRecognizeReceiveQueryService _recognizeReceiveQueryService;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IAppServiceContextAccessor _appServiceContextAccessor;
        public RecognizeReceiveController(
            ILogger<RecognizeReceiveController> logger,
            IRecognizeReceiveAppService recognizeReceiveService,
            IBaseAllQueryService<RecognizeReceiveDetailPo> receiveDetailQueryService,
            ISPDApiClient SPDApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IInvoiceQueryService invoiceQueryService,
            ISellApiClient sellApiClient,
            IInvoiceCreditQueryService invoiceCreditQueryService,
            ICreditQueryService creditQueryService,
            IBDSApiClient bDSApiClient,
            IAppServiceContextAccessor appServiceContextAccessor,
            Common.Clients.Interfaces.IProjectManageClient projectManageClient,
            IRecognizeReceiveQueryService recognizeReceiveQueryService, ISubLogService subLog) : base(subLog)
        {
            _recognizeReceiveService = recognizeReceiveService;
            _receiveDetailQueryService = receiveDetailQueryService;
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            _SPDApiClient = SPDApiClient;
            _invoiceQueryService = invoiceQueryService;
            _creditQueryService = creditQueryService;
            _sellApiClient = sellApiClient;
            _recognizeReceiveQueryService = recognizeReceiveQueryService;
            _bDSApiClient = bDSApiClient;
            _invoiceCreditQueryService = invoiceCreditQueryService;
            _projectApiClient = projectManageClient;
            _appServiceContextAccessor = appServiceContextAccessor;
        }
        /// <summary>
        /// 创建认款单
        /// </summary>
        /// <param name="input">认款单</param>
        /// <returns></returns>
        [HttpPost("created")]
        public async Task<BaseResponseData<int>> created([FromBody] ReceivedItemAddInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            var result = await _recognizeReceiveService.AddItemAsync(input);
            var res = new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }

        /// <summary>
        /// 指定收款类型
        /// </summary>
        /// <param name="input">认款单</param>
        /// <returns></returns>
        [HttpPost("sureType")]
        public async Task<BaseResponseData<int>> SureType([FromBody] List<RecognizeReceiveOutput> input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            if (input == null || input.Count <= 0)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败，原因：请选择数据后提交");
                return ret;
            }
            input = input.Where(p => !string.IsNullOrEmpty(p.kdReceivingtype)).ToList();
            if (input == null || input.Count <= 0)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败，原因：请指定收款类型后提交");
                return ret;
            }
            List<modifyCollectionTypeInput> kdInput = new List<modifyCollectionTypeInput>();
            foreach (var item in input)
            {
                kdInput.Add(new modifyCollectionTypeInput
                {
                    billno = item.billno,
                    jfzx_operationremake = item.remake,
                    type = "A",
                    receivingType = item.kdReceivingtype,
                    projectno = item.code,
                    orgno = item.deptId
                });
            }

            var kind = await _kingdeeApiClient.ModifyCollectionType(kdInput);
            if (kind != null && kind.Code != CodeStatusEnum.Success)
            {
                ret = BaseResponseData<int>.Failed(500, "操作失败，原因：" + kind.Message);
                return ret;
            }
            return ret;

        }

        #region 认款详细操作
        /// <summary>
        /// 创建认款单详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("createdDetail")]
        public async Task<BaseResponseData<int>> CreatedDetailAsync([FromBody] RecognizeReceiveDetailInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            var result = await _recognizeReceiveService.CreatedDetailAsync(input);
            var res = new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }
        /// <summary>
        /// 批量创建认款单详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SaveBatchDetailAsync")]
        public async Task<BaseResponseData<int>> SaveBatchDetailAsync([FromBody] RecognizeReceiveDetailsInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            var result = await _recognizeReceiveService.SaveBatchDetailAsync(input);
            return result;
        }

        /// <summary>
        /// 导入发票
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("import")]
        public async Task<BaseResponseData<int>> Import(IFormFile file, [FromQuery] Guid id, [FromQuery] Guid customerId, [FromQuery] bool isReturnCustomer)
        {
            var user = CurrentUser.UserName;
            var result = await _recognizeReceiveService.Import(file, id, customerId, isReturnCustomer, user);
            var res = new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }

        /// <summary>
        /// 导出发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ExportDetails")]
        public async Task<BaseResponseData<RecognizeDetailExcelOutput>> ExportDetails([FromBody] RecognizeDetailExcelInput input)
        {
            return await _recognizeReceiveService.ExportDetails(input.FileId, input.RecognizeReceiveItemId, input.IsReturnCustomer, CurrentUser.UserName, CurrentUser.Id);
        }

        /// <summary>
        /// 更新认款单详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("update")]
        public async Task<BaseResponseData<int>> UpdateDetailAsync(Guid id, [FromBody] RecognizeReceiveDetailInput input)
        {
            var result = await _recognizeReceiveService.UpdateDetailAsync(id, input);
            var res = new BaseResponseData<int>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }

        #endregion


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpDelete("delete")]
        public async Task<BaseResponseData<object>> Delete(RecognizeReceiveDetailDelDto dto)
        {

            var result = await _recognizeReceiveService.DeleteDetailAsync(dto);
            var res = new BaseResponseData<object>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }
        /// <summary>
        /// 删除单据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("item/delete")]
        public async Task<BaseResponseData<object>> DeleteItemAsync(Guid id)
        {
            var result = await _recognizeReceiveService.DeleteItemAsync(id, CurrentUser.UserName);
            var res = new BaseResponseData<object>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;
        }
        /// <summary>
        /// 金蝶保存提交
        /// </summary>
        /// <param name="id">收款单号</param>
        /// <returns></returns>
        [HttpPost("submit")]
        public async Task<BaseResponseData<object>> Submit(Guid id)
        {
            var result = await _recognizeReceiveService.SubmitToKd(id, CurrentUser.UserName);
            var res = new BaseResponseData<object>
            {
                Code = CodeStatusEnum.Success,
                Data = result
            };
            return res;

        }

        /// <summary>
        /// 根据客户id获取是否为第三方回款客户
        /// </summary>
        /// <param name="customerId">收款单号</param>
        /// <returns></returns>
        [HttpPost("isReturnCustomer")]
        public async Task<BaseResponseData<object>> GetCustomer(string customerId)
        {
            var customer = await _bDSApiClient.GetCustomer(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
            {
                id = customerId
            });
            var res = new BaseResponseData<object>
            {
                Code = CodeStatusEnum.Success,
                Data = customer
            };
            return res;
        }


        #region 附件
        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("AttachFileIds")]
        public async Task<BaseResponseData<int>> AttachFileIds(RecognizeItemAttachFileInput input)
        {
            return await _recognizeReceiveService.AttachFileIds(input);
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds")]
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(RecognizeItemAttachFileInput input)
        {
            return await _recognizeReceiveService.DeleteAttachFileIds(input);
        }
        #endregion


        /// <summary>
        /// 撤销认款
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpPost("cancelreceive")]
        public async Task<BaseResponseData<int>> CancelReceive(Guid itemId)
        {
            if (CurrentUser == null)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：当前用户信息无效");
            }
            return await _recognizeReceiveService.CancelReceive(itemId, CurrentUser.UserName);
        }

        /// <summary>
        /// 转货款
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("Transfer")]
        public async Task<BaseResponseData<int>> Transfer([FromBody] QueryById query)
        {
            var userName = CurrentUser.UserName;
            return await _recognizeReceiveService.Transfer(query.Id.Value, userName);
        }

        /// <summary>
        /// 初始化组件数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("InitData")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>> InitData(RecognizeReceiveInitDataInput input)
        {
            var datalist = new List<RecognizeReceiveDetailsByTypeOutput>();
            var total = input.list != null ? input.list.Count : 0;
            if (total > 0)
            {
                //id
                var recognizeReceiveItemId = input.list.FirstOrDefault().RecognizeReceiveItemId.HasValue ? input.list.FirstOrDefault().RecognizeReceiveItemId.Value : Guid.Empty;
                //获取可认款金额以及总金额
                #region 明细中发票数据
                var invoiceNos = input.list.Where(x => x.Type == (int)RecognizeTypeEnums.Invoice).Select(x => x.Code).ToList();
                var invoices = await _invoiceQueryService.GetInvoiceByInvoiceNos(invoiceNos);
                var rriList = _receiveDetailQueryService.GetIQueryable(x => x.Type == (int)RecognizeTypeEnums.Invoice);
                #endregion
                #region 明细中初始应收
                //明细初始应收
                var query = new CreditQueryInput();
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                query.IsgreaterThanZero = true;
                query.CreditType = "2";
                query.page = 1;
                query.limit = 20;
                var (clist, count) = await _creditQueryService.GetListAsync(query);
                #endregion
                #region 明细中订单
                //明细订单
                var dto = new PageQueryForFinancesInput();
                dto.UserId = _appServiceContextAccessor.Get().UserId;
                dto.PageSize = 20;
                dto.PageNum = 1;
                var recognize = await _recognizeReceiveQueryService.GetItemById(recognizeReceiveItemId);
                if (recognize != null)
                {
                    dto.CompanyId = Guid.Parse(recognize.CompanyId);
                    dto.BusinessDeptId = recognize.BusinessDepId;
                }
                // 默认值
                dto.StartTime = DateTime.Now.AddYears(-1);
                dto.EndTime = DateTime.Now;
                var saleOut = await _sellApiClient.PageQueryForFinancesAsync(dto);
                #endregion

                foreach (var item in input.list)
                {
                    if (item != null)
                    {
                        var model = item.Adapt<RecognizeReceiveDetailsByTypeOutput>();
                        //model.Id = "edit"; //作为修改标识
                        if (item.Type == (int)RecognizeTypeEnums.Invoice)
                        {
                            var invoice = invoices.FirstOrDefault(x => x.InvoiceNo == item.Code);
                            var rr = rriList.Where(x => x.Code == item.Code).ToList();
                            model.TotalAmount = invoices != null ? invoice.InvoiceAmount : 0;
                            model.CanAmount = invoices != null ? (invoice.InvoiceAmount - rr.Sum(x => x.Value) + item.Value) : 0;
                        }
                        else if (item.Type == (int)RecognizeTypeEnums.Orderno)
                        {
                            var order = saleOut != null && saleOut.List.Any() ? saleOut.List.FirstOrDefault(x => x.BillCode == item.Code) : null;
                            model.TotalAmount = order != null ? order.TotalAmount : 0;
                            model.CanAmount = order != null ? order.TotalAmount : 0;
                        }
                        else if (item.Type == (int)RecognizeTypeEnums.Credit)
                        {
                            var entity = clist.FirstOrDefault(x => x.BillCode == item.Code);
                            model.TotalAmount = entity != null ? entity.Value : 0;
                            model.CanAmount = entity != null ? entity.LeftAmount : 0;
                        }
                        model.Type = item.Type;
                        model.RecognizeReceiveItemId = recognizeReceiveItemId;
                        model.CustomerName = item.CustomerName;
                        model.BusinessId = item.Code;
                        model.Amount = item.Value;
                        model.Remark = item.Note;
                        datalist.Add(model);
                    }
                }
            }
            var res = new BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<RecognizeReceiveDetailsByTypeOutput>() { List = datalist, Total = total },
                Total = total
            };
            return res;
        }

        /// <summary>
        /// 根据认款类型查询数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetListByType")]
        public async Task<BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>> GetListByType([FromBody] RecognizeReceiveDetailsByTypeInput input)
        {
            var datalist = new List<RecognizeReceiveDetailsByTypeOutput>();
            int total = 0;
            try
            {
                if (input.Type == (int)RecognizeTypeEnums.Invoice)
                {
                    var query = new InvoicesQueryInput();
                    query.UserId = CurrentUser.Id.Value;
                    query.IsgreaterThanZero = true;
                    query.Status = -1; //排除已作废
                    query.UserName = CurrentUser.UserName;
                    query.searchKey = input.Code;
                    query.page = 1;
                    query.limit = int.MaxValue; //获取全部筛选可认款金额为0的
                    query.CompanyId = input.CompanyId;
                    query.sort = input.sort;
                    if (!string.IsNullOrEmpty(input.CustomerId))
                    {
                        query.CustomerId = input.CustomerId;
                    }
                    if (input.StartDate != "0" && input.EndDate != "0")
                    {
                        query.billDateBeging = input.StartDate;
                        query.billDateEnd = input.EndDate;
                    }
                    var (list, count) = await _invoiceQueryService.GetInvoiceListByRecognizeReceiveAsync(query);
                    total = count;
                    var hospitalQuery = new CreditOfHospitalInput();
                    hospitalQuery.InvoiceNos = list.Select(x => x.InvoiceNo).Distinct().ToList();
                    var hospitalList = await _creditQueryService.GetCreditHospitalByInvoiceNo(hospitalQuery);//根据发票号获取终端客户
                    if (list.Any())
                    {
                        // optimize 批量获取应收剩余可认款金额
                        var recognizeReceiveAmounts = await _recognizeReceiveService.GetRecognizeReceiveAmount(list.Select(p => p.InvoiceNo).ToList(), 1);
                        foreach (var item in list)
                        {
                            var hospitalNameList = hospitalList.Where(p => p.InvoiceNo == item.InvoiceNo).Select(x => x.HospitalName).Distinct().ToList();
                            string hospitalNames = string.Join(",", hospitalNameList);
                            var hospitalIdList = hospitalList.Where(p => p.InvoiceNo == item.InvoiceNo).Select(x => x.HospitalId).Distinct().ToList();
                            string hospitalIds = string.Join(",", hospitalIdList);
                            var invoiceValue = item.InvoiceAmount - Math.Abs(item.RedAmount ??= 0);
                            //发票可认款金额等于发票金额-初始化金额-红冲金额-新表对应发票使用金额和应收金额-记录对应应收用认款金额之和对比，取小
                            decimal? canAmount = invoiceValue;
                            // 初始化发票则加入计算
                            if (item.IsInit.HasValue && item.IsInit.Value)
                            {
                                invoiceValue -= Math.Abs(item.ReceiveAmount ?? 0);
                            }
                            var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.InvoiceNo);
                            if (rra != null)
                            {
                                canAmount = invoiceValue - rra.Amount > rra.CreditSurplusTotalValue ? rra.CreditSurplusTotalValue : invoiceValue - rra.Amount;
                            }
                            var model = new RecognizeReceiveDetailsByTypeOutput();
                            model.Id = item.Id;
                            model.CanAmount = canAmount;
                            model.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                            model.Classify = RecognizeReceiveDetailClassifyEnum.Goods; //默认商品款
                            model.CustomerId = item.CustomerId;
                            model.CustomerName = item.CustomerName;
                            model.HospitalName = item.HospitalName;
                            model.HospitalId = hospitalIds;//终端客户id
                            model.TotalAmount = item.InvoiceAmount;
                            model.BusinessId = item.InvoiceNo;
                            model.Type = input.Type;
                            model.InvoiceTime = item.InvoiceTime;

                            if (model.CanAmount < 0)
                            {
                                model.CanAmount = 0;
                            }
                            model.Amount = model.CanAmount;
                            if (model.Amount != 0)
                            {
                                datalist.Add(model);
                            }
                        }
                    }
                    total = datalist.Count();
                    datalist = datalist.Skip((input.page - 1) * input.limit).Take(input.limit).ToList();
                }
                else if (input.Type == (int)RecognizeTypeEnums.Orderno)
                {
                    var dto = new PageQueryForFinancesInput();
                    dto.UserId = _appServiceContextAccessor.Get().UserId;
                    dto.BillCode = input.Code;
                    dto.PageSize = input.limit;
                    dto.PageNum = input.page;
                    var recognize = await _recognizeReceiveQueryService.GetItemById(input.RecognizeReceiveItemId);
                    if (recognize != null)
                    {
                        dto.CompanyId = Guid.Parse(recognize.CompanyId);
                        //变更核算部门支持
                        var projectList = await _projectApiClient.GetProjectMateList(new Common.Clients.ApiServices.ProjectManage.Inputs.ProjectMateInfoInput { CompanyId = dto.CompanyId });
                        if (projectList == null || projectList.Count <= 0)
                        {
                            return new BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>
                            {
                                Code = CodeStatusEnum.Success,
                                Data = new PageResponse<RecognizeReceiveDetailsByTypeOutput>() { List = datalist, Total = total },
                            };
                            
                        }
                        dto.ProjectIds = projectList.Select(z => z.Id).ToList();

                        //dto.BusinessDeptId = recognize.BusinessDepId;
                    }
                    if (!string.IsNullOrEmpty(input.CustomerId))
                    {
                        dto.CustomerId = Guid.Parse(input.CustomerId);
                    }
                    if (input.StartDate != "0" && input.EndDate != "0")
                    {
                        dto.StartTime = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(input.StartDate));
                        dto.EndTime = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(input.EndDate));
                    }
                    else
                    {
                        // 默认值
                        dto.StartTime = DateTime.UtcNow.AddYears(-5);
                        dto.EndTime = DateTime.UtcNow.AddDays(1);
                    }
                    var saleOut = await _sellApiClient.PageQueryForFinancesAsync(dto);
                    if (saleOut != null)
                    {
                        total = saleOut.Total;
                        if (saleOut.List != null && saleOut.List.Any())
                        {
                            // optimize 批量获取应收剩余可认款金额
                            var codes = saleOut.List.Select(p => p.BillCode).ToList();
                            var recognizeReceiveAmounts = await _recognizeReceiveService.GetRecognizeReceiveAmount(codes, 2);
                            foreach (var sale in saleOut.List)
                            {
                                var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == sale.BillCode);
                                //订单可认款金额等于订单总金额-记录对应应收认款总金额（未生成应收的订单预存一条无应收单号、id的数据）和应收金额-记录对应应收用认款金额之和对比，取小
                                decimal? canAmount = sale.Amount;
                                if (rra != null)
                                {
                                    if (rra.CreditSurplusTotalValue.HasValue)
                                    {
                                        canAmount = sale.Amount - rra.Amount > rra.CreditSurplusTotalValue ? rra.CreditSurplusTotalValue : sale.Amount - rra.Amount;
                                    }
                                    else
                                    {
                                        canAmount = sale.Amount - rra.Amount;
                                    }
                                }
                                var model = new RecognizeReceiveDetailsByTypeOutput();
                                model.Id = sale.Id;
                                model.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                                model.Classify = RecognizeReceiveDetailClassifyEnum.Goods; //默认商品款S
                                model.CustomerId = sale.CustomerId;
                                model.CustomerName = sale.CustomerName;
                                model.HospitalName = sale.HospitalName;//终端客户名
                                model.HospitalId = sale.HospitalId;//终端客户id
                                model.BusinessId = sale.BillCode;
                                model.CanAmount = canAmount;
                                model.TotalAmount = sale.Amount;
                                model.Type = input.Type;
                                model.Classify = sale.SaleType == SaleTypeOutputEnum.ServiceFee ? RecognizeReceiveDetailClassifyEnum.ServiceFee : RecognizeReceiveDetailClassifyEnum.Goods;
                                if (model.CanAmount < 0)
                                {
                                    model.CanAmount = 0;
                                }
                                model.Amount = model.CanAmount;
                                datalist.Add(model);
                            }
                        }
                    }
                }
                else if (input.Type == (int)RecognizeTypeEnums.Credit)
                {
                    var query = new CreditQueryInput();
                    query.UserId = CurrentUser.Id.Value;
                    query.CurrentUserName = CurrentUser.UserName;
                    query.IsgreaterThanZero = true;
                    query.searchKey = input.Code;
                    query.CreditType = "2";
                    query.page = input.page;
                    query.limit = input.limit;
                    query.CompanyId = input.CompanyId;
                    query.AbatedStatus = AbatedStatusEnum.NonAbate;
                    if (!string.IsNullOrEmpty(input.CustomerId))
                    {
                        query.CustomerId = Guid.Parse(input.CustomerId);
                    }
                    if (input.StartDate != "0" && input.EndDate != "0")
                    {
                        query.BillDateS = Convert.ToInt64(input.StartDate);
                        query.BillDateE = Convert.ToInt64(input.EndDate);
                    }
                    var (list, count) = await _creditQueryService.GetListByRecognizeReceiveAsync(query);
                    total = count;
                    if (list.Any())
                    {
                        // optimize 批量获取应收剩余可认款金额
                        var codes = list.Select(p => p.BillCode).ToList();
                        var recognizeReceiveAmounts = await _recognizeReceiveService.GetRecognizeReceiveAmount(codes, 3);
                        foreach (var item in list)
                        {
                            var rra = recognizeReceiveAmounts.FirstOrDefault(x => x.Code == item.BillCode);
                            //初始应收可认款金额等于应收金额-应收已认款金额
                            decimal? canAmount = item.Value;
                            if (rra != null)
                            {
                                canAmount = rra.CreditSurplusTotalValue;
                            }
                            var model = new RecognizeReceiveDetailsByTypeOutput();
                            model.Id = item.Id;
                            model.CanAmount = canAmount;
                            model.Amount = model.CanAmount;
                            model.RecognizeReceiveItemId = input.RecognizeReceiveItemId;
                            model.Classify = RecognizeReceiveDetailClassifyEnum.Goods; //默认商品款
                            model.CustomerId = item.CustomerId.HasValue ? item.CustomerId.ToString() : string.Empty;
                            model.CustomerName = item.CustomerName;
                            model.HospitalName = item.HospitalName;//终端客户名
                            model.HospitalId = item.HospitalId;//终端客户Id
                            model.TotalAmount = item.Value;
                            model.BusinessId = item.BillCode;
                            model.Type = input.Type;
                            if (model.CanAmount < 0)
                            {
                                model.CanAmount = 0;
                            }
                            model.Amount = model.CanAmount;
                            datalist.Add(model);
                        }
                    }
                }
                //发票和订单
                if (input.Type == (int)RecognizeTypeEnums.Invoice || input.Type == (int)RecognizeTypeEnums.Orderno)
                {
                    //获取对应应收列表
                    var creditInfos = await _creditQueryService.GetCreditInfoByRecognizeReceiveCode(new RecognizeReceiveCreditInput
                    {
                        Type = input.Type,
                        Codes = datalist.Select(x => x.BusinessId).ToList(),
                    });
                    if (creditInfos != null && creditInfos.Any())
                    {
                        foreach (var single in datalist)
                        {
                            //组装参数
                            var currentCreditInfo = creditInfos.Where(x => x.RecognizeReceiveDetailCode == single.BusinessId).ToList();
                            single.CreditInfo = currentCreditInfo;
                            single.Amount = single.CreditInfo.Sum(x => x.CurrentValue);
                        }
                    }
                }
                var res = new BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<RecognizeReceiveDetailsByTypeOutput>() { List = datalist, Total = total },
                    Total = total
                };
                return res;
            }
            catch (Exception ex)
            {
                var res = new BaseResponseData<PageResponse<RecognizeReceiveDetailsByTypeOutput>>
                {
                    Code = CodeStatusEnum.Failed,
                    Data = new PageResponse<RecognizeReceiveDetailsByTypeOutput>() { List = datalist, Total = 0 },
                    Total = 0
                };
                return res;
            }
        }

        /// <summary>
        /// 获取应收信息（前端本地查询协助）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetCreditInfos")]
        public async Task<BaseResponseData<List<Application.DTOs.Recognize.CreditInfo>?>> GetCreditInfos([FromBody] CreditInfoQueryInput input)
        {
            var retList = await _creditQueryService.GetCreditInfoByRecognizeReceiveCode(new RecognizeReceiveCreditInput
            {
                Type = input.Type,
                Codes = new List<string?> { input.Code },
            });
            if (retList != null && retList.Any())
            {
                if (!string.IsNullOrEmpty(input.BillCode))
                {
                    retList = retList.Where(x => !string.IsNullOrEmpty(x.BillCode) && x.BillCode.Contains(input.BillCode)).ToList();
                }
                if (!string.IsNullOrEmpty(input.CreditTypeStr))
                {
                    retList = retList.Where(x => !string.IsNullOrEmpty(x.CreditTypeStr) && x.CreditTypeStr.Contains(input.CreditTypeStr)).ToList();
                }
                if (!string.IsNullOrEmpty(input.OrderNo))
                {
                    retList = retList.Where(x => !string.IsNullOrEmpty(x.OrderNo) && x.OrderNo.Contains(input.OrderNo)).ToList();
                }
                if (!string.IsNullOrEmpty(input.ProjectName))
                {
                    retList = retList.Where(x => !string.IsNullOrEmpty(x.ProjectName) && x.ProjectName.Contains(input.ProjectName)).ToList();
                }
                if (!string.IsNullOrEmpty(input.ServiceName))
                {
                    retList = retList.Where(x => !string.IsNullOrEmpty(x.ServiceName) && (x.ServiceName.Contains(input.ServiceName.ToUpper()) || x.ServiceName.Contains(input.ServiceName.ToLower()))).ToList();
                }
            }
            return BaseResponseData<List<Application.DTOs.Recognize.CreditInfo>?>.Success(retList, "查询成功");
        }

        /// <summary>
        /// 校验单条数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("VerifySingleData")]
        public async Task<BaseResponseData> VerifySingleData([FromBody] RecognizeReceiveDetailsInput input)
        {
            input.CreatedBy = CurrentUser.UserName ?? "none";
            var result = await _recognizeReceiveService.VerifySingleData(input);
            return result;
        }

        /// <summary>
        /// 保存上游回款显示日期
        /// </summary>
        /// <param name="input">入参</param>
        /// <returns></returns>
        [HttpPost("SaveBackDateTime")]
        public async Task<BaseResponseData<int>> SaveBackDateTime([FromBody] SaveBackTimeVo input)
        {
            input.UserName = CurrentUser.UserName;
            return await _recognizeReceiveService.SaveBackDateTime(input);
        }

        /// <summary>
        /// 撤销认款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("cancelreceivedetail")]
        public async Task<BaseResponseData<int>> CancelReceiveDetail(PartCancelInput input)
        {
            if (CurrentUser == null)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：当前用户信息无效");
            }
            input.UserName = CurrentUser.UserName;
            return await _recognizeReceiveService.CancelReceiveDetail(input);
        }

        /// <summary>
        /// 撤销暂收款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("cancelreceivetempdetail")]
        public async Task<BaseResponseData<int>> CancelReceiveTempDetail(PartCancelTempInput input)
        {
            input.UserName = CurrentUser.UserName;
            return await _recognizeReceiveService.CancelReceiveTempDetail(input);
        }

        /// <summary>
        /// 导出认款明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("exportDetailsTask")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailsTask([FromBody] ExportDetailsInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _recognizeReceiveService.ExportDetailsTask(query);
        }

        /// <summary>
        /// 导出明细包含应收
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("exportDetailCreditsTask")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailCreditsTask([FromBody] ExportDetailsInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _recognizeReceiveService.ExportDetailCreditsTask(query);
        }
    }
}
