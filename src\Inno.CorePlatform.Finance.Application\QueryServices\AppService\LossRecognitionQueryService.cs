﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Gateway.Client.WeaverOA;
using Inno.CorePlatform.Gateway.Common.WeaverOA;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics.Metrics;
using System.Linq.Expressions;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class LossRecognitionQueryService : QueryAppService, ILossRecognitionQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IBDSApiClient _bDSApiClient;
        protected readonly IPCApiClient _pcApiClient;
        private readonly IWeaverApiClient _weaverApiClient;
        protected readonly IAppServiceContextAccessor _appServiceContextAccessor;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="contextAccessor"></param>
        /// <param name="db"></param>
        /// <param name="bDSApiClient"></param>
        /// <param name="pcApiClient"></param>
        public LossRecognitionQueryService(IAppServiceContextAccessor? contextAccessor, FinanceDbContext db, IBDSApiClient bDSApiClient, IPCApiClient pcApiClient, IWeaverApiClient weaverApiClient) : base(contextAccessor)
        {
            this._db = db;
            this._bDSApiClient = bDSApiClient;
            this._weaverApiClient = weaverApiClient;
            this._appServiceContextAccessor = contextAccessor;
            this._pcApiClient = pcApiClient;
        }
        /// <summary>
        /// 损失确认列表查询Tab数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<LossRecognitionTabCountOutput>> GetTabCount(LossRecognitionQueryInput query)
        {
            var ret = BaseResponseData<LossRecognitionTabCountOutput>.Success("操作成功");

            Expression<Func<LossRecognitionItemPo, bool>> expAll = z => 1 == 1;
            Expression<Func<LossRecognitionItemPo, bool>> expWaitSubmit = z => 1 == 1;
            Expression<Func<LossRecognitionItemPo, bool>> expWaitAudit = z => 1 == 1;
            Expression<Func<LossRecognitionItemPo, bool>> expRefuse = z => 1 == 1;
            Expression<Func<LossRecognitionItemPo, bool>> expComplate = z => 1 == 1;
            Expression<Func<LossRecognitionItemPo, bool>> expMy = z => 1 == 1;

            var data = new LossRecognitionTabCountOutput();
            #region 查询条件
            query.Status = StatusEnum.all;
            expAll = await GetLossRecognitionExp(query, expAll);
            data.AllCount = await _db.LossRecognitionItem.Where(expAll).CountAsync();

            query.Status = StatusEnum.waitSubmit;
            expWaitSubmit = await GetLossRecognitionExp(query, expWaitSubmit);
            data.WaitSubmitCount = await _db.LossRecognitionItem.Where(expWaitSubmit).CountAsync();

            query.Status = StatusEnum.waitAudit;
            expWaitAudit = await GetLossRecognitionExp(query, expWaitAudit);
            data.WaitAuditCount = await _db.LossRecognitionItem.Where(expWaitAudit).CountAsync();

            query.Status = StatusEnum.Refuse;
            expRefuse = await GetLossRecognitionExp(query, expRefuse);
            data.RefuseCount = await _db.LossRecognitionItem.Where(expRefuse).CountAsync();

            query.Status = StatusEnum.Complate;
            expComplate = await GetLossRecognitionExp(query, expComplate);
            data.ComplateCount = await _db.LossRecognitionItem.Where(expComplate).CountAsync();

            query.Status = StatusEnum.My;
            expMy = await GetLossRecognitionExp(query, expMy);
            data.MyCount = await _db.LossRecognitionItem.Where(expMy).CountAsync();
            #endregion


            ret.Data = data;
            return ret;
        }
        /// <summary>
        /// 获取损失确认单表达式
        /// </summary>
        /// <param name="query"></param>
        /// <param name="exp"></param>
        /// <returns></returns>
        private async Task<Expression<Func<LossRecognitionItemPo, bool>>> GetLossRecognitionExp(LossRecognitionQueryInput query, Expression<Func<LossRecognitionItemPo, bool>> exp)
        {
            var user = await _bDSApiClient.GetUserByNamesAsync(new DTOs.BDSData.GetUserInput
            {
                Names = new List<string> { _appServiceContextAccessor.Get().UserName }
            });
            if (user == null || user.Data == null || user.Data.List == null || !user.Data.List.Any())
            {
                exp = exp.And(z => 1 != 1);
                return exp;
            }
            else
            {
                //获取用户数据策略
                var input = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
                var strategry = await _pcApiClient.GetStrategyAsync(input);
                if (strategry != null)
                {
                    var rowStrategies = strategry.RowStrategies;
                    if (!rowStrategies.Keys.Contains("company"))
                    {
                        exp = exp.And(z => 1 != 1);
                        return exp;
                    }
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key.ToLower() == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CompanyId.Value));
                            }
                        }
                        if (key.ToLower() == "customer")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].ToGuidHashSet();
                                exp = exp.And(t => strategList.Contains(t.CustomerId.Value));
                            }
                        }
                    }
                }
            }
            if (query.Id != null)
            {
                exp = exp.And(z => z.Id == query.Id);
            }
            if (query.searchKey != null && !string.IsNullOrWhiteSpace(query.searchKey))//关键字
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.CustomerName, $"%{query.searchKey}%")
                        || EF.Functions.Like(z.CompanyName, $"%{query.searchKey}%"));
            }
            if (query.CompanyId != null)//公司
            {
                exp = exp.And(z => z.CompanyId == query.CompanyId);
            }
            if (query.CustomerId != null)//客户
            {
                exp = exp.And(z => z.CustomerId == query.CustomerId);
            }
            if (query.Status != null && (int)query.Status > -1)//状态
            {
                if ((int)query.Status == 5000)
                {
                    var oaResultPre = await _weaverApiClient.GetToDoList(new WeaverTodoInput(query?.CurrentUser ?? default, WorkFlowCode.LossRecognitionForm.GetDescription()));
                    var oARequestIds = oaResultPre.Data?.Select(z => z.RequestId).ToList() ?? new List<string>();
                    exp = exp.And(c => c.OARequestId != null && oARequestIds.Contains(c.OARequestId) && c.Status == StatusEnum.waitAudit);
                }
                else
                {
                    exp = exp.And(z => z.Status == query.Status);
                    if (query.Status == StatusEnum.waitSubmit) //待提交，需控权，只显示当前账户创建的单据
                    {
                        exp = exp.And(z => z.CreatedBy == query.CurrentUser);
                    }
                }

            }
            if (query.CreditType != null && (int)query.CreditType > -1)//应收类型
            {
                exp = exp.And(z => z.CreditType == query.CreditType);
            }
            if (!string.IsNullOrWhiteSpace(query.BillCode))//单号
            {
                exp = exp.And(z => EF.Functions.Like(z.BillCode ?? "", $"%{query.BillCode}%"));
            }
            return exp;
        }
        /// <summary>
        /// 获取损失确认申请列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<LossRecognitionItemListOutput>, int)> GetListAsync(LossRecognitionQueryInput query)
        {
            Expression<Func<LossRecognitionItemPo, bool>> exp = z => 1 == 1;

            #region 查询条件 
            exp = await GetLossRecognitionExp(query, exp);
            #endregion

            IQueryable<LossRecognitionItemPo> baseQuery = _db.LossRecognitionItem.Where(exp).AsNoTracking();
            var sql = baseQuery.ToQueryString();

            #region 排序
            if (query.sort != null && query.sort.Any())
            {
                for (int i = query.sort.Count - 1; i >= 0; i--)
                {
                    var ss = query.sort[i].Split(',');
                    if (ss.Length > 1 && ss[0] == "billCode")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.BillCode);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.BillCode); }
                    }
                    if (ss.Length > 1 && ss[0] == "createdTime")
                    {
                        if (ss[1] == "desc")
                        {
                            baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
                        }
                        else { baseQuery = baseQuery.OrderBy(z => z.CreatedTime); }
                    }
                }
            }
            else
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion
            //总条数
            var count = await baseQuery.CountAsync();
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(z => z.Adapt<LossRecognitionItemListOutput>()).ToListAsync();
            return (list, count);
        }
        /// <summary>
        /// 获取损失确认申请详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<LossRecognitionDetailQueryOutput>, int)> GetListDetailAsync(LossRecognitionDetailQueryInput query)
        {
            IQueryable<LossRecognitionDetailPo> baseQuery = _db.LossRecognitionDetails.AsNoTracking();
            Expression<Func<LossRecognitionDetailPo, bool>> exp = z => z.LossRecognitionItemId == query.LossRecognitionItemId;
            if (query.Classify != null && (int)query.Classify > 0)
            {
                exp = exp.And(z => z.Classify == query.Classify);//区分类型
            }
            baseQuery = baseQuery.Where(exp);
            baseQuery = baseQuery.OrderByDescending(z => z.BillDate);
            //总条数
            var count = baseQuery.Count();
            //分页
            if (query.PageSize.HasValue && query.PageIndex.HasValue)
            {
                baseQuery = baseQuery.Skip((query.PageIndex.Value - 1) * query.PageSize.Value).Take(query.PageSize.Value);
            }
            var queryResult = await baseQuery.ToListAsync();
            return (queryResult.Select(z => z.Adapt<LossRecognitionDetailQueryOutput>()).ToList(), count);
        }
        /// <summary>
        /// 获取申请单通过id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<LossRecognitionItemListOutput> GetItemById(Guid? id)
        {
            var output = new LossRecognitionItemListOutput();
            if (!id.HasValue)
            {
                return output;
            }
            var item = await _db.LossRecognitionItem.Include(p => p.Details).Where(p => p.Id == id).AsNoTracking().FirstOrDefaultAsync();
            if (item == null)
            {
                return output;
            }
            output = item.Adapt<LossRecognitionItemListOutput>();
            var creditBillCodes = output?.Details?.Select(p => p.CreditBillCode).ToList();
            if (creditBillCodes != null && creditBillCodes.Any())
            {
                var credits = await _db.Credits.Where(p => creditBillCodes.Contains(p.BillCode)).Select(p => new { p.Id, p.BillCode }).ToListAsync();
                foreach (var detail in output.Details)
                {
                    var credit = credits.FirstOrDefault(p => p.BillCode == detail.CreditBillCode);
                    if (credit != null)
                    {
                        detail.CreditId = credit.Id;
                    }
                }
            }
            return output;
        }

    }
}
