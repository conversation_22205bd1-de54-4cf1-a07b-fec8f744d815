﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Credits
{
    public class CreditOfDeliveryInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public List<string> InvoiceNos { get; set; }

        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
    }

    public class CancelCreditInput
    {
        /// <summary>
        /// 销售订单号
        /// </summary>
        public List<string> SaleBillCodes { get; set; } 
    }

    public class GetInitCreditInput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public List<string> BillCodes { get; set; }
    }

    /// <summary>
    /// 分批确认收入入参
    /// </summary>
    public class GetPartialIncomeInput
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 确认金额
        /// </summary>
        public decimal? ConfirmAmount { get; set; }
        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTimeOffset? ConfirmDate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? NoTaxValue { get; set; }
        /// <summary>
        /// 含税成本
        /// </summary>
        public decimal? Cost { get; set; }
        /// <summary>
        /// 不含税成本
        /// </summary>
        public decimal? NoTaxCost { get; set; }
    }
    public class GetPartialIncomeOutput
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
    }
}
