﻿using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService.PurchaseCreatedNotifyService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.PurchasePaymentService
{
    public class GeneratePurchaseCreatedNotifyHandlerFactory : IGeneratePurchaseCreatedNotifyHandlerFactory
    {
        public IHandPurchaseCreatedNotify GetInstance(EventBusDTO input)
        {
            if (input.RelateId != null)
            {
                return new HandPrePayPurcaseCreatedNotify();
            }
            else
            {
                return new HandNotPrePayPurcahseCreatedNotify();
            }
        }
    }
}
