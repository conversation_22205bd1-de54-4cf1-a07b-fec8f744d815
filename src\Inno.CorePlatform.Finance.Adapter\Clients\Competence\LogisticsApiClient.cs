﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using static Inno.CorePlatform.Finance.Application.QueryServices.Inputs.BatchDownLoadInvoiceInput;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
    {
        public class LogisticsApiClient : BaseDaprApiClient<LogisticsApiClient>, ILogisticsApiClient
        {
            private readonly ILogger<LogisticsApiClient> _logger;
            private readonly DaprClient _daprClient;

            public LogisticsApiClient(DaprClient daprClient, ILogger<LogisticsApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
            {
                _daprClient = daprClient;
                _logger = logger;
            }

            /// <summary>
            /// 获取阳采配送点编码
            /// </summary>
            public async Task<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>> getPsdByPurchaseCodeForPm(string purchaseCode)
            {
                return await InvokeMethodAsync<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>>(string.Format(AppCenter.YCgetPsdByPurchaseCodeForPm, purchaseCode), RequestMethodEnum.POST);
            }

            /// <summary>
            /// 获取阳采配送单详情
            /// </summary>
            public async Task<BaseResponseData<List<ShycShipmentDetailOutput>>> getFullDetails(ShycShipmentDetaillnput query)
            {
                return await InvokeMethodWithQueryObjectAsync<ShycShipmentDetaillnput, BaseResponseData<List<ShycShipmentDetailOutput>>>(query, AppCenter.YCgetFullDetails);
            }

            /// <summary>
            /// 获取发票配送单导出数据
            /// </summary>
            public async Task<BaseResponseData<List<InvoiceShipmentOutput>>> getInvoiceShipmentExport(InvoiceShipmentInput query)
            {
                return await InvokeMethodWithQueryObjectAsync<InvoiceShipmentInput, BaseResponseData<List<InvoiceShipmentOutput>>>(query, AppCenter.GetInvoiceShipmentExport);
            }
            

            protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
            {
                var res = await _daprClient.InvokeMethodAsync<TResponse>(request);

                return res;
            }

            protected override string GetAppId()
            {
                return AppCenter.Logistics_APPID;
            }
            /// <summary>
            /// 对账函获取物流打印模板
            /// </summary>
            /// <param name="letterPrintInput"></param>
            /// <returns></returns>
            /// <exception cref="ApplicationException"></exception>
            public async Task<HttpContent> GetReconciliationLetterPDFStream(ReconciliationLetterPrintInput letterPrintInput)
            {

                var request = _daprClient.CreateInvokeMethodRequest(GetAppId(), AppCenter.LOGISTICS_LETTERPDFDOWNLOAD, letterPrintInput);
                request.Method = HttpMethod.Post;
                return await GetLogisticsPDFStream(request);
            }
        }
    }
}
