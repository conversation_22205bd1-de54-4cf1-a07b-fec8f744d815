﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Common.Utility.Permission;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using System.Linq;
using System.Linq.Expressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;


namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    /// <summary>
    /// 查询基类
    /// </summary>
    /// <typeparam name="T">注入日志的子类类型</typeparam>
    /// <typeparam name="QueryEntity">查询实体类型</typeparam>
    /// <typeparam name="OutputEntity">查询结果类型</typeparam>
    public abstract class BaseQueryService<T, QueryEntity, OutputEntity>
        where T : IBaseQueryInterface<OutputEntity>
        where QueryEntity : EntityWithBasicInfo<Guid>
        where OutputEntity : BaseQueryOutput
    {
        /// <summary>
        /// 日志实例
        /// </summary>
        protected readonly ILogger<T> _logger;
        /// <summary>
        /// 数据库实例
        /// </summary>
        protected readonly FinanceDbContext _db;
        /// <summary>
        /// 外部能力中心实例
        /// </summary>
        protected readonly IPCApiClient _pcApiClient;
        /// <summary>
        /// 基本的构造方法，用于依赖注入日志，以及数据库
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="db"></param>
        /// <param name="pcApiClient">数据策略权限中心的注入</param>
        protected BaseQueryService(ILogger<T> logger, FinanceDbContext db, IPCApiClient pcApiClient)
        {
            _db = db;
            _logger = logger;
            _pcApiClient = pcApiClient;
        }
        /// <summary>
        /// 基本的查询列表
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        public async Task<List<OutputEntity>> BaseGetList(BaseQueryInput queryModel)
        {
            var result = await (await BaseGetListIQueryable(queryModel)).ToListAsync();
            return result;
        }
        /// <summary>
        /// 基本的查询语句生成
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        protected async Task<IQueryable<OutputEntity>> BaseGetListIQueryable(BaseQueryInput queryModel)
        {
            var baseQuery =  BuildQuery(queryModel);
            baseQuery = await AddStrategyQueryAsync(queryModel, baseQuery);
            var orderQuery = OrderByQuery(baseQuery);
            return SelectQuery(orderQuery);
        }
        /// <summary>
        /// 基本的过滤和筛选
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        private IQueryable<QueryEntity> BuildQuery(BaseQueryInput queryModel)
        {
            var query = GenerateBaseDbContextQuery();
            Expression<Func<QueryEntity, bool>> baseExp = p => p.IsDeleted == false;
            baseExp = BuildCondition(queryModel, baseExp);
            return query = query.Where(baseExp);
        }

        /// <summary>
        /// 子类实现具体查询哪个PO
        /// </summary>
        /// <returns></returns>
        protected abstract IQueryable<QueryEntity> GenerateBaseDbContextQuery();
        /// <summary>
        /// 子类可以扩展查询筛选条件
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="exp"></param>
        protected virtual Expression<Func<QueryEntity, bool>> BuildCondition(BaseQueryInput queryModel, Expression<Func<QueryEntity, bool>> exp)
        {

            if (queryModel.Status.HasValue && queryModel.Status != -1)
            {
                exp = exp.And(z => z.Status == queryModel.Status);
                if (queryModel.Status == (int)PurchaseStatusEnums.Draft)
                {
                    exp = exp.And(z => z.CreatedBy == queryModel.CreatedBy);

                }
            }
            if (queryModel.BeginCreatedTime > 0)
            {
                exp = exp.And(t => t.CreatedTime >= Utility.ConvertToOffSet(queryModel.BeginCreatedTime));
            }
            if (queryModel.EndCreatedTime > 0)
            {
                exp = exp.And(t => t.CreatedTime <= Utility.ConvertToOffSet(queryModel.EndCreatedTime));
            }
            if (queryModel.BeginUpdatedTime > 0)
            {
                exp = exp.And(t => t.UpdatedTime >= Utility.ConvertToOffSet(queryModel.BeginUpdatedTime));
            }
            if (queryModel.EndUpdatedTime > 0)
            {
                exp = exp.And(t => t.UpdatedTime <= Utility.ConvertToOffSet(queryModel.EndUpdatedTime));
            }
            return exp;
        }
        /// <summary>
        /// 数据策略权限生效的状态
        /// </summary>
        /// <param name="queryModel"></param>
        /// <returns></returns>
        protected abstract bool EffectiveStrategyCondition(BaseQueryInput queryModel);
        /// <summary>
        /// 数据策略权限增加
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected virtual async Task<IQueryable<QueryEntity>> AddStrategyQueryAsync(BaseQueryInput queryModel, IQueryable<QueryEntity> query)
        {
            if (queryModel.StrategyQuery != null)
            {
                if (EffectiveStrategyCondition(queryModel))//数据策略权限生效的状态
                {
                    var strategys = await _pcApiClient.GetStrategyAsync(queryModel.StrategyQuery);
                    if (strategys != null && strategys.RowStrategies.Count > 0)
                    {
                        query = AnalysisStrategy(strategys.RowStrategies, query);
                    }
                    else
                    {
                        query = query.Where(p => 1 != 1);
                    }
                }

            }

            return query;
        }
        /// <summary>
        /// 数据策略权限具体到子类去实现，因为父类的这个解析没办法适配所有类型
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected abstract IQueryable<QueryEntity> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<QueryEntity> query);
        /// <summary>
        /// 子类实现列表返回字段
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        protected abstract IQueryable<OutputEntity> SelectQuery(IQueryable<QueryEntity> query);

        /// <summary>
        /// 可能不同业务申请会有排序不同，这里进行封装，子类可自己重写，实现特殊的排序
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        protected virtual IOrderedQueryable<QueryEntity> OrderByQuery(IQueryable<QueryEntity> query)
        {
            return query.OrderByDescending(c => c.CreatedTime);//默认按照创建时间降序排序
        }


    }
}
