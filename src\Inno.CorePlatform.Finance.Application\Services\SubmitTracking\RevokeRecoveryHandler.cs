using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Helpers;
using Inno.CorePlatform.Finance.Application.Services.InterfaceInvocation;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitTracking
{
    /// <summary>
    /// 撤销恢复处理器，用于实现撤销操作失败时的恢复方法
    /// </summary>
    public class RevokeRecoveryHandler : IRevokeRecoveryHandler
    {
        private readonly ILogger<RevokeRecoveryHandler> _logger;
        private readonly IInterfaceInvocationService _interfaceInvocationService;
        private readonly MergeInputBillHelper _mergeInputBillHelper;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="interfaceInvocationService">接口调用服务</param>
        /// <param name="mergeInputBillHelper">合并进项票辅助类</param>
        public RevokeRecoveryHandler(
            ILogger<RevokeRecoveryHandler> logger,
            IInterfaceInvocationService interfaceInvocationService,
            MergeInputBillHelper mergeInputBillHelper)
        {
            _logger = logger;
            _interfaceInvocationService = interfaceInvocationService;
            _mergeInputBillHelper = mergeInputBillHelper;
        }

        /// <summary>
        /// 恢复金蝶撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>恢复结果</returns>
        public async Task<KingdeeApiResult> RecoverKingdeeRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string currentUserName)
        {
            _logger.LogInformation("RecoverKingdeeRevokeAsync - 开始恢复金蝶撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 获取原始发票号列表
                var originalInvoiceNumbers = await _mergeInputBillHelper.GetOriginalInvoiceNumbersStringByMergeIdAsync(mergeInputBill.Id);
                _logger.LogInformation("RecoverKingdeeRevokeAsync - 获取到原始发票号: {OriginalInvoiceNumbers}", originalInvoiceNumbers);

                // 调用金蝶多发票指定应付接口，不创建MergeInputBillDebts记录
                var result = await _interfaceInvocationService.InvokeKingdeeManyInvoiceSpecifyApFin(
                    mergeInputBill, submitDetails, currentUserName, shouldCreateDebtRecords: false);

                _logger.LogInformation("RecoverKingdeeRevokeAsync - 恢复金蝶撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverKingdeeRevokeAsync - 恢复金蝶撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return KingdeeApiResult.Failure($"恢复金蝶撤销（重新提交）失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复经销购货入库撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverDistributionPurchaseRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverDistributionPurchaseRevokeAsync - 开始恢复经销购货入库撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用库存能力中心更新入库单明细接口
                var result = await _interfaceInvocationService.InvokeUpdateManyStoreInDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverDistributionPurchaseRevokeAsync - 恢复经销购货入库撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverDistributionPurchaseRevokeAsync - 恢复经销购货入库撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复经销购货入库撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复寄售转购货撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverConsignmentToPurchaseRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverConsignmentToPurchaseRevokeAsync - 开始恢复寄售转购货撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用采购能力中心更新寄售转购货明细接口
                var result = await _interfaceInvocationService.InvokeUpdateConsignToPurchaseDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverConsignmentToPurchaseRevokeAsync - 恢复寄售转购货撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverConsignmentToPurchaseRevokeAsync - 恢复寄售转购货撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复寄售转购货撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复服务费采购撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverServiceFeeProcurementRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverServiceFeeProcurementRevokeAsync - 开始恢复服务费采购撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用服务费采购更新接口
                var result = await _interfaceInvocationService.InvokeUpdateServiceFeeProcurementDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverServiceFeeProcurementRevokeAsync - 恢复服务费采购撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverServiceFeeProcurementRevokeAsync - 恢复服务费采购撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复服务费采购撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复经销调出撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverDistributionTransferRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverDistributionTransferRevokeAsync - 开始恢复经销调出撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用库存能力中心更新经销调出明细接口
                var result = await _interfaceInvocationService.InvokeUpdateDistributionTransferDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverDistributionTransferRevokeAsync - 恢复经销调出撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverDistributionTransferRevokeAsync - 恢复经销调出撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复经销调出撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复购货修订撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverPurchaseRevisionRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverPurchaseRevisionRevokeAsync - 开始恢复购货修订撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用采购能力中心更新购货修订明细接口
                var result = await _interfaceInvocationService.InvokeUpdatePurchaseRevisionDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverPurchaseRevisionRevokeAsync - 恢复购货修订撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverPurchaseRevisionRevokeAsync - 恢复购货修订撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复购货修订撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复换货转退货撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">提交明细</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverExchangeToReturnRevokeAsync(
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverExchangeToReturnRevokeAsync - 开始恢复换货转退货撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用库存能力中心更新换货转退货明细接口
                var result = await _interfaceInvocationService.InvokeUpdateExchangeToReturnDetail(mergeInputBill, details);

                _logger.LogInformation("RecoverExchangeToReturnRevokeAsync - 恢复换货转退货撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverExchangeToReturnRevokeAsync - 恢复换货转退货撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复换货转退货撤销（重新提交）失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 恢复损失确认撤销（重新提交）
        /// </summary>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="details">明细列表</param>
        /// <returns>恢复结果</returns>
        public async Task<BaseResponseData<bool>> RecoverLossRecognitionRevokeAsync(MergeInputBillPo mergeInputBill, List<MergeInputBillSubmitDetailPo> details)
        {
            _logger.LogInformation("RecoverLossRecognitionRevokeAsync - 开始恢复损失确认撤销（重新提交）, MergeInvoiceNumber: {MergeInvoiceNumber}",
                mergeInputBill.MergeInvoiceNumber);

            try
            {
                // 调用损失确认更新接口
                var result = await _interfaceInvocationService.InvokeUpdateLossRecognitionDetail(mergeInputBill, details);

                // 检查结果
                if (result == null || result.Code != CodeStatusEnum.Success)
                {
                    string errorMessage = result?.Message ?? "返回结果为空";
                    _logger.LogError("RecoverLossRecognitionRevokeAsync - 恢复损失确认撤销（重新提交）失败, 错误: {ErrorMessage}", errorMessage);

                    return new BaseResponseData<bool>
                    {
                        Code = CodeStatusEnum.Failed,
                        Message = $"恢复损失确认撤销（重新提交）失败: {errorMessage}"
                    };
                }

                _logger.LogInformation("RecoverLossRecognitionRevokeAsync - 恢复损失确认撤销（重新提交）成功, MergeInvoiceNumber: {MergeInvoiceNumber}",
                    mergeInputBill.MergeInvoiceNumber);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Success,
                    Message = "恢复损失确认撤销（重新提交）成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RecoverLossRecognitionRevokeAsync - 恢复损失确认撤销（重新提交）失败, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInputBill.MergeInvoiceNumber, ex.Message);

                return new BaseResponseData<bool>
                {
                    Code = CodeStatusEnum.Failed,
                    Message = $"恢复损失确认撤销（重新提交）失败: {ex.Message}"
                };
            }
        }
    }
}
