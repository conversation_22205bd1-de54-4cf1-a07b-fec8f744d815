﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 销项发票查询
    /// </summary>
    public class InvoiceCreditQueryInput:BaseQuery
    {
        /// <summary>
        /// 发票类型
        /// </summary> 
        public string? Type { get; set; }
        /// <summary>
        /// 发票号
        /// </summary> 
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName {  get; set; }
        /// <summary>
        /// 客户 付款单位
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称  收款单位
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        ///  货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        ///  规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        ///  编号  开票单
        /// </summary>
       
        public string? Code { get; set; }

        /// <summary>
        /// 单号   应收单
        /// </summary> 
        public string? BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateBeging { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public string? billDateEnd { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string? CreateDateBeging { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string? CreateDateEnd { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string? CreateName { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary> 
        public Guid? ServiceId { get; set; }
        public string? ServiceName { get; set; }


        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 1=作废，反之全部
        /// </summary>
        public int? Status { get; set; }
        /// <summary>
        ///是否需要数据策略
        /// </summary>
        public bool? IsStrategy {  get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
    }

    /// <summary>
    /// 分批确认收入数据查询
    /// </summary>
    public class CreditSureIncomeQueryInput
    {
        /// <summary>
        /// 应收单id
        /// </summary>
        public Guid? CreditId { get; set; }
    }
}
