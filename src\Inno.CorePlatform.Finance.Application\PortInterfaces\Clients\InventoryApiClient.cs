﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IInventoryApiClientOfResponseData
    {

        Task<BaseResponseData<string>> CreateExchangeInventory(ExchangeInventoryCreateRequestDto request);

        Task<BaseResponseData<string>> CreateSureIncomeInventory(SureIncomeInventoryCreateRequestDto request);
    }
    /// <summary>
    /// 库存能力中心
    /// </summary>
    public interface IInventoryApiClient
    {
        Task<InventoryStoreInOutput> QueryStoreInByCode(string storeInCode);
        Task<List<InventoryStoreInOutput>> QueryStoreInByCodes(List<string?>? storeInCodes);
        Task<InventoryStoreOutOutput> QueryStoreOutByCode(string storeOutCode);
        Task<List<InventoryStoreOutOutput>> QueryStoreOutByCodes(List<string?>? storeOutCode);

        /// <summary>
        /// 查询追溯信息
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<List<TraceCodeOutput>> QueryTraceInfoByCodes(List<string> codes);
        /// <summary>
        ///根据公司查询入库单号和入库明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<InventoryStoreInOutput>> QueryStoreInByCompany(StoreInDetailQueryInput input);
        /// <summary>
        /// 查询换货转退货明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<InventoryStoreInOutput>> QueryDetailForInputInvoice(StoreInDetailQueryInput input);


        /// <summary>
        /// 修改入库单详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<object>> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> input);

        #region 经销调出
        Task<InventoryRespon<StoreOutDetailForFinanceOutput>> QueryStoreOutByCompany(StoreInDetailQueryInput input);
        Task<InventoryRespon<object>> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> input);

        Task<InventoryRespon<object>> UpdateInvoiceInfoRevoke(List<InventoryStoreOutUpdateDetail> input);
        #endregion


        Task<List<Guid>> GetNoStoreRoomCompanys(List<Guid> companyIds);

        Task<CheckDataWithKingdeeOutputDto> QueryTempStoreOutToKingdee(CheckDataWithKingdeeInputDto input);

        Task<InventoryStoreExchangeBackOutput> QueryByCodeForFinance(string billCode);
        Task<List<QuerySaleRecallDetailRecOutput>> QuerySaleRecallDetailRec(QuerySaleRecallDetailRecInput input);
        /// <summary>
        /// 财务付款卡控,随货同行单校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<WaybillInfoDetailsOutput> WaybillInfoValidForFinance(WaybillInfoDetailsInput input);
    }
}
