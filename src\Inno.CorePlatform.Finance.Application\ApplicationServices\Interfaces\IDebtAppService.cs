﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data.Migrations;
using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.Interfaces
{
    /// <summary>
    /// 应付相关操作功能接口
    /// </summary>
    public interface IDebtAppService
    {
        /// <summary>
        /// 更新付款计划预计付款日期
        /// </summary>
        /// <param name="debtDetail"></param>
        /// <param name="ProbablyPayTime"></param>
        /// <returns></returns>
        Task UpdateProbablyPayTime(DebtDetailPo debtDetail, DateTime? ProbablyPayTime);

        /// <summary>
        /// 预计付款日期审核完成
        /// </summary>
        /// <param name="debtDetailAuditId"></param>
        /// <returns></returns>
        Task FinishChangeProbablyPayTime(Guid debtDetailAuditId);
    }
}
