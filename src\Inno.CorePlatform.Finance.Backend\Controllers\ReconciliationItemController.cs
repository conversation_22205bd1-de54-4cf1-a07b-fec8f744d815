﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReconciliationItemController : BaseController
    {
        private readonly IReconciliationItemAppService _reconciliationItemAppService;
        public ReconciliationItemController(
            IReconciliationItemAppService reconciliationItemAppService, ISubLogService subLog) : base(subLog)
        {
            this._reconciliationItemAppService = reconciliationItemAppService;
        }

        [HttpPost("Add")]
        public async Task<BaseResponseData<string>> Add(ReconciliationItemInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            return await _reconciliationItemAppService.Add(input);
        }

        [HttpPost("del")]
        public async Task<BaseResponseData<string>> Del(ReconciliationItemInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            return await _reconciliationItemAppService.Del(input);
        }
    }
}
