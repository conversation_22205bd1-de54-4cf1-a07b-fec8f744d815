﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Newtonsoft.Json;
using Npoi.Mapper;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 退货出库
    /// </summary>
    public class StoreOutReturnAppService : BaseAppService, IStoreOutReturnAppService
    {
        private IInventoryApiClient _inventoryApiClient;
        private IBDSApiClient _bDSApiClient;
        private IKingdeeApiClient _kingdeeApiClient;
        private IProjectMgntApiClient _projectMgntApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        public StoreOutReturnAppService(
            ICreditRepository creditItemRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork _unitOfWork,
            IBDSApiClient bDSApiClient,
            IInventoryApiClient inventoryApiClient,
            IKingdeeApiClient kingdeeApiClient,
            IDomainEventDispatcher? deDispatcher,
            IProjectMgntApiClient projectMgntApiClient,
            IPurchaseApiClient purchaseApiClient,
            IAppServiceContextAccessor? contextAccessor) :
            base(creditItemRepository, debtRepository, subLogRepository, _unitOfWork, deDispatcher, contextAccessor)
        {
            this._bDSApiClient = bDSApiClient;
            this._inventoryApiClient = inventoryApiClient;
            this._kingdeeApiClient = kingdeeApiClient;
            this._projectMgntApiClient = projectMgntApiClient;
            this._purchaseApiClient = purchaseApiClient;
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var storeout = await _inventoryApiClient.QueryStoreOutByCode(input.BusinessCode);
                if (storeout == null || !storeout.Details.Any())
                {
                    throw new Exception("订阅出库事件出错，原因：查询上游单据时未获取到相关数据");
                }
                var requestBody = JsonConvert.SerializeObject(input);
                var ret = await CreateDebtForPurchaseReturnStoreOut(storeout,input.BusinessSubType, requestBody,input.IsAutoBill);
                return ret;
            }
            catch (Exception ex)
            {
                throw;// new Exception("订阅出库事件出错，可能是上游单据接口异常，或者生成应付代码出错");
            }
        }
        /// <summary>
        /// 经销采退应付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private async Task<BaseResponseData<int>> CreateDebtForPurchaseReturnStoreOut(InventoryStoreOutOutput input,
            string classify,
            string preRequestBody,
            bool? isAutoBill=false)
        {
            var check = await base.IsCreatedDebtForBill(input.storeOutCode);
            if (check)
            {
                if (isAutoBill.HasValue && isAutoBill.Value)
                {
                    return BaseResponseData<int>.Success("操作成功:但是该数据已存在");
                }
                else
                {
                    throw new Exception("该单据已生成过应收");
                }
            }
            var ret = BaseResponseData<int>.Success("操作成功");
            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            if (companyInfo != null)
            {
                var serviceIds = input.Details.Where(p => p.businessUnitId.HasValue).Select(p => p.businessUnitId.Value).Distinct().ToList();
                var services = await _bDSApiClient.GetServiceMetaAsync(new CompetenceCenter.BDSCenter.Inputs.ServiceMetaInput
                {
                    ids = serviceIds.Select(p => p.ToString()).ToList()
                });
                var productNameIds = input.Details.Select(p => p.productNameId).Distinct().ToList();
                var productNameInfos = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);
                var projectIds = input.Details.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);
                var groupDetails = input.Details.GroupBy(p => new { p.businessUnitId, p.projectId, p.purchaseOrderCode });
                var billDate = DateTime.Parse(await _bDSApiClient.GetSystemMonth(companyInfo.companyId));
                var agents = input.agentId != null ? await _bDSApiClient.GetAgentBankInfoByAgentIds(new List<Guid>() { input.agentId.Value }) : null;
                var index = 1;
                var insertList = new List<DebtDto>();
                var kingdeeDebts = new List<KingdeeDebt>();
                if (string.IsNullOrEmpty(input.checker))
                {
                    throw new Exception("该单据没有复核人");
                }
                foreach (var g in groupDetails)
                {
                    var purchaseOrder = new PurchaseQueryInfoSimpleOutput();

                    if (g.Key.purchaseOrderCode!=null)
                    { 
                        purchaseOrder= await _purchaseApiClient.GetSimpleByCode(g.Key.purchaseOrderCode);
                    }
                    var thisProjectInfo = projectInfo.FirstOrDefault(t => t.Id == g.Key.projectId);
                    var debt = new DebtDto
                    {
                        AbatedStatus = (int)AbatedStatusEnum.NonAbate,
                        BillCode = $"{input.storeOutCode}-{index.ToString().PadLeft(3, '0')}",
                        BillDate = billDate, //DateTimeHelper.LongToDateTime(input.billDate),
                        CreatedBy = purchaseOrder == null ? input.checker : purchaseOrder.CreatedBy,
                        CreatedTime = DateTime.UtcNow,
                        Id = Guid.NewGuid(),
                        Value = -Math.Round(g.Sum(p => p.quantity * (p.originCost ?? 0)),2),
                        AgentId = input.agentId,
                        AgentName = input.agentName,
                        IsInnerAgent=agents?.FirstOrDefault()?.agentIsZXInternal,
                        CompanyId = input.companyId,
                        CompanyName = companyInfo.companyName,
                        RelateCode = input.storeOutCode,
                        DebtType = DebtTypeEnum.selfreturn,
                        NameCode = companyInfo.nameCode,
                        ServiceId = g.Key.businessUnitId,
                        BusinessDeptFullName = input.businessDeptFullName,
                        BusinessDeptFullPath = input.businessDeptFullPath,
                        BusinessDeptId = input.businessDeptId.ToString(),
                        ProjectId = g.Key.projectId,
                        ProjectCode = thisProjectInfo?.Code,
                        ProjectName = thisProjectInfo?.Name,
                        OrderNo = g.Key.purchaseOrderCode,
                        PurchaseCode = g.Key.purchaseOrderCode,
                        ProducerOrderNo = purchaseOrder?.ProducerOrderNo,
                        PurchaseContactNo = purchaseOrder?.Contract?.Code,
                        RMBAmount = -(g.Sum(p => p.quantity * (p.rmbAmount ?? 0))),
                        CoinCode = string.IsNullOrEmpty(g.First().coinAttribute) || g.First().coinAttribute == "CNY" ? "CNY" : g.First().coinAttribute,
                        CoinName = string.IsNullOrEmpty(g.First().coinName) || g.First().coinAttribute == "CNY" ? "人民币" : g.First().coinName,
                    }; 
                    if (purchaseOrder != null && !string.IsNullOrEmpty(purchaseOrder.RelateCode) && purchaseOrder.RelateCode.ToUpper().Contains("-PUS-"))
                    {
                        debt.RebateNo = purchaseOrder.RelateCode;
                    }
                    if (g.Key.businessUnitId.HasValue)
                    {
                        debt.ServiceName = services.FirstOrDefault(t => t.id.ToLower() == g.Key.businessUnitId.Value.ToString().ToLower())?.name;
                    }
                    #region 包装金蝶应付参数
                    InitKingdeeDebt(productNameInfos, kingdeeDebts, g, debt);
                    #endregion
                    if (debt.Value != 0)
                        insertList.Add(debt);
                    index++;
                }
                var kingdeeRes = await _kingdeeApiClient.PushDebtsToKingdee(kingdeeDebts,classify,preRequestBody);
                if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                {
                    await base.CreateManyDebts(insertList);
                    await _unitOfWork.CommitAsync();
                }
                else
                {
                    throw new Exception("生成应付到金蝶系统失败，原因：" + kingdeeRes.Message);
                }
            }
            else
            {
                throw new Exception("未找到对应公司公司");
            }
            return ret;
        }

        private static void InitKingdeeDebt(List<ProductNameInfoOutput> productNameInfos, List<KingdeeDebt> kingdeeDebts, IGrouping<object, Detail> g, DebtDto debt)
        {
            var kingdeeDebt = new KingdeeDebt()
            {
                asstact_number1 = debt.AgentId.Value,
                billno = debt.BillCode,
                bizdate = debt.BillDate.Value,
                org_number = debt.NameCode,
                payorg_number = debt.NameCode,
                jfzx_business_number = debt.BusinessDeptId,
                jfzx_order_number = debt.PurchaseCode,
                jfzx_creator = debt.CreatedBy ?? "none",
                billtypeid_number = KingdeeHelper.TransferDebtType(debt.DebtType.Value),
                currency_number = debt.CoinCode ?? "CNY",
                pricetaxtotal4 = debt.Value,
            };
            var kingdeeDebtDetails = new List<KingdeeDebtDetail>();
            var amount = 0m;
            g.ToList().GroupBy(a => new { a.productId, a.originCost, a.taxRate, a.rmbAmount }).ForEach(t =>
            {
                var d = new KingdeeDebtDetail();
                d.taxrate = string.IsNullOrEmpty(debt.CoinCode) || debt.CoinCode == "CNY" ? t.Key.taxRate.Value : 0; 
                d.e_amountbaseMany = t.Key.rmbAmount.HasValue ? t.Key.rmbAmount.Value * Math.Abs(d.quantity) : 0;
                d.quantity = t.Sum(b => b.quantity);
                d.pricetax = t.Key.originCost.Value;
                d.jfzx_project_number = debt.ProjectCode;//项目单号 预留
                var thisProductInfo = productNameInfos.FirstOrDefault(e => e.productNameId == t.First().productNameId);
                if (thisProductInfo.classificationNewGuid.HasValue)
                {
                    d.material_number1 = thisProductInfo.classificationNewGuid.ToString();
                }
                else
                {
                    d.material_number1 = thisProductInfo.classificationGuid.ToString();
                }
                kingdeeDebtDetails.Add(d);

                //应付不含税单价
                d.price2 = Math.Round((d.pricetax / (1 + d.taxrate / 100.00M)), 20);
                amount += d.price2 * d.quantity;
            });
            //应付不含税总额
            kingdeeDebt.amount2 = Math.Round(amount, 2);
            kingdeeDebt.billEntryModels = kingdeeDebtDetails;
            kingdeeDebts.Add(kingdeeDebt);
        }
    }
}
