﻿namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 冲销查询，出参
    /// </summary>
    public class AbatementQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 冲销的单据号
        /// </summary>
        public string CreditBillCode { get; set; }

        /// <summary>
        /// 冲销的单据类型
        /// </summary>
        public string CreditType { get; set; }

        /// <summary>
        /// 被冲销的单据号
        /// </summary>
        public string DebtBillCode { get; set; }

        /// <summary>
        /// 被重冲销的单据类型
        /// </summary>
        public string DebtType { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 冲销日期
        /// </summary>
        public DateTime Abtdate { get; set; }


        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string NameCode { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>  
        public string? AgentName { get; set; }

        /// <summary>
        /// 单据价值 (应收)
        /// </summary>
        public decimal CreditValue { get; set; }

        /// <summary>
        /// 单据价值 (应付)
        /// </summary>
        public decimal DebtValue { get; set; }

    }
}
