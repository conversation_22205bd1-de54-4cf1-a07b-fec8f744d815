using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Optimized
{
    /// <summary>
    /// 金蝶API重试策略配置
    /// </summary>
    public static class KingdeeRetryPolicy
    {
        /// <summary>
        /// 获取HTTP重试策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(ILogger logger)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError() // 处理HttpRequestException和5XX、408状态码
                .Or<TaskCanceledException>() // 处理超时
                .OrResult(msg => !msg.IsSuccessStatusCode && ShouldRetry(msg.StatusCode))
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // 指数退避
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        logger.LogWarning("金蝶API调用重试 - 第{RetryCount}次，等待{Delay}ms，原因：{Reason}",
                            retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
                    });
        }

        /// <summary>
        /// 获取断路器策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy(ILogger logger)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: 5,
                    durationOfBreak: TimeSpan.FromSeconds(30),
                    onBreak: (exception, duration) =>
                    {
                        logger.LogError("金蝶API断路器开启 - 持续时间：{Duration}s，原因：{Reason}",
                            duration.TotalSeconds, exception.Exception?.Message ?? exception.Result?.StatusCode.ToString());
                    },
                    onReset: () =>
                    {
                        logger.LogInformation("金蝶API断路器重置");
                    });
        }

        /// <summary>
        /// 获取超时策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy()
        {
            return Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromMinutes(3));
        }

        /// <summary>
        /// 获取组合策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetCombinedPolicy(ILogger logger)
        {
            var retryPolicy = GetRetryPolicy(logger);
            var circuitBreakerPolicy = GetCircuitBreakerPolicy(logger);
            var timeoutPolicy = GetTimeoutPolicy();

            return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy, timeoutPolicy);
        }

        /// <summary>
        /// 判断是否应该重试
        /// </summary>
        private static bool ShouldRetry(HttpStatusCode statusCode)
        {
            return statusCode switch
            {
                HttpStatusCode.RequestTimeout => true,
                HttpStatusCode.TooManyRequests => true,
                HttpStatusCode.InternalServerError => true,
                HttpStatusCode.BadGateway => true,
                HttpStatusCode.ServiceUnavailable => true,
                HttpStatusCode.GatewayTimeout => true,
                _ => false
            };
        }
    }

    /// <summary>
    /// 金蝶API配置选项
    /// </summary>
    public class KingdeeApiOptions
    {
        public const string SectionName = "KingdeeApi";

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 180;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 断路器失败阈值
        /// </summary>
        public int CircuitBreakerThreshold { get; set; } = 5;

        /// <summary>
        /// 断路器开启持续时间（秒）
        /// </summary>
        public int CircuitBreakerDurationSeconds { get; set; } = 30;

        /// <summary>
        /// 是否启用详细日志
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;

        /// <summary>
        /// 令牌缓存时间（分钟）
        /// </summary>
        public int TokenCacheMinutes { get; set; } = 59;
    }
}
