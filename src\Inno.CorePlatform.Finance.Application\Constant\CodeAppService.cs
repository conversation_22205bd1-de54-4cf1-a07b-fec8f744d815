﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Common.DTO.CodeGeneration;
using Inno.CorePlatform.ServiceClient;

namespace Inno.CorePlatform.Finance.Application.Constant
{ 
    public class CodeAppService
    {
        private readonly ICodeGenClient client;
        public CodeAppService(ICodeGenClient client) {
            this.client = client;
        }
        /// <summary>
        /// 单号生成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<ApplyCodeOutput> SetCode(ApplyCodeInput input)
        {
            ApplyCodeInput inputCode = new ApplyCodeInput();
            inputCode.BusinessArea = input.BusinessArea;
            inputCode.CompanyCode = input.CompanyCode;
            inputCode.BillType = input.BillType;
            inputCode.SysMonth = string.IsNullOrEmpty(input.SysMonth)?DateTime.Now.ToString("yyyy-MM"):input.SysMonth;
            inputCode.Num = 1;
            var output = await client.ApplyCode(inputCode);
            return output;
        }
    }
}
