﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class SaleOutputNew
    {
        /// <summary>
        /// 单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; }

        /// <summary>
        /// 公司编号
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 业务单元编号
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 事业部
        /// </summary>
        public BusinessDeptOutput BusinessDeptInfo { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }

        /// <summary>
        /// 明细
        /// </summary>
        public List<SaleDetailOutput> SaleDetails { get; set; }

        /// <summary>
        /// 核销明细
        /// </summary>
        public List<TempInventoryDetailOutput> TempInventoryDetails { get; set; }
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 修订明细
        /// </summary>
        public List<SaleReviseDetailDto> SaleReviseDetails { get; set; }
        /// <summary>
        /// 订单来源
        /// </summary>
        public SaleSourceEnum Source { get; set; }
    }
}
