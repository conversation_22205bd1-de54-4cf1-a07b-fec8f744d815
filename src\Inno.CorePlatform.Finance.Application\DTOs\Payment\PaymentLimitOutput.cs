﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Payment
{
    public class PaymentLimitOutput
    {
        /// <summary>
        /// 采购单号
        /// </summary> 
        public string PurchaseCode { get; set; }

        /// <summary>
        /// 总付款金额
        /// </summary>
        public decimal PaymentTotalValue { get; set; }

        /// <summary>
        /// 已冲销总金额
        /// </summary>
        public decimal AbatementTotalValue { get; set; }

        /// <summary>
        /// 未使用总金额
        /// </summary>
        public decimal NonUseTotalValue
        {
            get
            {
                var ret = 0;
                if (PaymentTotalValue > 0 && AbatementTotalValue > 0)
                {
                    return PaymentTotalValue - AbatementTotalValue;
                }
                return ret;
            }
        }

    }
}
