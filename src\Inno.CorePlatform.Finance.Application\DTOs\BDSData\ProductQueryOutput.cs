﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class ProductQueryOutput
    {
        public List<ProductItem> Content { get; set; }
    }

    public class ProductItem
    {

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }


        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 厂家ID
        /// </summary>

        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>

        public string? ProducerName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnitDesc { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 包装规格
        /// </summary>
        public string? PackDes { get; set; }

        /// <summary>
        /// 产品描述
        /// </summary>
        public string? ProductDes { get; set; }

        /// <summary>
        /// 注册证号
        /// </summary>
        public string? RegistrationNo { get; set; }
        /// <summary>
        /// 医保代码信息集合
        /// </summary>
        public List<MedicalCode>? MedicalCodes { get; set; }
    }
    /// <summary>
    /// 医保代码信息
    /// </summary>
    public class MedicalCode
    {
        /// <summary>
        /// 代码值
        /// </summary>
        public string codeValue { get; set; }
        /// <summary>
        /// 医保代码类型
        /// </summary>
        public string medicalCodeType { get; set; }
        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid productId { get; set; }
    }
}
