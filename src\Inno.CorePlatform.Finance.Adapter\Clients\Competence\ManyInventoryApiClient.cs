using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    /// <summary>
    /// 多对多勾稽库存能力中心客户端
    /// </summary>
    public class ManyInventoryApiClient : BaseDaprApiClient<ManyInventoryApiClient>, IManyInventoryApiClient
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="daprClient">Dapr客户端</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="httpContextAccessor">HTTP上下文访问器</param>
        public ManyInventoryApiClient(DaprClient daprClient, ILogger<ManyInventoryApiClient> logger, IHttpContextAccessor httpContextAccessor)
            : base(daprClient, logger, httpContextAccessor)
        {
        }

        #region 多对多勾稽-入库明细
        /// <summary>
        /// 财务进项票多对多专用-查询入库明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>入库单明细列表</returns>
        public async Task<ManyStoreInDetailQueryOutput> QueryManyToManyStoreInDetailForFinance(ManyStoreInDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreInDetailQueryInput, ManyStoreInDetailQueryOutput>(
                input,
                AppCenter.Inventory_Many_QueryStoreInByCompany,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-更新入库明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<BaseResponseData<object>> UpdateManyToManyStoreInDetailForFinance(ManyStoreInUpdateInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreInUpdateInput, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_Many_UpdateStoreInDetailsForFinance,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-撤销入库明细（多个发票）
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        public async Task<BaseResponseData<object>> RevokeManyToManyStoreInDetail(RevokeInventoryStoreInDetail input)
        {
            return await InvokeMethodWithQueryObjectAsync<RevokeInventoryStoreInDetail, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_UpdateInvoiceInfoRevoke,
                RequestMethodEnum.POST);
        }


        #endregion

        #region 多对多勾稽-出库明细
        /// <summary>
        /// 财务进项票多对多专用-获取出库明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>出库单明细列表</returns>
        public async Task<BaseResponseData<List<ManyStoreOutDetailItem>>> QueryManyToManyStoreOutDetailForFinance(ManyStoreOutDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreOutDetailQueryInput, BaseResponseData<List<ManyStoreOutDetailItem>>>(
                input,
                AppCenter.Inventory_Many_QueryStoreOutDetailForFinance,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-更新出库单明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<BaseResponseData<object>> UpdateManyToManyStoreOutDetailForFinance(ManyStoreOutUpdateInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreOutUpdateInput, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_Many_UpdateStoreOutDetailsForFinance,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新出库单明细发票信息
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        public async Task<BaseResponseData<object>> RevokeManyToManyStoreOutDetail(RevokeInventoryStoreInDetail input)
        {
            return await InvokeMethodWithQueryObjectAsync<RevokeInventoryStoreInDetail, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_Many_UpdateStoreOutInvoiceInfoRevoke,
                RequestMethodEnum.POST);
        }
        #endregion

        #region 多对多勾稽-换货转退货明细
        /// <summary>
        /// 财务进项票多对多专用-获取换货转退货明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>换货转退货明细列表</returns>
        public async Task<BaseResponseData<List<ManyStoreExchangeBackDetailItem>>> QueryManyToManyStoreExchangeBackDetailForFinance(ManyStoreExchangeBackDetailQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreExchangeBackDetailQueryInput, BaseResponseData<List<ManyStoreExchangeBackDetailItem>>>(
                input,
                AppCenter.Inventory_Many_QueryStoreExchangeBackDetailForFinance,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-更新换货转退货单明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        public async Task<BaseResponseData<object>> UpdateManyToManyStoreExchangeBackDetailForFinance(ManyStoreExchangeBackUpdateInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<ManyStoreExchangeBackUpdateInput, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_Many_UpdateStoreExchangeBackDetailsForFinance,
                RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新换货转退货明细发票信息
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        public async Task<BaseResponseData<object>> RevokeManyToManyStoreExchangeBackDetail(RevokeInventoryStoreInDetail input)
        {
            return await InvokeMethodWithQueryObjectAsync<RevokeInventoryStoreInDetail, BaseResponseData<object>>(
                input,
                AppCenter.Inventory_Many_UpdateStoreExchangeBackInvoiceInfoRevoke,
                RequestMethodEnum.POST);
        }
        #endregion

        /// <summary>
        /// 获取应用ID
        /// </summary>
        /// <returns>应用ID</returns>
        protected override string GetAppId()
        {
            return AppCenter.Inventory_APPID;
        }
        /// <summary>
        /// 默认响应处理
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="request">请求消息</param>
        /// <returns>响应结果</returns>
        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }

    }
}
