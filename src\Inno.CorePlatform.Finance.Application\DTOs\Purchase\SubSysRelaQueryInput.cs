﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 查询采购子系统配置入参
    /// </summary>
    public class SubSysRelaQueryInput
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public string? CompanyId { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public string? AgentId { get; set; }
        /// <summary>
        /// 业务单元id
        /// </summary>
        public string? ServiceId { get; set; }
        /// <summary>
        /// 部门id
        /// </summary>
        public string? DeptId { get; set; }
        /// <summary>
        /// 业务类型
        /// </summary>
        public int? BillType { get; set; }
    }

    /// <summary>
    /// 配置集合
    /// </summary>
    public class PurchaseConfigBox
    {
        /// <summary>
        /// 用户id
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public string? CompanyId { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public string? AgentId { get; set; }
        /// <summary>
        /// 业务单元id
        /// </summary>
        public string? ServiceId { get; set; }
        /// <summary>
        /// 是否屏蔽销售账期
        /// </summary>
        public bool HiddenSaleAccountInfo { get; set; } = false;
    }
}
