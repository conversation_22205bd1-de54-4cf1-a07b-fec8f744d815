﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 库存能力中心
    /// </summary>
    public interface IInventoryExcuteApiClient
    {
        /// <summary>
        /// 发票撤回，更新入库单明细发票信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<string>> UpdateInvoiceInfoRevoke(List<string> invoiceNumbers);

        /// <summary>
        /// 更改库存入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<object>> UpdateStoreInDetail(List<InventoryStoreInUpdateDetail> input);

        /// <summary>
        /// 更改库存出库入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<object>> UpdateStoreOutDetail(List<InventoryStoreOutUpdateDetail> input);

        /// <summary>
        /// 撤回 - 更改库存出库入票数接口 （20240928迁移，原因：无法正确返回库存错误信息）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<InventoryRespon<object>> UpdateInvoiceInfoRevoke(List<InventoryStoreOutUpdateDetail> input);
    }
}
