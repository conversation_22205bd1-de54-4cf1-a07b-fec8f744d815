﻿using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface ISPDApiClient
    {  
        /// <summary>
        /// 开票接口
        /// </summary>
        /// <returns></returns>
        Task<SPDResponse> ReceiveInvoice(InoviceSpdInput input);

        /// <summary>
        /// 收款
        /// </summary>
        /// <returns></returns>
        Task<SPDResponse> SynReceive(RecognizeReceiveSpdInput input);
        Task<SPDResponse> synReceiveNoInvoice(RecognizeReceiveSpdInitInput input);
        Task<SPDResponse> synReceiveAndInvoice(RecognizeReceivePushBusinessInput input);
    }
}
