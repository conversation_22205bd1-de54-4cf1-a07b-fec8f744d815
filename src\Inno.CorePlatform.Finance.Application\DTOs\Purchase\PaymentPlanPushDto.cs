﻿namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 采购付款计划推送dto
    /// </summary>
    public class PaymentPlanPushDto
    {
        /// <summary>
        /// 采购单号
        /// </summary>
        public string? PurchaseCode { get; set; }
        /// <summary>
        /// 账期类型
        /// </summary>
        public int AccountPeriodType { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 预计付款日期
        /// </summary>
        public DateTime? ProbablyPayTime { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public string? Company { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public string? Agent { get; set; }
    }
}
