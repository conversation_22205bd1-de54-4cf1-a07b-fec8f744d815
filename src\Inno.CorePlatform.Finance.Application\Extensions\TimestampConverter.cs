﻿
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// 时间戳序列化
/// </summary>
public class TimestampConverter : JsonConverter<DateTimeOffset>
{
    public override DateTimeOffset Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        long timestamp;
        if (reader.TokenType == JsonTokenType.Number)
        {
            timestamp = reader.GetInt64();
        }
        else if (reader.TokenType == JsonTokenType.String)
        {
            if (!long.TryParse(reader.GetString(), out timestamp))
            {
                throw new JsonException(string.Format("Cannot convert invalid value to {0}.", typeToConvert));
            }
        }
        else
            throw new JsonException("Invalid timestamp value");
        DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(timestamp);
        return dateTimeOffset;
    }
    public override void Write(Utf8JsonWriter writer, DateTimeOffset value, JsonSerializerOptions options)
    {
        var timestamp = value.ToUnixTimeMilliseconds();
        writer.WriteNumberValue(timestamp);
    }
}
