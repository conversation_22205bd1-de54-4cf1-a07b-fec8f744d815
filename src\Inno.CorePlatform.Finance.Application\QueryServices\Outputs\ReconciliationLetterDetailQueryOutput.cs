﻿using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class ReconciliationLetterDetailQueryOutput
    {
        /// <summary>
        /// 详情id
        /// </summary>
        public Guid? Id{ get; set; }
        /// <summary>
        /// 财务对账函Id
        /// </summary>
        public Guid? ReconciliationLetterItemId { get; set; }
        /// <summary>
        /// 明细类型
        /// </summary>
        public ReconciliationLetterEnum? Classify { get; set; }

        /// <summary>
        /// 明细单据日期（开票日期、应收日期）
        /// </summary> 
        public DateTime? BillDate { get; set; }


        /// <summary>
        /// 明细单据号（开票日期、应收日期）
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 明细金额（开票金额、应收金额）
        /// </summary>
        public decimal? Value { get; set; }

        /// <summary>
        /// 已收明细金额（开票金额、应收金额）
        /// </summary>

        public decimal? ReceivedValue { get; set; }

        /// <summary>
        /// 未收明细金额（开票金额、应收金额）
        /// </summary>
        public decimal? NonReceivedValue { get; set; }

    }

    public class ReconciliationLetterProductDetailQueryOutput
    {
        /// <summary>
        /// 详情id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 财务对账函Id
        /// </summary>
        public Guid? ReconciliationLetterItemId { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        [Comment("日期")]
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        [Comment("业务类型")]
        public string BusinessType { get; set; }

        /// <summary>
        /// 随货单号
        /// </summary>
        [Comment("随货单号")]
        public string? ShipmentCode { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        [Comment("单号")]
        [MaxLength(200)]
        public string? BillCode { get; set; }

        /// <summary>
        /// 业务员
        /// </summary>
        [Comment("业务员")]
        public string? Salesman { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        [Comment("货号")]
        public string? ProductNo { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        [Comment("规格")]
        public string? Specification { get; set; }

        //批号
        [Comment("批号")]
        public string? BatchNo { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Comment("单价")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Comment("数量")]
        public int? Quantity { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Comment("金额")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        [Comment("开票时间")]
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        [Comment("应收单号")]
        [MaxLength(500)]
        public string? ReceivableCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Comment("备注")]
        public string? Remark { get; set; }
    
}

}
