﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IPurchaseApiClient
    {
        Task<PurchaseOutPut> GetByIdAsync(Guid Id);
        Task<PurchaseQueryInfoSimpleOutput> GetSimpleByCode(string code);
        Task<PurchaseDataOutput> GetList(PurchaseDataInput input);

        /// <summary>
        /// 寄售转购货明细分组
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BasePageResult<ConsignToPurchaseDetailGroupOutput>> GetConsignToPurchaseDetailGroup(ConsignToPurchaseDetailGroupInput input);

        /// <summary>
        /// 更新入票数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int?> UpdateInvoiceQuantity(List<UpdateInvoiceQuantityInput> input);

        Task<BasePageResult<PurchaseReviseForInputBill>> GetPurchaseReviseForInputBills(PurchaseReviseForInputBillQueryDto query);

        /// <summary>
        /// 更新购货修订入票金额
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<bool>> UpdatePurchaseReviseInviceAmount(List<UpdatePurchaseReviseInvoiceAmountInputDto> input);
        Task<ServicePurchaseQueryInfoOutput> ServicePurchaseGetById(Guid id);
        Task<BasePageResult<ReconciliationOutput>> GetRevisionPurchaseOrderList(ReconciliationInput input);
        Task<List<RebateProvisionOfPurchaseOutput>> GetRebateProvision(RebateProvisionOfPurchaseInput input);

        Task<PurchaseOrderOutput> GetOrderDetailsByPurchaseCode(QueryOrderDetailsByPurchaseCodeInput input);

        /// <summary>
        /// 财务进项票多对多专用-获取寄售转购货明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>寄售转购货明细列表</returns>
        Task<List<ConsignToPurchaseDetailDto>> GetConsignToPurchaseDetailGroupForInvoice(ManyStoreInDetailQueryInput input);

        /// <summary>
        /// 财务进项票多对多专用-获取购货修订明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>购货修订明细列表</returns>
        Task<List<GetReviseOrderForFinanceInvoiceOutput>> GetPurchaseRevisionForFinanceInvoice(ManyStoreInDetailQueryInput input);

        /// <summary>
        /// 财务进项票多对多专用-更新寄售转购货
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<bool>> UpdateConsignToPurchaseDetailGroupForInvoice(UpdateInvoiceQuantityForFinanceInvoiceInput input);

        /// <summary>
        /// 财务进项票多对多专用-更新购货修订入票金额
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<bool>> UpdateReviseOrderForFinanceInvoice(UpdateInvoiceAmountForFinanceInvoiceInput input);

        /// <summary>
        /// 删除预付记录
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DeleteAdvancePayById(DeleteAdvancePayInput input);

        /// 生成购货修订订单
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> GenerateAdvancePaymentRevise(List<PurchaseDetailsInput> input);
    }
}
