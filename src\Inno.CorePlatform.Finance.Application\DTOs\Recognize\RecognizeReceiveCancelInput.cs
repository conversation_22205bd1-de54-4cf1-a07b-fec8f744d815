﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class RecognizeReceiveCancelInput
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        [Comment("认款单号")]
        [MaxLength(200)]
        public string Code { get; set; }

    }
    /// <summary>
    /// 金蝶审批通过
    /// </summary>
    public class RecognizeReceiveApproveInput
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        [Comment("认款单号")]
        [MaxLength(200)]
        public string Code { get; set; }

    }

    /// <summary>
    /// 推送至商务平台
    /// </summary>
    public class RecognizeReceivePushBusinessInput
    {
        /// <summary>
        /// 认款单号
        /// </summary>
        public string? confirmCode { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string? relateCode { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public double? amount { get; set; }
        /// <summary>
        /// 收款时间
        /// </summary>
        public DateTime? receiveTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public List<BillReceiveInvoiceRel>? billReceiveInvoiceRelList { get; set; }
    }

    public class BillReceiveInvoiceRel
    {
        /// <summary>
        /// 抄发票金额
        /// </summary>
        public double? receiveInvoiceAmount { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? invoiceNum { get; set; }
    }

    /// <summary>
    /// 推送至SPD
    /// </summary>
    public class RecognizeReceivePushSPDInput
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        public List<BillDetailToSPD>? list { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid CustomerId { get; set; }
    }

    public class BillDetailToSPD
    {
        /// <summary>
        /// 收到汇款时间
        /// </summary>
        public DateTime? remitTime { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public string? invoiceNum { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string? invoiceAmount { get; set; }
    }

    /// <summary>
    /// 根据订单号撤销认款入参
    /// </summary>
    public class CancelReceiveByOrderNoInput
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderNo { get; set; }
    }
}
