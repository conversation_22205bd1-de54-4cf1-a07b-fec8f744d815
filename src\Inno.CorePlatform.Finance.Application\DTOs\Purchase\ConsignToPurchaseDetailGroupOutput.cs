﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class ConsignToPurchaseDetailGroupOutput
    {
        //单号
        public string Code { get; set; }
        //日期
        public DateTimeOffset BillDate { get; set; }
        //业务单元
        public string ServiceName { get; set; }
        //公司
        public string CompanyName { get; set; }
        //供应商
        public string AgentName { get; set; }
        //品名
        public string ProductName { get; set; }
        public string? ProductNameId { get; set; }
        //货号
        public string ProductNo { get; set; }
        public Guid? ProductId { get; set; }
        //购货数量
        public decimal Quantity { get; set; }
        //已入票数量
        public decimal InvoiceQuantity { get; set; }
        //含税成本单价
        public decimal UnitCost { get; set; }
        //不含税成本单价
        public decimal NoTaxUnitCost
        {
            get
            {
                return UnitCost / (1 + (TaxRate / 100));
            }
        }
        //税率
        public decimal TaxRate { get; set; }
        public long StoreInDate { get { return BillDate.ToUnixTimeSeconds(); } }
        //明细
        public List<ConsignToPurchaseDetail> Details { get; set; }
    }
    public class ConsignToPurchaseDetail
    {
        public Guid PurchaseDetailId { get; set; }
        public decimal Quantity { get; set; }
        /// <summary>
        ///  货号Id
        /// </summary>
        public string? ProductId { get; set; }
        /// <summary>
        /// 已入发票数量
        /// </summary>
        public decimal? InvoiceQuantity { get; set; } = 0;

    }

    public class PurchaseReviseForInputBill
    {

        public Guid purchaseOrderId { get; set; }
        public string purchaseOrderCode { get; set; }
        public List<Guid> purchaseDetailIds { get; set; }
        public string relateCode { get; set; }
        public Guid productId { get; set; }
        public Guid productNameId { get; set; }
        public Guid agentId { get; set; }
        public Guid producerId { get; set; }
        public string productNo { get; set; }
        public string productName { get; set; }
        public string agentName { get; set; }
        public string producerName { get; set; }
        public decimal reviseAmount { get; set; }
        public decimal canInvoiceAmount { get; set; }

        public decimal taxRate { get; set; }
        public string companyName { get; set; } 

        public DateTimeOffset billDate { get; set; }
    }

    public class PurchaseReviseForInputBillQueryDto
    {
        public List<string> codes { get; set; }
        public List<string> relateCodes { get; set; }
        public List<Guid> companyIds { get; set; }
        public List<Guid> agentIds { get; set; }
        public DateTime? startTime { get; set; }
        public DateTime? endTime { get; set; }
        public int? pageIndex { get; set; } = 1;
        public int? pageSize { get; set; } = 50;

        /// <summary>
        /// 产品名称ID
        /// </summary>
        public List<string>? ProductNameIds { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }
    }

    public class UpdatePurchaseReviseInvoiceAmountInputDto 
    {
        public string purchaseOrderCode { get; set; }
        public decimal invoiceAmount { get; set; } 
        public Guid productId { get; set; } 
        public decimal taxRate { get; set; }
    }
}
