﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class ProjectApiExcuteClient : BaseDaprApiClient<ProjectApiClient>, IProjectApiExcuteClient
    {
        public ProjectApiExcuteClient(
            DaprClient daprClient, 
            ILogger<ProjectApiClient> logger,
            IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        } 

        public async Task<BaseResponseData<bool>> GetContractDelayInfoList(GetContractDelayInfoListInput input)
        {
            var res = await InvokeMethodWithQueryObjectAsync<GetContractDelayInfoListInput, BaseResponseData<bool>>(input, AppCenter.PM_GetContractDelayInfoList, RequestMethodEnum.POST);
            return res;
        }

        protected override string GetAppId()
        {
            return AppCenter.PM_APPID;
        }
        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        { 
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }
    }
}
