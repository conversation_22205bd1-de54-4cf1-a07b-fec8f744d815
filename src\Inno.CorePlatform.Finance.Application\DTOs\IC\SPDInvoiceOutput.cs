﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.IC
{
    public class SPDInvoiceOutput
    {
        public string? Code { get; set; }
        public decimal? Amount { get; set; }
        public Guid? CustomerId { get; set; }
    }

    /// <summary>
    /// 获取发票详情出参
    /// </summary>
    public class SpdInvoiceApplyDetailOutput
    {
        public Guid Id { get; set; }
        public Guid SpdInvoiceId { get; set; }

        public string? ThirdOrderId { get; set; }

        public string? ThirdOrderItemId { get; set; }

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string? Model { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnitDesc { get; set; }

        /// <summary>
        /// 申请数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 已处理数量
        /// </summary>
        public int SolvedQuantity { get; set; }

        /// <summary>
        /// 院内编码
        /// </summary>
        public string? SpdProductId { get; set; }

        /// <summary>
        /// 原价
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 销售税率
        /// </summary>
        public decimal? SaleTaxRate { get; set; }


        /// <summary>
        /// 生产日期
        /// </summary>
        public string? ProductDate { get; set; }

        /// <summary>
        /// 效期
        /// </summary>
        public string? ExpiryDate { get; set; }


        /// <summary>
        /// 批号
        /// </summary>
        public string? LotNo { get; set; }


        /// <summary>
        /// 注册证ID
        /// </summary>

        public string? RegNo { get; set; }


        /// <summary>
        /// 产品条码
        /// </summary>
        public string? BarCode { get; set; }


        /// <summary>
        /// 组套编码
        /// </summary>
        public string? CombinationCode { get; set; }
        /// <summary>
        ///  源销售单号
        /// </summary>
        public string? ThirdSaleCode { get; set; }
        /// <summary>
        /// 已开票数量
        /// </summary>
        public decimal? InvoicedQuantity { get; set; }
    }
}
