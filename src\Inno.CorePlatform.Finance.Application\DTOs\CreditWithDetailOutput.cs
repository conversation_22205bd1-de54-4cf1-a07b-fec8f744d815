using System;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.Dtos
{
    public class CreditWithDetailOutput
    {
        public Guid Id { get; set; }
        public string BillCode { get; set; }
        public Guid? CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string OriginOrderNo { get; set; }
        public string OrderNo { get; set; }
        public decimal Value { get; set; }
        public int CreditType { get; set; }
        public int CreditSaleSubType { get; set; }
        public List<CreditDetailOutput> Details { get; set; } = new List<CreditDetailOutput>();
    }

    public class CreditDetailOutput
    {
        public Guid Id { get; set; }
        public Guid CreditId { get; set; }
        public Guid? ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Amount { get; set; }
        public string Remark { get; set; }
    }
}
