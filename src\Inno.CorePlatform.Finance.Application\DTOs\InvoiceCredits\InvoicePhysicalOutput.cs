﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits
{
    public class InvoiceDeliveryOutput
    {
        /// <summary>
        /// 发票号
        /// </summary>  
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>  
        public string? InvoiceCode { get; set; }


        /// <summary>
        /// 发票验证码
        /// </summary> 
        [MaxLength(200)]
        public string? InvoiceCheckCode { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime InvoiceTime { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public long InvoiceTimeTimestamp
        {
            get
            {
                DateTimeOffset invoiceTime = new DateTimeOffset(InvoiceTime, TimeSpan.FromHours(8));// DateTimeOffset.Parse(InvoiceTime.ToString("yyyy-MM-dd HH:mm:ss"),time);
                // 将DateTime类型转换为时间戳(毫秒值)
                return invoiceTime.ToUnixTimeMilliseconds();
            }
        }
        /// <summary>
        /// 开票金额
        /// </summary>  
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 不含税金额
        /// </summary> 
        public decimal? InvoiceAmountNoTax { get; set; }
        /// <summary>
        /// 税额
        /// </summary> 
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>

        public string InvoiceType { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string? CustomerId { get; set; }

        /// <summary>
        /// 开票申请单号
        /// </summary>   
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary> 
        public string? Remark { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public int InvoiceStatus { get; set; }

        /// <summary>
        /// 开票人
        /// </summary>
        public string InvoiceCreatedBy { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string businessUnitId { get; set; }

        /// <summary>
        /// 终端医院Id
        /// </summary>
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端医院名称
        /// </summary>
        public string? HospitalName { get; set; }
    }
}
