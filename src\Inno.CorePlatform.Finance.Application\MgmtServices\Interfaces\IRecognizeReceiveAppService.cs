﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IRecognizeReceiveAppService
    {
        /// <summary>
        /// 添加认款清单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> AddItemAsync(ReceivedItemAddInput input);
        /// <summary>
        /// 创建认款单详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> CreatedDetailAsync(RecognizeReceiveDetailInput input);
        /// <summary>
        /// 编辑认款单详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> UpdateDetailAsync(Guid id, RecognizeReceiveDetailInput input);
        /// <summary>
        /// 删除认款订单详情
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<int> DeleteDetailAsync(RecognizeReceiveDetailDelDto dto);
        /// <summary>
        /// 提交
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<object> SubmitToKd(Guid id, string createdBy);
        /// <summary>
        /// 金蝶驳回
        /// </summary>
        /// <param name="input"></param>
        /// <param name="isCallBack"></param>
        /// <returns></returns>
        Task<int> Cancel(CancelReceiptInput input,bool? isCallBack=true);
        /// <summary>
        /// 金蝶审批通过
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<int> Approve(RecognizeReceiveApproveInput input);
        /// <summary>
        /// 金蝶审核通过 - 推送认款单数据到商务平台
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<SPDResponse> PushBusiness(RecognizeReceiveApproveInput input);
        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="file"></param>
        /// <param name="id"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        Task<int> Import(IFormFile file,Guid id,Guid customerId, bool isReturnCustomer, string? currentUser = null);
        /// <summary>
        /// 删除单据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteItemAsync(Guid id, string createdBy);
        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> AttachFileIds(RecognizeItemAttachFileInput input);
        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeleteAttachFileIds(RecognizeItemAttachFileInput input);

        /// <summary>
        /// 撤销销售认款
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceive(Guid itemId,string userName);
        /// <summary>
        /// 转货款
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> Transfer(Guid itemId,string userName);

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData> VerifySingleData([FromBody] RecognizeReceiveDetailsInput input);

        /// <summary>
        /// 批量保存认款单明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SaveBatchDetailAsync(RecognizeReceiveDetailsInput input);

        /// <summary>
        /// 获取已认款金额（集合）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<RecognizeReceiveAmountDto>> GetRecognizeReceiveAmount(List<string?>? codes, int type);

        /// <summary>
        /// 保存上游回款显示日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SaveBackDateTime(SaveBackTimeVo input);
        /// <summary>
        /// 撤销认款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceiveDetail(PartCancelInput input);
        /// <summary>
        /// 撤销暂收款明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceiveTempDetail(PartCancelTempInput input);
        /// <summary>
        /// 导入认款明细
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="userName"></param>
        /// <param name="recognizeReceiveItemId"></param>
        /// <param name="isReturnCustomer"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<BaseResponseData<RecognizeDetailExcelOutput>> ExportDetails(Guid fileId, Guid recognizeReceiveItemId,bool isReturnCustomer, string userName, Guid? userId);

        /// <summary>
        /// 根据订单号撤销认款
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelReceiveByOrderNo(string orderNo);

        /// <summary>
        /// 验证订单是否可以撤销认款
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> VerifyCancelReceiveByOrderNo(string orderNo);

        /// <summary>
        /// 更新贴现日期
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<BaseResponseData<int>> UpdateDiscountDate(KingdeeUpdateDiscountDateInput input);
        
        /// <summary>
        /// 导出认款明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailsTask(ExportDetailsInput query);

        /// <summary>
        /// 导出明细包含应收
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportDetailCreditsTask(ExportDetailsInput query);

        /// <summary>
        /// 更新收款认领明细中的销售单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UpdateBillCodeInRecognizeReceiveDetail(SaleBillCodeChangeInput input);
    }
}
