﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 对账函上传到OA入参
    /// </summary>
    public class ReconciliationLetterSubmitOAInput
    {
        public Guid ReconciliationLetterItemId { get; set; }
        public string CompanyName {  get; set; }
        public string UserName { get; set; }


    }
}
