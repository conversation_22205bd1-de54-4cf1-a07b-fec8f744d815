using EasyCaching.Core;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Services;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Optimized
{
    /// <summary>
    /// 金蝶财务相关API客户端
    /// </summary>
    public class KingdeeFinanceClient : KingdeeBaseClient, IKingdeeFinanceClient
    {
        public KingdeeFinanceClient(
            HttpClient httpClient,
            IEasyCachingProvider easyCaching,
            IConfiguration configuration,
            ISubLogService logger,
            IOptionsSnapshot<KingdeeSetting> kingdeeSetting,
            IDaprEventPublisher daprEventPublisher)
            : base(httpClient, easyCaching, configuration, logger, kingdeeSetting, daprEventPublisher)
        {
        }

        /// <summary>
        /// 保存财务应收单
        /// </summary>
        public async Task<KingdeeApiResult> PushCreditsToKingdeeAsync(
            List<KingdeeCredit> kingdeeCredits,
            string classify,
            string preRequestBody)
        {
            return await ExecuteFinancialOperationAsync(
                "kapi/v2/jfzx/ar/ar/batchSaveArFinBill",
                "保存财务应收单",
                kingdeeCredits,
                classify,
                preRequestBody);
        }

        /// <summary>
        /// 保存财务应付单
        /// </summary>
        public async Task<KingdeeApiResult> PushDebtsToKingdeeAsync(
            List<KingdeeDebt> kingdeeDebts,
            string classify,
            string preRequestBody)
        {
            return await ExecuteFinancialOperationAsync(
                "kapi/v2/jfzx/ap/ap/batchSaveApFinBill",
                "保存财务应付单",
                kingdeeDebts,
                classify,
                preRequestBody);
        }

        /// <summary>
        /// 更改收入确认标识到金蝶系统
        /// </summary>
        public async Task<KingdeeApiResult<int>> PushIncomeIsConfirmAsync(KingdeeIncomeIsConfirm incomeIsConfirm)
        {
            return await ExecuteRequestAsync<int>("kapi/v2/jfzx/ar/ar/updateiscofirm", incomeIsConfirm, "更改收入确认标识");
        }

        /// <summary>
        /// 应付付款结算
        /// </summary>
        public async Task<KingdeeApiResult> PaymentSettlementAsync(List<PaymentSettlementInput> inputs)
        {
            return await ExecuteRequestAsync("kapi/v2/jfzx/ap/ap/paymentSettlement", inputs, "应付付款结算");
        }

        /// <summary>
        /// 应付冲应收
        /// </summary>
        public async Task<KingdeeApiResult> PayablesOffsetReceivablesAsync(List<PaymentSettlementInput> inputs)
        {
            return await ExecuteRequestAsync("kapi/v2/jfzx/jfzx_ap_ext/ap/payablesOffsetReceivables", inputs, "应付冲应收");
        }

        /// <summary>
        /// 多发票指定应付
        /// </summary>
        public async Task<KingdeeApiResult> ManyInvoiceSpecifyApFinAsync(ManyInvoiceSpecifyApFinInput input)
        {
            const string endpoint = "kapi/v2/jfzx/ap/ap/manyInvoiceSpecifyApFin";
            const string operationName = "多发票指定应付";

            try
            {
                // 格式化金额为两位小数
                FormatAmounts(input);

                _logger.LogAzure("ManyInvoiceSpecifyApFin", input.ToJson(), "开始调用金蝶接口");

                var result = await ExecuteRequestAsync(endpoint, input, operationName);

                if (result.IsSuccess)
                {
                    _logger.LogAzure("ManyInvoiceSpecifyApFin", input.ToJson(), "调用金蝶接口成功");
                }
                else
                {
                    _logger.LogAzure("ManyInvoiceSpecifyApFin", $"入参：{input.ToJson()}，错误：{result.ErrorMessage}", "调用金蝶接口失败", Application.Enums.LogLevelEnum.Error);

                    // 如果是已存在的错误，返回成功
                    if (result.ErrorMessage?.Contains("已存在") == true)
                    {
                        _logger.LogAzure("ManyInvoiceSpecifyApFin", $"入参：{input.ToJson()}，错误：{result.ErrorMessage}", "数据已存在, 视为成功", Application.Enums.LogLevelEnum.Warning);
                        return KingdeeApiResult.Success("数据已存在，操作成功");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("ManyInvoiceSpecifyApFin", $"入参：{input.ToJson()}，错误：{ex.Message}", "调用金蝶接口异常", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult.Failure($"【金蝶】多发票指定应付出错，原因：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量更新收款调整单订单号
        /// </summary>
        public async Task<KingdeeApiResult> BatchUpdatePaymentModificationAsync(List<BatchUpdateOrderNumberInput> inputs)
        {
            _logger.LogAzure("BatchUpdatePaymentModification", inputs.ToJson(), "开始调用金蝶接口");

            try
            {
                var result = await ExecuteRequestAsync("kapi/v2/jfzx/cas/api/batchUpdatePaymentModification", inputs, "批量更新收款调整单订单号");

                if (result.IsSuccess)
                {
                    _logger.LogAzure("BatchUpdatePaymentModification", inputs.ToJson(), "调用金蝶接口成功");
                }
                else
                {
                    _logger.LogAzure("BatchUpdatePaymentModification", $"入参：{inputs.ToJson()}，错误：{result.ErrorMessage}", "调用金蝶接口失败", Application.Enums.LogLevelEnum.Error);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("BatchUpdatePaymentModification", $"入参：{inputs.ToJson()}，错误：{ex.Message}", "调用金蝶接口异常", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult.Failure($"【金蝶】批量更新收款调整单订单号出错，原因：{ex.Message}");
            }
        }

        /// <summary>
        /// 批量更新认款调整单订单号
        /// </summary>
        public async Task<KingdeeApiResult> BatchUpdateAcceptanceAsync(List<BatchUpdateOrderNumberInput> inputs)
        {
            _logger.LogAzure("BatchUpdateAcceptance", inputs.ToJson(), "开始调用金蝶接口");

            try
            {
                var result = await ExecuteRequestAsync("kapi/v2/jfzx/cas/api/batchUpdateAcceptance", inputs, "批量更新认款调整单订单号");

                if (result.IsSuccess)
                {
                    _logger.LogAzure("BatchUpdateAcceptance", inputs.ToJson(), "调用金蝶接口成功");
                }
                else
                {
                    _logger.LogAzure("BatchUpdateAcceptance", $"入参：{inputs.ToJson()}，错误：{result.ErrorMessage}", "调用金蝶接口失败", Application.Enums.LogLevelEnum.Error);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("BatchUpdateAcceptance", $"入参：{inputs.ToJson()}，错误：{ex.Message}", "调用金蝶接口异常", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult.Failure($"【金蝶】批量更新认款调整单订单号出错，原因：{ex.Message}");
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 执行财务操作（通用方法）
        /// </summary>
        private async Task<KingdeeApiResult> ExecuteFinancialOperationAsync<T>(
            string endpoint,
            string operationName,
            List<T> data,
            string classify,
            string? preRequestBody = null) where T : class
        {
            try
            {
                var result = await ExecuteRequestAsync(endpoint, data, operationName);
                await LogTempStoreAsync(data, classify, preRequestBody);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogAzure("operationName", $"入参：{data.ToJson()}，错误：{ex.Message}", $"{operationName}异常", Application.Enums.LogLevelEnum.Error);
                return KingdeeApiResult.Failure($"{operationName}出错，原因：{ex.Message}");
            }
        }

        /// <summary>
        /// 记录临时存储日志
        /// </summary>
        private async Task LogTempStoreAsync<T>(List<T> items, string classify, string? preRequestBody = null) where T : class
        {
            try
            {
                var content = $"分类: {classify}, 项目数量: {items.Count}";
                if (!string.IsNullOrEmpty(preRequestBody))
                {
                    content += $", 原始请求体长度: {preRequestBody.Length}";
                }
                await CreateSubLogAsync(SubLogSourceEnum.PushKingdee, content, "临时存储日志");
            }
            catch (Exception ex)
            {
                _logger.LogAzure("LogTempStoreAsync", $"错误：{ex.Message}", $"记录临时存储日志失败", Application.Enums.LogLevelEnum.Error);
            }
        }



        /// <summary>
        /// 格式化金额为两位小数
        /// </summary>
        private static void FormatAmounts(ManyInvoiceSpecifyApFinInput input)
        {
            foreach (var entry in input.finentry)
            {
                entry.f_usedamt = decimal.Parse(entry.f_usedamt.ToString("F2"));
            }
        }

        #endregion
    }
}
