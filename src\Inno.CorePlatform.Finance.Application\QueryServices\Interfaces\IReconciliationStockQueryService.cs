﻿using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IReconciliationStockQueryService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<ReconciliationOutput>> GetListPages(ReconciliationItemInput input);
        Task<PageResponse<ReconciliationOutput>> GetListByAgent(ReconciliationItemInput input);
        Task<PageResponse<ReconciliationOutput>> GetListByCustomer(ReconciliationItemInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfCompanyExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfAgentExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfAgentCustomerExport(ReconciliationItemExportInput input);
        Task<PageResponse<ReconciliationOutput>> GetListOfChangeAmountExport(ReconciliationItemExportInput input);
    }
}
