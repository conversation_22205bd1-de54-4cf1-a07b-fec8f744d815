﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Data.Migrations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    public class InventoryStoreOutDto : BaseQuery
    {
        /// <summary>
        /// 经销调出单发票到齐状态 0 未到齐 1到齐 2全部  SupposedType 默认 0 未到齐
        /// </summary>
        public int? supposedType { get; set; } = 0;
        public int? types { get; set; } = 0;
        /// <summary>
        /// 经销调出类型   下拉框的
        /// </summary>
        public List<int>? typeList
        {
            get
            {

                if (types == 0)
                {
                    //return new List<int>() { 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 999, 10000 };
                    return new List<int>() { 1,6 };
                }
                else
                {
                    var list = new List<int>();
                    list.Add(Convert.ToInt32(types));
                    return list;
                }

            }
        }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? companyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? companyName { get; set; }
        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? agentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? agentName { get; set; }
        /// <summary>
        /// 经销调出单号
        /// </summary>
        public string? storeInCode { get; set; }
        /// <summary>
        /// 经销调出单号
        /// </summary>
        public List<string>? storeInCodes { get; set; }
        /// <summary>
        /// 经销调出起始日期
        /// </summary>
        public string? storeInDateStart { get; set; }
        /// <summary>
        /// 经销调出截至时间
        /// </summary>
        public string? storeInDateEnd { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        public string? productName { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? productNo { get; set; }
        /// <summary>
        /// 上游订单号 (暂定)
        /// </summary>
        public string? UpstreamCode { get; set; }

        public Guid InputBillId { get; set; }

        /// <summary>
        /// 详情
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();
    }
    public class StoreOutDetailForFinanceOutput
    {

        /// <summary>
        /// 出库单明细ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 出库单id
        /// </summary>
        public string? StoreOutId { get; set; }

        /// <summary>
        /// 出库申请单号
        /// </summary>
        public string? StoreOutCode { get; set; }

        /// <summary>
        /// 库存明细id
        /// </summary>
        public string? StoreId { get; set; }

        /// <summary>
        /// 申请明细id
        /// </summary>
        public string? ApplyDetailId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目编号
        /// </summary>
        public string? ProjectCode { get; set; }

        ///项目折扣明细id
        public string? CostSettingDiscountId { get; set; }

        /// <summary>
        /// 追溯码
        /// </summary>
        public string? TraceCode { get; set; }

        /// <summary>
        /// 货物id
        /// </summary>
        public string? ProductId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名id
        /// </summary>
        public string? ProductNameId { get; set; }

        /// <summary>
        /// 品名名称冗余（暂不使用）
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 厂家id
        /// </summary>
        public string? ProducerId { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 注册证id
        /// </summary>
        public string? RegistrationId { get; set; }

        /// <summary>
        /// 注册证号
        /// </summary>
        public string? RegistrationNo { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        public string? LotNo { get; set; }

        /// <summary>
        /// 效期
        /// </summary>
        public long? ValidDate { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public long? ProduceDate { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public string? Sn { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 货品类型
        /// </summary>
        public int? Mark { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 发票数量
        /// </summary>
        public decimal? InvoiceQuantity { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 含税成本 -> 结算单价
        /// </summary>
        public decimal? SettlementCost { get; set; }

        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardUnitCost { get; set; }

        /// <summary>
        /// 箱子码
        /// </summary>
        public string? BoxCode { get; set; }

        /// <summary>
        /// 套包码
        /// </summary>
        public string? PackageCode { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? BusinessUnitId { get; set; }
        public string? BusinessUnitName { get; set; }
        /// <summary>
        ///       出库日期
        /// </summary>
        public long? StoreOutDate { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 注册人/备案人
        /// </summary>
        public string? RegisterName { get; set; }

        /// <summary>
        /// 受托生产企业
        /// </summary>
        public string? MarkProducerName { get; set; }

        /// <summary>
        /// 合格证明文件Id
        /// </summary>
        public string? CertFileId { get; set; }

        /// <summary>
        /// 随货同行单Id
        /// </summary>
        public string? AccompanyFileId { get; set; }

        /// <summary>
        /// 是否pc端拣货（0否、1是）
        /// </summary>
        public int? PcPick { get; set; }

        /// <summary>
        /// 创建人id
        /// </summary>
        public string? CreatorId { get; set; }

        /// <summary>
        /// 仓库Id
        /// </summary>
        public string? StoreHouseId { get; set; }

        /// <summary>
        /// 库区Id
        /// </summary>
        public string? StoreAreaId { get; set; }

        /// <summary>
        /// 库位Id
        /// </summary>
        public string? StoreLocationId { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public string? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public long BillDate { get; set; }

        /// <summary>
        /// 批号，条码，等字段
        /// </summary>
        public List<LotInfo>? LotInfo { get; set; } = new List<LotInfo>();
    }

    public class InventoryStoreOutUpdateDetail
    {

        public decimal invoiceQuantity { get; set; }

        public string id { get; set; }

        public decimal currentInvoiceQuantity { get; set; }

        public decimal invoiceAmount { get; set; }

        public string invoiceNumber { get; set; }

        public string InvoiceTypeStr { get; set; }

        /// <summary>
        /// utc时间戳
        /// </summary>
        public long InvoiceDate { get; set; }
    }
}
