﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class GetSaleDetailsForFinanceOutput
    {
        /// <summary>
        /// 销售订单Id
        /// </summary>
        public Guid SaleId { get; set; }

        /// <summary>
        /// 销售订单明细Id
        /// </summary>
        public Guid SaleDetailId { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 订单创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 货号ID
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        public string? LotNo { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 订单备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 发货单号
        /// </summary>
        public string? ShipmentCode { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public string? SaleTypeName { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }
    }

    public class GetSaleDetailsForFinanceInput
    {  
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTimeOffset StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTimeOffset EndTime { get; set; }
    }
}
