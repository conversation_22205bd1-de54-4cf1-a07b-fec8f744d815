﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Outputs;
using Inno.CorePlatform.Finance.Domain.DomainObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs
{
    /// <summary>
    /// 公司基本信息
    /// </summary>
    public class CompanyOutput : BaseOutput
    {
        public CompanyOutput()
        {

        }
        public CompanyOutput(ConditionDataOutput? source)
        {
            if (source != null)
            {
                this.Id = source.value.FirstOrDefault();
                this.Name = source.names.FirstOrDefault();
            }
            
        }
        /// <summary>
        /// 公司简码
        /// </summary>
        public string NameCode { get; set; }
    }
    /// <summary>
    /// 公司详细信息
    /// </summary>
    public class CompanyInfoOutput : CompanyOutput
    {
        /// <summary>
        /// 公司简称
        /// </summary>
        public string? ShortName { get; set; }
        /// <summary>
        /// 延期日期
        /// </summary>
        public int? DelayDays { get; set; }
        /// <summary>
        /// 公司系统月度
        /// </summary>
        public string? SysMonth { get; set; }
        /// <summary>
        /// 新经营范围
        /// </summary>
        public List<NewBusinessRangeOutput>? NewBusinessRanges { get; set; }
        /// <summary>
        /// 旧经营范围
        /// </summary>
        public List<OldBusinessRangeOutput>? OldBusinessRanges { get; set; }
        /// <summary>
        /// 启用状态
        /// </summary>
        public int Status { get; set; } = 0;
    }
}
