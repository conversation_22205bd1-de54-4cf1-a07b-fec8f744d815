﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 损失确认单查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class LossRecognitionQueryController : BaseController
    {
        public readonly ILossRecognitionQueryService _lossRecognitionQueryService;
        public readonly ICreditQueryService _creditQueryService;
        public readonly IDebtQueryService _debtQueryService;
        public LossRecognitionQueryController(ILossRecognitionQueryService lossRecognitionQueryService, ICreditQueryService creditQueryService, IDebtQueryService debtQueryService, ISubLogService subLog) : base(subLog)
        {
            this._lossRecognitionQueryService = lossRecognitionQueryService;
            _creditQueryService = creditQueryService;
            _debtQueryService = debtQueryService;
        }
        /// <summary>
        /// 获取损失确认单列表数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<LossRecognitionTabCountOutput>> GetTabCount([FromBody] LossRecognitionQueryInput query)
        {
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUser = CurrentUser.UserName;
            return await _lossRecognitionQueryService.GetTabCount(query);
        }
        /// <summary>
        /// 获取损失确认单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<LossRecognitionItemListOutput>> GetList([FromBody] LossRecognitionQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUser = CurrentUser.UserName;
                var (list, count) = await _lossRecognitionQueryService.GetListAsync(query);
                return new ResponseData<LossRecognitionItemListOutput>
                {
                    Code = 200,
                    Data = new Data<LossRecognitionItemListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取损失确认单明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListDetail")]
        public async Task<ResponseData<LossRecognitionDetailQueryOutput>> GetListDetail([FromBody] LossRecognitionDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _lossRecognitionQueryService.GetListDetailAsync(query);
                return new ResponseData<LossRecognitionDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<LossRecognitionDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 通过id获取损失确认申请单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("GetItemById")]
        public async Task<LossRecognitionItemListOutput> GetItemById( Guid? id)
        {
            try
            {
                var item = await _lossRecognitionQueryService.GetItemById(id);
                return item;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 通过id获取损失确认申请单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("GetLossRecognitionItemById")]
        public async Task<BaseResponseData<LossRecognitionItemListOutput>> GetLossRecognitionItemById(Guid? id)
        {
            try
            {
                var item = await _lossRecognitionQueryService.GetItemById(id);
                return new BaseResponseData<LossRecognitionItemListOutput>
                {
                    Code = CodeStatusEnum.Success,
                    Data = item
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取应收单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetCreditList")]
        public async Task<ResponseData<CreditQueryListOutput>> GetCreditList([FromBody] CreditQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                query.IsSureIncome = 1;//必须要确认收入
                query.AbatedStatus = Domain.AbatedStatusEnum.NonAbate;
                query.IsgreaterThanZero = true;
                var (list, count) = await _creditQueryService.GetListByLossRecognitionAsync(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        /// <summary>
        /// 获取应付
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDebtByCreditIds")]
        public async Task<ResponseData<DebtQueryListOutput>> GetDebtByCreditIds([FromBody] DebtQueryInput query)
        {
             try
            {
                query.userId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _debtQueryService.GetDebtByCreditId(query);
                return new ResponseData<DebtQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<DebtQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
}
    }
}
