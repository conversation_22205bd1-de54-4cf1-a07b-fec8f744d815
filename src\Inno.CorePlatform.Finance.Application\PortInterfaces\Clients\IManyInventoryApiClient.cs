using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 多对多勾稽库存能力中心接口
    /// </summary>
    public interface IManyInventoryApiClient
    {
        #region 多对多勾稽-入库明细
        /// <summary>
        /// 财务进项票多对多专用-查询入库明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>入库单明细列表</returns>
        Task<ManyStoreInDetailQueryOutput> QueryManyToManyStoreInDetailForFinance(ManyStoreInDetailQueryInput input);

        /// <summary>
        /// 财务进项票多对多专用-更新入库明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<object>> UpdateManyToManyStoreInDetailForFinance(ManyStoreInUpdateInput input);

        /// <summary>
        /// 财务进项票多对多专用-撤销入库明细（多个发票）
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        Task<BaseResponseData<object>> RevokeManyToManyStoreInDetail(RevokeInventoryStoreInDetail input);


        #endregion

        #region 多对多勾稽-出库明细
        /// <summary>
        /// 财务进项票多对多专用-获取出库明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>出库单明细列表</returns>
        Task<BaseResponseData<List<ManyStoreOutDetailItem>>> QueryManyToManyStoreOutDetailForFinance(ManyStoreOutDetailQueryInput input);

        /// <summary>
        /// 财务进项票多对多专用-更新出库单明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<object>> UpdateManyToManyStoreOutDetailForFinance(ManyStoreOutUpdateInput input);

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新出库单明细发票信息
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        Task<BaseResponseData<object>> RevokeManyToManyStoreOutDetail(RevokeInventoryStoreInDetail input);
        #endregion

        #region 多对多勾稽-换货转退货明细
        /// <summary>
        /// 财务进项票多对多专用-获取换货转退货明细
        /// </summary>
        /// <param name="input">查询参数</param>
        /// <returns>换货转退货明细列表</returns>
        Task<BaseResponseData<List<ManyStoreExchangeBackDetailItem>>> QueryManyToManyStoreExchangeBackDetailForFinance(ManyStoreExchangeBackDetailQueryInput input);

        /// <summary>
        /// 财务进项票多对多专用-更新换货转退货单明细
        /// </summary>
        /// <param name="input">更新参数</param>
        /// <returns>更新结果</returns>
        Task<BaseResponseData<object>> UpdateManyToManyStoreExchangeBackDetailForFinance(ManyStoreExchangeBackUpdateInput input);

        /// <summary>
        /// 财务进项票多对多专用-发票撤回,更新换货转退货明细发票信息
        /// </summary>
        /// <param name="input">撤销参数（多个发票号）</param>
        /// <returns>撤销结果</returns>
        Task<BaseResponseData<object>> RevokeManyToManyStoreExchangeBackDetail(RevokeInventoryStoreInDetail input);
        #endregion
    }
}
