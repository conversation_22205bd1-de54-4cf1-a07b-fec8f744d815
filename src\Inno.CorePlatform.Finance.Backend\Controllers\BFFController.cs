﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{

    [Route("api/bff")]
    [ApiController]
    public class BFFController : BaseController
    {
        private readonly IApplyBFFService _applyBFFService; 
        public BFFController(IApplyBFFService applyBFFService, ILogger<BFFController> logger, ISubLogService subLog) : base(subLog)
        {
            _applyBFFService = applyBFFService;
        }
        /// <summary>
        /// 获取用户可操作的业务单元（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetServices")]
        public async Task<List<ServiceOutput>> GetServiceMetaAsync([FromBody] ServiceMetaInput input)
        {
            var result = await _applyBFFService.GetServiceMetaListAsync(input);
            return result.Take(50).ToList();
        }

        /// <summary>
        /// 获取用户可操作的公司（带数据策略权限）
        /// 也可单独传公司Id获取公司信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetCompanies")]
        public async Task<List<CompanyOutput>> GetCompanyMetaAsync([FromBody] CompanyMetaInput input)
        {
            var result = await _applyBFFService.GetCompanyMetaListAsync(input);
            return result.Take(50).ToList();
        }

        /// <summary>
        /// 获取用户可操作的业务单元（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetUserByNames")]
        public async Task<ResponseData<UserOutput>> GetUserByNamesAsync([FromBody] GetUserInput input)
        {

            var result = await _applyBFFService.GetUserByNamesAsync(input);
            result.Data.List = result.Data.List.Take(50).ToList();
            return result;
        }
        /// <summary>
        /// 客户信息部门
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetCustomer")]
        public async Task<List<Application.CompetenceCenter.BDSCenter.Outputs.CustomerDeptMetaInfoOutput>> GetCustomersAsync(string? name)
        {
            BDSBaseInput input=new BDSBaseInput() { name= name };
            var result = await _applyBFFService.GetCustomerDeptMetaInfos(input);
            return result.Take(50).ToList();
        }
    }
}
