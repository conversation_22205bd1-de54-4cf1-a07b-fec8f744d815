﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class TaxClassCodeOutput
    {
        /// <summary>
        /// 简称
        /// </summary>
        public string? simpleName { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string? name { get; set; }

        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? number { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal? taxRate { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string? description { get; set; }
    }
}
