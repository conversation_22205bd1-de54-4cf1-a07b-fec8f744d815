﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.SPD
{
    public class RecognizeReceiveSpdInput
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        public string relateCode { get; set; }

        public List<abatement> billReceiveInvoiceRelList { get; set; }=new List<abatement>();
    }

    public class abatement
    { 
        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal? receiveInvoiceAmount { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string invoiceNum { get; set; }

        /// <summary>
        /// 应收单
        /// </summary>
        public string creditItemCode { get; set; }

    }


    public class RecognizeReceiveSpdInitInput
    {
        /// <summary>
        /// 收款单号
        /// </summary>
        public string relateCode { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal amount { get; set; }
        /// <summary>
        /// 收款时间
        /// </summary>
        public string receiveTime { get; set; }

        /// <summary>
        /// 收款时间
        /// </summary>
        public string? remark { get; set; }

        public List<abatementInit> billReceiveInvoiceRelList { get; set; } = new List<abatementInit>();
    }

    public class abatementInit
    {
        /// <summary>
        /// 应收认款金额
        /// </summary>
        public decimal? creditAmount { get; set; }

        /// <summary>
        /// SPD应收单号
        /// </summary>
        public string creditCode { get; set; }

    }
}
