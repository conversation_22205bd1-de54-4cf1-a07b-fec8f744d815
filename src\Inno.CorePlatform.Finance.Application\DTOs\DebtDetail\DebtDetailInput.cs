﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.DebtDetail
{
    public class DebtDetailInput
    {
        public int Guid { get; set; }

        /// <summary>
        /// 应付付款计划号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
        /// </summary>
        public int AccountPeriodType { get; set; }

        public decimal Discount { get; set; }

        /// <summary>
        /// 应付单Id
        /// </summary>
        public Guid? DebtId { get; set; }

        /// <summary>
        /// 应收单Id
        /// </summary>
        public Guid? CreditId { get; set; }


        /// <summary>
        /// 收款单号
        /// </summary>
        public string? ReceiveCode { get; set; }


        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 折前金额(原始金额)
        /// </summary>
        public decimal OriginValue { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public DebtDetailStatusEnum Status { get; set; }
    }

    public class DebtDetailOutput: DebtDetailInput
    {
        /// <summary>
        /// 应付单号
        /// </summary>
       public string DebtBillCode { get; set; }

    }
}
