﻿using Inno.CorePlatform.Sell.Application.Extensions;
using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class ProductQueryInput
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        [JsonProperty("productNameId")]
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 所属产品ID 列表
        /// </summary>
        [JsonProperty("productNameIds")]
        [System.Text.Json.Serialization.JsonConverter(typeof(TextJsonListGuidConverter))]
        public List<Guid>? ProductNameIds { get; set; }

        /// <summary>
        /// 厂家Id
        /// </summary>
        [JsonProperty("producerId")]
        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 货号Id列表
        /// </summary>
        [JsonProperty("productIds")]
        public List<Guid>? ProductIds { get; set; }

        /// <summary>
        /// 货号关键字（模糊搜索）
        /// </summary>
        [JsonProperty("likeProductNo")]
        public string? LikeProductNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty("sort")]
        public List<string>? Sort { get; set; }

        /// <summary>
        /// 模糊查询
        /// </summary>
        public string? SearchKey { get; set; }

        /// <summary>
        /// 是否启用:1 正常 ，0 停用 
        /// </summary>
        public int? Status { get; set; } 

        /// <summary>
        /// 分页参数：每页条数
        /// </summary>
        [JsonProperty("limit")]
        public int Limit { get; set; }


        /// <summary>
        /// 分页参数：页数
        /// </summary>

        [JsonProperty("page")]
        public int Page { get; set; }
    }

    public class ProductInfoQueryInput : ProductQueryInput
    {
        /// <summary>
        /// 型号集合查询
        /// </summary>
        [JsonProperty("models")]
        public List<string>? Models { get; set; }
    }

}
