﻿namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 账期起始日修改入参
    /// </summary>
    public class AccountPeroidEditInput : BaseQuery
    {

        /// <summary>
        /// 付款计划ID
        /// </summary>

        public Guid? Id { get; set; }
        /// <summary>
        /// 修改的账期起始日期
        /// </summary>
        public DateTime? AccountingPeriodDateEdit {  get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 附件ids
        /// </summary>
        public List<string> AttachFileIds { get; set; }
        /// <summary>
        /// 当前用户
        /// </summary>
        public string? CurrentUser {  get; set; }
    }
}
