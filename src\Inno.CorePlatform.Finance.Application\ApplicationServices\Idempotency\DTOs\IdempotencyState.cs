﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency.DTOs
{
    /// <summary>
    /// 幂等状态
    /// </summary>
    public class IdempotencyState
    {
        public int FailureCount { get; set; }
        public DateTime? NextRetryTime { get; set; }
        public ErrorResult LastResult { get; set; }
    }
}
