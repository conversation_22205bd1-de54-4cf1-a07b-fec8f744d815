﻿using Inno.CorePlatform.Finance.Application.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.LogServices.Interfaces
{
    /// <summary>
    /// 系统日志服务接口
    /// </summary>
    public interface ISubLogService
    {
        /// <summary>
        /// 通用记录日志实现方法
        /// 顺带记录日志到 Auzer 日志系统中
        /// </summary>
        /// <param name="actionName">方法名称</param>
        /// <param name="parametersJson">核心参数</param>
        /// <param name="operate">业务名称</param>
        /// <param name="logLevel">日志级别（默认Infomation）</param>
        /// <returns></returns>
        Task LogAsync(string actionName, string parametersJson, string operate = "", LogLevelEnum logLevel = LogLevelEnum.Information);

        /// <summary>
        /// 仅记录系统日志到 Azure 日志系统
        /// </summary>
        /// <param name="actionName">方法名</param>
        /// <param name="parametersJson">核心参数</param>
        /// <param name="operate">业务名称</param>
        /// <param name="logLevel">日志级别（默认Infomation）</param>
        /// <returns></returns>
        void LogAzure(string actionName, string parametersJson, string operate = "", LogLevelEnum logLevel = LogLevelEnum.Information);

        /// <summary>
        /// 保存日志到数据库
        /// 如果调用了此方法，则不会记录到 Azure 日志系统中
        /// 另外，调用此方法时，日志级别会被忽略，直接保存到数据库中，不区分日志级别。
        /// 此方法和LogAsync方法不可同时调用。会导致重复记录日志到数据库中。
        /// </summary>
        /// <param name="actionName"></param>
        /// <param name="parametersJson"></param>
        /// <param name="operate"></param>
        Task SaveLogToDB(string? actionName, string? parametersJson, string? operate = "");
    }
}
