﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YamlDotNet.Core;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 返利清单列表返回
    /// </summary>
    public class RebateProvisionItemQueryOutput
    {
        /// <summary>
        /// 返利计提id
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime? BillDate { get; set; }
        public string? BillCode { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public StatusEnum Status { get; set; }

        /// <summary>
        /// 状态说明
        /// </summary>
        public string StatusStr
        {
            get
            {
                return Status.GetDescription();
            }
        }

        /// <summary>
        /// 修改人
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTimeOffset? UpdatedTime { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public ProvisionTypeEnum ProvisionType { get; set; }
        /// <summary>
        /// 状态说明
        /// </summary>
        public string ProvisionTypeStr
        {
            get
            {
                return ProvisionType.GetDescription();
            }
        }

        public DateTimeOffset CreatedTime { get;  set; }
        public string CreatedBy { get;  set; }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 核算部门Code
        /// </summary>
        public string? BusinessDeptShortName { get; set; }
    }

    /// <summary>
    /// 返利清单列表返回
    /// </summary>
    public class RebateProvisionDetailQueryOutput
    {
        /// <summary>
        /// 返利计提Id
        /// </summary>
        public Guid? RebateProvisionItemId { get; set; }
        /// <summary>
        /// 确认函日期
        /// </summary>
        public DateTime? ConfirmationDate { get; set; }

        /// <summary>
        /// 返利不含税金额
        /// </summary>
        public decimal? ConfirmTaxAmount { get; set; }

        /// <summary>
        /// 返利类型 A:平移返利, B:指标返利, C:补偿返利	
        /// </summary>
        public RebateTypeOfKdEnum? RebateType { get; set; }

        /// <summary>
        ///  返利名称
        /// </summary>
        public string RebateTypeStr
        {
            get
            {
                return RebateType.HasValue? RebateType.GetDescription():string.Empty;
            }
        }

        /// <summary>
        /// 返利金额
        /// </summary>
        public decimal? RebateAmount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        public decimal? RebateTaxAmount { get; set; }

        /// <summary>
        /// 下家对应金额
        /// </summary>
        public decimal? NextAmount { get; set; }

        /// <summary>
        /// 下家不含税金额
        /// </summary>
        public decimal? NextTaxAmount { get; set; }

        /// <summary>
        /// 下家返利方式 A:发票, B:优惠劵
        /// </summary>
        public NextRebateMethodEnum? NextRebateMethod { get; set; }

        /// <summary>
        /// 下家返利方式说明
        /// </summary>
        public string NextRebateMethodStr
        {
            get
            {
                return NextRebateMethod.HasValue ? NextRebateMethod.GetDescription() : string.Empty;
            }
        }

        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>  
        public Guid? AgentId { get; set; } 
        public string? PeriodSummary { get; set; }
        /// <summary>
        /// 供应商
        /// </summary> 
        public string? AgentName { get; set; }
    }

    public class RebateProvisionItemTabOutput
    {
        public int AllCount { get; set; }
        public int WaitSubmitCount { get; set; }
        public int WaitAuditCount { get; set; }
        public int ComplateCount { get; set; }
    }
}
