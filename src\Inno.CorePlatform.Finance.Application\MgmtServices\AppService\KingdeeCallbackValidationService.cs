using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Abatment;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    /// <summary>
    /// 金蝶回调验证服务实现
    /// </summary>
    public class KingdeeCallbackValidationService : IKingdeeCallbackValidationService
    {
        private readonly FinanceDbContext _db;
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly IBDSApiClient _bdsApiClient;
        private readonly ILogger<KingdeeCallbackValidationService> _logger;

        public KingdeeCallbackValidationService(
            FinanceDbContext db,
            IInventoryMgmAppService inventoryMgmAppService,
            IBDSApiClient bdsApiClient,
            ILogger<KingdeeCallbackValidationService> logger)
        {
            _db = db;
            _inventoryMgmAppService = inventoryMgmAppService;
            _bdsApiClient = bdsApiClient;
            _logger = logger;
        }

        /// <summary>
        /// 检查批量付款冲销的盘点状态
        /// </summary>
        /// <param name="input">批量付款输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        public async Task CheckInventoryStatusForBatchPayment(List<GenerateAbtInput> input, string operationType)
        {
            try
            {
                if (input == null || !input.Any())
                {
                    return;
                }

                var paymentCode = input.First().PaymentCode;
                _logger.LogInformation("金蝶回调 {OperationType} - 付款单 {PaymentCode} 开始检查盘点状态", operationType, paymentCode);

                // 通过应付明细ID查询应付单获取公司ID
                var debtDetailIds = input.Select(x => x.DebtDetilId).ToList();
                var debtDetails = await _db.DebtDetails.Where(x => debtDetailIds.Contains(x.Id)).AsNoTracking().ToListAsync();
                if (!debtDetails.Any())
                {
                    _logger.LogWarning("金蝶回调 {OperationType} - 付款单 {PaymentCode} 未找到对应的应付明细", operationType, paymentCode);
                    return;
                }

                var debtIds = debtDetails.Where(x => x.DebtId.HasValue).Select(x => x.DebtId.Value).Distinct().ToList();
                var debts = await _db.Debts.Where(x => debtIds.Contains(x.Id)).AsNoTracking().ToListAsync();
                if (!debts.Any())
                {
                    _logger.LogWarning("金蝶回调 {OperationType} - 付款单 {PaymentCode} 未找到对应的应付单", operationType, paymentCode);
                    return;
                }

                var companyIds = debts.Where(x => x.CompanyId.HasValue).Select(x => x.CompanyId.Value).Distinct().ToList();
                foreach (var companyId in companyIds)
                {
                    await CheckInventoryStatusForCompany(companyId, operationType, paymentCode);
                }
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查金蝶回调 {operationType} 盘点状态时发生异常：{ex.Message}";
                _logger.LogError(ex, "金蝶回调盘点状态检查异常 - 操作类型: {OperationType}, 错误: {ErrorMessage}", operationType, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }

        /// <summary>
        /// 检查应收冲销的盘点状态
        /// </summary>
        /// <param name="input">应收冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        public async Task CheckInventoryStatusForCredit(GenerateAbtForCreditInput input, string operationType)
        {
            try
            {
                _logger.LogInformation("金蝶回调 {OperationType} - 单据 {Code} 开始检查盘点状态", operationType, input.Code);

                // 通过应收单号查询应收单获取公司ID
                var creditCodes = input.lstAbtDetail.Select(x => x.BillCode).Distinct().ToList();
                var credits = await _db.Credits.Where(x => creditCodes.Contains(x.BillCode)).AsNoTracking().ToListAsync();
                if (!credits.Any())
                {
                    _logger.LogWarning("金蝶回调 {OperationType} - 单据 {Code} 未找到对应的应收单", operationType, input.Code);
                    return;
                }

                var companyIds = credits.Where(x => x.CompanyId.HasValue).Select(x => x.CompanyId.Value).Distinct().ToList();
                foreach (var companyId in companyIds)
                {
                    await CheckInventoryStatusForCompany(companyId, operationType, input.RecognizeReceiveCode);
                }
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查金蝶回调 {operationType} 盘点状态时发生异常：{ex.Message} - 单据号: {input.Code}";
                _logger.LogError(ex, "金蝶回调盘点状态检查异常 - 操作类型: {OperationType}, 单据号: {Code}, 错误: {ErrorMessage}", 
                    operationType, input.Code, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }

        /// <summary>
        /// 检查收款(退款)与预付单冲销的盘点状态
        /// </summary>
        /// <param name="input">收款冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        public async Task CheckInventoryStatusForPayment(KingdeeAbatementPaymentInput input, string operationType)
        {
            try
            {
                _logger.LogInformation("金蝶回调 {OperationType} - 单据 {Code} 开始检查盘点状态", operationType, input.Code);

                // 通过付款单号查询付款单获取公司ID
                var payment = await _db.Payments.AsNoTracking().FirstOrDefaultAsync(x => x.Code == input.Code);
                if (payment?.CompanyId == null)
                {
                    _logger.LogWarning("金蝶回调 {OperationType} - 单据 {Code} 未找到对应的付款单或公司ID为空", operationType, input.Code);
                    return;
                }

                await CheckInventoryStatusForCompany(payment.CompanyId.Value, operationType, input.Code);
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查金蝶回调 {operationType} 盘点状态时发生异常：{ex.Message} - 单据号: {input.Code}";
                _logger.LogError(ex, "金蝶回调盘点状态检查异常 - 操作类型: {OperationType}, 单据号: {Code}, 错误: {ErrorMessage}", 
                    operationType, input.Code, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }

        /// <summary>
        /// 检查退款与应付单冲销的盘点状态
        /// </summary>
        /// <param name="input">应付冲销输入</param>
        /// <param name="operationType">操作类型</param>
        /// <returns></returns>
        public async Task CheckInventoryStatusForDebt(KingdeeAbatementDebtInput input, string operationType)
        {
            try
            {
                _logger.LogInformation("金蝶回调 {OperationType} - 单据 {Code} 开始检查盘点状态", operationType, input.Code);

                // 通过付款单号查询付款单获取公司ID
                var payment = await _db.Payments.AsNoTracking().FirstOrDefaultAsync(x => x.Code == input.Code);
                if (payment?.CompanyId == null)
                {
                    _logger.LogWarning("金蝶回调 {OperationType} - 单据 {Code} 未找到对应的付款单或公司ID为空", operationType, input.Code);
                    return;
                }

                await CheckInventoryStatusForCompany(payment.CompanyId.Value, operationType, input.Code);
            }
            catch (ApplicationException)
            {
                throw;
            }
            catch (Exception ex)
            {
                var errorMessage = $"检查金蝶回调 {operationType} 盘点状态时发生异常：{ex.Message} - 单据号: {input.Code}";
                _logger.LogError(ex, "金蝶回调盘点状态检查异常 - 操作类型: {OperationType}, 单据号: {Code}, 错误: {ErrorMessage}", 
                    operationType, input.Code, ex.Message);
                throw new ApplicationException(errorMessage);
            }
        }

        /// <summary>
        /// 检查指定公司的盘点状态
        /// </summary>
        /// <param name="companyId">公司ID</param>
        /// <param name="operationType">操作类型</param>
        /// <param name="billCode">单据号</param>
        /// <returns></returns>
        private async Task CheckInventoryStatusForCompany(Guid companyId, string operationType, string billCode)
        {
            // 获取公司名称
            string companyName = companyId.ToString();
            try
            {
                var companyInfos = await _bdsApiClient.GetCompanyInfoAsync(new BDSBaseInput
                {
                    ids = new List<string> { companyId.ToString() }
                });
                var companyInfo = companyInfos?.FirstOrDefault();
                if (companyInfo != null)
                {
                    companyName = $"{companyInfo.companyName}({companyInfo.nameCode})";
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("获取公司信息失败，使用公司ID作为显示名称 - 公司ID: {CompanyId}, 错误: {Error}", companyId, ex.Message);
            }

            // 检查是否正在盘点
            var inventoryCheckResult = await _inventoryMgmAppService.InventoryCheck(companyId);

            if (inventoryCheckResult.Code != CodeStatusEnum.Success)
            {
                var errorMessage = $"金蝶回调 {operationType} 失败：公司 {companyName} 正在盘点中，不允许进行冲销操作 - 认款单号: {billCode}";
                _logger.LogError("金蝶回调盘点状态检查失败 - {ErrorMessage}", errorMessage);
                throw new ApplicationException(errorMessage);
            }

            _logger.LogInformation("金蝶回调 {OperationType} - 单据 {BillCode} 公司 {CompanyName} 盘点状态检查通过", operationType, billCode, companyName);
        }
    }
}
