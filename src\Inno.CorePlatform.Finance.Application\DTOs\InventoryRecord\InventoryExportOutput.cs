﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord
{
    /// <summary>
    /// 应收盘点下载导出
    /// </summary>
    public class CreditExportOutput
    {
        /// <summary>
        /// 盘点单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public DateTime? BillDate { get; set; }

        /// <summary>
        /// 盘点日期
        /// </summary>
        public string BillDateStr
        {
            get
            {
                return BillDate.HasValue ? BillDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 应收单日期
        /// </summary>
        public DateTime? CreditBillDate { get; set; }

        /// <summary>
        /// 应收单日期
        /// </summary>
        public string CreditBillDateStr
        {
            get
            {
                return CreditBillDate.HasValue ? CreditBillDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal Vaule { get; set; }

        /// <summary>
        /// 冲销金额
        /// </summary>
        public decimal AbatedVaule { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目编码
        /// </summary>
        public string? ProjectCode { get; set; }
    }
}
