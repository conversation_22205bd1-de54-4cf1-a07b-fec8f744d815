﻿using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Advance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 垫资单
    /// </summary>
    public class AdvanceBusinessOutput : AdvanceBusinessApply
    {

    }


    /// <summary>
    /// 垫资明细
    /// </summary>
    public class AdvanceBusinessDetailOutput : AdvanceBusinessDetail
    {
        /// <summary>
        /// 垫资单号
        /// </summary>
        /// /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>
        public string ProjectCode { get; set; }



        /// <summary>
        /// 老致新平台单号
        /// </summary>
        public string? OldZXCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        #region 公司信息
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 核准的资金占用总额度（万元）
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 已审批预计垫资额度（万元）
        /// </summary>
        public decimal? ExpectedAmount { get; set; }

        /// <summary>
        /// 截止上月末资金占用金额（万元）
        /// </summary>
        public decimal? TotalAmountOfMonth { get; set; }

        /// <summary>
        /// 尚未收回的应收金额（万元）
        /// </summary>
        public decimal? ReceivableAmountOfNon { get; set; }

        /// <summary>
        /// 已经逾期的应收金额（万元）
        /// </summary>
        public decimal? ReceivableAmountOfTimeout { get; set; }

        #endregion

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid ServiceId { get; set; }

        public string ServiceName { get; set; }

        /// <summary>
        /// 是否发票入账
        /// </summary>
        public bool IsInvoice { get; set; }

        /// <summary>
        /// 业务单元组别
        /// </summary>
        public string? ServiceGroup { get; set; }

        /// <summary>
        /// 供应商付款天数
        /// </summary>
        public int? ProvidePayDays { get; set; }

        #region 医院指标
        /// <summary>
        /// 医院Id
        /// </summary>
        public Guid HospitalId { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 医院年销售额(垫资销售额)万元
        /// </summary>
        public decimal? SalesVolume { get; set; }

        /// <summary>
        /// 医院回款天数（天）
        /// </summary>        
        public int? ReturnMoneyDays { get; set; }

        /// <summary>
        /// 是否核准医院
        /// </summary>
        public bool IsVerify { get; set; }

        /// <summary>
        /// 非核准医院申请原因
        /// </summary>
        public string? NonVerifyRemark { get; set; }

        /// <summary>
        /// 是否全流程接管医院
        /// </summary>
        public bool IsTakeOver { get; set; }

        /// <summary>
        /// 该医院尚未收回的垫资应收金额（万元）
        /// </summary>
        public decimal UnrecycledReceivableAmount { get; set; }

        /// <summary>
        /// 该医院已经逾期的垫资应收金额（万元）
        /// </summary>
        public decimal TimeOutReceivableAmount { get; set; }

        /// <summary>
        /// 上游在该医院垫资应收金额（万元）
        /// </summary>
        public decimal PreUnrecycledReceivableAmount { get; set; }

        /// <summary>
        /// 上游在该医院逾期垫资应收金额（万元）
        /// </summary>
        public decimal PreTimeOutReceivableAmount { get; set; }
        #endregion


        /// <summary>
        /// 供应链金融折扣
        /// </summary>
        public decimal? SupplyChainDiscounts { get; set; }


        /// <summary>
        /// 终止时间
        /// </summary>
        public DateTime EndDateTime { get; set; }

        #region 风控指标
        /// <summary>
        /// 年化垫资利率（%）
        /// </summary>
        public decimal? RateOfYear { get; set; }

        /// <summary>
        /// 垫资比例（%）
        /// </summary>
        public decimal? Ratio { get; set; }

        /// <summary>
        /// 实际占用天数（天）
        /// </summary>
        public decimal? RealUseDays { get; set; }

        /// <summary>
        /// 实际供应链金融折扣（%）
        /// </summary>
        public decimal? RealSupplyChainDiscounts { get; set; }

        /// <summary>
        /// 合计折扣（%）
        /// </summary>
        public decimal? TotalDiscounts { get; set; }

        /// <summary>
        ///预计垫资金额（万元）
        /// </summary>
        public decimal? ExpectAmount { get; set; }

        /// <summary>
        ///预计垫资利息收入（万元）
        /// </summary>
        public decimal? ExpectInterestAmount { get; set; }
        #endregion


        public string CreditDateFormat
        {
            get
            {
                return CreditDate.ToString("yyyy-MM-dd");
            }
        }
        public string DebtDateFormat
        {
            get
            {
                return DebtDate.HasValue ? DebtDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string InvoiceDateFormat
        {
            get
            {
                return InvoiceDate.HasValue ? InvoiceDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string PaymentDateFormat
        {
            get
            {
                return PaymentDate.HasValue ? PaymentDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ExpectPaymentDateFormat
        {
            get
            {
                return ExpectPaymentDate.HasValue ? ExpectPaymentDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ReceiveDateFormat
        {
            get
            {
                return ReceiveDate.HasValue ? ReceiveDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public string ExpectReceiveDateFormat
        {
            get
            {
                return ExpectReceiveDate.HasValue ? ExpectReceiveDate.Value.ToString("yyyy-MM-dd") : "";
            }
        }
        public decimal ReceiveAmount
        {
            get
            {
                return ReceiveDate.HasValue ? CreditValue : 0;
            }
        }
        public decimal PaymentAmount
        {
            get
            {
                return PaymentDate.HasValue ? DebtValue : 0;
            }
        }
        /// <summary>
        /// 垫资天数
        /// </summary>
        public int AdvanceDays
        {
            get { return ReceivePeriod - AccountPeriod; }
        }
        /// <summary>
        /// 垫资应收到期时间
        /// 已付款：付款日期+垫资天数
        /// 未付款：开票日期+回款天数，若未开票，则未应收日期+回款天数
        /// </summary>
        public DateTime AdvanceExpireDate
        {
            get
            {
                if (PaymentDate == null)
                {
                    if (InvoiceDate != null)
                    {
                        return InvoiceDate.Value.Date.AddDays(ReceivePeriod);
                    }
                    else
                    {
                        return CreditDate.AddDays(ReceivePeriod);
                    }
                }
                else
                {
                    return PaymentDate.Value.AddDays(AdvanceDays);
                }
            }
        }
        public string AdvanceExpireDateFormat
        {
            get
            {
                return AdvanceExpireDate.ToString("yyyy-MM-dd");
            }
        }
        /// <summary>
        /// 逾期天数
        /// 如果已付款但未收款，为表格统计时间-垫资应收到期时间；
        /// 如果已付款且已收款，为收款日期-垫资应收到期时间；
        /// 如果未付款，不管是否收款，显示【未垫资】
        /// </summary>
        public string OverdueDays
        {
            get
            {
                if (PaymentDate != null && ReceiveDate == null)
                {
                    //return (DateTime.Now.Date - AdvanceExpireDate).Days.ToString();
                    return (DateTimeHelper.GetCurrentDate().Date - AdvanceExpireDate.Date).Days.ToString();
                }
                else if (PaymentDate != null && ReceiveDate != null)
                {
                    return (ReceiveDate.Value - AdvanceExpireDate).Days.ToString();
                }
                else
                {
                    return "未垫资";
                }
            }
        }
        /// <summary>
        /// 逾期状态
        /// 如果收款日期为空，逾期天数>0,显示【应收已逾期未回款】
        /// 如果收款日期不为空，逾期天数>0,显示【应收已逾期已回款】
        /// 如果收款日期为空，逾期天数<0, 显示【应收未逾期未回款】
        /// 如果收款日期不为空，逾期天数<0, 显示【应收未逾期已回款】
        /// 如果逾期天数显示【未垫资】，则显示【应收未逾期未垫资】
        /// </summary>
        public string OverdueStatus
        {
            get
            {
                if (OverdueDays == "未垫资")
                {
                    return "应收未逾期未垫资";
                }
                else
                {
                    if (!Int32.TryParse(OverdueDays, out int days))
                    {
                        days = 0;
                    }
                    if (ReceiveDate == null && days >= 0)
                    {
                        return "应收已逾期未回款";
                    }
                    else if (ReceiveDate == null && days < 0)
                    {
                        return "应收未逾期未回款";
                    }
                    else if (ReceiveDate != null && days >= 0)
                    {
                        return "应收已逾期已回款";
                    }
                    else if (ReceiveDate != null && days < 0)
                    {
                        return "应收未逾期已回款";
                    }
                    else
                    {
                        return "";
                    }
                }
            }
        }
        /// <summary>
        /// 垫资日利率
        /// 日利率=供应链金融折扣/垫资天数
        /// 供应链金融年化利率=供应链金融折扣/垫资天数*360
        /// </summary>
        public decimal DailyRate
        {
            get
            {
                return AdvanceDays != 0 ? SCFDiscount / AdvanceDays / 100 : 0;
            }
        }
        /// <summary>
        /// 逾期利息
        /// 如果逾期天数>0，为付款金额* 垫资日利率 *逾期天数，否则为0
        /// 如果逾期天数显示【未垫资】，则为0
        /// </summary>
        public decimal OverdueInterest
        {
            get
            {
                if (OverdueDays == "未垫资")
                {
                    return 0;
                }
                else
                {
                    if (!Int32.TryParse(OverdueDays, out int days))
                    {
                        days = 0;
                    }
                    if (days < 0)
                    {
                        return 0;
                    }
                    return DebtValue * DailyRate * days;
                }
            }
        }
        /// <summary>
        /// 提前回款利息
        /// 如果逾期天数大于等于0，返回0
        /// 如果有回款，为付款金额*（供应链金融年化利率（%）/360）*逾期天数*-1，（*-1是因为提前回款的话逾期天数是负数）否则为0
        /// </summary>
        public decimal AdvanceReceiveInterest
        {
            get
            {
                if (!ReceiveDate.HasValue)
                {
                    return 0;
                }
                if (!Int32.TryParse(OverdueDays, out int days))
                {
                    return 0;
                }
                if (days >= 0)
                {
                    return 0;
                }
                return DebtValue * DailyRate * days * -1;
            }
        }
        /// <summary>
        /// 资金占用余额
        /// 付款金额-（收款金额*折扣）
        /// </summary>
        public decimal OccupyFundBalance
        {
            get
            {
                if (ReceiveDate.HasValue)
                {
                    return 0;
                }
                return (PaymentDate.HasValue ? DebtValue : 0) - ((ReceiveDate.HasValue ? CreditValue : 0) * Discount / 100);
            }
        }
        /// <summary>
        /// 基础毛利
        /// 应收金额/(1+销售税率/100)*基础折扣
        /// </summary>
        public decimal BasicProfit
        {
            get
            {
                if (OverdueDays == "未垫资")
                {
                    return 0;
                }
                return CreditValue / (1 + (SalesTaxRate ?? 0) / 100m) * BaseDiscount / 100;
            }
        }
        /// <summary>
        /// 垫资利息收入
        /// 应收金额/(1+销售税率/100)*供应链金融折扣
        /// </summary>
        public decimal AdvanceInterest
        {
            get
            {
                if (OverdueDays == "未垫资")
                {
                    return 0;
                }
                return CreditValue / (1 + (SalesTaxRate ?? 0) / 100m) * SCFDiscount / 100;
            }
        }
        /// <summary>
        /// 合计毛利
        /// 基础毛利+垫资利息收入
        /// </summary>
        public decimal TotalProfit
        {
            get
            {
                return BasicProfit + AdvanceInterest;
            }
        }
        /// <summary>
        /// 校验
        /// 合计毛利*(1+销售税率/100)-（应收金额-应付金额）
        /// </summary>
        public decimal Check
        {
            get
            {
                if (OverdueDays == "未垫资")
                {
                    return 0;
                }
                return TotalProfit * (1 + (SalesTaxRate ?? 0) / 100) - (CreditValue - DebtValue);
            }
        }
        /// <summary>
        /// 开票后实际支付（天数）
        /// 付款日期-开票日期
        /// </summary>
        public string FactPayDays
        {
            get
            {
                if (InvoiceDate == null || PaymentDate == null)
                {
                    return "";
                }
                else
                {
                    return (PaymentDate.Value - InvoiceDate.Value.Date).Days.ToString();
                }
            }
        }
        /// <summary>
        /// 提示（放款风险）
        /// 开票后实际支付（天数）-90>=0，为开票后实际支付（天数）-90；
        /// 开票后实际支付（天数）-90<0,“风险”
        /// </summary>
        public string Risk
        {
            get
            {
                if (InvoiceDate == null || PaymentDate == null)
                {
                    return "";
                }
                else
                {
                    var days = (PaymentDate.Value - InvoiceDate.Value.Date).Days - 90;
                    return days < 0 ? "风险" : days.ToString();
                }
            }
        }
        /// <summary>
        /// 实际供应链金融折扣
        /// </summary>
        public decimal ActualSCFDiscount
        {
            get
            {
                if (ADFDiscount == null|| ADFDiscount == 0)
                {
                    ADFDiscount = 100;
                }
                return Math.Round(SCFDiscount / ((ADFDiscount ?? 100) * 0.01m), 2);
            }
        }
        /// <summary>
        /// 合计折扣
        /// </summary>
        public decimal TotalDZInterestRate
        {
            get
            {
                //基本折扣+SPD折扣+实际供应链金融折扣
                return BaseDiscount + SPDDiscount + SCFDiscount;
            }
        }
    }


    /// <summary>
    /// 垫资明细汇总
    /// </summary>
    public class AdvanceFundBusinessDetailGroupOutput
    {
        public string OverdueStatus { get; set; }
        public decimal OverdueInterest { get; set; }
        public decimal AdvanceReceiveInterest { get; set; }
        public decimal OccupyFundBalance { get; set; }
        public decimal BasicProfit { get; set; }
        public decimal AdvanceInterest { get; set; }
        public decimal TotalProfit { get; set; }
    }

}
