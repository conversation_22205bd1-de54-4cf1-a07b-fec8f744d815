using EasyCaching.Core;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Logging;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.DebtDetail;
using Inno.CorePlatform.Finance.Application.Enums;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using RestSharp;
using System.Text;
using static Inno.CorePlatform.Finance.Application.DTOs.PrepayBillInput;

namespace Inno.CorePlatform.Finance.Adapter.Clients
{
    public class KingdeeApiClient : IKingdeeApiClient
    {
        private protected IOptionsSnapshot<KingdeeSetting> _kingdeeSetting { get; set; }
        private readonly HttpClient _httpClient;
        private readonly IEasyCachingProvider _easyCaching;
        private readonly IConfiguration _configuration;
        private readonly ISubLogService _subLogService;
        private readonly FinanceDbContext _db;
        private readonly IUnitOfWork _unitOfWork;
        //private readonly ILogger<KingdeeApiClient> _logger;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="configuration"></param>
        public KingdeeApiClient(
            IConfiguration configuration,
            HttpClient httpClient,
            IOptionsSnapshot<KingdeeSetting> kingdeeSetting,
            IEasyCachingProvider easyCaching,
            ISubLogService subLogService,
            IUnitOfWork unitOfWork,
            FinanceDbContext db)
        {
            _httpClient = httpClient;
            _kingdeeSetting = kingdeeSetting;
            _easyCaching = easyCaching;
            _configuration = configuration;
            _subLogService = subLogService;
            _unitOfWork = unitOfWork;
            //_logger = logger;
            _db = db;
        }

        /// <summary>
        /// 获取appToken
        /// </summary>
        /// <returns></returns>
        private async Task<KingdeeAuthResultDTO<ApptokenData>> GetApptokenAsync()
        {
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/api/getAppToken.do");
                request.AddHeader("Content-Type", "application/json");
                var body = new
                {
                    appId = _configuration["KingdeeSetting:AppId"],//_kingdeeSetting.Value.AppId,
                    appSecret = _configuration["KingdeeSetting:AppSecret"],//_kingdeeSetting.Value.AppSecret,
                    tenantid = _configuration["KingdeeSetting:Tenantid"],//_kingdeeSetting.Value.Tenantid,
                    accountId = _configuration["KingdeeSetting:AccountId"],//_kingdeeSetting.Value.AccountId,
                    language = _configuration["KingdeeSetting:Language"] //_kingdeeSetting.Value.Language
                };
                var requestBody = JsonConvert.SerializeObject(body);
                request.AddParameter("application/json", body, ParameterType.RequestBody);
                var ret = await restClient.PostAsync<KingdeeAuthResultDTO<ApptokenData>>(request);
                return ret;
            }
        }

        /// <summary>
        /// 获取accessToken
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<string> GetAccesstokenAsync()
        {
            var token = await _easyCaching.GetAsync<string>("kingdeeaccesstoken");
            if (token == null || string.IsNullOrEmpty(token.Value))
            {
                var apptokenRes = await GetApptokenAsync();
                if (apptokenRes.status)
                {
                    using (var restClient = new RestClient(_httpClient))
                    {
                        var request = new RestRequest($"{_kingdeeSetting.Value.Host}/api/login.do");
                        request.AddHeader("Content-Type", "application/json");
                        var body = new
                        {
                            user = _configuration["KingdeeSetting:User"],// _kingdeeSetting.Value.User,
                            apptoken = apptokenRes.data.app_token,
                            tenantid = _configuration["KingdeeSetting:Tenantid"],//_kingdeeSetting.Value.Tenantid,
                            accountId = _configuration["KingdeeSetting:AccountId"],// _kingdeeSetting.Value.AccountId,
                            usertype = _configuration["KingdeeSetting:Usertype"]// _kingdeeSetting.Value.Usertype
                        };
                        request.AddParameter("application/json", body, ParameterType.RequestBody);
                        var ret = await restClient.PostAsync<KingdeeAuthResultDTO<AccesstokenData>>(request);
                        if (ret.status)
                        {
                            await _easyCaching.SetAsync<string>("kingdeeaccesstoken", ret.data.access_token, new TimeSpan(0, 59, 0));
                        }
                        else
                        {
                            throw new Exception("获取金蝶accesstoken出错，错误信息：" + ret.data.error_desc);
                        }
                        return ret.data.access_token;
                    }
                }
                else
                {
                    throw new Exception("获取金蝶Apptoken出错，请联系管理员");
                }
            }
            else
            {
                return token.Value;
            }
        }

        /// <summary>
        /// 创建订阅日志
        /// </summary>
        /// <param name="source">订阅来源</param>
        /// <param name="content">内容</param>
        /// <param name="userName">用户名</param>
        /// <param name="isCommit">是否提交</param>
        /// <returns></returns>
        public async Task CreateSubLog(SubLogSourceEnum source, string content, string userName, string operate, bool isCommit = true)
        {
            await _subLogService.LogAsync(source.ToString(), content, operate);
        }

        public async Task Test()
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/ap/ap_finapbill/batchSave");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var body = new
                {
                    billno = "1",
                    billtypeid_number = "2",
                    bizdate = DateTime.Now.ToString("yyyy-MM-dd")
                };
                request.AddParameter("application/json", new { data = body }, ParameterType.RequestBody);
                var ret = await restClient.PostAsync<object>(request);
            }
        }

        /// <summary>
        /// 批量付款执行，推送应付付款计划到金蝶系统
        /// </summary>
        /// <param name="lstDetail"></param>
        /// <returns></returns>
        public Task<bool> PushDebtDetailAsync(List<DebtDetailInput> lstDetail)
        {
            //TODO
            return Task.FromResult(true);
            // throw new NotImplementedException();
        }

        /// <summary>
        /// 保存财务应收单
        /// </summary>
        /// <param name="kingdeeCredits"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushCreditsToKingdee(List<KingdeeCredit> kingdeeCredits, string classify, string preRequestBody)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/batchSaveArFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = kingdeeCredits };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存财务应收单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"推送应收到金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var requestBody = JsonConvert.SerializeObject(para);
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "保存财务应收单-推送金蝶", false);
                    var tempStoreTokKngdees = kingdeeCredits.Select(p => new TempStoreToKingdeeLogPo
                    {
                        Id = Guid.NewGuid(),
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now,
                        Code = p.billno,
                        PushDateTime = DateTime.Now,
                        RequestBody = requestBody,
                        ResponseBody = responseBody,
                        Classify = classify,
                        PreRequestBody = preRequestBody
                    }).ToList();

                }
            }
        }

        /// <summary>
        /// 保存财务应付单
        /// </summary>
        /// <param name="kingdeeDebts"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushDebtsToKingdee(List<KingdeeDebt> kingdeeDebts, string classify, string preRequestBody)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchSaveApFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = kingdeeDebts };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存财务应付单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"保存财务应付单[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    _subLogService.LogAzure("PushDebtsToKingdee", $"保存财务应付单[金蝶]异常，原因：{ex.Message + ex.InnerException?.Message}", "保存财务应付单");
                    //_logger.LogError($"保存财务应付单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                    throw new Exception($"保存财务应付单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "保存财务应付单-推送金蝶", false);
                    var tempStoreTokKngdees = kingdeeDebts.Select(p => new TempStoreToKingdeeLogPo
                    {
                        Id = Guid.NewGuid(),
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now,
                        Code = p.billno,
                        PushDateTime = DateTime.Now,
                        RequestBody = requestBody,
                        ResponseBody = responseBody,
                        Classify = classify,
                        PreRequestBody = preRequestBody
                    }).ToList();

                }
            }
        }

        /// <summary>
        /// 更改收入确认标识到金蝶系统
        /// </summary>
        /// <param name="incomeIsConfirm"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushIncomeIsCofirm(KingdeeIncomeIsConfirm incomeIsConfirm)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/updateiscofirm");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.Timeout = new TimeSpan(0, 3, 0);
                var jsonStr = JsonConvert.SerializeObject(incomeIsConfirm);
                request.AddParameter("application/json", incomeIsConfirm, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "更改收入确认标识到金蝶系统-金蝶返回");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "更改收入确认标识到金蝶系统-推送金蝶", false);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"金蝶更改收入确认标识出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 保存付款申请单
        /// </summary>
        /// <param name="kingdeePayApply"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushPaymentApplyToKingdee(KingdeePayApplyDto kingdeePayApply)
        {
            // 调用重构后的方法，使用默认的GUID作为RequestId
            return await PushPaymentApplyToKingdee(kingdeePayApply, Guid.NewGuid().ToString());
        }

        /// <summary>
        /// 保存付款申请单（重构版本，支持RequestId）
        /// </summary>
        /// <param name="kingdeePayApply">付款申请数据</param>
        /// <param name="requestId">请求ID，用于幂等性控制</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushPaymentApplyToKingdee(KingdeePayApplyDto kingdeePayApply, string requestId)
        {
            //return BaseResponseData<int>.Success("操作成功");
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/cas/batchSaveCasPayApplayBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", requestId);
                var para = new { data = kingdeePayApply };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    _subLogService.LogAzure("PushPaymentApplyToKingdee",$"RequestId: {requestId}, 保存付款申请单-推送金蝶:{jsonStr}", "保存付款申请单");
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存付款申请单-金蝶返回");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存付款申请单-推送金蝶", false);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"保存付款申请单[金蝶]，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    _subLogService.LogAzure("PushPaymentApplyToKingdee", $"RequestId: {requestId}, 保存付款申请单-推送金蝶异常:{ex.Message + ex.InnerException?.Message}", "保存付款申请单",LogLevelEnum.Error);
                    throw new Exception($"保存付款申请单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 查询收款处理
        /// </summary>
        /// <param name="incomeIsConfirm"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<RecognizeReceiveOutput>>> PullReceiveBills(RecognizeReceiveInput kingdeeReceiveInput)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/queryCasRecbill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = kingdeeReceiveInput };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);

                await CreateSubLog(SubLogSourceEnum.PushKingdee, JsonConvert.SerializeObject(request), "none", "查询收款处理-推送金蝶", true);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<List<RecognizeReceiveOutput>>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<List<RecognizeReceiveOutput>>>(restClient, request, "查询收款处理-金蝶返回");
                    if (ret.status)
                    {
                        var res = BaseResponseData<List<RecognizeReceiveOutput>>.Success("操作成功");
                        res.Data = ret.data;
                        return res;
                    }
                    else
                    {
                        return BaseResponseData<List<RecognizeReceiveOutput>>.Failed(500, $"从金蝶拉取收款单出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"从金蝶拉取收款单出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 指定收款类型
        /// </summary>
        /// <param name="incomeIsConfirm"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ModifyCollectionType(List<modifyCollectionTypeInput> input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/modifyCollectionType");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "指定收款类型-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "指定收款类型-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"指定收款类型[金蝶]，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"指定收款类型[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 收款处理抄发票,订单信息
        /// </summary>
        /// <param name="Input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PushBathSaveReceiveBills(RecognizeReceiveBatchSaveInput Input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/batchSaveAcceptance");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = Input };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "收款处理抄发票,订单信息-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "收款处理抄发票-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"收款处理抄发票,订单信息出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"收款处理抄发票,订单信息出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        ///保存认款单
        /// </summary>
        /// <param name="Inputs"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PushBatchSaveAcceptances(List<BatchSaveAcceptanceInput> Inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/batchSaveAcceptance");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = Inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存认款单-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存认款单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金蝶]保存认款单出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金蝶]保存认款单出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 保存开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <param name="flow">N=保存后悔删除，Y=保存后不会删除</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushCustomizeInvoice(List<PushCustomizeInvoiceSaveInput> input, string flow = "Y")
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/saveBillingApplication");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input, flow = flow };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    _subLogService.LogAzure("PushCustomizeInvoice", $"保存开票申请-推送金蝶:{jsonStr}", "保存开票申请");
                  
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存开票申请-金蝶返回");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存开票申请-推送金蝶", false);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金碟]保存开票申请，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金碟]保存开票申请，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        ///保存付款调整单
        /// </summary>
        /// <param name="Inputs"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> SavePaymentAdjustment(List<SavePaymentAdjustmentInput> Inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/savePaymentAdjustment");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = Inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存付款调整单-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存付款调整单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金蝶]保存付款调整单，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金蝶]保存付款调整单，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 自动审核开票申请
        /// </summary>
        /// <param name="Input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> AuditBillingApplication(AuditBillingApplicationInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/auditBillingApplication");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var jsonStr = JsonConvert.SerializeObject(input);
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "自动审核开票申请-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "自动审核开票申请-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金碟]自动审核开票申请，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金碟]自动审核开票申请，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 撤销发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> ReturnCustomizeInvoice(ReturnCustomizeInvoiceSaveInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/BillingReturnApplication");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "撤销发票-金蝶返回");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "撤销发票-推送金蝶", false);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金碟]撤销发票出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金碟]撤销发票出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 暂存出库保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PushStoreOutToKingdeeWithoutFinance(List<HoldStockRemovalInput> inputs, string classify, string preRequestBody)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/holdStockRemoval");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "暂存出库单保存-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金蝶]暂存出库单保存，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var requestBody = JsonConvert.SerializeObject(para);
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "暂存出库单保存-推送金蝶", true);
                    var tempStoreTokKngdees = inputs.Select(p => new TempStoreToKingdeeLogPo
                    {
                        Id = Guid.NewGuid(),
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now,
                        Code = p.billno,
                        AccountingDateTime = p.jfzx_tallydate,
                        PushDateTime = DateTime.Now,
                        RequestBody = requestBody,
                        ResponseBody = responseBody,
                        Classify = classify,
                        PreRequestBody = preRequestBody
                    }).ToList();

                }
            }
        }

        /// <summary>
        /// 暂存入库单保存
        /// </summary>
        /// <param name="storeOuts"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PushStoreInToKingdeeWithoutFinance(List<HoldStorageInput> inputs, string classify, string preRequestBody)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/holdStorage");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "暂存入库保存-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"【金蝶】暂存入库保存出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"【金蝶】暂存入库单保存出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var requestBody = JsonConvert.SerializeObject(para);
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "暂存入库保存-推送金蝶", true);
                    var tempStoreTokKngdees = inputs.Select(p => new TempStoreToKingdeeLogPo
                    {
                        Id = Guid.NewGuid(),
                        CreatedTime = DateTime.Now,
                        UpdatedTime = DateTime.Now,
                        Code = p.billno,
                        AccountingDateTime = p.jfzx_tallydate,
                        PushDateTime = DateTime.Now,
                        RequestBody = requestBody,
                        ResponseBody = responseBody,
                        Classify = classify,
                        PreRequestBody = preRequestBody
                    }).ToList();

                }
            }
        }

        /// <summary>
        /// 获取回执单地址
        /// </summary>
        /// <param name="input"></param>
        /// <param name="type">单据类型(付款/退款:payBill,收款:recBill)	</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<List<ReceiptNumberModelOutput>>> SelectTheReceiptNumber(List<ReceiptNumberModelInput> input, string type)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/selectTheReceiptNumber");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input, type = type };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "获取回执单下载地址-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<List<ReceiptNumberModelOutput>>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<List<ReceiptNumberModelOutput>>>(restClient, request, "获取回执单下载地址-金蝶返回");
                    if (ret.status)
                    {
                        var res = BaseResponseData<List<ReceiptNumberModelOutput>>.Success("操作成功");
                        res.Data = ret.data;
                        return res;
                    }
                    else
                    {
                        return BaseResponseData<List<ReceiptNumberModelOutput>>.Failed(500, $"获取回执单下载地址出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取回执单下载地址出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 应付付款结算
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PaymentSettlement(List<PaymentSettlementInput> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var asstbillnos = inputs.Select(p => p.asstbillno).ToList();
                var payments = await _db.Payments.Where(p => asstbillnos.Contains(p.Code)).ToListAsync();
                foreach (var item in inputs)
                {
                    var payment = payments.Where(p => p.Code == item.asstbillno).FirstOrDefault();
                    if (payment != null)
                    {
                        if (string.IsNullOrEmpty(payment.OriginCode) && string.IsNullOrEmpty(payment.PurchaseCode) && string.IsNullOrEmpty(payment.PaymentAutoItemCode))
                        {
                            throw new ApplicationException($"付款单的原始单号不能为空，付款单：{item.asstbillno}");
                        }

                        if (string.IsNullOrEmpty(payment.OriginCode))
                        {
                            var nos = item.asstbillno.Split("-");
                            if (item.asstbillno.Contains("PV-"))
                            {
                                item.asstbillno = nos[0] + "-" + nos[1] + "-" + nos[2];
                            }
                            else if (item.asstbillno.Contains("PA-"))
                            {
                                item.asstbillno = nos[0] + "-" + nos[1] + "-" + nos[2] + "-" + nos[3];
                            }
                        }
                        else
                        {
                            item.asstbillno = payment.OriginCode;
                        }
                    }
                }
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/paymentSettlement");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "应付付款结算-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "应付付款结算-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"【金蝶】应付付款结算出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    //throw new Exception($"【金蝶】应付付款结算出错，原因：{ex.Message + ex.InnerException?.Message}");
                    return BaseResponseData<int>.Failed(500, ex.Message);
                }
            }
        }
        /// <summary>
        /// 应付冲应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> PayablesOffsetReceivables(List<PaymentSettlementInput> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/jfzx_ap_ext/ap/payablesOffsetReceivables");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.Timeout = new TimeSpan(0, 3, 0);
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "应付冲应收-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "应付冲应收-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"【金蝶】应付冲应收，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 多发票指定应付
        /// </summary>
        /// <param name="input">包含finentry数组、invoiceno和coreBillNo的请求对象</param>
        /// <returns>操作结果</returns>
        public async Task<BaseResponseData<int>> ManyInvoiceSpecifyApFin(ManyInvoiceSpecifyApFinInput input)
        {
            // 格式化金额为两位小数
            foreach (var entry in input.finentry)
            {
                entry.f_usedamt = decimal.Parse(entry.f_usedamt.ToString("F2"));
            }

            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/manyInvoiceSpecifyApFin");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);

                // 使用发票号和核心单据号作为幂等性键，确保相同的请求不会重复处理
                string idempotencyKey = $"{input.invoiceno}";
                request.AddHeader("Idempotency-Key", Guid.NewGuid());

                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);

                try
                {
                    _subLogService.LogAzure("ManyInvoiceSpecifyApFin", $"调用金蝶接口, 发票号: {input.invoiceno}, 核心单据号: {input.coreBillNo}, 幂等性键: {idempotencyKey}", "多发票指定应付");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "多发票指定应付-推送金蝶", false);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "多发票指定应付-金蝶返回");

                    if (ret.status)
                    {
                        _subLogService.LogAzure("ManyInvoiceSpecifyApFin", $"调用金蝶接口成功, 发票号: {input.invoiceno}, 核心单据号: {input.coreBillNo}, 幂等性键: {idempotencyKey}", "多发票指定应付");
                        
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        
                        _subLogService.LogAzure("ManyInvoiceSpecifyApFin", $"调用金蝶接口失败, 发票号: {input.invoiceno}, 核心单据号: {input.coreBillNo}, 幂等性键: {idempotencyKey}", "多发票指定应付");
                        // 如果是已存在的错误，返回成功
                        if (ret.message != null && ret.message.Contains("已存在"))
                        {
                            _subLogService.LogAzure("ManyInvoiceSpecifyApFin", $"数据已存在-视为成功, 发票号: {input.invoiceno}, 核心单据号: {input.coreBillNo}, 幂等性键: {idempotencyKey}", "多发票指定应付");
                            
                            return BaseResponseData<int>.Success("数据已存在，操作成功");
                        }

                        return BaseResponseData<int>.Failed(500, $"【金蝶】多发票指定应付出错，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    _subLogService.LogAzure("ManyInvoiceSpecifyApFin", $"调用金蝶接口异常, 发票号: {input.invoiceno}, 核心单据号: {input.coreBillNo}, 幂等性键: {idempotencyKey}", "多发票指定应付",LogLevelEnum.Error);
                    return BaseResponseData<int>.Failed(500, $"【金蝶】多发票指定应付出错，原因：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 单据信息回滚
        /// </summary>
        /// <param name="rollBackBill"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RollBackBill(RollBackBillDto rollBackBill)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/documentInformationRollback");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = new List<object> { rollBackBill } };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "单据信息回滚-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "单据信息回滚-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                    return BaseResponseData<int>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 金蝶财务数据盘点生成
        /// </summary>
        /// <param name="companyNameCode"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CreateKingdeeInventory(string companyNameCode, DateTime billDate, string accountPeriod)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/api/saveInventorySheet");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = new { settleorg = companyNameCode, jfzx_date = billDate, accountPeriod = accountPeriod } };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "金蝶财务数据盘点生成-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "金蝶财务数据盘点生成-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                    return BaseResponseData<int>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 撤销销售认款
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelSaleReceive(string itemCode)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/cancellationSubscription");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = new List<string>() { itemCode } };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "撤销销售认款-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "撤销销售认款-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, ex.Message);
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 进项发票提交到金蝶
        /// </summary>
        /// <param name="inputBillSubmitDto"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SubInputBillToKingdee(KingdeeInputBillSubmitDto inputBillSubmitDto)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/invoiceSpecifyApFin");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputBillSubmitDto };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "进项发票提交到金蝶-推送金蝶", false);
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "进项发票提交到金蝶-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, ex.Message);
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 汇率查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<ExchangeRateQueryOutput>> ExchangeRateQuery(ExchangeRateQueryInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/base/bd_exrate_tree/ExchangeRateQuery");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input, pageSize = 10, pageNo = 1 };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "汇率查询-推送金蝶", false);
                    //var kind = await restClient.PostAsync<KingdeeBusinessResult<ExchangeRateQueryOutput>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<ExchangeRateQueryOutput>>(restClient, request, "汇率查询-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<ExchangeRateQueryOutput>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<ExchangeRateQueryOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<ExchangeRateQueryOutput>.Failed(500, "【金蝶】汇率查询：" + ex.Message);
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 信用证-保存业务申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SaveCoreToBizapply(saveCoreToBizapplyInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/lc/lc/saveCoreToBizapply");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "信用证-保存业务申请单-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "信用证-保存业务申请单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, ex.Message);
                    //throw new Exception($"回滚金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 信用证列表查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<getLetterCreditOutput>>> GetLetterCredit(getLetterCreditInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/jfzx_lc_ext/lc/getLetterCredit");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "信用证列表查询-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<List<getLetterCreditOutput>>>(restClient, request, "信用证列表查询-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<List<getLetterCreditOutput>>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<List<getLetterCreditOutput>>.Failed(500, kind.message ?? "【金蝶】信用证列表查询错误");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<List<getLetterCreditOutput>>.Failed(500, "【金蝶】信用证列表查询错误：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 发票附件查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<List<QueryInvoiceAttachmentOutput>>> QueryInvoiceAttachment(QueryInvoiceAttachmentInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/queryInvoiceAttachment");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "发票附件查询-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var kind = await restClient.PostAsync<KingdeeBusinessResult<List<QueryInvoiceAttachmentOutput>>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<List<QueryInvoiceAttachmentOutput>>>(restClient, request, "发票附件查询-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<List<QueryInvoiceAttachmentOutput>>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<List<QueryInvoiceAttachmentOutput>>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<List<QueryInvoiceAttachmentOutput>>.Failed(500, "【金蝶】发票附件查询：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 查询退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<QueryPaymentRefundOutput>> QueryPaymentRefund(QueryPaymentRefundInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/queryPaymentRefund");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "查询退款数据-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    // var kind = await restClient.PostAsync<KingdeeBusinessResult<QueryPaymentRefundOutput>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<QueryPaymentRefundOutput>>(restClient, request, "查询退款数据-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<QueryPaymentRefundOutput>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<QueryPaymentRefundOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<QueryPaymentRefundOutput>.Failed(500, "【金蝶】查询退款数据：" + ex.Message);
                }
            }
        }

        private async Task<T> DeserializeKindResponse<T>(RestClient restClient, RestRequest request, string operate)
        {
            var restResponse = await restClient.PostAsync(request);
            if (restResponse != null && !string.IsNullOrEmpty(restResponse.Content))
            {
                _subLogService.LogAzure("金蝶响应结果", restResponse.Content.ToJson(), operate);
            }

            var kind = JsonConvert.DeserializeObject<T>(restResponse.Content);
            return kind;
        }

        /// <summary>
        /// 保存或更新退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> SaveOrUpdateRefund(SaveOrUpdateRefundInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/cas/saveOrUpdateRefund");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存或更新退款申请单-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<SaveOrUpdateRefundOutput>>(restClient, request, "保存或更新退款申请单-金蝶返回");
                    if (ret.status)
                    {
                        var res = BaseResponseData<SaveOrUpdateRefundOutput>.Success("操作成功");
                        res.Data = ret.data;
                        return res;
                    }
                    else
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "【金蝶】保存或更新退款申请单：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "【金蝶】保存或更新退款申请单：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 删除退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RefundDelete(RefundDeleteInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/refundDelete");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "删除退款数据-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "删除退款数据-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "【金蝶】删除退款数据：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, "【金蝶】删除退款数据：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 保存或修改行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> SaveLineNameLineNumber(SaveLineNameLineNumberInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/base/base/saveLineNameLineNumber");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { model = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存或修改行名行号-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存或修改行名行号-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<object>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<object>.Failed(500, "【金蝶】保存或修改行名行号：" + kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, "【金蝶】保存或修改行名行号：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 删除行名行号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> DeleteLineNameLineNumber(DeleteLineNameLineNumberInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/base/base/deleteLineNameLineNumber");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "删除行名行号-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    //var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "删除行名行号-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "【金蝶】删除行名行号：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, "【金蝶】删除行名行号：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 发票识别查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> RecognitionCheck(RecognitionCheckInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/app/rim/message");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new EolinkerInput<RecognitionCheckInput>()
                {
                    data = input,
                    messageType = "recognitionCheck",
                    messageId = Guid.NewGuid().ToString()
                };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "发票识别查验-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    // var kind = await restClient.PostAsync<KingdeeBusinessResult<QueryPaymentRefundOutput>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "发票识别查验-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<object>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<object>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, "【金蝶】发票识别查验：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 发票查验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> InvoiceCheck(InvoiceCheckInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/app/rim/message");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new EolinkerInput<InvoiceCheckInput>()
                {
                    data = input,
                    messageType = "invoiceCheck",
                    messageId = Guid.NewGuid().ToString()
                };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "发票查验-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    // var kind = await restClient.PostAsync<KingdeeBusinessResult<QueryPaymentRefundOutput>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "发票查验-金蝶返回");
                    if (kind.status)
                    {
                        var ret = BaseResponseData<object>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<object>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, "【金蝶】发票查验：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 查询结算方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SettlementtypeOutput>> GetSettlementtype(SettlementtypeInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/basedata/bd_settlementtype/getSettlementtype?pageNo={input.pageNo}&pageSize={input.pageSize}");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                try
                {
                    var kind = await restClient.GetAsync<KingdeeBusinessResult<SettlementtypeOutput>>(request);
                    if (kind.status)
                    {
                        var ret = BaseResponseData<SettlementtypeOutput>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<SettlementtypeOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<SettlementtypeOutput>.Failed(500, "【金蝶】发票查验：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 收款调整单保存
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> SavePaymentModification(SavePaymentModificationInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/savePaymentModification");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "收款调整单保存-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "收款调整单保存-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "【金蝶】收款调整单保存：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, "【金蝶】收款调整单保存：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 数电票发票单张查询
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<SimDataOutput>> GetDigitalInvoiceFile(SimData input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/app/sim/openApi");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new SimInput()
                {
                    data = Convert.ToBase64String(Encoding.UTF8.GetBytes(input.ToJson())),

                };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "数电票发票单张查询-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    // var kind = await restClient.PostAsync<KingdeeBusinessResult<QueryPaymentRefundOutput>>(request);
                    var kind = await DeserializeKindResponse<KingdeeBusinessResult<string>>(restClient, request, "发票查验-金蝶返回");
                    if (kind.status)
                    {
                        var dataJson = Encoding.UTF8.GetString(Convert.FromBase64String(kind.data));
                        SimDataOutput output = JsonConvert.DeserializeObject<SimDataOutput>(dataJson);
                        var ret = BaseResponseData<SimDataOutput>.Success("操作成功");
                        ret.Data = output;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<SimDataOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<SimDataOutput>.Failed(500, "【金蝶】发票查验：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 红字确认单编号查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<QueryRedConfirmationFormNumberOutput>> QueryRedConfirmationFormList(QueryRedConfirmationFormNumberInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/app/sim/openApi?access_token={access_token}");
                request.AddHeader("accesstoken", access_token);

                #region 参数封装
                Random random = new Random();
                int randomNumber = random.Next(100, 1000);
                SearchRedConfirmationFormNumberInput kdInput = new();
                kdInput.requestId = string.Concat(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), randomNumber);
                kdInput.interfaceCode = "ALLE.REDCONFIRMBILL.QUERY"; ;
                kdInput.businessSystemCode = "HEPT_SYSTEM";
                var kdInputItem = new QueryRedConfirmationFormNumberDto();
                kdInputItem.taxpayerId = input.taxpayerId;
                kdInputItem.orgCode = input.orgCode;
                kdInputItem.redInfoBillNo = ((int)TradeIdentityEnum.Seller).ToString();
                var jsonStr = JsonConvert.SerializeObject(kdInputItem);
                kdInput.data = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(jsonStr)).ToString();
                #endregion

                var jsonInputStr = JsonConvert.SerializeObject(kdInput);
                //await CreateSubLog(SubLogSourceEnum.RedConfirmationFormNumber, jsonInputStr, "none", "【金蝶】红字确认单编号查询", true);
                request.AddParameter("application/json", jsonInputStr, ParameterType.RequestBody);
                try
                {
                    var kind = await restClient.PostAsync<KingdeeBusinessResult<QueryRedConfirmationFormNumberOutput>>(request);
                    if (kind.status)
                    {
                        var ret = BaseResponseData<QueryRedConfirmationFormNumberOutput>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<QueryRedConfirmationFormNumberOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<QueryRedConfirmationFormNumberOutput>.Failed(500, "【金蝶】红字确认单编号查询：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 红字确认单编号下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<RedConfirmationFormNumberListReturnData>> DownloadRedConfirmationFormList(DownloadRedConfirmationFormNumberInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/queryRedConfirm");
                request.AddHeader("accesstoken", access_token);
                Random random = new Random();
                int randomNumber = random.Next(100, 1000);
                var requestId = string.Concat(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), randomNumber);
                request.AddHeader("Idempotency-Key", requestId);

                #region 参数封装
                //var now = DateTime.Now;
                if (input.startDate.HasValue && input.endDate.HasValue)
                {
                    input.startDate = new DateTime(input.startDate.Value.Year, input.startDate.Value.Month, input.startDate.Value.Day, 0, 0, 0);
                    input.endDate = new DateTime(input.endDate.Value.Year, input.endDate.Value.Month, input.endDate.Value.Day, 23, 59, 59, 999);
                }
                var jsonStr = JsonConvert.SerializeObject(input);
                #endregion
                await CreateSubLog(SubLogSourceEnum.RedConfirmationFormNumber, jsonStr, "none", "【金蝶】红字确认单下载", true);
                request.AddParameter("application/json", jsonStr, ParameterType.RequestBody);
                try
                {
                    var kind = await restClient.PostAsync<KingdeeBusinessResult<RedConfirmationFormNumberListReturnData>>(request);
                    if (kind.status)
                    {
                        var ret = BaseResponseData<RedConfirmationFormNumberListReturnData>.Success("操作成功");
                        ret.Data = kind.data;
                        return ret;
                    }
                    else
                    {
                        return BaseResponseData<RedConfirmationFormNumberListReturnData>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<RedConfirmationFormNumberListReturnData>.Failed(500, "【金蝶】红字确认单下载：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 红字确认单生成
        /// </summary>
        /// <typeparam name="BaseResponseData"></typeparam>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData> Generate(GenerateRedConfirmationFormNumberInput input)
        {
            var access_token = await GetAccesstokenAsync();
            var cachekey = "generate" + input.invoiceNo;
            using (var restClient = new RestClient(_httpClient))
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt != null && !string.IsNullOrEmpty(hasOpt.Value))
                {
                    return BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Failed(500, "存在并发操作");
                }
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/saveRedConfirm");
                request.AddHeader("accesstoken", access_token);
                Random random = new Random();
                //流水号使用蓝票号
                input.orderNo = string.Concat(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), random.Next(1000000, 9999999)); ;
                #region 参数封装
                int randomNumber = random.Next(100, 1000);
                SearchRedConfirmationFormNumberInput kdInput = new();
                kdInput.requestId = string.Concat(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), randomNumber);
                kdInput.interfaceCode = "REDCONFIRMBILL.APPLY";
                kdInput.businessSystemCode = "HEPT_SYSTEM";
                var jsonStr = JsonConvert.SerializeObject(input);
                kdInput.data = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(jsonStr)).ToString();
                #endregion

                var jsonInputStr = JsonConvert.SerializeObject(kdInput);
                //写入缓存避免重复操作
                await _easyCaching.SetAsync<string>(cachekey, jsonInputStr, TimeSpan.FromSeconds(5));

                await CreateSubLog(SubLogSourceEnum.RedConfirmationFormNumber, jsonInputStr, "none", "【金蝶】红字确认单生成", true);
                request.AddParameter("application/json", jsonInputStr, ParameterType.RequestBody);
                try
                {
                    //GenerateRedConfirmationFormNumberOutput
                    var kind = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    if (kind.status)
                    {
                        _easyCaching.Remove(cachekey);
                        var ret = BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Success("操作成功");
                        return ret;
                    }
                    else
                    {
                        _easyCaching.Remove(cachekey);
                        return BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Failed(500, string.Concat("【金蝶】", kind.message));
                    }
                }
                catch (Exception ex)
                {
                    _easyCaching.Remove(cachekey);
                    return BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Failed(500, "【金蝶】红字确认单生成：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 财务应收单换票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData> ChangeInvoiceCreditRelationship(ChangeRelationshipKingdeeInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/arFinbillChangeInvoice?accesstoken={access_token}");
                request.AddHeader("accesstoken", access_token);

                #region 参数封装
                var p = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(p);

                #endregion
                await CreateSubLog(SubLogSourceEnum.ChangeInvoiceCreditRelationship, jsonStr, "none", "【金蝶】财务应收单换票", false);
                request.AddParameter("application/json", jsonStr, ParameterType.RequestBody);
                try
                {
                    var kind = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    if (kind.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Failed(500, kind.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<GenerateRedConfirmationFormNumberDataOutput>.Failed(500, "【金蝶】财务应收单换票：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 保存返利计提
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> RebateProvisionSave(List<RebateProvisionSaveInput> input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/rebateProvisionSave");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存返利计提-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存返利计提-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "【金蝶】保存返利计提：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<string>.Failed(500, "【金蝶】保存返利计提：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 撤销认款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceiveInvoice(string code)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/cancellationSubscription");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = new List<string>() { code } };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.CancelReceiveInvoice, jsonStr, "none", "撤销认款推送-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "撤销认款推送-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "【金蝶】撤销认款推送：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, "【金蝶】撤销认款推送：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 撤销认款（包含明细）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> CancelReceive(List<QuashAcceptancesRequestVo> input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/cancellationSubscription");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.CancelReceiveInvoice, jsonStr, "none", "撤销认款推送-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "撤销认款推送-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, "【金蝶】撤销认款推送：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, "【金蝶】撤销认款推送：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 核心平台保存采购转固单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> BatchSavePurchaseCoreBill(List<PurchaseCoreBillInput> input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/fa/fa/batchSavePurchaseCoreBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "核心平台保存采购转固单-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "核心平台保存采购转固单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "【金蝶】核心平台保存采购转固单：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<string>.Failed(500, "【金蝶】核心平台保存采购转固单：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 核心平台删除采购转固单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> BatchDeletePurchaseCoreBill(DeletePurchaseCoreBillInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/fa/fa/batchDeletePurchaseCoreBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = input;
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "核心平台删除采购转固单-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "核心平台删除采购转固单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "【金蝶】核心平台删除采购转固单：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<string>.Failed(500, "【金蝶】核心平台删除采购转固单：" + ex.Message);
                }
            }
        }

        public async Task<BaseResponseData<List<CheckDataWithKingdeeOutputDto>>> QueryKingdeeTempStoreOutData(CheckDataWithKingdeeInputDtoForKingdee input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/reconciliationQuery");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    // var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<List<CheckDataWithKingdeeOutputDto>>>(restClient, request, "查询金蝶暂存出库数据");
                    if (ret.status)
                    {
                        return new BaseResponseData<List<CheckDataWithKingdeeOutputDto>>() { Code = CodeStatusEnum.Success, Data = ret.data, Message = "获取成功" };
                    }
                    else
                    {

                        return new BaseResponseData<List<CheckDataWithKingdeeOutputDto>>() { Code = CodeStatusEnum.Failed, Message = ret.message };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶暂存出库数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 发票查询子表初始化
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> InvoiceChildInit(List<InvoiceChildInitRequestVo> list)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/invoiceChildInit");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var input = new PushInitInvoicesInput();
                input.requestVoList = list;
                var para = new { data = input };
                var jsonStr = JsonConvert.SerializeObject(para);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "核心平台发票查询子表初始化-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "核心平台发票查询子表初始化-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "【金蝶】核心平台发票查询子表初始化：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<string>.Failed(500, "【金蝶】核心平台发票查询子表初始化：" + ex.Message);
                }
            }
        }

        /// <summary>
        /// 打包发送发票至邮箱
        /// </summary>
        /// <returns></returns>
        public async Task<BaseResponseData<string>> SendEmailInvoices(SendMailInvoicesInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/jfzx_sim_ext/api/downloadFiles");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());


                var jsonStr = JsonConvert.SerializeObject(input);
                //await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "核心平台打包发送发票至邮箱-通知金蝶", true);
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "核心平台打包发送发票至邮箱-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<string>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<string>.Failed(500, "【金蝶】核心平台打包发送发票至邮箱：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<string>.Failed(500, "【金蝶】核心平台打包发送发票至邮箱：" + ex.Message);
                }
            }
        }

        /// <summary>
        ///  新增报损/换货处理单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> BatchSaveBarterDisposeBill(List<BatchSaveBarterDisposeBillInput> input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchSaveBarterDisposeBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "新增报损/换货处理单");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"新增报损/换货处理单[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"新增报损/换货处理单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "新增报损/换货处理单", false);
                }
            }
        }

        /// <summary>
        ///  保存收入确认单
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RevcfmbillBatchSave(List<RevcfmbillBatchSaveInput> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar_revcfmbill/batchSave");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "收入确认单批量保存");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"收入确认单批量保存[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"收入确认单批量保存[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "收入确认单批量保存", false);
                }
            }
        }


        /// <summary>
        /// 关联发票号和应收单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> AssociatedInvoicing(AssociatedInvoicingInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/associatedInvoicing");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(input);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "关联发票号和应收单");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"关联发票号和应收单[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"关联发票号和应收单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "关联发票号和应收单", false);
                }
            }
        }

        /// <summary>
        /// 收入确认分批次确认
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> RevcfmbillBatchisConfirm(List<RevcfmbillBatchiscofirmInput> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar_revcfmbill/batchiscofirm");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "收入确认分批次确认");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"收入确认分批次确认[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"收入确认分批次确认[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "收入确认分批次确认", false);
                }
            }
        }


        /// <summary>
        /// 取消指定(进项票)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> InputBillUnassign(InputBillUnassignInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap_invoice/unassign");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, " 取消指定(进项票)");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $" 取消指定(进项票)[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($" 取消指定(进项票)[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", " 取消指定(进项票)", false);
                }
            }
        }

        #region 对账

        public async Task<KindeeCheckBillResData> GetKingdeeCheckData(string billCode, string org_number)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/v2/jfzx/ap/ap_finapbill/finapbillcheck001");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var input = new CheckDataForKingdeeInput() { data = new CheckDataForKingdeeDataInput { billno = billCode, org_number = org_number } };
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var s = await restClient.PostAsync<string>(request);
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<KindeeCheckBillResData>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        public async Task<KindeeCheckBillResData> GetKingdeeTempStoreOutCheckData(string billCode, string org_number)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/v2/jfzx/ar/jfzx_holdstockremoval/holdstockremovalcheck001");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var input = new CheckDataForKingdeeInput() { data = new CheckDataForKingdeeDataInput { billno = billCode, org_number = org_number } };
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var s = await restClient.PostAsync<string>(request);
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<KindeeCheckBillResData>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        public async Task<KindeeCheckBillResData> GetKingdeeCreditCheckData(string billCode, string org_number)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/v2/jfzx/ar/ar_finarbill/finarbillcheck001");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var input = new CheckDataForKingdeeInput() { data = new CheckDataForKingdeeDataInput { billno = billCode, org_number = org_number } };
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var s = await restClient.PostAsync<string>(request);
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<KindeeCheckBillResData>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        public async Task<KindeeCheckBillResData> GetKingdeeTempStoreInCheckData(string billCode, string org_number)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/v2/jfzx/ar/jfzx_holdstorage/holdstoragecheck001");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var input = new CheckDataForKingdeeInput() { data = new CheckDataForKingdeeDataInput { billno = billCode, org_number = org_number } };
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var s = await restClient.PostAsync<string>(request);
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<KindeeCheckBillResData>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        #endregion

        /// <summary>
        /// 税收分类查询接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<TaxClassCodeOutput>> TaxClassCode(TaxClassCodeInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/base/base/taxClassCode");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<TaxClassCodeOutput>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<TaxClassCodeOutput>>(restClient, request, " 税收分类查询接口");
                    if (ret.status)
                    {
                        return new BaseResponseData<TaxClassCodeOutput>() { Code = CodeStatusEnum.Success, Data = ret.data, Message = "获取成功", Total = ret.data.total };
                    }
                    else
                    {

                        return new BaseResponseData<TaxClassCodeOutput>() { Code = CodeStatusEnum.Failed, Message = ret.message };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金蝶]税收分类查询接口错误，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    //await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", " 税收分类查询接口", true);
                }
            }
        }

        /// <summary>
        /// 银行信息查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<QueryBeBankOutput>> QueryBeBank(TaxClassCodeInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/base/base/queryBeBank");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<QueryBeBankOutput>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<QueryBeBankOutput>>(restClient, request, " 银行信息查询");
                    if (ret.status)
                    {
                        return new BaseResponseData<QueryBeBankOutput>() { Code = CodeStatusEnum.Success, Data = ret.data, Message = "获取成功", Total = ret.data.total };
                    }
                    else
                    {

                        return new BaseResponseData<QueryBeBankOutput>() { Code = CodeStatusEnum.Failed, Message = ret.message };
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金蝶]银行信息查询错误，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    var responseBody = JsonConvert.SerializeObject(ret);
                    //await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", " 银行信息查询", true);
                }
            }
        }
        /// <summary>
        /// 应付退款结算接口
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> DebtRefundSettle(List<DebtRefundSettleInput> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/apRecSettle");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "应付退款结算-推送金蝶", false);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "应付退款结算-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"【金蝶】应付退款结算，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<int>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 库存更换项目-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBill(List<ApBarterDisposeBillModel> inputs)
        {
            var ketRet = new BaseResponseData<object>();
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchSaveBarterDisposeBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "库存更换项目-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "库存更换项目-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<object>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<object>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<object>.Failed(500, $"库存更换项目失败[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 库存更换部门-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByDept(List<ApBarterDisposeBillModel> inputs)
        {
            var ketRet = new BaseResponseData<object>();
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchSaveBarterDisposeBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "库存更换部门-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "库存更换部门-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<object>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<object>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<object>.Failed(500, $"库存更换部门失败[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 暂存-财务端
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<object>> BatchSaveBarterDisposeBillByStaging(List<ApBarterDisposeBillModel> inputs)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchSaveBarterDisposeBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = inputs };
                var jsonStr = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "暂存调整-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "暂存调整-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<object>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<object>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<object>.Failed(500, $"暂存调整失败[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<object>.Failed(500, ex.Message);
                }
            }
        }

        /// <summary>
        /// 获取金蝶退款数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<RefundResponseModelOutput> QueryKingdeePaymentRefund(QueryRefundDataInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/api/queryPaymentRefund");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<RefundResponseModelOutput>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"获取金蝶数据出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }
        /// <summary>
        /// 收款冲付款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<object> ReceiveAbtPayment(ReceiveAbtPaymentInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/cas/recPaySettle");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    if (ret.status)
                    {
                        return ret.data;
                    }
                    throw new Exception(ret.message);
                }
                catch (Exception ex)
                {
                    throw new Exception($"提交金蝶收款冲游离付款单出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 应付冲销撤销
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> ReverseSettlement(ReverseSettlementInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/jfzx_ap_ext/api/reverseSettlement");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                request.AddParameter("application/json", input, ParameterType.RequestBody);
                try
                {
                    var ret = await restClient.PostAsync<KingdeeBusinessResult<object>>(request);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"【金蝶】应付冲销撤销失败，原因：{ret.message}");
                    }

                }
                catch (Exception ex)
                {
                    throw new Exception($"金蝶应付撤销出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }
        /// <summary>
        /// 更换核算部门应收推送金蝶
        /// </summary>
        /// <param name="kingdeeCredits"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> DeptPushCreditsToKingdee(List<KingdeeCredit> kingdeeCredits)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ar/ar/batchAdjustArFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = kingdeeCredits };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "更换核算部门应收推送金蝶");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"推送更换核算部门应收推送金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 保存财务应付单关联单
        /// </summary>
        /// <param name="kingdeeAdjusts"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> PushDebtsAdjustToKingdee(List<KingdeeAdjustDebt> kingdeeAdjusts)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/ap/ap/batchAdjustApFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = kingdeeAdjusts };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var ret = new KingdeeBusinessResult<object>();
                var requestBody = JsonConvert.SerializeObject(para);
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存财务应付关联单-金蝶返回");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")//金蝶返回已存在
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"保存财务应付单[金蝶]：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"保存财务应付关联单[金蝶]，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "保存财务应付关联单-推送金蝶", false);
                }
            }
        }
        /// <summary>
        /// 保存损失确认负数应付单
        /// </summary>
        /// <param name="saveLossRecognitionDebtInput"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> SaveLossRecognitionDebt(List<SaveLossRecognitionDebtInput> saveLossRecognitionDebtInput)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/ap/batchAcknowledgmentApFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = saveLossRecognitionDebtInput };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存损失确认负数应付单推送金蝶");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"推送保存损失确认负数应付单推送金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 保存损失确认负数应收单
        /// </summary>
        /// <param name="saveLossRecognitionCreditInput"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> SaveLossRecognitionCredit(List<SaveLossRecognitionCreditInput> saveLossRecognitionCreditInput)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/ar/batchAcknowledgmentArFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = saveLossRecognitionCreditInput };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存损失确认负数应收单推送金蝶");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"推送保存损失确认负数应收单推送金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }

        /// <summary>
        /// 回滚损失确认负数应收单
        /// </summary>
        /// <param name="rollBackLossRecognitionCreditInput"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> RollBackLossRecognitionCredit(List<RollBackLossRecognitionCreditInput> rollBackLossRecognitionCreditInput)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/ar/arRollbackFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = rollBackLossRecognitionCreditInput };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "回滚损失确认负数应收单推送金蝶");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"回滚保存损失确认负数应收单推送金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "回滚保存损失确认负数应收单-推送金蝶", false);
                }
            }
        }

        /// <summary>
        /// 回滚损失确认负数应付单
        /// </summary>
        /// <param name="rollBackLossRecognitionCreditInput"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<int>> RollBackLossRecognitionDebt(List<RollBackLossRecognitionDebtInput> rollBackLossRecognitionDebtInput)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/ap/apRollbackFinBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = rollBackLossRecognitionDebtInput };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<object>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "回滚损失确认负数应付单推送金蝶");
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<int>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<int>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"回滚保存损失确认负数应付单推送金蝶出错，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "回滚保存损失确认负数应付单-推送金蝶", false);
                }
            }
        }

        /// <summary>
        /// 查询在途结算数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<List<NoPostBackSettleResponseVo>>> QueryNoPostbackSettleBill(NoPostBackSettleRequestVo input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/cas/cas/queryNoPostbackSettleBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input };
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<List<NoPostBackSettleResponseVo>>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<List<NoPostBackSettleResponseVo>>>(restClient, request, "查询在途结算数据");
                    if (ret.status)
                    {
                        return new BaseResponseData<List<NoPostBackSettleResponseVo>>() { Code = CodeStatusEnum.Success, Data = ret.data, Message = "获取成功" };
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<List<NoPostBackSettleResponseVo>>.Failed(800, $"操作失败：{ret.message}");
                        }
                        return BaseResponseData<List<NoPostBackSettleResponseVo>>.Failed(500, $"操作失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    string errMsg = $"查询在途结算数据，原因：{ex.Message + ex.InnerException?.Message}";
                    throw new Exception(errMsg);
                }
                finally
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "查询在途结算数据", false);
                }
            }
        }

        /// <summary>
        /// 查询合同台账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<BaseResponseData<QueryContractBillByKingdeeOutput>> QueryContractBill(QueryContractBillByKingdeeInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/em/er_contractbill/queryContractBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = input;
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                var requestBody = JsonConvert.SerializeObject(para);
                var ret = new KingdeeBusinessResult<QueryContractBillByKingdeeOutput>();
                try
                {
                    ret = await DeserializeKindResponse<KingdeeBusinessResult<QueryContractBillByKingdeeOutput>>(restClient, request, "查询合同台账");
                    if (ret.status)
                    {
                        return new BaseResponseData<QueryContractBillByKingdeeOutput>() { Code = CodeStatusEnum.Success, Data = ret.data, Message = "获取成功" };
                    }
                    else
                    {
                        if (ret.errorCode == "800")
                        {
                            return BaseResponseData<QueryContractBillByKingdeeOutput>.Failed(800, $"查询失败：{ret.message}");
                        }
                        return BaseResponseData<QueryContractBillByKingdeeOutput>.Failed(500, $"查询失败：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"查询合同台账，原因：{ex.Message + ex.InnerException?.Message}");
                }
                finally
                {
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, requestBody, "none", "查询合同台账", false);
                }
            }
        }

        /// <summary>
        /// 保存中标服务费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SaveOrUpdateRefundOutput>> SavePrepayBill(PrepayBillInput input)
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/em/em/savePrepayBill");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = new List<PrepayBillInput>() { input } };
                var jsonStr = JsonConvert.SerializeObject(para);
                await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存中标服务费-推送金蝶", true);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<SaveOrUpdateRefundOutput>>(restClient, request, "保存中标服务费-金蝶返回");
                    if (ret.status)
                    {
                        var res = BaseResponseData<SaveOrUpdateRefundOutput>.Success("操作成功");
                        res.Data = ret.data;
                        return res;
                    }
                    else
                    {
                        return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "【金蝶】保存中标服务费：" + ret.message);
                    }
                }
                catch (Exception ex)
                {
                    return BaseResponseData<SaveOrUpdateRefundOutput>.Failed(500, "【金蝶】保存中标服务费：" + ex.Message);
                }
            }
        }
        /// <summary>
        /// 保存负数开票申请
        /// </summary>
        /// <param name="input"></param>
        /// <param name="flow">N=保存后悔删除，Y=保存后不会删除</param>
        /// <returns></returns>
        public async Task<BaseResponseData<int>> saveRedBillingApplication(List<saveRedBillingApplicationInput> input, string flow = "Y")
        {
            var access_token = await GetAccesstokenAsync();
            using (var restClient = new RestClient(_httpClient))
            {
                var request = new RestRequest($"{_kingdeeSetting.Value.Host}/kapi/v2/jfzx/sim/api/saveRedBillingApplication");
                request.AddHeader("Content-Type", "application/json");
                request.AddHeader("accesstoken", access_token);
                request.AddHeader("Idempotency-Key", Guid.NewGuid());
                var para = new { data = input, flow = flow };
                var jsonStr = JsonConvert.SerializeObject(para);
                request.AddParameter("application/json", para, ParameterType.RequestBody);
                try
                {
                    _subLogService.LogAzure("saveRedBillingApplication", jsonStr, "保存负数开票申请-推送金蝶");
                    var ret = await DeserializeKindResponse<KingdeeBusinessResult<object>>(restClient, request, "保存负数开票申请-金蝶返回");
                    await CreateSubLog(SubLogSourceEnum.PushKingdee, jsonStr, "none", "保存负数开票申请-推送金蝶", false);
                    if (ret.status)
                    {
                        return BaseResponseData<int>.Success("操作成功");
                    }
                    else
                    {
                        return BaseResponseData<int>.Failed(500, $"[金碟]保存负数开票申请，原因：{ret.message}");
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"[金碟]保存负数开票申请，原因：{ex.Message + ex.InnerException?.Message}");
                }
            }
        }
    }
}
