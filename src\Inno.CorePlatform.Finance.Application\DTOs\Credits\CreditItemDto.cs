﻿using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;

namespace Inno.CorePlatform.Finance.Application.DTOs.Credits
{
    public class CreditDto : Credit
    {

    }

    public class SaleDetail
    {
        public Guid? BatchId { get; set; }
        public Guid? OriginalId { get; set; }
        public MarkEnum? Mark { get; set; }
        public Guid? ServiceId { get; set; }
        public Guid? ProjectId { get; set; }
        public decimal? Amount { get; set; }

        // 新增一个属性用于存储原始数据引用
        public List<SaleDetailOutput> OriginalData { get; set; } = new List<SaleDetailOutput>();
        // 是否大于0，用于服务费应收
        public bool? IsThenZero { get; set; }
        public ReviseRangeEnum? ReviseRange { get; set; }
    }

    public class CreditBillDateDTO
    {
        public Guid CompanyId { get; set; }
        public DateTime BillDate { get; set; }
    }


}
