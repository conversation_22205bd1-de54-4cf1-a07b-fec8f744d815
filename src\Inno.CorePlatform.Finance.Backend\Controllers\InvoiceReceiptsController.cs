﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Gateway.Client;
using Inno.CorePlatform.Gateway.Models.File;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 发票入账单
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceReceiptsController : BaseController
    {
        private readonly IInvoiceReceiptQueryService _invoiceReceiptItemQueryService;
        private readonly IFileGatewayClient _fileGatewayClient;

        /// <summary>
        /// 构造函数
        /// </summary>
        public InvoiceReceiptsController(IInvoiceReceiptQueryService invoiceReceiptItemQueryService, IFileGatewayClient fileGatewayClient, ISubLogService subLog) : base(subLog)
        {
            _invoiceReceiptItemQueryService = invoiceReceiptItemQueryService;
            _fileGatewayClient = fileGatewayClient;
        }

        /// <summary>
        /// 获取发票记账单列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<InvoiceReceiptItemQueryOutput>> GetList([FromBody] InvoiceReceiptItemQueryInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id;
                input.UserName = CurrentUser.UserName;
                var (list, count) = await _invoiceReceiptItemQueryService.GetListAsync(input);
                return new ResponseData<InvoiceReceiptItemQueryOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceReceiptItemQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取发票记账单明细
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetDetailsByItemId")]
        public async Task<ResponseData<InvoiceReceiptDetailQueryOutput>> GetDetailsByItemId([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceReceiptItemQueryService.GetDetailsByItemIdAsync(input);
                return new ResponseData<InvoiceReceiptDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceReceiptDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 获取发票记账单明细
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetDetailsSumByItemId")]
        public async Task<BaseResponseData<InvoiceReceiptDetailSumOutput>> GetDetailsSumByItemId([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var uid = CurrentUser.Id;
                var ret = BaseResponseData<InvoiceReceiptDetailSumOutput>.Success("操作成功");
                input.page = 1;
                input.limit = int.MaxValue;
                var (list, count) = await _invoiceReceiptItemQueryService.GetDetailsByItemIdAsync(input);
                var sum = new InvoiceReceiptDetailSumOutput()
                {
                   InvoiceAmountSum = list.Sum(p => p.InvoiceAmount),
                    CreditAmountSum = list.Sum(p => p.CreditAmount),
                };
                ret.Data = sum;
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 删除发票明细
        /// </summary>
        /// <returns></returns>
        [HttpPost("DeleteDetails")]
        public async Task<BaseResponseData<string>> DeleteDetails([FromBody] List<Guid?> ids)
        {
            try
            {
                var ret = await _invoiceReceiptItemQueryService.DeleteDetails(ids,CurrentUser.UserName);
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 创建
        /// </summary>
        /// <returns></returns>
        [HttpPost("Create")]
        public async Task<BaseResponseData<string>> Create([FromBody] InvoiceReceiptCreateInput input)
        {
            try
            {
                input.CreatedBy = CurrentUser.UserName;
                var ret = await _invoiceReceiptItemQueryService.Create(input);
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取编辑数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetItem")]
        public async Task<BaseResponseData<InvoiceReceiptItemEditOutput>> GetItem([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var ret = await _invoiceReceiptItemQueryService.GetItem(input);
                if (ret.Data != null && !string.IsNullOrEmpty(ret.Data.AttachFileIds))
                {
                    var model = ret.Data;
                    var fileIds = model.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    var bizFiles = new List<BizFileUploadOutput>();
                    foreach (var fileId in fileIds)
                    {
                        if (!string.IsNullOrWhiteSpace(fileId))
                        {
                            var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                            if (file != null)
                            {
                                bizFiles.Add(file);
                            }
                        }
                    }
                    ret.Data.Attachments = bizFiles;
                }
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 提交
        /// </summary>
        /// <returns></returns>
        [HttpPost("Submit")]
        public async Task<BaseResponseData<string>> Submit([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                input.UserId = CurrentUser.UserName;
                var ret = await _invoiceReceiptItemQueryService.Submit(input);
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 取消/撤回
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        [HttpPost("Withdraw")]
        public async Task<(int, string)> Cancel(InvoiceReceiptCancelInput input) 
        {
            try
            {
                input.CreatedBy = CurrentUser.UserName;
                var ret = await _invoiceReceiptItemQueryService.Cancel(input);
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 获取附件
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetAttachFile")]
        public async Task<BaseResponseData<List<BizFileUploadOutput>>> GetAttachFile([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var ret = BaseResponseData<List<BizFileUploadOutput>>.Success("操作成功！");
                var data = await _invoiceReceiptItemQueryService.GetItem(input);
                if (data.Data != null && !string.IsNullOrEmpty(data.Data.AttachFileIds))
                {
                    var model = data.Data;
                    var fileIds = model.AttachFileIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    var bizFiles = new List<BizFileUploadOutput>();
                    foreach (var fileId in fileIds)
                    {
                        if (!string.IsNullOrWhiteSpace(fileId))
                        {
                            var file = await _fileGatewayClient.GetFileMetadataAsync(Guid.Parse(fileId));
                            if (file != null)
                            {
                                bizFiles.Add(file);
                            }
                        }
                    }
                    ret.Data = bizFiles;
                }
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 删除附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DeleteAttachFileIds")]
        public async Task<BaseResponseData<string>> DeleteAttachFileIds(InvoiceReceiptAttachFileInput input)
        {
            return await _invoiceReceiptItemQueryService.DeleteAttachFileIds(input);
        }

        /// <summary>
        /// 删除发票入账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Remove")]
        public async Task<BaseResponseData<string>> Remove(InvoiceReceiptDetailQueryInput input)
        {
            input.UserId = CurrentUser.UserName;
            return await _invoiceReceiptItemQueryService.Remove(input);
        }

        /// <summary>
        ///  获取发票入账列表下载
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("DownLoad")]
        public async Task<IActionResult> DownLoadAsync([FromBody] InvoiceReceiptItemQueryInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id;
                input.UserName = CurrentUser.UserName;
                var ret = await _invoiceReceiptItemQueryService.DownLoad(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("发票入账单");
                    #region 表头
                    worksheet.Cells[1, 1].Value = "单据";
                    worksheet.Cells[1, 2].Value = "单据日期";
                    worksheet.Cells[1, 3].Value = "公司";
                    worksheet.Cells[1, 4].Value = "业务单元";
                    worksheet.Cells[1, 5].Value = "客户";
                    worksheet.Cells[1, 6].Value = "合约回款天数";
                    worksheet.Cells[1, 7].Value = "销售账期天数";
                    worksheet.Cells[1, 8].Value = "实际回款天数";
                    worksheet.Cells[1, 9].Value = "状态";
                    worksheet.Cells[1, 10].Value = "创建人";
                    worksheet.Cells[1, 11].Value = "备注";
                    #endregion

                    #region 数据
                    int row = 2;
                    foreach (var item in ret)
                    {
                        worksheet.Cells[row, 1].Value = item.BillCode;
                        worksheet.Cells[row, 2].Value = item.BillDate.Value.DateTime.ToString("yyyy-MM-dd");
                        worksheet.Cells[row, 3].Value = item.CompanyName;
                        worksheet.Cells[row, 4].Value = item.ServiceName;
                        worksheet.Cells[row, 5].Value = item.CustomerName;
                        worksheet.Cells[row, 6].Value = item.BackAmountDays;
                        worksheet.Cells[row, 7].Value = item.SaleAccountPeriodDays;
                        worksheet.Cells[row, 8].Value = item.ActualBackAmountDays;
                        worksheet.Cells[row, 9].Value = item.StatusStr;
                        worksheet.Cells[row, 10].Value = item.CreatedBy;
                        worksheet.Cells[row, 11].Value = item.Remark;
                        row++;
                    }
                    #endregion

                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("ExportByCoordinate")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportByCoordinate([FromBody] InvoiceReceiptItemQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.UserName = CurrentUser.UserName;//
            return await _invoiceReceiptItemQueryService.ExportByCoordinate(query);
        }

        /// <summary>
        /// 获取清单页签数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<InvoiceReceiptListTabOutput>> GetTabCount([FromBody] InvoiceReceiptItemQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                return await _invoiceReceiptItemQueryService.GetTabCount(query);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据公司id查询业务单元
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetServices")]
        public async Task<BaseResponseData<List<InvoiceReceiptsSelectAssemblyOutput>>> GetServices([FromBody] InvoiceReceiptsSelectAssemblyInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                var ret = BaseResponseData<List<InvoiceReceiptsSelectAssemblyOutput>>.Success("操作成功！");
                var data = await _invoiceReceiptItemQueryService.GetServices(input);
                ret.Data = data;
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据公司、业务单元获取客户
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetCustomers")]
        public async Task<BaseResponseData<List<InvoiceReceiptsSelectAssemblyOutput>>> GetCustomers([FromBody] InvoiceReceiptsSelectAssemblyInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                input.UserName = CurrentUser.UserName;
                var ret = BaseResponseData<List<InvoiceReceiptsSelectAssemblyOutput>>.Success("操作成功！");
                var data = await _invoiceReceiptItemQueryService.GetCustomers(input);
                ret.Data = data;
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据公司、业务单元获取客户
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetDayData")]
        public async Task<BaseResponseData<InvoiceReceiptsDayOutput>> GetDayData([FromBody] InvoiceReceiptsSelectAssemblyInput input)
        {
            try
            {
                input.UserId = CurrentUser.Id.Value;
                input.UserName = CurrentUser.UserName;
                var ret = BaseResponseData<InvoiceReceiptsDayOutput>.Success("操作成功！");
                var data = await _invoiceReceiptItemQueryService.GetDayData(input);
                ret.Data = data??=new InvoiceReceiptsDayOutput
                {
                    BackAmountDays = 0,
                    SaleAccountPeriodDays = 0,
                };
                return ret;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        
    }
}
