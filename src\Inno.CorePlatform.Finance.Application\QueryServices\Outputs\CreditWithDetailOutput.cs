﻿using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 应收查询，出参
    /// </summary>
    public class CreditWithDetailOutput
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        /// <summary>
        /// 应收值
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }
        /// <summary>
        /// 余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value) - AbatmentAmount; } }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum? InvoiceType { get; set; }
        public SaleSourceEnum? SaleSource { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum? SaleType { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        
        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 应收明细
        /// </summary>
        public List<CreditDetailPo>? Details { get; set; }
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get;  set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? HospitalName { get;  set; }
    }
}
