﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Purchase.Adapter.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    /// <summary>
    /// Dapr API客户端基类，提供统一的Dapr服务调用功能
    /// </summary>
    /// <typeparam name="T">具体实现类类型，用于日志记录</typeparam>
    public abstract class BaseDaprApiClient<T> : BaseApiClient<T>
    {
        #region 字段和构造函数

        protected readonly DaprClient _daprClient;
        protected readonly IHttpContextAccessor _httpContextAccessor;

        protected BaseDaprApiClient(DaprClient daprClient, ILogger<T> logger, IHttpContextAccessor httpContextAccessor) : base(logger)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _daprClient = daprClient ?? throw new ArgumentNullException(nameof(daprClient));
        }

        #endregion

        #region 抽象方法

        /// <summary>
        /// 子类实现AppId的赋值
        /// </summary>
        /// <returns>目标服务的AppId</returns>
        protected abstract string GetAppId();

        #endregion

        #region 公共调用方法

        /// <summary>
        /// 调用外部能力中心，针对无参数的接口
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="methodName">方法名称</param>
        /// <param name="httpMethod">HTTP方法类型，默认为POST</param>
        /// <returns>响应结果</returns>
        protected async Task<TResponse> InvokeMethodAsync<TResponse>(string methodName, RequestMethodEnum httpMethod = RequestMethodEnum.POST)
        {
            var appId = GetAppId();
            var requestUrl = $"{appId}/{methodName}";

            _logger.LogInformation("能力中心请求开始:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【Parameters】无参数",
                appId, methodName, requestUrl, httpMethod);

            try
            {
                var request = _daprClient.CreateInvokeMethodRequest(appId, methodName);
                return await SendRequestAsync<TResponse>(request, httpMethod);
            }
            catch (Exception ex)
            {
                return HandleInvokeException<TResponse>(ex, appId, methodName, requestUrl);
            }
        }

        /// <summary>
        /// 调用外部能力中心，针对有参数对象的接口
        /// </summary>
        /// <typeparam name="TRequest">请求参数类型</typeparam>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="inputParam">请求参数</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="httpMethod">HTTP方法类型，默认为POST</param>
        /// <returns>响应结果</returns>
        protected async Task<TResponse> InvokeMethodWithQueryObjectAsync<TRequest, TResponse>(TRequest inputParam, string methodName, RequestMethodEnum httpMethod = RequestMethodEnum.POST)
        {
            var appId = GetAppId();
            var requestUrl = $"{appId}/{methodName}";
            var paramJson = inputParam?.ToJson() ?? "null";

            _logger.LogInformation("能力中心请求开始:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【Parameters】{Parameters}",
                appId, methodName, requestUrl, httpMethod, paramJson);

            try
            {
                var request = _daprClient.CreateInvokeMethodRequest(appId, methodName, inputParam);
                return await SendRequestAsync<TResponse>(request, httpMethod);
            }
            catch (Exception ex)
            {
                return HandleInvokeException<TResponse>(ex, appId, methodName, requestUrl, paramJson);
            }
        }

        /// <summary>
        /// 调用外部能力中心，针对有参数对象的接口（支持指定用户信息）
        /// </summary>
        /// <typeparam name="TRequest">请求参数类型</typeparam>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="inputParam">请求参数</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="httpMethod">HTTP方法类型，默认为POST</param>
        /// <returns>响应结果</returns>
        protected async Task<TResponse> InvokeMethodWithQueryObjectAndUserAsync<TRequest, TResponse>(TRequest inputParam, string methodName, Guid? userId, string? userName, RequestMethodEnum httpMethod = RequestMethodEnum.POST)
        {
            var appId = GetAppId();
            var requestUrl = $"{appId}/{methodName}";
            var paramJson = inputParam?.ToJson() ?? "null";

            _logger.LogInformation("能力中心请求开始(指定用户):【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【UserId】{UserId}【UserName】{UserName}【Parameters】{Parameters}",
                appId, methodName, requestUrl, httpMethod, userId, userName, paramJson);

            try
            {
                var request = _daprClient.CreateInvokeMethodRequest(appId, methodName, inputParam);
                return await SendRequestWithUserAsync<TResponse>(request, httpMethod, userId, userName);
            }
            catch (Exception ex)
            {
                return HandleInvokeException<TResponse>(ex, appId, methodName, requestUrl, paramJson);
            }
        }

        #endregion

        #region 响应处理方法

        /// <summary>
        /// 对于外部能力中心返回数据进行统一解析，子类遇到特殊情况可以重写
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="request">HTTP请求消息</param>
        /// <returns>解析后的响应数据</returns>
        protected virtual async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var appId = GetAppId();
            var methodName = request.RequestUri?.PathAndQuery ?? "未知接口";
            var requestUrl = $"{appId}/{methodName}";

            var res = await _daprClient.InvokeMethodAsync<BaseResponseData<TResponse>>(request);

            // 记录详细的响应日志
            _logger.LogDebug("能力中心响应详情:【AppID】{AppId}【RequestUrl】{RequestUrl}【ResponseCode】{ResponseCode}【ResponseMessage】{ResponseMessage}【ResponseData】{ResponseData}",
                appId, requestUrl, res.Code, res.Message, res.Data?.ToJson() ?? "null");

            if (res.Code != CodeStatusEnum.Success)
            {
                // 详细的错误日志
                _logger.LogError("能力中心响应错误:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【ResponseCode】{ResponseCode}【ErrorMessage】{ErrorMessage}【FullResponse】{FullResponse}",
                    appId, methodName, requestUrl, res.Code, res.Message, res.ToJson());

                // 简洁的用户提示信息
                throw new ApplicationException($"能力中心【{appId}】调用失败：{res.Message ?? "未知错误"}");
            }

            return res.Data ?? default!;
        }

        /// <summary>
        /// 分页响应处理方法
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="request">HTTP请求消息</param>
        /// <returns>分页响应数据</returns>
        protected virtual async Task<BasePagedData<TResponse>> PageResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var appId = GetAppId();
            var methodName = request.RequestUri?.PathAndQuery ?? "未知接口";
            var requestUrl = $"{appId}/{methodName}";

            var res = await _daprClient.InvokeMethodAsync<BasePagedResponseData<TResponse>>(request);

            // 记录详细的分页响应日志
            _logger.LogDebug("能力中心分页响应详情:【AppID】{AppId}【RequestUrl】{RequestUrl}【ResponseCode】{ResponseCode}【ResponseMessage】{ResponseMessage}【TotalCount】{TotalCount}【DataCount】{DataCount}",
                appId, requestUrl, res.Code, res.Message, res.Data?.Total ?? 0, res.Data?.List?.Count ?? 0);

            if (res.Code != CodeStatusEnum.Success)
            {
                // 详细的错误日志
                _logger.LogWarning("能力中心分页响应错误:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【ResponseCode】{ResponseCode}【ErrorMessage】{ErrorMessage}【FullResponse】{FullResponse}",
                    appId, methodName, requestUrl, res.Code, res.Message, res.ToJson());

                // 分页查询失败时不抛异常，返回空数据，但记录警告日志
            }

            return res.Data ?? new BasePagedData<TResponse>();
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 统一处理调用异常
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="ex">异常对象</param>
        /// <param name="appId">应用ID</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="requestUrl">请求地址</param>
        /// <param name="parameters">请求参数（可选）</param>
        /// <returns>不会返回，总是抛出异常</returns>
        /// <exception cref="TimeoutException">超时异常</exception>
        /// <exception cref="ApplicationException">应用异常</exception>
        private TResponse HandleInvokeException<TResponse>(Exception ex, string appId, string methodName, string requestUrl, string? parameters = null)
        {
            // 检查是否为超时异常
            if (IsTimeoutException(ex))
            {
                // 详细的超时日志
                _logger.LogError(ex, "能力中心接口调用超时:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【Parameters】{Parameters}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}【InnerException】{InnerException}",
                    appId, methodName, requestUrl, parameters ?? "无", ex.GetType().Name, ex.Message, ex.InnerException?.Message ?? "无");

                // 简洁的用户提示信息
                throw new TimeoutException($"能力中心【{appId}】调用超时，请稍后重试", ex);
            }

            // 详细的异常日志
            _logger.LogError(ex, "能力中心接口调用异常:【AppID】{AppId}【MethodName】{MethodName}【RequestUrl】{RequestUrl}【Parameters】{Parameters}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}【StackTrace】{StackTrace}【InnerException】{InnerException}",
                appId, methodName, requestUrl, parameters ?? "无", ex.GetType().Name, ex.Message, ex.StackTrace, ex.InnerException?.Message ?? "无");

            // 简洁的用户提示信息
            throw new ApplicationException($"能力中心【{appId}】调用异常：{ex.Message}", ex);
        }

        /// <summary>
        /// 判断是否为超时异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否为超时异常</returns>
        private static bool IsTimeoutException(Exception ex)
        {
            return ex.Message.Contains("canceled", StringComparison.OrdinalIgnoreCase) ||
                   ex.Message.Contains("timed out", StringComparison.OrdinalIgnoreCase) ||
                   (ex.InnerException != null &&
                    (ex.InnerException.Message.Contains("canceled", StringComparison.OrdinalIgnoreCase) ||
                     ex.InnerException.Message.Contains("timed out", StringComparison.OrdinalIgnoreCase)));
        }
        /// <summary>
        /// 发送HTTP请求的统一封装
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="request">HTTP请求消息</param>
        /// <param name="httpMethod">HTTP方法类型</param>
        /// <returns>响应结果</returns>
        /// <exception cref="ApplicationException">应用异常</exception>
        private async Task<TResponse> SendRequestAsync<TResponse>(HttpRequestMessage request, RequestMethodEnum httpMethod)
        {
            var appId = GetAppId();
            var methodName = request.RequestUri?.PathAndQuery ?? "未知接口";
            var requestUrl = $"{appId}/{methodName}";

            try
            {
                // 设置HTTP方法
                request.Method = httpMethod switch
                {
                    RequestMethodEnum.GET => HttpMethod.Get,
                    RequestMethodEnum.POST => HttpMethod.Post,
                    _ => HttpMethod.Post
                };

                // 添加用户信息到请求头
                AddUserHeaders(request);

                // 记录详细的请求日志
                _logger.LogDebug("发送能力中心请求:【AppID】{AppId}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【Headers】{Headers}【RequestBody】{RequestBody}",
                    appId, requestUrl, httpMethod, request.Headers.ToJson(), request.Content?.ReadAsStringAsync().Result ?? "无");

                return await DefaultResponseAsync<TResponse>(request);
            }
            catch (Exception ex)
            {
                // 详细的错误日志
                _logger.LogError(ex, "发送能力中心请求失败:【AppID】{AppId}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}",
                    appId, requestUrl, httpMethod, ex.GetType().Name, ex.Message);

                // 重新抛出原异常，保持异常链
                throw;
            }
        }

        /// <summary>
        /// 发送HTTP请求的统一封装（支持指定用户信息）
        /// </summary>
        /// <typeparam name="TResponse">响应类型</typeparam>
        /// <param name="request">HTTP请求消息</param>
        /// <param name="httpMethod">HTTP方法类型</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <returns>响应结果</returns>
        /// <exception cref="ApplicationException">应用异常</exception>
        private async Task<TResponse> SendRequestWithUserAsync<TResponse>(HttpRequestMessage request, RequestMethodEnum httpMethod, Guid? userId, string? userName)
        {
            var appId = GetAppId();
            var methodName = request.RequestUri?.PathAndQuery ?? "未知接口";
            var requestUrl = $"{appId}/{methodName}";

            try
            {
                // 设置HTTP方法
                request.Method = httpMethod switch
                {
                    RequestMethodEnum.GET => HttpMethod.Get,
                    RequestMethodEnum.POST => HttpMethod.Post,
                    _ => HttpMethod.Post
                };

                // 添加指定用户信息到请求头
                AddUserHeaders(request, userId, userName);

                // 记录详细的请求日志
                _logger.LogDebug("发送能力中心请求(指定用户):【AppID】{AppId}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【UserId】{UserId}【UserName】{UserName}【Headers】{Headers}【RequestBody】{RequestBody}",
                    appId, requestUrl, httpMethod, userId, userName, request.Headers.ToJson(), request.Content?.ReadAsStringAsync().Result ?? "无");

                return await DefaultResponseAsync<TResponse>(request);
            }
            catch (Exception ex)
            {
                // 详细的错误日志
                _logger.LogError(ex, "发送能力中心请求失败(指定用户):【AppID】{AppId}【RequestUrl】{RequestUrl}【HttpMethod】{HttpMethod}【UserId】{UserId}【UserName】{UserName}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}",
                    appId, requestUrl, httpMethod, userId, userName, ex.GetType().Name, ex.Message);

                // 重新抛出原异常，保持异常链
                throw;
            }
        }

        /// <summary>
        /// 添加用户信息到请求头
        /// </summary>
        /// <param name="request">HTTP请求消息</param>
        private void AddUserHeaders(HttpRequestMessage request)
        {
            var userInfo = GetCurrentLoginUser();
            if (!string.IsNullOrEmpty(userInfo.UserId))
            {
                request.Headers.Add("X-Inno-UserId", userInfo.UserId);
            }
            if (!string.IsNullOrEmpty(userInfo.UserName))
            {
                request.Headers.Add("X-Inno-UserName", userInfo.UserName);
            }
        }

        /// <summary>
        /// 添加指定用户信息到请求头
        /// </summary>
        /// <param name="request">HTTP请求消息</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        private void AddUserHeaders(HttpRequestMessage request, Guid? userId, string? userName)
        {
            if (userId.HasValue && userId.Value != Guid.Empty)
            {
                request.Headers.Add("X-Inno-UserId", userId.Value.ToString());
            }
            if (!string.IsNullOrEmpty(userName))
            {
                request.Headers.Add("X-Inno-UserName", userName);
            }
        }
        /// <summary>
        /// 获取当前登录用户信息
        /// </summary>
        /// <returns>用户信息元组(UserId, UserName)</returns>
        private (string UserId, string UserName) GetCurrentLoginUser()
        {
            if (_httpContextAccessor.HttpContext?.User == null)
                return ("", "");

            var userId = _httpContextAccessor.HttpContext.User.Claims
                .FirstOrDefault(claim => claim.Type == "sub")?.Value ?? "";
            var userName = _httpContextAccessor.HttpContext.User.Claims
                .FirstOrDefault(claim => claim.Type == "upn")?.Value ?? "";

            return (userId, userName);
        }

        #endregion

        #region 其他功能方法

        /// <summary>
        /// 执行Dapr绑定操作
        /// </summary>
        /// <typeparam name="TRequest">请求类型</typeparam>
        /// <param name="operation">操作名称</param>
        /// <param name="input">输入数据</param>
        /// <returns>异步任务</returns>
        protected async Task InvokeBindingAsync<TRequest>(string operation, TRequest input)
        {
            var appId = GetAppId();
            var bindingUrl = $"{appId}/{operation}";
            var inputJson = input?.ToJson() ?? "null";

            try
            {
                _logger.LogInformation("能力中心绑定调用开始:【AppID】{AppId}【Operation】{Operation}【BindingUrl】{BindingUrl}【InputData】{InputData}",
                    appId, operation, bindingUrl, inputJson);

                await _daprClient.InvokeBindingAsync(appId, operation, input);

                _logger.LogInformation("能力中心绑定调用成功:【AppID】{AppId}【Operation】{Operation}【BindingUrl】{BindingUrl}",
                    appId, operation, bindingUrl);
            }
            catch (Exception ex)
            {
                // 详细的错误日志
                _logger.LogError(ex, "能力中心绑定调用异常:【AppID】{AppId}【Operation】{Operation}【BindingUrl】{BindingUrl}【InputData】{InputData}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}【StackTrace】{StackTrace}",
                    appId, operation, bindingUrl, inputJson, ex.GetType().Name, ex.Message, ex.StackTrace);

                // 注意：这里不重新抛出异常，保持原有行为
            }
        }

        /// <summary>
        /// 获取物流PDF流数据
        /// </summary>
        /// <param name="request">HTTP请求消息</param>
        /// <returns>PDF内容流</returns>
        /// <exception cref="ApplicationException">应用异常</exception>
        protected async Task<HttpContent> GetLogisticsPDFStream(HttpRequestMessage request)
        {
            request.Method = HttpMethod.Post;
            AddUserHeaders(request);

            var appId = GetAppId();
            var requestUrl = request.RequestUri?.ToString() ?? $"{appId}/pdf";

            _logger.LogInformation("获取物流PDF流开始:【AppID】{AppId}【RequestUrl】{RequestUrl}【Headers】{Headers}",
                appId, requestUrl, request.Headers.ToJson());

            try
            {
                var res = await _daprClient.InvokeMethodWithResponseAsync(request);

                // 记录详细的响应日志
                _logger.LogDebug("物流PDF流响应详情:【AppID】{AppId}【RequestUrl】{RequestUrl}【StatusCode】{StatusCode}【ContentType】{ContentType}【ContentLength】{ContentLength}",
                    appId, requestUrl, res.StatusCode, res.Content.Headers.ContentType?.ToString() ?? "未知", res.Content.Headers.ContentLength ?? 0);

                if (res.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    var responseContent = await res.Content.ReadAsStringAsync();

                    // 详细的错误日志
                    _logger.LogError("获取物流PDF流失败:【AppID】{AppId}【RequestUrl】{RequestUrl}【StatusCode】{StatusCode}【ResponseContent】{ResponseContent}【ReasonPhrase】{ReasonPhrase}",
                        appId, requestUrl, res.StatusCode, responseContent, res.ReasonPhrase ?? "未知");

                    // 简洁的用户提示信息
                    throw new ApplicationException($"能力中心【{appId}】获取PDF流失败：{res.ReasonPhrase ?? res.StatusCode.ToString()}");
                }

                _logger.LogInformation("获取物流PDF流成功:【AppID】{AppId}【RequestUrl】{RequestUrl}【ContentLength】{ContentLength}",
                    appId, requestUrl, res.Content.Headers.ContentLength ?? 0);

                return res.Content;
            }
            catch (Exception ex) when (!(ex is ApplicationException))
            {
                // 详细的异常日志
                _logger.LogError(ex, "获取物流PDF流异常:【AppID】{AppId}【RequestUrl】{RequestUrl}【ExceptionType】{ExceptionType}【ExceptionMessage】{ExceptionMessage}【StackTrace】{StackTrace}",
                    appId, requestUrl, ex.GetType().Name, ex.Message, ex.StackTrace);

                // 简洁的用户提示信息
                throw new ApplicationException($"能力中心【{appId}】获取PDF流异常：{ex.Message}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// HTTP请求方法枚举
    /// </summary>
    public enum RequestMethodEnum
    {
        /// <summary>
        /// POST请求
        /// </summary>
        POST = 0,

        /// <summary>
        /// GET请求
        /// </summary>
        GET = 1
    }
}
