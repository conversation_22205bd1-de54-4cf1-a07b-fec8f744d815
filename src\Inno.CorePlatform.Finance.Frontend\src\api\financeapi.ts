import request from '@/utils/request';
const gatewayUrl = window.gatewayUrl;
// 提交详情
export function CreateInputBillSubmit(query, InputBillId, bool) {
  return request({
    url: '/api/InputBillExecute/CreateInputBillSubmit',
    method: 'POST',
    data: {
      input: query,
      InputBillId: InputBillId,
      IsAdd: bool
    }
  });
}
// 提交发票
export function SubmitInputBill(InputBillId) {
  return request({
    url: '/api/InputBillExecute/SubmitInputBill',
    method: 'POST',
    data: InputBillId
  });
}

// 删除发票详情
export function DeleteBillSbumit(storeInCodes, id,detailIds) {
  return request({
    url: '/api/InputBillExecute/DeleteBillSbumit',
    method: 'POST',
    data: { storeInCodes: storeInCodes, inputBillId: id,detailIds:detailIds }
  });
}

//一键平尾差
export function EliminatingErrors(id) {
  return request({
    url: '/api/InputBillExecute/EliminatingErrors?id='+id,
    method: 'POST'
  });
}

//导出销项发票数据 数据大 可能会超时
export function ExportInvoiceCreditList(data) {
  return request({
    url: '/api/CreditQuery/ExportInvoiceCreditList',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出销项发票文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出销项发票文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
}

//导出发票清单数据 数据大 可能会超时
export function ExportInvoices(data) {
  return request({
    url: '/api/InvoiceQuery/ExportInvoices',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出发票清单文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出发票清单文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
}

//导出付款计划清单数据 数据大 可能会超时
export function ExportPayment(data) {
  return request({
    url: '/api/DebtQuery/GetExportPaymentList',
    data: data,
    method: 'POST',
    dataType: 'json',
    headers: { 'Content-type': 'application/json;' }, //它声明了请求体中的数据将会以json字符串的形式发送到后端
    responseType: 'blob' //判断是下载成功返回了二进制流还是失败返回了对象（比如服务端拒绝，返回对象，前端如果依然按二进制流处理会导致下载undefined文件），还可以是
  })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出付款计划清单文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    })
    .then((res) => {
      const xlsx = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([res.data], { type: xlsx }); //转换数据类型
      const a = document.createElement('a'); // 转换完成，创建一个a标签用于下载
      a.download = '导出付款计划清单文件' + new Date().getTime() + '.xlsx';
      a.href = window.URL.createObjectURL(blob);
      a.click();
      a.remove();
    })
    .catch((err) => {
      throw '请求错误';
    });
}

// 批量认款明细组件初始化数据
export function InitData(data) {
  return request({
    url: '/api/RecognizeReceive/initData',
    method: 'POST',
    data: {
      list: data
    }
  });
}


// 提交红字确认单生成
export function GenerateRedConfirmation(data) {
  return request({
    url: '/api/RedConfirmationFormNumberQuery/Generate',
    method: 'POST',
    data: data
  });
}

// 获取红字生成明细
export function GetDetails(data) {
  return request({
    url: '/api/RedConfirmationFormNumberQuery/GetDetails',
    method: 'POST',
    data: data
  });
}

//计算红冲金额（不含税）
export function ComputeAmount(quantity, priceTax, taxRate) {
   return request({
    url: '/api/RedConfirmationFormNumberQuery/ComputeAmount',
    method: 'POST',
    data: {
      quantity,
      priceTax,
      taxRate
    }
  });
}
export function GetProjectInfoByCompanyId(companyId) {
  return request({
    url: '/api/RefundApplyQuery/GetProjectInfoByCompanyId',
    method: 'POST',
    params: { companyId: companyId }
  });
}
export function getTaxRateList(type: any) {
  return request({
    url: gatewayUrl + `v1.0/bdsapi/api/dataDictionarys/getList`,
    method: 'post',
    data:{
      dictionaryType: type
    }
  });
}
export function GetReceiveBills(companyId, customerName, payerType = "bd_customer") {
  return request({
    url: '/api/RecognizeReceiveQuery/getReceiveBills',
    method: 'POST',
    data: { company:[companyId] ,customerName:customerName,payerType:payerType }
  });
}
