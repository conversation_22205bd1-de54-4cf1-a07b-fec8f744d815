﻿using Dapr.Client;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Interfaces;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Inno.CorePlatform.ServiceClient;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices
{
    public class DebtAppService : BaseAppService<DebtAppService>, IDebtAppService
    {
        public DebtAppService(FinanceDbContext db, DaprClient daprClient, ICodeGenClient codeClient, ILogger<DebtAppService> logger, IHttpContextAccessor httpContextAccessor, IApplyBFFService applyBFFService) : base(db, daprClient, codeClient, logger, httpContextAccessor, applyBFFService)
        {
        }

        /// <summary>
        /// 预付付款日期变更审核完成
        /// </summary>
        /// <param name="debtDetailAuditId"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task FinishChangeProbablyPayTime(Guid debtDetailAuditId)
        {

            var detailAudit = await _db.DebtDetailAudit.FirstOrDefaultAsync(p => p.Id == debtDetailAuditId);
            if (detailAudit != null)
            {
                var detail = await _db.DebtDetails.FirstOrDefaultAsync(p => p.Id == detailAudit.DebtDetailId);
                if (detail != null)
                {

                    using (var trans = await _db.Database.BeginTransactionAsync())
                    {
                        detail.AuditStatus = StatusEnum.Complate;
                        detailAudit.Status = StatusEnum.Complate;
                        detailAudit.UpdatedTime = DateTime.Now;
                        if (detail.BackPayTime != null)
                        {
                            detail.BackPayTime = null;
                        }
                        await UpdateProbablyPayTime(detail, detailAudit.CurrentProbablyPayTime);
                        await _db.SaveChangesAsync();
                        await trans.CommitAsync();
                    }
                }
            }
            else
            {
                throw new ApplicationException($"获取预计付款时间审核失败!预计付款时间审核Id:{debtDetailAuditId}");
            }


        }

        /// <summary>
        /// 更新预计付款日期
        /// </summary>
        /// <param name="debtDetail"></param>
        /// <param name="probablyPayTime"></param>
        /// <returns></returns>
        public async Task UpdateProbablyPayTime(DebtDetailPo debtDetail, DateTime? probablyPayTime)
        {
            debtDetail.UpdatedTime = DateTime.Now;
            debtDetail.ProbablyPayTime = probablyPayTime;
            _db.DebtDetails.Update(debtDetail);
        }
    }
}
