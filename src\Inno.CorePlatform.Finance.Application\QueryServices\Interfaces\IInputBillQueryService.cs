﻿using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 进项发票查询
    /// </summary>
     public interface IInputBillQueryService
    {
        // <summary>
        /// 获取进项发票数据发票抬头
        /// </summary>
        /// <returns></returns>
        Task<(List<LotInfo>, int)> GetStoreInItemCodeReceivedList(InputBillQueryInputDeatil query);
        /// <summary>
        /// 获取进项发票数据发票抬头
        /// </summary>
        /// <returns></returns>
        Task<(List<InputBillQueryListOut>, int)> GetListInputBillAsync(InputBillQueryInput query);

        /// <summary>
        /// 导出进项票数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<MemoryStream> ExportBill(InputBillQueryInput query);

        /// <summary>
        /// 获取进项发票数据金蝶详情
        /// </summary>
        /// <returns></returns>
        Task<(List<InputBillDeatilJDQueryListOut>, int)> GetListInputBillDetailJdAsync(InputBillQueryInputDeatil query);

        /// <summary>
        /// 获取进项发票数据提交详情
        /// </summary>
        /// <returns></returns>
        Task<(List<InputBillDeatilQueryListOut>, int)> GetListInputBillDetailAsync(InputBillQueryInputDeatil query);


        /// <summary>
        /// 获取经销购货入库单
        /// </summary>
        /// <returns></returns>
        Task<(List<StoreInDetailQueryOutput>, int)> GetListStoreInByCompanyAsync(StoreInDetailQueryInput query);

        /// <summary>
        /// 根据入库单获取 所有发票信息
        /// </summary>
        /// <returns></returns>
        Task<(List<InputBillDeatilQueryListOut>, int)> GetputBillListByStoreIncode(InputBillbyStoreIncode query);
         
        /// <summary>
        /// 查询显示 (经销调出单明细)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<StoreInDetailQueryOutput>, int)> GetListStoreOutByCompanyAsync(StoreInDetailQueryInput query); 
 
        Task<(List<StoreInDetailQueryOutput>, int)> GetConsignToPurchaseDetailGroup(StoreInDetailQueryInput input);

        /// <summary>
        /// 取购货修订的待入票数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(List<StoreInDetailQueryOutput>, int)> GetPurchaseReviseGroup(StoreInDetailQueryInput input);
        Task<List<StoreInDetailQueryOutput>> GetUseQuantity(List<StoreInDetailQueryOutput> input,Guid inputBillId);
        Task<(List<StoreInDetailQueryOutput>, int)> GetServerDebts(StoreInDetailQueryInput input);
        Task<(List<StoreInDetailQueryOutput>, int)> GetDebtsByType(StoreInDetailQueryInput input,DebtTypeEnum debtType);
        Task<(List<StoreInDetailQueryOutput>, int)> GetExchangeToReturn(StoreInDetailQueryInput input);
        Task<List<StoreInDetailQueryOutput>> GetUseDebtInvoiceAmount(List<StoreInDetailQueryOutput> input, Guid inputBillId);
        Task<List<PurchaseInputBillOutput>> GetPurchaseInputBill(PurchaseInputBillInput input);
        /// <summary>
        /// 获取进项票金蝶明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<InputBillQuerySumOutput> GetInputBillDetailSumJdAsync(InputBillQueryInputDeatil query);
        /// <summary>
        /// 获取进项发票提交明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
       Task<InputBillQuerySumOutput> GetInputBillDetailSumAsync(InputBillQueryInputDeatil query);

        /// <summary>
        /// 根据 应付单号获取进项发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<InputBillQueryListOut>, int)> GetListInputBillByDebtCodeAsync(InputBillQueryInput query);

        /// <summary>
        /// 根据 应付单号集合获取进项发票号
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<List<InputBillQueryByDebtCodesOutput>> GetListInputBillByDebtCodesAsync(List<string?>? codes);
    }
}
