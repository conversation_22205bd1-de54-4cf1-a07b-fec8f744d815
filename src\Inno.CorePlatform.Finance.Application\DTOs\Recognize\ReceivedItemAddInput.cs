﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    public class ReceivedItemAddInput
    {
        /// <summary>
        /// 收款单详情
        /// </summary>
        public List<RecognizeReceiveOutput> RecognizeReceiveInfos { get; set; }=new List<RecognizeReceiveOutput>();
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司Id
        /// </summary>
        public string? CompanyId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerNme { get; set; }
        /// <summary>
        /// 事业部编码
        /// </summary>
        public string? BusinessArea { get; set; }      
        /// <summary>
        /// 核算部门Id路径
        /// </summary>     
        public string BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>   
        public string BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门当前Id
        /// </summary>       
        public int BusinessDeptId { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public RecognizeReceiveClassifyEnum Classify { get; set; }
    }

   
}
