﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs
{
    public class AttachFileDto
    {
        /// <summary>
        /// 文件Id
        /// </summary>
        public string? AttachFileId { get; set; }

        /// <summary>
        /// 附件名
        /// </summary>
        public string? AttachFileName { get; set; }

        /// <summary>
        /// 附件文件大小
        /// </summary>
        public decimal AttachFileSize { get; set; } = 0;

        /// <summary>
        /// 附件类型
        /// </summary>
        public string? AttachFileType { get; set; }

        public string? UploadedBy { get; set; }
        public string? UploadedByName { get; set; }

        public DateTimeOffset? UploadedTime { get; set; }
    }
}
