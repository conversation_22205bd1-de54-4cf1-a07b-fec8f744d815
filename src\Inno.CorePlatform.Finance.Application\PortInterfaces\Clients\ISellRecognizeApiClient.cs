﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public   interface ISellRecognizeApiClient
    {
        /// <summary>
        /// 生成认款明细（销售）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool?>> CreateSellRecognizeReceive(List<SellRecognizeReceiveInput> input);
        /// <summary>
        /// 撤销认款明细（销售）
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool?>> CancelSellRecognizeReceive(List<CancelReceiptInput> input);

        /// <summary>
        /// 认款完成 
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<BaseResponseData<bool?>> Finished(string code);

        /// <summary>
        /// 根据销售子系统id获取公司
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<SaleSystemCompanyOutput?>> GetCompanyBySaleSystemId(string id);

        /// <summary>
        /// 根据销售子系统id获取列表（批量获取公司）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SaleSystemBaseOutput>> GetCompanyListBySaleSystemId(SaleSystemCompanyQueryInput input);

        /// <summary>
        /// 获取定单明细
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        Task<BaseResponseData<SellsDetailPageData>> GetSalesDetailByCodes(List<string> codes);
    }
}
