﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 返利计提查询入参
    /// </summary>
    public class RebateProvisionItemQueryInput : BaseQuery
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public Guid? UserId { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public string? BillDateBeging { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public string? BillDateEnd { get; set; }

        public int Status { get; set; }
    }

    /// <summary>
    /// 返利计提查询入参
    /// </summary>
    public class RebateProvisionDetailQueryInput : BaseQuery
    {
        /// <summary>
        /// 返利计提Id
        /// </summary>
        public Guid? RebateProvisionItemId { get; set; }
    }

    /// <summary>
    /// 创建返利计提Dto
    /// </summary>
    public class RebateProvisionItemCreateInput
    {
        public string? CurrentUser { get; set; }

        public Guid? UserId { get; set; }

        public string? UserName { get; set; }

        public Guid CompanyId { get; set; }

        public string? CompanyName { get; set; }

        public string? SysMonth { get; set; }

        public ProvisionTypeEnum ProvisionType { get; set; }

        public string BusinessDeptId { get; set; }

        public string? BusinessDeptFullName { get; set; }

        public string? BusinessDeptFullPath { get; set; }

        public string? BusinessDeptShortName { get; set; }
    }
}
