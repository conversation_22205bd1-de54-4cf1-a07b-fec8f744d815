﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.SendNotification;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    /// <summary>
    /// 发送消息通知
    /// </summary>
    public interface ISendNotificationClient
    {
        /// <summary>
        /// 发送消息通知
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task SendNotification(MessageNotificationInput input);
    }
}
