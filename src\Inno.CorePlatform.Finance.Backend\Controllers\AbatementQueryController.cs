﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 冲销查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AbatementQueryController : BaseController
    {
        private readonly IAbatementQueryService _abatementQueryService;
        private readonly ILogger<InvoiceQueryController> _logger;
        /// <summary>
        /// 冲销查询
        /// </summary>
        public AbatementQueryController(IAbatementQueryService abatementQueryService, ILogger<InvoiceQueryController> logger,ISubLogService subLog):base(subLog)
        {
            _abatementQueryService = abatementQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 冲销列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<AbatementQueryListOutput>> GetList([FromBody] AbatementQueryInput query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list,count)= await _abatementQueryService.GetListAsync(query);
                return new ResponseData<AbatementQueryListOutput>
                {
                    Code =200,
                    Data = new Data<AbatementQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 应收冲销列表查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetListByCredit")]
        public async Task<ResponseData<AbatementQueryListOutput>> GetListByCredit([FromBody] AbatementQueryInput query)
        {
            try
            {
                var uid = CurrentUser.Id;
                query.limit = 2000;
                var (list, count) = await _abatementQueryService.GetListAsync(query);
                return new ResponseData<AbatementQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<AbatementQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}

