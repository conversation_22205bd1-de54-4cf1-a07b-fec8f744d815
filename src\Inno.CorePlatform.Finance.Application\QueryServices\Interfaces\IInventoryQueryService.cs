﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IInventoryQueryService
    {
        /// <summary>
        /// 获取应收盘点列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditRecordOutputDto>, int)> GetCreditRecordListAsync(RecordBaseQueryDto query);

        /// <summary>
        /// 获取应收盘点详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditRecordDetailDto>, int)> GetCreditRecordDetailListAsync(DetailRecordBaseQueryDto query);



        /// <summary>
        /// 获取应付盘点列表
        /// </summary>
        /// <returns></returns>
        Task<(List<DebtRecordOutputDto>, int)> GetDebtRecordListAsync(RecordBaseQueryDto query);
        /// <summary>
        ///  获取应付盘点详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<DebtRecordDetailDto>, int)> GetDebtRecordDetailListAsync(DetailRecordBaseQueryDto query);


        /// <summary>
        /// 获取收款盘点列表
        /// </summary>
        /// <returns></returns>
        Task<(List<PaymentRecordOutputDto>, int)> GetPaymentRecordListAsync(RecordBaseQueryDto query);
        /// <summary>
        ///  获取收款盘点详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<PaymentRecordDetailDto>, int)> GetPaymentRecordDetailListAsync(DetailRecordBaseQueryDto query);

        /// <summary>
        /// 付款盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<PaymentDownLoadOutput>>> PaymentDownLoad(RecordBaseQueryDto query);
        /// <summary>
        /// 应付盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<DebtDownLoadOutput>>> DebtDownLoad(RecordBaseQueryDto query);

        /// <summary>
        /// 应收盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<CreditDownLoadOutput>>> CreditDownLoad(RecordBaseQueryDto query);
        /// <summary>
        /// 应收盘点下载（发票信息）- 已废弃，不再使用发票相关列
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [Obsolete("已废弃，应收盘点导出不再包含发票相关列")]
        Task<BaseResponseData<List<CreditDownLoadOutput>>> CreditDownLoadInvoiceInfo(List<Guid> ids);

        /// <summary>
        /// 订货系统应付盘点明细
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<CreditRecordDetailDto>, int)> GetCreditRecordDetailsListByIds(DetailRecordBaseQueryDto query);

        /// <summary>
        /// 垫资盘点下载
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<AdvanceRecordDetailDto>>> AdvanceDownLoad(RecordBaseQueryDto query);
        /// <summary>
        /// 获得垫资盘点
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
    Task<(List<AdvanceRecordOutputDto>, int)> GetAdvanceRecordListAsync(RecordBaseQueryDto query);
        /// <summary>
        /// 垫资盘点详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<(List<AdvanceRecordDetailDto>, int)> GetAdvanceRecordDetailListAsync(DetailRecordBaseQueryDto query);

        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportAsync(RecordBaseQueryDto query);

        /// <summary>
        /// 确认应收盘点
        /// </summary>
        /// <param name="id"></param>
        /// <param name="userName">确认人</param>
        /// <returns></returns>
        Task<BaseResponseData<string>> ConfirmCreditRecordItem(string id,string userName);
    }
}
