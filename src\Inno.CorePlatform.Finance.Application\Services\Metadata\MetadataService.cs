﻿using System.Reflection;

namespace Inno.CorePlatform.Finance.Application.Services.Metadata
{
    public class MetadataService : IMetadataService
    {
        public async Task<string> GetMetadata() => await ReadLoacalMetadata();


        private static async Task<string> ReadLoacalMetadata()
        {
            using var stream = ReadResource();

            if (stream == null)
                return string.Empty;

            using var streamReader = new StreamReader(stream);
            return await streamReader.ReadToEndAsync();
        }


        private static Stream ReadResource()
        {
            var currentAssembly = Assembly.GetCallingAssembly(); 
            return currentAssembly.GetManifestResourceStream($"{currentAssembly.GetName().Name}.StaticFiles.metadata.json");
        }

    }
}
