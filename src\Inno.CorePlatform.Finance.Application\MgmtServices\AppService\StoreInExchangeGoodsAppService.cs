﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Newtonsoft.Json;
using Polly;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class StoreInExchangeGoodsAppService : BaseAppService, IStoreInExchangeGoodsAppService
    {
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly Func<int, TimeSpan> _sleepDurationProvider;
        public StoreInExchangeGoodsAppService(ICreditRepository creditRepository,
            IDebtRepository debtRepository,
            ISubLogService subLogRepository,
            IUnitOfWork unitOfWork, IDomainEventDispatcher? deDispatcher,
            IAppServiceContextAccessor? contextAccessor,
            IInventoryApiClient inventoryApiClient,
            IBDSApiClient bDSApiClient,
            IProjectMgntApiClient projectMgntApiClient,
            IKingdeeApiClient kingdeeApiClient,
            Func<int, TimeSpan> sleepDurationProvider = null)
            : base(creditRepository, debtRepository, subLogRepository, unitOfWork, deDispatcher, contextAccessor)
        {
            _inventoryApiClient = inventoryApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _projectMgntApiClient = projectMgntApiClient;
            _sleepDurationProvider = sleepDurationProvider ?? ((retryAttempt) => TimeSpan.FromSeconds(10));
        }

        public override async Task<BaseResponseData<int>> PullIn(EventBusDTO input)
        {
            try
            {
                var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(3, _sleepDurationProvider);
                InventoryStoreInOutput storein = null;
                await retryPolicy.ExecuteAsync(async () =>
                {
                    storein = await _inventoryApiClient.QueryStoreInByCode(input.BusinessCode);
                    if (storein == null)
                    {
                        throw new Exception("未查询到入库单");
                    }
                    if (storein == null || !storein.storeInDetails.Any())
                    {
                        throw new Exception("订阅入库事件出错，原因：查询上游单据时未获取到相关数据");
                    }
                });
                var requestBody = JsonConvert.SerializeObject(input);
                var ret = await PushToKingdee(storein, input.BusinessSubType, requestBody, input.useBillDate);
                return ret;
            }
            catch
            {
                throw;
            }
        }


        private async Task<BaseResponseData<int>> PushToKingdee(InventoryStoreInOutput input
            , string classify, string preRequestBody,
            bool? useBillDate = false)
        {

            var companyInfo = (await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput
            {
                ids = new List<string> { input.companyId.ToString() }
            })).FirstOrDefault();
            var inputKD = new HoldStorageInput()
            {
                billno = input.storeInCode,
                jfzx_date = DateTimeHelper.LongToDateTime(input.storeInDate),
                jfzx_tallydate = DateTimeHelper.LongToDateTime(input.storeInDate),
                //jfzx_supplier = input.agentId?.ToString().ToUpper(),
                jfzx_customer = input.customerId?.ToString().ToUpper(),
                org = companyInfo.nameCode,
                jfzx_remake = input.remark ?? "无",
                jfzx_creator = input.storeInBy ?? "none",
                StoreInType = input.storeInType.Value,
                jfzx_businessorg = input.businessDeptId
            };
            if (input.storeInDetails != null && input.storeInDetails.Any())
            {
                var projectIds = input.storeInDetails.Select(p => p.projectId.Value).Distinct().ToList();
                var projectInfo = await _projectMgntApiClient.GetProjectInfoByIds(projectIds);

                var productNameIds = input.storeInDetails.Select(p => p.productNameId.Value).Distinct().ToList();
                var productNameInfo = await _bDSApiClient.GetProductNameInfoAsync(productNameIds);

                inputKD.entryentity = new List<HoldStorageDetail>();
                var fk_jfzx_totalsalescost = 0m;
                foreach (var storeInDetail in input.storeInDetails)
                {
                    var thisProject = projectInfo.FirstOrDefault(t => t.Id == storeInDetail.projectId);
                    var thisProductInfo = productNameInfo.FirstOrDefault(e => e.productNameId == storeInDetail.productNameId);
                    var jfzx_material = Guid.Empty;
                    if (thisProductInfo != null && thisProductInfo.classificationNewGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationNewGuid.Value;
                    }
                    else if (thisProductInfo != null && thisProductInfo.classificationGuid.HasValue)
                    {
                        jfzx_material = thisProductInfo.classificationGuid.Value;
                    }
                    if (storeInDetail.mark == 1 && storeInDetail.standardUnitCost == null)
                    {
                        throw new Exception("操作失败：原因，mark=1但是标准成本没有值");
                    }
                    var taxCost = storeInDetail.mark == 0 || storeInDetail.mark == 3 ? storeInDetail.unitCost.Value : storeInDetail.standardUnitCost.Value;
                    //var taxCost = storeInDetail.unitCost.Value;
                    var noTaxCost = Math.Round(taxCost / (1 + storeInDetail.taxRate.Value / 100.00M), 2);//不含税成本
                    //销售成本总额
                    var noTaxCost10 = Math.Round(taxCost / (1 + storeInDetail.taxRate.Value / 100.00M), 10);//不含税成本
                    if (storeInDetail.mark == 0 || storeInDetail.mark == 3)
                    {
                        fk_jfzx_totalsalescost += noTaxCost10 * storeInDetail.quantity;
                    }
                    else
                    {
                        fk_jfzx_totalsalescost += storeInDetail.standardUnitCost.Value * storeInDetail.quantity;
                    }
                    if ((storeInDetail.tariffAmount.HasValue && storeInDetail.tariffAmount.Value > 0) || (storeInDetail.importAddAmount.HasValue && storeInDetail.importAddAmount.Value > 0))
                    {
                        var detailInfo = new HoldStorageDetail
                        {
                            jfzx_count = storeInDetail.quantity,
                            jfzx_material = jfzx_material.ToString().ToUpper(),
                            jfzx_model = storeInDetail.specification,
                            jfzx_projectnos = thisProject?.Code,
                            jfzx_suppliers = storeInDetail.agentId.Value.ToString().ToUpper(),
                            Mark = storeInDetail.mark,
                            jfzx_unitprice = storeInDetail.mark == 0 || storeInDetail.mark == 3 ? noTaxCost : storeInDetail.standardUnitCost.Value,
                            jfzx_tariff = storeInDetail.tariffAmount,
                            jfzx_vat = storeInDetail.importAddAmount,
                        };
                        detailInfo.jfzx_sellingcost = detailInfo.jfzx_unitprice * storeInDetail.quantity;
                        inputKD.entryentity.Add(detailInfo);
                    }
                }
                inputKD.fk_jfzx_totalsalescost = Math.Round(fk_jfzx_totalsalescost, 2);
                if (inputKD.entryentity.Count() > 0)
                { 
                    var kingdeeRes = await _kingdeeApiClient.PushStoreInToKingdeeWithoutFinance(new List<HoldStorageInput> { inputKD }, classify, preRequestBody);
                    if (kingdeeRes.Code == CodeStatusEnum.Success || kingdeeRes.ErrorCode == 800)
                    {
                    }
                    else
                    {
                        throw new Exception(kingdeeRes.Message);
                    }
                }
            }
            else
            {
                return BaseResponseData<int>.Failed(500, "订阅入库事件出错，原因：没有明细数据");
            }
            return BaseResponseData<int>.Success("操作成功");
        }
    }
}
