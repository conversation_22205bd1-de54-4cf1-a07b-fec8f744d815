﻿
namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 发票入账单明细查询出参
    /// </summary>
    public class InvoiceReceiptDetailQueryOutput
    {
        public Guid? Id { get; set; }

        /// <summary>
        /// 认款单Id
        /// </summary>
        public Guid? InvoiceReceiptItemId { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary> 
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 发票验证码
        /// </summary> 
        public string? InvoiceCheckCode { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary> 
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 是否取消
        /// </summary>
        public bool? IsCancel { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }


    }
    public class InvoiceReceiptDetailVoOutput : InvoiceReceiptDetailQueryOutput
    {
        /// <summary>
        /// 应收金额
        /// </summary> 
        public decimal? CreditAmount { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }

        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalId { get; set; }
        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }
    }

    public class InvoiceReceiptQueryBaseDTO
    {
        /// <summary>
        /// 业务单元Id
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 查询辅助字段，拼接发票号和应收单号
        /// </summary>
        public string InvoiceDetailStr { get; set; }
    }
    public class InvoiceReceiptDetailSumOutput 
    {
        public decimal? InvoiceAmountSum { get; set; }
        public decimal? CreditAmountSum { get; set; }
    }

}
