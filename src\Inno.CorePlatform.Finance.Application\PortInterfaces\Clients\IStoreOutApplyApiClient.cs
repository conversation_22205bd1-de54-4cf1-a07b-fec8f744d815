﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IStoreOutApplyApiClient
    {
         Task<StoreOutApplyOutput> GetById(Guid Id);

        /// <summary>
        /// 获取出库申请列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<StoreOutApplyListDto>> GetStoreOutApplyList(StoreOutApplyInput input);
    }
}
