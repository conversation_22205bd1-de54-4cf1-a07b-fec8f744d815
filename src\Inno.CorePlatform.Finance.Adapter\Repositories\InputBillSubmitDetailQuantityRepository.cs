﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InputBillSubmitDetailQuantityRepository : EfBaseRepository<Guid, InputBillSubmitDetailQuantity, InputBillSubmitDetailQuantityPo>, IInputBillSubmitDetailQuantityRepository
    {
        private FinanceDbContext db;
        public InputBillSubmitDetailQuantityRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            db = dbContext;
        }

        public async Task<int> AddRangeAsync(List<InputBillSubmitDetailQuantity> list)
        {
            var addrang= list.Adapt<List<InputBillSubmitDetailQuantityPo>>();
            await db.InputBillSubmitDeatilQuantitys.AddRangeAsync(addrang);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }

        public async Task<int> DeteteManyAsync(List<Guid> lstId)
        {
            var lstPo = await db.InputBillSubmitDeatilQuantitys.Where(t => lstId.Contains(t.Id)).ToListAsync();
            db.InputBillSubmitDeatilQuantitys.RemoveRange(lstPo);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }

        public override Task<int> UpdateAsync(InputBillSubmitDetailQuantity root)
        {
            throw new NotImplementedException();
        }

        protected override InputBillSubmitDetailQuantityPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<InputBillSubmitDetailQuantityPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
