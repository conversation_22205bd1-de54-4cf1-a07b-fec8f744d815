﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices
{
    /// <summary>
    /// 能够查询所有数据表的接口
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public interface IBaseAllQueryService<TEntity> where TEntity : class
    {
        /// <summary>
        /// 返回一个IQueryable
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="includeTableNames"></param>
        /// <returns></returns>
        IQueryable<TEntity> GetIQueryable(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null);

        /// <summary>
        /// 是否存在
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="includeTableNames"></param>
        /// <returns></returns>
        Task<bool> IsExistAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null);
        /// <summary>
        /// 去满足条件的第一个
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="includeTableNames"></param>
        /// <returns></returns>
        Task<TEntity> FirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null);
        /// <summary>
        /// 取满足条件的集合
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="includeTableNames"></param>
        /// <param name="isTracking"></param>
        /// <param name="SelectColumn"></param>
        /// <returns></returns>
        Task<List<TEntity>> GetAllListAsync(Expression<Func<TEntity, bool>> predicate, List<string> includeTableNames = null, Expression<Func<TEntity, TEntity>> SelectColumn = null);
    }
}
