﻿using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.CustomizeInvoices;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.AppService
{
    public class CustomizeInvoiceCreditAppService : ICustomizeInvoiceCreditAppService
    {
        private readonly ICustomizeInvoiceCreditRepository _customizeInvoiceCreditRepository;
        public CustomizeInvoiceCreditAppService(ICustomizeInvoiceCreditRepository customizeInvoiceCreditRepository)
        {
            this._customizeInvoiceCreditRepository = customizeInvoiceCreditRepository;
        }
        public Task<int> AddManyAsync(List<CustomizeInvoiceCredit> inputs)
        {
            return _customizeInvoiceCreditRepository.AddManyAsync(inputs);
        }
    }
}
