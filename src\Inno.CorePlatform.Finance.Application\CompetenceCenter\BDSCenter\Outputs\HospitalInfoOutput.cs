﻿using Inno.CorePlatform.Finance.Domain.DomainObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{
    public class HospitalMetaOutput
    {
        public string customerId { get; set; }
        public string customerName { get; set; }

        public string latestUniCode { get; set; }
        public List<DaprHospitalDeptOutput> customerDeptRes { get; set; }
    }
    public class DaprHospitalDeptOutput
    {
        public string customerDeptId { get; set; }
        public string deptName { get; set; }
        public List<DaprHospitalAddressOutput> deptAddressList { get; set; }
        public List<DaprHospitalPersonOutput> customerDeptStaffList { get; set; }
    }
    public class DaprHospitalPersonOutput
    {
        public string customerDeptStaffId { get; set; }
        public string name { get; set; }
        public string telephone { get; set; }
    }
    public class DaprHospitalAddressOutput
    {
        /// <summary>
        /// 
        /// </summary>
        public string city { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string cityDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string county { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string countyDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerAddress { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerAddressId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int customerAddressType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerAddressTypeDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerContact { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerContactInfo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string customerId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string province { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string provinceDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string statusDesc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string unitIndexId { get; set; }
    }
}
