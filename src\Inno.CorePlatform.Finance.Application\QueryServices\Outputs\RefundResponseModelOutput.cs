﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 退款响应模型类
    /// </summary>
    public class RefundResponseModelOutput
    {
        /// <summary>
        /// 总条数
        /// </summary>
        public int? all { get; set; }
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<RefundResponseBodyModel>? list { get; set; }
    }
    /// <summary>
    /// 退款响应体模型类
    /// </summary>
    public class RefundResponseBodyModel
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string? billno { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string? e_payee { get; set; }
        /// <summary>
        /// 客户 id
        /// </summary>
        public string? e_payeeNum { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string? org { get; set; }
        /// <summary>
        /// 核算部门 id
        /// </summary>
        public string? orgNum { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public string? payment { get; set; }
        /// <summary>
        /// 公司 id
        /// </summary>
        public string? paymentNum { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        public string? e_settlementtype { get; set; }
        /// <summary>
        /// 付款方式 id
        /// </summary>
        public string? e_settlementtypeid { get; set; }
        /// <summary>
        /// 收款账号
        /// </summary>
        public string? e_payeeaccbanknum { get; set; }
        /// <summary>
        /// 退款类型
        /// </summary>
        public string? paymenttype { get; set; }
        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal? payeeamount { get; set; }
        /// <summary>
        /// 单据日期
        /// </summary>
        public string? applydate { get; set; }
        /// <summary>
        /// OA 流程 id
        /// </summary>
        public string? jfzx_requestid { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? creator { get; set; }
        /// <summary>
        /// 退款事由
        /// </summary>
        public string? applycause { get; set; }
        /// <summary>
        /// 单据状态
        /// </summary>
        public string? billstatus { get; set; }
        /// <summary>
        /// 付款状态
        /// </summary>
        public string? paidstatus { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string? createtime { get; set; }
        /// <summary>
        /// 收款单号列表
        /// </summary>
        public List<string>? receivablesNumberList { get; set; }
        /// <summary>
        /// 明细信息列表
        /// </summary>
        public List<RefundEntryModel>? refundEntry { get; set; }
    }
    /// <summary>
    /// 退款明细信息模型类
    /// </summary>
    public class RefundEntryModel
    {
        /// <summary>
        /// 负数应收单
        /// </summary>
        public string? jfzx_minusnumber { get; set; }
        /// <summary>
        /// 收款金额
        /// </summary>
        public decimal? e_payeeamount { get; set; }
    }
}
