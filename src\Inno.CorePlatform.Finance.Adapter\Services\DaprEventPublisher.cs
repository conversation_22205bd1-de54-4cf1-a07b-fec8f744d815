using Dapr.Client;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Services;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Services
{
    /// <summary>
    /// Dapr 事件发布服务实现
    /// </summary>
    public class DaprEventPublisher : IDaprEventPublisher
    {
        private readonly DaprClient _daprClient;
        private readonly ILogger<DaprEventPublisher> _logger;
        private const string PubSubName = "pubsub-default"; // 使用现有的 pubsub 组件

        public DaprEventPublisher(DaprClient daprClient, ILogger<DaprEventPublisher> logger)
        {
            _daprClient = daprClient;
            _logger = logger;
        }

        public async Task PublishAsync<T>(string topicName, T eventData, CancellationToken cancellationToken = default)
        {
            await PublishAsync(topicName, eventData, null, cancellationToken);
        }

        public async Task PublishAsync<T>(string topicName, T eventData, Dictionary<string, string>? metadata, CancellationToken cancellationToken = default)
        {
            try
            {
                await _daprClient.PublishEventAsync(PubSubName, topicName, eventData, metadata, cancellationToken);
                _logger.LogDebug("已发布事件到主题 {TopicName}", topicName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发布事件到主题 {TopicName} 失败", topicName);
                // 日志发布失败不影响主业务流程
            }
        }
    }

}
