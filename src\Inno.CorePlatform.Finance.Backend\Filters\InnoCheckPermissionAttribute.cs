﻿using Dapr.Client;
using Google.Rpc;
using Inno.CorePlatform.Common.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Inno.CorePlatform.Finance.Backend
{
    public class InnoCheckPermissionAttribute : ActionFilterAttribute
    {
        private readonly string functionsUri;

        public InnoCheckPermissionAttribute(string functionsUri)
        {
            this.functionsUri = functionsUri;
        }
        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            var userId = Guid.Parse(context.HttpContext.User.Claims.FirstOrDefault(t => t.Type == "sub")?.Value);
           // var userId = Guid.Parse("39c5483e-6c80-4328-8227-4412e53a3b35");


            try
            {
            
                //using var daprClient = new DaprClientBuilder().UseHttpEndpoint("http://localhost:3511").Build();

                using var daprClient = new DaprClientBuilder().Build();
                var lstUserPermission = await daprClient.InvokeMethodAsync<List<string>>(HttpMethod.Get, "pc-webapi", $"api/RoleMgmt?userId={userId}");

                if (lstUserPermission.Contains(functionsUri))
                {
                    await next();
                }
                else
                {
                    context.Result = new CustomUnauthorizedResult(new ResponseData<bool>
                    {
                        Code = 401,
                        Msg = "您没有执行该功能的权限!"
                    });
                }
            }
            catch (Exception)
            {
                await next();
            }
          

            //return base.OnActionExecutionAsync(context, next);
        }
        public class CustomUnauthorizedResult : JsonResult
        {
            public CustomUnauthorizedResult(object value)
                : base(value)
            {
                StatusCode = StatusCodes.Status401Unauthorized;
            }
        }
    }
}
