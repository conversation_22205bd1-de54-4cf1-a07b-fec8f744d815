﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Projects
{
    public class RuleConfigInput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 业务单元ID
        /// </summary>
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 厂家ID
        /// </summary>
        public Guid? Producerid { get; set; }
        /// <summary>
        /// 供应商ID
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public Guid? Hospitalid { get; set; }
        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid? ProductId { get; set; }

        public int? page { get; set; } = 0;
        public int? limit { get; set; } = 0;

        public Guid? ProjectId { get; set; }
    }

    public class ProductCostInput
    {
        public Guid? PreAuditId { get; set; }
        public string? ProductNo { get; set; }
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 0;
    }
    public class ProductCostOutput : ProductCost
    {
        public List<ProductCost>? Children { get; set; }
    }
    public class ProductCost {

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid PreAuditId { get; set; }

        /// <summary>
        /// 品名Id
        /// </summary>
        public Guid ProductNameId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? GeneralName { get; set; }

        /// <summary>
        /// 注册证号
        /// </summary>
        public string? RegisterNo { get; set; }

        /// <summary>
        /// 厂家
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 采购单位
        /// </summary>
        public string? PurchaseUnit { get; set; }

        /// <summary>
        /// 标准采购成本
        /// </summary>
        public decimal? PurchaseStandardCost { get; set; }

        /// <summary>
        /// 集采标准成购成本
        /// </summary>
        public decimal? CPurchaseStandardCost { get; set; }

        /// <summary>
        /// 销售单位
        /// </summary>
        public string? SaleUnit { get; set; }

        /// <summary>
        /// 标准销售成本
        /// </summary>
        public decimal? SaleStandardCost { get; set; }

        /// <summary>
        /// 集采标准销售成本
        /// </summary>
        public decimal? CSaleStandardCost { get; set; }
    }

    public class GetProjectInfoByIdOrNameInput
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        public string? Id { get; set; }

        ///// <summary>
        ///// 项目名称
        ///// </summary>
        //public string? Name { get; set; }

        ///// <summary>
        ///// 用户ID
        ///// </summary>
        //public Guid? UserId { get; set; }
        
        ///// <summary>
        ///// 
        ///// </summary>
        //public string? FunctionUri { get; set; }

        ///// <summary>
        ///// 页码
        ///// </summary>
        //public int? Page { get; set; }

        ///// <summary>
        ///// 每页数量
        ///// </summary>
        //public int? Limit { get; set; }

        ///// <summary>
        ///// 用户名称
        ///// </summary>
        //public string? UserName { get; set; }
    }
}
