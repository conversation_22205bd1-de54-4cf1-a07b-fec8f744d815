﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InventoryController : BaseController
    {
        private readonly IInventoryMgmAppService _inventoryMgmAppService;
        private readonly ICreditQueryService _creditQueryService;
        public InventoryController(
            IInventoryMgmAppService inventoryMgmAppService,
            ICreditQueryService creditQueryService, ISubLogService subLog) : base(subLog)
        {
            _inventoryMgmAppService = inventoryMgmAppService;
            _creditQueryService = creditQueryService;
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        [HttpPost("StartInventory")]
        public async Task<BaseResponseData<int>> StartInventory()
        {
            var res = await _inventoryMgmAppService.StartInventory();
            return res;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="queryDto"></param>
        /// <returns></returns>
        [HttpPost("GetInventoryList")]
        public async Task<BaseResponseData<PageResponse<InventoryDTO>>> GetInventoryList([FromBody] InventoryQueryDto queryDto)
        {
            queryDto.UserId = CurrentUser.Id.Value;
            var res = await _inventoryMgmAppService.GetInventoryList(queryDto);
            return new BaseResponseData<PageResponse<InventoryDTO>>()
            {
                Code = CodeStatusEnum.Success,
                Data = new PageResponse<InventoryDTO>
                {
                    List = res.Item1,
                    Total = res.Item2
                }
            };
        }


        /// <summary>
        /// 生成其他盘点
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpPost("CreateOtherCheck")]
        public async Task<BaseResponseData<int>> CreateOtherCheck(Guid companyId)
        {
            // 检查用户信息是否存在，如果不存在则提示重新登录
            if (!CurrentUser.Id.HasValue || CurrentUser.Id.Value == Guid.Empty || string.IsNullOrEmpty(CurrentUser.UserName))
            {
                return BaseResponseData<int>.Failed(0, "用户信息无效，请重新登录");
            }

            return await _inventoryMgmAppService.CreateOtherCheck(companyId, CurrentUser.Id.Value, CurrentUser.UserName);
        }


        /// <summary>
        /// 完成总盘点
        /// </summary>
        /// <param name="inventoryItemId"></param>
        /// <returns></returns>
        [HttpPost("FinishInventory")]
        public async Task<BaseResponseData<int>> FinishInventory(Guid inventoryItemId)
        {
            var userName = CurrentUser.UserName;
            return await _inventoryMgmAppService.FinishInventory(inventoryItemId, userName);
        }



        /// <summary>
        /// 获取应收单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<CreditQueryListOutput>> GetList([FromBody] CreditQueryInput query)
        {
            try
            {
                if (!query.IsNoNeedInvoice.HasValue)
                {
                    query.IsNoNeedInvoice = Domain.IsNoNeedInvoiceEnum.Need;
                }
                query.UserId = CurrentUser.Id.Value;
                query.CurrentUserName = CurrentUser.UserName;
                var (list, count) = await _creditQueryService.GetListAsync(query);
                return new ResponseData<CreditQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<CreditQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取未开票应收单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetCreditList")]
        public async Task<ResponseData<CreditQueryListOutput>> GetCreditList([FromBody] CreditQueryInput query)
        {
            query.IsNoNeedInvoice = Domain.IsNoNeedInvoiceEnum.Need;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;
            query.isNotInvoice = "notInvoice";
            var (list, count) = await _creditQueryService.GetListAsync(query);
            return new ResponseData<CreditQueryListOutput>
            {
                Code = 200,
                Data = new Data<CreditQueryListOutput>
                {
                    List = list,
                    Total = count,
                }
            };
        }

        /// <summary>
        /// 是否盘点期间
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("InventoryCheck")]
        public async Task<BaseResponseData<int>> InventoryCheck(Guid companyId)
        {
            return await _inventoryMgmAppService.InventoryCheck(companyId);
        }
    }
}
