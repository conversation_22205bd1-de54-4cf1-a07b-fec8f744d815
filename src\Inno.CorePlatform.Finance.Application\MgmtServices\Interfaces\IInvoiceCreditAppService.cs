﻿using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IInvoiceCreditAppService
    {
        Task<BaseResponseData<int>> CreateInvoiceCredit(InvoiceCreditInput input); 
        Task<BaseResponseData<string>> ChangeRelationship(ChangeRelationshipInput input);
        Task<BaseResponseData<List<CreditPo>?>> GetWriteOffCreditBillCode(string billCode);
        Task<BaseResponseData<string>> GetRecognizeReceiveDetailByInvoiceNos(List<string> invoiceNos);

        /// <summary>
        /// 预开票发票应收绑定
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CreatePreInvoiceCredit(PreInvoiceCreditInput input);

        Task<BaseResponseData<int>> SetSunPurchase(SetSunPurchaseInput input);

        /// <summary>
        /// 重推SPD商务平台
        /// </summary>
        /// <param name="invoiceNos"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> RefreshPushSPD(List<string?> invoiceNos);

        /// <summary>
        /// 协调服务导出
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoicesByCoordinate(InvoicesQueryInput query);
        /// <summary>
        /// 销项发票作废【非跨月】
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> CancelCustomizeInvoice(KingdeeCancelCustomizeInvoiceInput input);
        Task<BaseResponseData<string>> PushInvoiceToSPD(PushSPDInvoicesInput input);
    }
}
