﻿using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    public interface IReconciliationItemQueryService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        Task<PageResponse<ReconciliationItemOutput>> GetListPages(ReconciliationItemInput input);
    }
}
