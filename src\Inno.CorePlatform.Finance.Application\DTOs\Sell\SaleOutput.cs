﻿using Inno.CorePlatform.Finance.Domain;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class SaleOutput : BusinessDepartDTO
    {
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string RelateCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime BillDate { get; set; }

        /// <summary>
        /// 公司编号
        /// </summary>
        public Guid CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 业务单元编号
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 订货部门Id
        /// </summary>
        public Guid? CustomerDeptId { get; set; }
        /// <summary>
        /// 订货部门名称
        /// </summary>
        public string? DeptName { get; set; }
        /// <summary>
        /// 订货人Id
        /// </summary>
        public Guid CustomerPersonId { get; set; }

        /// <summary>
        /// 订货人名称
        /// </summary>
        public string? CustomerPersonName { get; set; }

        /// <summary>
        /// 默认收货地址
        /// </summary>
        public CustomerAddressOutput? DefaultAddress { get; set; }

        /// <summary>
        /// 实际收货地址
        /// </summary>
        public CustomerAddressOutput CustomerAddress { get; set; }

        /// <summary>
        /// 事业部
        /// </summary>
        public BusinessDeptOutput BusinessDeptInfo { get; set; }

        public string DisplayBusinessDept
        {
            get
            {
                string result = string.Empty;
                if (BusinessDeptInfo != null)
                {
                    result = BusinessDeptInfo.Name;
                }
                return result;
            }
        }
        public List<string> BusinessDepts
        {
            get
            {
                var result = new List<string>();
                if (this.BusinessDeptInfo != null)
                {
                    result = this.BusinessDeptInfo.OrignValue;
                }
                return result;
            }
        }

        /// <summary>
        /// 发货状态
        /// </summary>
        public SendStatusEnum SendStatus { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public SaleStatusEnum Status { get; set; }


        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        /// <summary>
        /// 订单属性
        /// </summary>
        public string? SaleProp { get; set; }



        /// <summary>
        /// 加急
        /// </summary>
        public bool? IsUrgent { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        public int? InvoiceType { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 客户备注（预订单备注）
        /// </summary>
        public string? PreSaleRemark { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public Guid? StoreHouseId { get; set; }

        /// <summary>
        /// 库房
        /// </summary>
        public string? StoreHouseName { get; set; }

        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginalSaleCode { get; set; }

        /// <summary>
        /// 原始订单类型
        /// </summary>
        public SaleTypeEnum? OriginalSaleType { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }


        /// <summary>
        /// 是否成本设定
        /// </summary>
        public bool IsCostSetting { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }


        /// <summary>
        /// 明细
        /// </summary>
        public List<SaleDetailOutput> SaleDetails { get; set; }

        /// <summary>
        /// 核销明细
        /// </summary>
        public List<TempInventoryDetailOutput> TempInventoryDetails { get; set; }
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 修订明细
        /// </summary>
        public List<SaleReviseDetailDto> SaleReviseDetails { get; set; }
        /// <summary>
        /// 订单来源
        /// </summary>
        public SaleSourceEnum Source { get; set; }

        /// <summary>
        /// 订单来源名称
        /// </summary>
        public string SourceName
        {
            get
            {
                return Source.GetDescription();
            }
        }

        /// <summary>
        /// 终端医院Id
        /// </summary> 
        public string? HospitalId { get; set; }

        /// <summary>
        /// 终端医院
        /// </summary>
        public string? HospitalName { get; set; }

        /// <summary>
        /// 返利类型
        /// </summary>
        public RebateTypeEnum? RebateType { get; set; }

        /// <summary>
        /// 是否直放的标识
        /// </summary>
        public SaleSubTypeEnum SaleSubType { get; set; }

        /// <summary>
        /// 供应商名称（直放类订单显示）2024-12-28
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 是否可以导入暂存核销明细
        /// </summary>
        public bool CanImportTempInventoryDetail { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 阳采订单号
        /// </summary>
        public string? SunPurchaseRelatecode { get; set; }

        /// <summary>
        /// 结算清单附件
        /// </summary>
        public List<AttachFile>? SettlementFiles { get; set; }

        public ServiceConfirmRevenuePlanModeEnum? ServiceConfirmRevenuePlanMode { get; set; }
        public PriceSourceEnum? PriceSourceType { get;  set; }

    }

    public class AttachFile
    {
        public string AttachFileId { get; set; }

        public string AttachFileName { get; set; }

        public string? AttachFilePath { get; set; } 
    }
    /// <summary>
    /// 销售出库直放类型枚举
    /// </summary>
    public enum SaleSubTypeEnum
    {
        /// <summary>
        /// 销售出库非直放订单
        /// </summary>
        [Description("销售出库")]
        SaleNoDirectOrder = 0,

        /// <summary>
        /// 销售出库直放订单
        /// </summary>
        [Description("直放订单")]
        SaleDirectOrder = 1,

        /// <summary>
        /// 维保服务
        /// </summary>
        [Description("维保服务")]
        MaintenanceService = 2,
        /// <summary>
        /// 技术服务
        /// </summary>
        [Description("技术服务")]
        TechnicalService = 3,

        /// <summary>
        /// 小额低频订单
        /// </summary>
        [Description("小额低频订单")]
        SaleSmallAmount = 4,

        /// <summary>
        /// B类订单
        /// </summary>
        [Description("B类订单")]
        SaleForB = 5,

        /// <summary>
        /// 租借服务
        /// </summary>
        [Description("租借服务")]
        Lease = 6,

        /// <summary>
        /// SPD服务
        /// </summary>
        [Description("SPD服务")]
        SPD = 8,

        /// <summary>
        /// 纯代服务
        /// </summary>
        [Description("纯代服务")]
        PureGeneration = 9,

        /// <summary>
        /// 仓储服务
        /// </summary>
        [Description("仓储服务")]
        Storage = 7,

        /// <summary>
        /// 核心平台软件服务
        /// </summary>
        [Description("核心平台软件服务")]
        CorePlatform = 10,

        /// <summary>
        /// 价外服务
        /// </summary>
        [Description("价外服务")]
        OutPrice = 11,
    }
    /// <summary>
    /// 返利类型
    /// </summary>

    public enum RebateTypeEnum
    {
        /// <summary>
        /// 平移返利
        /// </summary>
        [Description("平移返利")]
        PanningRebates = 4,

        /// <summary>
        /// 补偿返利
        /// </summary>
        [Description("补偿返利")]
        CompensationRebates = 5,


        /// <summary>
        /// 指标返利
        /// </summary>
        [Description("指标返利")]
        TargetsRebates = 6,

        /// <summary>
        /// 优惠返利
        /// </summary>
        [Description("优惠返利")]
        DiscountsRebates = 7,



    }
    public record class CustomerAddressOutput(
    Guid CustomerAddressId,
    string Address,
    string Contact,
    string ContactWay);

    public class BusinessDeptOutput
    {
        /// <summary>
        /// 原始值
        /// </summary>
        public List<string>? OrignValue { get; set; }
        /// <summary>
        /// 事业部短码，用于生成单号
        /// </summary>
        [MaxLength(250)]
        public string DeptShortName { get; set; }
        /// <summary>
        /// 选择路径
        /// </summary>
        public List<string>? Paths { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        public int? Id { get; set; }
        /// <summary>
        /// 显示名称
        /// </summary>
        [MaxLength(250)]
        public string Name { get; set; }
    }

    /// <summary>
    /// 发货状态
    /// </summary>
    public enum SendStatusEnum
    {
        /// <summary>
        /// 未发货
        /// </summary>
        [Description("未发货")]
        UnSended = 0,

        /// <summary>
        /// 部分发货
        /// </summary>
        [Description("部分发货")]
        PartialSended = 1,

        /// <summary>
        /// 已发货
        /// </summary>
        [Description("已发货")]
        Sended = 2,
    }
    /// <summary>
    /// 订单状态
    /// </summary>
    public enum SaleStatusEnum
    {
        /// <summary>
        /// 待提交
        /// </summary>
        [Description("待提交")]
        WaitSubmit = 0,

        /// <summary>
        /// 成本设定
        /// </summary>
        [Description("成本设定")]
        CostSetting = 5,

        /// <summary>
        /// 待收款
        /// </summary>
        [Description("待收款")]
        Receivable = 10,

        /// <summary>
        /// 审批中
        /// </summary>
        [Description("审批中")]
        Holding = 20,

        /// <summary>
        /// 已完成
        /// </summary>
        [Description("审批通过")]
        Finished = 99,


        /// <summary>
        /// 已驳回
        /// </summary>
        [Description("已驳回")]
        Rejected = 98,



        [Description("待收货")]  //细化准备中
        AllDeliver = 110,


        [Description("细化审核中")]
        DirectOrderHolding = 112,

        /// <summary>
        /// 收货待审核
        /// </summary>
        [Description("收货待审核")]
        HarvestPendingReview = 115,



        [Description("细化驳回")]
        DirectOrderRejected = 118,

        /// <summary>
        /// 已收货
        /// </summary>

        [Description("已收货")]
        Signed = 120,


    }

    public enum SaleTypeOutputEnum
    {
        /// <summary>
        /// 销售出库订单
        /// </summary>
        [Description("销售出库订单")]
        SaleOut = 1,

        /// <summary>
        /// 暂存核销订单
        /// </summary>
        [Description("暂存核销订单")]
        Temp = 4,

        /// <summary>
        /// 订单修订
        /// </summary>
        [Description("订单修订")]
        SaleRevise = 5,

        /// <summary>
        /// 跟台核销
        /// </summary>
        [Description("跟台核销")]
        StageSurgeryWriteOff = 6,

        /// <summary>
        /// 服务订单
        /// </summary>
        [Description("服务订单")]
        ServiceFee = 7,
    }

    public class SaleReviseDetailDto
    {
        public Guid Id { get; set; }

        /// <summary>
        /// 出库明细ID
        /// </summary>
        public Guid OutDetailId { get; set; }

        /// <summary>
        /// 销售订单明细ID
        /// </summary>
        public Guid SaleDetailId { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 业务单元id
        /// </summary>
        public Guid? BusinessUnitId { get; set; }

        /// <summary>
        /// 货号id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 货号名称
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public Guid? ProductNameId { get; set; }

        /// <summary>
        /// 所属厂家ID
        /// </summary>
        public Guid? ProducerId { get; set; }

        /// <summary>
        /// 所属厂家
        /// </summary>
        public string? ProducerName { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 四级产品分类
        /// </summary>
        public string? ProductTypeDesc { get; set; }

        /// <summary>
        /// 产品描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        public string? LotNo { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTimeOffset? ProduceDate { get; set; }

        /// <summary>
        /// 已用时间
        /// </summary>
        public DateTimeOffset? UseTime { get; set; }

        /// <summary>
        /// 有效期/失效期
        /// </summary>
        public DateTimeOffset? ValidDate { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public string? Sn { get; set; }

        /// <summary>
        /// 产品模式（0经销、1定率寄售）
        /// </summary>
        public MarkEnum? Mark { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 单位成本
        /// </summary>
        public decimal UnitCost { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 成本税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 销售税率
        /// </summary>
        public decimal? SalesTaxRate { get; set; }

        /// <summary>
        /// 暂存科室id
        /// </summary>
        public Guid? DeptId { get; set; }

        /// <summary>
        /// 暂存主键id
        /// </summary>
        public Guid? TempInventoryId { get; set; }

        /// <summary>
        /// 跟踪码
        /// </summary>
        public string? TraceCode { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Code
        /// </summary>
        public string? ProjectCode { get; set; }
    }
    public enum MarkEnum
    {
        /// <summary>
        /// 经销
        /// </summary>
        [Description("经销")]
        Normal = 0,

        /// <summary>
        /// 定率寄售
        /// </summary>
        [Description("定率寄售")]
        Park = 1,

        /// <summary>
        /// 集团定率寄售
        /// </summary>
        [Description("集团定率寄售")]
        GroupStoreIn = 2,

        /// <summary>
        /// 定价寄售
        /// </summary>
        [Description("定价寄售")]
        BulkPark = 3
    }
}
