﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using NPOI.OpenXmlFormats.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    public class CheckDataWithKingdeeService : ICheckDataWithKingdeeService
    {
        private IInventoryApiClient _inventoryApiClient;
        private ISginyApiClient _ginyApiClient;
        private IKingdeeApiClient _kingdeeApiClient;
        private IBDSApiClient _bDSApiClient;
        private IBaseAllQueryService<TempStoreToKingdeeLogPo> _tempStoreToKingdeeLogQuery;
        private IStoreInSelfOrderAppService _storeInSelfOrderAppService;
        private IStoreOutWithoutFinanceAppService _storeOutWithoutFinanceAppService;
        private ITempToSellAppService _tempToSellAppService;
        private ISellServiceFeeService _sellServiceFeeService;
        private ISellReviseAppService _sellReviseAppService;
        private IStoreOutSaleAppService _storeOutSaleAppService;
        private IConsignmentToPurchaseService _consignmentToPurchaseService;
        private IStoreInWithoutFinanceAppSerivce _storeInWithoutFinanceAppSerivce;
        private FinanceDbContext _db;

        public CheckDataWithKingdeeService(IInventoryApiClient inventoryApiClient, ISginyApiClient sginyApiClient, IKingdeeApiClient kingdeeApiClient, IBDSApiClient bDSApiClient, IBaseAllQueryService<TempStoreToKingdeeLogPo> tempStoreToKingdeeLogQuery,
            IStoreInSelfOrderAppService storeInSelfOrderAppService,
            IStoreOutWithoutFinanceAppService storeOutWithoutFinanceAppService,
            ITempToSellAppService tempToSellAppService,
            ISellServiceFeeService sellServiceFeeService,
            ISellReviseAppService sellReviseAppService,
            IStoreOutSaleAppService storeOutSaleAppService,
            IConsignmentToPurchaseService consignmentToPurchaseService,
            IStoreInWithoutFinanceAppSerivce storeInWithoutFinanceAppSerivce,
            FinanceDbContext db)
        {
            _inventoryApiClient = inventoryApiClient;
            _ginyApiClient = sginyApiClient;
            _kingdeeApiClient = kingdeeApiClient;
            _bDSApiClient = bDSApiClient;
            _tempStoreToKingdeeLogQuery = tempStoreToKingdeeLogQuery;
            _storeInSelfOrderAppService = storeInSelfOrderAppService;
            _storeOutWithoutFinanceAppService = storeOutWithoutFinanceAppService;
            _tempToSellAppService = tempToSellAppService;
            _sellServiceFeeService = sellServiceFeeService;
            _sellReviseAppService = sellReviseAppService;
            _storeOutSaleAppService = storeOutSaleAppService;
            _consignmentToPurchaseService = consignmentToPurchaseService;
            _storeInWithoutFinanceAppSerivce = storeInWithoutFinanceAppSerivce;
            _db = db;
        }

        

        public async Task<BaseResponseData<CheckDataWithKingdeeRes>> GetTempStoreOutDataAsync(CheckDataWithKingdeeInputDto input)
        {
            var stageryData = await _ginyApiClient.QueryStageSurgeryToKingdee(input);
            var tempStoreOutData = await _inventoryApiClient.QueryTempStoreOutToKingdee(input);
            var company = await _bDSApiClient.GetCompanyInfoAsync(new CompetenceCenter.BDSCenter.BDSBaseInput() { ids = new List<string>() { input.CompanyId.ToString() } });

            var q = new CheckDataWithKingdeeInputDtoForKingdee()
            {
                endDate = input.EndTime,
                startDate = input.StartTime,
                CompanyId = new List<string>() { company.FirstOrDefault().nameCode }
            };
            var kingdeeData = await _kingdeeApiClient.QueryKingdeeTempStoreOutData(q);
            var res = new CheckDataWithKingdeeRes();
            if (kingdeeData.Code == CodeStatusEnum.Success)
            {
                res.KingdeeData = kingdeeData.Data.FirstOrDefault();
                if (res.KingdeeData.CodeList == null)
                {
                    res.KingdeeData.CodeList = new List<CodeList>();
                }
            }
            else
            {
                return new BaseResponseData<CheckDataWithKingdeeRes>() { Code = CodeStatusEnum.Failed, Message = "获取金蝶数据报错：" + kingdeeData.Message };
            }
            res.CoreSysData = new CheckDataWithKingdeeOutputDto() { CodeList = new List<CodeList>(), TotalAmount = 0 };
            if (stageryData != null)
            {
                res.CoreSysData.CompanyName = stageryData.CompanyName;
                res.CoreSysData.TotalAmount = stageryData.TotalAmount;
                res.CoreSysData.CompanyId = stageryData.CompanyId;
                res.CoreSysData.CodeList = stageryData.CodeList;
            }
            if (tempStoreOutData != null)
            {
                if (!string.IsNullOrEmpty(res.CoreSysData.CompanyId))
                {
                    res.CoreSysData.CompanyName = tempStoreOutData.CompanyName;
                    res.CoreSysData.CompanyId = tempStoreOutData.CompanyId;
                }
                res.CoreSysData.TotalAmount += tempStoreOutData.TotalAmount;
                res.CoreSysData.CodeList.AddRange(tempStoreOutData.CodeList);
            }
            res.CoreSysData.CodeList = res.CoreSysData.CodeList.OrderByDescending(p => p.Code).ToList();
            if (res.KingdeeData != null && res.KingdeeData.CodeList != null)
            {
                res.KingdeeData.CodeList = res.KingdeeData.CodeList.OrderByDescending(p => p.Code).ToList();
            }
            res.CoreSysData.CodeList.ForEach(p =>
            {
                p.Amount = Math.Round(p.Amount, 2);
                var kingdeeCode = res.KingdeeData.CodeList.FirstOrDefault(t => t.Code == p.Code);
                if (kingdeeCode == null)
                {
                    res.CoreSysMore.Add(p.Code);
                }
            });
            res.CoreSysData.TotalAmount = Math.Round(res.CoreSysData.TotalAmount, 2);
            res.KingdeeData.CodeList.ForEach(p =>
            {
                var coreCode = res.CoreSysData.CodeList.FirstOrDefault(t => t.Code == p.Code);
                if (coreCode == null)
                {
                    res.KingdeeMore.Add(p.Code);
                }
            });
            return new BaseResponseData<CheckDataWithKingdeeRes>() { Code = CodeStatusEnum.Success, Data = res, Message = "获取成功" };
        }


        public async Task<BaseResponseData<List<CheckByBillOutput>>> CheckBillByBill(CheckByBillInput checkByBillInput)
        {
            var c = $"-{checkByBillInput.CompanyCode}-";

            var query = _db.TempStoreTokKngdeeLogs.Where(p => p.Code.Contains(c)).AsNoTracking();
            if (checkByBillInput.StartDate.HasValue && checkByBillInput.EndDate.HasValue)
            {
                query = query.Where(p => p.CreatedTime >= checkByBillInput.StartDate && p.CreatedTime <= checkByBillInput.EndDate);
            }
            if (!string.IsNullOrEmpty(checkByBillInput.BillCode))
            {
                query = query.Where(p => p.Code == checkByBillInput.BillCode);
            }
            var s = query.ToQueryString();
            var data = await query.ToListAsync();
             var checkRes = new List<CheckByBillOutput>();
            foreach (var item in data) 
            {
                switch (item.Classify) 
                {
                    case "经销购货入库":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _storeInSelfOrderAppService.GetKindeeDebtParams(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs( Math.Abs(d.pricetaxtotal) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs( Math.Abs(d.amount2) - Math.Abs(kRes.rows[0].jfzx_hxamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应付含税总金额不一致或应付不含税总金额不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalCost = d.pricetaxtotal, NoTaxTotalAmount = d.amount2 }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                        }
                        break;
                    case "暂存出库":
                        {
                            try
                            {
                                var res = await _storeOutWithoutFinanceAppService.GetTempStoreOutKingdeeParams(item.Code);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    var kRes = await _kingdeeApiClient.GetKingdeeTempStoreOutCheckData(item.Code, checkByBillInput.CompanyCode);
                                    if (Math.Abs(Math.Abs(res.Data.fk_jfzx_totalsalescost.Value) - Math.Abs(kRes.rows[0].jfzx_totalsalescost)) > checkByBillInput.AllowDiff)
                                    {
                                        checkRes.Add(new CheckByBillOutput()
                                        {
                                            BillCode = item.Code,
                                            KingdeeCode = item.Code,
                                            Match = 0,
                                            Desc = "暂存出库总金额不一致",
                                            KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxamount = kRes.rows[0].jfzx_totalsalescost },
                                            CoreSysOutput = new CheckByBillForCoreSysOutput() {  NoTaxTotalAmount = res.Data.fk_jfzx_totalsalescost.Value }
                                        });
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = ex.Message });
                            }
                        }
                        break;
                    case "暂存核销订单":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _tempToSellAppService.GetKingdeeTempSaleCreditParams(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCreditCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs(Math.Abs(d.jfzx_alltotalcost) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs( Math.Abs(Math.Abs(d.amount) - Math.Abs(kRes.rows[0].jfzx_hxamount))) > checkByBillInput.AllowDiff|| Math.Abs(Math.Abs(d.recamount) - Math.Abs(kRes.rows[0].jfzx_hxrecamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应收数据不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount, jfzx_hxtotalcost = kRes.rows[0].jfzx_hxtotalcost },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalAmount = d.recamount, NoTaxTotalAmount = d.amount, TotalCost = d.jfzx_alltotalcost }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "服务订单":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _sellServiceFeeService.GetServiceFeeKingdeeCreditParams(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCreditCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs(Math.Abs(d.jfzx_alltotalcost) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(Math.Abs(d.amount) - Math.Abs(kRes.rows[0].jfzx_hxamount))) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(d.recamount) - Math.Abs(kRes.rows[0].jfzx_hxrecamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应收数据不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount, jfzx_hxtotalcost = kRes.rows[0].jfzx_hxtotalcost },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalAmount = d.recamount, NoTaxTotalAmount = d.amount, TotalCost = d.jfzx_alltotalcost }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "寄售转购货":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _consignmentToPurchaseService.GetParkOrderKingdeeDebtParams(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs(Math.Abs(d.pricetaxtotal) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(d.amount2) - Math.Abs(kRes.rows[0].jfzx_hxamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应付含税总金额不一致或应付不含税总金额不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalCost = d.pricetaxtotal, NoTaxTotalAmount = d.amount2 }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "暂存调回":
                        {
                            try
                            {
                                var res = await _storeInWithoutFinanceAppSerivce.GetStoreInKingdeeParams(item.Code);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    var kRes = await _kingdeeApiClient.GetKingdeeTempStoreInCheckData(item.Code, checkByBillInput.CompanyCode);
                                    if (Math.Abs(Math.Abs(res.Data.fk_jfzx_totalsalescost.Value) - Math.Abs(kRes.rows[0].jfzx_totalsalescost)) > checkByBillInput.AllowDiff)
                                    {
                                        checkRes.Add(new CheckByBillOutput()
                                        {
                                            BillCode = item.Code,
                                            KingdeeCode = item.Code,
                                            Match = 0,
                                            Desc = "暂存出库总金额不一致",
                                            KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxamount = kRes.rows[0].jfzx_totalsalescost },
                                            CoreSysOutput = new CheckByBillForCoreSysOutput() { NoTaxTotalAmount = res.Data.fk_jfzx_totalsalescost.Value }
                                        });
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "寄售入库":
                        {
                            try
                            {
                                var res = await _storeInWithoutFinanceAppSerivce.GetStoreInKingdeeParams(item.Code);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    var kRes = await _kingdeeApiClient.GetKingdeeTempStoreInCheckData(item.Code, checkByBillInput.CompanyCode);
                                    if (Math.Abs(Math.Abs(res.Data.fk_jfzx_totalsalescost.Value) - Math.Abs(kRes.rows[0].jfzx_totalsalescost)) > checkByBillInput.AllowDiff)
                                    {
                                        checkRes.Add(new CheckByBillOutput()
                                        {
                                            BillCode = item.Code,
                                            KingdeeCode = item.Code,
                                            Match = 0,
                                            Desc = "暂存出库总金额不一致",
                                            KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxamount = kRes.rows[0].jfzx_totalsalescost },
                                            CoreSysOutput = new CheckByBillForCoreSysOutput() { NoTaxTotalAmount = res.Data.fk_jfzx_totalsalescost.Value }
                                        });
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "销售出库":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _storeOutSaleAppService.GetSaleStoreOutKindeeCreditParam(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCreditCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs(Math.Abs(d.jfzx_alltotalcost) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(Math.Abs(d.amount) - Math.Abs(kRes.rows[0].jfzx_hxamount))) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(d.recamount) - Math.Abs(kRes.rows[0].jfzx_hxrecamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应收数据不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount, jfzx_hxtotalcost = kRes.rows[0].jfzx_hxtotalcost },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalAmount = d.recamount, NoTaxTotalAmount = d.amount, TotalCost = d.jfzx_alltotalcost }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "寄售调出":
                        {
                            try
                            {
                                var res = await _storeOutWithoutFinanceAppService.GetTempStoreOutKingdeeParams(item.Code);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    var kRes = await _kingdeeApiClient.GetKingdeeTempStoreOutCheckData(item.Code, checkByBillInput.CompanyCode);
                                    if (Math.Abs(Math.Abs(res.Data.fk_jfzx_totalsalescost.Value) - Math.Abs(kRes.rows[0].jfzx_totalsalescost)) > checkByBillInput.AllowDiff)
                                    {
                                        checkRes.Add(new CheckByBillOutput()
                                        {
                                            BillCode = item.Code,
                                            KingdeeCode = item.Code,
                                            Match = 0,
                                            Desc = "暂存出库总金额不一致",
                                            KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxamount = kRes.rows[0].jfzx_totalsalescost },
                                            CoreSysOutput = new CheckByBillForCoreSysOutput() { NoTaxTotalAmount = res.Data.fk_jfzx_totalsalescost.Value }
                                        });
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = item.Code, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "订单修订":
                        {
                            var input = Newtonsoft.Json.JsonConvert.DeserializeObject<EventBusDTO>(item.PreRequestBody);
                            try
                            {
                                var res = await _sellReviseAppService.GetSaleReviseKingdeeCreditParams(input);
                                if (res.Code == CodeStatusEnum.Success)
                                {
                                    foreach (var d in res.Data)
                                    {
                                        var kRes = await _kingdeeApiClient.GetKingdeeCreditCheckData(d.billno, checkByBillInput.CompanyCode);
                                        if (Math.Abs(Math.Abs(d.jfzx_alltotalcost) - Math.Abs(kRes.rows[0].jfzx_hxpricetaxtotal)) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(Math.Abs(d.amount) - Math.Abs(kRes.rows[0].jfzx_hxamount))) > checkByBillInput.AllowDiff || Math.Abs(Math.Abs(d.recamount) - Math.Abs(kRes.rows[0].jfzx_hxrecamount)) > checkByBillInput.AllowDiff)
                                        {
                                            checkRes.Add(new CheckByBillOutput()
                                            {
                                                BillCode = d.billno,
                                                KingdeeCode = d.billno,
                                                Match = 0,
                                                Desc = "应收数据不一致",
                                                KingdeeOutput = new CheckByBillForKingdeeOutput() { jfzx_hxpricetaxtotal = kRes.rows[0].jfzx_hxpricetaxtotal, jfzx_hxamount = kRes.rows[0].jfzx_hxamount, jfzx_hxtotalcost = kRes.rows[0].jfzx_hxtotalcost },
                                                CoreSysOutput = new CheckByBillForCoreSysOutput() { TotalAmount = d.recamount, NoTaxTotalAmount = d.amount, TotalCost = d.jfzx_alltotalcost }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = res.Message });
                                }
                            }
                            catch (Exception ex)
                            {
                                checkRes.Add(new CheckByBillOutput() { BillCode = input.BusinessCode, Match = 0, Desc = ex.Message });
                            }
                            break;
                        }
                    case "退货出库":
                    case "经销购货修订":
                    case "跟台核销":
                    case "销售调回":
                    default:
                        continue;
                }
            }
            return new BaseResponseData<List<CheckByBillOutput>>()
            {
                Code = CodeStatusEnum.Success,
                Data = checkRes
            };
        }
    }
}
