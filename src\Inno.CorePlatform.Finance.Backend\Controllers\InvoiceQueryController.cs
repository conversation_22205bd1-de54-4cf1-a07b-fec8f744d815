﻿using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.DTO;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using OfficeOpenXml;
using static Inno.CorePlatform.Finance.Application.QueryServices.Inputs.BatchDownLoadInvoiceInput;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 发票查询
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class InvoiceQueryController : BaseController
    {
        private readonly IInvoiceQueryService _invoiceQueryService;
        private readonly IInvoiceCreditAppService _invoiceCreditAppService;
        private readonly ILogger<InvoiceQueryController> _logger;
        private readonly IPCApiClient _pCApiClient;
        private readonly DaprClient _daprClient;
        private readonly IEasyCachingProvider _easyCaching;

        /// <summary>
        /// 发票查询
        /// </summary>
        public InvoiceQueryController(
            IInvoiceQueryService invoiceQueryService,
            IInvoiceCreditAppService invoiceCreditAppService,
            ILogger<InvoiceQueryController> logger,
            IPCApiClient pCApiClient,
            IEasyCachingProvider easyCaching,
            DaprClient daprClient, ISubLogService subLog) : base(subLog)
        {
            this._invoiceQueryService = invoiceQueryService;
            this._logger = logger;
            this._pCApiClient = pCApiClient;
            this._invoiceCreditAppService = invoiceCreditAppService;
            this._daprClient = daprClient;
            _easyCaching = easyCaching;
        }

        /// <summary>
        /// 跟应收单获取发票列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<InvoiceQueryListOutput>> GetList([FromBody] InvoiceQueryInput query)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceQueryService.GetListAsync(query);
                return new ResponseData<InvoiceQueryListOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceQueryListOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取发票清单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInvoices")]
        public async Task<BaseResponseData<InvoicesPageResponse>> GetInvoices([FromBody] InvoicesQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                var (list, count) = await _invoiceQueryService.GetInvoiceListAsync(query);
                var invoiceAmountOutput = await _invoiceQueryService.GetInvoiceAmountAsync(query);
                return new BaseResponseData<InvoicesPageResponse>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new InvoicesPageResponse()
                    {
                        List = list,
                        Total = count,
                        TotalInvoiceAmount = invoiceAmountOutput.TotalInvoiceAmount,
                        TotalUWOAmount = invoiceAmountOutput.TotalUWOAmount,
                        TotalAWOAmount = invoiceAmountOutput.TotalAWOAmount,
                    },
                    Total = count,
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据应收单号获取冲销记录
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetAbatementByCreditBillCode")]
        public async Task<BaseResponseData<PageResponse<AbatementByCreditBillCodeQueryOutput>>> GetAbatementByCreditBillCode([FromBody] AbamentByCreditBillCodeQueryInput query)
        {
            try
            {
                var (list, count) = await _invoiceQueryService.GetAbatementByCreditBillCode(query);
                return new BaseResponseData<PageResponse<AbatementByCreditBillCodeQueryOutput>>
                {
                    Code = CodeStatusEnum.Success,
                    Data = new PageResponse<AbatementByCreditBillCodeQueryOutput>() { List = list, Total = count },
                    Total = count
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 打包发送发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("SendEmailInvoices")]
        public async Task<BaseResponseData<string>> SendEmailInvoices([FromBody] InvoicesQueryInput query)
        {
            if (string.IsNullOrEmpty(CurrentUser.UserName))
            {
                throw new ApplicationException("获取当前用户名为空，请先登录！");
            }
            if (query.CompanyId.HasValue)
            {
                query.CompanyIdList = new List<Guid>() { query.CompanyId.Value };
            }
            if (query.CompanyIdList == null || query.CompanyIdList.Count == 0)
            {
                throw new ApplicationException("请选择公司");
            }
            if (string.IsNullOrEmpty(query.CustomerEmail))
            {
                throw new ApplicationException("请填邮箱");
            }

            try
            {
                var jsonStr = JsonConvert.SerializeObject(query);
                _logger.LogWarning($"批量打包下载发票参数：" + jsonStr);
                query.UserId = CurrentUser.Id.Value;
                var (data, _) = await _invoiceQueryService.GetInvoiceListAsync(query, true);
                var batchDownloadInvoice = new BatchDownLoadInvoiceInput()
                {
                    UserName = CurrentUser.UserName,
                    TrueName = CurrentUser.Name,
                    CustomerEmail = query.CustomerEmail,
                    billDateBeging = query.billDateBeging,
                    billDateEnd = query.billDateEnd,
                    CompanyIdList = query.CompanyIdList,
                    CompanyInvoices = data?.Select(x => new BatchDownLoadInvoiceCompanyInput() { CompanyId = x.CompanyId, InvoiceNo = x.InvoiceNo }).ToList() ?? new List<BatchDownLoadInvoiceCompanyInput>(),
                };
                await _daprClient.PublishEventAsync<BatchDownLoadInvoiceInput>(DomainConstants.Default_PubSubName, DomainConstants.Batch_Download_Invoice, batchDownloadInvoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量打包下载发票失败");
            }

            return new BaseResponseData<string> { Message = "ok", Data = "ok" };
        }

        /// <summary>
        /// 获取SPD发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetSPDTabCount")]
        public async Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSPDTabCount([FromBody] InvoicesQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                return await _invoiceQueryService.GetSPDTabCount(query);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取阳采发票清单页签数量
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetSunPurchaseTabCount")]
        public async Task<BaseResponseData<SPDInvoiceListTabOutput>> GetSunPurchaseTabCount([FromBody] InvoicesQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                return await _invoiceQueryService.GetSunPurchaseTabCount(query);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 推送给spd
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("PushSPD")]
        public async Task<BaseResponseData<string>> PushSPD([FromBody] PushSPDInvoicesInput input)
        {
            var ret = await _invoiceCreditAppService.PushInvoiceToSPD(input);
            return ret;
        }


        /// <summary>
        /// 更换发票应收关系
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ChangeRelationship")]
        public async Task<BaseResponseData<string>> ChangeRelationship([FromBody] ChangeRelationshipInput input)
        {
            var cachekey = "changeRelationship" + input.InvoiceNo;
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    input.UserName = CurrentUser.UserName;
                    var jsonStr = JsonConvert.SerializeObject(input);
                    await _easyCaching.SetAsync<string>(cachekey, jsonStr, TimeSpan.FromSeconds(5));
                    var ret = await _invoiceCreditAppService.ChangeRelationship(input);
                    _easyCaching.Remove(cachekey);
                    return ret;
                }
                return BaseResponseData<string>.Failed(500, "已存在同等请求，请稍后再试");
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                return BaseResponseData<string>.Failed(500, ex.Message);
            }
        }

        /// <summary>
        /// 原应收校验
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetWriteOffCreditBillCode")]
        public async Task<BaseResponseData<string>> GetWriteOffCreditBillCode([FromBody] ChangeRelationshipInput input)
        {
            var ret = await _invoiceCreditAppService.GetWriteOffCreditBillCode(input.OldCreditBillCode);
            if (ret.Data != null && ret.Data.Any())
            {
                var str = string.Join(",", ret.Data.Select(x => x.Id).ToList());
                return BaseResponseData<string>.Success(str);
            }
            return BaseResponseData<string>.Failed(500, $"原应收单号{input.OldCreditBillCode}未被冲销，请先用负数应收冲销掉原应收");
        }

        /// <summary>
        /// 查询发票是否已认款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetRecognizeReceiveDetailByInvoiceNos")]
        public async Task<BaseResponseData<string>> GetRecognizeReceiveDetailByInvoiceNos([FromBody] ChangeRelationshipInput input)
        {
            var invoices = new List<string>();
            invoices.Add(input.InvoiceNo);
            return await _invoiceCreditAppService.GetRecognizeReceiveDetailByInvoiceNos(invoices);
        }

        /// <summary>
        /// 忽略阳采发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("IgnoreSunPurchaseInvoice")]
        public async Task<BaseResponseData<string>> IgnoreSunPurchaseInvoice([FromBody] InvoicesQueryInput query)
        {
            return await _invoiceQueryService.IgnoreSunPurchaseInvoice(query);
        }

        /// <summary>
        /// 同步阳采发票状态
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("SyncSunPurchaseInvoiceStatus")]
        public async Task<BaseResponseData<string>> SyncSunPurchaseInvoiceStatus([FromBody] InvoicesQueryInput query)
        {
            return await _invoiceQueryService.SyncSunPurchaseInvoiceStatus(query);
        }


        /// <summary>
        /// 导出发票清单
        /// </summary>
        /// <returns></returns>
        [HttpPost("ExportInvoices")]
        public async Task<IActionResult> ExportInvoices([FromBody] InvoicesQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                var InvoicesExportListOutputlist = new List<InvoicesExportListOutput>();
                var (list, count) = await _invoiceQueryService.GetInvoiceListAsync(query, true);
                var input = new SPDInvoiceQueryInput()
                {
                    InvoiceNos = list.Where(p => !string.IsNullOrEmpty(p.InvoiceNo)).Select(p => p.InvoiceNo).Distinct().ToList(),
                    CompanyIds = list.Where(p => p.CompanyId.HasValue).Select(p => p.CompanyId.ToString()).Distinct().ToList(),
                };
                var (invoiceCrdits, cnt) = await _invoiceQueryService.GetInvoiceCredits(input);
                foreach (var item in list)
                {
                    var invoicesSingle = item.Adapt<InvoicesExportListOutput>();
                    if (item.CompanyId.HasValue && !string.IsNullOrEmpty(item.InvoiceNo))
                    {
                        var details = invoiceCrdits.Where(x => x.InvoiceNo == item.InvoiceNo && x.CompanyId == item.CompanyId).ToList();
                        invoicesSingle.Details = details;
                    }
                    InvoicesExportListOutputlist.Add(invoicesSingle);
                }
                //InvoicesExportListOutputlist = InvoicesExportListOutputlist.OrderByDescending(x => x.InvoiceTime).ToList();
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.Cells[1, 1].Value = "发票号";
                    worksheet.Cells[1, 2].Value = "发票代码";
                    worksheet.Cells[1, 3].Value = "开票日期";
                    worksheet.Cells[1, 4].Value = "发票金额";
                    worksheet.Cells[1, 5].Value = "客户";
                    worksheet.Cells[1, 6].Value = "公司";
                    worksheet.Cells[1, 7].Value = "供应商";
                    worksheet.Cells[1, 8].Value = "厂家";
                    worksheet.Cells[1, 9].Value = "状态";

                    worksheet.Cells[1, 10].Value = "红冲状态";
                    worksheet.Cells[1, 11].Value = "红/蓝发票号";
                    worksheet.Cells[1, 12].Value = "应收单号";
                    worksheet.Cells[1, 13].Value = "应收日期";
                    worksheet.Cells[1, 14].Value = "订单号";
                    worksheet.Cells[1, 15].Value = "应收金额";
                    worksheet.Cells[1, 16].Value = "业务单元";
                    worksheet.Cells[1, 17].Value = "项目名称";
                    worksheet.Cells[1, 18].Value = "项目编码";
                    worksheet.Cells[1, 19].Value = "备注";

                    if (InvoicesExportListOutputlist.Any())
                    {
                        int row = 2;
                        foreach (var item in InvoicesExportListOutputlist)
                        {
                            worksheet.Cells[row, 1].Value = item.InvoiceNo;
                            worksheet.Cells[row, 2].Value = item.InvoiceCode;
                            worksheet.Cells[row, 3].Value = item.InvoiceTime?.ToString("yyyy-MM-dd") ?? string.Empty;
                            worksheet.Cells[row, 4].Value = Convert.ToDouble(item.InvoiceAmount);
                            worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0.00";
                            worksheet.Cells[row, 5].Value = item.CustomerName;
                            worksheet.Cells[row, 6].Value = item.CompanyName;
                            worksheet.Cells[row, 7].Value = item.AgentName;
                            worksheet.Cells[row, 8].Value = item.ProducerName;
                            worksheet.Cells[row, 9].Value = item.StatusStr;
                            worksheet.Cells[row, 10].Value = item.ChangedStatusStr;
                            worksheet.Cells[row, 11].Value = item.BlueInvoiceNo;
                            worksheet.Cells[row, 19].Value = item.Remark;
                            if (item.Details != null && item.Details.Any())
                            {
                                foreach (var detail in item.Details)
                                {
                                    worksheet.Cells[row, 12].Value = detail.BillCode;
                                    worksheet.Cells[row, 13].Value = detail.BillDate?.ToString("yyyy-MM-dd") ?? string.Empty;
                                    worksheet.Cells[row, 14].Value = detail.OrderNo;
                                    worksheet.Cells[row, 15].Value = detail.CreditAmount.HasValue ? decimal.Parse(detail.CreditAmount.Value.ToString("#0.00")) : 0;
                                    worksheet.Cells[row, 15].Style.Numberformat.Format = "#,##0.00";
                                    worksheet.Cells[row, 16].Value = detail.ServiceName;
                                    worksheet.Cells[row, 17].Value = detail.ProjectName;
                                    worksheet.Cells[row, 18].Value = detail.ProjectCode;
                                    row++;
                                }
                            }
                            else
                            {
                                row++;
                            }
                        }
                    }
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"出现错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据发票号获取应收列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInvoiceCreditsByInvoiceNo")]
        public async Task<ResponseData<InvoiceCreditsOutput>> GetInvoiceCreditsByInvoiceNo([FromBody] InvoiceQueryInput query)
        {
            try
            {
                // 发票单号、公司id任一为空则不查询
                if (string.IsNullOrEmpty(query.InvoiceNo) || string.IsNullOrEmpty(query.CompanyId))
                {
                    return new ResponseData<InvoiceCreditsOutput>
                    {
                        Data = new Data<InvoiceCreditsOutput>
                        {
                            List = new List<InvoiceCreditsOutput>(),
                            Total = 0
                        },
                    };
                }
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceQueryService.GetInvoiceCreditsByInvoiceNo(query);
                return new ResponseData<InvoiceCreditsOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceCreditsOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据发票号获取应收列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetInvoiceCreditsSumByInvoiceNo")]
        public async Task<BaseResponseData<InvoiceCreditsSumOutput>> GetInvoiceCreditsSumByInvoiceNo([FromBody] InvoiceQueryInput query)
        {
            try
            {
                var ret = BaseResponseData<InvoiceCreditsSumOutput>.Success("操作成功");
                // 发票单号、公司id任一为空则不查询
                if (string.IsNullOrEmpty(query.InvoiceNo) || string.IsNullOrEmpty(query.CompanyId))
                {
                    return ret;
                }
                var uid = CurrentUser.Id;
                var sum = await _invoiceQueryService.GetInvoiceCreditsSumByInvoiceNo(query);
                ret.Data = sum;
                return ret;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据发票号获取应收列表（SPD版）
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetSPDInvoiceCreditsByInvoiceNo")]
        public async Task<ResponseData<InvoiceCreditsOutput>> GetSPDInvoiceCreditsByInvoiceNo([FromBody] SPDInvoiceQueryInput query)
        {
            try
            {
                // 发票单号、公司id任一为空则不查询
                if (query.InvoiceNos == null || query.CompanyIds == null || !query.InvoiceNos.Any() || !query.CompanyIds.Any())
                {
                    return new ResponseData<InvoiceCreditsOutput>
                    {
                        Data = new Data<InvoiceCreditsOutput>
                        {
                            List = new List<InvoiceCreditsOutput>(),
                            Total = 0
                        },
                    };
                }
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceQueryService.GetSPDInvoiceCreditsByInvoiceNo(query);
                return new ResponseData<InvoiceCreditsOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceCreditsOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 根据发票号获取阳采订单详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetSunPurchaseInvoiceDetailByInvoiceNo")]
        public async Task<ResponseData<SunPurchaseInvoiceDetailsOutput>> GetSunPurchaseInvoiceDetailByInvoiceNo([FromBody] InvoicesQueryInput query)
        {
            try
            {
                // 发票单号为空则不查询
                if (query.InvoiceNo == null)
                {
                    return new ResponseData<SunPurchaseInvoiceDetailsOutput>
                    {
                        Data = new Data<SunPurchaseInvoiceDetailsOutput>
                        {
                            List = new List<SunPurchaseInvoiceDetailsOutput>(),
                            Total = 0
                        },
                    };
                }
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceQueryService.GetSunPurchaseInvoiceDetailByInvoiceNo(query);
                return new ResponseData<SunPurchaseInvoiceDetailsOutput>
                {
                    Code = 200,
                    Data = new Data<SunPurchaseInvoiceDetailsOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 编辑阳采发票详情
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("EditSunPurchaseInvoiceDetail")]
        public async Task<BaseResponseData<string>> EditSunPurchaseInvoiceDetail([FromBody] EditSunPurchaseInvoiceDetailInput query)
        {
            return await _invoiceQueryService.EditSunPurchaseInvoiceDetail(query);
        }

        /// <summary>
        /// 提交阳采发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("SubmitSunPurchaseInvoice")]
        public async Task<BaseResponseData<string>> SubmitSunPurchaseInvoice([FromBody] SunPurchaseInvoiceSubmitInput query)
        {
            query.updatedBy = CurrentUser.UserName;
            return await _invoiceQueryService.SubmitSunPurchaseInvoice(query);
        }

        /// <summary>
        /// 获取阳采发票配送编码
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetPsdByPurchaseCodeForPm")]
        public async Task<BaseResponseData<SunPurchaseInvoiceInfoForPmInput>> GetPsdByPurchaseCodeForPm([FromBody] SunPurchaseInvoiceSubmitInput query)
        {
            return await _invoiceQueryService.GetPsdByPurchaseCodeForPm(query);
        }

        /// <summary>
        /// 获取可入账的发票列表
        /// </summary>
        /// <returns></returns>
        [HttpPost("GetReceiptInvoiceList")]
        public async Task<ResponseData<InvoiceReceiptDetailQueryOutput>> GetReceiptInvoiceList([FromBody] InvoiceReceiptDetailQueryInput input)
        {
            try
            {
                var uid = CurrentUser.Id;
                var (list, count) = await _invoiceQueryService.GetReceiptInvoiceList(input);
                return new ResponseData<InvoiceReceiptDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<InvoiceReceiptDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 获取阳采配送单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("SunPurchaseGetFullDetails")]
        public async Task<BaseResponseData<List<ShycShipmentDetailOutput>>> SunPurchaseGetFullDetails([FromBody] ShycShipmentDetaillnput query)
        {
            return await _invoiceQueryService.SunPurchaseGetFullDetails(query);
        }

        /// <summary>
        /// 预开票发票绑定应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CreatePreInvoiceCredit")]
        public async Task<BaseResponseData<int>> CreatePreInvoiceCredit([FromBody] PreInvoiceCreditInput input)
        {
            var cacheKey = $"CreatePreInvoiceCredit_{input.InvoiceNo}";
            var cachedResult = await _easyCaching.GetAsync<string>(cacheKey);
            try
            {
                if (cachedResult != null && cachedResult.Value != null)
                {
                    return BaseResponseData<int>.Failed(500, "操作失败，原因：请勿重复操作，稍后5s后操作");
                }
                else
                {   // 设置缓存，有效期为5秒
                    await _easyCaching.SetAsync(cacheKey, input.InvoiceNo, TimeSpan.FromSeconds(5));
                    input.UserName = CurrentUser.UserName;
                    var ret = await _invoiceCreditAppService.CreatePreInvoiceCredit(input);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        _easyCaching.Remove(cacheKey);
                    }
                    return ret;
                }
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cacheKey);
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 重推SPD商务平台（仅限贵州致思蒲达企业管理有限公司的纸质专用发票）
        /// </summary>
        /// <param name="invoiceNos"></param>
        /// <returns></returns>
        [HttpPost("RefreshPushSPD")]
        public async Task<BaseResponseData<int>> RefreshPushSPD(List<string?> invoiceNos)
        {
            var ret = await _invoiceCreditAppService.RefreshPushSPD(invoiceNos);
            return ret;
        }

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("ExportInvoicesByCoordinate")]
        public async Task<BaseResponseData<List<ExportTaskResDto>>> ExportInvoicesByCoordinate([FromBody] InvoicesQueryInput query)
        {
            query.page = 1;
            query.limit = 20;
            query.UserId = CurrentUser.Id.Value;
            query.CurrentUserName = CurrentUser.UserName;//
            return await _invoiceCreditAppService.ExportInvoicesByCoordinate(query);
        }
    }
}

