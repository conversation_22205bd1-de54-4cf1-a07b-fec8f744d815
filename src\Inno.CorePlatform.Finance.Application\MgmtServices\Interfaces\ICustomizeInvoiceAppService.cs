﻿
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Interfaces
{
    /// <summary>
    /// 运营制作开票管理
    /// </summary>
    public interface ICustomizeInvoiceAppService
    {
        /// <summary>
        /// 保存开票明细（合并后）
        /// </summary>
        Task<(int, string)> SaveCustomizeInvoice(SaveCustomizeInvoiceInput input);

        /// <summary>
        /// 删除开票单
        /// </summary>
        Task<(int, string)> DeleteCustomizeInvoice(DeleteCustomizeInvoiceInput input);
        /// <summary>
        /// 编辑开票单
        /// </summary>
        Task<(int, string)> EditCustomizeInvoice(EditCustomizeInvoiceInput input);
        /// <summary>
        /// 提交开票单
        /// </summary>
        Task<(int, string)> SubmitCustomizeInvoice(SubmitCustomizeInvoiceInput input);
        /// <summary>
        /// 撤回开票单
        /// </summary>
        Task<(int, string)> RecallCustomizeInvoice(RecallCustomizeInvoiceInput input);

        /// <summary>
        /// 保存开票单明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(int, string)> SaveCustomizeInvoiceDetail(SaveCustomizeInvoiceDetailInput input);

        /// <summary>
        /// 设置为另一个开票单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<(int, string)> SetAsAnotherInvoice(SetAsAnotherInvoiceInput input);

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> Approve(KingdeeCustomizeInvoiceInput input);

        /// <summary>
        /// 上传附件
        /// </summary>
        Task<BaseResponseData<int>> AttachFileIds(CustomizeInvoiceAttachFileInput input);
        /// <summary>
        /// 上传结算清单附件
        /// </summary>
        Task<BaseResponseData<int>> AttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input);

        /// <summary>
        /// 删除附件
        /// </summary>
        Task<BaseResponseData<string>> DeleteAttachFileIds(CustomizeInvoiceAttachFileInput input);

        /// <summary>
        ///删除结算清单附件
        /// </summary>
        Task<BaseResponseData<string>> DeleteAttachFileIds_jsqd(CustomizeInvoiceAttachFileInput input);
        /// <summary>
        /// 新增折扣行
        /// </summary>
        Task<BaseResponseData<string>> Adddiscount(CustomizeInvoiceDetailInput input);

        /// <summary>
        /// 修改状态
        /// </summary>
        Task<BaseResponseData<string>> UpdateStatus(Guid id, CustomizeInvoiceStatusEnum statusEnum);

        Task<BaseResponseData<string>> SubmitCustomizeInvoiceToOA(SubmitCustomizeInvoiceInput input);

        Task<BaseResponseData<string>> AuditForOA(SubmitCustomizeInvoiceInput input);
        /// <summary>
        /// 开红冲发票
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> CreateOffsetInvoice(CreateOffsetInvoiceInput input);


        /// <summary>
        /// 拆分明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SplitSubmit(SplitSubmitInput input);
        Task<(int, string)> BatchSetAsAnotherInvoice(SetAsAnotherInvoiceInput input);
        /// <summary>
        /// 删除开票分类单列
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> DeleteCustomizeInvoiceClassify(CustomizeInvoiceClassifyInput input);
        Task<BaseResponseData<string>> SubmitByClassfiy(CustomizeInvoiceClassifyInput input);
        Task<BaseResponseData<List<customerInvoice>>> BeforeSubmitClassfiy(CustomizeInvoiceClassifyInput input);
        Task<BaseResponseData<int>> MargeByProductNamePriceOriginPrice(List<CustomizeInvoiceDetailOutput> input); 
        /// <summary>
        /// 一键生成服务费申开单
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="createdBy"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> GenerateServiceCustomizeInvoice(GenerateServiceCustomizeInvoiceInput input, string createdBy);
        /// <summary>
        /// 运营开票退回 - 金蝶
        /// </summary>
        /// <returns></returns>
        Task<BaseResponseData<int>> ReturnCustomizeInvoice(KindeeCustomizeInvoiceInput input);

        /// <summary>
        /// 同步税收分类编码
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SyncTaxTypeNo(SyncTaxTypeNoInput input);

        /// <summary>
        /// 设置为另一个开票分类
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SetAnotherCic(SetAnotherCicInput input);
        
        /// <summary>
        /// 合并开票单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> MergeCii(SetAnotherCicInput input);

        /// <summary>
        /// 开票明细导入
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="customizeInvoiceItemId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportCustomizeInvoiceDetail(Guid fileId, Guid customizeInvoiceItemId, string userName);

        /// <summary>
        /// 批量开票
        /// </summary>
        /// <param name="fileId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoicing(Guid fileId, string userName,Guid? userId);
        /// <summary>
        /// 批量编辑备注
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> EditBatchCustomizeInvoice(EditBatchCustomizeInvoiceInput input); 
        BaseResponseData<string> MargeCheck(List<OriginDetailOutput> details,int source);
        Task<BaseResponseData<CustomizeInvoiceDetailExcelOutput>> ExportBatchInvoiceDetail(Guid fileId, string userName, Guid? userId);
        Task<BaseResponseData<string>> CheckWDTMerge(CheckWDTMergeInput input); 
        Task<BaseResponseData<int>> MergeByDiscount(List<CustomizeInvoiceDetailOutput> input);
        Task<BaseResponseData<int>> MargeByProductNamePrice(List<CustomizeInvoiceDetailOutput> input);
        Task<BaseResponseData<int>> MargeByProductNamePriceSpecification(List<CustomizeInvoiceDetailOutput> input);
        Task<BaseResponseData<int>> MergeByInputQuantityPrice(List<CustomizeInvoiceDetailOutput> input, decimal quantity, decimal price);
        Task<BaseResponseData<int>> UpdateDetailQuantityPrice(MergeByInputQuantityPriceInput input);
        Task<BaseResponseData<int>> MargeByOriginSpecificationPriceOriginPrice(List<CustomizeInvoiceDetailOutput> input);
        Task<BaseResponseData<string>> SendEmailAsync(SendEmailInput input);
    }
}
