﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{
    public class CustomerDeptMetaInfoOutput
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }

    public class CustomerBankOutput
    {
        /// <summary>
        /// 
        /// </summary>
        public string? account { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? bank { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? bankCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? bankInfoId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? bankNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string? enterpriseId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? receiptId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? relationId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? swiftCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int? type { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? typeName { get; set; }

    }
    public class CustomerOutput
    {
        public string customerId { get; set; }
        public string customerName { get; set; }
        public string? customerType1Desc { get; set; }
        /// <summary>
        /// 0103 部队医院
        /// </summary>
        public string? customerType1 { get; set; }
        public string? latestUniCode { get; set; }
        public List<customerInvoice> customerInvoices { get; set; }
        public string? email { get; set; }
        /// <summary>
        /// 是否第三方回款客户
        /// </summary>
        public int isPaymentInstitution { get; set; }
        /// <summary>
        /// 是否第三方回款客户
        /// </summary>
        public string? isPaymentInstitutionDesc { get; set; }
    }

    public class customerInvoice
    {
        /// <summary>
        /// 是否开票主体 1是 0 否
        /// </summary>
        public int isInvoiceUnit { get; set; }

        /// <summary>
        /// 发票地址
        /// </summary>
        public string invoiceAddr { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        public string invoiceTel { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string invoiceBank { get; set; }

        /// <summary>
        /// 开户账号
        /// </summary>
        public string invoiceBankNo { get; set; }

        /// <summary>
        /// 税号
        /// </summary>
        public string invoiceCode { get; set; }

        /// <summary>
        /// 发票机构名称
        /// </summary>
        public string invoiceName { get; set; }

        /// <summary>
        /// 发票细类
        /// </summary>
        public string? salesInvoiceDetails { get; set; }

        /// <summary>
        /// 开票类型
        /// </summary>
        public string? salesInvoiceDetailsDesc { get; set; }
        /// <summary>
        /// 发票抬头，1：个人， 其它：公司
        /// </summary>
        public int? invoiceTitle { get; set; }

        /// <summary>
        /// 法人公司ids
        /// </summary>
        public string? companyIds { get; set; }

        /// <summary>
        /// 法人公司
        /// </summary>
        public string? companyNames { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string? customerId { get; set; }
    }

    public class companyInvoice
    {
        /// <summary>
        /// 税号
        /// </summary>
        public string? customerInvoiceCode { get; set; }

        /// <summary>
        /// 机构名称
        /// </summary>
        public string? unitName { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? customerInvoiceAddr { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        public string? customerInvoiceTel { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string? customerInvoiceBank { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string? customerInvoiceBankNo { get; set; }
    }
}
