﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 应收查询，出参
    /// </summary>
    public class CreditQueryListOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 应收id
        /// </summary>
        public Guid CreditId { get; set; }
        /// <summary>
        /// 冲销状态
        /// </summary>
        public AbatedStatusEnum? AbatedStatus { get; set; }

        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary> 
        public DateTime? BillDate { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string? NameCode { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public int? CreditType { get; set; }
        /// <summary>
        /// 应收类型
        /// </summary>
        public string? CreditTypeStr
        {
            get
            {
                string ret = string.Empty;
                if (CreditType != null)
                {
                    return ((CreditTypeEnum)CreditType).GetDescription();
                }
                return ret;
            }
        }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }


        /// <summary>
        /// 应收类型
        /// </summary>
        public int DebtorType { get; set; }

        /// <summary>
        /// 完成日期
        /// </summary>
        public DateTime? FinishDate { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public int? InvoiceStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Note { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; } = "none";

        /// <summary>
        /// 业务单元
        /// </summary>
        public Guid? ServiceId { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 应收值
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }


        ///// <summary>
        ///// 是否长期
        ///// </summary>
        //public int? IsLongTerm { get; set; }
        /// <summary>
        /// 是否确认收入
        /// </summary>
        public int? IsSureIncome { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal LeftAmount { get { return Math.Abs(Value) - AbatmentAmount; } }
        /// <summary>
        /// 损失确认金额
        /// </summary>
        public decimal? LossRecognitionValue { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public string? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 原始订单号
        /// </summary>
        public string? OriginOrderNo { get; set; }
        /// <summary>
        /// 已开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }
        /// <summary>
        /// 未开票金额
        /// </summary>
        public decimal NoInvoiceAmount { get { return Value - InvoiceAmount; } }
        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum? InvoiceType { get; set; }

        /// <summary>
        /// 是否无需开票,1=无需开票，0 or null=需要开票
        /// </summary> 
        public IsNoNeedInvoiceEnum? IsNoNeedInvoice { get; set; }

        /// <summary>
        /// 是否无需开票,1=无需开票，0 or null=需要开票
        /// </summary> 
        public int IsNoNeedInvoiceInt
        {
            get
            {
                return IsNoNeedInvoice.HasValue ? (int)IsNoNeedInvoice : 0;
            }
        }


        /// <summary>
        /// 销售子系统ID
        /// </summary>
        public Guid? SaleSystemId { get; set; }

        /// <summary>
        /// 销售子系统名称
        /// </summary>
        public string? SaleSystemName { get; set; }
        public SaleSourceEnum? SaleSource { get; set; }


        public string? HospitalId { get; set; }

        public string? HospitalName { get; set; }


        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum? SaleType { get; set; }

        /// <summary>
        /// 运输单号
        /// </summary>
        public string? ShipmentCode { get; set; }

        public string? DeptName { get; set; }

        /// <summary>
        /// 签收日期
        /// </summary> 
        public DateTime? IsSureIncomeDate { get; set; }

        /// <summary>
        /// 收入确认模式 有值并且值=1=分期生成
        /// </summary>
        public ServiceConfirmRevenuePlanModeEnum? ServiceConfirmRevenuePlanModeEnum { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        public string? CustomerOrderCode { get; set; }
        /// <summary>
        /// 订货人
        /// </summary>
        public string? CustomerPersonName { get; internal set; }

        /// <summary>
        /// 预计回款日期
        /// </summary>
        public DateTime? ProbablyBackTime { get; set; }
        /// <summary>
        /// 逾期天数
        /// </summary>
        public int? OverdueDay
        {
            get
            {
                return ProbablyBackTime.HasValue ? (ProbablyBackTime >= DateTime.Today || AbatedStatus == AbatedStatusEnum.Abated ? null : (int)(DateTime.Now - ProbablyBackTime).Value.TotalDays) : null;
            }
        }

        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        /// <summary>
        /// 采购成本金额
        /// </summary>
        public decimal? PurchaseCost { get; set; }

        /// <summary>
        /// 价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        /// 价格来源
        /// </summary>   
        public string? PriceSourceStr
        {
            get
            {
                return PriceSource.HasValue ? PriceSource.GetDescription() : string.Empty;
            }
        }

        public string? RedReversalConsumNo { get; set; }
        /// <summary>
        /// 是否包含销售账期（服务商客户端才有此字段）
        /// </summary>
        public bool? IsContainsAccountPeriodTypeBySale { get; set; } = false;
        /// <summary>
        /// 标记
        /// </summary>
        public int? Mark { get; set; }
    }
}
/// <summary>
/// 不同状态数量
/// </summary>
public class CreditQueryListTabOutput
{
    /// <summary>
    /// 全部
    /// </summary>
    public int AllCount { get; set; }
    /// <summary>
    /// 冲销数量
    /// </summary>
    public int AbatedCount { get; set; }
    /// <summary>
    /// 未冲销数量
    /// </summary>
    public int NonAbatedCount { get; set; }
}
/// <summary>
/// 应收明细查询，出参
/// </summary>
public class CreditDetailQueryListOutput
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; set; }
    /// <summary>
    /// 应付付款计划号
    /// </summary>
    public string Code { get; set; }


    /// <summary>
    /// 供应商Id
    /// </summary>  
    public Guid? AgentId { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>  
    public string? AgentName { get; set; }
    /// <summary>
    /// 是否需要付款
    /// </summary>
    public bool NeedGeneratePayment { get; set; }
    /// <summary>
    /// 是否付款
    /// </summary>
    public bool IsGeneratePayment { get; set; }
    /// <summary>
    /// 本次付款金额
    /// </summary>
    public decimal? Value { get; set; }
    /// <summary>
    ///  折扣
    /// </summary>
    public decimal? Discount { get; set; }
    /// <summary>
    /// 原始付款金额
    /// </summary>
    public decimal? OrginValue { get; set; }
    /// <summary>
    /// 业务单元
    /// </summary>

    public Guid? ServiceId { get; set; }
    public string? ServiceName { get; set; }

    /// <summary>
    /// 厂家
    /// </summary>
    public Guid? ProducerId { get; set; }
    public string? ProducerName { get; set; }
    /// <summary>
    /// 账号类型 0=回款账期 1=入库账期 2=销售账期 3=预付账期
    /// </summary>
    public AccountPeriodTypeEnum AccountPeriodType { get; set; }
}

/// <summary>
/// 应收确认收入，出参
/// </summary>
public class ConfirmReceiptOutput
{ }
