﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Gateway.Client;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CreditController : BaseController
    {
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;
        private readonly ICustomizeInvoiceAppService _customizeInvoiceAppService;
        private readonly ILogger<CustomizeInvoiceController> _logger;
        private readonly IFileGatewayClient _fileGatewayClient;
        private readonly ICreditAppService _creditAppService;
        /// <summary>
        /// 运营制作开票管理
        /// </summary>
        public CreditController(
            ICustomizeInvoiceQueryService customizeInvoiceQueryService,
            ICustomizeInvoiceAppService customizeInvoiceAppService,
            IFileGatewayClient fileGatewayClient,
            ICreditAppService creditAppService,
            ILogger<CustomizeInvoiceController> logger, ISubLogService subLog) : base(subLog)
        {
            this._fileGatewayClient = fileGatewayClient;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
            _customizeInvoiceAppService = customizeInvoiceAppService;
            _logger = logger;
            _creditAppService = creditAppService;
        }

        /// <summary>
        ///无需审批
        /// </summary>
        /// <returns></returns>
        [HttpPost("NoNeedInvoice")]
        public async Task<BaseResponseData<int>> NoNeedInvoice(NoNeedInvoiceInput input)
        {
            input.UserName= CurrentUser.UserName;
            return await _creditAppService.NoNeedInvoice(input);
        }
    }
}
