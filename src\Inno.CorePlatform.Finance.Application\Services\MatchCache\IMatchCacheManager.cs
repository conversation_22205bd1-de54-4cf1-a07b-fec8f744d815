﻿﻿using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using Inno.CorePlatform.Finance.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Services.MatchCache
{
    /// <summary>
    /// 匹配缓存管理器接口
    /// </summary>
    public interface IMatchCacheManager
    {
        /// <summary>
        /// 获取业务类型缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <returns>业务类型缓存数据</returns>
        List<MatchableDocumentItem> GetBusinessTypeCache(Guid mergeInputBillId, BusinessType businessType);

        /// <summary>
        /// 设置业务类型缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="data">缓存数据</param>
        void SetBusinessTypeCache(Guid mergeInputBillId, BusinessType businessType, List<MatchableDocumentItem> data);

        /// <summary>
        /// 获取所有缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>所有缓存数据</returns>
        Dictionary<BusinessType, List<MatchableDocumentItem>> GetAllCacheData(Guid mergeInputBillId);

        /// <summary>
        /// 清除所有缓存数据
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        void ClearAllCache(Guid mergeInputBillId);

        /// <summary>
        /// 获取匹配状态缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>匹配状态缓存</returns>
        Dictionary<BusinessType, bool> GetMatchStatusCache(Guid mergeInputBillId);

        /// <summary>
        /// 设置匹配状态缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="status">匹配状态</param>
        void SetMatchStatusCache(Guid mergeInputBillId, BusinessType businessType, bool status);


        /// <summary>
        /// 获取已匹配的明细
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>已匹配的明细</returns>
        List<MatchDetailItem> GetMatchedDetails(Guid mergeInputBillId);

        /// <summary>
        /// 将整个匹配结果存储到缓存中
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="result">匹配结果</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        void SaveMatchResultToCache(Guid mergeInputBillId, BusinessType businessType, Services.MatchLogic.MatchResult result, string? invoiceNumber = null);

        /// <summary>
        /// 从缓存中获取匹配结果
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="invoiceNumber">发票号（可选）</param>
        /// <returns>匹配结果，如果缓存中不存在则返回null</returns>
        Services.MatchLogic.MatchResult? GetMatchResultFromCache(Guid mergeInputBillId, BusinessType businessType, string? invoiceNumber = null);

        /// <summary>
        /// 根据条件查询缓存数据
        /// </summary>
        /// <param name="input">查询参数对象</param>
        /// <returns>符合条件的缓存数据</returns>
        List<MatchableDocumentItem> QueryBusinessTypeCache(QueryCacheParams input);

        /// <summary>
        /// 根据条件查询缓存数据（兼容旧版本）
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="businessCode">业务单号</param>
        /// <param name="startDate">业务单据开始时间</param>
        /// <param name="endDate">业务单据结束时间</param>
        /// <param name="productNo">货号</param>
        /// <param name="productName">产品名称</param>
        /// <param name="specification">规格</param>
        /// <param name="model">型号</param>
        /// <param name="producerName">厂家名称</param>
        /// <param name="producerOrderNo">厂家订单号</param>
        /// <param name="purchaseOrderCode">采购订单号</param>
        /// <param name="taxRate">税率</param>
        /// <returns>符合条件的缓存数据</returns>
        List<MatchableDocumentItem> QueryBusinessTypeCache(
            Guid mergeInputBillId,
            BusinessType? businessType = null,
            string? businessCode = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? productNo = null,
            string? productName = null,
            string? specification = null,
            string? model = null,
            string? producerName = null,
            string? producerOrderNo = null,
            string? purchaseOrderCode = null,
            decimal? taxRate = null);

        /// <summary>
        /// 保存查询条件到缓存
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="request">查询条件</param>
        void SaveQueryConditionToCache(Guid mergeInputBillId, GetMatchableDocumentsRequest request);

        /// <summary>
        /// 从缓存中获取查询条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>查询条件，如果缓存中不存在则返回null</returns>
        GetMatchableDocumentsRequest? GetQueryConditionFromCache(Guid mergeInputBillId);

        /// <summary>
        /// 检查是否有正在进行的提交操作
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>如果有正在进行的提交操作，返回发票号；否则返回null</returns>
        string? GetLockedInvoiceNumber(Guid mergeInputBillId);

        /// <summary>
        /// 设置提交锁
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="invoiceNumber">发票号</param>
        void SetSubmitLock(Guid mergeInputBillId, string invoiceNumber);

        /// <summary>
        /// 释放提交锁
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        void ReleaseSubmitLock(Guid mergeInputBillId);
    }
}
