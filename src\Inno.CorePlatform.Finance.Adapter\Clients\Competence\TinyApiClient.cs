﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.InventoryRecord;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class TinyApiClient : BaseDaprApiClient<TinyApiClient>, ITinyApiClient
    {
        public TinyApiClient(DaprClient daprClient, ILogger<TinyApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        /// <summary>
        /// 暂存-财务成本统计
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> FinanceCostStatistics(ReconciliationInput input)
        {
            try
            {
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.FinanceCostStatistics, RequestMethodEnum.POST);

            }
            catch (Exception)
            {

                throw new Exception("调用暂存-财务成本统计数据失败");
            }
        }


        protected override string GetAppId()
        {
            return AppCenter.Tiny_APPID;
        }
    }

    public class TinyApiClientOfResponseData : BaseDaprApiClient<TinyApiClientOfResponseData>, ITinyApiClientOfResponseData
    {
        public TinyApiClientOfResponseData(DaprClient daprClient, ILogger<TinyApiClientOfResponseData> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<TinyInventoryCreateOutputDto> CreateTinyInventory(TinyInventoryCreateRequestDto request)
        {
            BaseResponseData<TinyInventoryCreateOutputDto> res;

            // 如果请求中包含用户信息，使用指定用户信息的方法
            if (request.UserId.HasValue && !string.IsNullOrEmpty(request.UserName))
            {
                res = await InvokeMethodWithQueryObjectAndUserAsync<TinyInventoryCreateRequestDto, BaseResponseData<TinyInventoryCreateOutputDto>>(
                    request, AppCenter.CreateTinyInventroy, request.UserId, request.UserName, RequestMethodEnum.POST);
            }
            else
            {
                res = await InvokeMethodWithQueryObjectAsync<TinyInventoryCreateRequestDto, BaseResponseData<TinyInventoryCreateOutputDto>>(
                    request, AppCenter.CreateTinyInventroy, RequestMethodEnum.POST);
            }

            return res.Data;
        }

        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }
        protected override string GetAppId()
        {
            return AppCenter.Tiny_APPID;
        }
    }
}
