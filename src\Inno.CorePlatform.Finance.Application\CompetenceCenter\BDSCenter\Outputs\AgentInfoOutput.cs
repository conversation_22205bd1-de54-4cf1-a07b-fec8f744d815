﻿using Inno.CorePlatform.Finance.Domain.DomainObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{
    public class DaprAgentInfoOutput
    {
        public string agentEnname { get; set; }
        public string agentId { get; set; }
        public int? agentIsZXInternal { get; set; }
        public string agentName { get; set; }
        public string agentNote { get; set; }
        public int? agentProperty { get; set; }
        /// <summary>
        /// 供货类别
        /// </summary>
        public string agentPropertyDesc { get; set; }
        public bool? containValidCerts { get; set; }
        public string enterpriseId { get; set; }
        public int? isQuotedComp { get; set; }
        public string legalPerson { get; set; }
        public string manageRange { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string taxRate { get; set; }

        public List<BusinessRange> newManageScopeRes { get; set; }
        public List<BusinessRange> oldManageScopeRes { get; set; }
        public string socialCode { get; set; }
        public int? taxpayerType { get; set; }
    }
    public class AgentMetaOutput : BDSBaseOutput
    {



    }


    public class AgentBankInfo
    {
        public Guid agentId { get; set; }
        public string? agentName { get; set; }
        public string account { get; set; }
        public string bank { get; set; }
        public string bankCode { get; set; }
        public string bankNo { get; set; }
        public int? type { get; set; }
        public string? registerAddress { get; set; }
        public string? registerAddressName { get; set; }

        public List<receipt> receiptList { get; set; }

        /// <summary>
        /// 供应商简称
        /// </summary>
        public string agentAbbr { get; set; }

        /// <summary>
        ///  是否内部供应商(2:建发集团内部客户（非建发致新内部）12:建发致新内部客户（全资）11:建发致新内部客户（合资）)，0/null=外部供应商
        /// </summary>
        public int? agentIsZXInternal { get; set; }

        /// <summary>
        /// 是否内部企业描述（查询返回描述）
        /// </summary>
        public string? agentIsZXInternalDesc { get; set; }
    }

    public class receipt
    {
        public string? account { get; set; }
        public string? bank { get; set; }
        public string? bankCode { get; set; }
        public string? bankNo { get; set; }
        public int? type { get; set; }

        /// <summary>
        /// 公司法人集合
        /// </summary>
        public List<Guid>? companyList { get; set; }

        /// <summary>
        /// 公司id集合
        /// </summary>
        public string? companyIds { get; set; }

        /// <summary>
        /// 公司名称集合
        /// </summary>
        public string? companyNames { get; set; }
    }
}
