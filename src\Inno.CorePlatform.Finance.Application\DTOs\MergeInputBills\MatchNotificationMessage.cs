using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using System;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills
{
    /// <summary>
    /// 匹配通知消息
    /// </summary>
    public class MatchNotificationMessage
    {
        /// <summary>
        /// 合并进项发票ID
        /// </summary>
        public Guid MergeInputBillId { get; set; }

        /// <summary>
        /// 合并发票号
        /// </summary>
        public string MergeInvoiceNumber { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 匹配维度
        /// </summary>
        public int MatchPrecision { get; set; }

        /// <summary>
        /// 匹配项列表
        /// </summary>
        public List<MatchableDocumentItem> MatchedItems { get; set; } = new List<MatchableDocumentItem>();
    }
}
