﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 不产生财务数据的入库单
    /// </summary>
    public interface IStoreInWithoutFinanceAppSerivce: IInventoryAppService
    {
        Task<BaseResponseData<HoldStorageInput>> GetStoreInKingdeeParams(string  code);
    }
}
