﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 返回列表
    /// </summary>
    public class ReconciliationOutput
    {
        /// <summary>
        /// 公司Id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 单据类型
        /// </summary>
        public int? BillType { get; set; }

        /// <summary>
        /// mark
        /// </summary>
        public int? Mark { get; set; }

        /// <summary>
        /// 单据类型文本
        /// </summary>
        public string? BillTypeStr { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 业务单据号
        /// </summary>
        public string? SaleOrderNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? AgentId { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }
        /// <summary>
        /// 收入不含税
        /// </summary>
        public decimal? IncomeOfNoTax { get; set; }
        /// <summary>
        /// 成本不含税
        /// </summary>
        public decimal? CostOfNoTax { get; set; }
        /// <summary>
        /// 收入含税
        /// </summary>
        public decimal? Income { get; set; }
        /// <summary>
        /// 成本含税
        /// </summary>
        public decimal? Cost { get; set; }

        #region  采购专有
        /// <summary>
        /// 存货修订金额
        /// </summary>
        public decimal? InventoryChangeAmount { get; set; }

        /// <summary>
        /// 存货修订不含税金额（发生额）
        /// </summary>
        public decimal? InventoryChangeAmountOfNoTax { get; set; }

        #endregion
        /// <summary>
        /// 发生额
        /// </summary>
        public decimal? ChangeAmount { get; set; }
        #region  金蝶数据
        /// <summary>
        /// 金蝶收入不含税
        /// </summary>
        public decimal? KISIncomeOfNoTax { get; set; }
        /// <summary>
        /// 金蝶成本不含税
        /// </summary>
        public decimal? KISCostOfNoTax { get; set; }
        /// <summary>
        /// 金蝶收入含税
        /// </summary>
        public decimal? KISIncome { get; set; }
        /// <summary>
        /// 金蝶成本含税
        /// </summary>
        public decimal? KISCost { get; set; }

        /// <summary>
        /// 金蝶数据
        /// </summary>
        public decimal? KisData { get; set; }


        #endregion
        /// <summary>
        /// 
        /// </summary>
        public Guid? ReconciliationItemId { get; set; }
        /// <summary>
        /// 标准成本
        /// </summary>
        public decimal? StandardUnitCost { get; set; }
    }

    public class QueryReconForFmOutput
    {
        /// <summary>
        /// 发出商品(寄售)
        /// </summary>
        public List<ReconciliationOutput> consignSendGoods { get; set; } = new List<ReconciliationOutput>();

        /// <summary>
        /// 库存商品(寄售)
        /// </summary>
        public List<ReconciliationOutput> consignStoreGoods { get; set; } = new List<ReconciliationOutput>();

        /// <summary>
        /// 发出商品(经销)
        /// </summary>
        public List<ReconciliationOutput> sellSendGoods { get; set; } = new List<ReconciliationOutput>();

        /// <summary>
        /// 库存商品(经销)
        /// </summary>
        public List<ReconciliationOutput> sellStoreGoods { get; set; } = new List<ReconciliationOutput>();

        /// <summary>
        /// 库存商品(进口)
        /// </summary>
        public List<ReconciliationOutput> sellStoreImportGoods { get; set; } = new List<ReconciliationOutput>();
    }

    public class QueryReconIncomeOutput {
        /// <summary>
        /// 存货发生额(寄售)
        /// </summary>
        public List<ReconciliationOutput> consignSendGoods { get; set; } = new List<ReconciliationOutput>();

        /// <summary>
        /// 存货发生额(经销)
        /// </summary>

        public List<ReconciliationOutput> sellSendGoods { get; set; } = new List<ReconciliationOutput>();
        /// <summary>
        /// 收入成本
        /// </summary>
        public List<ReconciliationOutput> receivableCosts { get; set; } = new List<ReconciliationOutput>();
    }
}
