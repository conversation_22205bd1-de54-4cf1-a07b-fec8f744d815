﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    public class PaymentPlanOutPut
    {
        /// <summary>
        /// 采购单Id
        /// </summary>
        public Guid PurchaseOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string PurchaseOrderCode { get; set; }

        /// <summary>
        /// 账期类型
        /// Days payable outstanding type
        /// </summary>
        public int DPOType { get; set; }

        /// <summary>
        /// 账期比例
        /// Days payable outstanding proportion
        /// </summary>
        public decimal DPOProportion { get; set; }
        /// <summary>
        /// 修订比例
        /// 修改后的比例记录
        /// </summary>
        public decimal? ProportionRevision { get; set; }
        /// <summary>
        /// 账期天数
        /// Days payable outstanding
        /// </summary>
        public int DPO { get; set; }

        /// <summary>
        /// 付款状态
        /// </summary>
        public int PaymentStatus { get; set; }

        /// <summary>
        /// 应付单号
        /// Accounts Payable Bill Code
        /// </summary>
        public string? APBillCode { get; set; }

        /// <summary>
        /// 账期起始日
        /// </summary>
        public DateTime? DPOStartDate { get; set; }

        /// <summary>
        /// 应付时间
        /// Accounts Payable Time 
        /// </summary>
        public DateTime? APTime { get; set; }

        /// <summary>
        /// 应付金额
        /// Accounts Payable Amount 
        /// </summary>
        public decimal APAmount { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal WriteOffAmount { get; set; }

        /// <summary>
        /// 待冲销金额
        /// </summary>
        public decimal WaitWriteOffAmount { get; set; }

        /// <summary>
        /// 应付修订类型
        /// </summary>
        public int? RevisionType { get; set; }

        /// <summary>
        /// 实付金额
        /// </summary>
        public decimal ActualAmount { get; set; }
        /// <summary>
        /// 应付货品修订明细
        /// </summary>
        public List<APRevisionDetails>? APRevisionDetails { get; set; }
        /// <summary>
        /// 预计付款时间
        /// 暂时只有通道类项目的自动使用
        /// </summary>
        public DateTimeOffset? ProbablyPayTime { get; set; }
    }


    public enum DPOType
    {
        /// <summary>
        /// 回款账期
        /// </summary>
        [Description("回款账期")]
        ReturnedMoney = 0,
        /// <summary>
        /// 入库账期
        /// </summary>
        [Description("入库账期")]
        InStore = 1,
        /// <summary>
        /// 销售账期
        /// </summary>
        [Description("销售账期")]
        Sale = 2,
        /// <summary>
        /// 预付账期
        /// </summary>
        [Description("预付账期")]
        Prepayment = 3,
        /// <summary>
        /// 验收账期
        /// </summary>
        [Description("验收账期")]
        AcceptancePeriod = 4,

        /// <summary>
        /// 质保账期
        /// </summary>
        [Description("质保账期")]
        WarrantyPeriod = 5,
    }
    /// <summary>
    /// 1 修订账期比例（按比例则要移除明细）
    /// 2 修订明细金额
    /// </summary>
    public enum APRevisionType
    {
        /// <summary>
        /// 修订账期比例
        /// （按比例则要移除明细）
        /// </summary>
        [Description("修订账期比例")]
        Proportion = 1,
        /// <summary>
        /// 修订明细金额
        /// </summary>
        [Description("修订明细金额")]
        DetailAmount = 2
    }

    /// <summary>
    /// 应付货品修订明细
    /// </summary>
    public class APRevisionDetails
    {
        //品名信息
        public Product? Product { get; set; }
        /// <summary>
        /// 采购单价
        /// </summary>
        public decimal PurchaseCost { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 采购单价修订
        /// </summary>
        public decimal PurchaseCostRevision { get; set; }

        /// <summary>
        /// 比例修订
        /// </summary>
        public decimal? ProportionRevision { get; set; }

        /// <summary>
        /// 采购单详情Id
        /// </summary>
        public Guid? PurchaseDetailId { get; set; }
    }
}
