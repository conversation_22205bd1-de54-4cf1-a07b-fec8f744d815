﻿using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 创建服务采购明细出参
    /// </summary>
    public class ServicePurchaseQueryInfoOutput
    {
        /// <summary>
        /// 是否冲销 true=是 false=否
        /// </summary>
        public bool? Hedgereceivable { get; set;}
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        public string StatusName
        {
            get
            {
                return ((ServicePurchaseStatusEnums)Status).GetDescription();
            }
        }

        /// <summary>
        /// 事业部Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 事业部全名路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 事业部全路径Id
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 事业部短码
        /// </summary>
        public string? BusinessDeptShortName { get; set; }

        /// <summary>
        /// 服务采购合同
        /// </summary>
        public ServiceContractQueryOutput? Contract { get; set; }

        /// <summary>
        /// 服务采购明细
        /// </summary>
        public List<ServicePurchaseDetailQueryOutput>? Details { get; set; }

        /// <summary>
        /// 服务项类型代码
        /// </summary>
        public string ServiceItemTypeCode { get; set; }

        /// <summary>
        /// 服务项类型名称
        /// </summary>
        public string ServiceItemTypeName { get; set; }

        /// <summary>
        /// 关联项目
        /// </summary>
        public Project Project { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Consignee Company { get; set; }

        /// <summary>
        /// 供应商信息
        /// </summary>
        public Agent Agent { get; set; }

        /// <summary>
        /// 附件集合
        /// </summary>
        public string? AttachmentFileIds { get; set; }

        /// <summary>
        /// 是否需要采购合同
        /// </summary>
        public bool IsNeedContract { get; set; }

        /// <summary>
        /// 审核说明
        /// </summary>
        public string AuditRemark { get; set; } = "";
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = "";
        public DateTime? BillDate { get; set; }
        public string CreatedBy { get; set; }
    }

    /// <summary>
    /// 服务采购订单状态枚举
    /// </summary>
    public enum ServicePurchaseStatusEnums
    {
        /// <summary>
        /// 草稿
        /// </summary>
        [Description("临时草稿")]
        Draft = 1,
        /// <summary>
        /// 待审核
        /// </summary>
        [Description("待审核")]
        WaitAudit = 2,
        /// <summary>
        /// 待预付
        /// </summary>
        [Description("待预付")]
        WaitPay = 4,
        /// <summary>
        /// 已完成
        /// </summary>
        [Description("已完成")]
        Finished = 5,
        /// <summary>
        /// 已拒绝
        /// </summary>
        [Description("已拒绝")]
        Refuse = 7,
    }

    /// <summary>
    /// 服务合同出参
    /// </summary>
    public class ServiceContractQueryOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 买方名称(甲方)
        /// </summary>
        public string BuyerName { get; set; }

        /// <summary>
        /// 卖方名称(乙方)
        /// </summary>
        public string SellerName { get; set; }

        /// <summary>
        /// 签约日期
        /// </summary>
        public DateTime SignDate { get; set; }

        /// <summary>
        /// 签约地点
        /// </summary>
        public string? SignAddress { get; set; }

        /// <summary>
        /// 附件集合
        /// </summary>
        public string? AttachmentFileIds { get; set; }

        /// <summary>
        /// AC附件集合
        /// </summary>
        public string? AcAttachmentFileIds { get; set; }
    }

    /// <summary>
    /// 收货方信息（目前主要是公司）
    /// </summary>
    public class Consignee
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string? NameCode { get; set; }
        public Consignee() { }
        public Consignee(string id, string name)
        {
            this.Id = id;
            this.Name = name;
        }
        public Consignee(string id, string name, string nameCode)
        {
            this.Id = id;
            this.Name = name;
            this.NameCode = nameCode;
        }
    }

    /// <summary>
    /// 创建服务采购明细出参
    /// </summary>
    public class ServicePurchaseDetailQueryOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 质保类型名称
        /// </summary>
        public string WarrantyTypeName
        {
            get
            {
                return WarrantyType.HasValue ? WarrantyType.Value.GetDescription() : "";
            }
        }

        #region 服务项
        /// <summary>
        /// 服务项名称
        /// </summary>
        public string ServiceItemName { get; set; }

        /// <summary>
        /// 服务项Id
        /// </summary>
        public string ServiceItemId { get; set; }

        /// <summary>
        /// 服务项类型代码
        /// </summary>
        public string ServiceItemTypeCode { get; set; }

        /// <summary>
        /// 服务项类型名称
        /// </summary>
        public string ServiceItemTypeName { get; set; }

        /// <summary>
        /// 开票名称
        /// </summary>
        public string InvoiceName { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        #endregion

        /// <summary>
        /// 质保类型
        /// </summary>
        public WarrantyTypeEnums? WarrantyType { get; set; }

        /// <summary>
        /// 质保提供方
        /// </summary>
        public Agent? WarrantyProvider { get; set; }

        /// <summary>
        /// 成本
        /// </summary>
        public decimal? Cost { get; set; }

        /// <summary>
        /// 有效期开始
        /// </summary>
        public DateTimeOffset? ValidStartDate { get; set; }

        /// <summary>
        /// 有效期截至
        /// </summary>
        public DateTimeOffset? ValidEndDate { get; set; }

        /// <summary>
        /// 有效期天数
        /// </summary>
        public int? ValidDay { get; set; }

        /// <summary>
        /// 待入票金额
        /// </summary>
        public decimal? WaitInvoiceAmount { get; set; }

        /// <summary>
        /// 数量
        /// </summary> 
        public int? Quantity { get; set; } = 1;
    }

    /// <summary>
    /// 质保类型枚举
    /// </summary>
    public enum WarrantyTypeEnums
    {
        /// <summary>
        /// 原厂质保
        /// </summary>
        [Description("原厂质保")]
        Original = 1,

        /// <summary>
        /// 三方质保
        /// </summary>
        [Description("三方质保")]
        ThirdParty = 2,
    }
}
