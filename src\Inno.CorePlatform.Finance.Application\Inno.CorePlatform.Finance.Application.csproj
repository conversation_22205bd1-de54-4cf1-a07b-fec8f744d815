﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<DocumentationFile>..\..\docxml\dto.xml</DocumentationFile>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="ApplicationServices\ApplyBFFService\inputs\**" />
	  <Compile Remove="DTOs\PyableQueryDto\**" />
	  <EmbeddedResource Remove="ApplicationServices\ApplyBFFService\inputs\**" />
	  <EmbeddedResource Remove="DTOs\PyableQueryDto\**" />
	  <None Remove="ApplicationServices\ApplyBFFService\inputs\**" />
	  <None Remove="DTOs\PyableQueryDto\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="EasyCaching.Redis" Version="1.9.0" />
		<PackageReference Include="EasyCaching.Serialization.SystemTextJson" Version="1.9.0" />
		<PackageReference Include="EPPlus" Version="7.3.0" />
		<PackageReference Include="MediatR" Version="12.4.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
		<PackageReference Include="Npoi.Mapper" Version="6.0.0" />
		<PackageReference Include="Polly" Version="7.2.4" />
		<PackageReference Include="System.Threading.Tasks" Version="4.3.0" />
	</ItemGroup>

    <ItemGroup>
        <None Remove="StaticFiles\metadata.json" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="StaticFiles\metadata.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Data\Inno.CorePlatform.Finance.Data.csproj" />
		<ProjectReference Include="..\Inno.CorePlatform.Finance.Domain\Inno.CorePlatform.Finance.Domain.csproj" />
	</ItemGroup>

</Project>
