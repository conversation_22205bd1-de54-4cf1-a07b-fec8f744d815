﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class RecognizeReceiveDetailRepository : EfBaseRepository<Guid, RecognizeReceiveDetail, RecognizeReceiveDetailPo>, IRecognizeReceiveDetailRepository
    {
        private readonly FinanceDbContext _db;
        public RecognizeReceiveDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
            UowJoined = true;
        }
        public override async Task<int> UpdateAsync(RecognizeReceiveDetail root)
        {
            var isExist = await _db.RecognizeReceiveDetails.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new AppServiceException("认款单详情不存在！");
            }

            var po = root.Adapt<RecognizeReceiveDetailPo>();

            _db.RecognizeReceiveDetails.Update(po);
            if (UowJoined) return 0;
            return await _db.SaveChangesAsync();

        }
        protected override RecognizeReceiveDetailPo CreateDeletingPo(Guid id)
        {
            return new RecognizeReceiveDetailPo { Id = id };
        }
        protected override Task<RecognizeReceiveDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
        public override async Task<RecognizeReceiveDetail?> GetAsync(Guid id)
        {
            var po = await _db.RecognizeReceiveDetails.AsNoTracking().SingleOrDefaultAsync(t => t.Id == id);
            return po.Adapt<RecognizeReceiveDetail>();

        }
        public async Task<int> DeteteManyAsync(List<Guid> lstId)
        {

            var lstPo = await _db.RecognizeReceiveDetails.Where(t => lstId.Contains(t.Id)).ToListAsync();
            _db.RecognizeReceiveDetails.RemoveRange(lstPo);
            var lstTempPo = await _db.RecognizeReceiveTempDetails.Where(t => lstId.Contains(t.Id)).ToListAsync();
            _db.RecognizeReceiveTempDetails.RemoveRange(lstTempPo);
            return await _db.SaveChangesAsync();
        }

        public async Task<int> UpdateManyAsync(List<RecognizeReceiveDetail> lstDetail)
        {
            var lstPo = lstDetail.Adapt<List<RecognizeReceiveDetailPo>>();

            _db.RecognizeReceiveDetails.UpdateRange(lstPo);

            if (UowJoined) return 0;

            return await _db.SaveChangesAsync();
        }
        public async Task<bool> IsExist(string code, Guid itemId)
        {
            var exist = await _db.RecognizeReceiveDetails.Where(t => t.Code == code && t.RecognizeReceiveItemId == itemId).AsNoTracking().ToListAsync();
            if (exist != null && exist.Any()) return true;
            return false;
        }
    }
}
