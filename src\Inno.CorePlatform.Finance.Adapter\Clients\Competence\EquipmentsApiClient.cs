﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Equipments;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class EquipmentsApiClient : BaseDaprApiClient<EquipmentsApiClient>, IEquipmentsApiClient
    {
        public EquipmentsApiClient(DaprClient daprClient,
            ILogger<EquipmentsApiClient> logger,
            IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<BaseResponseData<bool>> EquipIsAllFinishByPurchaseOrderCodeAsync(PurchaseOrderCodeInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<PurchaseOrderCodeInput, BaseResponseData<bool>>(input, AppCenter.EquipIsAllFinishByPurchaseOrderCode, RequestMethodEnum.POST);
        }

        public async Task<BaseResponseData<string>> UpdateEquipmentForPut(List<EquipmentInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<EquipmentInput>, BaseResponseData<string>>(input, AppCenter.UpdateEquipmentForPut, RequestMethodEnum.POST);
        }
        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
        
            return res;
        }
        protected override string GetAppId()
        {
            return AppCenter.Equip_APPID;
        }
    }
}
