﻿using Inno.CorePlatform.Common.Clients.ApiServices.ProjectManage.Inputs;
using Inno.CorePlatform.Common.Clients.ApiServices.ProjectManage.Outputs;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Microsoft.AspNetCore.Mvc;
using NPOI.POIFS.Crypt.Dsig.Facets;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IProjectMgntApiClient
    {
        Task<List<RuleConfigOutPut>> GetRefAccountAndDiscount(RuleConfigInput input);
        /// <summary>
        /// 获取项目列表(带数据策略)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<ProjectMateInfoOutput>> GetProjectMateList(ProjectMateInfoInput input);
        Task<List<ProjectInfo>> GetProjectInfoByIds(List<Guid>? ids);
        Task<List<ProjectInfo>> GetProjectInfoByProjectNo(string projectNo);
        Task<List<ProductCostOutput>> GetProductCost(ProductCostInput input);
        Task<string> UpdatePaymentStatus(List<UpdatePaymentStatusInput> input);
        Task<List<ProjectOutput>> GetProjectListByNames(Guid? userId,List<string>? names, string functionUri = "");
        /// <summary>
        /// 根据公司获取项目
        /// </summary>
        /// <param name="companyId"></param> 
        /// <returns></returns>
        Task<List<ProjectOutput>> GetProjectListByCompanyId(string companyId);
        /// <summary>
        /// 通过项目id获取项目和核算部门
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<ProjectAndBusinessDeptInfoOutput>> GetProjectListByIds(List<Guid> ids);

        /// <summary>
        /// 根据项目Id或者名称获取项目信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<GetProjectInfoByIdOrNameOut>> GetProjectInfoByIdOrName(GetProjectInfoByIdOrNameInput input);
        /// <summary>
        /// 根据核算部门获取项目
        /// </summary>
        /// <param name="businessDeptIds"></param> 
        /// <returns></returns>
        Task<List<ProjectOutput>> GetProjectListByBusinessDeptIds(List<string> businessDeptIds);
        /// <summary>
        /// 获取项目负责人
        /// </summary>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        Task<List<GetProjectLeaderOutput>> GetProjectLeader(List<Guid> projectIds);
    }
    public interface IProjectApiExcuteClient
    {
        Task<BaseResponseData<bool>> GetContractDelayInfoList(GetContractDelayInfoListInput input);
    }
}
