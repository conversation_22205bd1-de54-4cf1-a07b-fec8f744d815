﻿using Inno.CorePlatform.Finance.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Abatment
{
    public class GenerateAbtForDebtInput
    {
        public Guid DebtId { get; set; }
        /// <summary>
        /// 被冲销的应付单
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 冲销的金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 单据金额
        /// </summary>
        public decimal? BillValue { get; set; }

    }

    /// <summary>
    /// 损失确认正负应付冲销入参
    /// </summary>
    public class LossAbtForDebtInput
    {
        public DebtPo OldDebt { get; set; }
        public DebtPo NewDebt { get; set; }
    }



}
