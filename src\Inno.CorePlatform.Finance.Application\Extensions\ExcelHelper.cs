﻿using Inno.CorePlatform.Common.DDD;
using OfficeOpenXml;
using System.Reflection;

namespace Inno.CorePlatform.Sell.Application.Extensions
{
    /// <summary>
    /// Excel 帮助类
    /// </summary>
    public class ExcelHelper
    {

        /// <summary>
        /// 导入Excel  
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="stream">Excel文件流</param>
        /// <param name="dicField">字段字典</param>
        /// <param name="sheetIndex">工作表索引</param>
        /// <returns></returns>
        public static List<T> ImportExcel<T>(Stream stream, Dictionary<string, string> dicField, int sheetIndex = 0) where T : new()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 设置许可证上下文
            List<T> dataList = new List<T>();
            try
            {
                // 打开Excel文件
                using (var package = new ExcelPackage(stream))
                {
                    var workbook = package.Workbook;
                    if (workbook != null && workbook.Worksheets.Count > sheetIndex)
                    {
                        var worksheet = workbook.Worksheets[sheetIndex];
                        // 获取单元格的值
                        if (worksheet.Dimension.Rows > 10000)
                        {
                            throw new ApplicationException("一次最多导入10000条数据");
                        }
                        var properties = typeof(T).GetProperties();

                        // 读取行数据
                        for (int row = worksheet.Dimension.Start.Row + 1; row <= worksheet.Dimension.End.Row; row++)
                        {
                            var dataItem = new T();
                            // 处理空行
                            if (IsRowEmpty(worksheet, row))
                            {
                                break;
                            }

                            // 读取列数据
                            for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
                            {
                                var cellValue = worksheet.Cells[row, col].Value;
                                if (worksheet.Cells[1, col].Value == null || !dicField.ContainsKey(worksheet.Cells[1, col].Value?.ToString()))
                                {
                                    continue;
                                }
                                var field = dicField[worksheet.Cells[1, col].Value?.ToString()];
                                // 根据属性名称找到对应的列，并将值赋给属性
                                var property = properties.FirstOrDefault(p => p.Name.Equals(field, StringComparison.OrdinalIgnoreCase));
                                if (property != null)
                                {
                                    try
                                    {
                                        #region Nullable
                                        Type? nullableType = Nullable.GetUnderlyingType(property.PropertyType);
                                        if (property.PropertyType != typeof(string))
                                        {
                                            if (cellValue == null && nullableType == null)
                                            {
                                                throw new ApplicationException("格式有误");
                                            }
                                        }
                                        else
                                        {
                                            if (property.GetCustomAttribute<CanBeNullAttribute>() == null && cellValue == null)
                                            {
                                                throw new ApplicationException("格式有误");
                                            }
                                        }

                                        if (property.PropertyType == typeof(DateTimeOffset?) || property.PropertyType == typeof(DateTimeOffset))
                                        {
                                            // excel 导入时间，默认按东八时区处理
                                            if (nullableType != null)
                                            {
                                                if (cellValue != null)
                                                {
                                                    DateTimeOffset? tempDateTime = new DateTimeOffset(DateTime.Parse(cellValue?.ToString()), TimeSpan.FromHours(8));
                                                    property.SetValue(dataItem, tempDateTime);
                                                }
                                            }
                                            else
                                            {
                                                property.SetValue(dataItem, new DateTimeOffset(DateTime.Parse(cellValue?.ToString()), TimeSpan.FromHours(8)));
                                            }
                                            continue;
                                        }

                                        if (nullableType != null)
                                        {
                                            if (cellValue != null)
                                            {
                                                property.SetValue(dataItem, Convert.ChangeType(cellValue, nullableType));
                                            }
                                        }
                                        else
                                        {
                                            property.SetValue(dataItem, Convert.ChangeType(cellValue, property.PropertyType));
                                        }
                                        #endregion
                                    }
                                    catch (Exception aex)
                                    {
                                        throw new ApplicationException($"第【{row}】行，【{worksheet.Cells[1, col].Value?.ToString()}】数据格式不对");
                                    }
                                }
                            }
                            dataList.Add(dataItem);
                        }
                    }
                }
                return dataList;
            }
            catch (ApplicationException ex)
            {
                throw new ApplicationException(ex.Message);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("导入文件中的字段类型格式不对，无法正常解析");
            }
        }


        /// <summary>
        /// 导入Excel  
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="stream">Excel文件流</param>
        /// <param name="dicField">字段字典</param>
        /// <param name="sheetIndex">工作表索引</param>
        /// <returns></returns>
        public static List<T> ImportExcelPlus<T>(Stream stream, Dictionary<string, string> dicField, List<CorePlatform.Common.Utility.ExcelErrorItem> excelErrorItems,int sheetIndex = 0) where T : new()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial; // 设置许可证上下文
            List<T> dataList = new List<T>();
            try
            {
                // 打开Excel文件
                using (var package = new ExcelPackage(stream))
                {
                    var workbook = package.Workbook;
                    if (workbook != null && workbook.Worksheets.Count > sheetIndex)
                    {
                        var worksheet = workbook.Worksheets[sheetIndex];
                        // 获取单元格的值
                        if (worksheet.Dimension.Rows > 10000)
                        {
                            throw new ApplicationException("一次最多导入10000条数据");
                        }
                        var properties = typeof(T).GetProperties();

                        // 读取行数据
                        for (int row = worksheet.Dimension.Start.Row + 1; row <= worksheet.Dimension.End.Row; row++)
                        {
                            List<string> listErrTexts = new List<string>();
                            var importItem = new CorePlatform.Common.Utility.ExcelErrorItem();

                            var dataItem = new T();
                            // 处理空行
                            if (IsRowEmpty(worksheet, row))
                            {
                                break;
                            }
                            // 读取列数据
                            for (int col = worksheet.Dimension.Start.Column; col <= worksheet.Dimension.End.Column; col++)
                            {
                                var cellValue = worksheet.Cells[row, col].Value;
                                if (worksheet.Cells[1, col].Value == null || !dicField.ContainsKey(worksheet.Cells[1, col].Value?.ToString()))
                                {
                                    continue;
                                }
                                var field = dicField[worksheet.Cells[1, col].Value?.ToString()];
                                // 根据属性名称找到对应的列，并将值赋给属性
                                var property = properties.FirstOrDefault(p => p.Name.Equals(field, StringComparison.OrdinalIgnoreCase));
                                if (property != null)
                                {
                                    try
                                    {
                                        #region Nullable
                                        Type? nullableType = Nullable.GetUnderlyingType(property.PropertyType);
                                        if (property.PropertyType != typeof(string))
                                        {
                                            if (cellValue == null && nullableType == null)
                                            {
                                                //throw new ApplicationException("格式有误");
                                                listErrTexts.Add("行记录有空值或格式有误");
                                            }
                                        }
                                        else
                                        {
                                            if (property.GetCustomAttribute<CanBeNullAttribute>() == null && cellValue == null)
                                            {
                                                //throw new ApplicationException("格式有误");
                                                listErrTexts.Add("行记录有空值或格式有误");
                                            }
                                        }

                                        if (property.PropertyType == typeof(DateTimeOffset?) || property.PropertyType == typeof(DateTimeOffset))
                                        {
                                            // excel 导入时间，默认按东八时区处理
                                            if (nullableType != null)
                                            {
                                                if (cellValue != null)
                                                {
                                                    DateTimeOffset? tempDateTime = new DateTimeOffset(DateTime.Parse(cellValue?.ToString()), TimeSpan.FromHours(8));
                                                    property.SetValue(dataItem, tempDateTime);
                                                }
                                            }
                                            else
                                            {
                                                property.SetValue(dataItem, new DateTimeOffset(DateTime.Parse(cellValue?.ToString()), TimeSpan.FromHours(8)));
                                            }
                                            continue;
                                        }

                                        if (nullableType != null)
                                        {
                                            property.SetValue(dataItem, Convert.ChangeType(cellValue, nullableType));
                                        }
                                        else
                                        {
                                            property.SetValue(dataItem, Convert.ChangeType(cellValue, property.PropertyType));
                                        }
                                        #endregion
                                    }
                                    catch (Exception aex)
                                    {
                                        //throw new ApplicationException($"第【{row}】行，【{worksheet.Cells[1, col].Value?.ToString()}】数据格式不对");
                                        listErrTexts.Add("数据格式不对");
                                    }

                                   
                                }
                            }

                            if (listErrTexts.Count > 0)
                            {
                                importItem.RowIndex = row - 1;
                                importItem.Errors = listErrTexts;
                                excelErrorItems.Add(importItem);
                            }
                            dataList.Add(dataItem);
                        }
                    }
                }
                return dataList;
            }
            catch (ApplicationException ex)
            {
                throw new ApplicationException(ex.Message);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("导入文件中的字段类型格式不对，无法正常解析");
            }
        }
        /// <summary>
        /// 检测是否空行   前三列为空则判断整行数据都是为空
        /// </summary>
        /// <param name="worksheet"></param>
        /// <param name="row"></param>
        /// <returns></returns>
        public static bool IsRowEmpty(ExcelWorksheet worksheet, int row)
        {
            bool isRowEmpty = true;
            // 检查前三个单元格是否都为空
            for (int colIndex = 1; colIndex <= 3 && colIndex <= worksheet.Dimension.End.Column; colIndex++) // 假设检查前三列
            {
                var cell = worksheet.Cells[row, colIndex];
                if (!string.IsNullOrWhiteSpace(cell.Text))
                {
                    isRowEmpty = false;
                    break;
                }
            }

            return isRowEmpty;
        }


        /// <summary>
        /// excel 导出
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list">数据</param>
        /// <param name="dicField">字段字典</param>
        /// <returns></returns>
        /// <exception cref="AppServiceException"></exception>

        public static Stream ExportExcel<T>(List<T> list, Dictionary<string, string> dicField)
        {
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial; // 设置许可证上下文

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                Type type = typeof(T);
                int curRowIndex = 1;
                var index = 1;
                foreach (var keyValuePair in dicField)
                {
                    worksheet.Cells[curRowIndex, index].Value = keyValuePair.Key;
                    index++;
                }
                foreach (var row in list)
                {
                    curRowIndex++;
                    index = 1;
                    foreach (var keyValuePair in dicField)
                    {
                        // 获取字段信息
                        var fieldInfo = type.GetProperty(keyValuePair.Value);
                        if (fieldInfo == null)
                            throw new AppServiceException("导出字段有误");
                        worksheet.Cells[curRowIndex, index].Value = fieldInfo.GetValue(row);
                        index++;
                    }
                }
                // 根据文字自动调整列宽
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                //设置第一行的字体加粗
                worksheet.Row(1).Style.Font.Bold = true;
                // 保存Excel文件
                var stream = new MemoryStream();
                package.SaveAs(stream);
                stream.Position = 0;
                return stream;
            }
        }
    }


    /// <summary>
    /// 可为空属性  处理 string 与string？的类型
    /// 因为在运行时类型信息（RTTI）中，string? 和 string 都是相同的 System.String 类型
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, Inherited = false)]
    public class CanBeNullAttribute : Attribute
    {
    }
}
