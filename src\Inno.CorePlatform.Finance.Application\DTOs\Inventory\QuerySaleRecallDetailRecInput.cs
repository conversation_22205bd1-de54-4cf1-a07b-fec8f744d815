﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Inventory
{
    public class QuerySaleRecallDetailRecInput
    { 
        /// <summary>
        /// 公司id
        /// </summary>
        public string companyId { get; set; }
        /// <summary>
        /// 截止时间
        /// </summary>
        public long recEndTime { get; set; }
        /// <summary>
        /// 起始时间
        /// </summary>
        public long recStartTime { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public string shipperId { get; set; } 

    }

    public class QuerySaleRecallDetailRecOutput
    {
        /// <summary>
        /// 金额
        /// </summary>
        public decimal amount { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        public string lotNo { get; set; }
        /// <summary>
        /// 单价
        /// </summary>
        public decimal price { get; set; }
        /// <summary>
        /// 货号Id
        /// </summary>
        public string productId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string productNo { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal quantity { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        public string specification { get; set; }
        /// <summary>
        /// 入库申请单号
        /// </summary>
        public string storeInApplyCode { get; set; }
        /// <summary>
        /// 入库申请创建时间
        /// </summary>
        public long storeInApplyCreatedTime { get; set; }
        /// <summary>
        /// 入库单号
        /// </summary>
        public string storeInCode { get; set; }
        /// <summary>
        /// storeInCreatedBy
        /// </summary>
        public string storeInCreatedBy { get; set; }
    }
}
