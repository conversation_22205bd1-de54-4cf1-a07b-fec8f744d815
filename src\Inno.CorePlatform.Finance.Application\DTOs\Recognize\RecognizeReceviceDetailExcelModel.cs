﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Recognize
{
    /// <summary>
    /// 认款明细导入模板
    /// </summary>
    public class RecognizeReceviceDetailExcelModel
    {
        /// <summary>
        /// 发票/订单/初始应收单号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditCode { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Value { get; set; }
        /// <summary>
        /// 认款类型
        /// </summary>
        public int Type { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 是否跳号
        /// </summary>
        public bool IsSkip { get; set; }
        /// <summary>
        /// 细分类型
        /// </summary>
        public RecognizeReceiveDetailClassifyEnum? Classify { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrMsg { get; set; }
    }
}
