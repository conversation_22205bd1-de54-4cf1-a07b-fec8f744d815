﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.UC
{
    public class GetUserListByEmployeeIdsOutput
    {
        public string id { get; set; }
        public int isAdminOut { get; set; }
        public int isAdmin { get; set; }
        public string isExternalStr { get; set; }
        public object userGroupStr { get; set; }
        public string statusName { get; set; }
        public string institutionName { get; set; }
        public string name { get; set; }
        public string displayName { get; set; }
        public bool isExternal { get; set; }
        public string employeeId { get; set; }
        public string mfaPhoneNumber { get; set; }
        public string mfaWxWorkId { get; set; }
        public int status { get; set; }
        public string email { get; set; }
        public string oaAccount { get; set; }
        public DateTime createdTime { get; set; }
        public object updatedTime { get; set; }
        public object deletedTime { get; set; }
        public string createdBy { get; set; }
        public object updatedBy { get; set; }
        public object deletedBy { get; set; }
        public bool isDeleted { get; set; }
        public object optUserId { get; set; }
        public object institutionType { get; set; }
        public object institutions { get; set; }
    }
}
