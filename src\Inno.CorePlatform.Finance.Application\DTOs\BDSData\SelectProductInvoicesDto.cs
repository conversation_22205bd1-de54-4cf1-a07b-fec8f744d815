﻿namespace Inno.CorePlatform.Finance.Application.DTOs.BDSData
{
    public class SelectProductInvoicesInput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid? CompanyId { get; set; }
        public List<SelectProductInvoicesInputData>? List { get; set; }

    }
    public class SelectProductInvoicesInputData
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId { get; set; }
    }
    public class SelectProductInvoicesOutput
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public Guid? CustomerId
        {
            get; set;
        }
        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId
        {
            get; set;
        }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage
        {
            get; set;
        }
        /// <summary>
        /// 开票名称
        /// </summary>
        public string? InvoiceName
        {
            get; set;
        }
        /// <summary>
        /// 开票规格
        /// </summary>
        public string? InvoiceSpec
        {
            get; set;
        }
        /// <summary>
        /// 开票单位
        /// </summary>
        public string? InvoiceUnit
        {
            get; set;
        }
        /// <summary>
        /// 包装单位
        /// </summary>
        public string? packUnit
        {
            get; set;
        }
        /// <summary>
        /// 开票单位描述
        /// </summary>
        public string? InvoiceUnitDesc
        {
            get; set;
        }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName
        {
            get; set;
        }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo
        {
            get; set;
        }
        /// <summary>
        /// 销售税率
        /// </summary>
        public decimal? SaleTax { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification
        {
            get; set;
        }
    }
}
