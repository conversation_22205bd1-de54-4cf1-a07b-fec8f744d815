﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InputBillSubmitDetailRepository : EfBaseRepository<Guid, InputBillSubmitDetail, InputBillSubmitDetailPo>, IInputBillSubmitDetailRepository
    {
        private FinanceDbContext db;
        public InputBillSubmitDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            db = dbContext;
        }
        public override async Task<int> UpdateAsync(InputBillSubmitDetail root)
        {
            var isExist = await db.InputBillSubmitDetails.AnyAsync(x => x.Id == root.Id);
            if (isExist == false)
            {
                throw new Exception("提交明细不存在！");
            }

            var po = root.Adapt<InputBillSubmitDetailPo>();

            db.InputBillSubmitDetails.Update(po);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }

        public async Task<int> AddManyAsync(List<InputBillSubmitDetail> list)
        {
            var addrang = list.Adapt<List<InputBillSubmitDetailPo>>();
            await db.InputBillSubmitDetails.AddRangeAsync(addrang);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }
        public async Task<int> updateManyAsync(List<InputBillSubmitDetail> list)
        {
            var addrang = list.Adapt<List<InputBillSubmitDetailPo>>();
            db.InputBillSubmitDetails.UpdateRange(addrang);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }

        public async Task<int> DeteteManyAsync(List<Guid> lstId)
        {
            // 根据主表的 ID 获取主表实体
            var inputBillSubmitDetails = await db.InputBillSubmitDetails.Include(x=>x.InputBillSubmitDetailQuantity).Where(t => lstId.Contains(t.Id)).ToListAsync();

            // 删除关联的关联表数据
            foreach (var inputBillSubmitDetail in inputBillSubmitDetails)
            {
                db.InputBillSubmitDeatilQuantitys.RemoveRange(inputBillSubmitDetail.InputBillSubmitDetailQuantity);
            }


            // 删除主表数据
            db.InputBillSubmitDetails.RemoveRange(inputBillSubmitDetails);
           
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }
        protected override InputBillSubmitDetailPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<InputBillSubmitDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
