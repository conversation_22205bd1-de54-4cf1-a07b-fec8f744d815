﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface ITempToSellAppService :ISellAppService
    {
        public Task<BaseResponseData<List<KingdeeCredit>>> GetKingdeeTempSaleCreditParams(EventBusDTO dto);
    }
}
