﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Gateway.Models.File;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IReconciliationLetterAppService
    {
        /// <summary>
        /// 提交对账函
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> SubmitItemAsync(Guid? id,string user);
        /// <summary>
        /// 删除对账函
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteItemAsync(Guid? id);
     
        /// <summary>
        /// 上传回函件
        /// </summary>
        Task<BaseResponseData<int>> AttachFileIds_letter(ReconciliationLetterAttachFileInput input);
        /// <summary>
        /// 查看回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
         Task<List<BizFileUploadOutput>> GetAttachFile_letter(ReconciliationLetterAttachFileInput input);
        /// <summary>
        /// 查看附件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<List<BizFileUploadOutput>> GetAttachFile(ReconciliationLetterAttachFileInput input);
        /// <summary>
        /// 删除回函件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> DeleteAttachFileIds_letter(ReconciliationLetterAttachFileInput input);
        /// <summary>
        /// 创建财务对账函
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> CreateReconciliationLetter(CreateReconciliationLetterInput input);
        /// <summary>
        /// 更新对账函
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<string>> UpdateReconciliationLetter(CreateReconciliationLetterInput input);
        /// <summary>
        /// 修改状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="statusEnum"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        Task<BaseResponseData<string>> UpdateStatus(Guid id, StatusEnum statusEnum);
        /// <summary>
        /// 根据请求id 更新询证函回函件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="attachFileDtos"></param>
        /// <returns></returns>
         Task UpdateConfirmAttachFileldsByRequestId(string id, List<AttachFileDto> attachFileDtos);
        /// <summary>
        /// 重新获取同步对账函明细数据
        /// </summary>
        /// <param name="id"></param>
        /// <param name="user"></param>
        /// <returns></returns>
         Task<BaseResponseData<string>> ResetItemAsync(Guid? id, string user);
        /// <summary>
        /// 对账函物流打印模板
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        Task<HttpContent> GetReconciliationLetterPDF(Guid id);
        /// <summary>
        /// 删除详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="itemId"></param>
        /// <returns></returns> 
        Task<int> DeleteDetailItemAsync(Guid id, Guid itemId);
    }
}
