﻿using Inno.CorePlatform.Finance.Data.Migrations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.Common
{
    /// <summary>
    /// 日期辅助类
    /// </summary>
    public static class DateTimeHelper
    {
        /// <summary>
        /// 将时间戳转换为日期
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public static DateTime LongToDateTime(long time)
        {
            return new DateTime(1970, 1, 1, 8, 0, 0).AddMilliseconds(time);
        }
        /// <summary>
        /// 时间戳Timestamp转换成日期
        /// </summary>
        /// <param name="timeStamp"></param>
        /// <returns></returns>
        public static DateTime GetDateTime(long timeStamp)
        {

            DateTime dt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            dt = dt.AddMilliseconds(timeStamp).ToLocalTime();

            return dt;

        }
        /// <summary>
        /// 获取当前北京时间
        /// </summary>
        /// <returns></returns>
        public static DateTime GetCurrentDate() {
            // 获取UTC时间
            DateTime utcTime = DateTime.UtcNow;

            // 获取中国标准时间的TimeZoneInfo对象
            TimeZoneInfo chinaTimeZone = TimeZoneInfo.FindSystemTimeZoneById("China Standard Time");

            // 将UTC时间转换为中国标准时间
            DateTime chinaTime = TimeZoneInfo.ConvertTimeFromUtc(utcTime, chinaTimeZone);

           return chinaTime;

        }
        /// <summary>
        /// 将时间转换为时间戳
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static long DateTimeToUnixTimestamp(DateTime dateTime)
        {
            DateTime epoch = new DateTime(1970, 1, 1, 8, 0, 0, DateTimeKind.Utc);//加八个时区
            TimeSpan timeSpan = dateTime.ToUniversalTime() - epoch;
            return (long)timeSpan.TotalMilliseconds;
        }
    }
}
