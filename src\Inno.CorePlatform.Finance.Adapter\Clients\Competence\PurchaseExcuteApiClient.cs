﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class PurchaseExcuteApiClient : BaseDaprApiClient<PurchaseExcuteApiClient>, IPurchaseExcuteApiClient
    {
        public PurchaseExcuteApiClient(DaprClient daprClient, ILogger<PurchaseExcuteApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        /// <summary>
        /// 更改购货修订数量接口 20240929迁移（修复无法正确获取返回参数问题）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int?>> UpdatePurchaseReviseInviceAmount(List<UpdatePurchaseReviseInvoiceAmountInputDto> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<UpdatePurchaseReviseInvoiceAmountInputDto>, BaseResponseData<int?>>(input, AppCenter.PurchaseReviseInputBillUpdate, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 更新入票数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int?>> UpdateInvoiceQuantity(List<UpdateInvoiceQuantityInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<UpdateInvoiceQuantityInput>, BaseResponseData<int?>>(input, AppCenter.Purchase_UpdateInvoiceQuantity, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 获取采购子系统配置
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<SubSysRelaQueryOutput?>> GetSubSysRelaConfig(SubSysRelaQueryInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<SubSysRelaQueryInput, BaseResponseData<SubSysRelaQueryOutput?>>(input, AppCenter.Purchase_SubSysRela, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 生成购货修订订单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<BaseResponseData<int?>> GenerateAdvancePaymentRevise(List<PurchaseDetailsInput> input)
        {
            return await InvokeMethodWithQueryObjectAsync<List<PurchaseDetailsInput>, BaseResponseData<int?>>(input, AppCenter.Purchase_GenerateAdvancePaymentRevise, RequestMethodEnum.POST);
        }

        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }

        protected override string GetAppId()
        {
            return AppCenter.Purchase_APPID;
        }
    }
}
