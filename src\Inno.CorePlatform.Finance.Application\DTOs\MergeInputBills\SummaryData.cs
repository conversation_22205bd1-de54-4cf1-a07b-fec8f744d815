﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills
{
    /// <summary>
    /// 合计数据
    /// </summary>
    public class SummaryData
    {
        /// <summary>
        /// 数量合计
        /// </summary>
        public decimal TotalQuantity { get; set; }

        /// <summary>
        /// 匹配数量合计
        /// </summary>
        public decimal TotalMatchQuantity { get; set; }

        /// <summary>
        /// 不含税金额合计
        /// </summary>
        public decimal TotalNoTaxAmount { get; set; }

        /// <summary>
        /// 含税金额合计
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 税额合计
        /// </summary>
        public decimal TotalTaxAmount { get; set; }

        /// <summary>
        /// 匹配金额合计
        /// </summary>
        public decimal TotalMatchedAmount { get; set; }
    }
}
