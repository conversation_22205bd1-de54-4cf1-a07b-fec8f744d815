#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:7.0 AS base
WORKDIR /app
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build

COPY ["src/Inno.CorePlatform.Finance.Backend/Inno.CorePlatform.Finance.Backend.csproj", "src/Inno.CorePlatform.Finance.Backend/"]

COPY ["src/Inno.CorePlatform.Finance.Domain/", "/src/Inno.CorePlatform.Finance.Domain/"]
COPY ["src/Inno.CorePlatform.Finance.Adapter/", "/src/Inno.CorePlatform.Finance.Adapter/"]
COPY ["src/Inno.CorePlatform.Finance.Application/", "/src/Inno.CorePlatform.Finance.Application/"]
COPY ["src/Inno.CorePlatform.Finance.Backend/", "/src/Inno.CorePlatform.Finance.Backend/"]

WORKDIR "/src/Inno.CorePlatform.Finance.Backend"
ARG NUGET_FEED_ACCESSTOKEN
RUN dotnet nuget add source https://ado.innostic.com/dc/CorePlatform/_packaging/innostic-core/nuget/v3/index.json --name innostic-core --valid-authentication-types basic --store-password-in-clear-text --username "innostic-core" --password ${NUGET_FEED_ACCESSTOKEN}


RUN dotnet restore "Inno.CorePlatform.Finance.Backend.csproj"
RUN dotnet build "Inno.CorePlatform.Finance.Backend.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Inno.CorePlatform.Finance.Backend.csproj" -c Release -o /app/publish /p:UseAppHost=false
RUN cp "/docxml/dto.xml" "/app/publish"
RUN cp "/docxml/backend.xml" "/app/publish"

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Inno.CorePlatform.Finance.Backend.dll"]