﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.IC
{

    public class GetRefundStoreInDetailOutput
    {
        public string storeInCode { get; set; }
        public string saleRefundCode { get; set; }
        public string saleOutCode { get; set; }
        public string tempInventoryCode { get; set; }
        public string tmallOriginalCode { get; set; }
        public DateTime saleRefundDate { get; set; }
        public string companyId { get; set; }
        public string customerId { get; set; }
        public List<RefundStoreInDetail> items { get; set; }
    }

    public class RefundStoreInDetail
    {
        public string productId { get; set; }
        public int quantity { get; set; }
        public decimal price { get; set; }
        public decimal consumerPrice { get; set; }
        public decimal platformCouponPrice { get; set; }
        public DateTime? produceDate { get; set; }
        public DateTime? validDate { get; set; }
        public string lotNo { get; set; }
        public string barCode { get; set; }
        public string sn { get; set; }
        public Guid? TempInventoryDetailId { get; set; }
    }
    public class ApplyCodeOutput
    {
        public string RedReverseConsumeCode { get; set; }

        public string ApplyCode { get; set; }
    }
}

