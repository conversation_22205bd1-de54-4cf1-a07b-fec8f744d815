using System.Text;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.Helpers
{
    /// <summary>
    /// 合并进项票辅助类 - 处理debt查询和发票金额计算
    /// </summary>
    public class MergeInputBillHelper
    {
        private readonly FinanceDbContext _db;
        private readonly ILogger<MergeInputBillHelper> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="logger">日志记录器</param>
        public MergeInputBillHelper(FinanceDbContext db, ILogger<MergeInputBillHelper> logger)
        {
            _db = db;
            _logger = logger;
        }

        #region Debt查询相关方法

        /// <summary>
        /// 根据关联单号查询应付单
        /// </summary>
        /// <param name="relateCodes">关联单号列表</param>
        /// <param name="includeZeroValue">是否包含金额为0的应付单</param>
        /// <param name="excludeAutoTypes">排除的自动类型列表</param>
        /// <returns>应付单列表</returns>
        public async Task<List<DebtPo>> GetDebtsByRelateCodesAsync(
            List<string> relateCodes,
            bool includeZeroValue = false,
            List<string> excludeAutoTypes = null)
        {
            if (relateCodes == null || !relateCodes.Any())
            {
                return new List<DebtPo>();
            }

            // 构建查询条件
            var query = _db.Debts.Where(p => relateCodes.Contains(p.RelateCode));

            // 是否包含金额为0的应付单
            if (!includeZeroValue)
            {
                query = query.Where(p => p.Value != 0);
            }

            // 排除特定的自动类型
            if (excludeAutoTypes != null && excludeAutoTypes.Any())
            {
                query = query.Where(p => !excludeAutoTypes.Contains(p.AutoType));
            }

            // 按关联单号和金额绝对值排序
            var result = await query
                .OrderBy(p => p.RelateCode)
                .ThenByDescending(p => Math.Abs(p.Value))
                .ToListAsync();

            _logger.LogInformation("GetDebtsByRelateCodesAsync - 查询应付单, 关联单号数量: {RelateCodeCount}, 查询结果数量: {ResultCount}",
                relateCodes.Count, result.Count);

            return result;
        }

        /// <summary>
        /// 获取应付单已占用金额
        /// </summary>
        /// <param name="debtIds">应付单ID列表</param>
        /// <param name="excludeMergeInputBillId">需要排除的合并进项发票ID（可选）</param>
        /// <returns>应付单ID与已占用金额的字典</returns>
        public async Task<Dictionary<Guid, decimal>> GetDebtUsedAmountsAsync(List<Guid> debtIds, Guid? excludeMergeInputBillId = null)
        {
            if (debtIds == null || !debtIds.Any())
            {
                return new Dictionary<Guid, decimal>();
            }

            // 构建查询条件
            var query = _db.MergeInputBillDebts.Where(d => debtIds.Contains(d.DebtId));

            // 如果指定了需要排除的合并进项发票ID，则排除该ID的记录
            if (excludeMergeInputBillId.HasValue && excludeMergeInputBillId.Value != Guid.Empty)
            {
                query = query.Where(d => d.MergeInputBillId != excludeMergeInputBillId.Value);
                _logger.LogInformation("GetDebtUsedAmountsAsync - 排除合并进项发票ID: {ExcludeMergeInputBillId}", excludeMergeInputBillId.Value);
            }

            // 查询已占用的应付金额
            var existingDebts = await query.ToListAsync();

            _logger.LogInformation("GetDebtUsedAmountsAsync - 查询已占用的应付金额, 应付单ID数量: {DebtIdCount}, 已占用记录数量: {ExistingCount}",
                debtIds.Count, existingDebts.Count);

            // 计算每个应付单已占用的金额
            var debtUsedAmounts = existingDebts
                .GroupBy(d => d.DebtId)
                .ToDictionary(g => g.Key, g => g.Sum(d => d.DebtAmount));

            return debtUsedAmounts;
        }

        /// <summary>
        /// 计算应付单可占用金额
        /// </summary>
        /// <param name="debt">应付单</param>
        /// <param name="debtUsedAmounts">应付单已占用金额字典</param>
        /// <returns>可占用金额</returns>
        public decimal CalculateAvailableAmount(DebtPo debt, Dictionary<Guid, decimal> debtUsedAmounts, string logPrefix = "")
        {
            if (debt == null)
            {
                return 0;
            }

            // 获取已占用金额
            decimal usedAmount = 0;
            if (logPrefix == "ProcessPurchaseRevision")
            {
                usedAmount = 0;//购货修订不计算占用金额
            }
            else if (debtUsedAmounts.TryGetValue(debt.Id, out decimal amount))
            {
                usedAmount = amount;
            }

            // 计算可用金额 = 应付单金额 - 已占用金额
            decimal availableAmount = Math.Abs(debt.Value) - Math.Abs(usedAmount);

            // 检查可用金额是否小于0
            if (availableAmount < 0)
            {
                throw new ApplicationException($"应付单 {debt.BillCode} 的可用金额计算结果为 ({availableAmount})，已占用金额 ({usedAmount}) 超过了应付单金额 ({debt.Value})");
            }

            return availableAmount;
        }

        /// <summary>
        /// 分配应付单金额
        /// </summary>
        /// <param name="debtList">应付单列表</param>
        /// <param name="totalAmount">需要分配的总金额</param>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="logPrefix">日志前缀</param>
        /// <param name="isNegativeAmount">是否为负数金额</param>
        /// <param name="businessCode">业务单号</param>
        /// <returns>合并进项发票关联应付列表</returns>
        public async Task<List<MergeInputBillDebtPo>> AllocateDebtAmountsAsync(
            List<DebtPo> debtList,
            decimal totalAmount,
            Guid mergeInputBillId,
            string logPrefix,
            bool isNegativeAmount = false,
            string businessCode = null)
        {
            var result = new List<MergeInputBillDebtPo>();

            if (debtList.Count == 0 || totalAmount == 0) return result;

            // 获取应付单ID列表
            var debtIds = debtList.Select(d => d.Id).ToList();

            // 获取已占用金额，排除当前合并进项发票ID的占用
            var debtUsedAmounts = await GetDebtUsedAmountsAsync(debtIds, mergeInputBillId);

            _logger.LogInformation("{LogPrefix} - 分配应付单金额, 应付单数量: {DebtCount}, 总金额: {TotalAmount}, 是否负数金额: {IsNegativeAmount}",
                logPrefix, debtList.Count, totalAmount, isNegativeAmount);

            // 剩余需要分配的金额
            decimal remainingAmount = Math.Abs(totalAmount);
            //提示到具体的应付单号
            StringBuilder matchMessage = new StringBuilder();
            matchMessage.AppendLine($"业务单号：{businessCode}，勾稽明细金额：{totalAmount}");
            if (debtList.Count <= 0)
            {
                matchMessage.AppendLine("未找到对应的应付单");
            }
            // 遍历应付单进行金额分配
            foreach (var debt in debtList)
            {
                if (remainingAmount <= 0)
                {
                    break;
                }

                // 计算当前应付单可用金额
                decimal availableAmount = CalculateAvailableAmount(debt, debtUsedAmounts, logPrefix);

                if (availableAmount <= 0)
                {
                    matchMessage.AppendLine($"应付单:{debt.BillCode},可用金额为{availableAmount}");
                    continue;
                }

                // 计算本次分配金额
                decimal allocateAmount = Math.Min(availableAmount, remainingAmount);

                // 如果是负数金额，需要将分配金额转为负数
                decimal finalAllocateAmount = isNegativeAmount ? -allocateAmount : allocateAmount;

                // 创建合并进项发票关联应付记录
                var mergeInputBillDebt = new MergeInputBillDebtPo
                {
                    Id = Guid.NewGuid(),
                    MergeInputBillId = mergeInputBillId,
                    DebtId = debt.Id,
                    DebtCode = debt.BillCode,
                    DebtAmount = finalAllocateAmount,
                    CreatedBy = "System",
                    CreatedTime = DateTimeOffset.Now
                };

                result.Add(mergeInputBillDebt);

                // 更新剩余金额
                remainingAmount -= allocateAmount;

                _logger.LogInformation("{LogPrefix} - 分配应付单金额, 应付单号: {DebtCode}, 可用金额: {AvailableAmount}, 分配金额: {AllocateAmount}, 剩余金额: {RemainingAmount}",
                    logPrefix, debt.BillCode, availableAmount, finalAllocateAmount, remainingAmount);
                matchMessage.AppendLine($"应付单号: {debt.BillCode},分配金额: {finalAllocateAmount}");
            }

            // 如果还有剩余金额未分配，记录警告日志并抛出异常
            if (remainingAmount > 0 && logPrefix != "ProcessPurchaseRevision")
            {
                // 计算已分配的总金额
                decimal allocatedAmount = totalAmount - remainingAmount;

                // 获取所有应付单的可分配总金额
                decimal totalAvailableAmount = debtList.Sum(d => CalculateAvailableAmount(d, debtUsedAmounts));

                // 针对经销入库、经销调出、寄售转购货、换货转退货，如果勾稽金额大于应付金额在1元以内的，不做校验
                bool isToleratedBusinessType = logPrefix == "ProcessDistributionPurchase" ||
                                             logPrefix == "ProcessDistributionTransfer" ||
                                             logPrefix == "ProcessConsignmentToPurchase" ||
                                             logPrefix == "ProcessExchangeToReturn";

                if (isToleratedBusinessType && remainingAmount <= 1)
                {
                    _logger.LogInformation("{LogPrefix} - 勾稽金额大于应付金额在1元以内，允许通过，业务单号: {BusinessCode}, 勾稽明细金额: {TotalAmount}, 应付单可分配总金额: {TotalAvailableAmount}, 剩余未分配金额: {RemainingAmount}",
                        logPrefix, businessCode, totalAmount, totalAvailableAmount, remainingAmount);
                    return result;
                }

                // 记录详细日志，但不在错误消息中显示
                _logger.LogWarning("{LogPrefix} - 应付单金额分配不足，业务单号: {BusinessCode}, 勾稽明细金额: {TotalAmount}, 已分配金额: {AllocatedAmount}, 应付单可分配总金额: {TotalAvailableAmount}, 剩余未分配金额: {RemainingAmount}",
                    logPrefix, businessCode, totalAmount, allocatedAmount, totalAvailableAmount, remainingAmount);

                throw new ApplicationException(matchMessage.ToString());
            }

            return result;
        }

        #endregion

        #region 发票金额计算相关方法

        /// <summary>
        /// 计算匹配金额
        /// </summary>
        /// <param name="businessType">业务类型</param>
        /// <param name="matchQuantity">匹配数量</param>
        /// <param name="taxCost">含税单价</param>
        /// <returns>匹配金额</returns>
        public decimal CalculateMatchedAmount(int businessType, decimal matchQuantity, decimal taxCost)
        {
            // 对于购货修订、服务费和损失确认，直接返回匹配数量（实际上是金额）
            if (businessType == (int)BusinessType.PurchaseRevision ||
                businessType == (int)BusinessType.ServiceFeeProcurement ||
                businessType == (int)BusinessType.LossRecognition)
            {
                return matchQuantity;
            }

            // 对于其他业务类型，返回含税单价 * 匹配数量
            return matchQuantity != 0 ? Math.Round(taxCost * Math.Abs(matchQuantity), 4) : 0;
        }

        /// <summary>
        /// 计算税额
        /// </summary>
        /// <param name="noTaxAmount">不含税金额</param>
        /// <param name="taxRate">税率</param>
        /// <returns>税额</returns>
        public decimal CalculateTaxAmount(decimal noTaxAmount, decimal taxRate)
        {
            return Math.Round(noTaxAmount * (taxRate / 100), 4);
        }

        /// <summary>
        /// 计算不含税金额
        /// </summary>
        /// <param name="totalAmount">含税金额</param>
        /// <param name="taxRate">税率</param>
        /// <returns>不含税金额</returns>
        public decimal CalculateNoTaxAmount(decimal totalAmount, decimal taxRate)
        {
            return Math.Round(totalAmount / (1 + taxRate / 100), 4);
        }

        /// <summary>
        /// 计算含税金额
        /// </summary>
        /// <param name="noTaxAmount">不含税金额</param>
        /// <param name="taxAmount">税额</param>
        /// <returns>含税金额</returns>
        public decimal CalculateTotalAmount(decimal noTaxAmount, decimal taxAmount)
        {
            return Math.Round(noTaxAmount + taxAmount, 4);
        }

        /// <summary>
        /// 根据业务类型判断是否为特殊业务类型（购货修订、服务费或损失确认）
        /// </summary>
        /// <param name="businessType">业务类型</param>
        /// <returns>是否为特殊业务类型</returns>
        public bool IsSpecialBusinessType(int businessType)
        {
            return businessType == (int)BusinessType.PurchaseRevision ||
                   businessType == (int)BusinessType.ServiceFeeProcurement ||
                   businessType == (int)BusinessType.LossRecognition;
        }

        #endregion

        #region 发票号相关方法

        /// <summary>
        /// 根据合并单号获取原始发票号列表
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>原始发票号列表</returns>
        public async Task<List<string>> GetOriginalInvoiceNumbersByMergeIdAsync(Guid mergeInputBillId)
        {
            try
            {
                _logger.LogInformation("GetOriginalInvoiceNumbersByMergeIdAsync - 开始查询原始发票号, MergeInputBillId: {MergeInputBillId}", mergeInputBillId);

                // 查询合并进项发票与原始进项发票的关系
                var relations = await _db.MergeInputBillRelations
                    .Where(x => x.MergeInputBillId == mergeInputBillId)
                    .Include(x => x.InputBill)
                    .OrderBy(x => x.InputBill.InvoiceNumber)
                    .ToListAsync();

                _logger.LogInformation("GetOriginalInvoiceNumbersByMergeIdAsync - 找到关联关系数量: {Count}", relations.Count);

                // 获取原始发票号列表
                var invoiceNumbers = relations
                    .Where(x => x.InputBill != null)
                    .Select(x => x.InputBill.InvoiceNumber)
                    .Where(x => !string.IsNullOrEmpty(x))
                    .Distinct()
                    .ToList();

                _logger.LogInformation("GetOriginalInvoiceNumbersByMergeIdAsync - 找到原始发票号数量: {Count}", invoiceNumbers.Count);

                return invoiceNumbers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetOriginalInvoiceNumbersByMergeIdAsync - 查询原始发票号异常, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                    mergeInputBillId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 根据合并单号获取原始发票号（以逗号分隔的字符串形式）
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>以逗号分隔的原始发票号字符串</returns>
        public async Task<string> GetOriginalInvoiceNumbersStringByMergeIdAsync(Guid mergeInputBillId)
        {
            var invoiceNumbers = await GetOriginalInvoiceNumbersByMergeIdAsync(mergeInputBillId);
            return string.Join(",", invoiceNumbers);
        }

        /// <summary>
        /// 根据合并发票号获取原始发票号列表
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>原始发票号列表</returns>
        public async Task<List<string>> GetOriginalInvoiceNumbersByMergeNumberAsync(string mergeInvoiceNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(mergeInvoiceNumber))
                {
                    _logger.LogWarning("GetOriginalInvoiceNumbersByMergeNumberAsync - 合并发票号为空");
                    return [];
                }

                _logger.LogInformation("GetOriginalInvoiceNumbersByMergeNumberAsync - 开始查询原始发票号, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInvoiceNumber);

                // 先查询合并进项发票
                var mergeInputBill = await _db.MergeInputBills
                    .FirstOrDefaultAsync(x => x.MergeInvoiceNumber == mergeInvoiceNumber);

                if (mergeInputBill == null)
                {
                    _logger.LogWarning("GetOriginalInvoiceNumbersByMergeNumberAsync - 未找到合并进项发票, MergeInvoiceNumber: {MergeInvoiceNumber}", mergeInvoiceNumber);
                    return [];
                }

                // 使用已有方法获取原始发票号列表
                return await GetOriginalInvoiceNumbersByMergeIdAsync(mergeInputBill.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetOriginalInvoiceNumbersByMergeNumberAsync - 查询原始发票号异常, MergeInvoiceNumber: {MergeInvoiceNumber}, 错误: {ErrorMessage}",
                    mergeInvoiceNumber, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 根据合并发票号获取原始发票号（以逗号分隔的字符串形式）
        /// </summary>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <returns>以逗号分隔的原始发票号字符串</returns>
        public async Task<string> GetOriginalInvoiceNumbersStringByMergeNumberAsync(string mergeInvoiceNumber)
        {
            var invoiceNumbers = await GetOriginalInvoiceNumbersByMergeNumberAsync(mergeInvoiceNumber);
            return string.Join(",", invoiceNumbers);
        }

        #endregion
    }
}
