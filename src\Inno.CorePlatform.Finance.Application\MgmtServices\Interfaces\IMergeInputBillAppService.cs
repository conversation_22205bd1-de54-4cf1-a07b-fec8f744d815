﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.MergeInputBills;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 合并进项发票应用服务接口
    /// </summary>
    public interface IMergeInputBillAppService
    {
        /// <summary>
        /// 创建合并进项发票
        /// </summary>
        /// <param name="request">创建合并进项发票请求</param>
        /// <returns>创建合并进项发票响应</returns>
        Task<CreateMergeInputBillResponse> CreateMergeInputBill(CreateMergeInputBillRequest request);

        /// <summary>
        /// 查询合并进项发票列表
        /// </summary>
        /// <param name="request">查询合并进项发票列表请求</param>
        /// <returns>查询合并进项发票列表响应</returns>
        Task<QueryMergeInputBillListResponse> QueryMergeInputBillList(QueryMergeInputBillListRequest request);

        /// <summary>
        /// 还原合并进项发票
        /// </summary>
        /// <param name="request">还原合并进项发票请求</param>
        /// <returns>操作结果</returns>
        Task<bool> RestoreMergeInputBill(RestoreMergeInputBillRequest request);

        /// <summary>
        /// 提交勾稽结果
        /// </summary>
        /// <param name="request">提交勾稽结果请求</param>
        /// <returns>操作结果</returns>
        Task<bool> SubmitMatch(SubmitMatchRequest request);

        /// <summary>
        /// 获取合并进项发票明细
        /// </summary>
        /// <param name="request">获取合并进项发票明细请求</param>
        /// <returns>合并进项发票明细分页数据（带合计）</returns>
        Task<PagedDataWithSummary<MergeInputBillDetailItem>> GetMergeInputBillDetail(GetMatchedDetailsRequest request);

        /// <summary>
        /// 开始异步匹配
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>操作结果</returns>
        Task<BaseResponseData<bool>> StartAsyncMatch(StartAsyncMatchRequest request);

        /// <summary>
        /// 处理匹配任务
        /// </summary>
        /// <param name="request">开始异步匹配请求</param>
        /// <returns>处理结果</returns>
        Task ProcessMatchTask(StartAsyncMatchRequest request);

        /// <summary>
        /// 撤销勾稽结果
        /// </summary>
        /// <param name="request">撤销勾稽结果请求</param>
        /// <returns>操作结果</returns>
        Task<bool> RevokeMatch(RevokeMatchRequest request);

        /// <summary>
        /// 取消已提交勾稽
        /// </summary>
        /// <param name="request">取消已提交勾稽请求</param>
        /// <returns>操作结果</returns>
        Task<bool> CancelSubmittedMatch(CancelSubmittedMatchRequest request);

        /// <summary>
        /// 保存勾稽结果
        /// </summary>
        /// <param name="request">保存勾稽结果请求</param>
        /// <returns>操作结果</returns>
        Task<bool> SaveMatch(SaveMatchRequest request);

        /// <summary>
        /// 删除匹配明细
        /// </summary>
        /// <param name="request">删除匹配明细请求</param>
        /// <returns>操作结果</returns>
        Task<bool> DeleteMatch(DeleteMatchRequest request);

        /// <summary>
        /// 获取缓存中的明细数据
        /// </summary>
        /// <param name="request">获取缓存中的明细数据请求</param>
        /// <returns>缓存中的明细数据</returns>
        Task<GetCacheDetailsResponse> GetCacheDetails(GetCacheDetailsRequest request);

        /// <summary>
        /// 获取待提交的明细
        /// </summary>
        /// <param name="request">获取待提交的明细请求</param>
        /// <returns>待提交的明细分页数据（带合计）</returns>
        Task<PagedDataWithSummary<SubmitDetailItem>> GetSubmitDetails(GetSubmitDetailsRequest request);

        /// <summary>
        /// 获取发票金额统计
        /// </summary>
        /// <param name="request">获取发票金额统计请求</param>
        /// <returns>发票金额统计响应</returns>
        Task<GetInvoiceAmountStatisticsResponse> GetInvoiceAmountStatistics(GetInvoiceAmountStatisticsRequest request);

        /// <summary>
        /// 获取匹配查询条件
        /// </summary>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <returns>匹配查询条件</returns>
        Task<GetMatchableDocumentsRequest> GetMatchQueryCondition(Guid mergeInputBillId);

        /// <summary>
        /// 获取缓存中的匹配条件
        /// </summary>
        /// <param name="request">获取缓存中的匹配条件请求</param>
        /// <returns>缓存中的匹配条件</returns>
        Task<GetCachedMatchConditionResponse> GetCachedMatchCondition(GetCachedMatchConditionRequest request);
    }
}
