﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.LossRecognition
{
    public class CreateLossRecognitionInput
    {
        public Guid? Id { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }

        /// <summary>
        /// 应收类型
        /// </summary>
        public CreditTypeEnum CreditType { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 用户
        /// </summary>
        public string? CurrentUser { get; set; }
        /// <summary>
        /// 核算部门Id路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 核算部门简写
        /// </summary>
        public string? BusinessDeptShortName { get; set; }
        /// <summary>
        /// 操作类型,更新update，新增 insert
        /// </summary>
        public string? OperateType { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 附件id
        /// </summary>
        public string? AttachFileIds { get; set; }

        public List<LossRecognitionDetail> LossRecognitionDetail { get; set; }
    }
    public class LossRecognitionDetail
    {
        /// <summary>
        /// 明细类型
        /// </summary>
        public LossRecognitionDetailTypeEnum Classify { get; set; }

        /// <summary>
        /// 明细单据日期（应收日期、应付日期）
        /// </summary>
        [Comment("明细单据日期（应收日期、应付日期）")]
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 明细单据号（应收日期、应付日期）
        /// </summary>
        [Comment("明细单据号（应收日期、应付日期）")]
        public string BillCode { get; set; }

        /// <summary>
        /// 应付对应应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 明细金额（应收金额、应付金额）
        /// </summary>
        [Comment("明细金额（应收金额、应付金额）")]
        public decimal Value { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        [Comment("已冲销金额")]
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        [Comment("余额")]
        public decimal LeftAmount { get; set; }

        /// <summary>
        /// 确认坏账金额
        /// </summary>
        [Comment("确认坏账金额")]
        public decimal? BadAmount { get; set; }

        [Comment("终端医院Id")]

        public string? HospitalId { get; set; }

        [Comment("终端医院")]
        public string? HospitalName { get; set; }

        /// <summary>
        /// 项目单号
        /// </summary>

        [Comment("项目单号")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 已使用金额（已入票金额）
        /// </summary>
        [Comment("已使用金额（已入票金额）")]
        public decimal? InvoicedAmount { get; set; }
    }
}
