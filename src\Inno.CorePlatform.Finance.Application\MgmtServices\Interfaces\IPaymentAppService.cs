﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Payments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    public interface IPaymentAppService
    {
        Task<BaseResponseData<int>> AbatementPayment(KingdeeAbatementPaymentInput input);
        Task<BaseResponseData<decimal>> ChangeByKingdeeAsync(List<KingdeePaymentInput> input);
        Task<BaseResponseData<string>> CreateNotProducerCodePayment(PaymentInputOfKd input);
        Task<BaseResponseData<int>> CreditPay(KindeeCreditPayInput input);
        Task<BaseResponseData<int>> FundDelete(FundDeleteInput input);
        Task<Payment> GetById(Guid id);
        Task<BaseResponseData<int>> ReturnAbatementDebt(KingdeeAbatementDebtInput input);
    }
}
