﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Sell.Application.Extensions
{

    /// <summary>
    /// TextJson GUID 序列化大写
    /// </summary>
    public class TextJsonGuidConverter : JsonConverter<Guid>
    {
        /// <summary>
        /// Read
        /// </summary>
        /// <param name="reader"></param>
        /// <param name="typeToConvert"></param>
        /// <param name="options"></param>
        /// <returns></returns>
        public override Guid Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return Guid.Empty;
            }
            if (reader.TokenType != JsonTokenType.String)
            {
                throw new JsonException($"Unexpected token type: {reader.TokenType}");
            }

            if (!Guid.TryParse(reader.GetString(), out Guid value))
            {
                throw new JsonException($"Invalid GUID format: {reader.GetString()}");
            }
            return value;
        }

        /// <summary>
        /// Write
        /// </summary>
        /// <param name="writer"></param>
        /// <param name="value"></param>
        /// <param name="options"></param>
        public override void Write(Utf8JsonWriter writer, Guid value, JsonSerializerOptions options)
        {
            if (!value.Equals(Guid.Empty))
            {
                writer.WriteStringValue(value.ToString("D").ToUpper());
            }
            else
            {
                writer.WriteNullValue();
            }
        }
    }


    /// <summary>
    /// 
    /// </summary>
    public class TextJsonListGuidConverter : JsonConverter<List<Guid>>
    {
        public override List<Guid> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType != JsonTokenType.StartArray)
            {
                throw new JsonException();
            }

            List<Guid> guidList = new List<Guid>();

            while (reader.Read())
            {
                if (reader.TokenType == JsonTokenType.EndArray)
                {
                    return guidList;
                }
                if (reader.TokenType != JsonTokenType.String)
                {
                    throw new JsonException();
                }
                guidList.Add(reader.GetGuid());
            }

            throw new JsonException();
        }

        public override void Write(Utf8JsonWriter writer, List<Guid> value, JsonSerializerOptions options)
        {
            writer.WriteStartArray();

            foreach (Guid guid in value)
            {
                writer.WriteStringValue(guid.ToString("D").ToUpper());
            }
            writer.WriteEndArray();
        }
    }
}
