﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.UC;
using Microsoft.AspNetCore.Mvc;
using NPOI.POIFS.Crypt.Dsig.Facets;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.PortInterfaces.Clients
{
    public interface IUCApiClient
    {
        Task<List<GetUserListByEmployeeIdsOutput>> GetUserListByEmployeeIds(List<string> input);
    }
}
