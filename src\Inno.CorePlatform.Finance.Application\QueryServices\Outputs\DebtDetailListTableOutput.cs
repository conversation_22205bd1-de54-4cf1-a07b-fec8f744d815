﻿using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using StackExchange.Redis;
using System.ComponentModel.DataAnnotations;
using System.Data;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs

{
    public class DebtDetailListTableOutput
    {
        /// <summary>
        /// 全部-1
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 待审核1
        /// </summary>
        public int WaitAuditCount { get; set; }
        /// <summary>
        /// 已拒绝66
        /// </summary>
        public int RefuseCount { get; set; }
        /// <summary>
        /// 已完成99
        /// </summary>
        public int ComplateCount { get; set; }
        /// <summary>
        /// 我的5000
        /// </summary>
        public int MyCount { get; set; }

    }
}
