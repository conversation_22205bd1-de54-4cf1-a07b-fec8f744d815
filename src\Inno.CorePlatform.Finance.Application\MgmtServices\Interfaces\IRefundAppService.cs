﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Refund;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces
{
    /// <summary>
    /// 退款申请
    /// </summary>
    public interface IRefundAppService
    {
        /// <summary>
        /// 保存退款申请单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> SaveRefund(RefundSaveInput input);

        /// <summary>
        /// 提交退款到金蝶
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<BaseResponseData<SaveOrUpdateRefundOutput>> SubmitKingdee(RefundSubmitInput input);
        /// <summary>
        /// 更新退款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        Task<BaseResponseData<int>> UpdateRefund(RefundSaveInput input, RefundStatusEnum status);
        /// <summary>
        /// 更改退款状态
        /// </summary>
        /// <param name="input"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        Task<BaseResponseData<int>> UpdateRefundState(RefundStateSynchronizationInput input); 

         Task<BaseResponseData<int>> DeleteRefund(Guid id);
    }
}
