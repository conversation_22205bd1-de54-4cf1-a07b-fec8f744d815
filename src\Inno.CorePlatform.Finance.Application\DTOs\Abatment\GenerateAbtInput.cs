﻿using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Abatment
{
    public class GenerateAbtInput
    {
        /// <summary>
        /// 应付付款计划明细Id
        /// </summary>
        public Guid DebtDetilId { get; set; }

        /// <summary>
        /// 核心业务平台公司全称
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 付款单号
        /// </summary>
        public string PaymentCode { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 执行人账号
        /// </summary>
        public string UserName { get; set; }


        /// <summary>
        /// 支付类型（现金支付，承兑发票）
        /// </summary> 
        public string? PayClassify { get; set; }
    }
}
