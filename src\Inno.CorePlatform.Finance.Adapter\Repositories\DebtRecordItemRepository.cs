﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class DebtRecordItemRepository : EfBaseRepository<Guid, DebtRecordItem, DebtRecordItemPo>, IDebtRecordItemRepository
    {
        private FinanceDbContext _db;
        public DebtRecordItemRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<int> InsetrCredit(DebtRecordItem DebtRecordItem, List<DebtRecordDetail> DebtRecordDetail)
        {
            var DebtRecordItemPo = DebtRecordItem.Adapt<DebtRecordItemPo>();
            var DebtRecordDetailPo = DebtRecordDetail.Adapt<List<DebtRecordDetailPo>>();
            DebtRecordItemPo.DebtRecordDetail = DebtRecordDetailPo;
            _db.DebtRecordItems.Add(DebtRecordItemPo);
            return await _db.SaveChangesAsync();
        }

        public override async Task<int> UpdateAsync(DebtRecordItem root)
        {
            var isExist = await _db.DebtRecordItems.AnyAsync(x => x.Id == root.Id);
            if (isExist)
            {
                var po = root.Adapt<DebtRecordItemPo>();
                _db.DebtRecordItems.Update(po);
                if (UowJoined) return 0;
                return await _db.SaveChangesAsync();
            }
            else
            {
                return 0;
            }
        }

        protected override DebtRecordItemPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<DebtRecordItemPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
