﻿using Dapr.Client;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class SellApiClient : BaseDaprApiClient<SellApiClient>, ISellApiClient
    {
        public SellApiClient(DaprClient daprClient, ILogger<SellApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }
        public async Task<SaleOutput> GetTempSaleByCodeAsync(string code)
        {
            return await InvokeMethodAsync<SaleOutput>(string.Format(AppCenter.Sell_TempSaleGetByCode, code), RequestMethodEnum.POST);
        }

        public async Task<PageResponse<PageQueryForFinancesOutput>> PageQueryForFinancesAsync(PageQueryForFinancesInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<PageQueryForFinancesInput, PageResponse<PageQueryForFinancesOutput>>(input, AppCenter.Sell_PageQueryForFinances, RequestMethodEnum.POST);
        }
        public async Task<List<CustomerProductBillingOutput>> GetCustomerProductBillingAsync(CustomerProductBillingInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<CustomerProductBillingInput, List<CustomerProductBillingOutput>>(input, AppCenter.Sell_GetCustomerProductBilling, RequestMethodEnum.POST);
        }

        public async Task<List<PreSaleListForProjectOutput>> GetPreSaleListForProjectAsync(QueryPreSaleListForProjectInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<QueryPreSaleListForProjectInput, List<PreSaleListForProjectOutput>>(input, AppCenter.Sell_GetPreSaleListForProject, RequestMethodEnum.POST);
        }

        /// <summary>
        /// 财务对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> SaleForReconciliation(ReconciliationInput input)
        {
            try
            {
                //4是暂存核销，5是订单修订,6是跟台核销
                input.BillType = 4;
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.Sell_PageQueryForReconciliation, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用销售暂存核销-查询收入成本对账失败");
            }
        }


        /// <summary>
        /// 财务对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> SaleForReconciliation_revise(ReconciliationInput input)
        {
            try
            {
                ///4是暂存核销，5是订单修订,6是跟台核销
                input.BillType = 5;
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.Sell_PageQueryForReconciliation, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用销售订单修订-查询收入成本对账失败");
            }
        }

        /// <summary>
        /// 财务对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> SaleForReconciliation_follow(ReconciliationInput input)
        {
            try
            {
                ///4是暂存核销，5是订单修订,6是跟台核销
                input.BillType = 6;
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.Sell_PageQueryForReconciliation, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用销售订单修订-查询收入成本对账失败");
            }
        }

        /// <summary>
        /// 财务对账
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ReconciliationOutput>> SaleForReconciliation_service(ReconciliationInput input)
        {
            try
            {
                ///4是暂存核销，5是订单修订,6是跟台核销,7是服务订单
                input.BillType = 7;
                return await InvokeMethodWithQueryObjectAsync<ReconciliationInput, List<ReconciliationOutput>>(input, AppCenter.Sell_PageQueryForReconciliation, RequestMethodEnum.POST);
            }
            catch (Exception)
            {

                throw new Exception("调用销售订单修订-查询收入成本对账失败");
            }
        }

        public async Task<List<SaleOutput>> GetSaleList(GetSaleListInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<GetSaleListInput, List<SaleOutput>>(input, AppCenter.Sell_GetSaleList, RequestMethodEnum.POST);
        }
        public async Task<List<TempInventoryDetailOutput>> GetTempDetailsBySaleDetailIds(GetTempDetailsBySaleDetailIdsInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<GetTempDetailsBySaleDetailIdsInput, List<TempInventoryDetailOutput>>(input, AppCenter.Sell_GetTempDetailsBySaleDetailIds, RequestMethodEnum.POST);
        }

        public async Task<List<RebateProvisionOfSaleOutput>> GetRebateProvision(RebateProvisionOfSaleInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<RebateProvisionOfSaleInput, List<RebateProvisionOfSaleOutput>>(input, AppCenter.Sell_GetRebateProvisionOfSale, RequestMethodEnum.POST);
        }

        public async Task<SaleOutput> GetSaleByCode(string code)
        {
            return await InvokeMethodAsync<SaleOutput>(string.Concat(AppCenter.Sell_GetSaleByCode,"?code=", code), RequestMethodEnum.POST);
        }
        public async Task<List<SaleAdvancePeriodInfoOutput>> GetSaleAdvancePeriodInfo(SaleAdvancePeriodInfoInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<SaleAdvancePeriodInfoInput, List<SaleAdvancePeriodInfoOutput>>(input, AppCenter.Sell_GetSaleAdvancePeriodInfo, RequestMethodEnum.POST);
        }
        /// <summary>
        /// 查询销售明细(财务)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<GetSaleDetailsForFinanceOutput>> GetSaleDetailsForFinanceAsync(GetSaleDetailsForFinanceInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<GetSaleDetailsForFinanceInput, List<GetSaleDetailsForFinanceOutput>>(input, AppCenter.Sell_GetSaleDetailsForFinance, RequestMethodEnum.POST);
        }
        protected override string GetAppId()
        {
            return AppCenter.Sell_APPID;
        }
    }
}
