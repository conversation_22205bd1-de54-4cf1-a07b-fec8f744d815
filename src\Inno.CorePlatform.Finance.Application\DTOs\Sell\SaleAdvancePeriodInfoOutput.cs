﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    /// <summary>
    /// 获取销售订单预收账期信息
    /// </summary>
    public class SaleAdvancePeriodInfoOutput 
    {

        /// <summary>
        /// 订单ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 是否有预收账期
        /// </summary>
        public bool HasAdvancePeriod { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public SaleStatusEnum Status { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public SaleTypeEnum SaleType { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription
        {
            get
            {
                return this.Status.GetDescription();
            }
        }
    }
}
