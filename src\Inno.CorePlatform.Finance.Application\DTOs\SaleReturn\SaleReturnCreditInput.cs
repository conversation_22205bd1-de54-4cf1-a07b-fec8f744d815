using System;
using System.Collections.Generic;
using Inno.CorePlatform.Finance.Application.DTOs.IC;
using Inno.CorePlatform.Finance.Application.DTOs.InputBills;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Domain;
using Mapster;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.Application.DTOs.SaleReturn
{
    /// <summary>
    /// 销售调回应收输入参数
    /// </summary>
    public class SaleReturnCreditInput
    {
        /// <summary>
        /// 公司ID
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 入库申请ID
        /// </summary>
        public Guid? ApplyId { get; set; }

        /// <summary>
        /// 入库单号
        /// </summary>
        public string? StoreInCode { get; set; }

        /// <summary>
        /// 入库日期
        /// </summary>
        public long StoreInDate { get; set; }

        /// <summary>
        /// 入库人
        /// </summary>
        public string? StoreInBy { get; set; }

        /// <summary>
        /// 发货者ID
        /// </summary>
        public Guid? ShipperId { get; set; }

        /// <summary>
        /// 发货者名称
        /// </summary>
        public string? ShipperName { get; set; }

        /// <summary>
        /// SPD发票编号
        /// </summary>
        public string? SpdInvoiceCode { get; set; }

        /// <summary>
        /// 业务部门ID
        /// </summary>
        public string? BusinessDeptId { get; set; }

        /// <summary>
        /// 业务部门全名
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 业务部门全路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        public string? Remark { get; set; }

        /// <summary>
        /// 入库明细
        /// </summary>
        public List<StoreInDetailOutput> StoreInDetails { get; set; } = new List<StoreInDetailOutput>();

        /// <summary>
        /// 业务分类
        /// </summary>
        public string Classify { get; set; }

        /// <summary>
        /// 请求体（用于日志记录）
        /// </summary>
        public string RequestBody { get; set; }

        /// <summary>
        /// 是否使用单据日期
        /// </summary>
        public bool UseBillDate { get; set; } = false;

        /// <summary>
        /// 是否自动生成单据
        /// </summary>
        public bool IsAutoBill { get; set; } = false;

        /// <summary>
        /// 销售退货单号
        /// </summary>
        public string saleRefundCode { get; set; }

        /// <summary>
        /// 销售出库单号
        /// </summary>
        public string saleOutCode { get; set; }

        /// <summary>
        /// 暂存单号
        /// </summary>
        public string tempInventoryCode { get; set; }

        /// <summary>
        /// 天猫原始单号
        /// </summary>
        public string tmallOriginalCode { get; set; }
        /// <summary>
        /// 是否需要生成应收
        /// </summary>
        public bool IsNeedCreateReceivable { get; set; } = true;
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
        public int? RelateCodeType { get;  set; }

        /// <summary>
        /// 从入库信息和事件总线DTO创建销售调回应收输入参数
        /// </summary>
        /// <param name="storein">入库信息</param>
        /// <param name="input">事件总线DTO</param>
        /// <returns>销售调回应收输入参数</returns>
        public static SaleReturnCreditInput FromStoreIn(InventoryStoreInOutput storein, EventBusDTO input)
        {
            return new SaleReturnCreditInput
            {
                CompanyId = storein.companyId,
                ApplyId = storein.applyId,
                StoreInCode = storein.storeInCode,
                StoreInDate = storein.storeInDate,
                StoreInBy = storein.storeInBy,
                ShipperId = storein.shipperId,
                ShipperName = storein.shipperName,
                SpdInvoiceCode = storein.spdInvoiceCode,
                BusinessDeptId = storein.businessDeptId,
                BusinessDeptFullName = storein.businessDeptFullName,
                BusinessDeptFullPath = storein.businessDeptFullPath,
                RelateCode = storein.relateCode,
                Remark = storein.remark,
                StoreInDetails = storein.storeInDetails,
                Classify = input.BusinessSubType,
                RequestBody = JsonConvert.SerializeObject(input),
                UseBillDate = input.useBillDate.HasValue && input.useBillDate.Value,
                IsAutoBill = input.IsAutoBill.HasValue && input.IsAutoBill.Value,
                RelateCodeType= storein.relateCodeType
            };
        }

        /// <summary>
        /// 从退货入库详情创建销售调回应收输入参数的基本方法
        /// </summary>
        /// <param name="refundStoreInDetail">退货入库详情</param>
        /// <param name="input">事件总线DTO</param>
        /// <returns>销售调回应收输入参数</returns>
        internal static SaleReturnCreditInput FromRefundStore(InventoryStoreInOutput storein, EventBusDTO input)
        {
            // 创建基础的SaleReturnCreditInput对象
            return new SaleReturnCreditInput
            {
                CompanyId = storein.companyId,
                ApplyId = storein.applyId,
                StoreInCode = storein.storeInCode,
                StoreInDate = storein.storeInDate,
                StoreInBy = storein.storeInBy,
                ShipperId = storein.shipperId,
                ShipperName = storein.shipperName,
                SpdInvoiceCode = storein.spdInvoiceCode,
                BusinessDeptId = storein.businessDeptId,
                BusinessDeptFullName = storein.businessDeptFullName,
                BusinessDeptFullPath = storein.businessDeptFullPath,
                RelateCode = storein.relateCode,
                Remark = storein.remark,
                Classify = input.BusinessSubType,
                RequestBody = JsonConvert.SerializeObject(input),
                UseBillDate = input.useBillDate.HasValue && input.useBillDate.Value,
                IsAutoBill = input.IsAutoBill.HasValue && input.IsAutoBill.Value,
                StoreInDetails = storein.storeInDetails.Adapt<List<StoreInDetailOutput>>()
            };
        }

        /// <summary>
        /// 从退货入库详情创建销售调回应收输入参数，使用消费者价格
        /// </summary>
        /// <param name="refundStoreInDetail">退货入库详情</param>
        /// <param name="input">事件总线DTO</param>
        /// <returns>销售调回应收输入参数</returns>
        internal static SaleReturnCreditInput FromRefundStoreWithConsumerPrice(InventoryStoreInOutput storein, GetRefundStoreInDetailOutput refundStoreInDetail, EventBusDTO input)
        {
            var result = FromRefundStore(storein, input);
            result.tempInventoryCode = refundStoreInDetail.tempInventoryCode;
            result.CreditSaleSubType = CreditSaleSubTypeEnum.personal;
            var addStoreInDetailList = new List<StoreInDetailOutput>();
            // 使用消费者价格创建入库明细,已退货明细为准，然后找对应的入库明细，如果没有不管，因为可能在拆分的入库单中
            if (refundStoreInDetail.items != null && refundStoreInDetail.items.Count > 0)
            {
                var refundStoreInDetailList = refundStoreInDetail.items.ToList();
                foreach (var item in refundStoreInDetailList)
                {
                    //todo: 取对应销售明细对应的退货明细价格，因为可以会对应同货号价格拆分的情况,需要改成saledetailId的判断条件
                    var originalDetail = result.StoreInDetails.FirstOrDefault(w => w.relateId == item.TempInventoryDetailId);
                    if (originalDetail != null)
                    {
                        var storeInDetail = originalDetail.Adapt<StoreInDetailOutput>();
                        storeInDetail.quantity = item.quantity;
                        storeInDetail.price = item.consumerPrice;
                        addStoreInDetailList.Add(storeInDetail);//如果没有对应的入库明细，不生成应收
                    }
                }
                result.StoreInDetails = addStoreInDetailList;
            }
            return result;
        }

        /// <summary>
        /// 从退货入库详情创建销售调回应收输入参数，使用平台优惠价格
        /// </summary>
        /// <param name="refundStoreInDetail">退货入库详情</param>
        /// <param name="input">事件总线DTO</param>
        /// <returns>销售调回应收输入参数</returns>
        internal static SaleReturnCreditInput FromRefundStoreWithPlatformCouponPrice(InventoryStoreInOutput storein, GetRefundStoreInDetailOutput refundStoreInDetail, EventBusDTO input)
        {
            var result = FromRefundStore(storein, input);
            result.tempInventoryCode = refundStoreInDetail.tempInventoryCode;
            result.CreditSaleSubType = CreditSaleSubTypeEnum.platform;
            var addStoreInDetailList = new List<StoreInDetailOutput>();
            // 使用消费者价格创建入库明细，红包金额，应该根据对应的货号求和
            if (refundStoreInDetail.items != null && refundStoreInDetail.items.Count > 0)
            {
                var groupRefundStoreInDetail = refundStoreInDetail.items.GroupBy(g => g.productId).Select(s => new
                {
                    productId = s.Key,
                    quantity = 1,
                    platformCouponPrice = s.Sum(s => s.platformCouponPrice * s.quantity)
                }).ToList();
                foreach (var item in groupRefundStoreInDetail)
                {
                    var storeInDetail = result.StoreInDetails.FirstOrDefault(w => string.Equals(w.productId.ToString(), item.productId, StringComparison.InvariantCultureIgnoreCase));
                    if (storeInDetail != null)
                    {
                        storeInDetail.quantity = item.quantity;
                        storeInDetail.price = item.platformCouponPrice;
                        addStoreInDetailList.Add(storeInDetail);
                    }
                }
                result.StoreInDetails = addStoreInDetailList;
                if (result.StoreInDetails.Sum(s => s.price) == 0)
                {
                    result.IsNeedCreateReceivable = false;
                }
            }
            return result;
        }
    }
}
