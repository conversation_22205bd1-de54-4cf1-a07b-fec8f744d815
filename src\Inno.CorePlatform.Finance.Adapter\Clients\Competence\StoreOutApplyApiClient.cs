﻿using Dapr.Client;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.Equipments;
using Inno.CorePlatform.Finance.Application.DTOs.StoreInApply;
using Inno.CorePlatform.Finance.Application.DTOs.StoreOutApply;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class StoreOutApplyApiClient : BaseDaprApiClient<StoreOutApplyApiClient>, IStoreOutApplyApiClient
    {
        public StoreOutApplyApiClient(DaprClient daprClient, ILogger<StoreOutApplyApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        public async Task<StoreOutApplyOutput> GetById(Guid Id)
        {
            return await InvokeMethodAsync<StoreOutApplyOutput>(string.Format(AppCenter.StoreOutApply_GetById, Id), RequestMethodEnum.GET);
        }

        public async Task<BaseResponseData<StoreOutApplyListDto>> GetStoreOutApplyList(StoreOutApplyInput input)
        {
            return await InvokeMethodWithQueryObjectAsync<StoreOutApplyInput, BaseResponseData<StoreOutApplyListDto>>(input, AppCenter.StoreOutApply_GetStoreOutApplyList, RequestMethodEnum.POST);
        }

        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            return res;
        }

        protected override string GetAppId()
        {
            return AppCenter.StoreOutApply_APPID;
        }

    }
}
