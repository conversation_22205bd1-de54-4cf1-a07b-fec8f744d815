﻿namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 冲销查询，入参
    /// </summary>
    public class AbatementQueryInput : BaseQuery
    {

        /// <summary>
        /// 被冲销的单据号(应付单号)
        /// </summary>

        public string? DebtBillCode { get; set; }

        /// <summary>
        /// 冲销的单据号(应收单号)
        /// </summary>
        public string? CreditBillCode { get; set; }

        /// <summary>
        /// 被冲销的单据类型
        /// </summary>
        public string? DebtType { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 是否过滤损失确认单
        /// </summary>
        public bool? FilterLoss { get; set; }
    }
}
