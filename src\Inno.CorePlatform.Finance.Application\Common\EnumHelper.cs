﻿using System.ComponentModel;
using System.Reflection;

namespace Inno.CorePlatform.Finance.Application.Common
{
    public static class EnumHelper
    {
        /// <summary>
        /// 根据描述特性字符串获取枚举值
        /// </summary>
        /// <typeparam name="TEnum"></typeparam>
        /// <param name="value"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static TEnum GetEnumByValue<TEnum>(string value) where TEnum : struct, Enum
        {
            foreach (var field in typeof(TEnum).GetFields(BindingFlags.Public | BindingFlags.Static))
            {
                var attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
                if (attribute != null && attribute.Description == value)
                {
                    return (TEnum)field.GetValue(null);
                }
            }

            throw new ArgumentException($"没有找到与描述 '{value}' 匹配的 {typeof(TEnum).Name} 枚举值。");
        }
    }
}
