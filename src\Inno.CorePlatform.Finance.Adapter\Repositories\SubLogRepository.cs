﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    //public class SubLogRepository : EfBaseRepository<Guid, SubLog, SubLogPo>, ISubLogRepository
    //{
    //    private FinanceDbContext db;
    //    public SubLogRepository(FinanceDbContext dbContext) : base(dbContext)
    //    {
    //        db = dbContext;
    //    }
    //    public override Task<int> UpdateAsync(SubLog root)
    //    {
    //        throw new NotImplementedException();
    //    }

    //    protected override SubLogPo CreateDeletingPo(Guid id)
    //    {
    //        throw new NotImplementedException();
    //    }

    //    protected override Task<SubLogPo> GetPoWithIncludeAsync(Guid id)
    //    {
    //        throw new NotImplementedException();
    //    }
    //}
}
