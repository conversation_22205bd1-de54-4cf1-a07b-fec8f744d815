﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;

namespace Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice
{
    public class SaveCustomizeInvoiceInput
    {
        public List<OriginDetailOutput> DetailList { get; set; }
        public List<OriginDetailOutput> OriginDetailList { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }
        /// <summary>
        /// 销售应收子类型
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; } = CreditSaleSubTypeEnum.platform;
        /// <summary>
        /// 0=更据应收开票全额，1=根据应收明细开票部分
        /// </summary>
        public int? Source { get; set; } = 0;
    }


    public class KingdeeCustomizeInvoiceInput
    {
        /// <summary>
        /// 申开单号
        /// </summary>
        public string billNo { get; set; }
        public bool isPass { get; set; }
    }

    public class KingdeeCancelCustomizeInvoiceInput
    {
        /// <summary>
        /// 申开单号
        /// </summary>
        public string billNo { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        public string userName { get; set; }
    }

    /// <summary>
    /// 初始应收导入模板
    /// </summary>
    public class InitCreditExport
    {
        /// <summary>
        /// 初始应收单号
        /// </summary>
        public string? InitCreditNo { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Value { get; set; }


        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 税收分类编码税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 行性质（商品行、折扣行）
        /// </summary>
        public string? Tag { get; set; }
    }

    /// <summary>
    /// 根据初始应收单号分组
    /// </summary>
    public class GroupByInitCreditNoDto
    {
        /// <summary>
        /// 初始应收单号
        /// </summary>
        public string? InitCreditNo { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal? TotalValue { get; set; }
        /// <summary>
        /// 已冲销金额
        /// </summary>
        public decimal? TotalUsedAmount { get; set; }
    }
}
