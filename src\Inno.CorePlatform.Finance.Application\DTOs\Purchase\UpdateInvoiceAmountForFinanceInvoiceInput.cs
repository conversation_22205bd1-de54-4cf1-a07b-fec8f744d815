using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Application.DTOs.Purchase
{
    /// <summary>
    /// 更新购货修订入票金额（进项票）输入参数
    /// </summary>
    public class UpdateInvoiceAmountForFinanceInvoiceInput
    {
        /// <summary>
        /// 勾稽合并单号
        /// </summary>
        public string invoiceNumber { get; set; }

        /// <summary>
        /// 是否入票 默认true 入票 false 取消入票
        /// </summary>
        public bool InvoiceFlag { get; set; } = true;

        /// <summary>
        /// 更新明细列表
        /// </summary>
        public List<UpdateInvoiceAmountForFinanceInput> List { get; set; }
    }

    /// <summary>
    /// 更新购货修订入票金额（进项票）
    /// </summary>
    public class UpdateInvoiceAmountForFinanceInput
    {
        /// <summary>
        /// 购货修订Code
        /// </summary>
        public string PurchaseOrderCode { get; set; }

        /// <summary>
        /// 入票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 品名ID
        /// </summary>
        public string ProductNameId { get; set; }

        /// <summary>
        /// 品名
        /// </summary>
        public string ProductName { get; set; }
    }
}
