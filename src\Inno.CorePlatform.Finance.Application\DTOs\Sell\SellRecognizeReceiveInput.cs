﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Sell
{
    public class SellRecognizeReceiveInput
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string billCode { get; set; }
        /// <summary>
        /// 收款单号
        /// </summary>
        public string receiptCode { get; set; }
        public string companyId { get; set; }
        public string customerId { get; set; }
        public string userName { get; set; }
        /// <summary>
        /// 认款单号
        /// </summary>
        public string recognizeCode { get; set; }
        public decimal amount { get; set; }
    }


    public class SellsDetailPageData 
    {
        public List<SellsDetailOutputDto> Data { get; set; }
    }

    public class SellsDetailOutputDto 
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 订单Id
        /// </summary>
        public Guid? SaleId { get; set; }


        /// <summary>
        /// 订单Id
        /// </summary>
        public string? BillCode { get; set; }

        /// <summary>
        /// 业务单元编号
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 业务单元名称
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Code
        /// </summary>
        public string? ProjectCode { get; set; }

        

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid ProductId { get; set; }

        
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        
        /// <summary>
        /// 优惠后单价
        /// </summary>
        public decimal Price { get; set; }

    }

    /// <summary>
    /// 根据销售子系统id查询公司返回
    /// </summary>
    public class SaleSystemCompanyOutput
    {
        public Guid? id { get; set; }
        public string? billCode { get; set; }
        public string? name { get; set; }
        public Guid? companyId { get; set; }
        public string? companyName { get; set; }
    }

    /// <summary>
    /// 销售子系统返回封装
    /// </summary>
    public class SaleSystemBaseOutput
    {
        public List<SaleSystemCompanyOutput>? list { get; set; }
    }

    /// <summary>
    /// 获取销售子系统列表（主要查公司）
    /// </summary>
    public class SaleSystemCompanyQueryInput
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        /// <summary>
        /// 数量
        /// </summary>
        public int PageSize { get; set; } = 20;
        /// <summary>
        /// 销售子系统id集合
        /// </summary>
        public List<Guid>? Ids { get; set; }
        /// <summary>
        /// 是否跳过策略
        /// </summary>
        public bool Strategyless { get; set; } = true;
    }
}
