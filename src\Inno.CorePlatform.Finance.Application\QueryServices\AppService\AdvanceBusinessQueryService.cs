﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Reconciliation;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using Org.BouncyCastle.Crypto.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{

    /// <summary>
    /// 垫资查询服务
    /// </summary>
    public class AdvanceBusinessQueryService : IAdvanceBusinessQueryService
    {
        private IBaseAllQueryService<AdvanceBusinessApplyPO> _baseAdvanceBusinessApplyQueryService;
        private IBaseAllQueryService<AdvanceBusinessDetailPO> _baseAdvanceBusinessDetailQueryService;
        private IPCApiClient _pCApiClient;
        public AdvanceBusinessQueryService(IBaseAllQueryService<AdvanceBusinessApplyPO> baseAdvanceBusinessApplyQueryService, IPCApiClient pCApiClient,
                                           IBaseAllQueryService<AdvanceBusinessDetailPO> baseAdvanceBusinessDetailQueryService)
        {
            _baseAdvanceBusinessApplyQueryService = baseAdvanceBusinessApplyQueryService;
            _pCApiClient = pCApiClient;
            _baseAdvanceBusinessDetailQueryService = baseAdvanceBusinessDetailQueryService;
        }


        public async Task<(List<AdvanceBusinessOutput>, int)> GetAdvanceList(AdvanceBusinessInput input)
        {
            var query = _baseAdvanceBusinessApplyQueryService.GetIQueryable(p => true);
            #region 查询条件
            if (!string.IsNullOrEmpty(input.searchKey))
            {
                query = query.Where(p => EF.Functions.Like(p.Code, $"%{input.searchKey}%") ||
                                         EF.Functions.Like(p.ServiceName, $"%{input.searchKey}%") ||
                                         EF.Functions.Like(p.CompanyName, $"%{input.searchKey}%") ||
                                         EF.Functions.Like(p.HospitalName, $"%{input.searchKey}%"));
            }
            if (!string.IsNullOrEmpty(input.Code))
            {
                query = query.Where(p => p.Code == input.Code);
            }
            if (!string.IsNullOrEmpty(input.BillDateS) && !string.IsNullOrEmpty(input.BillDateE))
            {
                query = query.Where(p => p.BillDate >= input.DateS && p.BillDate <= input.DateE);
            }
            if (input.Status.HasValue && input.Status > 0)
            {
                query = query.Where(p => p.Status == input.Status);
            }
            var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
            var strategys = await _pCApiClient.GetStrategyAsync(strategryquery);
            if (!strategys.RowStrategies.Keys.Contains("company"))
            {
                return (new List<AdvanceBusinessOutput> { }, 0);
            }
            if (input.CompanyId.HasValue)
            {
                query = query.Where(p => p.CompanyId == input.CompanyId.Value);
            }
            else
            {
                var companys = strategys.RowStrategies.Where(p => p.Key == "company").Select(p => p.Value).ToList();
                var strCompanyIds = new List<string>();
                foreach (var c in companys)
                {
                    strCompanyIds.AddRange(c);
                }
                if (strCompanyIds.Count != 0 && !strCompanyIds.Any(p => p == "@all"))
                {
                    var companyIds = strCompanyIds.Select(Guid.Parse).ToList();
                    query = query.Where(p => companyIds.Contains(p.CompanyId));
                }
            }
            if (input.ServiceId.HasValue)
            {
                query = query.Where(p => p.ServiceId == input.ServiceId.Value);
            }
            if (input.CustomerId.HasValue)
            {
                query = query.Where(p => p.HospitalId == input.CustomerId.Value);
            }
            else
            {
                var customers = strategys.RowStrategies.Where(p => p.Key == "customer").Select(p => p.Value).ToList();
                var strCustomersIds = new List<string>();
                foreach (var c in customers)
                {
                    strCustomersIds.AddRange(c);
                }
                if (strCustomersIds.Count != 0 && !strCustomersIds.Any(p => p == "@all"))
                {
                    var custmerIds = strCustomersIds.Select(Guid.Parse).ToList();
                    query = query.Where(p => custmerIds.Contains(p.HospitalId));
                }
            }
            #endregion

            var total = await query.CountAsync();
            var res = await query.Skip((input.page - 1) * input.limit).Take(input.limit).Select(t => t.Adapt<AdvanceBusinessOutput>()).ToListAsync();
            return (res, total);
        }

        public async Task<(List<AdvanceBusinessDetailOutput>, int)> GetAdvanceDetails(AdvanceBusinessInput input)
        {
            var query = _baseAdvanceBusinessDetailQueryService.GetIQueryable(p => true, new List<string>() { "AdvanceBusinessApply" });
            if (input.AdvanceBusinessApplyId.HasValue)
            {
                query = query.Where(p => p.AdvanceBusinessApplyId == input.AdvanceBusinessApplyId);
            }
            else
            {
                if (!string.IsNullOrEmpty(input.searchKey))
                {
                    query = query.Where(p => EF.Functions.Like(p.AdvanceBusinessApply.Code, $"%{input.searchKey}%") ||
                                             EF.Functions.Like(p.AdvanceBusinessApply.ServiceName, $"%{input.searchKey}%") ||
                                             EF.Functions.Like(p.AdvanceBusinessApply.CompanyName, $"%{input.searchKey}%") ||
                                             EF.Functions.Like(p.AdvanceBusinessApply.HospitalName, $"%{input.searchKey}%"));
                }
                if (!string.IsNullOrEmpty(input.Code))
                {
                    query = query.Where(p => p.AdvanceBusinessApply.Code == input.Code);
                }
                if (!string.IsNullOrEmpty(input.BillDateS) && !string.IsNullOrEmpty(input.BillDateE))
                {
                    query = query.Where(p => p.AdvanceBusinessApply.BillDate >= input.DateS && p.AdvanceBusinessApply.BillDate <= input.DateE);
                }
                var strategryquery = new StrategyQueryInput() { userId = input.UserId, functionUri = "metadata://fam" };
                var strategys = await _pCApiClient.GetStrategyAsync(strategryquery);
                if (input.CompanyId.HasValue)
                {
                    query = query.Where(p => p.AdvanceBusinessApply.CompanyId == input.CompanyId.Value);
                }
                else
                {
                    var companys = strategys.RowStrategies.Where(p => p.Key == "company").Select(p => p.Value).ToList();
                    var strCompanyIds = new List<string>();
                    foreach (var c in companys)
                    {
                        strCompanyIds.AddRange(c);
                    }
                    if (strCompanyIds.Count != 0 && !strCompanyIds.Any(p => p == "@all"))
                    {
                        var companyIds = strCompanyIds.Select(Guid.Parse).ToList();
                        query = query.Where(p => companyIds.Contains(p.AdvanceBusinessApply.CompanyId));
                    }
                }
                if (input.ServiceId.HasValue)
                {
                    query = query.Where(p => p.AdvanceBusinessApply.ServiceId == input.ServiceId.Value);
                }
                if (input.CustomerId.HasValue)
                {
                    query = query.Where(p => p.AdvanceBusinessApply.HospitalId == input.CustomerId.Value);
                }
                else
                {
                    var customers = strategys.RowStrategies.Where(p => p.Key == "customer").Select(p => p.Value).ToList();
                    var strCustomersIds = new List<string>();
                    foreach (var c in customers)
                    {
                        strCustomersIds.AddRange(c);
                    }
                    if (strCustomersIds.Count != 0 && !strCustomersIds.Any(p => p == "@all"))
                    {
                        var custmerIds = strCustomersIds.Select(Guid.Parse).ToList();
                        query = query.Where(p => custmerIds.Contains(p.AdvanceBusinessApply.HospitalId));
                    }
                }
            }

            var count = await query.CountAsync();
            var res = await query.Select(p => new AdvanceBusinessDetailOutput
            {
                Code = p.AdvanceBusinessApply.Code,
                AccountPeriod = p.AccountPeriod,
                ADFDiscount = p.ADFDiscount,
                AdvanceBusinessApplyId = p.AdvanceBusinessApplyId,
                BaseDiscount = p.BaseDiscount,
                BillDate = p.AdvanceBusinessApply.BillDate,
                CompanyId = p.AdvanceBusinessApply.CompanyId,
                CompanyName = p.AdvanceBusinessApply.CompanyName,
                CreditCode = p.CreditCode,
                CreditDate = p.CreditDate,
                CreditId = p.CreditId,
                CreditValue = p.CreditValue,
                DebtCode = p.DebtCode,
                DebtDate = p.DebtDate,
                DebtDetailId = p.DebtDetailId,
                DebtValue = p.DebtValue,
                Discount = p.Discount,
                EndDateTime = p.AdvanceBusinessApply.EndDateTime,
                ExpectAmount = p.AdvanceBusinessApply.ExpectAmount,
                ExpectedAmount = p.AdvanceBusinessApply.ExpectedAmount,
                ExpectInterestAmount = p.AdvanceBusinessApply.ExpectInterestAmount,
                ExpectPaymentDate = p.ExpectPaymentDate,
                ExpectReceiveDate = p.ExpectReceiveDate,
                HospitalId = p.AdvanceBusinessApply.HospitalId,
                HospitalName = p.AdvanceBusinessApply.HospitalName,
                Id = p.Id,
                InvoiceDate = p.InvoiceDate,
                IsInvoice = p.AdvanceBusinessApply.IsInvoice,
                IsTakeOver = p.AdvanceBusinessApply.IsTakeOver,
                IsVerify = p.AdvanceBusinessApply.IsVerify,
                NonVerifyRemark = p.AdvanceBusinessApply.NonVerifyRemark,
                PaymentCode = p.PaymentCode,
                PaymentDate = p.PaymentDate,
                PreTimeOutReceivableAmount = p.AdvanceBusinessApply.PreTimeOutReceivableAmount,
                PreUnrecycledReceivableAmount = p.AdvanceBusinessApply.PreUnrecycledReceivableAmount,
                ProvidePayDays = p.AdvanceBusinessApply.ProvidePayDays,
                RateOfYear = p.AdvanceBusinessApply.RateOfYear,
                Ratio = p.AdvanceBusinessApply.Ratio,
                RealSupplyChainDiscounts = p.AdvanceBusinessApply.RealSupplyChainDiscounts,
                RealUseDays = p.AdvanceBusinessApply.RealUseDays,
                ReceivableAmountOfNon = p.AdvanceBusinessApply.ReceivableAmountOfNon,
                ReceivableAmountOfTimeout = p.AdvanceBusinessApply.ReceivableAmountOfTimeout,
                ReceiveCode = p.ReceiveCode,
                ReceiveDate = p.ReceiveDate,
                ReceivePeriod = p.ReceivePeriod,
                ReturnMoneyDays = p.AdvanceBusinessApply.ReturnMoneyDays,
                TimeOutReceivableAmount = p.AdvanceBusinessApply.TimeOutReceivableAmount,
                SalesTaxRate = p.SalesTaxRate,
                SalesVolume = p.AdvanceBusinessApply.SalesVolume,
                SCFDiscount = p.SCFDiscount,
                ServiceId = p.AdvanceBusinessApply.ServiceId??Guid.Empty,
                ServiceName = p.AdvanceBusinessApply.ServiceName??"",
                ServiceGroup = p.AdvanceBusinessApply.ServiceGroup,
                UnrecycledReceivableAmount = p.AdvanceBusinessApply.UnrecycledReceivableAmount,
                TotalDiscounts = p.AdvanceBusinessApply.TotalDiscounts,
                TotalAmountOfMonth = p.AdvanceBusinessApply.TotalAmountOfMonth,
                TotalAmount = p.AdvanceBusinessApply.TotalAmount,
                SupplyChainDiscounts = p.AdvanceBusinessApply.SupplyChainDiscounts,
                SPDDiscount = p.SPDDiscount,
            }).OrderByDescending(p => p.Code).Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();

            return (res, count);
        }

        public async Task<List<AdvanceFundBusinessDetailGroupOutput>> GetAdvanceDetailGroup(Guid advanceBusinessApplyId)
        {
            var input = new AdvanceBusinessInput() { AdvanceBusinessApplyId = advanceBusinessApplyId, limit = 1000000, page = 1 };
            var details = await GetAdvanceDetails(input);
            var res = details.Item1.GroupBy(t => t.OverdueStatus)
                                   .Select(t => new AdvanceFundBusinessDetailGroupOutput
                                   {
                                       OverdueStatus = t.Key,
                                       OverdueInterest = Math.Round(t.Sum(i => i.OverdueInterest), 2),
                                       AdvanceReceiveInterest = Math.Round(t.Sum(i => i.AdvanceReceiveInterest), 2),
                                       OccupyFundBalance = Math.Round(t.Sum(i => i.OccupyFundBalance), 2),
                                       BasicProfit = Math.Round(t.Sum(i => i.BasicProfit), 2),
                                       AdvanceInterest = Math.Round(t.Sum(i => i.AdvanceInterest), 2),
                                       TotalProfit = Math.Round(t.Sum(i => i.TotalProfit), 2)
                                   }).ToList();
            return res;
        }

    }
}
