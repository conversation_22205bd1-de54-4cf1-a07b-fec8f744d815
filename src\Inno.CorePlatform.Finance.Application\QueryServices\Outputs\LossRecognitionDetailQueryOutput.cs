﻿using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class LossRecognitionDetailQueryOutput
    {
        /// <summary>
        /// 详情id
        /// </summary>
        public Guid? Id{ get; set; }
        /// <summary>
        /// 损失确认Id
        /// </summary>
        public Guid LossRecognitionItemId { get; set; }

        /// <summary>
        /// 明细类型
        /// </summary>
        public LossRecognitionDetailTypeEnum Classify { get; set; }

        /// <summary>
        /// 明细单据日期（应收日期、应付日期）
        /// </summary> 
        [Comment("明细单据日期（应收日期、应付日期）")]
        public DateTime BillDate { get; set; }


        /// <summary>
        /// 明细单据号（应收日期、应付日期）
        /// </summary>
        [Comment("明细单据号（应收日期、应付日期）")]
        public string BillCode { get; set; }

        /// <summary>
        /// 明细金额（应收金额、应付金额）
        /// </summary>
        [Comment("明细金额（应收金额、应付金额）")]
        public decimal Value { get; set; }

        /// <summary>
        /// 已冲销金额
        /// </summary>
        [Comment("已冲销金额")]
        public decimal AbatmentAmount { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        [Comment("余额")]
        public decimal LeftAmount { get; set; }

        /// <summary>
        /// 确认坏账金额
        /// </summary>
        [Comment("确认坏账金额")]
        public decimal? BadAmount { get; set; }
        [Comment("终端医院Id")]

        public string? HospitalId { get; set; }

        [Comment("终端医院")]
        public string? HospitalName { get; set; }

        /// <summary>
        /// 项目单号 
        /// </summary> 

        [Comment("项目单号")]
        public string? ProjectCode { get; set; }

        /// <summary>
        /// 项目名称 
        /// </summary> 

        [Comment("项目名称")]
        public string? ProjectName { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary> 

        [Comment("项目Id")]
        public Guid? ProjectId { get; set; }


        /// <summary>
        /// 客户Id
        /// </summary>
        public Guid? CustomerId { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string? AgentName { get; set; } 

        public string CreditBillCode {  get; set; }
        public Guid? CreditId { get;  set; }
    }
   
}
