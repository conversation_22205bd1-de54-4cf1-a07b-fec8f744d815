﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using NetTopologySuite.Index.HPRtree;
using static Google.Protobuf.WellKnownTypes.Field.Types;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RedConfirmationFormNumberQueryController : BaseController
    {
        private readonly ILogger<PaymentQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly ICreditQueryService _creditQueryService;
        private readonly IApplyBFFService _applyBFFService;
        private readonly ICustomizeInvoiceQueryService _customizeInvoiceQueryService;

        public RedConfirmationFormNumberQueryController(
           ILogger<PaymentQueryController> logger,
           IKingdeeApiClient kingdeeApiClient,
           ICreditQueryService creditQueryService, IApplyBFFService applyBFFService, ICustomizeInvoiceQueryService customizeInvoiceQueryService, ISubLogService subLog) : base(subLog)
        {
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            _creditQueryService = creditQueryService;
            _applyBFFService = applyBFFService;
            _customizeInvoiceQueryService = customizeInvoiceQueryService;
        }

        /// <summary>
        /// 获取红字确认单编号列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<RedConfirmationFormNumberListOutput2>> GetList(DownloadRedConfirmationFormNumberInput input)
        {
            var ret = new ResponseData<RedConfirmationFormNumberListOutput2>();
            try
            {
                //组织编码和授权对应税号二者必填其一
                if (string.IsNullOrEmpty(input.org) && string.IsNullOrEmpty(input.taxNo))
                {
                    ret.Code = -1;
                    ret.Msg = "公司和销方税号二者必填其一";
                    return ret;
                }

                if (!string.IsNullOrEmpty(input.org))
                {
                    var _CompanyList = await _applyBFFService.GetCompanyInfosAsync(new BDSBaseInput() { ids = new List<string> { input.org } });
                    if (!_CompanyList.Any())
                    {
                        ret.Code = -1;
                        ret.Msg = "未找到该公司信息";
                        return ret;
                    }
                    input.org = _CompanyList.FirstOrDefault().NameCode;
                }
                var kdRet = await _kingdeeApiClient.DownloadRedConfirmationFormList(input);

                if (kdRet.Code == CodeStatusEnum.Success)
                {
                    var lst = kdRet.Data.dataList;
                    ret.Data = new Data<RedConfirmationFormNumberListOutput2>
                    {
                        List = lst,
                        Total = kdRet.Data.totalElement,
                    };
                }
                else
                {
                    ret.Code = -1;
                    ret.Msg = kdRet.Message;
                }
                return ret;
            }
            catch (Exception ex)
            {
                ret.Code = -1;
                ret.Msg = ex.Message;
                return ret;
            }
        }

        /// <summary>
        /// 红字确认单生成
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("Generate")]
        public async Task<BaseResponseData> Generate(GenerateRedConfirmationFormNumberInput input)
        {
            var r = await _kingdeeApiClient.Generate(input);
            return r;
        }

        /// <summary>
        /// 根据蓝票号搜索销项发票明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetDetails")]
        public async Task<ResponseData<GenerateRedConfirmationFormNumberItemInput>> GetDetails([FromBody] GenerateRedConfirmationFormNumberInput input)
        {
            var ret = new ResponseData<GenerateRedConfirmationFormNumberItemInput>();
            //组织编码和授权对应税号二者必填其一
            if (string.IsNullOrEmpty(input.invoiceNo))
            {
                return ret;
            }
            InvoiceCreditQueryInput query = new();
            query.page = 1;
            query.limit = 999;
            query.UserId = CurrentUser.Id.Value;
            query.UserName = CurrentUser.UserName;
            query.InvoiceNo = input.invoiceNo;
            query.IsStrategy = false;
            var (list, count) = await _creditQueryService.GetInvoiceCreditListAsync(query);
            var (ciList, ciCount) = await _customizeInvoiceQueryService.GetCustomizeInvoiceDetailByOrderNo(input.orderNo);
            var data = new List<GenerateRedConfirmationFormNumberItemInput>();
            var retLst = new List<GenerateRedConfirmationFormNumberItemInput>();
            int originalSeq = 1;
            foreach (var item in list)
            {
                var ci = ciList.FirstOrDefault(x => x.ProductNo == item.ProductNo);
                var model = new GenerateRedConfirmationFormNumberItemInput();
                model.originalSeq = item.RowNo.HasValue ? item.RowNo : (ci != null ? (string.IsNullOrEmpty(ci.CustomizeInvoiceIndex) ? null : int.Parse(ci.CustomizeInvoiceIndex)) : originalSeq);
                model.price = (item.NoTaxAmountPrice ?? 0).ToString();
                model.priceTax = (item.Price ?? 0).ToString();
                model.units = item.PackUnit;
                model.specification = item.ProductNo;
                model.amount = (0 - Math.Abs(item.TotalNoTaxAmountPrice.Value)).ToString("F2");
                model.lineProperty = string.IsNullOrEmpty(item.ProductNo) && item.Quantity == 0 ? "1" : "0";
                model.goodsName = item.ProductName;
                model.goodsCode = item.ProductNo;
                model.quantity = (0 - Math.Abs(item.Quantity.Value)).ToString();
                model.taxAmount = (0 - Math.Abs(item.TaxAmount.Value)).ToString();
                model.taxRate = string.Format("{0:0.00}", item.TaxRate / 100);
                model.revenueCode = item.TaxTateCodeId ?? "请填写税收分类编码";
                data.Add(model);
                originalSeq++;
            }
            if (data != null && data.Any())
            {
                data = data.OrderBy(x => x.originalSeq).ToList();
                //递归
                for (int i = 0; i < data.Count(); i++)
                {
                    if (data[i].lineProperty == "1")
                    {
                        //与上一行商品行合并
                        var model = data[i - 1].Adapt<GenerateRedConfirmationFormNumberItemInput>();
                        var amount = Convert.ToDecimal(data[i].amount) + Convert.ToDecimal(data[i].taxAmount) - (Convert.ToDecimal(model.amount) + Convert.ToDecimal(model.taxAmount));
                        var taxAmount = getTaxAmount(amount, Convert.ToDecimal(model.taxRate)) ?? 0;
                        model.amount = (0 - Math.Abs(Convert.ToDecimal(data[i].amount) - Convert.ToDecimal(model.amount))).ToString("F2");
                        model.price = Math.Abs(Convert.ToDecimal(model.amount) / Convert.ToDecimal(model.quantity)).ToString();
                        model.taxAmount = (0 - Math.Abs(taxAmount)).ToString("F2");
                        model.priceTax = Math.Abs(amount / Convert.ToDecimal(model.quantity)).ToString();
                        if (i > 0)
                        {
                            retLst.Remove(data[i - 1]);
                        }
                        if (amount != 0)
                        {
                            retLst.Add(model);
                        }
                    }
                    else
                    {
                        retLst.Add(data[i]);
                    }
                }
            }
            ret.Data = new Data<GenerateRedConfirmationFormNumberItemInput>
            {
                List = retLst
            };
            return ret;
        }

        /// <summary>
        /// 根据蓝票号搜索销项发票明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ComputeAmount")]
        public async Task<BaseResponseData<GenerateRedConfirmationFormNumberItemInput>> ComputeAmount([FromBody] ComputeAmountInput input)
        {
            var model = new GenerateRedConfirmationFormNumberItemInput();
            decimal amount = 0M;
            decimal taxamount = 0M;
            if (!string.IsNullOrEmpty(input.quantity) && !string.IsNullOrEmpty(input.priceTax) && !string.IsNullOrEmpty(input.taxRate))
            {
                decimal quantity = 0M;
                if (!Decimal.TryParse(input.quantity, out quantity))
                {
                    return BaseResponseData<GenerateRedConfirmationFormNumberItemInput>.Failed(500, "请输入正确的数量");
                }
                decimal priceTax = 0M;
                if (!Decimal.TryParse(input.priceTax, out priceTax))
                {
                    return BaseResponseData<GenerateRedConfirmationFormNumberItemInput>.Failed(500, "请输入正确的含税单价");
                }
                decimal taxRate = 0M;
                if (!Decimal.TryParse(input.taxRate, out taxRate))
                {
                    return BaseResponseData<GenerateRedConfirmationFormNumberItemInput>.Failed(500, "请输入正确的税率价");
                }
                amount = quantity * priceTax;
                taxamount = getTaxAmount(amount, taxRate) ?? 0;
                model.amount = (amount - taxamount).ToString("F2");
                model.taxAmount = taxamount.ToString("F2");
                model.price = quantity == 0 ? "0" : (priceTax - taxamount / quantity).ToString();
            }
            return BaseResponseData<GenerateRedConfirmationFormNumberItemInput>.Success(model, "计算成功");
        }

        /// <summary>
        /// 获取税额
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="taxRate"></param>
        /// <returns></returns>
        private decimal? getTaxAmount(decimal? amount, decimal? taxRate)
        {
            return amount / (1 + taxRate) * taxRate;
        }
    }
}
