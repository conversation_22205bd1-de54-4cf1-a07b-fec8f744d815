﻿using Inno.CorePlatform.Finance.Application.QueryServices;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.Reconciliation
{
    /// <summary>
    /// 对账入参
    /// </summary>
    public class ReconciliationInput
    {
        /// <summary>
        /// 公司
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 月度如2023-12
        /// </summary>
        public string? SysMonth { get; set; }

        /// <summary>
        /// 订单类型billType：4是暂存核销，5是订单修订,6是跟台核销
        /// </summary>
        public int? BillType { get; set; }

        public Guid ReconciliationItemId { get; set; }
    }
}
