﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class InputBillDetailRepository: EfBaseRepository<Guid, InputBillDetail, InputBillDetailPo>, IInputBillDetailRepository
    {
        private FinanceDbContext db;
        public InputBillDetailRepository(FinanceDbContext dbContext) : base(dbContext)
        {
            db = dbContext;
        }

        public override Task<int> UpdateAsync(InputBillDetail root)
        {
            throw new NotImplementedException();
        }


        public async Task<int> AddRangeAsync(List<InputBillDetail> list)
        {
            var addrang = list.Adapt<List<InputBillDetailPo>>();
            await db.InputBillDetails.AddRangeAsync(addrang);
            if (UowJoined) return 0;
            return await db.SaveChangesAsync();
        }
        protected override InputBillDetailPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<InputBillDetailPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }
    }
}
