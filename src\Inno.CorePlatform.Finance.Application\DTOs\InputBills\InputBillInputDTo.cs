﻿using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.InputBills
{
    public class InputBillDetailDeleteInput
    {
        public List<string>? storeInCodes { get; set; }
        public Guid? inputBillId { get; set; }
        public List<Guid> DetailIds { get; set; }
    }
    public class InputBillInputDTo
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string InvoiceNumber { get; set; }



        /// <summary>
        /// 公司编码
        /// </summary>
        public string? CompanyCode { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public Guid AgentId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string AgentName { get; set; }

        /// <summary>
        /// 开票时间
        /// </summary> 
        public DateTime BillTime { get; set; }

        /// <summary>
        /// 票据类型 1,普票 2，专票   默认普票
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 发票代码
        /// </summary>
        public string InvoiceCode { get; set; }

        /// <summary>
        /// 购买方税号
        /// </summary>
        public string? PurchaseDutyNumber { get; set; } = "暂无";


        /// <summary>
        /// 销售方税号
        /// </summary>
        public string? SaleDutyNumber { get; set; } = "暂无";


        /// <summary>
        /// 金额
        /// </summary> 
        public decimal NotaxAmount { get; set; }

        /// <summary>
        /// 税额=金额*税率
        /// </summary> 
        public decimal TaxAmount { get; set; }
        /// <summary>
        /// 总金额=金额+税额
        /// </summary> 
        public decimal Amount { get; set; }
        /// <summary>
        /// 发票详情
        /// </summary>
        public List<InputBillDetailInputDto> InputBillDetail { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
    public class PloInputilDetail
    {
        public List<StoreInDetaiSubmitInput> input { get; set; }
        public bool IsAdd { get; set; } = false;
        public Guid InputBillId { get; set; }
    }

    public class InputBillDetailInputDto
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 货号
        /// </summary>
        public string ProductNo { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }
        /// <summary>
        /// 含税单价
        /// </summary>
        public decimal TaxCost { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        public decimal NoTaxCost { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal NoTaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public decimal TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxAmount { get; set; }
    }

    public class InputBillImportDto 
    {
        public string CompanyName { get; set; }
        public string InvoiceNo { get; set; }
        public string BillCode { get; set; }
    }

    public class InputBillBatchImportDto
    {
        public string CompanyName { get; set; }
        public string InvoiceNo { get; set; }
        public string BillCode { get; set; }
        public string? ProductNo { get; set; }
        public decimal? Number { get; set; }

        public string? ErrMessage { get; set; }
    }

    /// <summary>
    /// 入票数量辅助校验dto
    /// </summary>
    public class PollCheckDto
    {
        public string? BillCode { get; set; }
        public string? ProductNo { get; set; }
        /// <summary>
        /// 已经抵扣的锁定数量
        /// </summary>
        public decimal Number { get; set; }
    }

    /// <summary>
    /// 入票数量辅助校验dto
    /// </summary>
    public class PollCheckByInvoiceDto
    {
        public string? InvoiceNo { get; set; }
        public string? BillCode { get; set; }
        public string? ProductNo { get; set; }
        /// <summary>
        /// 已经抵扣的锁定数量
        /// </summary>
        public decimal Number { get; set; }
        /// <summary>
        /// 是否已计算占用
        /// </summary>
        public bool IsClear { get; set; }
    }
}
