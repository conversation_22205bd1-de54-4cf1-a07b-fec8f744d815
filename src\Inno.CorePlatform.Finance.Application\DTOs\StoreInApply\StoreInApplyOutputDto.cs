﻿using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.StoreInApply
{
    public class StoreInApplyOutputDto
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = "none";
        /// <summary>
        /// 最后更新人
        /// </summary>
        public string? UpdatedBy { get; set; }
        /// <summary>
        /// 事业部
        /// </summary>
        public BusinessDept BusinessDept { get; set; }
        /// <summary>
        /// 采购订单Id
        /// </summary>
        public PurchaseOrder? PurchaseOrder { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary>
        public Project? Project { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Company Company { get; set; }
        /// <summary>
        /// 业务单元
        /// </summary>
        public Service? Service { get; set; }
        /// <summary>
        /// 供应商
        /// </summary>
        public Agent Agent { get; set; }
        /// <summary>
        /// 厂家信息
        /// </summary>
        public Producer? Producer { get; set; }
    }

    /// <summary>
    /// 入库申请获取
    /// </summary>
    public class StoreInApplyGetInput
    {
        /// <summary>
        /// 
        /// </summary>
        public string code { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int status { get; set; } = -1;
        /// <summary>
        /// 
        /// </summary>
        public List<int> arrivalStatusEnums { get; set; }

    }
    
    public class StoreInApplyGetListOutputDto
    {
        public List<StoreInApplyGetOutputDto> list { get; set; }
    }

    public class StoreInApplyGetOutputDto
    {
        public long billDate { get; set; }
        public long createdTime { get; set; }
        public long updatedTime { get; set; }
        public string returnReason { get; set; }
        public string id { get; set; }
        public string relateCode { get; set; }
        public string relateCodeTypeName { get; set; }
        public string remark { get; set; }
        public int status { get; set; }
        public string createdBy { get; set; }
        public string updatedBy { get; set; }
        public string businessDeptId { get; set; }
        public string businessDeptFullName { get; set; }
        public string businessDeptFullPath { get; set; }
        public string businessDeptShortName { get; set; }
        public string code { get; set; }
        public int type { get; set; }
        public string billFlowDirectionName { get; set; }
        public string displayHouseTag { get; set; }
        public int arrivalStatus { get; set; }
        public int warehousingStatus { get; set; }
        public string applyTypeName { get; set; }
        public string arrivalStatusName { get; set; }
        public string warehousingStatusName { get; set; }
        public string statusName { get; set; }
        public string deliveryStatusName { get; set; }
    }
   
}
