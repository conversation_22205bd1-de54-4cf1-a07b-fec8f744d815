﻿using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;

namespace Inno.CorePlatform.Finance.Application.DTOs.CustomizeInvoice
{
    /// <summary>
    /// 导入明细出参
    /// </summary>
    public class CustomizeInvoiceDetailExcelOutput
    {
        /// <summary>
        /// 总数
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 失败数
        /// </summary>
        public int FailNumber { get; set; }
        /// <summary>
        /// 失败错误信息文件id
        /// </summary>
        public Guid FailReportFileId {  get; set; }
        /// <summary>
        /// 成功数
        /// </summary>
        public int SuccessNumber {  get; set; }

        /// <summary>
        /// 应收集合
        /// </summary>
        public List<CreditQueryListOutput> Credits=new List<CreditQueryListOutput>();

        /// <summary>
        /// 开票明细
        /// </summary>
        public List<OriginDetailOutput> CreditDetails=new List<OriginDetailOutput>();

    }
}
