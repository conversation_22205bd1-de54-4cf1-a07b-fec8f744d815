﻿using Inno.CorePlatform.Common.DDD;
using Inno.CorePlatform.Common.Utility.Expressions;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.DTOs.Debts;
using Inno.CorePlatform.Finance.Application.DTOs.Inventory;
using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Migrations;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.InputBills;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Driver.Linq;
using NetTopologySuite.Index.HPRtree;
using NetTopologySuite.Operation.Relate;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using OfficeOpenXml;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static MongoDB.Driver.WriteConcern;

namespace Inno.CorePlatform.Finance.Application.QueryServices.AppService
{
    /// <summary>
    /// 进项发票查询服务
    /// </summary>
    public class InputBillQueryService : QueryAppService, IInputBillQueryService
    {
        private readonly FinanceDbContext _db;
        private readonly IInventoryApiClient _inventoryApiClient;
        private readonly IBaseAllQueryService<InputBillPo> _queryInputBillPo;
        private readonly IBaseAllQueryService<InputBillDetailPo> _queryInputBillDetailPo;
        private readonly IBaseAllQueryService<InputBillSubmitDetailPo> _queryInputBillSubmitDetailPo;
        private readonly ILogger<InputBillQueryService> _logger;
        private readonly IPCApiClient _pCApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;
        private readonly IBDSApiClient _bDSApiClient;

        public InputBillQueryService(IAppServiceContextAccessor? contextAccessor,
           FinanceDbContext db,
           IBaseAllQueryService<InputBillPo> queryInputBillPo,
           IBaseAllQueryService<InputBillDetailPo> queryInputBillDetailPo,
           IBaseAllQueryService<InputBillSubmitDetailPo> queryInputBillSubmitDetailPo,
           ILogger<InputBillQueryService> logger,
           IPCApiClient pCApiClient,
           IInventoryApiClient inventoryApiClient,
           IBDSApiClient bDSApiClient,
           IPurchaseApiClient purchaseApiClient) :
           base(contextAccessor)
        {
            this._pCApiClient = pCApiClient;
            this._db = db;
            this._inventoryApiClient = inventoryApiClient;
            this._queryInputBillPo = queryInputBillPo;
            this._queryInputBillDetailPo = queryInputBillDetailPo;
            this._queryInputBillSubmitDetailPo = queryInputBillSubmitDetailPo;
            this._logger = logger;
            this._purchaseApiClient = purchaseApiClient;
            _bDSApiClient = bDSApiClient;
        }

        public async Task<(List<InputBillQueryListOut>, int)> GetListInputBillAsync(InputBillQueryInput query)
        {
            Expression<Func<InputBillPo, bool>> exp = z => true;
            var strategyInput = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    return (new List<InputBillQueryListOut>(), 0);
                }
            }
            //exp = exp.And(z => z.Status != 9); //过滤的忽略 状态 9=忽略开票
            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.beginCreatedTime) && !string.IsNullOrWhiteSpace(query.endCreatedTime))
            {
                var end = DateTimeHelper.LongToDateTime(Convert.ToInt64(query.endCreatedTime));
                var start = DateTimeHelper.LongToDateTime(Convert.ToInt64(query.beginCreatedTime));
                exp = exp.And(z => z.BillTime <= end && z.BillTime >= start);
            }
            if (query.Type.HasValue && query.Type > 0)
            {
                exp = exp.And(z => z.Type == query.Type);
            }
            if (query.Status.HasValue && query.Status > 0)
            {
                exp = exp.And(z => z.Status == query.Status);
            }
            if (!string.IsNullOrWhiteSpace(query.InvoiceNumber))
            {
                exp = exp.And(z => z.InvoiceNumber.Contains(query.InvoiceNumber));
            }
            if (query.CompanyId.HasValue)
            {
                exp = exp.And(z => z.CompanyId == query.CompanyId.Value);
            }
            //if (!string.IsNullOrWhiteSpace(query.AgentName))
            //{
            //    exp = exp.And(z => z.AgentName.Contains(query.AgentName));
            //}
            if (!string.IsNullOrWhiteSpace(query.searchKey))
            {
                exp = exp.And(z => z.AgentName.Contains(query.searchKey) || z.CompanName.Contains(query.searchKey) || z.InvoiceNumber.Contains(query.searchKey));
            }
            if (query.AgentIds != null && query.AgentIds.Any())
            {
                exp = exp.And(z => query.AgentIds.Contains(z.AgentId));
            }
            if (!string.IsNullOrEmpty(query.StoreInItemCode))
            {
                var idList = await _db.InputBillSubmitDetails.Where(z => z.StoreInItemCode == query.StoreInItemCode).Select(z => z.InputBillId).ToListAsync();
                exp = exp.And(z => idList.Contains(z.Id));
            }
            if (query.HasDetail == 1) //是
            {
                exp = exp.And(z => z.InputBillSubmitDetail.Count > 0);
            }
            else if (query.HasDetail == 2) //否
            {
                exp = exp.And(z => z.InputBillSubmitDetail == null || z.InputBillSubmitDetail.Count == 0);
            }
            if (!string.IsNullOrEmpty(query.ProductNo))
            {
                var inputBillIds = await _db.InputBillSubmitDetails.Where(x => x.ProductNo == query.ProductNo).Select(x => x.InputBillId).ToListAsync();
                if (inputBillIds != null && inputBillIds.Any())
                {
                    exp = exp.And(z => inputBillIds.ToHashSet().Contains(z.Id));
                }
                else
                {
                    exp = exp.And(z => false);
                }
            }

            // 取消勾稽状态过滤
            if (query.cancelReconciliationStatusName.HasValue)
            {
                if (query.cancelReconciliationStatusName.Value)
                {
                    exp = exp.And(z => z.IsCancelledReconciliation == query.cancelReconciliationStatusName.Value);
                }
                else
                {
                    exp = exp.And(z => z.IsCancelledReconciliation != true);
                }
            }

            // 取消勾稽时间范围过滤
            if (!string.IsNullOrWhiteSpace(query.beginCancelReconciliationTime) && !string.IsNullOrWhiteSpace(query.endCancelReconciliationTime))
            {
                var endTime = Convert.ToDateTime(query.endCancelReconciliationTime).AddDays(1).AddMilliseconds(-1);
                var startTime = Convert.ToDateTime(query.beginCancelReconciliationTime);
                exp = exp.And(z => z.CancelReconciliationTime.HasValue && z.CancelReconciliationTime.Value <= endTime && z.CancelReconciliationTime.Value >= startTime);
            }
            #endregion
            IQueryable<InputBillPo> baseQuery = _db.InputBills.Where(exp).AsNoTracking();//全部
            if (query.HasDetail > 0)
            {
                baseQuery = _db.InputBills.Include(z => z.InputBillSubmitDetail).Where(exp).AsNoTracking();
            }

            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion


            #region 获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            baseQuery = await AddStrategyQueryAsync(strategryquery, baseQuery);
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(p => new InputBillQueryListOut()
            {
                Id = p.Id,
                AgentId = p.AgentId,
                AgentName = p.AgentName,
                Amount = p.Amount,
                BillTime = p.BillTime,
                CompanName = p.CompanName,
                CompanyId = p.CompanyId,
                InvoiceCode = p.InvoiceCode,
                InvoiceNumber = p.InvoiceNumber,
                NotaxAmount = p.NotaxAmount,
                PurchaseDutyNumber = p.PurchaseDutyNumber,
                SaleDutyNumber = p.SaleDutyNumber,
                TaxAmount = p.TaxAmount,
                TypeName = p.Type == 1 ? "普票" : "专票",
                Status = (InputBillStatusEnum)p.Status,
                CreatedTime = p.CreatedTime,
                Remark = p.Remark,
                CancelReconciliationTime = p.CancelReconciliationTime,
                IsCancelledReconciliation = p.IsCancelledReconciliation,
            }).ToListAsync();
            return (list, count);
        }

        /// <summary>
        /// 导出进项票数据
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<MemoryStream> ExportBill(InputBillQueryInput query)
        {
            try
            {
                var stream = new MemoryStream();
                var (list, count) = await GetListInputBillAsync(query);
                if (list == null || list.Count <= 0)
                {
                    return stream;
                }
                var ids = list.Select(x => x.Id).ToList();
                var details = _db.InputBillSubmitDetails.Where(x => ids.Contains(x.InputBillId)).ToList();
                var productNos = details.Select(x => x.ProductNo).ToList();
                var products = await _bDSApiClient.GetByNos(productNos, query.CompanyId.ToString());

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("进项发票查询导出数据");
                    worksheet.SetValue(1, columnIndex++, "发票号");
                    worksheet.SetValue(1, columnIndex++, "公司名称");
                    worksheet.SetValue(1, columnIndex++, "供应商名称");
                    worksheet.SetValue(1, columnIndex++, "开票时间");
                    worksheet.SetValue(1, columnIndex++, "票据类型");
                    worksheet.SetValue(1, columnIndex++, "创建时间");
                    worksheet.SetValue(1, columnIndex++, "发票代码");
                    worksheet.SetValue(1, columnIndex++, "购买方税号");
                    worksheet.SetValue(1, columnIndex++, "销售方税号");
                    worksheet.SetValue(1, columnIndex++, "税额（元）");
                    worksheet.SetValue(1, columnIndex++, "发票状态");
                    worksheet.SetValue(1, columnIndex++, "金额（元）");
                    worksheet.SetValue(1, columnIndex++, "总金额（元）");
                    worksheet.SetValue(1, columnIndex++, "备注");
                    //详情
                    worksheet.SetValue(1, columnIndex++, "入库单号/应付单号");
                    worksheet.SetValue(1, columnIndex++, "货号");
                    worksheet.SetValue(1, columnIndex++, "产品名称");
                    worksheet.SetValue(1, columnIndex++, "型号");
                    worksheet.SetValue(1, columnIndex++, "本次入票数");
                    worksheet.SetValue(1, columnIndex++, "含税单价（元）");
                    worksheet.SetValue(1, columnIndex++, "不含税单价（元）");
                    worksheet.SetValue(1, columnIndex++, "金额（元）");
                    worksheet.SetValue(1, columnIndex++, "税率（%）");
                    worksheet.SetValue(1, columnIndex++, "税额（元）");

                    var index = 2;
                    list.ForEach(p =>
                    {
                        worksheet.SetValue(index, 1, p.InvoiceNumber);
                        worksheet.SetValue(index, 2, p.CompanName);
                        worksheet.SetValue(index, 3, p.AgentName);
                        worksheet.SetValue(index, 4, p.BillTime.ToString("yyyy-MM-dd"));
                        worksheet.SetValue(index, 5, p.TypeName);
                        worksheet.SetValue(index, 6, p.CreatedTime.ToString("yyyy-MM-dd hh:mm:ss"));
                        worksheet.SetValue(index, 7, p.InvoiceCode);
                        worksheet.SetValue(index, 8, p.PurchaseDutyNumber);
                        worksheet.SetValue(index, 9, p.SaleDutyNumber);
                        worksheet.SetValue(index, 10, p.TaxAmount);
                        worksheet.Cells[index, 10].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, 11, p.StatusName);
                        worksheet.SetValue(index, 12, p.NotaxAmount);
                        worksheet.Cells[index, 12].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, 13, p.Amount);
                        worksheet.Cells[index, 13].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, 14, p.Remark);

                        var currentDetails = details.Where(x => x.InputBillId == p.Id).ToList();
                        if (currentDetails.Any())
                        {
                            foreach (var d in currentDetails)
                            {
                                var single = products.FirstOrDefault(x => x.productNo == d.ProductNo);
                                worksheet.SetValue(index, 15, d.StoreInItemCode);
                                worksheet.SetValue(index, 16, d.ProductNo);
                                worksheet.SetValue(index, 17, d.ProductName);
                                worksheet.SetValue(index, 18, single != null ? single.model : string.Empty);
                                worksheet.SetValue(index, 19, d.Quantity);
                                worksheet.Cells[index, 19].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, 20, d.TaxCost);
                                worksheet.Cells[index, 20].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, 21, d.NoTaxCost);
                                worksheet.Cells[index, 21].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, 22, d.NoTaxAmount);
                                worksheet.Cells[index, 22].Style.Numberformat.Format = "#,##0.00";
                                worksheet.SetValue(index, 23, d.TaxRate);
                                worksheet.SetValue(index, 24, d.TaxAmount);
                                worksheet.Cells[index, 24].Style.Numberformat.Format = "#,##0.00";
                                index++;
                            }
                        }
                        else
                        {
                            index++;
                        }
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;
                    package.SaveAs(stream);
                    stream.Position = 0;
                    return stream;
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException(ex.Message);
            }
        }

        /// <summary>
        /// 数据策略权限增加(进项发票)
        /// </summary>
        /// <param name="queryModel"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        private async Task<IQueryable<InputBillPo>> AddStrategyQueryAsync(StrategyQueryInput queryModel, IQueryable<InputBillPo> query)
        {
            if (queryModel != null)
            {
                var strategys = await _pCApiClient.GetStrategyAsync(queryModel);
                if (strategys != null && strategys.RowStrategies.Count > 0)
                {
                    query = AnalysisStrategy(strategys.RowStrategies, query);
                }

            }

            return query;
        }
        /// <summary>
        /// 增加数据策略权限(进项发票)
        /// </summary>
        /// <param name="rowStrategies"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        protected IQueryable<InputBillPo> AnalysisStrategy(Dictionary<string, List<string>> rowStrategies, IQueryable<InputBillPo> query)
        {
            foreach (var key in rowStrategies.Keys)
            {
                switch (key)
                {
                    case "company":
                        if (!rowStrategies[key].Any(s => s == "@all"))
                        {
                            var strategList = rowStrategies[key].Select(s => Guid.Parse(s.ToLower())).ToHashSet();
                            query = query.Where(z => z.CompanyId != null && strategList.Contains(z.CompanyId));
                        }
                        break;
                    //case "accountingDept":
                    //    if (!rowStrategies[key].Any(s => s == "@all"))
                    //    {
                    //        var strategList = rowStrategies[key].Select(s => s.ToLower()).ToList();
                    //        query = query.Where(z => z.BusinessDeptId != null && strategList.Contains(z.BusinessDeptId));
                    //    }
                    //    break;
                    default:
                        break;
                }
            }
            return query;
        }

        /// <summary>
        /// 点击获取添加的发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InputBillDeatilQueryListOut>, int)> GetListInputBillDetailAsync(InputBillQueryInputDeatil query)
        {
            Expression<Func<InputBillSubmitDetailPo, bool>> exp = z => 1 == 1;

            #region 无需聚合
            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.StoreInItemCode))
            {
                exp = exp.And(z => z.StoreInItemCode.Equals(query.StoreInItemCode));
            }
            if (query.InputBillId != null && query.InputBillId != Guid.Empty)
            {
                exp = exp.And(z => z.InputBillId == query.InputBillId);
            }
            if (!string.IsNullOrWhiteSpace(query.ProductName))
            {
                exp = exp.And(z => z.ProductName.Contains(query.ProductName));
            }
            if (!string.IsNullOrWhiteSpace(query.ProductNo))
            {
                exp = exp.And(z => z.ProductNo.Contains(query.ProductNo));
            }
            #endregion

            #region 聚合显示
            //var baseQueryList = await _db.InputBillSubmitDetails.Where(exp).Include(x => x.InputBillSubmitDetailQuantity).AsNoTracking().ToListAsync();

            //var submitDetailPos = baseQueryList.GroupBy(x => new { x.StoreInItemCode, x.ProductNo, x.ProductId, x.ProductNameId, x.TaxCost, x.NoTaxCost, x.TaxRate }).ToList();

            //List<InputBillDeatilQueryListOut> reslist = new List<InputBillDeatilQueryListOut>();

            //foreach (var item in submitDetailPos)
            //{
            //    var guids = new List<Guid>();
            //    var storeInDetailOuts = new List<StoreInDetailOut>();
            //    if (query.StoreInDetail != null && query.StoreInDetail.Count > 0)
            //    {
            //        item.ForEach(t =>
            //        {
            //            var submitDetailQuantityPos = t.InputBillSubmitDetailQuantity.Select(x => x.StoreInDetailId).ToList();
            //            guids.AddRange(submitDetailQuantityPos);
            //        });
            //        storeInDetailOuts = query.StoreInDetail.Where(x => guids.Contains(Guid.Parse(x.StoreInDetailId))).ToList();
            //    }
            //    InputBillDeatilQueryListOut inputBillDeatilQueryListOut = new InputBillDeatilQueryListOut();
            //    inputBillDeatilQueryListOut.Id = Guid.NewGuid();
            //    inputBillDeatilQueryListOut.InputBillId = item.First().InputBillId;
            //    inputBillDeatilQueryListOut.ProductName = item.First().ProductName;
            //    inputBillDeatilQueryListOut.ProductNo = item.First().ProductNo;
            //    inputBillDeatilQueryListOut.NoTaxCost = item.First().NoTaxCost;
            //    inputBillDeatilQueryListOut.TaxCost = item.First().TaxCost;
            //    inputBillDeatilQueryListOut.TaxRate = item.First().TaxRate;

            //    inputBillDeatilQueryListOut.Quantity = item.Sum(x => x.Quantity);
            //    if (query.StoreInDetail != null && query.StoreInDetail.Count > 0)
            //    {
            //        inputBillDeatilQueryListOut.StroeInQuantity = storeInDetailOuts.Sum(x => Convert.ToInt32(x.Quantity));
            //    }
            //    inputBillDeatilQueryListOut.TaxAmount = item.Sum(x => x.TaxAmount);
            //    inputBillDeatilQueryListOut.NoTaxAmount = item.Sum(x => x.NoTaxAmount);

            //    inputBillDeatilQueryListOut.storeInCodes = item.Select(x => x.StoreInItemCode).ToList();
            //    inputBillDeatilQueryListOut.StoreInItemCode = item.First().StoreInItemCode;
            //    reslist.Add(inputBillDeatilQueryListOut);
            //}
            #endregion

            #region 排序
            //{
            //    //submitDetailPos = submitDetailPos.OrderByDescending(z => z.CreatedTime).ToList();
            //}
            #endregion

            #endregion

            var reslist = await _db.InputBillSubmitDetails.Where(exp).Include(x => x.InputBillSubmitDetailQuantity).Select(p => new InputBillDeatilQueryListOut
            {
                StoreInItemCode = p.StoreInItemCode,
                ProductNo = p.ProductNo,
                Quantity = p.Quantity,
                ProductName = p.ProductName,
                TaxAmount = p.TaxAmount,
                NoTaxAmount = p.NoTaxAmount,
                TaxCost = p.TaxCost,
                NoTaxCost = p.NoTaxCost,
                TaxRate = p.TaxRate,
                Id = p.Id,
                ProducerOrderNo = p.ProducerOrderNo
            }).OrderBy(p => p.ProductNo).AsNoTracking().ToListAsync();

            if (!string.IsNullOrEmpty(query.StoreInItemCode))
            {
                reslist = reslist.Where(x => x.StoreInItemCode == query.StoreInItemCode).ToList();
            }
            if (!string.IsNullOrEmpty(query.ProductNo))
            {
                reslist = reslist.Where(x => x.ProductNo.Contains(query.ProductNo)).ToList();
            }
            if (!string.IsNullOrEmpty(query.ProductName))
            {
                reslist = reslist.Where(x => x.ProductName.Contains(query.ProductName)).ToList();
            }

            var productNos = reslist.Select(x => x.ProductNo).ToList();
            var products = await _bDSApiClient.GetByNos(productNos, "");
            foreach (var item in reslist)
            {
                var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                item.Model = single != null ? single.model : string.Empty;
            }
            //总条数
            var count = reslist.Count();

            //分页
            var list = reslist.Skip((query.page - 1) * query.limit).Take(query.limit).ToList();

            return (list, count);
        }

        public async Task<(List<InputBillDeatilJDQueryListOut>, int)> GetListInputBillDetailJdAsync(InputBillQueryInputDeatil query)
        {
            Expression<Func<InputBillDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件

            exp = exp.And(z => z.InputBillId == query.InputBillId);
            if (!string.IsNullOrWhiteSpace(query.ProductName))
            {
                exp = exp.And(z => z.ProductName.Contains(query.ProductName));
            }
            if (!string.IsNullOrWhiteSpace(query.ProductNo))
            {
                exp = exp.And(z => z.ProductNo.Contains(query.ProductNo));
            }

            #endregion
            IQueryable<InputBillDetailPo> baseQuery = _db.InputBillDetails.Where(exp).AsNoTracking();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion

            //总条数
            var count = await baseQuery.CountAsync();

            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(p => new InputBillDeatilJDQueryListOut()
            {
                InputBillId = p.InputBillId,
                Id = p.Id,
                NoTaxAmount = p.NoTaxAmount,
                NoTaxCost = p.NoTaxCost,
                ProductName = p.ProductName,
                ProductNo = p.ProductNo,
                Quantity = p.Quantity,
                TaxAmount = p.TaxAmount,
                TaxCost = p.TaxCost,
                TaxRate = p.TaxRate,
                AgentName = query.AgentName,
                CompanName = query.CompanName,
            }).ToListAsync();

            return (list, count);
        }
        /// <summary>
        /// 获取进项发票提交明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<InputBillQuerySumOutput> GetInputBillDetailSumAsync(InputBillQueryInputDeatil query)
        {
            Expression<Func<InputBillSubmitDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            if (!string.IsNullOrWhiteSpace(query.StoreInItemCode))
            {
                exp = exp.And(z => z.StoreInItemCode.Equals(query.StoreInItemCode));
            }
            if (query.InputBillId != null && query.InputBillId != Guid.Empty)
            {
                exp = exp.And(z => z.InputBillId == query.InputBillId);
            }
            if (!string.IsNullOrWhiteSpace(query.ProductName))
            {
                exp = exp.And(z => z.ProductName.Contains(query.ProductName));
            }
            if (!string.IsNullOrWhiteSpace(query.ProductNo))
            {
                exp = exp.And(z => z.ProductNo.Contains(query.ProductNo));
            }
            #endregion
            var reslist = await _db.InputBillSubmitDetails.Where(exp).Include(x => x.InputBillSubmitDetailQuantity).Select(p => new InputBillDeatilQueryListOut
            {
                Quantity = p.Quantity,
                TaxAmount = p.TaxAmount,
                NoTaxAmount = p.NoTaxAmount,
            }).AsNoTracking().ToListAsync();
            var output = new InputBillQuerySumOutput()
            {
                NumberSum = reslist.Sum(p => p.Quantity),
                TaxValueSum = reslist.Sum(p => p.TaxAmount),
                ValueSum = reslist.Sum(p => p.NoTaxAmount),
            };
            return output;
        }
        /// <summary>
        /// 获取进项票金蝶明细汇总
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<InputBillQuerySumOutput> GetInputBillDetailSumJdAsync(InputBillQueryInputDeatil query)
        {
            Expression<Func<InputBillDetailPo, bool>> exp = z => 1 == 1;
            #region 查询条件
            exp = exp.And(z => z.InputBillId == query.InputBillId);
            if (!string.IsNullOrWhiteSpace(query.ProductName))
            {
                exp = exp.And(z => z.ProductName.Contains(query.ProductName));
            }
            if (!string.IsNullOrWhiteSpace(query.ProductNo))
            {
                exp = exp.And(z => z.ProductNo.Contains(query.ProductNo));
            }
            #endregion
            IQueryable<InputBillDetailPo> baseQuery = _db.InputBillDetails.Where(exp).AsNoTracking();
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion
            var list = await baseQuery.Select(p => new InputBillDeatilJDQueryListOut()
            {
                NoTaxAmount = p.NoTaxAmount,
                Quantity = p.Quantity,
            }).ToListAsync();
            var output = new InputBillQuerySumOutput()
            {
                NumberSum = list.Sum(p => p.Quantity),
                ValueSum = list.Sum(p => p.NoTaxAmount),
            };
            return output;
        }

        /// <summary>
        /// 查询显示 (入库单明细)
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetListStoreInByCompanyAsync(StoreInDetailQueryInput input)
        {
            //返回数据
            List<StoreInDetailQueryOutput> list = new List<StoreInDetailQueryOutput>();
            var resultList = new InventoryRespon<InventoryStoreInOutput>();
            //总条数
            var count = 0;
            try
            {
                //编辑显示
                if (!string.IsNullOrEmpty(input.storeInCode))
                {
                    if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    {
                        input.storeInCodes.Add(input.storeInCode);
                    }
                    else
                    {
                        input.storeInCodes = new List<string>() { input.storeInCode };
                    }
                }
                if (input.storeInCodes != null && input.storeInCodes.Any())
                {
                    input.storeInCodes = input.storeInCodes.Where(p => !string.IsNullOrEmpty(p)).ToList();
                }
                if (!string.IsNullOrEmpty(input.ProductId))
                {
                    var productNameId = input.ProductId;
                    input.ProductNameId = productNameId;
                    input.ProductId = string.Empty;
                }
                //input.supposedType = 2;
                //if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                //{
                //input.supposedType = 2;
                //}

                //关键字就为入库单
                if (!string.IsNullOrEmpty(input.searchKey))
                {
                    input.storeInCode = input.searchKey;
                }
                resultList = await _inventoryApiClient.QueryStoreInByCompany(input);
                if (resultList == null || resultList.list == null)
                {
                    _logger.LogInformation($"入库清单，无数据{JsonConvert.SerializeObject(input)}");
                    return (list, 0);
                }
                count = resultList.total;

            }
            catch (Exception e)
            {
                throw new ApplicationException($"入库清单查询错误：{e.Message}");
            }
            try
            {
                var inputBillSubmitDetails = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId).Include(x => x.InputBillSubmitDetailQuantity).AsNoTracking().ToListAsync();
                var productNos = new List<string>();
                #region 数据处理和转化
                //赋值
                foreach (var item in resultList.list)
                {
                    var detailoutput = new StoreInDetailQueryOutput();
                    detailoutput.ProductId = item.LotInfo[0].productId;
                    detailoutput.ProductNameId = item.LotInfo[0].productNameId;
                    detailoutput.ProductName = item.LotInfo[0].productName ?? "";
                    detailoutput.ProductNo = item.LotInfo[0].productNo ?? "";
                    productNos.Add(detailoutput.ProductNo);
                    detailoutput.StoreInItemCode = item.storeInCode ?? "";
                    detailoutput.StoreInDate = item.storeInDate;
                    detailoutput.ServiceName = item.businessUnitName ?? "";
                    detailoutput.CompanyName = item.companyName ?? "";
                    detailoutput.AgentName = item.agentName ?? "";
                    detailoutput.PolyQuantity = item.quantity ?? 0;
                    detailoutput.PolyInvoiceQuantity = item.invoiceQuantity ?? 0;
                    detailoutput.TaxCost = item.LotInfo[0].unitCost ?? 0;
                    detailoutput.TaxRate = item.LotInfo[0].taxRate ?? 0;
                    detailoutput.TaxAmount = (detailoutput.TaxCost ?? 0) * detailoutput.PolyQuantity * (detailoutput.TaxRate ?? 0) / 100;
                    detailoutput.LotInfo = item.LotInfo;
                    detailoutput.BusinessType = 1;
                    detailoutput.rebate = item.rebate;
                    //不含税单价
                    detailoutput.NoTaxCost = detailoutput.TaxCost - getTaxAmount(detailoutput.TaxCost, item.taxRate);
                    detailoutput.NoTaxAmount = (detailoutput.NoTaxCost ?? 0) * detailoutput.PolyQuantity;
                    //采购订单号
                    detailoutput.PurchaseCode = item.purchaseOrderCode;

                    var producerOrderNos = item.LotInfo?.Select(p => p.producerOrderNo).Distinct().ToList();
                    detailoutput.ProducerOrderNo = producerOrderNos == null ? "" : string.Join(",", producerOrderNos);

                    //编辑显示
                    //if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    //{
                    //    detailoutput.inputBillId = input.InputBillId;
                    //    detailoutput = await UpdateShowCurrentInputQuntity(detailoutput);
                    //}
                    //提交显示  自动匹配金蝶发票(填入合适的发票数量对于入库数来说)
                    //else
                    //{
                    //    if (inputBillSubmitDetails == null || !inputBillSubmitDetails.Any())
                    //    {
                    //        detailoutput = await IsMatch(detailoutput, input.InputBillId);
                    //    }
                    //}
                    //如果发票明细中有数据则ThisQuantity，回显发票明细中的
                    var details = inputBillSubmitDetails.Where(p => p.StoreInItemCode == detailoutput.StoreInItemCode &&
                                                                    p.ProductId == detailoutput.ProductId &&
                                                                    p.TaxCost == detailoutput.TaxCost &&
                                                                    p.TaxRate == detailoutput.TaxRate).ToList();
                    foreach (var detail in details)
                    {
                        if (detail != null)
                        {
                            var storeInDetailId = item.LotInfo[0] != null && !string.IsNullOrEmpty(item.LotInfo[0].storeInDetailId) ? Guid.Parse(item.LotInfo[0].storeInDetailId) : Guid.NewGuid();
                            if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any() && detail.InputBillSubmitDetailQuantity.Where(x => x.StoreInDetailId == storeInDetailId).Count() > 0)
                            {
                                detailoutput.NoTaxCost = detail.NoTaxCost;
                                detailoutput.ThisQuantity = detail.Quantity;
                            }
                        }
                    }
                    list.Add(detailoutput);
                }
                var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
                foreach (var item in list)
                {
                    var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                    item.Model = single != null ? single.model : string.Empty;
                    item.Specification = single != null ? single.specification : string.Empty;
                }
                #endregion
                //排序
                list = list.OrderByDescending(x => x.StoreInDate).ToList();
                return (list, count);
            }
            catch (Exception e)
            {
                throw new ApplicationException($"入库清单数据处理和转化错误：{e.Message}");
            }
        }

        /// <summary>
        /// 获取税额
        /// </summary>
        /// <param name="amount"></param>
        /// <param name="taxRate"></param>
        /// <returns></returns>
        private decimal? getTaxAmount(decimal? amount, decimal? taxRate)
        {
            return amount / (1 + taxRate / 100) * (taxRate / 100);
        }
        #region  经销调出
        /// <summary>
        /// 查询显示 (经销调出单明细)
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetListStoreOutByCompanyAsync(StoreInDetailQueryInput input)
        {
            //返回数据
            List<StoreInDetailQueryOutput> list = new List<StoreInDetailQueryOutput>();
            var resultList = new InventoryRespon<StoreOutDetailForFinanceOutput>();
            //总条数
            var count = 0;
            var inputBillSubmitDetails = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId).Include(x => x.InputBillSubmitDetailQuantity).AsNoTracking().ToListAsync();

            try
            {
                //input.supposedType = 2;
                //编辑显示
                if (!string.IsNullOrEmpty(input.storeInCode))
                {
                    if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    {
                        input.storeInCodes.Add(input.storeInCode);
                    }
                    else
                    {
                        input.storeInCodes = new List<string>() { input.storeInCode };
                    }
                }
                if (!string.IsNullOrEmpty(input.ProductId))
                {
                    var productNameId = input.ProductId;
                    input.ProductNameId = productNameId;
                    input.ProductId = string.Empty;
                }
                //19销毁出库
                input.storeOutTypeList = new List<int> { 1, 6, 19 };
                resultList = await _inventoryApiClient.QueryStoreOutByCompany(input);

                if (resultList == null || resultList.list == null)
                {
                    _logger.LogInformation($"经销调出清单，无数据{JsonConvert.SerializeObject(input)}");
                    return (list, 0);
                }
                count = resultList.total;
            }
            catch (Exception e)
            {
                throw new ApplicationException($"经销调出清单查询错误：{e.Message}");
            }
            try
            {
                #region 数据处理和转化
                var productNos = new List<string>();
                //赋值
                foreach (var item in resultList.list)
                {
                    var detailoutput = new StoreInDetailQueryOutput();
                    detailoutput.ProductId = item.LotInfo[0].productId;
                    detailoutput.ProductNameId = item.LotInfo[0].productNameId;
                    detailoutput.ProductName = item.LotInfo[0].productName ?? "";
                    detailoutput.ProductNo = item.LotInfo[0].productNo ?? "";
                    productNos.Add(detailoutput.ProductNo);
                    detailoutput.StoreInItemCode = item.StoreOutCode ?? "";
                    detailoutput.StoreInDate = item.BillDate;
                    detailoutput.ServiceName = item.BusinessUnitName ?? "";
                    detailoutput.CompanyName = item.CompanyName ?? "";
                    detailoutput.AgentName = item.AgentName ?? "";
                    detailoutput.PolyQuantity = item.Quantity ?? 0;
                    detailoutput.PolyInvoiceQuantity = item.InvoiceQuantity ?? 0;
                    detailoutput.TaxCost = item.LotInfo[0].settlementCost ?? 0;
                    detailoutput.TaxRate = item.LotInfo[0].taxRate ?? 0;
                    detailoutput.TaxAmount = detailoutput.TaxCost * detailoutput.PolyQuantity * detailoutput.TaxRate / 100;
                    detailoutput.NoTaxAmount = detailoutput.NoTaxCost * detailoutput.PolyQuantity;
                    detailoutput.LotInfo = item.LotInfo;
                    detailoutput?.LotInfo?.ForEach(t => t.storeInDetailId = t.id);
                    detailoutput.BusinessType = 2;

                    //不含税单价
                    detailoutput.NoTaxCost = detailoutput.TaxCost - getTaxAmount(detailoutput.TaxCost, item.LotInfo[0].taxRate);
                    detailoutput.NoTaxAmount = (detailoutput.NoTaxCost ?? 0) * detailoutput.PolyQuantity;
                    //编辑显示
                    //if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    //{
                    //    detailoutput.inputBillId = input.InputBillId;
                    //    detailoutput = await UpdateShowCurrentInputQuntity(detailoutput);
                    //}
                    //提交显示  自动匹配金蝶发票(填入合适的发票数量对于入库数来说)
                    //else
                    //{
                    //    detailoutput = await IsMatch(detailoutput, input.InputBillId);
                    //}
                    //如果发票明细中有数据则ThisQuantity，回显发票明细中的
                    var details = inputBillSubmitDetails.Where(p => p.StoreInItemCode == detailoutput.StoreInItemCode &&
                                                                    p.ProductId == detailoutput.ProductId &&
                                                                    p.TaxCost == detailoutput.TaxCost &&
                                                                    p.TaxRate == detailoutput.TaxRate).ToList();
                    foreach (var detail in details)
                    {
                        if (detail != null)
                        {
                            var storeInDetailId = item.LotInfo[0] != null && !string.IsNullOrEmpty(item.LotInfo[0].storeInDetailId) ? Guid.Parse(item.LotInfo[0].storeInDetailId) : Guid.NewGuid();
                            if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any() && detail.InputBillSubmitDetailQuantity.Where(x => x.StoreInDetailId == storeInDetailId).Count() > 0)
                            {
                                detailoutput.NoTaxCost = detail.NoTaxCost;
                                detailoutput.ThisQuantity = detail.Quantity;
                            }
                        }
                    }
                    list.Add(detailoutput);
                }


                #endregion
                var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
                foreach (var item in list)
                {
                    var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                    item.Model = single != null ? single.model : string.Empty;
                    item.Specification = single != null ? single.specification : string.Empty;
                }
                //排序
                list = list.OrderByDescending(x => x.StoreInDate).ToList();

                return (list, count);
            }
            catch (Exception e)
            {
                throw new ApplicationException($"经销调出清单数据处理和转化错误：{e.Message}");
            }


        }
        private async Task<StoreInDetailQueryOutput> UpdateShowCurrentInputQuntityStoreOut(StoreInDetailQueryOutput Res)
        {

            //提交入库单信息
            var submitDetailPos = await _queryInputBillSubmitDetailPo.FirstOrDefaultAsync(x => Res.StoreInItemCode == x.StoreInItemCode, new List<string>() { "InputBillSubmitDetailQuantity" }
                );



            //应该入库的 (编辑前的)
            var InputQuentity = submitDetailPos.InputBillSubmitDetailQuantity.Sum(x => x.Quantity);

            //应该入库的 (显示实际可入的数量)
            var FactQuentity = Res.PolyQuantity - Res.PolyInvoiceQuantity;


            if (InputQuentity >= FactQuentity)
            {
                Res.ThisQuantity = FactQuentity;
            }
            else
            {
                Res.ThisQuantity = InputQuentity;
            }


            return Res;


        }

        private async Task<StoreInDetailQueryOutput> IsMatchStoreOut(StoreInDetailQueryOutput Res, Guid InputBillId)
        {

            //金蝶发票明细信息  (当前这张进项发票的信息)
            var inputBillDetailPos = await _queryInputBillDetailPo.GetAllListAsync(x => x.InputBillId == InputBillId);

            //添加进来的发票 和每一张金蝶发票比较
            foreach (var item in inputBillDetailPos)
            {
                bool ismatch = false;
                bool isnext = true;
                if (item.ProductNo == Res.ProductNo && isnext)
                {
                    ismatch = true;
                    isnext = false;


                }
                if (item.ProductName.Equals(Res.ProductName) && isnext)
                {
                    ismatch = true;
                    isnext = false;

                }
                if (Res.ProductName.Length != item.ProductName.Length && isnext)
                {
                    var levenshteinDistance = new LevenshteinDistance(Res.ProductName, item.ProductName);
                    levenshteinDistance.Compute();
                    if (Convert.ToDouble(levenshteinDistance.ComputeResult.Rate) > 0.9)
                    {
                        ismatch = true;
                        isnext = false;
                    }
                }

                //匹配到了
                if (ismatch)
                {
                    //本次最大入票数
                    var canInvoiceQuantity = Res.PolyQuantity - Res.PolyInvoiceQuantity;

                    //如果金蝶发票数量小于等于最大入票数量
                    if (item.Quantity <= canInvoiceQuantity)
                    {
                        Res.ThisQuantity = item.Quantity;
                    }
                    //否则
                    else
                    {
                        Res.ThisQuantity = canInvoiceQuantity;
                    }

                    //添加的数据的 金额 不能 大于匹配到的金蝶发票明细总金额
                    //if (Res.ThisQuantity * Res.TaxCost > item.NoTaxAmount)
                    //{
                    //    throw new Exception($"{Res.ProductNo}添加的发票数量的总金额大于金蝶的");
                    //}

                    //Res.TaxCost = item.TaxCost;
                    //Res.NoTaxCost = item.NoTaxCost;
                    //StoreInDetailQueryOutput.TaxCost = item.unitCost ?? 0;
                    //StoreInDetailQueryOutput.TaxRate = item.taxRate ?? 0;
                    //StoreInDetailQueryOutput.TaxAmount = StoreInDetailQueryOutput.TaxCost * StoreInDetailQueryOutput.PolyQuantity * StoreInDetailQueryOutput.TaxRate / 100;
                    //StoreInDetailQueryOutput.NoTaxAmount = StoreInDetailQueryOutput.NoTaxCost * StoreInDetailQueryOutput.PolyQuantity;
                    Res.IsMatch = true;

                    break;
                }

            }

            return Res;

        }
        #endregion

        #region 寄售转购货
        /// <summary>
        /// 寄售转购货
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetConsignToPurchaseDetailGroup(StoreInDetailQueryInput input)
        {
            //返回数据
            var ret = new List<StoreInDetailQueryOutput>();
            var puaInput = new ConsignToPurchaseDetailGroupInput
            {
                AgentId = input.agentId.HasValue ? input.agentId.ToString() : "",
                CompanyId = input.companyId.HasValue ? input.companyId.ToString() : "",
                Code = input.storeInCode,
                pageIndex = input.page,
                pageSize = input.limit,
                StrategyQuery = null,
                InvoiceStatus = input.supposedType.HasValue ? input.supposedType.Value : 2,
                ProductName = input.productName,
                ProductNameIds = input.ProductId != null ? new List<string>() { input.ProductId } : null,
                ProductNo = input.productNo
            };
            if (!string.IsNullOrEmpty(input.storeInDateStart) && !string.IsNullOrEmpty(input.storeInDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                puaInput.StartTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateStart)).AddHours(8);
                puaInput.EndTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateEnd)).AddHours(8);
            }
            var pageResult = await _purchaseApiClient.GetConsignToPurchaseDetailGroup(puaInput);
            var productNos = new List<string>();
            if (pageResult != null && pageResult.Data != null && pageResult.Data.Any())
            {
                var inputBillSubmitDetails = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId).Include(x => x.InputBillSubmitDetailQuantity).AsNoTracking().ToListAsync();
                //if (!string.IsNullOrEmpty(input.productNo))
                //{
                //    pageResult.Data = pageResult.Data.Where(p => p.ProductNo == input.productNo).ToList();
                //}
                foreach (var item in pageResult.Data)
                {
                    productNos.Add(item.ProductNo);
                    var detailoutput = new StoreInDetailQueryOutput
                    {
                        AgentName = item.AgentName,
                        CompanyName = item.CompanyName,
                        Id = Guid.NewGuid(),
                        NoTaxCost = item.NoTaxUnitCost,
                        PolyInvoiceQuantity = item.InvoiceQuantity,
                        PolyQuantity = item.Quantity,
                        ProductId = item.ProductId,
                        ProductNo = item.ProductNo,
                        ProductName = item.ProductName,
                        ServiceName = item.ServiceName,
                        ProductNameId = Guid.Parse(item.ProductNameId),
                        StoreInItemCode = item.Code,
                        TaxRate = item.TaxRate,
                        TaxCost = item.UnitCost,
                        NoTaxAmount = item.Quantity * item.UnitCost,
                        TaxAmount = (item.Quantity * item.UnitCost) - (item.Quantity * item.NoTaxUnitCost),
                        StoreInDate = item.BillDate.ToUnixTimeMilliseconds(),
                        BusinessType = 3,
                    };
                    detailoutput.LotInfo = new List<LotInfo>();
                    foreach (var subitem in item.Details)
                    {
                        detailoutput.LotInfo.Add(new LotInfo
                        {
                            productId = Guid.Parse(subitem.ProductId),
                            id = Guid.Empty.ToString(),
                            invoiceQuantity = subitem.InvoiceQuantity,
                            quantity = subitem.Quantity,
                            storeInDetailId = subitem.PurchaseDetailId.ToString(),
                        });
                    }
                    //编辑显示
                    //if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    //{
                    //    detailoutput.inputBillId = input.InputBillId;
                    //    detailoutput = await UpdateShowCurrentInputQuntity(detailoutput);
                    //}
                    ////提交显示  自动匹配金蝶发票(填入合适的发票数量对于入库数来说)
                    //else
                    //{
                    //    detailoutput = await IsMatch(detailoutput, input.InputBillId);
                    //}
                    //如果发票明细中有数据则ThisQuantity，回显发票明细中的

                    var details = inputBillSubmitDetails.Where(p => p.StoreInItemCode == detailoutput.StoreInItemCode &&
                                                                    p.ProductId == detailoutput.ProductId &&
                                                                    p.TaxCost == detailoutput.TaxCost &&
                                                                    p.TaxRate == detailoutput.TaxRate).ToList();
                    foreach (var detail in details)
                    {
                        if (detail != null)
                        {
                            var storeInDetailId = item.Details[0] != null ? item.Details[0].PurchaseDetailId : Guid.NewGuid();
                            if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any() && detail.InputBillSubmitDetailQuantity.Where(x => x.StoreInDetailId == storeInDetailId).Count() > 0)
                            {
                                detailoutput.NoTaxCost = detail.NoTaxCost;
                                detailoutput.ThisQuantity = detail.Quantity;
                            }
                        }
                    }
                    ret.Add(detailoutput);
                }

                var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
                foreach (var item in ret)
                {
                    var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                    item.Model = single != null ? single.model : string.Empty;
                    item.Specification = single != null ? single.specification : string.Empty;
                }
                return (ret, pageResult.Total);
            }
            else
            {
                return (ret, 0);
            }
        }
        #endregion

        /// <summary>
        /// 取购货修订的待入票数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetPurchaseReviseGroup(StoreInDetailQueryInput input)
        {
            //返回数据
            var ret = new List<StoreInDetailQueryOutput>();
            var query = new PurchaseReviseForInputBillQueryDto()
            {
                agentIds = new List<Guid>() { input.agentId.Value },
                companyIds = new List<Guid>() { input.companyId.Value },
                pageIndex = input.page,
                pageSize = input.limit,
                ProductName = input.productName,
                ProductNameIds = input.ProductId != null ? new List<string>() { input.ProductId } : null,
                ProductNo = input.productNo
            };
            if (!string.IsNullOrEmpty(input.storeInCode))
            {
                query.codes = new List<string>() { input.storeInCode };
            }
            if (!string.IsNullOrEmpty(input.storeInDateStart) && !string.IsNullOrEmpty(input.storeInDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                query.startTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateStart)).AddHours(8);
                query.endTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateEnd)).AddHours(8);
            }
            var purchaseResult = await _purchaseApiClient.GetPurchaseReviseForInputBills(query);
            if (purchaseResult != null && purchaseResult.List != null && purchaseResult.List.Any())
            {
                var details = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 4).ToListAsync();
                //var userDetails = await _db.InputBillSubmitDetails.Where(p => p.StoreInItemCode == input.storeInCode && p.BusinessType == 4).ToListAsync();
                var productNos = new List<string>();
                //if (!string.IsNullOrEmpty(input.productNo))
                //{
                //    purchaseResult.List = purchaseResult.List.Where(p => p.productNo == input.productNo).ToList();
                //}
                //if (!string.IsNullOrEmpty(input.productName))
                //{
                //    purchaseResult.List = purchaseResult.List.Where(p => p.productName == input.productName).ToList();
                //}

                purchaseResult.List.ForEach(p =>
                {
                    productNos.Add(p.productNo);
                    var thisDetail = details.FirstOrDefault(t => t.StoreInItemCode == p.purchaseOrderCode && t.ProductId == p.productId && t.TaxRate == p.taxRate);
                    var item = new StoreInDetailQueryOutput()
                    {
                        AgentName = p.agentName,
                        CompanyName = p.companyName,
                        PolyInvoiceQuantity = Math.Abs(p.reviseAmount) - p.canInvoiceAmount,
                        PolyQuantity = p.reviseAmount,
                        ProductId = p.productId,
                        ProductNameId = p.productNameId,
                        ProductNo = p.productNo,
                        ProductName = p.productName,
                        StoreInDate = p.billDate.ToUnixTimeMilliseconds(),
                        StoreInItemCode = p.purchaseOrderCode,
                        ThisQuantity = thisDetail == null ? 0 : thisDetail.NoTaxAmount,
                        TaxCost = p.reviseAmount,
                        NoTaxCost = Math.Round(p.reviseAmount / (1 + p.taxRate / 100.00M), 4),
                        //TaxAmount = p.reviseAmount - (p.reviseAmount / (1 + p.taxRate / 100.00M)),
                        TaxRate = p.taxRate,
                        BusinessType = 4
                    };
                    item.TaxAmount = item.TaxCost - item.NoTaxCost;
                    ret.Add(item);
                });

                var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
                foreach (var item in ret)
                {
                    var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                    item.Model = single != null ? single.model : string.Empty;
                    item.Specification = single != null ? single.specification : string.Empty;
                }
                return (ret, purchaseResult.Total);
            }
            else
            {
                return (ret, 0);
            }
        }

        /// <summary>
        /// 获取服务费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetServerDebts(StoreInDetailQueryInput input)
        {
            var ret = new List<StoreInDetailQueryOutput>();
            DateTime? startTime = null;
            DateTime? endTime = null;

            if (!string.IsNullOrEmpty(input.storeInDateStart) && !string.IsNullOrEmpty(input.storeInDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                startTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateStart));
                endTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateEnd));
            }
            //返回数据
            var query = _db.Debts.Where(p =>
               p.DebtType == Domain.DebtTypeEnum.servicefee &&
               p.AgentId == input.agentId &&
               p.CompanyId == input.companyId &&
               (startTime.HasValue ? p.BillDate >= startTime : true) &&
               (endTime.HasValue ? p.BillDate <= endTime : true) &&
               Math.Abs(p.Value) > Math.Abs(p.InvoiceAmount ?? 0)
            ).OrderBy(p => p.CreatedTime)
            .Select(p => new InputBillOfDebt
            {
                BillDate = new DateTimeOffset(p.BillDate.Value, TimeSpan.FromHours(8)),
                BillCode = p.BillCode,
                Value = p.Value,
                InvoiceAmount = p.InvoiceAmount,
                TaxRate = p.TaxRate,
                PurchaseContactNo = p.PurchaseContactNo
            });
            if (!string.IsNullOrEmpty(input.storeInCode))
            {
                query = query.Where(x => x.BillCode == input.storeInCode);
            }
            if (!string.IsNullOrEmpty(input.productName))
            {
                var billCodes = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 5 && p.ProductName.Contains(input.productName)).Select(o => o.StoreInItemCode).ToListAsync();
                if (billCodes.Any())
                {
                    query = query.Where(x => billCodes.Contains(x.BillCode));
                }
                else
                {
                    return (ret, 0);
                }
            }
            if (!string.IsNullOrEmpty(input.ProductId))
            {
                var billCodes = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 5 && p.ProductId.ToString() == input.ProductId).Select(o => o.StoreInItemCode).ToListAsync();
                if (billCodes.Any())
                {
                    query = query.Where(x => billCodes.Contains(x.BillCode));
                }
                else
                {
                    return (ret, 0);
                }
            }
            var count = query.Count();
            var data = await query.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
            var details = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 5).ToListAsync();
            foreach (var item in data)
            {
                var thisDetail = details.FirstOrDefault(t => t.StoreInItemCode == item.BillCode);
                ret.Add(new StoreInDetailQueryOutput
                {
                    BusinessType = 5,
                    ProductId = Guid.Empty,
                    ProductNameId = Guid.Empty,
                    ProductName = thisDetail?.ProductName ?? "",
                    ProductNo = "服务费",
                    PolyInvoiceQuantity = item.InvoiceAmount.HasValue ? item.InvoiceAmount.Value : 0,
                    StoreInDate = item.BillDate.ToUnixTimeMilliseconds(),
                    ThisQuantity = thisDetail == null ? 0 : thisDetail.NoTaxAmount,
                    StoreInItemCode = item.BillCode,
                    PolyQuantity = item.Value,
                    TaxRate = item.TaxRate,
                    PurchaseContactNo = item.PurchaseContactNo
                });
                ;
            }
            return (ret, count);
        }

        /// <summary>
        /// 获取服务费
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetDebtsByType(StoreInDetailQueryInput input,DebtTypeEnum debtType)
        {
            var ret = new List<StoreInDetailQueryOutput>();
            DateTime? startTime = null;
            DateTime? endTime = null;

            if (!string.IsNullOrEmpty(input.storeInDateStart) && !string.IsNullOrEmpty(input.storeInDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                startTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateStart));
                endTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateEnd));
            }
            //返回数据
            var query = _db.Debts.Where(p =>
               p.DebtType == debtType &&
               p.AgentId == input.agentId &&
               p.CompanyId == input.companyId &&
               (startTime.HasValue ? p.BillDate >= startTime : true) &&
               (endTime.HasValue ? p.BillDate <= endTime : true) &&
               Math.Abs(p.Value) > Math.Abs(p.InvoiceAmount ?? 0)
            ).OrderBy(p => p.CreatedTime)
            .Select(p => new InputBillOfDebt
            {
                BillDate = new DateTimeOffset(p.BillDate.Value, TimeSpan.FromHours(8)),
                BillCode = p.BillCode,
                Value = p.Value,
                InvoiceAmount = p.InvoiceAmount,
                TaxRate = p.TaxRate,
                PurchaseContactNo = p.PurchaseContactNo
            });
            if (!string.IsNullOrEmpty(input.storeInCode))
            {
                query = query.Where(x => x.BillCode == input.storeInCode);
            }
            if (!string.IsNullOrEmpty(input.productName))
            {
                var billCodes = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 5 && p.ProductName.Contains(input.productName)).Select(o => o.StoreInItemCode).ToListAsync();
                if (billCodes.Any())
                {
                    query = query.Where(x => billCodes.Contains(x.BillCode));
                }
                else
                {
                    return (ret, 0);
                }
            }
            if (!string.IsNullOrEmpty(input.ProductId))
            {
                var billCodes = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 5 && p.ProductId.ToString() == input.ProductId).Select(o => o.StoreInItemCode).ToListAsync();
                if (billCodes.Any())
                {
                    query = query.Where(x => billCodes.Contains(x.BillCode));
                }
                else
                {
                    return (ret, 0);
                }
            }
            var count = query.Count();
            var data = await query.Skip((input.page - 1) * input.limit).Take(input.limit).ToListAsync();
            
            foreach (var item in data)
            {
                ret.Add(new StoreInDetailQueryOutput
                {
                    BusinessType = (int)debtType,
                    ProductId = Guid.Empty,
                    ProductNameId = Guid.Empty,
                    ProductName = "",
                    ProductNo="",
                    PolyInvoiceQuantity = item.InvoiceAmount.HasValue ? item.InvoiceAmount.Value : 0,
                    StoreInDate = item.BillDate.ToUnixTimeMilliseconds(),
                    StoreInItemCode = item.BillCode,
                    PolyQuantity = item.Value,
                    TaxRate = item.TaxRate,
                    PurchaseContactNo = item.PurchaseContactNo
                });
                ;
            }
            return (ret, count);
        }
        /// <summary>
        /// 获取换货转退货数据
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetExchangeToReturn(StoreInDetailQueryInput input)
        {
            //返回数据
            List<StoreInDetailQueryOutput> list = new List<StoreInDetailQueryOutput>();
            var resultList = new InventoryRespon<InventoryStoreInOutput>();
            //总条数
            var count = 0;
            try
            {
                //编辑显示
                if (!string.IsNullOrEmpty(input.storeInCode))
                {
                    if (input.billCodes != null && input.billCodes.Count > 0)
                    {
                        input.billCodes.Add(input.storeInCode);
                    }
                    else
                    {
                        input.billCodes = new List<string>() { input.storeInCode };
                    }
                }
                if (input.storeInCodes != null && input.storeInCodes.Any())
                {
                    input.billCodes = input.storeInCodes.Where(p => !string.IsNullOrEmpty(p)).ToList();
                }
                if (!string.IsNullOrEmpty(input.ProductId))
                {
                    var productNameId = input.ProductId;
                    input.ProductNameId = productNameId;
                    input.ProductId = string.Empty;
                }

                //关键字
                if (!string.IsNullOrEmpty(input.searchKey))
                {
                    input.storeInCode = input.searchKey;
                }
                resultList = await _inventoryApiClient.QueryDetailForInputInvoice(input);
                if (resultList == null || resultList.list == null || resultList.list.Count == 0)
                {
                    _logger.LogInformation($"换货转退货，无数据{JsonConvert.SerializeObject(input)}");
                    return (list, 0);
                }
                count = resultList.total;

            }
            catch (Exception e)
            {
                throw new ApplicationException($"换货转退货查询错误：{e.Message}");
            }
            try
            {
                var inputBillSubmitDetails = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId).Include(x => x.InputBillSubmitDetailQuantity).AsNoTracking().ToListAsync();
                var productNos = new List<string>();
                #region 数据处理和转化
                //赋值
                foreach (var item in resultList.list)
                {
                    var detailoutput = new StoreInDetailQueryOutput();
                    detailoutput.ProductId = item.LotInfo[0].productId;
                    detailoutput.ProductNameId = item.LotInfo[0].productNameId;
                    detailoutput.ProductName = item.LotInfo[0].productName ?? "";
                    detailoutput.ProductNo = item.LotInfo[0].productNo ?? "";
                    productNos.Add(detailoutput.ProductNo);
                    detailoutput.StoreInItemCode = item.billCode ?? "";
                    detailoutput.StoreInDate = item.billDate;
                    detailoutput.ServiceName = item.businessUnitName ?? "";
                    detailoutput.CompanyName = item.companyName ?? "";
                    detailoutput.AgentName = item.agentName ?? "";
                    detailoutput.PolyQuantity = item.quantity.HasValue ? 0 - Math.Abs(item.quantity.Value) : 0;
                    detailoutput.PolyInvoiceQuantity = item.invoiceQuantity ?? 0;
                    detailoutput.TaxCost = item.LotInfo[0].settlementUnitCost ?? 0;
                    detailoutput.TaxRate = item.LotInfo[0].taxRate ?? 0;
                    detailoutput.TaxAmount = (detailoutput.TaxCost ?? 0) * detailoutput.PolyQuantity * (detailoutput.TaxRate ?? 0) / 100;
                    detailoutput.LotInfo = item.LotInfo;
                    // 给storeDetailId赋值
                    detailoutput.LotInfo.ForEach(p =>
                    {
                        p.storeInDetailId = p.id;
                    });
                    detailoutput.BusinessType = 6;
                    detailoutput.rebate = item.rebate;
                    //不含税单价
                    detailoutput.NoTaxCost = detailoutput.TaxCost - getTaxAmount(detailoutput.TaxCost, item.taxRate);
                    detailoutput.NoTaxAmount = (detailoutput.NoTaxCost ?? 0) * detailoutput.PolyQuantity;
                    //采购订单号
                    detailoutput.PurchaseCode = item.purchaseOrderCode;

                    var producerOrderNos = item.LotInfo?.Select(p => p.producerOrderNo).Distinct().ToList();
                    detailoutput.ProducerOrderNo = producerOrderNos == null ? "" : string.Join(",", producerOrderNos);

                    //编辑显示
                    //if (input.storeInCodes != null && input.storeInCodes.Count > 0)
                    //{
                    //    detailoutput.inputBillId = input.InputBillId;
                    //    detailoutput = await UpdateShowCurrentInputQuntity(detailoutput);
                    //}
                    //提交显示  自动匹配金蝶发票(填入合适的发票数量对于入库数来说)
                    //else
                    //{
                    //    if (inputBillSubmitDetails == null || !inputBillSubmitDetails.Any())
                    //    {
                    //        detailoutput = await IsMatch(detailoutput, input.InputBillId);
                    //    }
                    //}
                    //如果发票明细中有数据则ThisQuantity，回显发票明细中的
                    var details = inputBillSubmitDetails.Where(p => p.StoreInItemCode == detailoutput.StoreInItemCode &&
                                                                    p.ProductId == detailoutput.ProductId &&
                                                                    p.TaxCost == detailoutput.TaxCost &&
                                                                    p.TaxRate == detailoutput.TaxRate).ToList();
                    foreach (var detail in details)
                    {
                        if (detail != null)
                        {
                            var storeInDetailId = item.LotInfo[0] != null && !string.IsNullOrEmpty(item.LotInfo[0].storeInDetailId) ? Guid.Parse(item.LotInfo[0].storeInDetailId) : Guid.NewGuid();
                            if (detail.InputBillSubmitDetailQuantity != null && detail.InputBillSubmitDetailQuantity.Any() && detail.InputBillSubmitDetailQuantity.Where(x => x.StoreInDetailId == storeInDetailId).Count() > 0)
                            {
                                detailoutput.NoTaxCost = detail.NoTaxCost;
                                detailoutput.ThisQuantity = detail.Quantity;
                            }
                        }
                    }
                    list.Add(detailoutput);
                }
                var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
                foreach (var item in list)
                {
                    var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                    item.Model = single != null ? single.model : string.Empty;
                    item.Specification = single != null ? single.specification : string.Empty;
                }
                #endregion
                //排序
                list = list.OrderByDescending(x => x.StoreInDate).ToList();
                return (list, count);
            }
            catch (Exception e)
            {
                throw new ApplicationException($"入库清单数据处理和转化错误：{e.Message}");
            }
        }

        /// <summary>
        /// 取购应付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<StoreInDetailQueryOutput>, int)> GetDebtGroup(StoreInDetailQueryInput input)
        {
            //返回数据
            var ret = new List<StoreInDetailQueryOutput>();
            var query = new PurchaseReviseForInputBillQueryDto()
            {
                agentIds = new List<Guid>() { input.agentId.Value },
                companyIds = new List<Guid>() { input.companyId.Value },
                pageIndex = input.page,
                pageSize = input.limit
            };
            if (!string.IsNullOrEmpty(input.storeInCode))
            {
                query.codes = new List<string>() { input.storeInCode };
            }
            if (!string.IsNullOrEmpty(input.storeInDateStart) && !string.IsNullOrEmpty(input.storeInDateEnd))
            {
                var dateTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                query.startTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateStart)).AddHours(8);
                query.endTime = dateTime.AddMilliseconds(long.Parse(input.storeInDateEnd)).AddHours(8);
            }
            var purchaseResult = await _purchaseApiClient.GetPurchaseReviseForInputBills(query);
            var details = await _db.InputBillSubmitDetails.Where(p => p.InputBillId == input.InputBillId && p.BusinessType == 4).ToListAsync();
            var productNos = new List<string>();
            purchaseResult.List.ForEach(p =>
            {
                productNos.Add(p.productNo);
                var thisDetail = details.FirstOrDefault(t => t.StoreInItemCode == p.purchaseOrderCode && t.ProductId == p.productId && t.TaxRate == p.taxRate);
                var item = new StoreInDetailQueryOutput()
                {
                    AgentName = p.agentName,
                    CompanyName = p.companyName,
                    PolyInvoiceQuantity = Math.Abs(p.reviseAmount) - p.canInvoiceAmount,
                    PolyQuantity = p.reviseAmount,
                    ProductId = p.productId,
                    ProductNameId = p.productNameId,
                    ProductNo = p.productNo,
                    ProductName = p.productName,
                    StoreInDate = p.billDate.ToUnixTimeMilliseconds(),
                    StoreInItemCode = p.purchaseOrderCode,
                    ThisQuantity = thisDetail == null ? 0 : thisDetail.NoTaxAmount,
                    TaxCost = p.reviseAmount,
                    NoTaxCost = Math.Round(p.reviseAmount / (1 + p.taxRate / 100.00M), 4),
                    //TaxAmount = p.reviseAmount - (p.reviseAmount / (1 + p.taxRate / 100.00M)),
                    TaxRate = p.taxRate,
                    BusinessType = 4
                };
                item.TaxAmount = item.TaxCost - item.NoTaxCost;
                ret.Add(item);
            });

            var products = await _bDSApiClient.GetByNos(productNos, input.companyId.ToString());
            foreach (var item in ret)
            {
                var single = products.FirstOrDefault(x => x.productNo == item.ProductNo);
                item.Model = single != null ? single.model : string.Empty;
            }
            return (ret, purchaseResult.Total);
        }

        public async Task<List<StoreInDetailQueryOutput>> GetUseQuantity(List<StoreInDetailQueryOutput> input, Guid inputBillId)
        {
            if (input.Any())
            {
                var productIds = input.Select(x => x.ProductId).ToList();
                var storeInItemCodes = input.Select(x => x.StoreInItemCode).ToList();
                var taxCosts = input.Select(x => x.TaxCost).ToList();
                //var inputBills = await _db.InputBillSubmitDetails.Include(p => p.InputBillSubmitDetailQuantity).Where(p =>
                //productIds.Contains(p.ProductId) &&
                //TaxCosts.Contains(p.TaxCost) &&
                //p.InputBillId != inputBillId &&
                //p.InputBill.Status != 2 &&
                //StoreInItemCodes.Contains(p.StoreInItemCode)).AsNoTracking().ToListAsync();
                Expression<Func<InputBillSubmitDetailQuantityPo, bool>> exp = p => p.InputBillSubmitDetail.InputBillId != inputBillId && p.InputBillSubmitDetail.InputBill.Status != 2;
                if (productIds != null)
                {
                    exp = exp.And(p => productIds.ToHashSet().Contains(p.InputBillSubmitDetail.ProductId));
                }
                if (taxCosts != null)
                {
                    exp = exp.And(p => taxCosts.ToHashSet().Contains(p.InputBillSubmitDetail.TaxCost));
                }
                if (storeInItemCodes != null)
                {
                    exp = exp.And(p => storeInItemCodes.ToHashSet().Contains(p.InputBillSubmitDetail.StoreInItemCode));
                }
                var inputBillSubmitDetailQuantities = await _db.InputBillSubmitDeatilQuantitys.Include(p => p.InputBillSubmitDetail).Where(exp).AsNoTracking().ToListAsync();
                foreach (var item in input)
                {
                    var storeInDetailIds = item?.LotInfo?.Select(p => Guid.Parse(p.storeInDetailId ?? Guid.Empty.ToString())).ToList();

                    item.UseQuantity = inputBillSubmitDetailQuantities.Where(p => p.InputBillSubmitDetail.ProductId == item.ProductId &&
                                                             p.InputBillSubmitDetail.TaxCost == item.TaxCost &&
                                                             storeInDetailIds.Contains(p.StoreInDetailId)
                    ).Sum(p => p.Quantity);
                }
            }
            return input;
        }

        public async Task<List<StoreInDetailQueryOutput>> GetUseDebtInvoiceAmount(List<StoreInDetailQueryOutput> input, Guid inputBillId)
        {
            if (input.Any())
            {

                var debtCodes = input.Select(p => p.StoreInItemCode).ToList();

                var inputBillSubmitDetails = await _db.InputBillSubmitDetails.Include(p => p.InputBill).Where(p =>
                p.InputBill.Status != 2 &&
                p.InputBillId != inputBillId &&
                debtCodes.Contains(p.StoreInItemCode)
                ).AsNoTracking().ToListAsync();
                foreach (var item in input)
                {
                    item.UseQuantity = inputBillSubmitDetails.Where(p => p.StoreInItemCode == item.StoreInItemCode).Sum(p => p.NoTaxAmount);
                }
            }
            return input;
        }
        public async Task<StoreInDetailQueryOutput> UpdateShowCurrentInputQuntity(StoreInDetailQueryOutput Res)
        {

            //提交入库单信息
            var submitDetailPos = await _queryInputBillSubmitDetailPo.FirstOrDefaultAsync(x => Res.StoreInItemCode == x.StoreInItemCode && x.InputBillId == Res.inputBillId,
                new List<string>() { "InputBillSubmitDetailQuantity" });


            if (submitDetailPos == null) { return Res; }
            //应该入库的 (编辑前的)
            var InputQuentity = submitDetailPos.InputBillSubmitDetailQuantity?.Sum(x => x.Quantity) ?? 0;
            //应该入库的 (显示实际可入的数量)
            var FactQuentity = Res.PolyQuantity - Res.PolyInvoiceQuantity - Res.ShowUseQuantity;
            if (InputQuentity >= FactQuentity)
            {
                Res.ThisQuantity = FactQuentity;
            }
            else
            {
                Res.ThisQuantity = InputQuentity;
            }
            Res.ThisQuantity = FactQuentity;
            return Res;
        }

        public async Task<StoreInDetailQueryOutput> IsMatch(StoreInDetailQueryOutput Res, Guid InputBillId)
        {
            //金蝶发票明细信息  (当前这张进项发票的信息)
            var inputBillDetailPos = await _queryInputBillDetailPo.GetAllListAsync(x => x.InputBillId == InputBillId);

            //添加进来的发票 和每一张金蝶发票比较
            foreach (var item in inputBillDetailPos)
            {
                bool ismatch = false;
                bool isnext = true;
                if (item.ProductNo == Res.ProductNo && isnext)
                {
                    ismatch = true;
                    isnext = false;

                }
                if (item.ProductName.Equals(Res.ProductName) && isnext)
                {
                    ismatch = true;
                    isnext = false;

                }
                if (Res.ProductName.Length != item.ProductName.Length && isnext)
                {
                    var levenshteinDistance = new LevenshteinDistance(Res.ProductName, item.ProductName);
                    levenshteinDistance.Compute();
                    if (Convert.ToDouble(levenshteinDistance.ComputeResult.Rate) > 0.9)
                    {
                        ismatch = true;
                        isnext = false;
                    }
                }

                //匹配到了
                if (ismatch)
                {
                    //本次最大入票数
                    var canInvoiceQuantity = Res.PolyQuantity - Res.PolyInvoiceQuantity;

                    //如果金蝶发票数量小于等于最大入票数量
                    if (item.Quantity <= canInvoiceQuantity)
                    {
                        Res.ThisQuantity = item.Quantity;
                    }
                    //否则
                    else
                    {
                        Res.ThisQuantity = canInvoiceQuantity;
                    }

                    //添加的数据的 金额 不能 大于匹配到的金蝶发票明细总金额
                    //if (Res.ThisQuantity * Res.TaxCost > item.NoTaxAmount)
                    //{
                    //    throw new Exception($"{Res.ProductNo}添加的发票数量的总金额大于金蝶的");
                    //}

                    //Res.TaxCost = item.TaxCost;
                    //Res.NoTaxCost = item.NoTaxCost;
                    //StoreInDetailQueryOutput.TaxCost = item.unitCost ?? 0;
                    //StoreInDetailQueryOutput.TaxRate = item.taxRate ?? 0;
                    //StoreInDetailQueryOutput.TaxAmount = StoreInDetailQueryOutput.TaxCost * StoreInDetailQueryOutput.PolyQuantity * StoreInDetailQueryOutput.TaxRate / 100;
                    //StoreInDetailQueryOutput.NoTaxAmount = StoreInDetailQueryOutput.NoTaxCost * StoreInDetailQueryOutput.PolyQuantity;
                    Res.IsMatch = true;

                    break;
                }

            }

            return Res;

        }

        /// <summary>
        /// 查询货号 入库单  在那些发票里有
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InputBillDeatilQueryListOut>, int)> GetputBillListByStoreIncode(InputBillbyStoreIncode query)
        {
            #region 聚合显示
            var baseQueryList = await _db.InputBillSubmitDetails.Where(x => x.StoreInItemCode == query.storeIncode && x.ProductNo == query.productNo).
                                                                //Include(x => x.InputBillSubmitDetailQuantity).
                                                                Include(x => x.InputBill).Where(p => p.InputBill.Status != 1).AsNoTracking().ToListAsync();

            var submitDetailPos = baseQueryList.GroupBy(x => new { x.ProductNo, x.ProductId, x.ProductNameId, x.TaxCost, x.NoTaxCost, x.TaxRate, x.InputBill.InvoiceNumber }).ToList();

            List<InputBillDeatilQueryListOut> reslist = new List<InputBillDeatilQueryListOut>();

            foreach (var item in submitDetailPos)
            {

                InputBillDeatilQueryListOut inputBillDeatilQueryListOut = new InputBillDeatilQueryListOut();
                inputBillDeatilQueryListOut.InvoiceNumber = item.First().InputBill.InvoiceNumber;
                inputBillDeatilQueryListOut.Id = Guid.NewGuid();
                inputBillDeatilQueryListOut.InputBillId = item.First().InputBillId;
                inputBillDeatilQueryListOut.ProductName = item.First().ProductName;
                inputBillDeatilQueryListOut.ProductNo = item.First().ProductNo;
                inputBillDeatilQueryListOut.NoTaxCost = item.First().NoTaxCost;
                inputBillDeatilQueryListOut.TaxCost = item.First().TaxCost;
                inputBillDeatilQueryListOut.TaxRate = item.First().TaxRate;

                inputBillDeatilQueryListOut.Quantity = item.Sum(x => x.Quantity);

                inputBillDeatilQueryListOut.TaxAmount = item.Sum(x => x.TaxAmount);
                inputBillDeatilQueryListOut.NoTaxAmount = item.Sum(x => x.NoTaxAmount);

                inputBillDeatilQueryListOut.storeInCodes = item.Select(x => x.StoreInItemCode).ToList();

                reslist.Add(inputBillDeatilQueryListOut);
            }
            #endregion

            //总条数
            var count = reslist.Count();

            //分页
            var list = reslist.Skip((query.page - 1) * query.limit).Take(query.limit).ToList();

            return (list, count);


        }

        /// <summary>
        /// 统计入库单发票明细
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<(List<LotInfo>, int)> GetStoreInItemCodeReceivedList(InputBillQueryInputDeatil input)
        {
            //返回数据
            List<LotInfo> list = new List<LotInfo>();
            try
            {
                var productids = new List<Guid>();
                if (input.LotInfo != null)
                {
                    productids = input.LotInfo.Select(p => p.productId).ToList();
                }
                if (input.StoreInItemCode.ToLower().Contains("so") || input.StoreInItemCode.ToLower().Contains("si"))
                {
                    StoreInDetailQueryInput query = new StoreInDetailQueryInput();
                    query.supposedType = 2;
                    query.limit = int.MaxValue;
                    query.page = 1;
                    query.storeInCode = input.StoreInItemCode;
                    query.productNo = input.ProductNo;
                    //总条数
                    var count = 0;

                    #region 数据处理和转化
                    if (input.IsStoreIn == true)
                    {
                        var res = await _inventoryApiClient.QueryStoreInByCompany(query);
                        if (res == null || res.list == null)
                        {
                            _logger.LogInformation($"入库清单，无数据{JsonConvert.SerializeObject(query)}");
                            return (list, 0);
                        }
                        count = res.total;
                        //赋值
                        foreach (var item in res.list)
                        {
                            foreach (var itemLotInfo in item.LotInfo)
                            {
                                if (input.LotInfo != null && input.LotInfo.Any())
                                {
                                    if (input.LotInfo.Select(p => p.storeInDetailId).Contains(itemLotInfo.storeInDetailId))
                                    {
                                        list.Add(itemLotInfo);
                                    }
                                }
                                else
                                {
                                    list.Add(itemLotInfo);
                                }
                            }
                        }
                    }
                    else
                    {
                        query.storeOutCode = query.storeInCode;
                        var res = await _inventoryApiClient.QueryStoreOutByCompany(query);
                        if (res == null || res.list == null)
                        {
                            _logger.LogInformation($"经销调出清单，无数据{JsonConvert.SerializeObject(query)}");
                            return (list, 0);
                        }
                        count = res.total;
                        var productIds = input.LotInfo.Select(p => p.productId).ToList();
                        var lotNos = input.LotInfo.Select(p => p.lotNo).ToList();
                        //赋值
                        foreach (var item in res.list)
                        {
                            foreach (var itemLotInfo in item.LotInfo)
                            {
                                if (input.LotInfo != null && input.LotInfo.Any())
                                {
                                    if (productIds.Contains(itemLotInfo.productId) && lotNos.Contains(itemLotInfo.lotNo))
                                    {
                                        list.Add(itemLotInfo);
                                    }
                                }
                                else
                                {
                                    list.Add(itemLotInfo);
                                }
                            }
                        }
                    }
                }
                else /*if (input.StoreInItemCode.ToLower().Contains("pua"))*/
                {
                    var res = await _purchaseApiClient.GetConsignToPurchaseDetailGroup(new ConsignToPurchaseDetailGroupInput
                    {
                        Code = input.StoreInItemCode,
                        pageIndex = input.page,
                        pageSize = input.limit,
                        InvoiceStatus = 2,
                        StrategyQuery = new StrategyQuery
                        {
                            FunctionUri = "metadata://fam"
                        },
                    });
                    if (res == null || res.Data == null)
                    {
                        _logger.LogInformation($"采购清单，无数据");
                        return (list, 0);
                    }
                    if (productids.Count() > 0)
                    {
                        res.Data = res.Data.Where(p => p.ProductId.HasValue && productids.Contains(p.ProductId.Value)).ToList();
                    }
                    //赋值
                    foreach (var item in res.Data)
                    {
                        foreach (var detail in item.Details)
                        {
                            list.Add(new LotInfo
                            {
                                quantity = detail.Quantity,
                                invoiceQuantity = detail.InvoiceQuantity,
                                productId = string.IsNullOrEmpty(detail.ProductId) ? Guid.Empty : Guid.Parse(detail.ProductId),
                            });
                        }
                    }
                }

                #endregion

                //排序
                list = list.Where(p => (string.IsNullOrEmpty(input.barcode) ? true : p.barcode.Equals(input.barcode))).ToList();

                if (input.produceDate.HasValue)
                {
                    var produceDateStr = DateTimeHelper.LongToDateTime(input.produceDate.Value).ToString("yyyy-MM-dd");
                    list = list.Where(p => p.produceDateStr == produceDateStr).ToList();
                }
                list = list.Skip((input.page - 1) * input.limit).Take(input.limit).OrderByDescending(x => x.produceDateStr).ToList();
                return (list, list.Count);
            }
            catch (Exception e)
            {
                return (list, 0);
            }
        }

        public async Task<List<PurchaseInputBillOutput>> GetPurchaseInputBill(PurchaseInputBillInput input)
        {
            var debtCodes = input.Codes.ToHashSet();

            // 先查出所有相关的 Debts
            var debts = await _db.Debts
                .Where(d => debtCodes.Contains(d.PurchaseCode))
                .Select(d => new { d.PurchaseCode, d.RelateCode, d.BillCode })
                .ToListAsync();

            // 取出所有相关的 StoreInItemCode
            var relateCodes = debts.Select(d => d.RelateCode).Where(x => !string.IsNullOrEmpty(x)).ToHashSet();
            var billCodes = debts.Select(d => d.BillCode).Where(x => !string.IsNullOrEmpty(x)).ToHashSet();

            // 一次查出所有相关的 InputBillSubmitDetails
            var details = await _db.InputBillSubmitDetails
                .Where(d => relateCodes.Contains(d.StoreInItemCode) || billCodes.Contains(d.StoreInItemCode))
                .Select(detail => new
                {
                    detail.StoreInItemCode,
                    detail.ProductNameId,
                    detail.ProductName,
                    detail.ProductNo,
                    detail.ProductId,
                    detail.TaxRate,
                    detail.NoTaxCost,
                    detail.TaxCost,
                    detail.Quantity,
                    detail.NoTaxAmount,
                    detail.TaxAmount,
                    detail.InputBill,
                })
                .AsNoTracking()
                .ToListAsync();

            // 组装结果
            var result = (
                from debt in debts
                from detail in details
                where
                    (debt.RelateCode == detail.StoreInItemCode || debt.BillCode == detail.StoreInItemCode)
                select new PurchaseInputBillOutput
                {
                    InvoiceNumber = detail.InputBill.InvoiceNumber,
                    CompanyName = detail.InputBill.CompanName,
                    AgentName = detail.InputBill.AgentName,
                    BillTime = detail.InputBill.BillTime,
                    Type = detail.InputBill.Type,
                    CreatedTime = detail.InputBill.CreatedTime,
                    InvoiceCode = detail.InputBill.InvoiceCode,
                    PurchaseDutyNumber = detail.InputBill.PurchaseDutyNumber,
                    SaleDutyNumber = detail.InputBill.SaleDutyNumber,
                    StoreInItemCode = detail.StoreInItemCode,
                    PurchaseCode = debt.PurchaseCode,
                    Status = detail.InputBill.Status,
                    InvoiceAmount = detail.InputBill.Amount,
                    TaxAmount = detail.InputBill.TaxAmount,
                    NoTaxAmount = detail.InputBill.NotaxAmount,
                    ProductNameId = detail.ProductNameId,
                    ProductName = detail.ProductName,
                    ProductNo = detail.ProductNo,
                    ProductId = detail.ProductId,
                    TaxRate = detail.TaxRate,
                    NoTaxCost = detail.NoTaxCost,
                    TaxCost = detail.TaxCost,
                    Quantity = detail.Quantity
                }
            ).Distinct().ToList();

            return result;
        }

        /// <summary>
        /// 根据 应付单号获取进项发票
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<(List<InputBillQueryListOut>, int)> GetListInputBillByDebtCodeAsync(InputBillQueryInput query)
        {
            Expression<Func<InputBillPo, bool>> exp = z => 1 == 1;
            var strategyInput = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("company"))
                {
                    return (new List<InputBillQueryListOut>(), 0);
                }
            }
            #region 查询条件
            if (query.DebtType == (int)DebtTypeEnum.selforder || query.DebtType == (int)DebtTypeEnum.selfreturn)
            {
                if (!string.IsNullOrEmpty(query.StoreInItemCode))//关联单号
                {
                    var idList = await _db.InputBillSubmitDetails.Where(z => z.StoreInItemCode == query.StoreInItemCode).Select(z => z.InputBillId).ToListAsync();

                    exp = exp.And(z => idList.Contains(z.Id));
                }

            }
            else if (query.DebtType == (int)DebtTypeEnum.order || query.DebtType == (int)DebtTypeEnum.revise || query.DebtType == (int)DebtTypeEnum.selfrevise || query.DebtType == (int)DebtTypeEnum.servicefee)
            {
                if (!string.IsNullOrEmpty(query.DebtCode))//应付单号
                {
                    var idList = await _db.InputBillSubmitDetails.Where(z => z.StoreInItemCode == query.DebtCode).Select(z => z.InputBillId).ToListAsync();
                    var _mergeInputBillIds = await (from md in _db.MergeInputBillDebts
                                                    join mr in _db.MergeInputBillRelations on md.MergeInputBillId equals mr.MergeInputBillId
                                                    where md.DebtCode == query.DebtCode
                                                    select mr.InputBillId).ToListAsync();
                    if (_mergeInputBillIds.Any())
                    {
                        idList.AddRange(_mergeInputBillIds);
                    }
                    exp = exp.And(z => idList.Contains(z.Id));
                }
            }
            else //两个类型都不是，直接返回空
            {
                return (new List<InputBillQueryListOut>(), 0);
            }
            exp = exp.And(z => z.Status == (int)InputBillStatusEnum.Submitted);

            // 取消勾稽状态过滤
            if (query.cancelReconciliationStatusName.HasValue)
            {
                if (query.cancelReconciliationStatusName.Value)
                {
                    exp = exp.And(z => z.IsCancelledReconciliation == query.cancelReconciliationStatusName.Value);
                }
                else
                {
                    exp = exp.And(z => z.IsCancelledReconciliation != true);
                }
            }

            // 取消勾稽时间范围过滤
            if (!string.IsNullOrWhiteSpace(query.beginCancelReconciliationTime) && !string.IsNullOrWhiteSpace(query.endCancelReconciliationTime))
            {
                var endTime = DateTimeHelper.LongToDateTime(Convert.ToInt64(query.endCancelReconciliationTime));
                var startTime = DateTimeHelper.LongToDateTime(Convert.ToInt64(query.beginCancelReconciliationTime));
                exp = exp.And(z => z.CancelReconciliationTime.HasValue && z.CancelReconciliationTime.Value <= endTime && z.CancelReconciliationTime.Value >= startTime);
            }
            #endregion
            IQueryable<InputBillPo> baseQuery = _db.InputBills.Where(exp).AsNoTracking();//全部
            #region 排序
            {
                baseQuery = baseQuery.OrderByDescending(z => z.CreatedTime);
            }
            #endregion
            #region 获取用户数据策略
            var strategryquery = new StrategyQueryInput() { userId = query.UserId, functionUri = "metadata://fam" };
            baseQuery = await AddStrategyQueryAsync(strategryquery, baseQuery);
            #endregion
            //总条数
            var count = await baseQuery.CountAsync();
            //分页
            var list = await baseQuery.Skip((query.page - 1) * query.limit).Take(query.limit).Select(p => new InputBillQueryListOut()
            {
                Id = p.Id,
                AgentId = p.AgentId,
                AgentName = p.AgentName,
                Amount = p.Amount,
                BillTime = p.BillTime,
                CompanName = p.CompanName,
                CompanyId = p.CompanyId,
                InvoiceCode = p.InvoiceCode,
                InvoiceNumber = p.InvoiceNumber,
                NotaxAmount = p.NotaxAmount,
                PurchaseDutyNumber = p.PurchaseDutyNumber,
                SaleDutyNumber = p.SaleDutyNumber,
                TaxAmount = p.TaxAmount,
                TypeName = p.Type == 1 ? "普票" : "专票",
                Status = (InputBillStatusEnum)p.Status,
                CreatedTime = p.CreatedTime,
                Remark = p.Remark,
                CancelReconciliationTime = p.CancelReconciliationTime,
                IsCancelledReconciliation = p.IsCancelledReconciliation,
            }).ToListAsync();
            return (list, count);
        }

        /// <summary>
        /// 根据应付单号集合获取进项发票号
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public async Task<List<InputBillQueryByDebtCodesOutput>> GetListInputBillByDebtCodesAsync(List<string?>? codes)
        {
            // 使用 HashSet 提高查找性能
            var codeSet = new HashSet<string>(codes);

            // 方法1：使用 Union 合并两个查询路径（推荐，最清晰且高效）
            var query = _db.InputBills.Where(ib => ib.Status == 2)
                .Join(_db.InputBillSubmitDetails,
                    ib => ib.Id,
                    ibsd => ibsd.InputBillId,
                    (ib, ibsd) => new { ib, ibsd })
                .Join(_db.Debts.Where(d => codeSet.Contains(d.BillCode)),
                    temp => temp.ibsd.StoreInItemCode,
                    d => d.RelateCode,
                    (temp, d) => new { temp.ib, temp.ibsd, d })
                .Select(x => new InputBillQueryByDebtCodesOutput
                {
                    InvoiceNumber = x.ib.InvoiceNumber,
                    Status = x.ib.Status,
                    BillCode = x.d.BillCode,
                    Value = x.ib.Amount,
                    NoTaxAmount = x.ibsd.NoTaxAmount,
                    Id = x.ibsd.Id,
                })
                .Union(
                    _db.InputBills.Where(ib => ib.Status == 2)
                    .Join(_db.InputBillSubmitDetails,
                        ib => ib.Id,
                        ibsd => ibsd.InputBillId,
                        (ib, ibsd) => new { ib, ibsd })
                    .Join(_db.Debts.Where(d => codeSet.Contains(d.BillCode)),
                        temp => temp.ibsd.StoreInItemCode,
                        d => d.BillCode,
                        (temp, d) => new { temp.ib, temp.ibsd, d })
                    .Select(x => new InputBillQueryByDebtCodesOutput
                    {
                        InvoiceNumber = x.ib.InvoiceNumber,
                        Status = x.ib.Status,
                        BillCode = x.d.BillCode,
                        Value = x.ib.Amount,
                        NoTaxAmount = x.ibsd.NoTaxAmount,
                        Id = x.ibsd.Id
                    })
                );

            var result = await query.ToListAsync();
            return result;
        }

    }
}
