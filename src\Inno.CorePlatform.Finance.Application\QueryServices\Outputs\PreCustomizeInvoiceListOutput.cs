﻿using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 预开票列表查询出参
    /// </summary>
    public class PreCustomizeInvoiceListOutput
    {
        /// <summary>
        /// id
        /// </summary>
        public Guid? Id { get; set; }
        /// <summary>
        /// 项目Id
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 项目编码
        /// </summary>
        public string? ProjectCode { get; set; }
        /// <summary>
        /// 核算部门路径
        /// </summary>
        public string? BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 核算部门名称
        /// </summary>
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 核算部门id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }
        /// <summary>
        /// 公司NameCode
        /// </summary>
        public string? NameCode { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public string? CustomerId { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public PreCustomizeInvoiceItemStatusEnum? Status { get; set; }
        /// <summary>
        /// 状态描述
        /// </summary>
        public string? StatusStr 
        {
            get
            {
                return Status != null ? Status.GetDescription() : "未知状态";
            }
        }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreatedBy { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset? CreatedTime { get; set; }

        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? Value { get; set; }
    }

    /// <summary>
    /// 预开票列表页签数量
    /// </summary>
    public class PreCustomizeInvoiceListTabOutput
    {
        /// <summary>
        /// 待提交
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 已提交
        /// </summary>
        public int WaitAuditCount { get; set; }
        /// <summary>
        /// 已完成
        /// </summary>
        public int ComplateCount { get; set; }
        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
    }

    /// <summary>
    /// 预开票明细列表出参
    /// </summary>
    public class PreCustomizeInvoiceDetailsOutput
    {
        /// <summary>
        /// 预开票主表id
        /// </summary>
        public Guid? PreCustomizeInvoiceItemId { get; set; }
        /// <summary>
        /// 品名
        /// </summary>
        public string? ProductName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? PackUnit { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Value { get; set; }


        /// <summary>
        /// 税率
        /// </summary>
        public decimal? TaxRate { get; set; }

        /// <summary>
        /// 税收分类编码
        /// </summary>
        public string? TaxTypeNo { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 货号Id
        /// </summary>
        public Guid? ProductId { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string? ProductNo { get; set; }

        /// <summary>
        /// 行性质（商品行、折扣行）
        /// </summary>
        public string? Tag { get; set; }
    }
}
