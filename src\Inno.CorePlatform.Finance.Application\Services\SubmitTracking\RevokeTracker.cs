using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.Enums;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Application.Services.SubmitTracking
{
    /// <summary>
    /// 撤销跟踪器，用于跟踪已成功调用的撤销接口，以便在失败时进行恢复（重新提交）
    /// </summary>
    public class RevokeTracker
    {
        private readonly ILogger _logger;
        private readonly List<RevokeOperation> _successfulOperations = new List<RevokeOperation>();
        private readonly Guid _mergeInputBillId;
        private readonly string _mergeInvoiceNumber;
        private readonly MergeInputBillPo _mergeInputBill;
        private readonly List<MergeInputBillSubmitDetailPo> _submitDetails;
        private readonly string _currentUserName;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="mergeInputBillId">合并进项发票ID</param>
        /// <param name="mergeInvoiceNumber">合并发票号</param>
        /// <param name="mergeInputBill">合并进项发票</param>
        /// <param name="submitDetails">提交明细</param>
        /// <param name="currentUserName">当前用户名</param>
        public RevokeTracker(
            ILogger logger,
            Guid mergeInputBillId,
            string mergeInvoiceNumber,
            MergeInputBillPo mergeInputBill,
            List<MergeInputBillSubmitDetailPo> submitDetails,
            string currentUserName)
        {
            _logger = logger;
            _mergeInputBillId = mergeInputBillId;
            _mergeInvoiceNumber = mergeInvoiceNumber;
            _mergeInputBill = mergeInputBill;
            _submitDetails = submitDetails;
            _currentUserName = currentUserName;
        }

        /// <summary>
        /// 添加成功操作
        /// </summary>
        /// <param name="operation">操作类型</param>
        /// <param name="businessType">业务类型</param>
        /// <param name="data">操作数据</param>
        public void AddSuccessfulOperation(RevokeOperationType operation, BusinessType? businessType, object data)
        {
            _successfulOperations.Add(new RevokeOperation
            {
                OperationType = operation,
                BusinessType = businessType,
                Data = data
            });

            _logger.LogInformation("RevokeTracker - 添加成功操作, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}",
                operation, businessType, _mergeInputBillId);
        }

        /// <summary>
        /// 执行恢复操作（重新提交）
        /// </summary>
        /// <param name="recoveryHandler">恢复处理器</param>
        /// <returns>恢复结果</returns>
        public async Task<bool> RecoverAsync(IRevokeRecoveryHandler recoveryHandler)
        {
            _logger.LogWarning("RevokeTracker - 开始执行恢复操作, 成功操作数量: {Count}, MergeInputBillId: {MergeInputBillId}",
                _successfulOperations.Count, _mergeInputBillId);

            // 反向遍历成功操作列表，按照与撤销相反的顺序进行恢复（重新提交）
            for (int i = _successfulOperations.Count - 1; i >= 0; i--)
            {
                var operation = _successfulOperations[i];
                try
                {
                    _logger.LogInformation("RevokeTracker - 恢复操作, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}",
                        operation.OperationType, operation.BusinessType, _mergeInputBillId);

                    switch (operation.OperationType)
                    {
                        case RevokeOperationType.KingdeeRevoke:
                            // 恢复金蝶接口调用（重新提交）
                            await recoveryHandler.RecoverKingdeeRevokeAsync(_mergeInputBill, _submitDetails, _currentUserName);
                            break;
                        case RevokeOperationType.DistributionPurchaseRevoke:
                            // 恢复经销购货入库撤销（重新提交）
                            var distributionPurchaseDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.DistributionPurchase).ToList();
                            if (distributionPurchaseDetails.Any())
                            {
                                await recoveryHandler.RecoverDistributionPurchaseRevokeAsync(_mergeInputBill, distributionPurchaseDetails);
                            }
                            break;
                        case RevokeOperationType.ConsignmentToPurchaseRevoke:
                            // 恢复寄售转购货撤销（重新提交）
                            var consignmentToPurchaseDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.ConsignmentToPurchase).ToList();
                            if (consignmentToPurchaseDetails.Any())
                            {
                                await recoveryHandler.RecoverConsignmentToPurchaseRevokeAsync(_mergeInputBill, consignmentToPurchaseDetails);
                            }
                            break;
                        case RevokeOperationType.ServiceFeeProcurementRevoke:
                            // 恢复服务费采购撤销（重新提交）
                            var serviceFeeDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.ServiceFeeProcurement).ToList();
                            if (serviceFeeDetails.Any())
                            {
                                await recoveryHandler.RecoverServiceFeeProcurementRevokeAsync(_mergeInputBill, serviceFeeDetails);
                            }
                            break;
                        case RevokeOperationType.DistributionTransferRevoke:
                            // 恢复经销调出撤销（重新提交）
                            var distributionTransferDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.DistributionTransfer).ToList();
                            if (distributionTransferDetails.Any())
                            {
                                await recoveryHandler.RecoverDistributionTransferRevokeAsync(_mergeInputBill, distributionTransferDetails);
                            }
                            break;
                        case RevokeOperationType.PurchaseRevisionRevoke:
                            // 恢复购货修订撤销（重新提交）
                            var purchaseRevisionDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.PurchaseRevision).ToList();
                            if (purchaseRevisionDetails.Any())
                            {
                                await recoveryHandler.RecoverPurchaseRevisionRevokeAsync(_mergeInputBill, purchaseRevisionDetails);
                            }
                            break;
                        case RevokeOperationType.ExchangeToReturnRevoke:
                            // 恢复换货转退货撤销（重新提交）
                            var exchangeToReturnDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.ExchangeToReturn).ToList();
                            if (exchangeToReturnDetails.Any())
                            {
                                await recoveryHandler.RecoverExchangeToReturnRevokeAsync(_mergeInputBill, exchangeToReturnDetails);
                            }
                            break;
                        case RevokeOperationType.LossRecognitionRevoke:
                            // 恢复损失确认撤销（重新提交）
                            var lossRecognitionDetails = _submitDetails.Where(x => x.BusinessType == (int)BusinessType.LossRecognition).ToList();
                            if (lossRecognitionDetails.Any())
                            {
                                await recoveryHandler.RecoverLossRecognitionRevokeAsync(_mergeInputBill, lossRecognitionDetails);
                            }
                            break;
                        default:
                            _logger.LogWarning("RevokeTracker - 不支持的操作类型: {OperationType}, MergeInputBillId: {MergeInputBillId}",
                                operation.OperationType, _mergeInputBillId);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "RevokeTracker - 恢复操作失败, 操作类型: {OperationType}, 业务类型: {BusinessType}, MergeInputBillId: {MergeInputBillId}, 错误: {ErrorMessage}",
                        operation.OperationType, operation.BusinessType, _mergeInputBillId, ex.Message);
                    // 继续恢复其他操作
                }
            }

            _logger.LogInformation("RevokeTracker - 恢复操作完成, MergeInputBillId: {MergeInputBillId}", _mergeInputBillId);
            return true;
        }
    }

    /// <summary>
    /// 撤销操作类型
    /// </summary>
    public enum RevokeOperationType
    {
        /// <summary>
        /// 金蝶撤销
        /// </summary>
        KingdeeRevoke,

        /// <summary>
        /// 经销购货入库撤销
        /// </summary>
        DistributionPurchaseRevoke,

        /// <summary>
        /// 寄售转购货撤销
        /// </summary>
        ConsignmentToPurchaseRevoke,

        /// <summary>
        /// 服务费采购撤销
        /// </summary>
        ServiceFeeProcurementRevoke,

        /// <summary>
        /// 经销调出撤销
        /// </summary>
        DistributionTransferRevoke,

        /// <summary>
        /// 购货修订撤销
        /// </summary>
        PurchaseRevisionRevoke,

        /// <summary>
        /// 换货转退货撤销
        /// </summary>
        ExchangeToReturnRevoke,

        /// <summary>
        /// 损失确认撤销
        /// </summary>
        LossRecognitionRevoke
    }

    /// <summary>
    /// 撤销操作
    /// </summary>
    public class RevokeOperation
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public RevokeOperationType OperationType { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public BusinessType? BusinessType { get; set; }

        /// <summary>
        /// 操作数据
        /// </summary>
        public object Data { get; set; }
    }
}
