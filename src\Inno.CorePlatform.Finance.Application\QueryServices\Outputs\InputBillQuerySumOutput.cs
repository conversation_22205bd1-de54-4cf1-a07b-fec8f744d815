﻿using Inno.CorePlatform.Finance.Data.Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    /// <summary>
    /// 进项票明细汇总
    /// </summary>
    public class InputBillQuerySumOutput
    {
        /// <summary>
        /// 数量
        /// </summary>
        public decimal NumberSum { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal ValueSum {  get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public decimal TaxValueSum {  get; set; }
    }
}
