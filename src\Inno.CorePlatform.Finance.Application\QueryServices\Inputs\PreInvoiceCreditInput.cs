﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 预开票发票入参
    /// </summary>
    public class PreInvoiceCreditInput
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 开票申请单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }
        /// <summary>
        /// 应收总金额
        /// </summary>
        public decimal? CreditValue { get; set; }
        /// <summary>
        /// 应收总金额
        /// </summary>
        public List<PartCreditInfoForPreInvoice>? Credits { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? BlueInvoiceNo { get; set; }
    }

    /// <summary>
    /// 部分应收信息 - 预开票
    /// </summary>
    public class PartCreditInfoForPreInvoice
    {
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? BillCode { get; set; }
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? Value { get; set; }
    }
}
