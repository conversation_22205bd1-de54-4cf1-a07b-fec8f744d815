﻿using Inno.CorePlatform.Common.EntityFramework;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Data;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain.AggregateRoot;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.PaymentAggregate;
using Inno.CorePlatform.Finance.Domain.PortInterfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using NPOI.OpenXmlFormats.Wordprocessing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Adapter.Repositories
{
    public class PaymentAutoAgent : EfBaseRepository<Guid, PaymentAutoAgentBankInfo, PaymentAutoAgentBankInfoPo>, IPaymentAutoAgent
    {
        private FinanceDbContext _db;
        public PaymentAutoAgent(FinanceDbContext dbContext) : base(dbContext)
        {
            _db = dbContext;
        }

        public async Task<BaseResponseData<int>> CreateBankInfos(List<PaymentAutoAgentBankInfo> bankInfos)
        {
            var insertList = bankInfos.Adapt<List<PaymentAutoAgentBankInfoPo>>();
            insertList.ForEach(p =>
            {
                p.Id = Guid.NewGuid();
            });
            var exists = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == insertList.FirstOrDefault().PaymentAutoItemId).ToListAsync();
            _db.PaymentAutoAgentBankInfos.RemoveRange(exists);
            await _db.PaymentAutoAgentBankInfos.AddRangeAsync(insertList);
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("操作成功");
        }
        public async Task<BaseResponseData<int>> DeleteByPaymentAutoItemId(Guid itemId)
        {
            var data = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == itemId).ToListAsync();
            _db.PaymentAutoAgentBankInfos.RemoveRange(data);
            await _db.SaveChangesAsync();
            return BaseResponseData<int>.Success("操作成功");
        }



        public async Task<BaseResponseData<int>> GetAgentBankCount(Guid itemId)
        {
            var banks = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == itemId).AsNoTracking().ToListAsync();
            return new BaseResponseData<int>() { Code = CodeStatusEnum.Success, Data = banks.Count() };
        }
        public async Task<List<PaymentAutoAgentBankInfo>> GetBankInfoByPaymentAutoItemId(Guid itemId)
        {
            var bank = await _db.PaymentAutoAgentBankInfos.Where(p => p.PaymentAutoItemId == itemId).Select(t=>t.Adapt<PaymentAutoAgentBankInfo>()).AsNoTracking().ToListAsync();
            return bank;
        }
        public override Task<int> UpdateAsync(PaymentAutoAgentBankInfo root)
        {
            throw new NotImplementedException();
        }

        protected override PaymentAutoAgentBankInfoPo CreateDeletingPo(Guid id)
        {
            throw new NotImplementedException();
        }

        protected override Task<PaymentAutoAgentBankInfoPo> GetPoWithIncludeAsync(Guid id)
        {
            throw new NotImplementedException();
        }


    }
}
