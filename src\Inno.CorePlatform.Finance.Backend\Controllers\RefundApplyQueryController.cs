﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Adapter.Clients.Competence;
using Inno.CorePlatform.Finance.Application.Common;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.Payment;
using Inno.CorePlatform.Finance.Application.DTOs.Projects;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Inno.CorePlatform.Finance.Application.QueryServices.AppService;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Data.Models;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.Credits;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RecognizeReceive;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RefundApplyQueryController : BaseController
    {
        private readonly ICreditQueryService _creditQueryService;
        private readonly ILogger<PaymentQueryController> _logger;
        private readonly IKingdeeApiClient _kingdeeApiClient;
        private readonly IPCApiClient _pCApiClient;
        private readonly IBDSApiClient _bDSApiClient;
        private readonly IRefundQueryService _refundQueryService;
        private readonly IProjectMgntApiClient _projectMgntApiClient;
        private readonly IRecognizeReceiveQueryService _recognizeReceiveQueryService;
        private readonly IPaymentQueryService _paymentQueryService;
        /// <summary>
        /// 构造
        /// </summary>
        public RefundApplyQueryController(ICreditQueryService creditQueryService,
            ILogger<PaymentQueryController> logger,
            IKingdeeApiClient kingdeeApiClient,
            IPCApiClient pCApiClient,
            IBDSApiClient bDSApiClient,
            IRefundQueryService refundQueryService,
            IProjectMgntApiClient projectMgntApiClient,
            IRecognizeReceiveQueryService recognizeReceiveQueryService,
            IPaymentQueryService paymentQueryService, ISubLogService subLog) : base(subLog)
        {
            _creditQueryService = creditQueryService;
            _logger = logger;
            _kingdeeApiClient = kingdeeApiClient;
            _pCApiClient = pCApiClient;
            _bDSApiClient = bDSApiClient;
            _refundQueryService = refundQueryService;
            _projectMgntApiClient = projectMgntApiClient;
            _recognizeReceiveQueryService = recognizeReceiveQueryService;
            _paymentQueryService = paymentQueryService;
        }

        /// <summary>
        /// 获取退款申请列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<QueryPaymentRefundOutputData>> GetList(QueryPaymentRefundInput input)
        {
            var strategyInput = new StrategyQueryInput() { userId = CurrentUser.Id, functionUri = "metadata://fam" };
            var strategry = await _pCApiClient.GetStrategyAsync(strategyInput);
            if (strategry != null)
            {
                var rowStrategies = strategry.RowStrategies;
                if (!rowStrategies.Keys.Contains("accountingDept") || !rowStrategies.Keys.Contains("company"))
                {
                    return new ResponseData<QueryPaymentRefundOutputData>
                    {
                        Data = new Data<QueryPaymentRefundOutputData>()
                    };
                }
            }
            if (input.paymentNum == null || !input.paymentNum.Any())
            {

                if (strategry != null && strategry.RowStrategies.Any())
                {
                    foreach (var key in strategry.RowStrategies.Keys)
                    {
                        if (key == "company")
                        {
                            if (!strategry.RowStrategies[key].Any(z => z == "@all"))
                            {
                                var strategList = strategry.RowStrategies[key].Select(z => z.ToUpper()).ToList();
                                input.paymentNum = strategList;
                            }
                            break;
                        }
                    }
                }
            }
            if (input.paymentNum != null && input.paymentNum.Any())
            {
                var daprCompanies = await _bDSApiClient.GetCompanyInfoAsync(new Application.CompetenceCenter.BDSCenter.BDSBaseInput
                {
                    ids = input.paymentNum
                });
                input.paymentNum = daprCompanies.Select(p => p.nameCode).ToList();
            }

            var ret = new ResponseData<QueryPaymentRefundOutputData>();
            if (!input.status.HasValue || input.status.Value == Domain.RefundStatusEnum.waitSubmit)
            {
                //无状态或待提交查询本地保存的数据
                var (list, count) = await _refundQueryService.GetListAsync(input);
                ret.Data = new Data<QueryPaymentRefundOutputData>
                {
                    List = list,
                    Total = count,
                };
            }
            else if (input.refundParty == 0)
            {
                //获取已冲销的
                input.status = RefundStatusEnum.Complate;
                var (list, count) = await _refundQueryService.GetListAsync(input);
                ret.Data = new Data<QueryPaymentRefundOutputData>
                {
                    List = list,
                    Total = count,
                };
            }
            else
            {
                if (input.status == Domain.RefundStatusEnum.waitAudit)
                {
                    //已提交
                    input.billstatus = "B";
                }
                else if (input.status == Domain.RefundStatusEnum.Complate)
                {
                    //审核通过（完成）
                    input.billstatus = "C";
                }
                else if (input.status == Domain.RefundStatusEnum.Refuse)
                {
                    //审核不通过
                    input.billstatus = "E";
                }
                var kdRet = await _kingdeeApiClient.QueryPaymentRefund(input);
                var lst = new List<QueryPaymentRefundOutputData>();
                if (kdRet.Data != null && kdRet.Data.list != null && kdRet.Data.list.Any())
                {
                    lst = kdRet.Data.list.OrderByDescending(p => p.createtime).ToList();//按时间倒序
                    if (!string.IsNullOrEmpty(input.searchKey))
                    {
                        lst = lst.Where(p => (!string.IsNullOrEmpty(p.billno) && p.billno.Contains(input.searchKey)) || (!string.IsNullOrEmpty(p.payment) && p.payment.Contains(input.searchKey)) || (!string.IsNullOrEmpty(p.orgStr) && p.orgStr.Contains(input.searchKey)) || (!string.IsNullOrEmpty(p.e_payee) && p.e_payee.Contains(input.searchKey))).ToList();
                    }
                    foreach (var item in lst)
                    {
                        var company = await _bDSApiClient.GetCompanyMetaInfosAsync(new Application.CompetenceCenter.BDSCenter.Inputs.CompanyMetaInfosInput()
                        {
                            nameCodeEq = item.paymentNum,
                        });
                        if (company != null && company.Count > 0)
                        {
                            item.companyId = company.FirstOrDefault().companyId;
                        }
                    }
                    ret.Data = new Data<QueryPaymentRefundOutputData>
                    {
                        List = lst,
                        Total = kdRet.Data.all,
                    };
                }
            }
            if (ret.Data != null && ret.Data.List != null)
            {
                foreach (var item in ret.Data.List)
                {
                    if (item.paymenttype == "退回收到的保证金" && item.e_payeetype == "bd_supplier")
                    {
                        item.paymenttype = "供应商打错款";
                    }
                }
            }
            return ret;
        }

        /// <summary>
        /// 获取没有冲销的退回应收
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getCredit")]
        public async Task<BaseResponseData<List<Credit>>> getCredit(GetCreditInput input)
        {
            var ret = new BaseResponseData<List<Credit>>
            {
                Data = await _creditQueryService.GetNonAbated(input),
                Code = CodeStatusEnum.Success
            };
            return ret;
        }

        /// <summary>
        /// 查询结算方式
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetSettlementType")]
        public async Task<BaseResponseData<List<PaymentSettlementtypeOutput>>> GetSettlementType(SettlementtypeInput input)
        {
            var kindRet = await _kingdeeApiClient.GetSettlementtype(input);
            if (kindRet.Code != CodeStatusEnum.Success)
            {
                return BaseResponseData<List<PaymentSettlementtypeOutput>>.Failed(500, kindRet.Message);
            }
            else
            {
                if (kindRet.Data != null && kindRet.Data.rows != null)
                {
                    var lst = kindRet.Data.rows.Where(p => p.enable.Equals("1")).Select(p => new PaymentSettlementtypeOutput
                    {
                        Id = p.number,
                        Name = p.name
                    }).ToList();
                    var ret = BaseResponseData<List<PaymentSettlementtypeOutput>>.Success();
                    ret.Data = lst;
                    return ret;
                }
                return BaseResponseData<List<PaymentSettlementtypeOutput>>.Failed(500, "【金蝶】没有返回任何数据");
            }
        }

        /// <summary>
        /// 客户银行收款查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SelectReceiptInfo")]
        public async Task<BaseResponseData<List<CustomerBankOutput>>> SelectReceiptInfo(SelectReceiptInfoInput input)
        {
            var ret = BaseResponseData<List<CustomerBankOutput>>.Success("操作成功！");
            if (!string.IsNullOrEmpty(input.id))
            {
                ret.Data = await _bDSApiClient.SelectReceiptInfo(input);
            }
            return ret;
        }

        /// <summary>
        /// 供应商银行收款查询
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SelectReceiptInfoByAgentId")]
        public async Task<BaseResponseData<List<receipt>>> SelectReceiptInfoByAgentId(SelectReceiptInfoInput input)
        {
            var ret = BaseResponseData<List<receipt>>.Success("操作成功！");
            var ids = new List<Guid>();
            ids.Add(Guid.Parse(input.id));
            if (!string.IsNullOrEmpty(input.id))
            {
                var result = await _bDSApiClient.GetAgentBankInfoByAgentIds(ids);
                if (result != null && result.Count > 0)
                {
                    ret.Data = result.FirstOrDefault().receiptList;
                }
            }
            return ret;
        }

        /// <summary>
        /// 获取金蝶附件
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet("GetKDFilePath")]
        public async Task<BaseResponseData<List<ReceiptNumberModelOutput>>> GetKDFilePath(string code)
        {
            var ret = await _kingdeeApiClient.SelectTheReceiptNumber(new List<ReceiptNumberModelInput> {
                new ReceiptNumberModelInput {
                     paymentNum = code,
                }
            }, type: "payBill");
            return ret;
        }
        /// <summary>
        /// 获取项目根据公司
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpPost("GetProjectInfoByCompanyId")]
        public async Task<BaseResponseData<List<ProjectOutput>>> GetProjectInfoByCompanyId(string companyId)
        {
            var ret = BaseResponseData<List<ProjectOutput>>.Success("操作成功！");
            ret.Data = await _projectMgntApiClient.GetProjectListByCompanyId(companyId);
            return ret;
        }
        /// <summary>
        /// 根据公司+客商进行筛选收款单号
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getReceive")]
        public async Task<BaseResponseData<List<RecognizeReceiveOutput>>> getReceive(string companyCode, string customerName)
        {
            var res = new BaseResponseData<List<RecognizeReceiveOutput>>
            {
                Code = CodeStatusEnum.Success
            };
            if (!string.IsNullOrEmpty(companyCode))
            {
                var result = await _recognizeReceiveQueryService.GetKdReceiveBills(new RecognizeReceiveInput
                {
                    company = new List<string> { companyCode },
                    customerName = customerName,
                    type = new List<string> { ((int)RecognizeReceiveTypeEnum.RefundPurchasePayment).ToString() },
                    displayNearInt = 0
                });
                res.Data = result;
            }
            return res;
        }
        /// <summary>
        /// 根据公司+客商进行筛选游离付款单
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("getPaymnet")]
        public async Task<BaseResponseData<List<PaymentQueryOutput>>> getPayment(string agentId, string companyId, string projectCode)
        {
            var payments = await _paymentQueryService.RefundApplyGetPayment(Guid.Parse(agentId), Guid.Parse(companyId), projectCode);
            var ret = new BaseResponseData<List<PaymentQueryOutput>>
            {
                Data = payments,
                Code = CodeStatusEnum.Success
            };
            return ret;
        }
    }
}
