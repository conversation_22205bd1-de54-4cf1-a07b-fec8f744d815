﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.DTOs.Sell;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain.AggregateRoot.RebateProvision;
using Mapster;
using Microsoft.AspNetCore.Mvc;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RebateProvisionController : BaseController
    {
        private readonly IRebateProvisionQueryService _recognizeReceiveService;
        private readonly ISellApiClient _sellApiClient;
        private readonly IPurchaseApiClient _purchaseApiClient;

        /// <summary>
        /// 返利计提查询
        /// </summary>
        public RebateProvisionController(
            IRebateProvisionQueryService recognizeReceiveService,
            ISellApiClient sellApiClient,
            IPurchaseApiClient purchaseApiClient
            , ISubLogService subLog) : base(subLog)
        {
            _purchaseApiClient = purchaseApiClient;
            _recognizeReceiveService = recognizeReceiveService;
            _sellApiClient = sellApiClient;
        }

        /// <summary>
        /// 获取返利计提清单列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetList")]
        public async Task<ResponseData<RebateProvisionItemQueryOutput>> GetList([FromBody] RebateProvisionItemQueryInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                query.UserName = CurrentUser.UserName;
                var (list, count) = await _recognizeReceiveService.GetListAsync(query);
                return new ResponseData<RebateProvisionItemQueryOutput>
                {
                    Code = 200,
                    Data = new Data<RebateProvisionItemQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 获取返利计提清单明细列表
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetDetailsByItemId")]
        public async Task<ResponseData<RebateProvisionDetailQueryOutput>> GetDetailsByItemId([FromBody] RebateProvisionDetailQueryInput query)
        {
            try
            {
                var (list, count) = await _recognizeReceiveService.GetDetailsByItemIdAsync(query);
                return new ResponseData<RebateProvisionDetailQueryOutput>
                {
                    Code = 200,
                    Data = new Data<RebateProvisionDetailQueryOutput>
                    {
                        List = list,
                        Total = count,
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        /// <summary>
        /// 提交返利计提
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("Submit")]
        public async Task<BaseResponseData<string>> Submit(Guid id)
        {
            var ret = BaseResponseData<string>.Failed(500, "操作失败");
            ret = await _recognizeReceiveService.Submit(id);
            return ret;
        }

        /// <summary>
        /// 获取页签数量
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("GetTabCount")]
        public async Task<BaseResponseData<RebateProvisionItemTabOutput>> GetTabCountAsync([FromBody] RebateProvisionItemQueryInput query)
        {
            query.UserId = CurrentUser.Id.Value;
            query.UserName = CurrentUser.UserName;
            var model = await _recognizeReceiveService.GetTabCountAsync(query);
            return new BaseResponseData<RebateProvisionItemTabOutput>
            {
                Code = CodeStatusEnum.Success,
                Data = model
            };
        }

        /// <summary>
        /// 创建返利计提
        /// </summary>
        /// <returns></returns>
        [HttpPost("Create")]
        public async Task<BaseResponseData<int>> Create([FromBody] RebateProvisionItemCreateInput input)
        {
            input.CurrentUser = CurrentUser.UserName;
            input.UserId = CurrentUser.Id.Value;
            input.UserName = CurrentUser.UserName;
            int count = await _recognizeReceiveService.Create(input);
            if (count > 0)
            {
                await PullDetailsData(new RebateProvisionOfSaleInput { CompanyId = input.CompanyId });
            }
            return new BaseResponseData<int>
            {
                Code = count > 0 ? CodeStatusEnum.Success : CodeStatusEnum.Failed,
                Data = count
            };
        }

        /// <summary>
        /// 删除返利计提
        /// </summary>
        /// <returns></returns>
        [HttpPost("Remove")]
        public async Task<BaseResponseData<int>> Remove([FromBody] RebateProvisionDetailQueryInput input)
        {
            int count = await _recognizeReceiveService.Remove(input);
            return new BaseResponseData<int>
            {
                Code = count > 0 ? CodeStatusEnum.Success : CodeStatusEnum.Failed,
                Data = count
            };
        }


        /// <summary>
        /// 拉取数据
        /// </summary>
        /// <returns></returns>
        [HttpPost("PullDetailsData")]
        public async Task<BaseResponseData<int>> PullDetailsData(RebateProvisionOfSaleInput input)
        {
            var ret = BaseResponseData<int>.Success("操作成功");
            var retCount = await _recognizeReceiveService.RemoveDetails(new RebateProvisionDetailQueryInput
            {
                RebateProvisionItemId = input.RebateProvisionItemId
            });
            var detailOfSale = await _sellApiClient.GetRebateProvision(input);

            if (detailOfSale != null && detailOfSale.Any())
            {
                if (retCount >= 0)
                {
                    var details = detailOfSale.Adapt<List<RebateProvisionDetail>>();
                    await _recognizeReceiveService.AddDetails(details, input.RebateProvisionItemId);
                }
            }
            var detailOfPurchase = await _purchaseApiClient.GetRebateProvision(new Application.DTOs.Purchase.RebateProvisionOfPurchaseInput
            {
                CompanyId = input.CompanyId
            });
            if (detailOfPurchase != null && detailOfPurchase.Any())
            {
                if (retCount >= 0)
                {
                    var details = detailOfPurchase.Adapt<List<RebateProvisionDetail>>();
                    await _recognizeReceiveService.AddDetails(details, input.RebateProvisionItemId);
                }
            }
            return ret;
        }



        /// <summary>
        /// 导入返利计提明细
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost("ImportDetailsData")]
        public async Task<BaseResponseData<int>> ImportDetailsData(Guid id,IFormFile file)
        {
            return await _recognizeReceiveService.ImportDetailsData(id,file,CurrentUser.Id, CurrentUser.UserName);
        }
    }
}
