﻿using Inno.CorePlatform.Finance.Data.Enums;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits
{
    public class InvoiceQueryWebApiInput
    {
        /// <summary>
        /// 制作开票申请编码
        /// </summary>
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 发票号
        /// </summary> 
        public List<string>? InvoiceNos { get; set; }

        /// <summary>
        /// 公司
        /// </summary> 
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 开票时间 开始
        /// </summary>
        public DateTime? InvoiceTimeStart { get; set; }
        /// <summary>
        /// 开票时间 结束
        /// </summary>
        public DateTime? InvoiceTimeEnd { get; set; }
    }

    /// <summary>
    /// 根据发票号获取订单入参
    /// </summary>
    public class OrderInfoForInvoiceNoInput
    {
        /// <summary>
        /// 发票号集合
        /// </summary>
        public List<string> invoiceNos { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? customerId { get; set; }
    }

    public class GetInvoiceByOrderNosInput
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public List<string>? OrderNos { get; set; }
        /// <summary>
        /// 发票号
        /// </summary>
        public List<string>? InvoiceNos { get; set; }
        /// <summary>
        /// 是否取消
        /// </summary>
        public bool? IsCancel { get; set; }
    }

    public class GetInvoiceByOrderNosOutput
    {
        /// <summary>
        /// 发票号
        /// </summary>  
        public string? InvoiceNo { get; set; }


        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>  
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary> 
        public string Type { get; set; }

        /// <summary>
        /// 开票申请单号
        /// </summary> 
        public string? CustomizeInvoiceCode { get; set; }

        /// <summary>
        /// 税额
        /// </summary> 
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public string? CustomerName { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string? CompanyName { get; set; }

        /// <summary>
        /// 业务单元
        /// </summary>
        public string? ServiceName { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string? BusinessDeptFullName { get; set; }
        /// <summary>
        /// 应收单号
        /// </summary>
        public string? CreditBillCode { get; set; }
        /// <summary>
        /// 关联单号
        /// </summary>
        public string? RelateCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        public string StatusStr { get; set; }

        /// <summary>
        /// 发票备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 应收金额
        /// </summary>
        public decimal? CreditAmount { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 是否红冲
        /// </summary> 
        public CustomizeInvoiceChangedStatusEnum? ChangedStatus { get; internal set; }
    }
}
