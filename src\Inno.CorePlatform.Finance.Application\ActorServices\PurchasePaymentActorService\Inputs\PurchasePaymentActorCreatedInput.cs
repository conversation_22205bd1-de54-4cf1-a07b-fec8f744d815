﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.ActorServices.PurchasePaymentActorService.Inputs
{
    public class PurchasePaymentActorCreatedInput
    {
        /// <summary>
        /// 标识位
        /// </summary>
        public Guid? RelateId { get; set; }
        /// <summary>
        /// 采购订单
        /// </summary>
        public Guid? PurchaseOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string? Code { get; set; }

        /// <summary>
        /// 采购类型 1-采购定单，2-寄售转购货，3经销购货修订
        /// </summary>
        public int? PurchaseType { get; set; }

        public string PurchaseTypeStr
        {
            get
            {
                var ret = string.Empty;
                switch (PurchaseType)
                {
                    case 1:
                        ret = "采购定单";
                        break;
                    case 2:
                        ret = "寄售转购货";
                        break;
                    case 3:
                        ret = "经销购货修订";
                        break;
                    case 4:
                        ret = "订单修订";
                        break;
                    case 5:
                        ret = "寄售购货修订";
                        break;
                    default:
                        break;
                }
                return ret;
            }
        }
    }
}
