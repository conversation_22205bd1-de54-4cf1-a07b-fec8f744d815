﻿using Inno.CorePlatform.Finance.Data.Enums;
using Inno.CorePlatform.Finance.Domain;
using System.ComponentModel.DataAnnotations;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Outputs
{
    public class CustomizeInvoiceItemOutput
    {
        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        ///  编号
        /// </summary>
        [MaxLength(200)]
        public string Code { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        public string CompanyName { get; set; }

        /// <summary>
        ///  付款单位Id
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        ///  付款单位名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 发票总金额
        /// </summary> 
        public decimal InvoiceTotalAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum InvoiceType { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public string InvoiceTypeStr
        {
            get
            {
                return InvoiceType.GetDescription();
            }
        }
        /// <summary>
        /// 是否推送金碟
        /// </summary>
        public bool IsPush { get; set; }

        /// <summary>
        /// 是否已开票
        /// </summary>
        public bool IsInvoiced { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? ApproveRemark { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }


        public string? AttachFileIds { get; set; }
        public string? OARequestId { get; set; }
        public CustomizeInvoiceStatusEnum? Status { get; set; }
        public string StatusStr
        {
            get
            {
                return Status.GetDescription();
            }
        }
        /// <summary>
        /// 关系Code
        /// </summary>
        [MaxLength(200)]
        public string? RelationCode { get; set; }

        /// <summary>
        /// 关系类型
        /// </summary>
        public CustomizeInvoiceRelationTypeEunm? RelationType { get; set; }

        /// <summary>
        /// 变更状态
        /// </summary>
        public CustomizeInvoiceChangedStatusEnum? ChangedStatus { get; set; }
        public string ChangedStatusStr
        {
            get
            {
                if (string.IsNullOrEmpty(RelationCode))
                {
                    if (ChangedStatus.HasValue)
                    {

                        return ChangedStatus.GetDescription();
                    }
                    else
                    {
                        return "未红冲";
                    }
                }
                else
                {
                    return "";
                }
            }
        }
        /// <summary>
        /// 销售折让
        /// </summary>
        public bool? IsSaleDiscount { get; set; }
        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 蓝字红冲金额
        /// </summary>  
        public decimal? BlueRedInvoiceAmount { get; set; }
        /// <summary>
        /// 申请方 2=销方申请，1=购方申请-未抵扣，0=购方申请-已抵扣
        /// </summary>
        public int? RedOffsetOpter { get; set; }

        public string RedOffsetOptStr
        {
            get
            {
                var ret = string.Empty;
                if (RedOffsetOpter != null)
                {
                    switch (RedOffsetOpter.Value)
                    {
                        case 0:
                            ret = "购方申请-已抵扣";
                            break;
                        case 1:
                            ret = "购方申请-未抵扣";
                            break;
                        case 2:
                            ret = "销方申请";
                            break;
                        default:
                            break;
                    }
                }
                return ret;

            }
        }
        /// <summary>
        /// 冲红原因 销货退回=1 开票有误=2 服务中止=3 销售折让=4
        /// </summary>
        public int? RedOffsetReason { get; set; }

        public string RedOffsetReasonStr
        {
            get
            {
                var ret = string.Empty;
                if (RedOffsetReason != null)
                {
                    switch (RedOffsetReason.Value)
                    {
                        case 1:
                            ret = "销货退回";
                            break;
                        case 2:
                            ret = "开票有误";
                            break;
                        case 3:
                            ret = "服务中止";
                            break;
                        case 4:
                            ret = "销售折让";
                            break;
                        default:
                            break;
                    }
                }
                return ret;

            }
        }

        public Guid? CustomizeInvoiceClassifyId { get; set; } 
        /// <summary>
        /// 是否有红字确认单，0=否，1=是
        /// </summary>
        public int? IsNoRedConfirm { get; set; }
    }

    public class CustomizeInvoiceClassifyOutput
    {
        /// <summary>
        ///  编号
        /// </summary> 
        public string BillCode { get; set; }
        public Guid Id { get; set; }
        public string CreatedBy { get; set; }
        public Guid CompanyId { get; set; }
        public Guid CustomerId { get; set; }
        public string? Remark { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public string? CompanyName { get; set; }

        public string? CustomerName { get; set; }

        public CustomizeInvoiceStatusEnum? Status { get; set; }
        public string? StatusStr
        {
            get
            {
                if (Status == CustomizeInvoiceStatusEnum.WaitSubmit)
                {
                    return "待提交";
                }
                else if (Status == CustomizeInvoiceStatusEnum.Auditing)
                {
                    return "审批中";
                }
                else
                {
                    return "已审批";
                }
            }
        }
        public string? OARequestId { get; internal set; }
        public DateTimeOffset CreatedTime { get; set; }
        public List<string>? CustomizeInvoiceItemCodes { get; set; }
        public List<string?>? RelationCodes { get; set; }
        public List<string?>? CreditBillCodes { get; set; }
        public string? SaleSystemName { get; set; }

        public string? AttachFileIds { get; set; }
        public string? CustomerEmail { get; set; }
        public List<CustomizeInvoiceStatusEnum?>? CustomizeInvoiceItemStatus { get; set; } = new List<CustomizeInvoiceStatusEnum?>();

        public CustomizeInvoiceClassifyEnum? Classify { get; set; }
        public string? ClassifyStr
        {
            get
            {
                return Classify.HasValue ? Classify.GetDescription() : string.Empty;
            }
        }
        /// <summary>
        /// 关系单号
        /// </summary>
        public string? RelationCode { get; set; }
        /// <summary>
        /// 销售应收子类型 1个人消费者  2平台
        /// </summary>
        public CreditSaleSubTypeEnum? CreditSaleSubType { get; set; }
    }
    /// <summary>
    /// 不同状态数量
    /// </summary>
    public class CustomizeInvoiceItemTabOutput
    {
        /// <summary>
        /// 全部
        /// </summary>
        public int AllCount { get; set; }
        /// <summary>
        /// 临时草稿数量 0
        /// </summary>
        public int WaitSubmitCount { get; set; }
        /// <summary>
        /// 审批中数量
        /// </summary>
        public int AuditingCount { get; set; }

        /// <summary>
        /// 未开票数量 2
        /// </summary>
        public int WaitInvoiceCount { get; set; }
        /// <summary>
        /// 已开票数量 3
        /// </summary>
        public int InvoicedCount { get; set; }
        /// <summary>
        /// 我的审批 5000
        /// </summary>
        public int MyAuditCount { get; set; }
        /// <summary>
        /// 已作废 
        /// </summary>
        public int CancelCount { get; set; }
    }
    public class CustomizeInvoiceDetailOutput
    {
        /// <summary>
        /// 开票下标Index
        /// </summary>
        public string? CustomizeInvoiceIndex { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceAmount { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        ///  原明细id
        /// </summary>

        public string OriginDetailId { get; set; }
        /// <summary>
        ///  货品名称（开票名称）
        /// </summary>

        public string ProductName { get; set; }
        public string OriginProductName { get; set; }
        public Guid? ProductId { get; set; }
        public string? ProductNo { get; set; }
        /// <summary>
        ///  计量单位
        /// </summary> 
        public string PackUnit { get; set; }


        /// <summary>
        ///  计量单位
        /// </summary> 
        public string? OriginPackUnit { get; set; }

        /// <summary>
        ///  规格型号
        /// </summary>

        public string Specification { get; set; }
        /// <summary>
        ///  原始规格型号
        /// </summary>

        public string? OriginSpecification { get; set; }

        /// <summary>
        ///  数量
        /// </summary>

        public decimal Quantity { get; set; }
        /// <summary>
        ///  单价
        /// </summary>

        public decimal Price { get; set; }

        /// <summary>
        ///  原始单价
        /// </summary>

        public decimal? OriginalPrice { get; set; }
        /// <summary>
        ///  金额
        /// </summary>

        public decimal Value { get; set; }
        /// <summary>
        ///  税率
        /// </summary>

        public decimal TaxRate { get; set; }

        /// <summary>
        ///  税率
        /// </summary>

        public decimal CompanyTaxRate { get; set; }

        /// <summary>
        ///  税收分类编码
        /// </summary>

        public string? TaxTypeNo { get; set; }


        /// <summary>
        ///  税额
        /// </summary>

        public decimal TaxAmount { get; set; }
        /// <summary>
        ///  金额
        /// </summary>

        public decimal NoTaxValue
        {
            get
            {
                var temp = (Math.Abs(Value) - Math.Abs(TaxAmount));
                return Value < 0 ? -temp : temp;
            }
        }

        /// <summary>
        ///  应收单号
        /// </summary>

        public string CreditBillCode { get; set; }

        /// <summary>
        ///  关联单号
        /// </summary>
        public string RelateCode { get; set; }
        /// <summary>
        ///  订单号
        /// </summary>

        public string OrderNo { get; set; }
        /// <summary>
        ///  付款单位ID
        /// </summary>

        public string CustomerId { get; set; }
        /// <summary>
        ///  付款单位名称
        /// </summary>

        public string CustomerName { get; set; }
        public string? Tag { get; set; }
        /// <summary>
        /// 运营制作开票单Id
        /// </summary>
        public Guid CustomizeInvoiceItemId { get; set; }
        public string? TaxTypeNoStr { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        public string? AgentId { get; set; }

        /// <summary>
        /// 是否高值（0：否，1：是）
        /// </summary>
        public int? IFHighValue { get; set; }

        /// <summary>
        /// 父Id
        /// </summary>
        public Guid? ParentId { get; set; }


        /// <summary>
        /// 关联Id(红冲)
        /// </summary>
        public Guid? RelateId { get; set; }
        /// <summary>
        ///价格来源
        /// </summary>   
        public PriceSourceEnum? PriceSource { get; set; }

        /// <summary>
        ///价格来源描述
        /// </summary>   
        public string PriceSourceStr
        {
            get
            {
                if (PriceSource.HasValue)
                {
                    return PriceSource.GetDescription();
                }
                else
                {
                    return "";
                }
            }
        }
    }

    public class MergeByInputQuantityPriceInput
    {
        public List<CustomizeInvoiceDetailOutput> customizeInvoiceDetails { get; set; }
        public decimal quantity { get; set; }
        public decimal price { get; set; }
    }
    public class CustomizeInvoiceItemDownLoadOutput
    {
        #region 表头信息
        /// <summary>
        ///  编号
        /// </summary>
        [MaxLength(200)]
        public string Code { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary> 
        public string CompanyName { get; set; }

        /// <summary>
        ///  付款单位名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 发票总金额
        /// </summary> 
        public decimal InvoiceTotalAmount { get; set; }

        /// <summary>
        /// 发票类型
        /// </summary>
        public InvoiceTypeEnum InvoiceType { get; set; }
        /// <summary>
        /// 发票类型
        /// </summary>
        public string InvoiceTypeStr
        {
            get
            {
                return InvoiceType.GetDescription();
            }
        }

        public CustomizeInvoiceStatusEnum? Status { get; set; }
        public string StatusStr
        {
            get
            {
                return Status.GetDescription();
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? ApproveRemark { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTimeOffset CreatedTime { get; set; }

        /// <summary>
        /// 红冲单号
        /// </summary>
        [MaxLength(200)]
        public string? RelationCode { get; set; }

        /// <summary>
        /// 关系类型
        /// </summary>
        public CustomizeInvoiceRelationTypeEunm? RelationType { get; set; }

        /// <summary>
        /// 变更状态
        /// </summary>
        public CustomizeInvoiceChangedStatusEnum? ChangedStatus { get; set; }
        /// <summary>
        /// 销售折让
        /// </summary>
        public bool? IsSaleDiscount { get; set; }
        /// <summary>
        /// 红字信息表编号
        /// </summary>
        public string? RedOffsetCode { get; set; }

        /// <summary>
        /// 蓝字发票号
        /// </summary>
        public string? InvoiceNo { get; set; }

        /// <summary>
        /// 蓝字发票代码
        /// </summary>
        public string? InvoiceCode { get; set; }

        /// <summary>
        /// 蓝字红冲金额
        /// </summary>  
        public decimal? BlueRedInvoiceAmount { get; set; }
        /// <summary>
        /// 申请方 2=销方申请，1=购方申请-未抵扣，0=购方申请-已抵扣
        /// </summary>
        public int? RedOffsetOpter { get; set; }

        public string RedOffsetOptStr
        {
            get
            {
                var ret = string.Empty;
                if (RedOffsetOpter != null)
                {
                    switch (RedOffsetOpter.Value)
                    {
                        case 0:
                            ret = "购方申请-已抵扣";
                            break;
                        case 1:
                            ret = "购方申请-未抵扣";
                            break;
                        case 2:
                            ret = "销方申请";
                            break;
                        default:
                            break;
                    }
                }
                return ret;

            }
        }
        /// <summary>
        /// 冲红原因 销货退回=1 开票有误=2 服务中止=3 销售折让=4
        /// </summary>
        public int? RedOffsetReason { get; set; }

        public string RedOffsetReasonStr
        {
            get
            {
                var ret = string.Empty;
                if (RedOffsetReason != null)
                {
                    switch (RedOffsetReason.Value)
                    {
                        case 1:
                            ret = "销货退回";
                            break;
                        case 2:
                            ret = "开票有误";
                            break;
                        case 3:
                            ret = "服务中止";
                            break;
                        case 4:
                            ret = "销售折让";
                            break;
                        default:
                            break;
                    }
                }
                return ret;

            }
        }
        #endregion

        public List<CustomizeInvoiceDetailOutput> CustomizeInvoiceDetail { get; set; }
    }
}
