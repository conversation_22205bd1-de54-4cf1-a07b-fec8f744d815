﻿using Dapr.Client;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.Constant;

using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Inno.CorePlatform.Finance.Adapter.Clients.Competence
{
    public class PCApiClient : BaseDaprApiClient<PCApiClient>, IPCApiClient
    {
        public PCApiClient(DaprClient daprClient, ILogger<PCApiClient> logger, IHttpContextAccessor httpContextAccessor) : base(daprClient, logger, httpContextAccessor)
        {
        }

        protected override string GetAppId()
        {
            return AppCenter.PERMISSION_APPID;
        }
        protected override async Task<TResponse> DefaultResponseAsync<TResponse>(HttpRequestMessage request)
        {
            var res = await _daprClient.InvokeMethodAsync<TResponse>(request);
            if (res == null)
            {
                _logger.LogInformation($"请求内容 {request.ToJson()} 远程接口未能成功返回信息");
            }
            return res;
        }
        /// <summary>
        /// 获取数据策略
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApplicationException"></exception>
        public async Task<StrategyQueryOutput> GetStrategyAsync(StrategyQueryInput inputParam)
        {
            return await InvokeMethodWithQueryObjectAsync<StrategyQueryInput, StrategyQueryOutput>(inputParam, AppCenter.PERMISSION_GET_STRATEGY);
        }

        
    }
}
