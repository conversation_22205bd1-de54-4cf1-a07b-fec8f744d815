﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs
{
    public class DaprUserInfoOutput
    {
        public string? careerDeptId { get; set; }
        public string? careerDeptName { get; set; }
        public string? careerDeptShortName { get; set; }
        public string? deptId { get; set; }
        public string? deptName { get; set; }
        public string? careerBizDeptId { get; set; }
        public string? deptShortName { get; set; }
        public string? email { get; set; }
        public string id { get; set; }
        public string mobile { get; set; }
        public string personId { get; set; }
        public string staffId { get; set; }
        public string staffName { get; set; }
        public string userId { get; set; }
        public string userName { get; set; }

    }

    
}
