﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplicationInterfaces;
using Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService.Outputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Inputs;
using Inno.CorePlatform.Finance.Application.CompetenceCenter.PMCenter.Inputs;
using Inno.CorePlatform.Finance.Application.Constant;
using Inno.CorePlatform.Finance.Application.DTOs.BDSData;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Application.QueryServices;

namespace Inno.CorePlatform.Finance.Application.ApplicationServices.ApplyBFFService
{
    /// <summary>
    /// 外部能力中心WebApi整合应用服务
    /// 通过自身的组织，将外部能力中心的不规整数据整合为入库能力中心所需的规整数据
    /// </summary>
    public class ApplyBFFService : IApplyBFFService
    {
        private readonly IBDSApiClient _bDSApiClient;

        /// <summary>
        /// 能力中心整合服务构造方法
        /// </summary>
        /// <param name="bDSApiClient">基础数据</param>
        public ApplyBFFService(
            IBDSApiClient bDSApiClient)
        {
            _bDSApiClient = bDSApiClient;
        }

        /// <summary>
        /// 获取系统月度
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public async Task<string> GetSysmonth(Guid companyId)
        {
            return await _bDSApiClient.GetSystemMonth(companyId.ToString());
        }
        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的公司
        /// </summary>
        /// <returns></returns>
        public async Task<List<CompanyOutput>> GetCompanyMetaListAsync(CompanyMetaInput input)
        {
            input.functionUri = StrategyConstant.CreditQuery_LIST_STRATEGY;
            var daprResult = await _bDSApiClient.GetCompanyMetaAsync(input);
            return daprResult.Select(x => new CompanyOutput
            {
                Id = x.id,
                Name = x.name,
                NameCode = x.extraInfo.nameCode
            }).ToList();
        }
        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的医院权限
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<HospitalOutput>> GetHospitalMetaListAsync(HospitalMetaInput input)
        {
            var returnList = new List<HospitalOutput>();
            input.functionUri = StrategyConstant.CreditQuery_LIST_STRATEGY;
            var daprResult = await _bDSApiClient.GetHospitalMetaAsync(input);
            foreach (var item in daprResult)
            {
                var personList = new List<HospitalPersonOutput>();
                if (item.customerDeptRes!=null)
                {
                    foreach (var dept in item.customerDeptRes)
                    {
                        if (dept.customerDeptStaffList!=null)
                        {
                            foreach (var p in dept.customerDeptStaffList)
                            {
                                personList.Add(new HospitalPersonOutput(p, dept));
                            }
                        }
                    }
                }
                
                returnList.Add(new HospitalOutput
                {
                    Id = item.customerId,
                    Name = item.customerName,
                    Persons = personList
                });
            }
            return returnList;
        }
        /// <summary>
        /// 获取客户部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<CompetenceCenter.BDSCenter.Outputs.CustomerDeptMetaInfoOutput>> GetCustomerDeptMetaInfos(BDSBaseInput input)
        {
            return await _bDSApiClient.GetCustomerDept(input);
        }
        
        /// <summary>
        /// 获取用户可操作的核算部门（带数据策略权限）
        /// 也可单独传核算部门Id获取核算部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<BusinessDeptOutput>> GetBusinessDeptMetaAsync(BusinessDeptMetaInput input)
        {
            input.functionUri = StrategyConstant.CreditQuery_LIST_STRATEGY;
            var daprResult = await _bDSApiClient.GetBusinessDeptMetaAsync(input);
            return daprResult.Select(x => new BusinessDeptOutput
            {
                Id = x.id,
                Name = x.name,
                Children = x.extraInfo.children.Select(z => new BusinessDeptOutput
                {
                    Id = z.id,
                    Name = z.name,
                    DeptShortName = z.extraInfo.deptShortName,
                }).ToList(),
                DeptShortName = x.extraInfo.deptShortName,
            }).ToList();
        }
        /// <summary>
        /// 获取厂家信息（带数据策略权限）
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<ProducerOutput>> GetProducerMetaListAsync(ProducerMetaInput input)
        {
            var daprResult = await _bDSApiClient.GetProducerMetaListAsync(input);
            return daprResult.Select(x => new ProducerOutput
            {
                Id = x.id,
                Name = x.name
            }).ToList();
        }

        /// <summary>
        /// 事业部orignValue分隔符
        /// </summary>
        const char BusinessDeptOrignValueSplitChar = '_';

        
        /// <summary>
        /// 根据UserId获取用户的信息
        /// 包含用户所属核算部门信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<UserInfoOutput> GetUserInfoByIdAsync(BDSBaseInput input)
        {
            var daprResult = await _bDSApiClient.GetUserStaffInfoAsync(input);
            return new UserInfoOutput { Id = daprResult.deptId ?? "", Name = daprResult.userName, DeptId = daprResult.deptId ?? "", DeptName = daprResult.deptName, DeptShortName = daprResult.careerDeptShortName ?? "" };
        }
        /// <summary>
        /// 获取公司详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<CompanyInfoOutput>> GetCompanyInfosAsync(BDSBaseInput input)
        {
            var daprResult = await _bDSApiClient.GetCompanyInfoAsync(input);
            return daprResult.Select(p => new CompanyInfoOutput
            {
                Id = p.companyId,
                Name = p.companyName,
                ShortName = p.companyShortName,
                SysMonth = p.sysMonth,
                NameCode = p.nameCode,
                DelayDays = p.delayDays,
                NewBusinessRanges = p.newManageScopeRes != null ? p.newManageScopeRes.Select(t => new NewBusinessRangeOutput { Id = t.id, Name = t.name }).ToList() : new List<NewBusinessRangeOutput>(),
                OldBusinessRanges = p.oldManageScopeRes != null ? p.oldManageScopeRes.Select(t => new OldBusinessRangeOutput { Id = t.id, Name = t.name }).ToList() : new List<OldBusinessRangeOutput>(),
                Status=p.Status,
            }).ToList();
        }
        /// <summary>
        /// 获取供应商详情
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<AgentInfoOutput>> GetAgentInfosAsync(BDSBaseInput input)
        {
            var daprResult = await _bDSApiClient.GetAgentInfoAsync(input);
            return daprResult.Select(p => new AgentInfoOutput
            {
                Id = p.agentId,
                Name = p.agentName,
                AgentPropertyDesc=p.agentPropertyDesc,
                LegalPerson=p.legalPerson,
                SocialCode=p.socialCode,
                TaxRate=p.taxRate,
                NewBusinessRanges = p.newManageScopeRes != null ? p.newManageScopeRes.Select(t => new NewBusinessRangeOutput { Id = t.id, Name = t.name }).ToList() : new List<NewBusinessRangeOutput>(),
                OldBusinessRanges = p.oldManageScopeRes != null ? p.oldManageScopeRes.Select(t => new OldBusinessRangeOutput { Id = t.id, Name = t.name }).ToList() : new List<OldBusinessRangeOutput>()
            }).ToList();
        }
        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<AgentOutput>> GetAgentMetaListAsync(AgentMetaInput input)
        {
            input.functionUri = StrategyConstant.CreditQuery_LIST_STRATEGY;
            var daprResult = await _bDSApiClient.GetAgentMetaListAsync(input);
            return daprResult.Select(x => new AgentOutput
            {
                Id = x.id,
                Name = x.name
            }).ToList();
        }
        

       
        /// <summary>
        /// 根据当前登录用户来获取用户可以操作的业务单元
        /// </summary>
        /// <returns></returns>
        public async Task<List<ServiceOutput>> GetServiceMetaListAsync(ServiceMetaInput input)
        {
            input.functionUri = StrategyConstant.CreditQuery_LIST_STRATEGY;
            var daprResult = await _bDSApiClient.GetServiceMetaAsync(input);
            return daprResult.Select(x => new ServiceOutput
            {
                Id = x.id,
                Name = x.name
            }).ToList();
        }

        /// <summary>
        /// 从用户中心，获取员工数据 byNames
        /// </summary>
        /// <returns></returns>
        public async Task<ResponseData<UserOutput>> GetUserByNamesAsync(GetUserInput input)
        {
            var Result = await _bDSApiClient.GetUserByNamesAsync(input);
            return Result;
        }
        /// <summary>
        /// 查询客户信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<CompetenceCenter.BDSCenter.Outputs.CustomerOutput> GetCustomer(BDSBaseInput input)
        {
            var Result = await _bDSApiClient.GetCustomer(input);
            return Result;
        }

        /// <summary>
        /// 获取业务单元详情列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<List<CompetenceCenter.BDSCenter.Outputs.ServiceOutput>> QueryServiceItem(BDSBaseInput input)
        {
            return await _bDSApiClient.QueryServiceItem(input);
        }
        private void BuildBusinessDeptOutputTree(
            List<BusinessDeptOutput> treeList,
            List<CompetenceCenter.PMCenter.Outputs.BusinessDeptOutput> modelList,
            List<BusinessDeptOutput> topTreeList)
        {
            if (topTreeList == null)
            {
                topTreeList = new List<BusinessDeptOutput>();
            }
            if (treeList == null)
            {
                treeList = new List<BusinessDeptOutput>();
            }
            var modelCount = modelList.Count;
            for (var idx = 0; idx < modelCount; idx++)
            {
                var model = modelList[idx];
                if (model.paths.Count == 0)
                {
                    continue;
                }
                if (model.paths.Count == 1)
                {
                    var treeNode = new BusinessDeptOutput
                    {
                        Id = model.paths[0]
                    };
                    var orignValueArray = model.orignValue[0].Split('_');
                    if (orignValueArray.Length > 1)
                    {
                        treeNode.Name = orignValueArray[1];
                        treeNode.DeptShortName = orignValueArray[1];
                    }
                    topTreeList.Add(treeNode);
                    // 移除顶级
                    modelList.Remove(model);
                    continue;
                }

                // 最后一位是子节点
                var childrenId = model.paths[model.paths.Count - 1];
                var childrenNode = new BusinessDeptOutput();
                childrenNode.Id = childrenId;
                var cOrignValueArray = model.orignValue[0].Split('_');
                if (cOrignValueArray.Length > 1)
                {
                    childrenNode.Name = cOrignValueArray[1];
                    childrenNode.DeptShortName = cOrignValueArray[1];
                }

                // var childrenNode = new CompetenceCenter.PMCenter.Outputs.BusinessDeptOutput();
                //，倒是第二位为最后一位的父节点
                var parentId = model.paths[model.paths.Count - 2];
                var parentNode = treeList.FirstOrDefault(w => w.Id == parentId);
                if (parentNode == null)
                {
                    continue;
                }

                if (parentNode.Children == null)
                {
                    parentNode.Children = new List<BusinessDeptOutput>();
                }
                parentNode.Children.Add(childrenNode);

                // 移除父级
                modelList.RemoveAll(w => w.paths[w.paths.Count - 1] == parentId);
                // 递归查找下一级
                BuildBusinessDeptOutputTree(
                    parentNode.Children,
                    modelList,
                    topTreeList);
            }

        }

        /// <summary>
        /// 获取公司信息
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<CompetenceCenter.BDSCenter.Outputs.CompanyMetaInfosOut>> GetCompanyMetaInfosAsync(CompanyMetaInfosInput input)
        {
            
            var daprResult = await _bDSApiClient.GetCompanyMetaInfosAsync(input);
            return daprResult;
        }
    }
}
