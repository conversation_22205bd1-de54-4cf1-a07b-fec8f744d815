﻿using Inno.CorePlatform.Finance.Backend.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Inno.CorePlatform.Finance.Backend.Filters
{
    public class ParameterLoggingFilter : IAsyncActionFilter
    {
        public async Task OnActionExecutionAsync(
            ActionExecutingContext context,
            ActionExecutionDelegate next)
        {
            
            if (context.Controller is BaseController controller &&
                controller.EnableParameterLogging&& !context.ActionDescriptor.EndpointMetadata.Any(m => m is SkipLoggingAttribute))
            {
                var parameters = context.ActionArguments
                    .ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value ?? new object() // 处理null值
                    );

                await controller.LogParametersAsync(parameters);
            }

            await next();
        }
    }

    [AttributeUsage(AttributeTargets.Method)]
    public class SkipLoggingAttribute : Attribute { }



}
