﻿using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Inputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Interfaces;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;

namespace Inno.CorePlatform.Finance.Backend.Controllers
{
    /// <summary>
    /// 寄售垫资评价
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AdvanceBusinessController : BaseController
    {
        private IAdvanceBusinessQueryService _advanceBusinessQueryService;

        /// <summary>
        /// 
        /// </summary>
        public AdvanceBusinessController(IAdvanceBusinessQueryService advanceBusinessQueryService, ISubLogService subLog) : base(subLog)
        {
            _advanceBusinessQueryService = advanceBusinessQueryService;
        }

        /// <summary>
        /// 查询垫资单
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpPost("GetAdvanceList")]
        public async Task<BaseResponseData<PageResponse<AdvanceBusinessOutput>>> GetAdvanceList([FromBody] AdvanceBusinessInput query)
        {
            try
            {
                query.UserId = CurrentUser.Id.Value;
                var (list, count) = await _advanceBusinessQueryService.GetAdvanceList(query);
                return new BaseResponseData<PageResponse<AdvanceBusinessOutput>>
                {
                    Data = new PageResponse<AdvanceBusinessOutput>()
                    {
                        List = list,
                        Total = count
                    }
                };
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        /// <summary>
        /// 获取垫资明细汇总
        /// </summary>
        /// <param name="advanceBusinessApplyId"></param>
        /// <returns></returns>
        [HttpPost("GetAdvanceDetailGroup")]
        public async Task<BaseResponseData<List<AdvanceFundBusinessDetailGroupOutput>>> GetAdvanceDetailGroup(Guid advanceBusinessApplyId)
        {
            var res = await _advanceBusinessQueryService.GetAdvanceDetailGroup(advanceBusinessApplyId);
            return new BaseResponseData<List<AdvanceFundBusinessDetailGroupOutput>>()
            {
                Code = CodeStatusEnum.Success,
                Data = res
            };
        }
        /// <summary>
        ///导出
        /// </summary>
        /// <returns></returns>
        [HttpPost("Export")]
        public async Task<IActionResult> Export([FromBody] AdvanceBusinessInput input)
        {
            try
            {
                input.page = 1;
                input.limit = int.MaxValue;
                input.UserId = CurrentUser.Id.Value;
                var (dataList, total) = await _advanceBusinessQueryService.GetAdvanceDetails(input);
                var stream = new MemoryStream();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (var package = new ExcelPackage(stream))
                {
                    var columnIndex = 1;
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    worksheet.SetValue(1, columnIndex++, "垫资申请单号");
                    worksheet.SetValue(1, columnIndex++, "单据日期");
                    worksheet.SetValue(1, columnIndex++, "公司");
                    worksheet.SetValue(1, columnIndex++, "业务单元");
                    worksheet.SetValue(1, columnIndex++, "业务类型");
                    worksheet.SetValue(1, columnIndex++, "医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于核准医院");
                    worksheet.SetValue(1, columnIndex++, "是否属于全流程接管医院");
                    worksheet.SetValue(1, columnIndex++, "是否发票入账");
                    worksheet.SetValue(1, columnIndex++, "销售账期天数");
                    worksheet.SetValue(1, columnIndex++, "回款账期天数");
                    worksheet.SetValue(1, columnIndex++, "垫资天数");
                    worksheet.SetValue(1, columnIndex++, "年化垫资利率(%)");
                    worksheet.SetValue(1, columnIndex++, "折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "基础折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "SPD折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "实际供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "垫资比例(%)");
                    worksheet.SetValue(1, columnIndex++, "供应链金融折扣(%)");
                    worksheet.SetValue(1, columnIndex++, "合计折扣(%)");

                    worksheet.SetValue(1, columnIndex++, "应收单号");
                    worksheet.SetValue(1, columnIndex++, "应收日期");
                    worksheet.SetValue(1, columnIndex++, "开票日期");
                    worksheet.SetValue(1, columnIndex++, "应收金额");
                    worksheet.SetValue(1, columnIndex++, "应付单号");
                    worksheet.SetValue(1, columnIndex++, "应付日期");
                    worksheet.SetValue(1, columnIndex++, "应付金额");

                    worksheet.SetValue(1, columnIndex++, "收款单号");
                    worksheet.SetValue(1, columnIndex++, "收款日期");
                    worksheet.SetValue(1, columnIndex++, "收款金额");

                    worksheet.SetValue(1, columnIndex++, "付款单号");
                    worksheet.SetValue(1, columnIndex++, "付款日期");
                    worksheet.SetValue(1, columnIndex++, "预计付款日期");
                    worksheet.SetValue(1, columnIndex++, "付款金额");

                    worksheet.SetValue(1, columnIndex++, "计划回款日期");
                    worksheet.SetValue(1, columnIndex++, "垫资应收到期时间");
                    worksheet.SetValue(1, columnIndex++, "逾期天数");
                    worksheet.SetValue(1, columnIndex++, "是否逾期");
                    worksheet.SetValue(1, columnIndex++, "逾期利息");
                    worksheet.SetValue(1, columnIndex++, "提前回款利息");
                    worksheet.SetValue(1, columnIndex++, "资金占用余额");
                    worksheet.SetValue(1, columnIndex++, "基础毛利");
                    worksheet.SetValue(1, columnIndex++, "垫资利息收入");
                    worksheet.SetValue(1, columnIndex++, "合计毛利");
                    worksheet.SetValue(1, columnIndex++, "校验");
                    worksheet.SetValue(1, columnIndex++, "开票后实际支付(天数)");
                    worksheet.SetValue(1, columnIndex++, "提示(放款风险)");
                    //worksheet.SetValue(1, columnIndex++, "收款单类型");
                    worksheet.SetValue(1, columnIndex++, "税率折扣");

                    var index = 2;
                    dataList.ForEach(p =>
                    {
                        columnIndex = 1;
                        worksheet.SetValue(index, columnIndex++, p.Code);
                        worksheet.SetValue(index, columnIndex++, p.BillDate.ToString("yyyy-MM-dd"));
                        worksheet.SetValue(index, columnIndex++, p.CompanyName);
                        worksheet.SetValue(index, columnIndex++, p.ServiceName);
                        worksheet.SetValue(index, columnIndex++, "其它");
                        worksheet.SetValue(index, columnIndex++, p.HospitalName);
                        worksheet.SetValue(index, columnIndex++, p.IsVerify ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsTakeOver ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.IsInvoice ? "是" : "否");
                        worksheet.SetValue(index, columnIndex++, p.AccountPeriod);
                        worksheet.SetValue(index, columnIndex++, p.ReceivePeriod);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceDays);
                        worksheet.SetValue(index, columnIndex++, p.RateOfYear);
                        worksheet.SetValue(index, columnIndex++, p.Discount);
                        worksheet.SetValue(index, columnIndex++, p.BaseDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SPDDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        //worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ActualSCFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.ADFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.SCFDiscount);
                        worksheet.SetValue(index, columnIndex++, p.TotalDiscounts);


                        worksheet.SetValue(index, columnIndex++, p.CreditCode);
                        worksheet.SetValue(index, columnIndex++, p.CreditDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.InvoiceDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.CreditValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.DebtCode);
                        worksheet.SetValue(index, columnIndex++, p.DebtDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.DebtValue);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ReceiveCode);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ReceiveAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.PaymentCode);
                        worksheet.SetValue(index, columnIndex++, p.PaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.ExpectPaymentDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.PaymentAmount);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";

                        worksheet.SetValue(index, columnIndex++, p.ExpectReceiveDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.AdvanceExpireDateFormat);
                        worksheet.SetValue(index, columnIndex++, p.OverdueDays);
                        worksheet.SetValue(index, columnIndex++, p.OverdueStatus);
                        worksheet.SetValue(index, columnIndex++, p.OverdueInterest);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.AdvanceReceiveInterest);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.OccupyFundBalance);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.BasicProfit);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.AdvanceInterest);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.TotalProfit);
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.Check.ToString("0.##"));
                        worksheet.Cells[index, columnIndex - 1].Style.Numberformat.Format = "#,##0.00";
                        worksheet.SetValue(index, columnIndex++, p.FactPayDays);
                        worksheet.SetValue(index, columnIndex++, p.Risk);
                        worksheet.SetValue(index, columnIndex++, p.SalesTaxRate);

                        index++;
                    });
                    var headerRow = worksheet.Row(1);
                    headerRow.Style.Font.Bold = true;

                    package.SaveAs(stream);
                    stream.Position = 0;
                    return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                }
            }
            catch (Exception ex)
            {

                return StatusCode(500, $"导出错误: {ex.Message}");
            }
        }
    }
}

