﻿using Inno.CorePlatform.Finance.Application.CompetenceCenter.BDSCenter.Outputs;
using Inno.CorePlatform.Finance.Application.QueryServices.Outputs;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.QueryServices.Inputs
{
    /// <summary>
    /// 查询预开票列表入参
    /// </summary>
    public class PreCustomizeInvoiceListInput : BaseQuery
    {
        /// <summary>
        /// 发票号
        /// </summary>
        public string? InvoiceNo { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? ProjectName { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 核算部门
        /// </summary>
        public string? BusinessDeptId { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string? OrderNo { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public List<string?>? CreatedBy { get; set; }
        /// <summary>
        /// 创建时间起
        /// </summary>
        public string? CreatedDateBeging { get; set; }
        /// <summary>
        /// 创建时间止
        /// </summary>
        public string? CreatedDateEnd { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public PreCustomizeInvoiceItemStatusEnum? Status { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        public string? Code { get; set; }
        /// <summary>
        /// 当前登录人
        /// </summary>
        public string? UserName { get; set; }
        /// <summary>
        /// 当前登录ID
        /// </summary>
        public Guid? UserId { get; set; }
    }

    /// <summary>
    /// 预开票列表明细查询
    /// </summary>
    public class PreCustomizeInvoiceDetailsInput : BaseQuery
    {
        /// <summary>
        /// 预开票主表id
        /// </summary>
        public Guid? PreCustomizeInvoiceItemId { get; set; }
        /// <summary>
        /// 产品名称
        /// </summary>
        public string? ProductName { get; set; }
    }

    /// <summary>
    /// 预开票提交
    /// </summary>
    public class SubmitPreCustomizeInvoiceInput
    {
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid? ProjectId { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        public Project? Project { get; set; }
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        public Company? Company { get; set; }
        /// <summary>
        /// 客户id
        /// </summary>
        public Guid? CustomerId { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public Customer? Customers { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public NewDepart? newDepart { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal? amount { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? createdBy { get; set; }
    }

    /// <summary>
    /// 项目信息
    /// </summary>
    public class Project
    {
        /// <summary>
        /// 项目id
        /// </summary>
        public Guid? id { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string? name { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? status { get; set; }
        /// <summary>
        /// 项目code
        /// </summary>
        public string? code { get; set; }
        /// <summary>
        /// leaderId
        /// </summary>
        public string? leaderId { get; set; }
        /// <summary>
        /// 项目type
        /// </summary>
        public string? type { get; set; }
        /// <summary>
        /// 项目typeLabel
        /// </summary>
        public string? typeLabel { get; set; }
        /// <summary>
        /// subType
        /// </summary>
        public string? subType { get; set; }
        /// <summary>
        /// subTypeLabel
        /// </summary>
        public string? subTypeLabel { get; set; }
    }

    /// <summary>
    /// 公司信息
    /// </summary>
    public class Company
    {
        /// <summary>
        /// id
        /// </summary>
        public Guid? id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string? name { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? status { get; set; }
        /// <summary>
        /// extraInfo
        /// </summary>
        public ExtraInfo? extraInfo { get; set; }
        /// <summary>
        /// ids
        /// </summary>
        public string? ids { get; set; }
    }
    /// <summary>
    /// 客户信息
    /// </summary>
    public class Customer
    {
        /// <summary>
        /// id
        /// </summary>
        public Guid? id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string? name { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int? status { get; set; }
    }
    /// <summary>
    /// 核算部门信息
    /// </summary>
    public class NewDepart
    {
        /// <summary>
        /// id
        /// </summary>
        public string? id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string? name { get; set; }
        /// <summary>
        /// 路径
        /// </summary>
        public string? path { get; set; }
        /// <summary>
        /// fullname
        /// </summary>
        public string? fullName { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string? BusinessArea { get; set; }
    }

    /// <summary>
    /// 同步税收分类编码入参
    /// </summary>
    public class SyncTaxTypeNoInput
    {
        /// <summary>
        /// 公司id
        /// </summary>
        public Guid? CompanyId { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<CustomizeInvoiceDetailOutput>? List { get; set; }
    }
}
