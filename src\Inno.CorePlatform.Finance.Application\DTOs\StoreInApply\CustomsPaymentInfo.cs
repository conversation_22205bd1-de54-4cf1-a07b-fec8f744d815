﻿using Inno.CorePlatform.Finance.Application.DTOs.Purchase;
using Inno.CorePlatform.Finance.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Inno.CorePlatform.Finance.Application.DTOs.StoreInApply
{
    public class CustomsPaymentInfo
    {
        public Guid Id { get; set; }
        public Guid? PayeeId { get; set; }
        public string? PayeeName { get; set; }
        public DateTime? BillDate { get; set; }
        public Guid CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string? BusinessDeptFullPath { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreatedBy { get; set; }
        /// <summary>
        /// 核算部门名称路径
        /// </summary>
        public string? BusinessDeptFullName { get; set; }

        /// <summary>
        /// 核算部门当前Id
        /// </summary>
        public string? BusinessDeptId { get; set; }
        public List<CustomsPaymentDetailInfo> Details { get; set; }

        public PayInfo PayInfo { get; set; }
    }

    public class CustomsPaymentDetailInfo
    {
        public string Id { get; set; }
        public string StoreInCode { get; set; }
        public string PurcaseCode { get; set; }
        public Guid? ProjectId { get; set; }
        public string ProjectName { get; set; }
        public string ProjectCode { get; set; }
        public decimal TariffAmount { get; set; }
        public decimal ImportAddAmount { get; set; }
    }

    public class PayInfo
    {
        /// <summary>
        /// 附言
        /// </summary>
        public string? TransferDiscourse { get; set; } = "";
        /// <summary>
        /// 结算方式编码  
        /// JSFS02 现金支票
        /// JSFS03 转账支票
        /// JSFS04 电汇
        /// JSFS05 信汇
        /// JSFS06 商业承兑汇票
        /// JSFS07 银行承兑汇票
        /// JSFS08 信用证
        /// JSFS09 应收票据背书
        /// JSFS10 内部利息结算
        /// JSFS11 集中结算
        /// JSFS12 票据退票
        /// JSFS13 银企支付
        /// JSFS-YXFY-01	营销费用账户 
        /// </summary>
        public string? SettlementModel { get; set; }
        /// <summary>
        /// 供应商账号
        /// </summary>
        public BankInfo? AgentBank { get; set; }

    }
}
